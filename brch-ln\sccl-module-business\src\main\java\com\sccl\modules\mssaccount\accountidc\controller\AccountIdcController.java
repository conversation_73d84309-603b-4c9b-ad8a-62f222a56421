package com.sccl.modules.mssaccount.accountidc.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.mssaccount.accountidc.domain.AccountIdc;
import com.sccl.modules.mssaccount.accountidc.service.IAccountIdcService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 推送数据平台IDC电量电费 信息操作处理
 * 
 * <AUTHOR>
 * @date 2020-06-22
 */
@RestController
@RequestMapping("/mssaccount/accountIdc")
public class AccountIdcController extends BaseController
{
    private String prefix = "mssaccount/accountIdc";
	
	@Autowired
	private IAccountIdcService accountIdcService;
	
	@RequiresPermissions("mssaccount:accountIdc:view")
	@GetMapping()
	public String accountIdc()
	{
	    return prefix + "/accountIdc";
	}
	
	/**
	 * 查询推送数据平台IDC电量电费列表
	 */
	@RequiresPermissions("mssaccount:accountIdc:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(AccountIdc accountIdc)
	{
		startPage();
        List<AccountIdc> list = accountIdcService.selectList(accountIdc);
		return getDataTable(list);
	}
	
	/**
	 * 新增推送数据平台IDC电量电费
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存推送数据平台IDC电量电费
	 */
	@RequiresPermissions("mssaccount:accountIdc:add")
	@Log(title = "推送数据平台IDC电量电费", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody AccountIdc accountIdc)
	{		
		return toAjax(accountIdcService.insert(accountIdc));
	}

	/**
	 * 修改推送数据平台IDC电量电费
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		AccountIdc accountIdc = accountIdcService.get(id);

		Object object = JSONObject.toJSON(accountIdc);

        return this.success(object);
	}
	
	/**
	 * 修改保存推送数据平台IDC电量电费
	 */
	@RequiresPermissions("mssaccount:accountIdc:edit")
	@Log(title = "推送数据平台IDC电量电费", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody AccountIdc accountIdc)
	{		
		return toAjax(accountIdcService.update(accountIdc));
	}
	
	/**
	 * 删除推送数据平台IDC电量电费
	 */
	@RequiresPermissions("mssaccount:accountIdc:remove")
	@Log(title = "推送数据平台IDC电量电费", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(accountIdcService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看推送数据平台IDC电量电费
     */
    @RequiresPermissions("mssaccount:accountIdc:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		AccountIdc accountIdc = accountIdcService.get(id);

        Object object = JSONObject.toJSON(accountIdc);

        return this.success(object);
    }

}
