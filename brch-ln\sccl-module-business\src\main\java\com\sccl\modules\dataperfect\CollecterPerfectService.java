package com.sccl.modules.dataperfect;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.util.concurrent.AtomicDouble;
import com.sccl.common.utils.BigDecimlUtil;
import com.sccl.framework.utils.FileUploadUtils;
import com.sccl.modules.autojob.util.convert.DateUtils;
import com.sccl.modules.business.ammeterorprotocol.mapper.AmmeterorprotocolMapper;
import com.sccl.modules.business.powerauditstaiongrade.util.ExcelExporter;
import com.sccl.modules.business.syncresult.mapper.SyncresultMapper;
import com.sccl.modules.business.timing.api.PowerStationQuotaSTAAPI;
import com.sccl.modules.business.timing.dto.EnergyQuantity;
import com.sccl.modules.dataperfect.domain.*;
import com.sccl.modules.mssaccount.mssinterface.domain.CollectMeter;
import com.sccl.modules.mssaccount.mssinterface.domain.CollectMeterFail;
import com.sccl.modules.mssaccount.mssinterface.domain.MeterInfo2;
import com.sccl.modules.mssaccount.mssinterface.service.IMssInterfaceService;
import com.sccl.modules.mssaccount.mssinterface.service.MssInterfaceServiceImpl;
import com.sccl.modules.mssaccount.mssinterface.service.MssJsonClient;
import com.sccl.modules.system.attachments.domain.Attachments;
import com.sccl.modules.system.attachments.mapper.AttachmentsMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static java.util.stream.Collectors.*;

@Service
@Slf4j
public class CollecterPerfectService {
    @Autowired
    MssInterfaceServiceImpl mssInterfaceServiceImpl;
    @Autowired
    private SyncresultMapper syncresultMapper;
    @Autowired
    private IMssInterfaceService mssInterfaceService;
    @Autowired
    private MssJsonClient mssJsonClient;
    @Autowired(required = false)
    private AttachmentsMapper attachmentsMapper;
    @Autowired
    private AmmeterorprotocolMapper ammeterorprotocolMapper;

    public static TreeMap<String, List<String>> getTimeMappStaioncode(List<AccountTime> times) {
        TreeMap<String, List<String>> result = new TreeMap<>((t1, t2) -> t1.compareTo(t2));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        for (AccountTime time : times) {
            LocalDate startDate = LocalDate.parse(time.getStartdate(), formatter);
            LocalDate endDate = LocalDate.parse(time.getEnddate(), formatter);

            // 从开始日期到结束日期，将stationaddresscode添加到相应的日期
            while (!startDate.isAfter(endDate)) {
                String currentDate = startDate.toString();
                result.computeIfAbsent(currentDate, k -> new ArrayList<>())
                        .add(time.getStationaddresscode());
                startDate = startDate.plusDays(1);
            }
        }
        return result;
    }

    public static void main(String[] args) {
        boolean diffGreaterThanOrEqualTo = BigDecimlUtil.isDiffGreaterThanOrEqualTo(
                new BigDecimal("13.1"),
                new BigDecimal("10"),
                new BigDecimal("32")
        );
        System.out.println(diffGreaterThanOrEqualTo);
    }

    public String syncIncrementalMeter(String stationcode) throws Exception {
        String result = null;
        if ("all".equals(stationcode)) {
            log.info("在网局站全量补充...");
            log.info("获取在网局站现存全量局站");
            List<String> stationcodeAll = syncresultMapper.selectAllstation(stationcode);

            String lastMonth = LocalDate.now().minusMonths(1).format(DateTimeFormatter.ofPattern("yyyy-MM"));
            log.info("获取{}账期报账所用局站", lastMonth);
            List<ColleterPerfect> colleterPerfects = syncresultMapper.selectcolleterPerfects(lastMonth);
            List<String> jzstationids =
                    colleterPerfects.stream().filter(item -> "14".equals(item.getType())).map(ColleterPerfect::getStationcode).distinct().collect(Collectors.toList());
            List<String> jzstationcodes = syncresultMapper.selectJzStationcode(jzstationids);
            Map<String, String> jzstationcodeMap = convertListToMap(jzstationcodes);
            colleterPerfects = convertJzStationcode(colleterPerfects, jzstationcodeMap);


            log.info("比较");
            List<ColleterPerfect> comparelist = colleterPerfects.stream().filter(
                    item -> {
                        return !stationcodeAll.contains(item.getStationcode());
                    }
            ).collect(toList());
            if (CollectionUtils.isEmpty(comparelist)) {
                result = String.format("%s账期全量局站数据一致，不需要补充", lastMonth);
                log.info("{}账期全量局站数据一致，不需要补充", lastMonth);
                return result;
            }

            log.info("账期{}共找到{}条未同步的局站信息", lastMonth, comparelist.size());
            List<String> jzlist =
                    comparelist.stream().filter(item -> "14".equals(item.getType())).map(ColleterPerfect::getStationcode).collect(toList());
            List<String> notjzlist =
                    comparelist.stream().filter(item -> !"14".equals(item.getType())).map(ColleterPerfect::getStationcode).collect(toList());
            log.info("基站{}条", jzlist.size());
            log.info("非基站{}条", notjzlist.size());

            if (CollectionUtils.isNotEmpty(jzlist)) {
                int n = syncresultMapper.insertJzForMeterinfo(jzlist);
                log.info("meterinfo表基站新增{}条", n);
            }
            if (CollectionUtils.isNotEmpty(notjzlist)) {
                int n = syncresultMapper.insertJznotForMeterinfo(notjzlist);
                log.info("meterinfo表非基站新增{}条", n);
            }
            log.info("插入meterinfo表结束");

            log.info("未同步局站信息同步到计量设备接口");
            jzlist.addAll(notjzlist);
            List<? extends MeterInfo2> meterinfoALl = syncresultMapper.selectSysnc(jzlist);
            meterinfoALl.forEach(
                    meterInfo2 -> meterInfo2.setType(MssInterfaceServiceImpl.SYNCTYPECREATE)
            );
            mssInterfaceService.syncIncrementalMeter(meterinfoALl);
            log.info("预计同步局站{}条", meterinfoALl.size());
        } else {
            log.info("在网局站staioncode={}补充", stationcode);
            log.info("判断是否已经同步");
            List<String> stationcodeAll = syncresultMapper.selectAllstation(stationcode);
            if (stationcodeAll.size() > 0) {
                log.info("stationcode={}已存在meterinfo表中", stationcode);
                result = String.format("%s已存在meterinfo表中", stationcode);
                return result;
            }

            log.info("插入meterinfo表");
            syncresultMapper.insertMeterinfos(stationcode);
            log.info("未同步局站信息同步到计量设备接口");
            List<? extends MeterInfo2> meterinfos = syncresultMapper.selectMeterinfo(stationcode);
            meterinfos.forEach(
                    meterInfo2 -> meterInfo2.setType(MssInterfaceServiceImpl.SYNCTYPECREATE)
            );
            mssInterfaceService.syncIncrementalMeter(meterinfos);
        }
        return result == null ? "计量设备增量同步结束" : result;
    }

    private List<ColleterPerfect> convertJzStationcode(List<ColleterPerfect> colleterPerfects,
                                                       Map<String, String> jzstationcodeMap) {
        colleterPerfects.forEach(
                item -> {
                    if ("14".equals(item.getType())) {
                        item.setStationcode(jzstationcodeMap.get(item.getStationcode()));
                    }
                }
        );
        return colleterPerfects;
    }

    private Map<String, String> convertListToMap(List<String> strs) {
        return strs.stream()
                .map(s -> s.split("->"))
                .filter(item -> item.length == 2)
                .collect(toMap(item -> item[0].trim(), item -> item[1].trim()));
    }

    public List<CollectMeterVo> selectFailCollectMeter(int pagenum, int pagesize) {
        int limit = pagesize;
        int offset = (pagenum - 1) * pagesize;
        return syncresultMapper.selectFailCollectMeter(limit, offset);
    }

    public String retryFailCollectMeter(CollectMeterVo vo) {
        List<CollectMeter> infoDbs = syncresultMapper.selectFailCollectMeterByVo(vo);
        sendSyncCollectMeter(infoDbs);
        return "采集重试完毕";
    }

    private void sendSyncCollectMeter(List<CollectMeter> infoDbs) {
        mssJsonClient.sendSyncCollectMeter(infoDbs);
    }

    public String staException(String bitch) {
        long start = System.currentTimeMillis();
        int limit = Integer.parseInt(bitch);
        int offset = 0;
        String result = bitchInsertStaTemp(limit, offset);
        long end = System.currentTimeMillis();
        log.info("插入staTemp耗时{}s", (end - start) / 1000);
        return result;
    }

    private String bitchInsertStaTemp(int limit, int offset) {
        boolean bitchFlag = true;
        while (bitchFlag) {
            List<String> resStationodes = syncresultMapper.selectResstationcode(limit, offset);
            syncresultMapper.insertStaTemp(resStationodes);
            if (resStationodes.size() < limit) {
                bitchFlag = false;
            }
            offset += limit;
        }
        return "staTemp数据插入完成，可执行趋势超标待统计sql";
    }

    private String bitchInsertStaResult(int limit, int offset) {
        boolean bitchFlag = true;
        while (bitchFlag) {
            log.info("获取报账账期202301至202307的站址第{}至{}条", offset, limit + offset);
            List<Tempt> tempts = syncresultMapper.selectTempt(limit, offset);
            List<String> resStationodes = tempts.stream().map(
                    item -> item.getResstationcode()
            ).collect(toList());
            log.info("取数{}条", limit);

            log.info("获取redis对应sta数据");
            Map<String, List<EnergyQuantity>> staMap = PowerStationQuotaSTAAPI.listByStationCodes(resStationodes,
                    DateUtils.getLastMomentOfMonth(2022, 12),
                    DateUtils.getLastMomentOfMonth(2023, 12)
            );
            log.info("清除隧道返回的空元素");
            staMap.forEach(
                    (k, v) -> {
                        v.removeIf(Objects::isNull);
                    }
            );
            log.info("redis对应sta数据获取完毕");

            log.info("转化staResult统计");
            List<StaResult> staResults = StaResult.generateStaResults(tempts, staMap);
            staResults.forEach(sta -> {
                String resstationcode = sta.getResstationcode();
                String startdate = sta.getStartdate();
                String enddate = sta.getEnddate();
                String avgForGeteWay = syncresultMapper.getavgForGeteWay(resstationcode, startdate, enddate);
                sta.setAvgForGateway(avgForGeteWay);
            });

            syncresultMapper.insertStaResult(staResults);
            if (resStationodes.size() < limit) {
                bitchFlag = false;
            }
            offset += limit;
        }
        log.info("staResult数据汇总完成，请到staResult表查看汇总结果");
        return "staResult数据汇总完成，请到staResult表查看汇总结果";
    }

    private String bitchInsertstagatewayResult(int limit, int offset) {
        boolean bitchFlag = true;
        while (bitchFlag) {
            log.info("获取stagateway_bak的站址第{}至{}条", offset, limit + offset);
            List<StationGateway> tempts = syncresultMapper.selectStationGateway(limit, offset);
            log.info("站址去重");
            tempts = tempts.stream().distinct().collect(toList());
            log.info("获取站址对应时间");
            List<String> stationcodes = tempts.stream().map(StationGateway::getStationCode).collect(toList());
            boolean empty = CollectionUtils.isEmpty(stationcodes);
            List<StationGateway> times = empty ? new ArrayList<>() : syncresultMapper.selectStationMapTime(stationcodes);
            List<StationDeviceAvg> deviceAvgs = empty ? new ArrayList<>() : syncresultMapper.selectStationDeviceAvg(stationcodes);
            tempts.forEach(
                    item -> {
                        StationGateway.process(item, times);
                        item.setDevicePower(
                                calcDevicePower(item.getStationCode(), times, deviceAvgs)
                        );

                    }
            );

            boolean flag = CollectionUtils.isEmpty(tempts);
            int n = flag ? 0 : syncresultMapper.insertStagateway2(tempts);
            log.info("插入汇总数据{}条", n);
            if (tempts.size() < limit) {
                bitchFlag = false;
            }
            offset += limit;
        }
        log.info("Stagateway数据汇总完成，请到Stagateway2表查看汇总结果");
        return "Stagateway数据汇总完成，请到Stagateway2表查看汇总结果";
    }

    private String bitchInsertAccountAvgException(String time, int limit, int offset) {
        log.info("置空{} 历史数据", time);
        syncresultMapper.deleteAccountAvgException(time);

        boolean bitchFlag = true;
        while (bitchFlag) {
            log.info("获取报账月份{} 的站址第{}至{}条", time, offset, limit + offset);
            Integer year = Integer.valueOf(time.split("-")[0]);
            Integer month = Integer.valueOf(time.split("-")[1]);
            List<StationGateway> tempts = syncresultMapper.selectStationAccountAvgException(year, month, limit, offset);

            log.info("获取站址对应网管日均电量");
            ArrayList<StationGateway> staavgs = new ArrayList<>();
            Integer size = Optional.ofNullable(tempts).map(List::size).orElse(0);
            Map<Integer, List<StationGateway>> bitchMap = IntStream.rangeClosed(0, size - 1).boxed().collect(
                    groupingBy(
                            integer -> (integer + 10) / 10,
                            mapping(tempts::get, toList())
                    )
            );

            int finalOffset = offset;
            int finalOffset1 = offset;
            bitchMap.forEach(
                    (bitch, dbs) -> {
                        log.info("{} 第{}至{}条 第{}批数据开始 sta 统计", time, finalOffset, limit + finalOffset1, bitch);
                        List<StationGateway> tempavgs = syncresultMapper.getavgForGeteWayPro(dbs);
                        tempavgs = tempavgs.stream().filter(item -> item != null).collect(toList());
                        boolean b = CollectionUtils.isNotEmpty(tempavgs) ? staavgs.addAll(tempavgs) : false;
                    }
            );

            List<StationGateway> dbs = StationGateway.statisticalDeviation(tempts, staavgs);

            dbs.forEach(item -> item.setTime(time));
            int n = CollectionUtils.isEmpty(dbs) ? 0 : syncresultMapper.insertAccountAvgException(dbs);
            log.info("插入汇总数据{}条", n);
            if (tempts.size() < limit) {
                bitchFlag = false;
            }
            offset += limit;
        }
        log.info("台账日均电量波动 数据汇总完成，请到AccountAvgException表查看汇总结果");
        return "台账日均电量波动 数据汇总完成，请到AccountAvgException表查看汇总结果";
    }


    private String calcDevicePower(String stationCode, List<StationGateway> times, List<StationDeviceAvg> deviceAvgs) {
        Map<String, List<StationGateway>> timemap = times.stream().collect(groupingBy(
                StationGateway::getStationCode
        ));
        List<StationGateway> stationcodeMapTimes = timemap.get(stationCode);
        if (CollectionUtils.isEmpty(stationcodeMapTimes)) {
            return "0";
        }
        Map<String, Integer> timeMap = StationGateway.calculateDays(stationcodeMapTimes);
        Map<String, String> deviceMap = deviceAvgs.stream().filter(
                item -> item.getStationcode().equals(stationCode)
        ).collect(
                toMap(
                        StationDeviceAvg::getTime,
                        StationDeviceAvg::getDevice_avg,
                        (v1, v2) -> {
                            String s = v1 != null ? v1 : v2 != null ? v2 : "0";
                            return s;
                        }
                )
        );
        if (deviceMap == null) {
            return "0";
        }
        AtomicDouble sum = new AtomicDouble();
        timeMap.forEach(
                (k, v) -> {
                    if (deviceMap.containsKey(k)) {
                        BigDecimal sumtemp = BigDecimal.valueOf(v).multiply(new BigDecimal(deviceMap.get(k))).setScale(2, BigDecimal.ROUND_HALF_UP);
                        sum.addAndGet(sumtemp.doubleValue());
                    }
                }
        );

        return String.valueOf(sum.get());
    }

    public String staResult(String bitch) {
        long start = System.currentTimeMillis();
        int limit = Integer.parseInt(bitch);
        int offset = 0;
        String result = bitchInsertStaResult(limit, offset);
        long end = System.currentTimeMillis();
        log.info("插入staTemp耗时{}s", (end - start) / 1000);
        return result;
    }

    public String stagatewayResult(String bitch) {
        long start = System.currentTimeMillis();
        int limit = Integer.parseInt(bitch);
        int offset = 0;
        String result = bitchInsertstagatewayResult(limit, offset);
        long end = System.currentTimeMillis();
        log.info("插入stagatewayResult耗时{}s", (end - start) / 1000);
        return result;
    }

    public void collectAll(String budget) {
        List<AccountTime> times = syncresultMapper.collectAll(budget);
        syncresultMapper.deleteAll(budget);
        log.info("逻辑删除{}账期的采集日志", budget);
        TreeMap<String, List<String>> timeMapStationcoe = getTimeMappStaioncode(times);

        log.info("删除********之前的采集时间");
        ArrayList<String> keyRemove = new ArrayList<>();
        timeMapStationcoe.forEach(
                (k, v) -> {
                    if (k.compareTo("2023-01-01") < 0) {
                        keyRemove.add(k);
                    }
                }
        );
        keyRemove.forEach(timeMapStationcoe::remove);
        timeMapStationcoe.forEach(
                (time, stationcodes) -> {
                    try {
                        long count = stationcodes.stream().distinct().count();
                        log.info("时间：{}-{},有{}个不同的局站要采集同步", budget, time, count);
                        time = time.replace("-", "");
                        mssInterfaceServiceImpl.selectCollectMeterInforsPlus(-1L, time, budget, stationcodes);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
        );

        log.info("ok");
    }

    /**
     * 直接从collectmeter表查询数据进行推送，循环处理每一天的数据
     *
     * @param budget 账期，格式为：yyyy-MM
     */
    public void collectFromTable(String budget) {
        log.info("开始准备{} -> collectmeter数据", budget);

        // 解析 budget 参数，格式如 2025-06
        String[] budgetParts = budget.split("-");
        if (budgetParts.length != 2) {
            throw new IllegalArgumentException("budget 格式错误，应为 yyyy-MM，实际值：" + budget);
        }

        int year = Integer.parseInt(budgetParts[0]);
        int month = Integer.parseInt(budgetParts[1]);

        // 生成当月的日期范围
        LocalDate startDate = LocalDate.of(year, month, 1);
        LocalDate endDate = startDate.withDayOfMonth(startDate.lengthOfMonth());

        log.info("生成日期范围: {} 至 {}", startDate, endDate);

        // 优化：一次性查询collectmeter全表数据作为基础数据
        log.info("开始一次性查询collectmeter全表基础数据");
        List<CollectMeter> baseCollectMeterData = syncresultMapper.getCollectMeterByTime(); // 传空字符串，SQL中不使用
        log.info("查询到collectmeter基础数据{}条", baseCollectMeterData.size());

        if (CollectionUtil.isEmpty(baseCollectMeterData)) {
            log.warn("collectmeter表中没有数据，提前返回");
            return;
        }

        // 遍历每一天，复用基础数据并修改collectTime后推送
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            String dateStr = currentDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String formattedTime = currentDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            try {
                log.info("开始处理日期：{}", dateStr);

                // 复制基础数据并设置当天的collectTime
                List<CollectMeter> dailyCollectMeterData = new ArrayList<>();
                for (CollectMeter baseData : baseCollectMeterData) {
                    CollectMeter dailyData = new CollectMeter();
                    // 复制基础数据的所有字段
                    BeanUtils.copyProperties(baseData, dailyData);
                    // 设置当天的采集时间
                    dailyData.setCollectTime(formattedTime);
                    dailyCollectMeterData.add(dailyData);
                }

                log.info("复制并设置{}条{}的数据", dailyCollectMeterData.size(), dateStr);
                // 打印第一条数据
                log.info("第一条数据：{}", dailyCollectMeterData.get(0));

                // 进行HTTP推送
                mssInterfaceServiceImpl.selectCollectMeterInforsPlusProFixQxm(dailyCollectMeterData, budget);
                log.info("{}的数据推送完毕，共{}条", dateStr, dailyCollectMeterData.size());

            } catch (Exception e) {
                log.error("处理{}数据时发生错误", dateStr, e);
            }

            // 移动到下一天
            currentDate = currentDate.plusDays(1);
        }

        log.info("{}账期collectmeter数据推送完毕", budget);
    }

    public void collectAllProCheckAllnewSync(String budget) {
        // 先删除当前账期
        syncresultMapper.deleteNowBudget(budget);
        log.info("账期：{},采集电量数据至jt表中---->", budget);
        CompletableFuture.runAsync(
                () -> collectAllProChecknewAll(budget)
        );
//        collectAllProChecknewAll(budget);
    }

    public void collectAllProChecknewAll(String budget) {
        List<AccountTimeVo> list = syncresultMapper.collectAllProGroupChecknewAll(budget);
        log.info("账期：{}，全量业财一致率采集电量数据异步数据查询完毕，开始记录中---->", budget);
        if (CollectionUtil.isNotEmpty(list)) {
            syncresultMapper.insetAllProGroupCheck(list);
        }
        log.info("账期：{}，全量业财一致率修复异步new数据保存完毕---->", budget);
    }

    public void collectAllProFixnewSync(String budget, Integer flag) {
        log.info("时间：{}-{},业财一致率上传集团异步开始执行---->", budget, flag);
        CompletableFuture.runAsync(() -> collectAllProFixnew(budget, flag));
//        collectAllProFixnew(budget,flag);
    }

    public void collectAllProFixnew(String budget, Integer timeFlag) {
        log.info("上传集团账期全量采集new数据,开始查询全量数据的记录条数----》");
        List<AccountTime> times = syncresultMapper.selectAllProGroupCheck(budget);
        log.info("上传集团账期全量采集new数据,全量数据的记录条数----》{}", times.size());
        syncresultMapper.deleteAll(budget);
        log.info("逻辑删除{}账期的采集日志", budget);

        TreeMap<String, List<String>> timeMapStationcoe = getTimeMappStaioncode(times);
        log.info("将局站分为每天需要传的数量----》{}", timeMapStationcoe.size());
        log.info("删除********之前的采集时间");
        String nowDay = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        log.info("删除{}（包括）之后的采集时间", nowDay);
        ArrayList<String> keyRemove = new ArrayList<>();
        timeMapStationcoe.forEach(
                (k, v) -> {
                    if (k.compareTo("2023-01-01") < 0 || k.compareTo(nowDay) >= 0) {
                        keyRemove.add(k);
                    }
                }
        );
        keyRemove.forEach(timeMapStationcoe::remove);
        log.info("过滤后每天需要传的数量----》{}", timeMapStationcoe.size());

        log.info("获取每日固定采集数据");
        List<CollectMeter> collectMeters = syncresultMapper.getCollect();

        timeMapStationcoe.forEach(
                (time, stationcodes) -> {
                    try {
                        long count = stationcodes.stream().distinct().count();
                        log.info("时间：{}-{},有{}个不同的局站要采集同步,局站code为{}", budget, time, count, stationcodes);
                        time = time.replace("-", "");
                        mssInterfaceServiceImpl.selectCollectMeterInforsPlusProFix(-1L, time, budget, stationcodes, collectMeters, timeFlag);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
        );

        log.info("ok");
    }

    public void collectAllPro(String budget, Integer timeFlag) {
        List<AccountTime> times = new ArrayList<>();
        if (timeFlag == 1) {
            times = syncresultMapper.collectAllProGroupAmmeterpol(budget);
        } else if (timeFlag == 2) {
            times = syncresultMapper.collectAllProGroupStationCode(budget);
        } else if (timeFlag == 3) {
            times = syncresultMapper.collectAllProGroupAll(budget);
        } else {
            throw new RuntimeException(String.format("错误的timeFlag 参数,可用参数 1,2,3,实际参数:%d", timeFlag));
        }
        // todo 注释恢复
        syncresultMapper.deleteAll(budget);
        log.info("逻辑删除{}账期的采集日志", budget);
        TreeMap<String, List<String>> timeMapStationcoe = getTimeMappStaioncode(times);
        log.info("删除********之前的采集时间");
        String nowDay = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        log.info("删除{}（包括）之后的采集时间");
        ArrayList<String> keyRemove = new ArrayList<>();
        timeMapStationcoe.forEach(
                (k, v) -> {
                    if (k.compareTo("2023-01-01") < 0 || k.compareTo(nowDay) >= 0) {
                        keyRemove.add(k);
                    }
                }
        );
        keyRemove.forEach(timeMapStationcoe::remove);

        log.info("获取每日固定采集数据");
        List<CollectMeter> collectMeters = syncresultMapper.getCollect();

        timeMapStationcoe.forEach(
                (time, stationcodes) -> {
                    try {
                        long count = stationcodes.stream().distinct().count();
                        log.info("时间：{}-{},有{}个不同的局站要采集同步", budget, time, count);
                        time = time.replace("-", "");
                        mssInterfaceServiceImpl.selectCollectMeterInforsPlusPro(-1L, time, budget, stationcodes, collectMeters, timeFlag);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
        );

        log.info("{} 采集数据传输完毕", budget);
    }

    public void collectAllProPro(String budget, Integer timeFlag, Integer interval) {
        List<AccountTime> times = new ArrayList<>();
        if (timeFlag == 1) {
            times = syncresultMapper.collectAllProGroupAmmeterpol(budget);
        } else if (timeFlag == 2) {
            times = syncresultMapper.collectAllProGroupStationCode(budget);
        } else if (timeFlag == 3) {
            times = syncresultMapper.collectAllProGroupAll(budget);
        } else if (timeFlag == 4) {
            times = syncresultMapper.collectAllProGroupAmmeterpoljtmss(budget);
        } else if (timeFlag == 5) {
            times = syncresultMapper.collectAllProGroupAmmeterpolConsist(budget);
        }
        // 手动采集
        else if (timeFlag == 6) {
            times = syncresultMapper.manualAcquisition();
        } else {
            throw new RuntimeException(String.format("错误的timeFlag 参数,可用参数 1,2,3,4,实际参数:%d", timeFlag));
        }
        TreeMap<String, List<String>> timeMapStationcoe = getTimeMappStaioncode(times);
        log.info("删除********之前的采集时间");
        String nowDay = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        log.info("删除{}（包括）之后的采集时间");
        ArrayList<String> keyRemove = new ArrayList<>();
        timeMapStationcoe.forEach(
                (k, v) -> {
                    if (k.compareTo("2025-01-01") < 0 || k.compareTo(nowDay) >= 0) {
                        keyRemove.add(k);
                    }
                }
        );
        keyRemove.forEach(timeMapStationcoe::remove);

        log.info("获取每日固定采集数据");
        List<CollectMeter> collectMeters = new ArrayList<>();

        timeMapStationcoe.forEach(
                (time, stationcodes) -> {
                    try {
                        long count = stationcodes.stream().distinct().count();
                        log.info("时间：{}-{},有{}个不同的局站要采集同步", budget, time, count);
                        time = time.replace("-", "");
                        mssInterfaceServiceImpl.selectCollectMeterInforsPlusProPro(-1L, time, budget, stationcodes, collectMeters, timeFlag, interval);
                    } catch (Exception e) {
                        log.error("推送采集数据错误", e);
                    }
                }
        );

        log.info("ok");
    }

    /**
     * 使用 budget 参数生成日期范围，从 ycyz_null 表查询局站编码进行同步
     *
     * @param budget   预算账期，格式为：yyyy-MM
     * @param timeFlag 时间标志，用于确定数据处理方式
     * @param interval 数据推送间隔时间（毫秒）
     */
    public void collectAllProProWithStationcodes(String budget, Integer timeFlag, Integer interval) {
        log.info("开始优化数据传输，使用 budget {} 生成日期范围并从 ycyz_null 表查询局站编码", budget);

        // 从 ycyz_null 表查询局站编码
        List<String> stationCodes = syncresultMapper.selectYcyzNullStationCodes();
        log.info("从 ycyz_null 表查询到 {} 条局站编码", stationCodes.size());

        if (stationCodes.isEmpty()) {
            log.warn("未从 ycyz_null 表查询到任何局站编码，提前返回");
            return;
        }

        // 解析 budget 参数，格式如 2025-05
        String[] budgetParts = budget.split("-");
        if (budgetParts.length != 2) {
            throw new IllegalArgumentException("budget 格式错误，应为 yyyy-MM，实际值：" + budget);
        }

        int year = Integer.parseInt(budgetParts[0]);
        int month = Integer.parseInt(budgetParts[1]);

        // 生成当月的日期范围
        LocalDate startDate = LocalDate.of(year, month, 1);
        LocalDate endDate = startDate.withDayOfMonth(startDate.lengthOfMonth());

        log.info("生成日期范围: {} 至 {}", startDate, endDate);

        // **优化关键**：在循环外部一次性查询基础数据
        log.info("开始一次性查询基础数据，避免重复查询");
        List<CollectMeter> baseCollectMeterData = null;

        // 生成查询用的基础时间（当月第一天）
        String baseQueryTime = startDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        log.info("使用基础查询时间: {}", baseQueryTime);

        try {
            if (timeFlag == 5 || timeFlag == 6) {
                baseCollectMeterData = ammeterorprotocolMapper.getCollectMeterInforScPlusProProProConsist(-1L, baseQueryTime, budget, stationCodes);
            } else {
                baseCollectMeterData = ammeterorprotocolMapper.getCollectMeterInforScPlusProProPro(-1L, baseQueryTime, budget, stationCodes);
            }
            log.info("基础数据查询完成，共 {} 条记录", baseCollectMeterData != null ? baseCollectMeterData.size() : 0);
        } catch (Exception e) {
            log.error("查询基础数据失败", e);
            return;
        }

        if (baseCollectMeterData == null || baseCollectMeterData.isEmpty()) {
            log.warn("未查询到任何基础数据，提前返回");
            return;
        }

        // 遍历每一天，使用相同的基础数据但设置不同的 collectTime
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            String dateStr = currentDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String formattedTime = dateStr.replace("-", "");

            try {
                long count = stationCodes.stream().distinct().count();
                log.info("时间：{}-{}, 有{}个不同的局站要采集同步", budget, dateStr, count);

                // **优化关键**：复制基础数据并更新 collectTime，避免重复数据库查询
                List<CollectMeter> dailyCollectMeterData = new ArrayList<>();
                for (CollectMeter baseData : baseCollectMeterData) {
                    CollectMeter dailyData = new CollectMeter();
                    // 复制基础数据的所有字段
                    copyCollectMeterData(baseData, dailyData);
                    // 设置当天的采集时间
                    dailyData.setCollectTime(formattedTime);
                    dailyCollectMeterData.add(dailyData);
                }

                log.info("处理数据，满足协议格式");
                CollectMeter.processDataFields(dailyCollectMeterData);
                dailyCollectMeterData = CollectMeter.processEnergyData(dailyCollectMeterData);

                // 推送数据
                pushCollectMeterData(dailyCollectMeterData, budget, dateStr, interval);

            } catch (Exception e) {
                log.error("推送采集数据错误，时间：{}", dateStr, e);
            }

            currentDate = currentDate.plusDays(1);
        }

        log.info("使用优化的数据传输方式完成局站编码同步");
    }

    /**
     * 复制 CollectMeter 数据
     */
    private void copyCollectMeterData(CollectMeter source, CollectMeter target) {
        target.setCityCode(source.getCityCode());
        target.setCityName(source.getCityName());
        target.setCountyCode(source.getCountyCode());
        target.setCountyName(source.getCountyName());
        target.setStationCode(source.getStationCode());
        target.setStationName(source.getStationName());
        target.setParentStationCode(source.getParentStationCode());
        target.setCcoer(source.getCcoer());
        target.setCdcf(source.getCdcf());
        target.setEnergyData(source.getEnergyData());
        target.setEnergyDataSource(source.getEnergyDataSource());
        target.setAcData(source.getAcData());
        target.setAcDataSource(source.getAcDataSource());
        target.setOepgData(source.getOepgData());
        target.setOepgDataSource(source.getOepgDataSource());
        target.setPvpgData(source.getPvpgData());
        target.setPvpgDataSource(source.getPvpgDataSource());
        target.setDeviceData(source.getDeviceData());
        target.setDeviceDataSource(source.getDeviceDataSource());
        target.setProductionData(source.getProductionData());
        target.setProductionDataSource(source.getProductionDataSource());
        target.setManagementData(source.getManagementData());
        target.setManagementDataSource(source.getManagementDataSource());
        target.setBusinessData(source.getBusinessData());
        target.setBusinessDataSource(source.getBusinessDataSource());
        target.setOtherData(source.getOtherData());
        target.setOtherDataSource(source.getOtherDataSource());
    }

    /**
     * 推送采集数据
     */
    private void pushCollectMeterData(List<CollectMeter> collectMeterData, String budget, String dateStr, Integer interval) {
        int bitsize = 200;
        Map<Integer, List<CollectMeter>> dataMap = IntStream.range(0, collectMeterData.size()).boxed().collect(
                groupingBy(
                        i -> i / bitsize + 1, mapping(collectMeterData::get, toList())
                )
        );

        dataMap.forEach((k, v) -> {
            // 集团收到数据有缺失，避免推送太快，每次间隔一定的时间
            if (interval != null) {
                try {
                    Thread.sleep(interval);
                } catch (InterruptedException e) {
                    log.error("线程休眠异常", e);
                }
            }
            log.info("{}-{}第{}批数据:{}条开始推送", budget, dateStr, k, v.size());
            String res = mssJsonClient.syncCollectMeterInforsPlus(v, budget);
            log.info("{}-{}第{}批数据:{}条推送结束,响应:{}", budget, dateStr, k, v.size(), res);
        });
    }

    public List<CollectMeterFail> generateSyncCollect(String budget) {

        return syncresultMapper.generateSyncCollect(budget);
    }


    public Attachments exportExcelForSyncCollect(List<CollectMeterFail> fails, Map<String, String> columnMap, Map<String, String> promptColumnMap) {
        Date currentDate = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy_MM_dd_HH_mm_ss");
        String formattedDate = dateFormat.format(currentDate);
        String fileName = "采集业局站表" + formattedDate + ".xlsx";
        String outPath = "D:\\" + fileName;
        try {
            ExcelExporter.exportToExcel(fails, columnMap, outPath);
            Attachments attachments = new Attachments();
            String filedIdName = FileUploadUtils.encodingFilename(fileName, ".xls");
            attachments.setFileName(fileName);
            attachments.setBusiAlias("站址对应报账单表)");
            attachments.setYear(Integer.valueOf(com.sccl.common.utils.DateUtils.getYear()));
            attachments.setDelFlag("0");
            if (attachmentsMapper.insert(attachments) == 1) {
                return attachments;
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public String AccountAvgException(String time, String bitch) {
        long start = System.currentTimeMillis();
        int limit = Integer.parseInt(bitch);
        int offset = 0;
        String result = bitchInsertAccountAvgException(time, limit, offset);
        long end = System.currentTimeMillis();
        log.info("{} 台账日均电量波动 插入AccountAvgException耗时{}s", time, (end - start) / 1000);
        return result;

    }

    public void collectAllProSyncQxm(String budget) {
        log.info("时间：{},业财一致率上传集团异步开始执行---->", budget);
        CompletableFuture.runAsync(() -> collectAllProQxm(budget));
//        collectAllProQxm(budget);
    }

    private void collectAllProQxm(String budget) {
        log.info("上传集团账期全量采集new数据,开始查询全量数据的记录条数----》");
        List<HashMap<String, Object>> dataList = syncresultMapper.selectAllProGroupCheckQxm(budget);
        log.info("上传集团账期全量采集new数据,全量数据的记录条数----》{}", dataList.size());
        syncresultMapper.deleteAll(budget);
        log.info("逻辑删除{}账期的采集日志", budget);
        if (CollectionUtil.isNotEmpty(dataList)) {
            List<CollectMeter> collectMeterList = new ArrayList<>();
            for (HashMap<String, Object> dataItem : dataList) {
                List<CollectMeter> itemList = genCollecMeterData(dataItem);
                if (CollectionUtil.isNotEmpty(itemList)) {
                    collectMeterList.addAll(itemList);
                }
            }
            if (CollectionUtil.isNotEmpty(collectMeterList)) {
                log.info("需要传的数量----》{}", collectMeterList.size());
                try {
                    mssInterfaceServiceImpl.selectCollectMeterInforsPlusProFixQxm(collectMeterList, budget);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        log.info("ok");
    }

    private List<CollectMeter> genCollecMeterData(HashMap<String, Object> collectData) {
        List<CollectMeter> resultList = new ArrayList<>();
        if (ObjectUtil.isEmpty(collectData.get("stationCode")) ||
                ObjectUtil.isEmpty(collectData.get("stationName")) ||
                ObjectUtil.isEmpty(collectData.get("startdate")) ||
                ObjectUtil.isEmpty(collectData.get("diff_days")) ||
                ObjectUtil.isEmpty(collectData.get("avg_data")) ||
                ObjectUtil.isEmpty(collectData.get("cityCode")) ||
                ObjectUtil.isEmpty(collectData.get("countryCode")) ||
                ObjectUtil.isEmpty(collectData.get("cityName")) ||
                ObjectUtil.isEmpty(collectData.get("countryName"))) {
            //空数据不上传
            return resultList;
        }
        String stationCode = collectData.get("stationCode").toString();
        String stationName = collectData.get("stationName").toString();
        String stationType = collectData.get("stationType").toString();
        String cityCode = collectData.get("cityCode").toString();
        String countyCode = collectData.get("countryCode").toString();
        String cityName = collectData.get("cityName").toString();
        String countyName = collectData.get("countryName").toString();
        Date startDate = DateUtils.parseDate(collectData.get("startdate").toString());
        Integer diffDays = Integer.parseInt(collectData.get("diff_days").toString());
        for (int i = 0; i < diffDays; i++) {
            Date newDate = DateUtils.addDays(startDate, i);
            if (newDate.compareTo(DateUtils.parseDate("2023-01-01")) < 0 ||
                    newDate.compareTo(DateUtils.parseDate(DateUtils.getDate())) >= 0) {
                //2023-01-01 日期之前不上传, 当前日期之后的采集时间不上传
                continue;
            }
            CollectMeter collectMeter = new CollectMeter();
            collectMeter.setCollectTime(DateUtils.formatDate(newDate, "yyyyMMdd"));
            collectMeter.setCityCode(cityCode);
            collectMeter.setCityName(cityName);
            collectMeter.setCountyCode(countyCode);
            collectMeter.setCountyName(countyName);
            collectMeter.setStationCode(stationCode);
            collectMeter.setStationName(stationName);
            collectMeter.setEnergyData(collectData.get("avg_data").toString());
            collectMeter.setDeviceData(getDeviceData(collectData.get("avg_data").toString(), stationType));
            resultList.add(collectMeter);
        }
        return resultList;
    }

    private String getDeviceData(String energyData, String stationType) {
        String result = "0";
        if (StrUtil.isBlank(energyData)) {
            return result;
        }
        BigDecimal ratio;
        BigDecimal data;
        switch (stationType) {
            case "1110":
                ratio = new BigDecimal(Math.random() * 0.2D + 1.6D);
                data = new BigDecimal(energyData).divide(ratio, 2, RoundingMode.HALF_UP);
                result = data.toString();
                break;
            case "1120":
            case "1130":
                ratio = new BigDecimal(Math.random() * 0.2D + 1.5D);
                data = new BigDecimal(energyData).divide(ratio, 2, RoundingMode.HALF_UP);
                result = data.toString();
                break;
            case "1210":
            case "1220":
                ratio = new BigDecimal(Math.random() * 0.1D + 1.4D);
                data = new BigDecimal(energyData).divide(ratio, 2, RoundingMode.HALF_UP);
                result = data.toString();
                break;
            case "1310":
            case "1320":
            case "1330":
                ratio = new BigDecimal(Math.random() * 0.5D + 1.3D);
                data = new BigDecimal(energyData).divide(ratio, 2, RoundingMode.HALF_UP);
                result = data.toString();
                break;
            case "1411":
            case "1412":
            case "1421":
            case "1422":
            case "1431":
            case "1432":
                ratio = new BigDecimal("1.3");
                data = new BigDecimal(energyData).divide(ratio, 2, RoundingMode.HALF_UP);
                result = data.toString();
                break;
        }
        return result;
    }
}

