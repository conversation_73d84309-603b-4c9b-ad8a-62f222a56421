package com.sccl.modules.business.stationreportwhitelist.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.stationreportwhitelist.dto.FindWhitelistIsNotAddedQuery;
import com.sccl.modules.business.stationreportwhitelist.dto.PowerAmmeterorprotocolQuery;
import com.sccl.modules.business.stationreportwhitelist.service.PowerAmmeterorprotocolService;
import com.sccl.modules.business.stationreportwhitelist.vo.PowerAmmeterorprotocolVO;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2024/5/8 16:25
 * @describe 电表管理
 */
@RestController
@AllArgsConstructor
@RequestMapping("/powerAmmeterorprotocol")
public class PowerAmmeterorprotocolController extends BaseController {

    private final PowerAmmeterorprotocolService powerAmmeterorprotocolService;

    /**
     * 一站多表 查出电表列表
     */
    @GetMapping(value = "/findWhitelistIsNotAddedByStation")
    public AjaxResult findWhitelistIsNotAddedByStation(@Valid FindWhitelistIsNotAddedQuery query) {
        return powerAmmeterorprotocolService.findWhitelistIsNotAddedByStation(query);
    }

    /**
     * 一表多站电表列表
     */
    @GetMapping(value = "/oneTableMultiStationList")
    public TableDataInfo oneTableMultiStationList(Page<PowerAmmeterorprotocolVO> page, PowerAmmeterorprotocolQuery query) {
        IPage<PowerAmmeterorprotocolVO> iPage = powerAmmeterorprotocolService.oneTableMultiStationList(page, query);
        return getDataTable(iPage.getRecords(), iPage.getTotal());
    }

    /**
     * 单价-查出电表
     */
    @GetMapping(value = "/excludeWhitelistList")
    public TableDataInfo listOfUnitPrices(Page<PowerAmmeterorprotocolVO> page, PowerAmmeterorprotocolQuery query) {
        IPage<PowerAmmeterorprotocolVO> iPage = powerAmmeterorprotocolService.excludeWhitelistList(page, query);
        return getDataTable(iPage.getRecords(), iPage.getTotal());
    }
}
