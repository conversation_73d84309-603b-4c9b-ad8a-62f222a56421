package com.sccl.modules.business.trunkstation.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;


/**
 * 铁塔站址基础数据表 trunk_station
 * 
 * <AUTHOR>
 * @date 2023-05-10
 */
public class TrunkStation extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
    /** 铁塔端ID */
    private String towerId;
    /** 地市 */
    private String city;
    /** 区县 */
    private String county;
    /** 电表表号 */
    private String dbbh;
    /** 户号 */
    private String hh;
    /** 对外结算类型 */
    private String dwjslx;
    /** 转供电协议单价 */
    private String zgdxydj;
    /** 分摊比例_联通 */
    private String ftblLt;
    /** 分摊比例_电信 */
    private String ftblDx;
    /** 分摊比例_移动 */
    private String ftblYd;
    /** 协议起止日期 */
    private String xyqzrq;
    /** 站址类型 */
    private String zzlx;
    /** 站址编码_铁塔 */
    private String zzbmTt;
    /** 站址名称_铁塔 */
    private String zzmcTt;
    /** 是否起租 */
    private String sfqz;
    /** 共享运营商名称 */
    private String gxyysmc;
    /** 站址地址 */
    private String zzdz;
    /** 附件 */
    private String fj;
    /** 0-一对一 1-一对多 2-多对一 */
    private Integer type;
    /** 能耗country编码 */
    private Long country;
    /** 能耗company编码 */
    private Long company;
    /** 流程id */
    private Long processId;
    /** 状态 0-有效 1-无效 */
    private Integer status;


	public void setTowerId(String towerId)
	{
		this.towerId = towerId;
	}

	public String getTowerId() 
	{
		return towerId;
	}

	public void setCity(String city)
	{
		this.city = city;
	}

	public String getCity() 
	{
		return city;
	}

	public void setCounty(String county)
	{
		this.county = county;
	}

	public String getCounty() 
	{
		return county;
	}

	public void setDbbh(String dbbh)
	{
		this.dbbh = dbbh;
	}

	public String getDbbh() 
	{
		return dbbh;
	}

	public void setHh(String hh)
	{
		this.hh = hh;
	}

	public String getHh() 
	{
		return hh;
	}

	public void setDwjslx(String dwjslx)
	{
		this.dwjslx = dwjslx;
	}

	public String getDwjslx() 
	{
		return dwjslx;
	}

	public void setZgdxydj(String zgdxydj)
	{
		this.zgdxydj = zgdxydj;
	}

	public String getZgdxydj() 
	{
		return zgdxydj;
	}

	public void setFtblLt(String ftblLt)
	{
		this.ftblLt = ftblLt;
	}

	public String getFtblLt() 
	{
		return ftblLt;
	}

	public void setFtblDx(String ftblDx)
	{
		this.ftblDx = ftblDx;
	}

	public String getFtblDx() 
	{
		return ftblDx;
	}

	public void setFtblYd(String ftblYd)
	{
		this.ftblYd = ftblYd;
	}

	public String getFtblYd() 
	{
		return ftblYd;
	}

	public void setXyqzrq(String xyqzrq)
	{
		this.xyqzrq = xyqzrq;
	}

	public String getXyqzrq() 
	{
		return xyqzrq;
	}

	public void setZzlx(String zzlx)
	{
		this.zzlx = zzlx;
	}

	public String getZzlx() 
	{
		return zzlx;
	}

	public void setZzbmTt(String zzbmTt)
	{
		this.zzbmTt = zzbmTt;
	}

	public String getZzbmTt() 
	{
		return zzbmTt;
	}

	public void setZzmcTt(String zzmcTt)
	{
		this.zzmcTt = zzmcTt;
	}

	public String getZzmcTt() 
	{
		return zzmcTt;
	}

	public void setSfqz(String sfqz)
	{
		this.sfqz = sfqz;
	}

	public String getSfqz() 
	{
		return sfqz;
	}

	public void setGxyysmc(String gxyysmc)
	{
		this.gxyysmc = gxyysmc;
	}

	public String getGxyysmc() 
	{
		return gxyysmc;
	}

	public void setZzdz(String zzdz)
	{
		this.zzdz = zzdz;
	}

	public String getZzdz() 
	{
		return zzdz;
	}

	public void setFj(String fj)
	{
		this.fj = fj;
	}

	public String getFj() 
	{
		return fj;
	}

	public void setType(Integer type)
	{
		this.type = type;
	}

	public Integer getType() 
	{
		return type;
	}

	public void setCountry(Long country)
	{
		this.country = country;
	}

	public Long getCountry() 
	{
		return country;
	}

	public void setCompany(Long company)
	{
		this.company = company;
	}

	public Long getCompany() 
	{
		return company;
	}


	public void setProcessId(Long processId)
	{
		this.processId = processId;
	}

	public Long getProcessId() 
	{
		return processId;
	}

	public void setStatus(Integer status)
	{
		this.status = status;
	}

	public Integer getStatus() 
	{
		return status;
	}


	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("towerId", getTowerId())
            .append("city", getCity())
            .append("county", getCounty())
            .append("dbbh", getDbbh())
            .append("hh", getHh())
            .append("dwjslx", getDwjslx())
            .append("zgdxydj", getZgdxydj())
            .append("ftblLt", getFtblLt())
            .append("ftblDx", getFtblDx())
            .append("ftblYd", getFtblYd())
            .append("xyqzrq", getXyqzrq())
            .append("zzlx", getZzlx())
            .append("zzbmTt", getZzbmTt())
            .append("zzmcTt", getZzmcTt())
            .append("sfqz", getSfqz())
            .append("gxyysmc", getGxyysmc())
            .append("zzdz", getZzdz())
            .append("fj", getFj())
            .append("type", getType())
            .append("country", getCountry())
            .append("company", getCompany())
            .append("createTime", getCreateTime())
            .append("processId", getProcessId())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
