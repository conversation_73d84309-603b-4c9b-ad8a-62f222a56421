package com.sccl.modules.business.equipmentdict.mapper;

import com.sccl.modules.business.equipmentdict.domain.EquipmentDict;
import com.sccl.framework.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 铁塔站址设备字典 数据层
 *
 * <AUTHOR>
 * @date 2022-08-09
 */
public interface EquipmentDictMapper extends BaseMapper<EquipmentDict> {
    /**
     * 查询指定组ID的最新的一条记录
     *
     * @param groupId 设备组ID
     * @return com.sccl.modules.business.equipmentdict.domain.EquipmentDict
     * <AUTHOR>
     * @date 2022/8/9 15:39
     */
    EquipmentDict getLatest(@Param("groupId") long groupId);

    /**
     * 批量获取指定GroupId的所有最新版本的设备
     *
     * @param groupIds 要查询的组Id
     * @return java.util.List<com.sccl.modules.business.equipmentdict.domain.EquipmentDict>
     * <AUTHOR>
     * @date 2022/8/10 10:20
     */
    List<EquipmentDict> getLatests(List<Long> groupIds);

    /**
     * 获取所有设备表，该方法返回的设备是最新版本的设备
     *
     * @return java.util.List<com.sccl.modules.business.equipmentdict.domain.EquipmentDict>
     * <AUTHOR> Yongxiang
     * @date 2022/8/9 16:37
     */
    List<EquipmentDict> listAll();

    List<EquipmentDict> listAllConditional(EquipmentDict equipmentDict);

    /**
     * 获取某个设备的所有历史版本
     *
     * @param groupId 设备组Id
     * @return java.util.List<com.sccl.modules.business.equipmentdict.domain.EquipmentDict>
     * <AUTHOR> Yongxiang
     * @date 2022/8/9 16:39
     */
    List<EquipmentDict> listHistory(@Param("groupId") long groupId);

    /**
     * 逻辑删除某设备的所有信息
     *
     * @param groupId 组Id
     * @return int
     * <AUTHOR> Yongxiang
     * @date 2022/8/9 17:18
     */
    int deleteByGroupId(@Param("groupId") long groupId);


}