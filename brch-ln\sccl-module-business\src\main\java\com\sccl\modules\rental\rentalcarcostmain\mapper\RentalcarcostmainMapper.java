package com.sccl.modules.rental.rentalcarcostmain.mapper;

import com.sccl.modules.rental.rentalcarcostmain.domain.Rentalcarcostmain;
import com.sccl.framework.mapper.BaseMapper;

import java.util.List;
import java.util.Map;

/**
 * 租赁费用主 数据层
 * 
 * <AUTHOR>
 * @date 2019-08-29
 */
public interface RentalcarcostmainMapper extends BaseMapper<Rentalcarcostmain>
{

    Rentalcarcostmain selectById(Long id);

    /**
     * @Description: 通过id数组批量查询数据
     * @author: dongk
     * @date: 2019/9/5
     * @param:
     * @return:
     */
    List<Rentalcarcostmain> selectByIds(String[] ids);
    List<Map<String,Object>> statistical(Map<String,Object> params);

}