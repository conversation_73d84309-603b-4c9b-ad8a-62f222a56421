package com.sccl.modules.rental.rentalsupplier.mapper;

import com.sccl.modules.rental.rentalsupplier.domain.Rentalsupplier;
import com.sccl.framework.mapper.BaseMapper;

/**
 * 供应商 数据层
 * 
 * <AUTHOR>
 * @date 2019-08-23
 */
public interface RentalsupplierMapper extends BaseMapper<Rentalsupplier>
{

    /**
     * @Description: 通过编码查询供应商对象
     * @author: dongk
     * @date: 2019/8/26
     * @param:
     * @return:
     */
    Rentalsupplier selectByLifnr(String lifnr);

}