package com.sccl.modules.business.quotaconfig.service;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.business.alertcontrol.domain.AlertControl;
import com.sccl.modules.business.quotaconfig.mapper.QuotaConfigMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sccl.modules.business.quotaconfig.domain.QuotaConfig;

import java.util.ArrayList;
import java.util.List;


/**
 * 基站定额分公司设置 服务层实现
 * 
 * <AUTHOR>
 * @date 2023-06-07
 */
@Service
public class QuotaConfigServiceImpl extends BaseServiceImpl<QuotaConfig> implements IQuotaConfigService
{
    @Autowired
    QuotaConfigMapper quotaConfigMapper;
    public String selectOrgAllChidren(QuotaConfig quotaConfig) {
        //先取出所有分公司子ID
        String ids = quotaConfigMapper.selectIds(quotaConfig);
        return ids;
    }
    public String selectCountryAllChidren(QuotaConfig quotaConfig) {
        //先取出所有分公司子ID
        String ids = quotaConfigMapper.selectCountryIds(quotaConfig);
        return ids;
    }
    public List<QuotaConfig> selectListCustomized(QuotaConfig quotaConfig, String ids, String countryIds){
        List<String> listIds = new ArrayList<String>();
        if(ids!=""){
            ids=ids.substring(1);
            String[] temp = ids.split(",");
            for(int i=0;i<temp.length;i++){
                listIds.add(String.valueOf(temp[i]));
            }
        }
        List<String> listCountryIds = new ArrayList<String>();
        if(!"".equals(countryIds)){
            countryIds=countryIds.substring(1);
            String[] temp = countryIds.split(",");
            for(int i=0;i<temp.length;i++){
                listCountryIds.add(String.valueOf(temp[i]));
            }
        }
        quotaConfig.setListIds(listIds);
        quotaConfig.setCountryIds(listCountryIds);
        return quotaConfigMapper.selectListCustomized(quotaConfig);
    }
}
