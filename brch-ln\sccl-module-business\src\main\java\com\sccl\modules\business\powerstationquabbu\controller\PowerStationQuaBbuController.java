package com.sccl.modules.business.powerstationquabbu.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.business.powerstationquabbu.domain.PowerStationQuaBbu;
import com.sccl.modules.business.powerstationquabbu.service.IPowerStationQuaBbuService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 站址bbu能耗指标 信息操作处理
 * 
 * <AUTHOR>
 * @date 2021-09-14
 */
@RestController
@RequestMapping("/business/powerStationQuaBbu")
public class PowerStationQuaBbuController extends BaseController
{
    private String prefix = "business/powerStationQuaBbu";
	
	@Autowired
	private IPowerStationQuaBbuService powerStationQuaBbuService;
	
	@RequiresPermissions("business:powerStationQuaBbu:view")
	@GetMapping()
	public String powerStationQuaBbu()
	{
	    return prefix + "/powerStationQuaBbu";
	}
	
	/**
	 * 查询站址bbu能耗指标列表
	 */
	@RequiresPermissions("business:powerStationQuaBbu:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(PowerStationQuaBbu powerStationQuaBbu)
	{
		startPage();
        List<PowerStationQuaBbu> list = powerStationQuaBbuService.selectList(powerStationQuaBbu);
		return getDataTable(list);
	}
	
	/**
	 * 新增站址bbu能耗指标
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存站址bbu能耗指标
	 */
	@RequiresPermissions("business:powerStationQuaBbu:add")
	@Log(title = "站址bbu能耗指标", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody PowerStationQuaBbu powerStationQuaBbu)
	{		
		return toAjax(powerStationQuaBbuService.insert(powerStationQuaBbu));
	}

	/**
	 * 修改站址bbu能耗指标
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		PowerStationQuaBbu powerStationQuaBbu = powerStationQuaBbuService.get(id);

		Object object = JSONObject.toJSON(powerStationQuaBbu);

        return this.success(object);
	}
	
	/**
	 * 修改保存站址bbu能耗指标
	 */
	@RequiresPermissions("business:powerStationQuaBbu:edit")
	@Log(title = "站址bbu能耗指标", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody PowerStationQuaBbu powerStationQuaBbu)
	{		
		return toAjax(powerStationQuaBbuService.update(powerStationQuaBbu));
	}
	
	/**
	 * 删除站址bbu能耗指标
	 */
	@RequiresPermissions("business:powerStationQuaBbu:remove")
	@Log(title = "站址bbu能耗指标", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(powerStationQuaBbuService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看站址bbu能耗指标
     */
    @RequiresPermissions("business:powerStationQuaBbu:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		PowerStationQuaBbu powerStationQuaBbu = powerStationQuaBbuService.get(id);

        Object object = JSONObject.toJSON(powerStationQuaBbu);

        return this.success(object);
    }

}
