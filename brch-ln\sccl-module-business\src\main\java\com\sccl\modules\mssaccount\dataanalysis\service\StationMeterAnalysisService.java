package com.sccl.modules.mssaccount.dataanalysis.service;

import cn.hutool.core.lang.Dict;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.mssaccount.dataanalysis.dto.StatisticsListDTO;
import com.sccl.modules.mssaccount.dataanalysis.dto.StatisticsMeterListDTO;
import com.sccl.modules.mssaccount.dataanalysis.vo.PowerStationInfoDetailListVO;
import com.sccl.modules.mssaccount.dataanalysis.vo.PowerStationInfoMeterDetailListVO;

import java.util.List;

public interface StationMeterAnalysisService {

    TableDataInfo statisticsList(StatisticsListDTO dto);

    List<PowerStationInfoDetailListVO> stationStatisticsDetailList(StatisticsListDTO dto);

    Dict statisticsMeterList(StatisticsMeterListDTO dto);

    List<PowerStationInfoMeterDetailListVO> stationStatisticsMeterDetailList(StatisticsMeterListDTO dto);
}
