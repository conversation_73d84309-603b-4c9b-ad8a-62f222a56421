package com.sccl.modules.business.poweraudit.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.enrising.dcarbon.manage.BaseEntity;
import com.sccl.framework.aspectj.lang.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-22
 */
@TableName("power_audit")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PowerAuditEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 地市
     */
    @TableField("city")
    private String city;

    /**
     * 时间（月）
     */
    @TableField("month")
    private String month;

    /**
     * 电表协议编号
     */
    @TableField("ammeterid")
    private String ammeterid;

    /**
     * 局站编码
     */
    @TableField("stationcode")
    private String stationcode;

    /**
     * 铁塔站址编码
     */
    @TableField("tower_site_code")
    private String towerSiteCode;

    /**
     * 报账单ID
     */
    @TableField("mss_account_id")
    private String mssAccountId;

    /**
     * 报账申请日期
     */
    @TableField("apply_time")
    private Date applyTime;

    /**
     * 一表多站/一站多表
     */
    @TableField("muti_jtlte_codes")
    private String mutiJtlteCodes;

    /**
     * 电表站址一致性
     */
    @TableField("address_consistence")
    private String addressConsistence;

    /**
     * 电表与支付对象一致性
     */
    @TableField("payment_consistence")
    private String paymentConsistence;

    /**
     * 重复交叉缴费
     */
    @TableField("repeat")
    private String repeat;

    /**
     * 台账电量合理性
     */
    @TableField("electricity_rationality")
    private String electricityRationality;

    /**
     * 电表度数连续性
     */
    @TableField("electricity_continuity")
    private String electricityContinuity;

    /**
     * 沉默电表
     */
    @TableField("electricity_meter")
    private String electricityMeter;

    /**
     * 台账周期异常
     */
    @TableField("periodic_anomaly")
    private String periodicAnomaly;

    /**
     * 独享站分摊比例准确性
     */
    @TableField("exclusive_accuracy")
    private String exclusiveAccuracy;

    /**
     * 共享站分摊比例准确性
     */
    @TableField("share_accuracy")
    private String shareAccuracy;

    /**
     * 台账日均耗电量合理性
     */
    @TableField("consume_continuity")
    private String consumeContinuity;

    /**
     * 台账日均电量波动合理性
     */
    @TableField("fluctuate_continuity")
    private String fluctuateContinuity;

    /**
     * 电价合理性
     */
    @TableField("electricity_prices")
    private String electricityPrices;

    /**
     * 台账周期连续性
     */
    @TableField("reimbursement_cycle")
    private String reimbursementCycle;

    /**
     * 分摊比例一致性
     */
    @TableField("consistency_proportion")
    private String consistencyProportion;


    /**
     * 1:成功，0:失败
     */
    @TableField("if_qk_success")
    private Integer ifQkSuccess;

    /**
     * 1:成功，0:失败
     */
    @TableField("if_success")
    private Integer ifSuccess;


    /**
     * 1:基站类，0:非基站
     */
    @TableField("site_type")
    private Integer siteType;

    /**
     * 稽核时间
     */
    @TableField("audit_time")
    private Date auditTime;

    /**
     *  上次稽核时间
     */
    private String auditTimeLast;


    /**
     * 台账ID
     */
    @TableField("pcid")
    private String pcid;

    /**
     * 区县
     */
    @Excel(name = "区县")
    private String countyCompanies;

    /**
     * 运营分局
     */
//    @Excel(name = "运营分局")
    private String operationsBranch;

    /**
     * 台账期号
     */
//    @Excel(name = "台账期号")
    private String ledgerPeriod;

    /**
     * 地市编码
     */
//    @Excel(name = "运营分局")
    private String cityCode;

    /**
     * 区县编码
     */
//    @Excel(name = "台账期号")
    private String countyCompaniesCode;

    private String createTimeStr;

    /**
     * 稽核后查询结果的唯一key
     */
    private String ctgKey;

    /**
     * 电表ID
     */
    @TableField("pam_id")
    private Long pamId;
}