package com.sccl.modules.business.stationequipment.service;

import com.sccl.common.lang.StringUtils;
import com.sccl.common.utils.BigDecimlUtil;
import com.sccl.common.utils.ObjectStoreUtils;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.utils.ExcelUtil;
import com.sccl.framework.utils.FileUploadUtils;
import com.sccl.modules.autojob.util.convert.DateUtils;
import com.sccl.modules.business.basestation.domain.StationGrade;
import com.sccl.modules.business.basestation.mapper.StationGradeMapper;
import com.sccl.modules.business.basestation.service.IStationGradeService;
import com.sccl.modules.business.budget.domain.PageBean;
import com.sccl.modules.business.equipmentdict.domain.EquipmentDict;
import com.sccl.modules.business.equipmentdict.mapper.EquipmentDictMapper;
import com.sccl.modules.business.stationequipment.domain.StationEquipment;
import com.sccl.modules.business.stationequipment.domain.TowerStationEquipment2;
import com.sccl.modules.business.stationequipment.mapper.StationEquipmentMapper;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;
import com.sccl.modules.system.attachments.domain.Attachments;
import com.sccl.modules.system.attachments.mapper.AttachmentsMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;


/**
 * 铁塔站址设备 服务层实现
 *
 * <AUTHOR> Yongxiang
 * @date 2022-08-09
 */
@Service
@Slf4j
public class StationEquipmentServiceImpl extends BaseServiceImpl<StationEquipment> implements IStationEquipmentService {
    @Autowired(required = false)
    private StationEquipmentMapper mapper;
    @Autowired
    private StationGradeMapper stationGradeMapper;

    @Autowired(required = false)
    private EquipmentDictMapper equipmentDictMapper;

    @Autowired(required = false)
    private AttachmentsMapper attachmentsMapper;
    @Autowired
    private IStationGradeService gradeService;

    public static void main(String[] args) {
        LocalDate.parse("2020-10-01");
        String year = "202210".substring(0, 4);
        String month = "202210".substring(4);
        System.out.println(year);
        System.out.println(month);
    }

    @Override
    public StationEquipment getLatestByGroupId(long groupId) {
        return mapper.getLatest(groupId);
    }

    @Override
    public List<TowerStationEquipment2> getAllLatestByGroupId(List<Long> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return Collections.emptyList();
        }
        return mapper.getLatests(groupIds);
    }

    @Override
    public List<StationEquipment> listLatest() {
        return mapper.listAll();
    }

    @Override
    public List<TowerStationEquipment2> listLatestConditional(TowerStationEquipment2 stationEquipment) {
        return mapper.listAllConditional(stationEquipment);
    }

    @Override
    public List<StationEquipment> listHistoryByGroupId(long groupId) {
        return mapper.listHistory(groupId);
    }

    @Override
    public int deleteByGroupId(long groupId) {
        return mapper.deleteByGroupId(groupId);
    }

    @Override
    public Attachments exportExcel(List<TowerStationEquipment2> stationEquipments, Map<String, String> columnMap,
                                   Map<String, String> promptMap) {
        ExcelUtil<TowerStationEquipment2> excelUtil = new ExcelUtil<>(TowerStationEquipment2.class);
        String fileName = "站址设备信息表" + DateUtils.getTime().replace(" ", "_");
        try {
            InputStream inputStream = excelUtil.exportExcel(stationEquipments, columnMap, promptMap, "站址设备信息表");
            Attachments attachments = new Attachments();
            String filedIdName = FileUploadUtils.encodingFilename(fileName, ".xls");
            String filedId = filedIdName.substring(0, filedIdName.lastIndexOf("."));
            attachments.setFileName(fileName);
            attachments.setMongodbFileId(filedId);
            attachments.setBusiAlias("附件(站址设备信息导出)");
            attachments.setCategoryCode("file");
            attachments.setYear(Integer.valueOf(com.sccl.common.utils.DateUtils.getYear()));
            attachments.setDelFlag("0");
            attachments.setCollection("附件(站址设备信息导出)");
            attachments.setBusiId(ShiroUtils.getUserId());
            if (ObjectStoreUtils.upload2OSS(filedId + ".xls", inputStream) && attachmentsMapper.insert(attachments) == 1) {
                String url = ObjectStoreUtils.shareFileByKey(filedId + ".xls");
                attachments.setUploadUrl(url);
                return attachments;
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public int insertList2(List<TowerStationEquipment2> content) {
        return mapper.insertList2(content);
    }

    @Override
    public List<TowerStationEquipment2> selectList1(TowerStationEquipment2 towerStationEquipment2s) {
        return mapper.selectList1(towerStationEquipment2s);
    }

    @Override
    public List<TowerStationEquipment2> selectList2(TowerStationEquipment2 towerStationEquipment2s) {
        return mapper.selectList1(towerStationEquipment2s);
    }

    @Override
    public List<TowerStationEquipment2> selectList3(TowerStationEquipment2 towerStationEquipment2s) {
        return mapper.selectList3(towerStationEquipment2s);
    }

    @Override
    public Long selectList3num(TowerStationEquipment2 towerStationEquipment2s) {
        return mapper.selectList3num(towerStationEquipment2s);
    }

    @Override
    public BigDecimal calcActual(TowerStationEquipment2 towerStationEquipment2) {
        return BigDecimlUtil.add(
                towerStationEquipment2.getPowerAauRru1(),
                towerStationEquipment2.getPowerBbu1(),
                towerStationEquipment2.getPowerAauRru2(),
                towerStationEquipment2.getPowerBbu2(),
                towerStationEquipment2.getPowerPrru1(),
                towerStationEquipment2.getPowerPrru2(),
                towerStationEquipment2.getPowerC(),
                towerStationEquipment2.getPowerRemote(),
                towerStationEquipment2.getPowerNear(),
                towerStationEquipment2.getPowerWei(),
                towerStationEquipment2.getPowerIpran(),
                towerStationEquipment2.getPowerG(),
                towerStationEquipment2.getPowerTrans(),
                towerStationEquipment2.getPowerSwitch(),
                towerStationEquipment2.getPowerDomain(),
                towerStationEquipment2.getPowerOther()
        );
    }

    @Override
    public BigDecimal caclStandardBefore(TreeSet<StationEquipment> set, long days) {
        // 从铁塔设备字典表 取到 最新 设备信息
        List<EquipmentDict> equipmentDicts = equipmentDictMapper.listAll();
        //交集 （设备厂家，设备型号，设备类型）
        BigDecimal reduce = equipmentDicts.stream().filter(
                equipmentDict -> {
                    return compareProperty(equipmentDict, set, days);
                }
        ).map(
                equipmentDict -> equipmentDict.getEnergyCostPerDay()
        ).reduce(
                BigDecimal.ZERO, BigDecimal::add
        );

        return reduce;


    }

    @Override
    public List<TowerStationEquipment2> importExcel(String sheetName, InputStream input) throws IOException,
            InvalidFormatException {

        List<TowerStationEquipment2> list = new ArrayList<>();

        Workbook workbook = WorkbookFactory.create(input);
        Sheet sheet = workbook.getSheet(sheetName);
        if (StringUtils.isNotEmpty(sheetName)) {
            // 如果指定sheet名,则取指定sheet中的内容.
            sheet = workbook.getSheet(sheetName);
        }
        if (sheet == null) {
            // 如果传入的sheet名不存在则默认指向第1个sheet.
            sheet = workbook.getSheetAt(0);
        }
        int rows = sheet.getPhysicalNumberOfRows();

        if (rows > 0) {
            Row row0 = sheet.getRow(0);
            for (int i = 1; i < rows; i++) {
                TowerStationEquipment2 tower = new TowerStationEquipment2();
                Row rowo = sheet.getRow(0);
                Row row = sheet.getRow(i);
                int cellNum = sheet.getRow(0).getPhysicalNumberOfCells();
                for (int j = 0; j < cellNum; j++) {
                    Cell cello = rowo.getCell(j);
                    Cell cell = row.getCell(j);
                    if (cell == null) {
                        continue;
                    } else {
                        String cellValue = cello.getStringCellValue();
                        String value = cell.getStringCellValue();
                        if ("省份标识".equals(cellValue)) {
                            tower.setProvince(value);
                        }
                        if ("月账期".equals(cellValue)) {
                            String year = value.substring(0, 4);
                            String month = value.substring(4);
                            tower.setMonthaccount(LocalDate.parse(year + "-" + month + "-01"));
                        }
                        if ("省份名称".equals(cellValue)) {
                            tower.setProvincename(value);
                        }
                        if ("本地网标识".equals(cellValue)) {
                            tower.setNetworkLocal(Integer.valueOf(value));
                        }
                        if ("本地网名称".equals(cellValue)) {
                            tower.setNetworkLocalName(value);
                        }
                        if ("市局组织编码".equals(cellValue)) {
                            tower.setCity(value);
                        }
                        if ("站址编码-报账".equals(cellValue)) {
                            tower.setMssStationCode(value);
                        }
                        if ("站址编码-旧".equals(cellValue)) {
                            tower.setOldStationCode(value);
                        }
                        if ("铁塔站址编码".equals(cellValue)) {
                            tower.setTowerStationCode(value);
                        }
                        if ("机房类型".equals(cellValue)) {
                            tower.setTypeComputer(value);
                        }
                        if ("报账单号".equals(cellValue)) {
                            tower.setMssCode(value);
                        }
                        if ("电表编码".equals(cellValue)) {
                            tower.setCodeMeter(value);
                        }
                        if ("填报人名称".equals(cellValue)) {
                            tower.setNameFull(value);
                        }
                        if ("开始日期".equals(cellValue)) {
                            String year = value.substring(0, 4);
                            String month = value.substring(4, 6);
                            String day = value.substring(6, 8);
                            tower.setTimeStart(LocalDate.parse(year + "-" + month + "-" + day));
                        }
                        if ("结束日期".equals(cellValue)) {
                            String year = value.substring(0, 4);
                            String month = value.substring(4, 6);
                            String day = value.substring(6, 8);
                            tower.setTimeEnd(LocalDate.parse(year + "-" + month + "-" + day));
                        }
                        if ("报账金额".equals(cellValue)) {
                            tower.setMssaccount(new BigDecimal(value));
                        }
                        if ("报账电量".equals(cellValue)) {
                            tower.setPowerMss(new BigDecimal(value));
                        }
                        if ("站址稽核电量-全部".equals(cellValue)) {
                            tower.setPowerStationAudit1(new BigDecimal(value));
                        }
                        if ("报账与能耗差异-全部".equals(cellValue)) {
                            tower.setDiffMssFornh1(value);
                        }
                        if ("站址稽核电量-不含CDMA".equals(cellValue)) {
                            tower.setPowerStationAudit2(new BigDecimal(value));
                        }
                        if ("报账与能耗差异-不含CDMA%".equals(cellValue)) {
                            tower.setDiffMssFornh2(value);
                        }
                        if ("差异区间".equals(cellValue)) {
                            tower.setDiffRange(value);
                        }
                        if ("AAU/RRU数量（5G)".equals(cellValue)) {
                            tower.setQuantityAauRru1(Integer.valueOf(value));
                        }
                        if ("BBU数量(5G)".equals(cellValue)) {
                            tower.setQuantityBbu1(Integer.valueOf(value));
                        }
                        if ("AAU/RRU数量（4G)".equals(cellValue)) {
                            tower.setQuantityAauRru2(Integer.valueOf(value));
                        }
                        if ("BBU数量(4G)".equals(cellValue)) {
                            tower.setQuantityBbu2(Integer.valueOf(value));
                        }
                        if ("PRRU数量(5G)".equals(cellValue)) {
                            tower.setQuantityPrru1(Integer.valueOf(value));
                        }
                        if ("PRRU数量(4G)".equals(cellValue)) {
                            tower.setQuantityPrru1(Integer.valueOf(value));
                        }

                        if ("C网设备数量".equals(cellValue)) {
                            tower.setQuantityC(Double.valueOf(value));
                        }

                        if ("远端直放站数量".equals(cellValue)) {
                            tower.setQuantityRemote(Double.valueOf(value));
                        }

                        if ("近端直放站数量".equals(cellValue)) {
                            tower.setQuantityNear(Double.valueOf(value));
                        }
                        if ("微波数量".equals(cellValue)) {
                            tower.setQuantityWei(Double.valueOf(value));
                        }
                        if ("IPRAN数量".equals(cellValue)) {
                            tower.setQuantityIppan(Double.valueOf(value));
                        }
                        if ("皮站数量".equals(cellValue)) {
                            tower.setQuantityP(Double.valueOf(value));
                        }
                        if ("光接入数量".equals(cellValue)) {
                            tower.setQuantityG(Double.valueOf(value));
                        }
                        if ("传输设备数量".equals(cellValue)) {
                            tower.setQuantityTrans(Double.valueOf(value));
                        }
                        if ("交换机数量".equals(cellValue)) {
                            tower.setQuantitySwitch(Double.valueOf(value));
                        }
                        if ("城域网数量".equals(cellValue)) {
                            tower.setPowerDomain(new BigDecimal(value));
                        }
                        if ("其它设备数量".equals(cellValue)) {
                            tower.setQuantityOther(Double.valueOf(value));
                        }
                        if ("AAU/RRU能耗（5G)".equals(cellValue)) {
                            tower.setPowerAauRru1(new BigDecimal(value));
                        }
                        if ("BBU能耗(5G)".equals(cellValue)) {
                            tower.setPowerBbu1(new BigDecimal(value));
                        }
                        if ("AAU/RRU能耗（4G)".equals(cellValue)) {
                            tower.setPowerAauRru2(new BigDecimal(value));
                        }
                        if ("BBU能耗(5G)".equals(cellValue)) {
                            tower.setPowerBbu2(new BigDecimal(value));
                        }
                        if ("PRRU能耗(5G)".equals(cellValue)) {
                            tower.setPowerPrru1(new BigDecimal(value));
                        }
                        if ("PRRU能耗(4G)".equals(cellValue)) {
                            tower.setPowerPrru2(new BigDecimal(value));
                        }
                        if ("C网设备能耗".equals(cellValue)) {
                            tower.setPowerC(new BigDecimal(value));
                        }
                        if ("远端直放站能耗".equals(cellValue)) {
                            tower.setPowerRemote(new BigDecimal(value));
                        }
                        if ("近端直放站能耗".equals(cellValue)) {
                            tower.setPowerNear(new BigDecimal(value));
                        }
                        if ("微波能耗".equals(cellValue)) {
                            tower.setPowerWei(new BigDecimal(value));
                        }
                        if ("IPRAN能耗".equals(cellValue)) {
                            tower.setPowerIpran(new BigDecimal(value));
                        }
                        if ("光接入能耗".equals(cellValue)) {
                            tower.setPowerG(new BigDecimal(value));
                        }
                        if ("传输设备能耗".equals(cellValue)) {
                            tower.setPowerTrans(new BigDecimal(value));
                        }
                        if ("交换机能耗".equals(cellValue)) {
                            tower.setPowerSwitch(new BigDecimal(value));
                        }
                        if ("城域网能耗".equals(cellValue)) {
                            tower.setPowerDomain(new BigDecimal(value));
                        }
                        if ("其它设备能耗".equals(cellValue)) {
                            tower.setPowerOther(new BigDecimal(value));
                        }
                        if ("PUE系数(主要)".equals(cellValue)) {
                            tower.setPueMain(new BigDecimal(value));
                        }
                        if ("PUE系数(机房)".equals(cellValue)) {
                            tower.setPueComputer(new BigDecimal(value));
                        }
                        if ("PUE系数(默认)".equals(cellValue)) {
                            tower.setPueDefault(new BigDecimal(value));
                        }
                        if ("是否打包报账站址".equals(cellValue)) {
                            tower.setPackFlag(
                                    "否".equals(value) ? 0 : 1
                            );
                        }
                        if ("是否有换充电业务".equals(cellValue)) {
                            tower.setChargingFlag(
                                    "否".equals(value) ? 0 : 1
                            );
                        }
                        if ("是否有智联业务".equals(cellValue)) {
                            tower.setZlFlag(
                                    "否".equals(value) ? 0 : 1
                            );
                        }
                        if ("是否报账周期异常".equals(cellValue)) {
                            tower.setExceptionMsFlag(
                                    "否".equals(value) ? 0 : 1
                            );
                        }
                        if ("是否需要核查".equals(cellValue)) {
                            tower.setValidateFlag(
                                    "否".equals(value) ? 0 : 1
                            );
                        }

                    }
                }
//                if(!StringUtils.isEmpty(tower.getStationaddrcode()) && !StringUtils.isEmpty(tower.getCity()) &&
//                !StringUtils.isEmpty(tower.getStationaddrname()) && !StringUtils.isEmpty(tower.getAddress())){
//                    if(StringUtils.isEmpty(tower.getProvice())){
//                        if("sc".equals(deployTo)){
//                            tower.setProvice("四川省");
//                        }else if("ln".equals(deployTo)){
//                            tower.setProvice("辽宁省");
//                        }
//                    }
                list.add(tower);
            }
        }
        return list;
    }

    @Override
    public int deleteRepeat() {
        return mapper.deleteRepeat();
    }

    @Override
    public int insertTowerInfo() {
        return mapper.insertTowerInfo();
    }

    @Override
    public int deleteAll() {
        return mapper.deleteAll();
    }

    @Override
    public int insertList2_Temporary(List newList) {
        return mapper.insertList2_Temporary(newList);
    }

    @Override
    public String selCity(String orgcode) {
        return mapper.selCity(orgcode);
    }

    @Override
    public String selCity2(String company) {
        return mapper.selCity2(company);
    }

    private boolean compareProperty(EquipmentDict equipmentDict, TreeSet<StationEquipment> set, long days) {
        return set.stream().filter(stationEquipment -> {
            int n1 = StringUtils.compare(stationEquipment.getFactory(), equipmentDict.getFactory());
            int n2 = StringUtils.compare(stationEquipment.getModel(), equipmentDict.getModel());
            int n3 = StringUtils.compare(stationEquipment.getType(), equipmentDict.getType());
            boolean b = (n2 == 0 && n3 == 0);
            if (b) {
                //将 设备字典表 对象 标准功耗 替换 标准功耗*站址个数
                BigDecimal energyCostPerDay = equipmentDict.getEnergyCostPerDay();
                Integer count = stationEquipment.getCount();

                boolean c = energyCostPerDay == null || count == null;
                BigDecimal multiply = c ? BigDecimal.ZERO :
                        energyCostPerDay.multiply(BigDecimal.valueOf(count)).multiply(BigDecimal.valueOf(days));

                equipmentDict.setEnergyCostPerDay(multiply);
            }
            return b;
        }).count() > 0;
    }

    @Override
    public BigDecimal caclStandard(TowerStationEquipment2 towerStationEquipment2) {
        // 从铁塔设备字典表 取到 最新 设备信息
        List<EquipmentDict> equipmentDicts = equipmentDictMapper.listAll();
        BigDecimal reduce = equipmentDicts.stream().map(
                equipmentDict -> {
                    return calcStandard1(equipmentDict, towerStationEquipment2);
                }
        ).reduce(BigDecimal.ZERO, BigDecimal::add);

        int num = towerStationEquipment2.getMonthaccount().getMonthValue();


        return reduce.multiply(BigDecimal.valueOf(num));


    }

    @Override
    public TowerStationEquipment2 getNewestStation(Long id) {
        return mapper.getNewestStation(id);
    }

    @Override
    public String generateGrade(MssAccountbill mssAccountbill) {
//        Long MsID = mssAccountbill.getId();
//        TowerStationEquipment2 newestStation = getNewestStation(MsID);
//        //最新设备标准
//        BigDecimal standard = caclStandard(newestStation);
//        //评级信息
//        TowerStationGrade grade = new TowerStationGrade();
//        grade.setBillid(MsID);
//        grade.setCalcpower();
//        grade.setStandardpower(standard);
//        grade.setDeviate();
        return null;
    }

    @Override
    //批量循环操作方法： objList要操作的数据，maxValue每次批量处理的条数
    public void batchSql(List<TowerStationEquipment2> objList, int maxValue) {
        LocalDate monthaccount = objList.get(0).getMonthaccount();
        int n0 = gradeService.updatesByMonthAccount(monthaccount);
        log.info("生成{}月站址评级表，预逻辑删除 条数{}", monthaccount.getMonthValue(), n0);

        List<TowerStationEquipment2> list = new ArrayList<>();
        List<StationGrade> grades = new ArrayList<>();
        int size = objList.size();
        int total = size / maxValue;
        if (size % maxValue != 0) {
            total += 1;
        }

        for (int i = 0; i < total; i++) {
            if (i == total - 1) {
                maxValue = size - (i * maxValue);
            }
            for (int j = 0; j < maxValue; j++) {
                list.add(objList.get(j));
            }

            //构建stationcode->orgcode的映射
            List<TowerStationEquipment2> orgcodes = stationGradeMapper.getorgcodeMap(list);
            Map<String, String> orgcodeMap = orgcodes.stream().filter(TowerStationEquipment2::orgCodeKey)
                                                  .collect(toMap(
                                                          TowerStationEquipment2::getMssStationCode,
                                                          TowerStationEquipment2::getOrgCode,
                                                          (item1, item2) -> {
                                                              return item1;
                                                          }
                                                  ));


            grades = list.stream().map(
                    towerStationEquipment2 -> {

                        StationGrade grade = new StationGrade();
                        grade.setGid(1L);
                        grade.setAuditType(2);
                        grade.setStationcode(towerStationEquipment2.getMssStationCode());
                        grade.setTime(towerStationEquipment2.getMonthaccount());
                        grade.setDelflag(0);
                        grade.setCreatetime(LocalDateTime.now());
                        grade.setUpdatetime(LocalDateTime.now());

                        //set 实际功率
                        grade.setCalcpower(calcActual(towerStationEquipment2));
                        //set 标准功率
                        grade.setStandardpower(caclStandard(towerStationEquipment2));

                        String gradeStr = gradeService.processGrade(grade);

                        grade.setEvaluate(gradeStr);
                        grade.setOrgCode(getOrgcode(orgcodeMap, grade));
                        return grade;
                    }
            ).collect(toList());
            // 批量处理的方法
            //先把对应的 grades 逻辑删除


            int n = gradeService.insertList2(grades);
            log.info("日志：批量插入 设备评级表 数据{}条", n);
            objList.removeAll(list);
            list.clear();
            grades.clear();
        }
    }

    private String getOrgcode(Map<String, String> orgcodeMap, StationGrade grade) {
        if (StringUtils.isBlank(grade.getStationcode())) {
            return "站址编码不存在";
        }

        String s = orgcodeMap.get(grade.getStationcode());
        if (StringUtils.isBlank(s)) {
            return "未找到映射";
        }
        return s;
    }


    @Override
    public List<TowerStationEquipment2> getAll(PageBean pageBean) {

        return mapper.getAll(pageBean);
    }

    private BigDecimal calcStandard1(EquipmentDict equipmentDict, TowerStationEquipment2 towerStationEquipment2) {
        BigDecimal energyCostPerDay = equipmentDict.getEnergyCostPerDay();
        if (energyCostPerDay == null) {
            return BigDecimal.ZERO;
        }
        if ("AAU/RRU(5G)".equals(equipmentDict.getType())) {
            Integer quantityAauRru1 = towerStationEquipment2.getQuantityAauRru1();
            if (quantityAauRru1 == null) {
                return BigDecimal.ZERO;
            }
            return energyCostPerDay.multiply(BigDecimal.valueOf(quantityAauRru1));
        }
        if ("BBU(5G)".equals(equipmentDict.getType())) {
            Integer quantityBbu1 = towerStationEquipment2.getQuantityBbu1();
            if (quantityBbu1 == null) {
                return BigDecimal.ZERO;
            }
            return energyCostPerDay.multiply(BigDecimal.valueOf(quantityBbu1));
        }
        if ("AAU/RRU(4G)".equals(equipmentDict.getType())) {
            Integer quantityAauRru2 = towerStationEquipment2.getQuantityAauRru2();
            if (quantityAauRru2 == null) {
                return BigDecimal.ZERO;
            }
            return energyCostPerDay.multiply(BigDecimal.valueOf(quantityAauRru2));
        }
        if ("BBU(4G)".equals(equipmentDict.getType())) {
            Integer quantityBbu2 = towerStationEquipment2.getQuantityBbu2();
            if (quantityBbu2 == null) {
                return BigDecimal.ZERO;
            }
            return energyCostPerDay.multiply(BigDecimal.valueOf(quantityBbu2));
        }
        if ("PPRU(5G)".equals(equipmentDict.getType())) {

            Integer quantityPrru1 = towerStationEquipment2.getQuantityPrru1();
            if (quantityPrru1 == null) {
                return BigDecimal.ZERO;
            }
            return energyCostPerDay.multiply(BigDecimal.valueOf(quantityPrru1));
        }
        if ("PPRU(4G)".equals(equipmentDict.getType())) {
            Integer quantityPrru2 = towerStationEquipment2.getQuantityPrru2();
            if (quantityPrru2 == null) {
                return BigDecimal.ZERO;
            }
            return energyCostPerDay.multiply(BigDecimal.valueOf(quantityPrru2));
        }
        if ("C网".equals(equipmentDict.getType())) {
            Double quantityC = towerStationEquipment2.getQuantityC();
            if (quantityC == null) {
                return BigDecimal.ZERO;
            }
            return energyCostPerDay.multiply(BigDecimal.valueOf(quantityC));
        }
        if ("直放站".equals(equipmentDict.getType())) {
            if ("近端".equals(equipmentDict.getModel())) {
                Double quantityNear = towerStationEquipment2.getQuantityNear();
                if (quantityNear == null) {
                    return BigDecimal.ZERO;
                }
                return energyCostPerDay.multiply(BigDecimal.valueOf(quantityNear));
            }
            if ("远端".equals(equipmentDict.getModel())) {
                Double quantityRemote = towerStationEquipment2.getQuantityRemote();
                if (quantityRemote == null) {
                    return BigDecimal.ZERO;
                }
                return energyCostPerDay.multiply(BigDecimal.valueOf(quantityRemote));
            }

        }
        if ("微波".equals(equipmentDict.getType())) {
            Double quantityWei = towerStationEquipment2.getQuantityWei();
            if (quantityWei == null) {
                return BigDecimal.ZERO;
            }
            return energyCostPerDay.multiply(BigDecimal.valueOf(quantityWei));
        }
        if ("IPRAN".equals(equipmentDict.getType())) {
            Double quantityIppan = towerStationEquipment2.getQuantityIppan();
            if (quantityIppan == null) {
                return BigDecimal.ZERO;
            }
            return energyCostPerDay.multiply(BigDecimal.valueOf(quantityIppan));
        }
        if ("皮站".equals(equipmentDict.getType())) {
            Double quantityP = towerStationEquipment2.getQuantityP();
            if (quantityP == null) {
                return BigDecimal.ZERO;
            }
            return energyCostPerDay.multiply(BigDecimal.valueOf(quantityP));
        }
        if ("光接入".equals(equipmentDict.getType())) {
            Double quantityG = towerStationEquipment2.getQuantityG();
            if (quantityG == null) {
                return BigDecimal.ZERO;
            }
            return energyCostPerDay.multiply(BigDecimal.valueOf(quantityG));
        }
        if ("传输".equals(equipmentDict.getType())) {
            Double quantityTrans = towerStationEquipment2.getQuantityTrans();
            if (quantityTrans == null) {
                return BigDecimal.ZERO;
            }
            return energyCostPerDay.multiply(BigDecimal.valueOf(quantityTrans));
        }
        if ("交换机".equals(equipmentDict.getType())) {
            Double quantitySwitch = towerStationEquipment2.getQuantitySwitch();
            if (quantitySwitch == null) {
                return BigDecimal.ZERO;
            }
            return energyCostPerDay.multiply(BigDecimal.valueOf(quantitySwitch));
        }
        if ("域网".equals(equipmentDict.getType())) {
            Double quantityDomain = towerStationEquipment2.getQuantityDomain();
            if (quantityDomain == null) {
                return BigDecimal.ZERO;
            }
            return energyCostPerDay.multiply(BigDecimal.valueOf(quantityDomain));
        }
        if ("其它".equals(equipmentDict.getType())) {
            Double quantityOther = towerStationEquipment2.getQuantityOther();
            if (quantityOther == null) {
                return BigDecimal.ZERO;
            }
            return energyCostPerDay.multiply(BigDecimal.valueOf(quantityOther));
        }
        if (equipmentDict.getType() == null || equipmentDict.getType() == "") {
            return BigDecimal.ZERO;
        }
//        throw new BaseException("设备类型异常，请联系管理员");
        return BigDecimal.ZERO;
    }
}

