package com.sccl.modules.business.stationinfo.service;


import com.sccl.framework.service.IBaseService;
import com.sccl.modules.business.account.domain.NhSite;
import com.sccl.modules.business.stationinfo.domain.StationJt5gjz;

import java.util.List;

/**
 * 集团LTE网管基站 服务层
 * 
 * <AUTHOR>
 * @date 2021-05-16
 */
public interface IStationJt5gjzService extends IBaseService<StationJt5gjz>
{


    NhSite getNhsite(Long nhSiteId);

    List<StationJt5gjz> selectListLike(StationJt5gjz stationJt5gjz);
}
