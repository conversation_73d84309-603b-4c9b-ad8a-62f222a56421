package com.sccl.modules.business.stationreportwhitelist.controller;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.lang.Dict;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.utils.ExcelUtil;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.stationreportwhitelist.domain.StationReportWhitelist;
import com.sccl.modules.business.stationreportwhitelist.dto.StationReportWhitelistDTO;
import com.sccl.modules.business.stationreportwhitelist.dto.StationReportWhitelistQuery;
import com.sccl.modules.business.stationreportwhitelist.enums.WhitelistType;
import com.sccl.modules.business.stationreportwhitelist.service.StationReportWhitelistService;
import com.sccl.modules.business.stationreportwhitelist.vo.OneWatchHasManyStationsExport;
import com.sccl.modules.business.stationreportwhitelist.vo.RationalityOfUnitPriceExport;
import com.sccl.modules.business.stationreportwhitelist.vo.StationReportWhitelistVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/11 15:14
 * @describe 一站多表白名单接口
 */
@Slf4j
@RestController
@RequestMapping("/stationReportWhitelist")
public class StationReportWhitelistController extends BaseController {

    private final StationReportWhitelistService stationReportWhitelistService;

    @Autowired
    public StationReportWhitelistController(StationReportWhitelistService stationReportWhitelistService) {
        this.stationReportWhitelistService = stationReportWhitelistService;
    }

    /**
     * 根据id查询
     *
     * @param id id
     * @return StationReportWhitelist
     */
    @GetMapping("/getById/{id}")
//    @RequiresPermissions("business:stationReportWhitelist:detail")
    public AjaxResult findById(@PathVariable Long id) {
        return AjaxResult.success(stationReportWhitelistService.findById(id));
    }

    /**
     * 查询列表
     *
     * @param query stationReportWhitelist
     * @return list
     */
    @GetMapping("/list")
//    @RequiresPermissions("business:stationReportWhitelist:list")
    public TableDataInfo list(Page<StationReportWhitelistVO> page, StationReportWhitelistQuery query) {
        IPage<StationReportWhitelistVO> iPage = stationReportWhitelistService.selectList(page, query);
        return getDataTable(iPage.getRecords(), iPage.getTotal());
    }

    /**
     * 导出 一表多站
     */
    @GetMapping("/exportOneWatchHasManyStationsExport")
//    @RequiresPermissions("business:stationReportWhitelist:list")
    public void exportOneWatchHasManyStationsExport(Page<StationReportWhitelistVO> page, StationReportWhitelistQuery query, HttpServletResponse response) {
        List<OneWatchHasManyStationsExport> exportList = stationReportWhitelistService.exportOneWatchHasManyStationsExport(page, query);
        ExcelUtil<OneWatchHasManyStationsExport> excelUtil = new ExcelUtil<>(OneWatchHasManyStationsExport.class);
        excelUtil.exportExcelToBrowser(response, exportList, "一表多站");
    }

    /**
     * 导出 单价合理性
     */
    @GetMapping("/exportRationalityOfUnitPrice")
//    @RequiresPermissions("business:stationReportWhitelist:list")
    public void export(Page<StationReportWhitelistVO> page, StationReportWhitelistQuery query, HttpServletResponse response) {
        List<RationalityOfUnitPriceExport> exportList = stationReportWhitelistService.exportRationalityOfUnitPrice(page, query);
        ExcelUtil<RationalityOfUnitPriceExport> excelUtil = new ExcelUtil<>(RationalityOfUnitPriceExport.class);
        excelUtil.exportExcelToBrowser(response, exportList, "单价合理性");
    }

    /**
     * 新增
     *
     * @param stationReportWhitelist 表单数据
     */
    @PostMapping("/insert")
//    @RequiresPermissions("business:stationReportWhitelist:insert")
    @Log(title = "一站多表白名单", action = BusinessType.INSERT)
    public AjaxResult insert(@RequestBody @Validated StationReportWhitelistDTO stationReportWhitelist) {
        String id = stationReportWhitelistService.add(stationReportWhitelist);
        return AjaxResult.success("保存成功", Dict.create().set("id", id));
    }

    /**
     * 修改
     *
     * @param dto 表单数据
     */
    @PostMapping(value = "/update")
//    @RequiresPermissions("business:stationReportWhitelist:update")
    @Log(title = "一站多表白名单", action = BusinessType.UPDATE)
    public AjaxResult update(@RequestBody @Validated StationReportWhitelistDTO dto) {
        int update = stationReportWhitelistService.edit(dto);
        return toAjax(update);
    }

    /**
     * 根据id删除
     *
     * @param id id
     */
    @PostMapping("/remove/{id}")
//    @RequiresPermissions("business:stationReportWhitelist:delete")
    @Log(title = "一站多表白名单", action = BusinessType.DELETE)
    public AjaxResult deleteById(@PathVariable Long id) {
        int delete = stationReportWhitelistService.del(id);
        return toAjax(delete);
    }

    /**
     * 移除白名单
     */
    @PostMapping("/billStatusRemove/{id}")
//    @RequiresPermissions("business:stationReportWhitelist:delete")
    @Log(title = "一站多表白名单", action = BusinessType.DELETE)
    public AjaxResult remove(@PathVariable Long id) {
        StationReportWhitelist whitelist = new StationReportWhitelist();
        whitelist.setId(id);
//        whitelist.setBillStatus(BillStatus.REMOVE.getCode());
        boolean update = stationReportWhitelistService.updateById(whitelist);
        return toAjax(update);
    }

    /**
     * 获取白名单类型列表
     */
    @GetMapping("/getWhitelistTypeList")
    //    @RequiresPermissions("business:stationReportWhitelist:list")
    public AjaxResult getWhitelistTypeList() {
        return AjaxResult.success(WhitelistType.getDictList());
    }


    /**
     * 初始化白名单电表/协议id
     */
    @PostMapping(value = "/initMeterId")
    public AjaxResult initMeterId() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        stationReportWhitelistService.initMeterId();
        stopWatch.stop();
        log.info("初始化白名单电表/协议id耗时：{}", stopWatch.getTotalTimeMillis());
        return AjaxResult.success("初始化成功");
    }
}
