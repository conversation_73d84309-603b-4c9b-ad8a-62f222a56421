package com.sccl.modules.pue.service.impl;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.pue.domain.PueDetail;
import com.sccl.modules.pue.mapper.PueMapper;
import com.sccl.modules.pue.service.PueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PueServiceImpl extends BaseServiceImpl<PueDetail> implements PueService {

    @Autowired
    private PueMapper pueMapper;

    @Override
    public List<PueDetail> findList(PueDetail pueDetail) {
        List<PueDetail> list = pueMapper.findList(pueDetail);
        return list;
    }
}
