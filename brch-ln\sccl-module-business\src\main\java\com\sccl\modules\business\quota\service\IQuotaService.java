package com.sccl.modules.business.quota.service;

import com.sccl.framework.service.IBaseService;
import com.sccl.modules.business.quota.domain.Quota;
import com.sccl.modules.business.quota.domain.QuotaBaseResult;
import com.sccl.modules.business.quota.domain.QuotaCondition;

import java.util.List;

/**
 * 定额管理 服务层
 * 
 * <AUTHOR>
 * @date 2019-05-13
 */
public interface IQuotaService extends IBaseService<Quota>
{
    /**
     * 定额列表
     * 根据条件查询数据
     * @param quotaCondition
     * @return
     */
    public List<QuotaBaseResult> selectListBySearch(QuotaCondition quotaCondition);

    /**
     * 删除
     * @param ids
     * @return
     */
    public int deleteQuota(String ids);
    /**
     * 定额编辑
     * 根据id查询数据
     * @param id 查询条件
     * @return
     */
    public QuotaBaseResult getById(Long id);

    public int addQuota(Quota quota);

    public int updateQuota(Quota quota);
    public List<QuotaBaseResult> checkAmmProByQuota(Quota quota);

	
}
