package com.sccl.modules.mssaccount.mssaccountbill.frame;

import com.sccl.common.utils.SpringUtil;
import com.sccl.modules.business.auditafter.mapper.AfterAuditMapper;

import java.util.List;
import java.util.StringJoiner;

public class AccountStatusVerify implements RemoveVerifyRule {

    @Override
    public String Verify(Long billId, List<Long> accountIds) {
        AfterAuditMapper mapper = SpringUtil.getBean(AfterAuditMapper.class);
        List<String> result = mapper.getAccountStatueVerify(billId, accountIds);
        StringJoiner joiner = new StringJoiner("\n");
        result.forEach(joiner::add);
        return joiner.toString();
    }


}
