package com.sccl.modules.business.trunkstation.controller;

import java.util.List;

import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.modules.business.ecceptionprocess.domain.EcceptionProcess;
import com.sccl.modules.system.role.domain.Role;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.service.IUserService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.business.trunkstation.domain.TrunkStation;
import com.sccl.modules.business.trunkstation.service.ITrunkStationService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 铁塔站址基础数据 信息操作处理
 * 
 * <AUTHOR>
 * @date 2023-05-10
 */
@RestController
@RequestMapping("/business/trunkStation")
public class TrunkStationController extends BaseController
{
    private String prefix = "business/trunkStation";
	
	@Autowired
	private ITrunkStationService trunkStationService;
	@Autowired
	private IUserService userService;
	@RequiresPermissions("business:trunkStation:view")
	@GetMapping()
	public String trunkStation()
	{
	    return prefix + "/trunkStation";
	}
	
	/**
	 * 查询铁塔站址基础数据列表
	 */
	@RequiresPermissions("business:trunkStation:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(TrunkStation trunkStation)
	{
		trunkStation = new TrunkStation();
		User user = ShiroUtils.getUser();
		List<Role> roles = userService.selectUserRole(user.getId());
		boolean isProAdmin = false;
		boolean isCityAdmin = false;
		boolean isSubAdmin = false;
		for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
			if (role.getCode().startsWith("PROVI_")) {//省能耗费管理员
				isProAdmin = true;
			}
			if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
				isCityAdmin = true;
			}
			if (role.getCode().startsWith("SUB_")) {//县能耗费管理员
				isSubAdmin = true;
			}
		}
		//  权限设置
		if (isProAdmin) {

		} else if (isCityAdmin) {
			List<IdNameVO> companies = user.getCompanies();
			if (companies != null && companies.size() > 0)
				trunkStation.setCompany(Long.parseLong(companies.get(0).getId()));
		} else if (isSubAdmin) {
			List<IdNameVO> departments = user.getDepartments();
			if (departments != null && departments.size() > 0)
				trunkStation.setCountry(Long.parseLong(departments.get(0).getId()));
		}
		startPage();
        List<TrunkStation> list = trunkStationService.selectList(trunkStation);
		return getDataTable(list);
	}
	
	/**
	 * 新增铁塔站址基础数据
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存铁塔站址基础数据
	 */
	@RequiresPermissions("business:trunkStation:add")
	@Log(title = "铁塔站址基础数据", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody TrunkStation trunkStation)
	{		
		return toAjax(trunkStationService.insert(trunkStation));
	}

	/**
	 * 修改铁塔站址基础数据
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		TrunkStation trunkStation = trunkStationService.get(id);

		Object object = JSONObject.toJSON(trunkStation);

        return this.success(object);
	}
	
	/**
	 * 修改保存铁塔站址基础数据
	 */
	@RequiresPermissions("business:trunkStation:edit")
	@Log(title = "铁塔站址基础数据", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody TrunkStation trunkStation)
	{		
		return toAjax(trunkStationService.update(trunkStation));
	}
	
	/**
	 * 删除铁塔站址基础数据
	 */
	@RequiresPermissions("business:trunkStation:remove")
	@Log(title = "铁塔站址基础数据", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(trunkStationService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看铁塔站址基础数据
     */
    @RequiresPermissions("business:trunkStation:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		TrunkStation trunkStation = trunkStationService.get(id);

        Object object = JSONObject.toJSON(trunkStation);

        return this.success(object);
    }

}
