package com.sccl.modules.business.powermodel.service;

import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol;
import com.sccl.modules.business.powermodel.entity.PowerModleInit;
import com.sccl.modules.business.powermodel.entity.SupplyCodeMapBill;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 电量模型原始数据(PowerModleInit)表服务接口
 *
 * <AUTHOR>
 * @since 2022-10-20 10:44:40
 */
public interface PowerModleInitService {
//    extends IBaseService<PowerModleInit>

    static PowerModleInit reduce(PowerModleInit powerModleInit1, PowerModleInit powerModleInit2) {
        if (powerModleInit1.getPowercSize() == null) {
            return powerModleInit2;
        }
        powerModleInit1.setPowercSize(powerModleInit1.getPowercSize() == null ? "0" :
                                              powerModleInit1.getPowercSize() + powerModleInit2.getPowercSize());
        powerModleInit1.setPowercIndustrySize(powerModleInit1.getPowercIndustrySize() == null ? "0" :
                                                      powerModleInit1.getPowercIndustrySize()
                                                              + powerModleInit2.getPowercIndustrySize());
        powerModleInit1.setPowercIndustrySizeNot(powerModleInit1.getPowercIndustrySizeNot() == null ? "0" :
                                                         powerModleInit1.getPowercIndustrySizeNot()
                                                                 + powerModleInit2.getPowercIndustrySizeNot());
        powerModleInit1.setPowerTotalSize(powerModleInit1.getPowerTotalSize() == null ? "0" :
                                                  powerModleInit1.getPowerTotalSize()
                                                          + powerModleInit2.getPowerTotalSize());
        powerModleInit1.setAveragepriceTotal(
                powerModleInit1.getAveragepriceTotal() == null ? BigDecimal.ZERO :
                        powerModleInit1.getAveragepriceTotal().
                                add(powerModleInit2.getAveragepriceTotal())
                                       .divide(new BigDecimal("2"), 2));
        return powerModleInit1;


    }

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    PowerModleInit queryById(Long id);

    /**
     * 新增数据
     *
     * @param powerModleInit 实例对象
     * @return 实例对象
     */
//  PowerModleInit insert(PowerModleInit powerModleInit);*

    /**
     * 修改数据
     *
     * @param powerModleInit 实例对象
     * @return 实例对象
     */
    /*    PowerModleInit update(PowerModleInit powerModleInit);*/

    /**
     * 分页查询
     *
     * @param powerModleInit 筛选条件
     * @param pageRequest    分页对象
     * @return 查询结果
     */
    Page<PowerModleInit> queryByPage(PowerModleInit powerModleInit, PageRequest pageRequest);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Long id);

    /**
     * 检查导入excel
     *
     * @param content
     * @return
     */
    List<PowerModleInit> checkImportExcel(List<PowerModleInit> content);

    /**
     * @param content 批量导入 初始数据
     * @return
     */
    Integer batchAdd(List<PowerModleInit> content);

    /**
     * 计算国网代购测算值单价
     *
     * @param init
     * @return
     */
    BigDecimal calcPriceContryProxy(PowerModleInit init);

    /**
     * 国网代购分峰平谷单价
     *
     * @param init
     * @return
     */
    BigDecimal calcPriceCountryProxyTimeshar(PowerModleInit init);

    /**
     * 计算 代购电价 分时 峰电价
     *
     * @param init
     * @return
     */
    BigDecimal calcPriceCountryProxyHigh(PowerModleInit init);

    /**
     * @param init 计算代购电价 分时 平电价
     * @return
     */
    BigDecimal calcPriceCountryProxyMid(PowerModleInit init);

    /**
     * @param init 计算代购电价 分时 谷电价
     * @return
     */
    BigDecimal calcPriceCountryProxyLow(PowerModleInit init);

    /**
     * 计算 直购电（实际）
     *
     * @param init
     * @return
     */
    BigDecimal calcPriceDirectpurele(PowerModleInit init);

    BigDecimal calcPriceDirectpureleTheoryEstimate(PowerModleInit init);

    BigDecimal calcPriceDirectpureleTheoryEstimate2(PowerModleInit init);

    BigDecimal calcPriceDirectpureleEstimate(PowerModleInit init);

    BigDecimal calcPriceDirectpurele2(PowerModleInit init);

    /**
     * @param init 计算直购电收益
     * @return
     */
    BigDecimal caclProfitDire(PowerModleInit init);

    /**
     * @param list 将计算之后的 数据 插入到 power_modle_init_calc表
     * @return
     */
    Integer batchAddToOhter(List<PowerModleInit> list);

    /**
     * @param content 批量更新数据
     * @return
     */
    int updateListById(List<PowerModleInit> content);

    /**
     * 插入 如果存在就 删除然后更新
     *
     * @param content
     * @return
     */
    int replaceList(List<PowerModleInit> content);

    List<PowerModleInit> selectList(PowerModleInit msg);

    List<PowerModleInit> selectList1(PowerModleInit msg);

    void checkAndInput(PowerModleInit init, String budget, Map<String, List<Ammeterorprotocol>> ammeterorprotocolMap, Map<String, List<SupplyCodeMapBill>> supplyCodeBillMap);

    AjaxResult getPower(String accountNo, String startDate, String endDate);

    int update(PowerModleInit modlePricesp);


    int updateForModel(PowerModleInit modleInit);

    int updateForModelList(List<PowerModleInit> powerModleInits);

    AjaxResult reloadAuditinit(PowerModleInit powerModleInit, String auditType);

    void deleteCalcAndInsert(PowerModleInit powerModleInit1, List<PowerModleInit> content);

    AjaxResult calcByinit(PowerModleInit powerModleInitTemp);

    AjaxResult updateMeter(List<Ammeterorprotocol> ammeterorprotocols);

    AjaxResult quetyMeter(Ammeterorprotocol msg);

    AjaxResult updateMeterend(List<Ammeterorprotocol> ammeterorprotocols);

    AjaxResult queryMeter(Ammeterorprotocol msg);

    void SetBigWord(List<PowerModleInit> content);
}
