package com.sccl.modules.business.stationaudit.msshistory;

import com.enrising.dcarbon.audit.AbstractReferee;
import com.enrising.dcarbon.audit.AbstractRefereeDatasourceCreator;
import com.enrising.dcarbon.audit.Auditable;
import com.enrising.dcarbon.audit.RefereeDatasource;
import com.sccl.common.utils.SpringUtil;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;
import com.sccl.modules.mssaccount.mssaccountbill.mapper.MssAccountbillMapper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MssHistroyCreator extends AbstractRefereeDatasourceCreator {
    @Override
    public Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> collect(Auditable auditable) {
        //从Spring上下文取得mapper
        MssAccountbillMapper mapper = SpringUtil.getBean(MssAccountbillMapper.class);
        //可能上下文没有这个mapper
        if (mapper == null) {
            return null;
        }

        MssAccountbill accountBill = (MssAccountbill) auditable;
        String stationCode  = mapper.getStationCode(accountBill);
        //todo:过去6个月 报账历史
        List<MssHistory> list = mapper.getHistroy(stationCode, 6);
        list.stream().filter(mssHistory -> mssHistory!=null)
            .forEach(mssHistory -> mssHistory.setNodetype(1));
        if (list.size()==0) {
            MssHistory history = new MssHistory();
            history.setNodetype(1);
            history.setAuditmsg("当前站址没有报账历史在过去6月");
            list.add(history);
        }
        Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> datasource = new HashMap<>();
        //可以添加多种不同类型的评判数据
        datasource.put(MssAccountbill.class,new ArrayList<>(list));

        return datasource;
    }

    @Override
    public AbstractReferee getReferee() {
        return new MssHistroyReferee();
    }
}
