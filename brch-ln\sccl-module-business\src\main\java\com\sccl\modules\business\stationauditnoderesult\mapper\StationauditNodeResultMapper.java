package com.sccl.modules.business.stationauditnoderesult.mapper;

import com.enrising.dcarbon.audit.RefereeDatasource;
import com.sccl.modules.business.stationauditnoderesult.domain.StationauditNodeResult;
import com.sccl.framework.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 基站一站式稽核结果 数据层
 * 
 * <AUTHOR>
 * @date 2022-11-16
 */
public interface StationauditNodeResultMapper extends BaseMapper<StationauditNodeResult>
{


    /**
     *  将责任链的结果 插入到数据库
     * @param collect
     * @return
     */
    int insertListResults(@Param("list") List<RefereeDatasource> collect);
}