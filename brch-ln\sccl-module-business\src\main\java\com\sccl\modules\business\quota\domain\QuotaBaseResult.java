package com.sccl.modules.business.quota.domain;

import com.sccl.framework.web.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * 定额 返回结果
 *
 * <AUTHOR>
 * @date 2019-05-13
 */
public class QuotaBaseResult extends BaseEntity {
    /**
     * id
     */
    private Long id;
    /**
     * 电表户号/协议编码
     */
    private String ammeterCode;
    /**
     * 流程id
     */
    private Long processinstId;
    /**
     * 流程类型
     */
    private String busiAlias;

    /**
     * 关联电表或者协议id
     */
    private Long deviceId;
    /**
     * 区分标识，0:电表 1:协议
     */
    private String sign;
    /**
     * 项目名称，电表或者协议带出
     */
    private String projectName;
    /**
     * 责任中心id，电表或者协议带出
     */
    private Long country;
    /**
     * 责任中心名称，电表或者协议带出
     */
    private String countryName;
    /**
     * 所属分公司，当前管理员带出
     */
    private Long company;
    /**
     * 1月电量定额值
     */
    private BigDecimal janQuotaValue;
    /**
     * 2月电量定额值
     */
    private BigDecimal febQuotaValue;
    /**
     * 3月电量定额值
     */
    private BigDecimal marQuotaValue;
    /**
     * 4月电量定额值
     */
    private BigDecimal aprQuotaValue;
    /**
     * 5月电量定额值
     */
    private BigDecimal mayQuotaValue;
    /**
     * 6月电量定额值
     */
    private BigDecimal junQuotaValue;
    /**
     * 7月电量定额值
     */
    private BigDecimal julQuotaValue;
    /**
     * 8月电量定额值
     */
    private BigDecimal augQuotaValue;
    /**
     * 9月电量定额值
     */
    private BigDecimal sepQuotaValue;
    /**
     * 10月电量定额值
     */
    private BigDecimal octQuotaValue;
    /**
     * 11月电量定额值
     */
    private BigDecimal novQuotaValue;
    /**
     * 12月电量定额值
     */
    private BigDecimal decQuotaValue;
    /**
     * 备注信息
     */
    private String remark;
    /**
     * 状态 -1：无效  0：草稿 1：流程中 2：申请流程归档完成
     */
    private Integer status;
    /**
     * 审批人
     */
    private Long approverId;

    private Boolean _disabled;


    /** 单据状态  0：草稿 1：流程中 2：申请流程归档完成*/
    private Integer billStatus;

    public Integer getBillStatus() {
        return billStatus;
    }

    public void setBillStatus(Integer billStatus) {
        this.billStatus = billStatus;
    }


    public Boolean get_disabled() {
        return _disabled;
    }

    public void set_disabled(Boolean _disabled) {
        this._disabled = _disabled;
    }


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAmmeterCode() {
        return ammeterCode;
    }

    public void setAmmeterCode(String ammeterCode) {
        this.ammeterCode = ammeterCode;
    }
    public Long getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(Long deviceId) {
        this.deviceId = deviceId;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public Long getCountry() {
        return country;
    }

    public void setCountry(Long country) {
        this.country = country;
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }

    public Long getCompany() {
        return company;
    }

    public void setCompany(Long company) {
        this.company = company;
    }

    public BigDecimal getJanQuotaValue() {
        return janQuotaValue;
    }

    public void setJanQuotaValue(BigDecimal janQuotaValue) {
        this.janQuotaValue = janQuotaValue;
    }

    public BigDecimal getFebQuotaValue() {
        return febQuotaValue;
    }

    public void setFebQuotaValue(BigDecimal febQuotaValue) {
        this.febQuotaValue = febQuotaValue;
    }

    public BigDecimal getMarQuotaValue() {
        return marQuotaValue;
    }

    public void setMarQuotaValue(BigDecimal marQuotaValue) {
        this.marQuotaValue = marQuotaValue;
    }

    public BigDecimal getAprQuotaValue() {
        return aprQuotaValue;
    }

    public void setAprQuotaValue(BigDecimal aprQuotaValue) {
        this.aprQuotaValue = aprQuotaValue;
    }

    public BigDecimal getMayQuotaValue() {
        return mayQuotaValue;
    }

    public void setMayQuotaValue(BigDecimal mayQuotaValue) {
        this.mayQuotaValue = mayQuotaValue;
    }

    public BigDecimal getJunQuotaValue() {
        return junQuotaValue;
    }

    public void setJunQuotaValue(BigDecimal junQuotaValue) {
        this.junQuotaValue = junQuotaValue;
    }

    public BigDecimal getJulQuotaValue() {
        return julQuotaValue;
    }

    public void setJulQuotaValue(BigDecimal julQuotaValue) {
        this.julQuotaValue = julQuotaValue;
    }

    public BigDecimal getAugQuotaValue() {
        return augQuotaValue;
    }

    public void setAugQuotaValue(BigDecimal augQuotaValue) {
        this.augQuotaValue = augQuotaValue;
    }

    public BigDecimal getSepQuotaValue() {
        return sepQuotaValue;
    }

    public void setSepQuotaValue(BigDecimal sepQuotaValue) {
        this.sepQuotaValue = sepQuotaValue;
    }

    public BigDecimal getOctQuotaValue() {
        return octQuotaValue;
    }

    public void setOctQuotaValue(BigDecimal octQuotaValue) {
        this.octQuotaValue = octQuotaValue;
    }

    public BigDecimal getNovQuotaValue() {
        return novQuotaValue;
    }

    public void setNovQuotaValue(BigDecimal novQuotaValue) {
        this.novQuotaValue = novQuotaValue;
    }

    public BigDecimal getDecQuotaValue() {
        return decQuotaValue;
    }

    public void setDecQuotaValue(BigDecimal decQuotaValue) {
        this.decQuotaValue = decQuotaValue;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getApproverId() {
        return approverId;
    }

    public void setApproverId(Long approverId) {
        this.approverId = approverId;
    }

    public Long getProcessinstId() {
        return processinstId;
    }

    public void setProcessinstId(Long processinstId) {
        this.processinstId = processinstId;
    }

    public String getBusiAlias() {
        return busiAlias;
    }

    public void setBusiAlias(String busiAlias) {
        this.busiAlias = busiAlias;
    }
}
