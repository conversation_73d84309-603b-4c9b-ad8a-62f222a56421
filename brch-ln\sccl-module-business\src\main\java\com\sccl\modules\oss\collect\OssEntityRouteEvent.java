package com.sccl.modules.oss.collect;

import com.enrising.dcarbon.collector.CollectedDatasource;
import com.enrising.dcarbon.collector.RouteEvent;

import java.util.List;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-06-29 15:52
 * @email <EMAIL>
 */
public class OssEntityRouteEvent extends RouteEvent {
    public OssEntityRouteEvent(List<CollectedDatasource> source) {
        super(source);
    }

    public OssEntityRouteEvent(List<CollectedDatasource> source, Class<? extends CollectedDatasource>... publishTypes) {
        super(source, publishTypes);
    }
}
