package com.sccl.modules.business.examine.controller;

import com.enrising.dcarbon.date.DateUtils;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.utils.ExportExcelUtil;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.autojob.util.convert.StringUtils;
import com.sccl.modules.business.examine.service.IBudgetExamineService;
import com.sccl.modules.business.examine.vo.BudgetExamineResultVo;
import com.sccl.modules.business.examine.vo.BudgetExamineSearchVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;

/**
 * 预算考核
 *
 * <AUTHOR>
 * @date 2025-02-12
 */
@RestController
@RequestMapping("/business/examine/budgetExamine")
@Slf4j
public class BudgetExamineController extends BaseController {

    @Autowired
    private IBudgetExamineService budgetExamineService;

    /**
     * 预算考核 一览查询
     */
    @GetMapping("/list")
    public TableDataInfo list(BudgetExamineSearchVo searchVo) {
        setParam(searchVo);//参数处理
        //startPage();
        List<BudgetExamineResultVo> result = budgetExamineService.list(searchVo);
        return getDataTable(result);
    }

    /**
     * 预算考核 导出
     */
    @GetMapping("/export")
    public AjaxResult export(BudgetExamineSearchVo searchVo, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            setParam(searchVo);//参数处理
            //startPage();
            List<BudgetExamineResultVo> list = budgetExamineService.list(searchVo);
            InputStream inputStream = this.getClass().getResourceAsStream("/template/budgetExamineList.xls");
            ExportExcelUtil exportExce = new ExportExcelUtil(inputStream);
            String[] fields = {"companyName",//所属分公司
                    "used1",          //1月（万元）
                    "used2",          //2月（万元）
                    "used3",          //3月（万元）
                    "used4",          //4月（万元）
                    "used5",          //5月（万元）
                    "used6",          //6月（万元）
                    "used7",          //7月（万元）
                    "used8",          //8月（万元）
                    "used9",          //9月（万元）
                    "used10",         //10月（万元）
                    "used11",         //11月（万元）
                    "used12",         //12月（万元）
                    "usedTotal",      //累计使用预算
                    "com",            //同比增幅
                    "budgetAmount",   //年度预算（万元）
                    "useRate",        //预算使用比增幅
                    "monthScore",     //月度得分
                    "yearScore"       //年度得分
            };
            if (list != null) {
                exportExce.cteateTableByList(list, fields, 1);
            }
            // 标题设置
            String title = "预算考核情况";
            if (StringUtils.isNotBlank(searchVo.getYear())) {
                title = searchVo.getYear() + "年" + title;
            }
            exportExce.getWb().setSheetName(0, title);
            return exportExce.outputExcel(title, request, response);
        } catch (Exception e) {
            return error("导出Excel失败，请联系网站管理员！");
        }
    }

    /**
     * 查询参数处理
     * @param searchVo
     */
    private void setParam(BudgetExamineSearchVo searchVo) {
        String year = DateUtils.getYear();//当前年份
        if (StringUtils.isBlank(searchVo.getYear())) {
            searchVo.setYear(year);
        }
        searchVo.setPreYear(String.valueOf(Integer.valueOf(searchVo.getYear()) - 1));//上一年份
        if (searchVo.getYear().equals(year)) {
            //当前月份数
            searchVo.setMonthCnt(Integer.valueOf(DateUtils.getMonth()) - 1);
        } else {
            searchVo.setMonthCnt(12);
        }
        if ("-1".equals(searchVo.getCompany())) {
            searchVo.setCompany("");
        }
    }
}
