package com.sccl.modules.business.oilaccount.controller;

import com.sccl.common.constant.Constants;
import com.sccl.common.support.Convert;
import com.sccl.common.utils.DateUtils;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.utils.ExcelUtil;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.autojob.util.convert.StringUtils;
import com.sccl.modules.business.oilaccount.domain.OilAccount;
import com.sccl.modules.business.oilaccount.domain.OilAccountRequest;
import com.sccl.modules.business.oilaccount.service.OilAccountService;
import com.sccl.modules.system.organization.service.IOrganizationService;
import com.sccl.modules.system.role.domain.Role;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.service.IUserService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 用油控制层
 * @date 2024/8/27  11:26
 */
@RestController
@RequestMapping("/business/oil/account")
public class OilAccountController extends BaseController {

    @Resource(name = "oilAccountServiceImpl")
    private OilAccountService accountService;

    @Resource(name = "userServiceImpl")
    private IUserService userService;

    @Resource(name = "organizationServiceImpl")
    private IOrganizationService organizationService;

    /**
     * 新增或修改用油台账
     * @param accounts
     * @return
     */
    @PostMapping("/addOrUpdate")
    public AjaxResult batchAddOrUpdateOilAccount(@RequestBody List<OilAccount> accounts) {
        // 获取登录用户信息
        User user = ShiroUtils.getUser();
        if (null == user) {
            return error("当前系统无登录用户信息，请检查！！！");
        }
        Map<String, Integer> map = new HashMap<>(4);
        map.put("num", accountService.batchAddOrUpdateOilAccount(accounts, user));
        map.put("code", 0);
        return success(map);
    }

    /**
     * 查询用油台账列表
     */
    @RequestMapping("/list")
    public TableDataInfo selectOilAccountPageList(OilAccountRequest request) {
        // 获取查询权限
        getSelectAuthority(request);
        // 参数处理
        if (null != request.getAccountno() && String.valueOf(Constants.All_CODE).equals(request.getAccountno())) {
            request.setAccountno(null);
        }
        if (null != request.getOilType() && request.getOilType().compareTo(Constants.All_CODE) == 0) {
            request.setOilType(null);
        }
        if (null != request.getOilCategory() && request.getOilCategory().compareTo(Constants.All_CODE) == 0) {
            request.setOilCategory(null);
        }
        // 台账查询
        if (null != request.getQuery() && request.getQuery()) {
            if (request.getStartAccountNo() != null && request.getEndAccountNo() == null) {
                request.setCurrentAccountNo(DateUtils.getDate("yyyyMM"));
            }
        }
        startPage();
        List<OilAccount> list = accountService.listOilAccount(request);
        return getDataTable(list);
    }

    /**
     * 删除用油台账
     */
    @DeleteMapping("/delete")
    public AjaxResult deleteOilAccount(@RequestBody HashMap<String, String> map) {
        return toAjax(accountService.deleteByIds(Convert.toStrArray(map.get("ids"))));
    }

    /**
     * 用油台账批量导入
     */
    @PostMapping("/import")
    public Map<String, Object> importOilAccount(HttpServletRequest request, HttpServletResponse response) throws Exception{
        return accountService.importOilAccount(request, response);
    }

    /**
     * 用油台账excel模板下载
     */
    @GetMapping("/template/load")
    public void loadCoalTemplateExcel(HttpServletResponse response) throws Exception {
        InputStream in = getClass().getClassLoader().getResourceAsStream("oil.xlsx");
        if (null == in) {
            throw new Exception("导入模板下载失败，请联系管理员！！！");
        }
        ExcelUtil.getTemplateExcel(response, in, "用油台账导入模板.xlsx");
    }

    /**
     * 获取查询权限
     *
     * @param request
     */
    private void getSelectAuthority(OilAccountRequest request){
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
            if (role.getCode().startsWith("SUB_")) {//县能耗费管理员
                isSubAdmin = true;
            }
        }
        if (isCityAdmin) {
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0)
                request.setCompany(companies.get(0).getId());
        } else if (isSubAdmin) {
            List<IdNameVO> departments = user.getDepartments();
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0) {
                List<Map<String, Object>> countrys = organizationService.selectSubordinateOrgByRole(companies.get(0).getId(), "1");
                if (countrys != null && countrys.size() > 0) {
                    request.setCountrys(countrys);
                } else {
                    if (departments != null && departments.size() > 0 && StringUtils.isEmpty(request.getCountry()))
                        request.setCountry(departments.get(0).getId());
                }
            }
        } else {
            request.setFillInAccount(user.getLoginId());
        }
    }
}
