package com.sccl.modules.business.powerauditstaiongrade.entity;

import lombok.Data;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class TowerAuditPro {
    private String company;
    private String companyName;
    private String country;
    private String countryName;
    /**
     * 1月
     */
    private String time1;
    /**
     * 稽核通过数
     */
    private String auditNum1;
    /**
     * 一次性稽核通过数
     */
    private String auditNumOne1;
    /**
     * 稽核总数
     */
    private String totalNum1;
    /**
     * 稽核通过比例
     */
    private String auditScale1;
    /**
     * 一次性稽核通过比例
     */
    private String auditOneScale1;

    private String time2;
    private String auditNum2;
    private String auditNumOne2;
    private String totalNum2;
    private String auditScale2;
    private String auditOneScale2;

    private String time3;
    private String auditNum3;
    private String auditNumOne3;
    private String totalNum3;
    private String auditScale3;
    private String auditOneScale3;

    private String time4;
    private String auditNum4;
    private String auditNumOne4;
    private String totalNum4;
    private String auditScale4;
    private String auditOneScale4;

    private String time5;
    private String auditNum5;
    private String auditNumOne5;
    private String totalNum5;
    private String auditScale5;
    private String auditOneScale5;

    private String time6;
    private String auditNum6;
    private String auditNumOne6;
    private String totalNum6;
    private String auditScale6;
    private String auditOneScale6;

    private String time7;
    private String auditNum7;
    private String auditNumOne7;
    private String totalNum7;
    private String auditScale7;
    private String auditOneScale7;

    private String time8;
    private String auditNum8;
    private String auditNumOne8;
    private String totalNum8;
    private String auditScale8;
    private String auditOneScale8;

    private String time9;
    private String auditNum9;
    private String auditNumOne9;
    private String totalNum9;
    private String auditScale9;
    private String auditOneScale9;

    private String time10;
    private String auditNum10;
    private String auditNumOne10;
    private String totalNum10;
    private String auditScale10;
    private String auditOneScale10;

    private String time11;
    private String auditNum11;
    private String auditNumOne11;
    private String totalNum11;
    private String auditScale11;
    private String auditOneScale11;

    private String time12;
    private String auditNum12;
    private String auditNumOne12;
    private String totalNum12;
    private String auditScale12;
    private String auditOneScale12;


    public static List<TowerAuditPro> mergeAuditList(List<TowerAudit> auditList) {
        Map<String, TowerAuditPro> auditProMap = new HashMap<>();

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
        for (TowerAudit audit : auditList) {
            String key = audit.getCompany() + "-" + audit.getCountry();
            String time = audit.getTime();
            int month = getMonthFromTime(time);

            if (!auditProMap.containsKey(key)) {
                TowerAuditPro auditPro = new TowerAuditPro();
                auditPro.setCompany(audit.getCompany());
                auditPro.setCompanyName(audit.getCompanyName());
                auditPro.setCountry(audit.getCountry());
                auditPro.setCountryName(audit.getCountryName());
                auditProMap.put(key, auditPro);
            }
            TowerAuditPro auditPro = auditProMap.get(key);
            setMonthData(audit, time, month, auditPro);
        }

        return new ArrayList(auditProMap.values());
    }

    public static void setMonthData(TowerAudit audit, String time, int month, TowerAuditPro auditPro) {
        switch (month) {
            case 1:
                auditPro.setTime1(time);
                auditPro.setAuditNum1(audit.getAuditNum());
                auditPro.setAuditNumOne1(audit.getAuditNumOne());
                auditPro.setTotalNum1(audit.getTotalNum());
                auditPro.setAuditScale1(audit.getAuditScale());
                auditPro.setAuditOneScale1(audit.getAuditOneScale());
                break;
            case 2:
                auditPro.setTime2(time);
                auditPro.setAuditNum2(audit.getAuditNum());
                auditPro.setAuditNumOne2(audit.getAuditNumOne());
                auditPro.setTotalNum2(audit.getTotalNum());
                auditPro.setAuditScale2(audit.getAuditScale());
                auditPro.setAuditOneScale2(audit.getAuditOneScale());
                break;
            case 3:
                auditPro.setTime3(time);
                auditPro.setAuditNum3(audit.getAuditNum());
                auditPro.setAuditNumOne3(audit.getAuditNumOne());
                auditPro.setTotalNum3(audit.getTotalNum());
                auditPro.setAuditScale3(audit.getAuditScale());
                auditPro.setAuditOneScale3(audit.getAuditOneScale());
                break;
            case 4:
                auditPro.setTime4(time);
                auditPro.setAuditNum4(audit.getAuditNum());
                auditPro.setAuditNumOne4(audit.getAuditNumOne());
                auditPro.setTotalNum4(audit.getTotalNum());
                auditPro.setAuditScale4(audit.getAuditScale());
                auditPro.setAuditOneScale4(audit.getAuditOneScale());
                break;
            case 5:
                auditPro.setTime5(time);
                auditPro.setAuditNum5(audit.getAuditNum());
                auditPro.setAuditNumOne5(audit.getAuditNumOne());
                auditPro.setTotalNum5(audit.getTotalNum());
                auditPro.setAuditScale5(audit.getAuditScale());
                auditPro.setAuditOneScale5(audit.getAuditOneScale());
                break;
            case 6:
                auditPro.setTime6(time);
                auditPro.setAuditNum6(audit.getAuditNum());
                auditPro.setAuditNumOne6(audit.getAuditNumOne());
                auditPro.setTotalNum6(audit.getTotalNum());
                auditPro.setAuditScale6(audit.getAuditScale());
                auditPro.setAuditOneScale6(audit.getAuditOneScale());
                break;
            case 7:
                auditPro.setTime7(time);
                auditPro.setAuditNum7(audit.getAuditNum());
                auditPro.setAuditNumOne7(audit.getAuditNumOne());
                auditPro.setTotalNum7(audit.getTotalNum());
                auditPro.setAuditScale7(audit.getAuditScale());
                auditPro.setAuditOneScale7(audit.getAuditOneScale());
                break;
            case 8:
                auditPro.setTime8(time);
                auditPro.setAuditNum8(audit.getAuditNum());
                auditPro.setAuditNumOne8(audit.getAuditNumOne());
                auditPro.setTotalNum8(audit.getTotalNum());
                auditPro.setAuditScale8(audit.getAuditScale());
                auditPro.setAuditOneScale8(audit.getAuditOneScale());
                break;
            case 9:
                auditPro.setTime9(time);
                auditPro.setAuditNum9(audit.getAuditNum());
                auditPro.setAuditNumOne9(audit.getAuditNumOne());
                auditPro.setTotalNum9(audit.getTotalNum());
                auditPro.setAuditScale9(audit.getAuditScale());
                auditPro.setAuditOneScale9(audit.getAuditOneScale());
                break;
            case 10:
                auditPro.setTime10(time);
                auditPro.setAuditNum10(audit.getAuditNum());
                auditPro.setAuditNumOne10(audit.getAuditNumOne());
                auditPro.setTotalNum10(audit.getTotalNum());
                auditPro.setAuditScale10(audit.getAuditScale());
                auditPro.setAuditOneScale10(audit.getAuditOneScale());
                break;
            case 11:
                auditPro.setTime11(time);
                auditPro.setAuditNum11(audit.getAuditNum());
                auditPro.setAuditNumOne11(audit.getAuditNumOne());
                auditPro.setTotalNum11(audit.getTotalNum());
                auditPro.setAuditScale11(audit.getAuditScale());
                auditPro.setAuditOneScale11(audit.getAuditOneScale());
                break;
            case 12:
                auditPro.setTime12(time);
                auditPro.setAuditNum12(audit.getAuditNum());
                auditPro.setAuditNumOne12(audit.getAuditNumOne());
                auditPro.setTotalNum12(audit.getTotalNum());
                auditPro.setAuditScale12(audit.getAuditScale());
                auditPro.setAuditOneScale12(audit.getAuditOneScale());
                break;
            default:
                break;
        }
    }

    private static int getMonthFromTime(String time) {
        // 解析时间字符串，获取月份，这里简化为假设时间字符串的格式为'yyyy-MM'
        String[] parts = time.split("-");
        if (parts.length == 2) {
            return Integer.parseInt(parts[1]);
        }
        return 0;
    }
}
