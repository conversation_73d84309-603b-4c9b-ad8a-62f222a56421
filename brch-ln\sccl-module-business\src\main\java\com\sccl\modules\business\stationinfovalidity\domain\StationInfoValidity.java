package com.sccl.modules.business.stationinfovalidity.domain;

import com.sccl.framework.web.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;


/**
 * 电站址有效表 power_station_info_validity
 *
 * <AUTHOR>
 * @date 2023-03-31
 */
@Data
public class StationInfoValidity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 市
     */
    private String companyname;
    /**
     * 区
     */
    private String countryname;
    /**
     * 市
     */
    private String company;
    /**
     * 区
     */
    private String country;

    /**
     * 集团5gr基站站址编码
     */
    private String stationcode5gr;
    /**
     * 集团5gr基站站址名称
     */
    private String stationname5gr;
    /**
     * 站址名称
     */
    private String stationname;
    /**
     * 站址编码INTID
     */
    private String stationcodeintid;
    /**
     * 主数据站址编码
     */
    private String stationcodemds;
    /**
     * 铁塔站址编码
     */
    private String stationcodetowercode;
    /**
     * 铁塔站址名称
     */
    private String stationcodetowername;
    /**
     *
     */
    private String status;
    /**
     *
     */
    private Date inputdate;
    /**
     *机房类型
     */
    private String  roomType;



    public String getRoomType() {
        return roomType;
    }

    public void setRoomType(String roomType) {
        this.roomType = roomType;
    }

    public String getCompanyname() {
        return companyname;
    }

    public void setCompanyname(String companyname) {
        this.companyname = companyname;
    }

    public String getCountryname() {
        return countryname;
    }

    public void setCountryname(String countryname) {
        this.countryname = countryname;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getStationname() {
        return stationname;
    }

    public void setStationname(String stationname) {
        this.stationname = stationname;
    }

    public String getStationcodeintid() {
        return stationcodeintid;
    }

    public void setStationcodeintid(String stationcodeintid) {
        this.stationcodeintid = stationcodeintid;
    }

    public String getStationcodemds() {
        return stationcodemds;
    }

    public void setStationcodemds(String stationcodemds) {
        this.stationcodemds = stationcodemds;
    }

    public String getStationcodetowercode() {
        return stationcodetowercode;
    }

    public void setStationcodetowercode(String stationcodetowercode) {
        this.stationcodetowercode = stationcodetowercode;
    }

    public String getStationcodetowername() {
        return stationcodetowername;
    }

    public void setStationcodetowername(String stationcodetowername) {
        this.stationcodetowername = stationcodetowername;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getInputdate() {
        return inputdate;
    }

    public void setInputdate(Date inputdate) {
        this.inputdate = inputdate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("companyname", getCompanyname())
                .append("countryname", getCountryname())
                .append("company", getCompany())
                .append("country", getCountry())
                .append("stationname", getStationname())
                .append("stationcodeintid", getStationcodeintid())
                .append("stationcodemds", getStationcodemds())
                .append("stationcodetowercode", getStationcodetowercode())
                .append("stationcodetowername", getStationcodetowername())
                .append("status", getStatus())
                .append("delFlag", getDelFlag())
                .append("inputdate", getInputdate())
                .toString();
    }
}
