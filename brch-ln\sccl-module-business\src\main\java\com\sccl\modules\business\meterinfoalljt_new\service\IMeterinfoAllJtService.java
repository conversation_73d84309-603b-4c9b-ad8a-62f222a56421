package com.sccl.modules.business.meterinfoalljt_new.service;


import com.sccl.modules.business.meterinfoalljt_new.dto.MeterinfoAllJtQueryDTO;
import com.sccl.modules.business.meterinfoalljt_new.vo.MeterinfoAllJtVO;

import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * 表计清单查询 服务层
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface IMeterinfoAllJtService {

    /**
     * 查询表计清单列表
     *
     * @param queryDTO 查询条件
     * @return 表计清单列表
     */
    List<MeterinfoAllJtVO> selectMeterinfoAllJtList(MeterinfoAllJtQueryDTO queryDTO);

    /**
     * 根据ID查询表计清单详情
     *
     * @param id 主键ID
     * @return 表计清单详情
     */
    MeterinfoAllJtVO selectMeterinfoAllJtById(Long id);

    /**
     * 导入表计清单Excel
     *
     * @param sheetName 工作表名称
     * @param input     输入流
     * @return 导入结果
     */
    Map<String, Object> importExcel(String sheetName, InputStream input) throws Exception;
}
