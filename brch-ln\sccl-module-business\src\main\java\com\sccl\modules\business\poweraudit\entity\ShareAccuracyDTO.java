package com.sccl.modules.business.poweraudit.entity;

import com.sccl.framework.aspectj.lang.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: 芮永恒
 * @CreateTime: 2024-03-01  16:29
 * @Description: 共享站分摊比例  独享站分摊比例
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShareAccuracyDTO {

    /**
     * 地市
     */
    @Excel(name = "所属部门")
    private String city;

    /**
     * 区县
     */
//    @Excel(name = "区县公司")
    private String countyCompanies;

    /**
     * 运营分局
     */
    @Excel(name = "运营分局")
    private String operationsBranch;

    /**
     * 分摊比例准确性类型
     */
    @Excel(name = "类型")
    private String ratioErrorType = "铁塔推送分摊比例与协议管理中分摊比例不一致";

    /**
     * 分摊比例准确性类型
     */
//    @Excel(name = "类型")
    private String errorType = "铁塔推送分摊比例与协议管理中分摊比例不一致";

    /**
     * 台账期号
     */
    @Excel(name = "台账期号")
    private String accountNo;


    /**
     * 局站编码
     */
    @Excel(name = "集团站址编码")
    private String stationcode;

    /**
     * 电表协议编号
     */
    @Excel(name = "电表户名/协议号码")
    private String ammeterid;

    /**
     * 铁塔站址编码
     */
    @Excel(name = "铁塔站址编码")
    private String towerSiteCode;


//    @Excel(name = "协议管理能耗比例")
    private String meterPercent;


    @Excel(name = "维护共享家数")
    private String shareNum;

    /**
     * 电信
     */
    @Excel(name = "电信")
    private String dxApportionmentratio;

    /**
     * 移动
     */
    @Excel(name = "移动")
    private String mobileApportionmentratio;

    /**
     * 联通
     */
    @Excel(name = "联通")
    private String unicomApportionmentratio;

    /**
     * 拓展
     */
    @Excel(name = "拓展")
    private String expandApportionmentratio;

    /**
     * 能源
     */
    @Excel(name = "能源")
    private String energyApportionmentratio;

    /**
     * 合计
     */
    @Excel(name = "合计")
    private String totalApportionmentratio;

}
