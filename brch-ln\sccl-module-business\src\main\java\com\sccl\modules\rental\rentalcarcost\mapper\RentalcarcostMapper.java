package com.sccl.modules.rental.rentalcarcost.mapper;

import com.sccl.modules.rental.rentalcarcost.domain.Rentalcarcost;
import com.sccl.framework.mapper.BaseMapper;

/**
 * 租赁费用 数据层
 * 
 * <AUTHOR>
 * @date 2019-08-28
 */
public interface RentalcarcostMapper extends BaseMapper<Rentalcarcost>
{

    /**
     * @Description: 通过关联字段删除数据
     * @author: dongk
     * @date: 2019/9/5
     * @param:
     * @return:
     */
	int deleteByRcmids(String[] rcmids);
}