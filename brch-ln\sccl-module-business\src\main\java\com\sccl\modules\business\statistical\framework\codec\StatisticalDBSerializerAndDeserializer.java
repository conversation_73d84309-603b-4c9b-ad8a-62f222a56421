package com.sccl.modules.business.statistical.framework.codec;

import com.sccl.common.utils.SpringUtil;
import com.sccl.modules.autojob.util.convert.JsonUtil;
import com.sccl.modules.autojob.util.id.IdGenerator;
import com.sccl.modules.business.statistical.domain.StatisticalEntity;
import com.sccl.modules.business.statistical.framework.StatisticalIndex;
import com.sccl.modules.business.statistical.framework.mapper.StatisticalMapper;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/10/26 9:38
 */
public class StatisticalDBSerializerAndDeserializer implements StatisticalSerializer<StatisticalEntity>, StatisticalDeserializer<StatisticalEntity> {
    @Override
    public StatisticalIndex deserialize(StatisticalEntity statisticalIndex) {
        StatisticalIndex index = new StatisticalIndex();
        try {
            index.setContentType(Class.forName(statisticalIndex.getContentType()));
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        index.setStatisticalTime(statisticalIndex.getStatisticalTime());
        index.setContent(JsonUtil.jsonStringToPojo(statisticalIndex.getContent(), index.getContentType()));
        index.setGroupAlias(statisticalIndex.getGroupAlias());
        index.setOwnerAs(statisticalIndex.getOwnerAs());
        index.setTitle(statisticalIndex.getTitle());
        return index;
    }

    @Override
    public StatisticalEntity serialize(StatisticalIndex statisticalIndex) {
        StatisticalMapper mapper = SpringUtil.getBean(StatisticalMapper.class);
        if (mapper == null) {
            return null;
        }
        StatisticalEntity entity = new StatisticalEntity();
        entity.setId(IdGenerator.getNextIdAsLong());
        Long groupId = mapper.selectGroupId(statisticalIndex.getGroupAlias());
        entity.setGroupId(groupId == null ? IdGenerator.getNextIdAsLong() : groupId);
        entity.setContent(JsonUtil.pojoToJsonString(statisticalIndex.getContent()));
        entity.setContentType(statisticalIndex
                .getContentType()
                .getName());
        entity.setDel_flag(0);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        entity.setGroupAlias(statisticalIndex.getGroupAlias());
        entity.setOwnerAs(statisticalIndex.getOwnerAs());
        entity.setTitle(statisticalIndex.getTitle());
        entity.setStatisticalTime(statisticalIndex.getStatisticalTime());
        return entity;
    }
}
