package com.sccl.modules.business.stationreportwhitelist.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/5/9 17:23
 * @describe 电表查询
 */
@Getter
@Setter
public class PowerAmmeterorprotocolQuery {

    /**
     * 电表id
     */
    private Long id;

    /**
     * 电表户名
     */
    private String ammetername;



    /**
     * 项目名称
     */
    private String projectname;


    /**
     * 分公司
     */
    private Long company;

    /**
     * 部门
     */
    private Long country;

    /**
     * 白名单类型 1:一表多站 2:一站多表 3:单价
     */
    @NotBlank(message = "白名单类型不能为空")
    private String whitelistType;

}
