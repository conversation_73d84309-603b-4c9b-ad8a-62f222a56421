package com.sccl.modules.business.jhanomalydetails.util;

import com.sccl.framework.aspectj.lang.annotation.Excel;
import com.sccl.framework.utils.ExcelUtil;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.autojob.util.convert.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.jsoup.select.Evaluator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;


/**
 * 稽核导出
 * */
public  class JhExcelUtil {


    private static final Logger log = LoggerFactory.getLogger(JhExcelUtil.class);

    //clazz导出类
    //list数据类
    public static <T,U> AjaxResult exportExcelToBrowser(HttpServletResponse response, List<T> list, String sheetName,String key,Class<U> clazz) {
        long start = System.currentTimeMillis();
        // 得到所有定义字段
        Field[] allFields = clazz.getDeclaredFields();
        List<Field> fields = new ArrayList<Field>();

        // 得到所有field并存放到一个list中.
        for (Field field : allFields) {
            //区分是台账还是报账列表
            if (field.isAnnotationPresent(Excel.class) ) {
                //获取当前名称
               String name = field.getAnnotation(Excel.class).name();
               if(name.contains(key))
                fields.add(field);
            }
        }

        // 产生工作薄对象
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        // excel2003中每个sheet中最多有65536行
        int sheetSize = 65536;
        // 取出一共有多少个sheet.
        double sheetNo = Math.ceil(list.size() / sheetSize);
        for (int index = 0; index <= sheetNo; index++) {
            // 产生工作表对象
            SXSSFSheet sheet = workbook.createSheet();
            if (sheetNo == 0) {
                workbook.setSheetName(index, sheetName);
            } else {
                // 设置工作表的名称.
                workbook.setSheetName(index, sheetName + index);
            }
            SXSSFRow row;
            SXSSFCell cell; // 产生单元格

            // 产生一行
            row = sheet.createRow(0);
            // 写入各个字段的列头名称
            for (int i = 0; i < fields.size(); i++) {
                Field field = fields.get(i);
                Excel attr = field.getAnnotation(Excel.class);
                // 创建列
                cell = row.createCell(i);
                // 设置列中写入内容为String类型
                cell.setCellType(HSSFCell.CELL_TYPE_STRING);
                CellStyle cellStyle = workbook.createCellStyle();
                cellStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
                cellStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
                if (attr.name().indexOf("注：") >= 0) {
                    Font font = workbook.createFont();
                    font.setColor(HSSFFont.COLOR_RED);
                    cellStyle.setFont(font);
                    cellStyle.setFillForegroundColor(HSSFColor.LIGHT_YELLOW.index);
                    sheet.setColumnWidth(i, 6000);
                } else {
                    Font font = workbook.createFont();
                    // 粗体显示
                    font.setBoldweight(XSSFFont.BOLDWEIGHT_BOLD);
                    // 选择需要用到的字体格式
                    cellStyle.setFont(font);
                    cellStyle.setFillForegroundColor(HSSFColor.LIGHT_YELLOW.index);
                    // 设置列宽
                    sheet.setColumnWidth(i, 3766);
                }
                cellStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
                cellStyle.setWrapText(true);
                cell.setCellStyle(cellStyle);
                //取掉权限后缀
                String[] s = StringUtils.split(attr.name(), '_');
                // 写入列名
                cell.setCellValue(s[0]);
            }

            int startNo = index * sheetSize;
            int endNo = Math.min(startNo + sheetSize, list.size());
            // 写入各条记录,每条记录对应excel表中的一行
            CellStyle cs = workbook.createCellStyle();
            cs.setAlignment(HSSFCellStyle.ALIGN_CENTER);
            cs.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
            for (int i = startNo; i < endNo; i++) {
                row = sheet.createRow(i + 1 - startNo);
                // 得到导出对象
                T vo =  list.get(i);

               Class<?> target = vo.getClass();

                for (int j = 0; j < fields.size(); j++) {
                    // 获得field.
                    Field field = fields.get(j);
                    // 设置实体类私有属性可访问
                    field.setAccessible(true);
                    try {
                        // 创建cell
                        cell = row.createCell(j);
                        cell.setCellStyle(cs);
                        cell.setCellType(HSSFCell.CELL_TYPE_STRING);
                        Method method;
                        if (vo == null) {
                            // 如果数据存在就填入,不存在填入空格.
                            cell.setCellValue("");
                        } else {
                            // 如果数据存在就填入,不存在填入空格.
                            //通过反射调用
                           String name= field.getName();
                           String getMethod = "get" +    name.substring(0, 1).toUpperCase() + name.substring(1);
                            method = target.getMethod(getMethod);
                           Object result =  method.invoke(vo);
                            cell.setCellValue(result == null ? "" : String.valueOf(result));
                        }

                    } catch (Exception e) {
                        log.error("导出Excel失败{}", e.getMessage());
                    }
                }
            }
        }
        System.out.println("生成EXCEL耗时:" + ((System.currentTimeMillis() - start) / 1000));
        try {
            String filename = sheetName + ".xls";
//            OutputStream os = new FileOutputStream("E:\\导出测试.xlsx");
//            workbook.write(os);
//            os.close();
            response.setContentType("application/vnd.ms-excel");
            response.addHeader("Content-Disposition", "attachment;filename=".concat(String.valueOf(URLEncoder.encode(filename, "UTF-8"))));
            InputStream in = null;
            //临时缓冲区
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            //创建临时文件
            workbook.write(out);
            byte[] bookByteAry = out.toByteArray();
            in = new ByteArrayInputStream(bookByteAry);
            OutputStream outputStream = response.getOutputStream();
            int bufferSize = 8192;
            byte[] buffer = new byte[bufferSize];
            BufferedOutputStream bos = null;
            BufferedInputStream bis = null;
            try {
                bos = new BufferedOutputStream(outputStream);
                bis = new BufferedInputStream(in);
                int n = 0;
                while ((n = bis.read(buffer, 0, bufferSize)) != -1) {
                    bos.write(buffer, 0, n);
                }
                response.flushBuffer();
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                if (bis != null) {
                    bis.close();
                }
                try {
                    if (bos != null) {
                        bos.close();
                    }
                } catch (Exception e) {
                    e.fillInStackTrace();
                }
                if (outputStream != null) {
                    outputStream.flush();
                    outputStream.close();
                }
            }
            return AjaxResult.success(filename);
        } catch (Exception e) {
            log.error("关闭flush失败{}", e.getMessage());
            return AjaxResult.error("导出Excel失败，请联系网站管理员！");
        }


    }
}
