package com.sccl.modules.business.modelegetprice.domain;

import com.sccl.framework.web.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;


/**
 * 来自官网的单价数据表 power_modele_getprice
 *
 * <AUTHOR>
 * @date 2023-03-03
 */
@Data
public class ModeleGetprice extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 省份
     */
    private String province;
    /**
     * 年份
     */
    private String year;
    /**
     * 月份
     */
    private String month;
    /**
     * 发布国网代购价
     */
    private BigDecimal indirectcountry;
    /**
     * 发布损益
     */
    private BigDecimal profitloss;
    /**
     * 线损
     */
    private BigDecimal lineLoss;
    /**
     * 基金附加费
     */
    private BigDecimal appendFund;

    public BigDecimal getAppendFund() {
        return appendFund;
    }

    public void setAppendFund(BigDecimal appendFund) {
        this.appendFund = appendFund;
    }


    public void setProvince(String province) {
        this.province = province;
    }

    public String getProvince() {
        return province;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getYear() {
        return year;
    }

    public void setMonth(String month) {
        this.month = month;
    }

    public String getMonth() {
        return month;
    }

    public void setIndirectcountry(BigDecimal indirectcountry) {
        this.indirectcountry = indirectcountry;
    }

    public BigDecimal getIndirectcountry() {
        return indirectcountry;
    }

    public void setProfitloss(BigDecimal profitloss) {
        this.profitloss = profitloss;
    }

    public BigDecimal getProfitloss() {
        return profitloss;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("province", getProvince())
                .append("year", getYear())
                .append("month", getMonth())
                .append("indirectcountry", getIndirectcountry())
                .append("profitloss", getProfitloss())
                .toString();
    }
}
