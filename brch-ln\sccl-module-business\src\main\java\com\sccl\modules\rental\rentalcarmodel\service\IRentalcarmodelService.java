package com.sccl.modules.rental.rentalcarmodel.service;

import com.sccl.modules.rental.rentalcarmodel.domain.Rentalcarmodel;
import com.sccl.framework.service.IBaseService;

import java.util.List;
import java.util.Map;

/**
 * 车辆 （model） 服务层
 * 
 * <AUTHOR>
 * @date 2019-08-23
 */
public interface IRentalcarmodelService extends IBaseService<Rentalcarmodel>
{


    int removeByid(Long modelid);

    List<Rentalcarmodel> selectListByIds(String[] toStrArray);
    List<Map<String,Object>> statistical(Rentalcarmodel rentalcarmodel);

}
