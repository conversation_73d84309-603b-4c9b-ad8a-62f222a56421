package com.sccl.modules.common.domain;

import com.sccl.common.constant.enums.TicketTaxRateTypeEnum;
import com.sccl.common.lang.StringUtils;
import com.sccl.common.utils.BigDecimlUtil;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 常量校验类
 * @date 2024/9/12  10:39
 */
public class ConstantCheck {

    public static final String datePattern = "^\\d{4}\\.(0[1-9]|1[0-2])\\.(0[1-9]|[12]\\d|3[01])$";

    /**
     * 普票含税金额校验、专票含税金额校验、专票税率校验
     * @param ticketMoney
     * @param taxTicketMoney
     * @param taxImportRate
     * @param result
     */
    public static void taxTicketCheck(BigDecimal ticketMoney, BigDecimal taxTicketMoney,
                                      BigDecimal taxImportRate, boolean isImport, StringBuilder result){
        // 普票含税金额校验
        if (null == ticketMoney || ticketMoney.compareTo(BigDecimal.ZERO) < 0) {
            result.append("普票含税金额为空或为负数，");
        }
        // 专票含税金额校验
        if (null == taxTicketMoney || taxTicketMoney.compareTo(BigDecimal.ZERO) < 0) {
            result.append("专票含税金额为空或为负数，");
        }
        // 专票税率校验
        if (isImport) {
            if (null == taxImportRate
                    || !TicketTaxRateTypeEnum.getInfoList().contains(BigDecimlUtil.convertPercent(taxImportRate, 2))) {
                result.append("专票税率为空或税率不正确，");
            }
        }
    }

    /**
     * 备注校验
     * @param otherFee
     * @param remark
     * @param result
     */
    public static void checkRemark(BigDecimal otherFee, String remark, StringBuilder result){
        if (null != otherFee && otherFee.compareTo(BigDecimal.ZERO) > 0) {
            result.append(StringUtils.isEmpty(remark) ? "有其他费用时备注不能为空，" : "");
        }
        if (!StringUtils.isEmpty(remark) && remark.length() > 500) {
            result.append("备注过长,");
        }
    }

    /**
     * 校验费用发生日
     * @param accountNo
     * @param feeStartDate
     */
    public static void feeStartDateCheck(String accountNo, String feeStartDate, StringBuilder result){
        if (StringUtils.isEmpty(feeStartDate) || !feeStartDate.matches(datePattern)) {
            result.append("费用发生日为空或格式不正确，");
        }
        if (StringUtils.isNotEmpty(accountNo) && StringUtils.isNotEmpty(feeStartDate)) {
            // 获取期号中的月份
            String accountMonth = accountNo.substring(4, 6);
            // 获取费用发生日中的月份
            String feeMonth = feeStartDate.substring(5,7);
            // 日期校验
            if (!accountMonth.equals(feeMonth)) {
                result.append("费用发生日错误，");
            }
        }
    }

    /**
     * 校验费用发生日
     * @param accountNo
     */
    public static void accountNoCheck(String accountNo, StringBuilder result){
        if (StringUtils.isEmpty(accountNo) || !accountNo.matches("\\d{4}(0[1-9]|1[0-2])")) {
            result.append("期号为空或格式不正确，");
        }
    }
}
