package com.sccl.modules.mssaccount.dataanalysis.vo;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sccl.framework.serialize.PercentageBigDecimalSerialize;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 报账单统计分析
 */
@Data
public class ElectricityBillResultVO {

    /**
     * 时间期号
     */
    private String index;

    /**
     * 公司ID
     */
    private Long orgId;

    /**
     * 所属公司
     */
    private String orgName;

    /**
     * 总用电量(千瓦时)
     */
    private BigDecimal kwh;

    /**
     * 同比-总用电量(千瓦时)
     */
    @JsonSerialize(using = PercentageBigDecimalSerialize.class)
    private BigDecimal yearOverYearTotalKWh;

    /**
     * 环比-总用电量(千瓦时)
     */
    @JsonSerialize(using = PercentageBigDecimalSerialize.class)
    private BigDecimal yearOverMonthTotalKWh;


    /**
     * 平均电单价
     */
    private BigDecimal unitPrice;

    /**
     * 同比-平均电单价
     */
    @JsonSerialize(using = PercentageBigDecimalSerialize.class)
    private BigDecimal yearOverYearUnitPrice;

    /**
     * 环比-平均电单价
     */
    @JsonSerialize(using = PercentageBigDecimalSerialize.class)
    private BigDecimal yearOverMonthUnitPrice;

    /**
     * 总电费(元)
     */
    private BigDecimal electricityCost;

    /**
     * 同比-总电费(元)
     */
    @JsonSerialize(using = PercentageBigDecimalSerialize.class)
    private BigDecimal yearOverYearElectricityCost;

    /**
     * 环比-总电费(元)
     */
    @JsonSerialize(using = PercentageBigDecimalSerialize.class)
    private BigDecimal yearOverMonthElectricityCost;

}