package com.sccl.modules.business.statinAudit.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.sccl.common.utils.SpringUtil;
import com.sccl.modules.autojob.util.convert.DateUtils;
import com.sccl.modules.business.account.domain.Account;
import com.sccl.modules.business.account.domain.AccountBaseResult;
import com.sccl.modules.business.account.domain.AccountCondition;
import com.sccl.modules.business.account.domain.AccountQua;
import com.sccl.modules.business.account.mapper.AccountMapper;
import com.sccl.modules.business.account.service.IAccountService;
import com.sccl.modules.business.accountEs.domain.AccountEsResult;
import com.sccl.modules.business.accountEs.domain.PowerAccountEs;
import com.sccl.modules.business.accountEs.service.IPowerAccountEsService;
import com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol;
import com.sccl.modules.business.ammeterorprotocol.dto.AmmeterorprotocolDto;
import com.sccl.modules.business.ammeterorprotocol.mapper.AmmeterorprotocolMapper;
import com.sccl.modules.business.ammeterorprotocol.service.IAmmeterorprotocolService;
import com.sccl.modules.business.jhanomalydetails.domain.JhAnomalyDetails;
import com.sccl.modules.business.jhanomalydetails.service.IJhAnomalyDetailsService;
import com.sccl.modules.business.poweraudit.entity.PowerAuditEntity;
import com.sccl.modules.business.poweraudit.mapper.PowerAuditMapper;
import com.sccl.modules.business.poweraudit.service.PowerAuditService;
import com.sccl.modules.business.statinAudit.mapper.PowerStationInfoMapper;
import com.sccl.modules.business.stationinfo.domain.PowerStationInfoRJtlte;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: 李佳杰
 * @CreateTime: 2024-02-19  15:32
 * @Description: 稽核工具类
 * @Version: 1.0
 */
@Slf4j
public class StationAuditUtil {


    private static IAmmeterorprotocolService ammeterorprotocolService;
    private static IAccountService accountService;
    private static PowerStationInfoMapper powerStationInfoMapper;
    private static PowerAuditService powerAuditService;
    private static IPowerAccountEsService iPowerAccountEsService;

    private static PowerAuditMapper powerAuditMapper;
    private static AccountMapper accountMapper;
    private static IJhAnomalyDetailsService iJhAnomalyDetailsService;
    private static SimpleDateFormat format = new SimpleDateFormat("yyyyMM");

//    private static SysConfigMapper sysConfigMapper;

    private static AmmeterorprotocolMapper ammeterorprotocolMapper;

//    private static AuditSyncFeedBackMapper auditSyncFeedBackMapper;

    static {
        ammeterorprotocolService = (IAmmeterorprotocolService) SpringUtil.getBean("ammeterorprotocolServiceImpl");
        accountService = (IAccountService) SpringUtil.getBean("accountServiceImpl");
        log.info("accountService getBean {}", loadBeanFlag() ? "is null" : "is not null");
        powerStationInfoMapper = (PowerStationInfoMapper) SpringUtil.getBean("powerStationInfoMapper");
        powerAuditService = (PowerAuditService) SpringUtil.getBean("powerAuditServiceImp");
        powerAuditMapper = SpringUtil.getBean(PowerAuditMapper.class);
        accountMapper = SpringUtil.getBean(AccountMapper.class);
        iJhAnomalyDetailsService = (IJhAnomalyDetailsService)
                SpringUtil.getBean("jhAnomalyDetailsServiceImpl");
        iPowerAccountEsService = (IPowerAccountEsService)
                SpringUtil.getBean("powerAccountEsServiceImpl");
//        sysConfigMapper = SpringUtil.getBean(SysConfigMapper.class);
        ammeterorprotocolMapper = SpringUtil.getBean(AmmeterorprotocolMapper.class);
//        auditSyncFeedBackMapper = SpringUtil.getBean(AuditSyncFeedBackMapper.class);
    }

    public static boolean loadBeanFlag() {
        return accountService == null;
    }

    public static void loadBean() {
        if (loadBeanFlag()) {
            ammeterorprotocolService = (IAmmeterorprotocolService) SpringUtil.getBean("ammeterorprotocolServiceImpl");
            accountService = (IAccountService) SpringUtil.getBean("accountServiceImpl");
            powerStationInfoMapper = (PowerStationInfoMapper) SpringUtil.getBean("powerStationInfoMapper");
            powerAuditService = (PowerAuditService) SpringUtil.getBean("powerAuditServiceImp");
            powerAuditMapper = SpringUtil.getBean(PowerAuditMapper.class);
            accountMapper = SpringUtil.getBean(AccountMapper.class);
            iJhAnomalyDetailsService = (IJhAnomalyDetailsService)
                    SpringUtil.getBean("jhAnomalyDetailsServiceImpl");
            iPowerAccountEsService = (IPowerAccountEsService)
                    SpringUtil.getBean("powerAccountEsServiceImpl");
//            sysConfigMapper = SpringUtil.getBean(SysConfigMapper.class);
            ammeterorprotocolMapper = SpringUtil.getBean(AmmeterorprotocolMapper.class);
//            auditSyncFeedBackMapper = SpringUtil.getBean(AuditSyncFeedBackMapper.class);
        }
    }


    public static List<String> filterEleType(List<Account> modes) {

        List<String> result = new ArrayList<>();
        List<String> ammeterCodes = new ArrayList<>();
        // 因为不同台账，传入的协议/电表属性不一样
        modes.forEach(node->{
            if(StrUtil.isNotBlank(node.getAmmetercode())){
                ammeterCodes.add(node.getAmmetercode());

            } else if(StrUtil.isNotBlank(node.getAmmetername())){
                ammeterCodes.add(node.getAmmetername());
            }
        });
        // 去重
        List<String> collect = ammeterCodes.stream().distinct().collect(Collectors.toList());
        List<Ammeterorprotocol> list =  ammeterorprotocolService.getEleTypeByAmmeterCodes(collect);
        if(CollectionUtil.isEmpty(list)){
            return result;
        }
        list.forEach(node->{
            if(node.getCategory() == 1){
                result.add(node.getAmmetername());
            } else {
                result.add(node.getProtocolname());
            }
        });
        return result;
    }

    private static String getYmmcAccount(Account account) {
        Ammeterorprotocol ammeterorprotocol = ammeterorprotocolService.get(account.getAmmeterid());
        if (ObjectUtil.isNotEmpty(ammeterorprotocol) &&
                ObjectUtil.isNotEmpty(ammeterorprotocol.getElectrotype()) &&
                ammeterorprotocol.getElectrotype().compareTo(1400L) > 0) {
            if (2 == ammeterorprotocol.getProperty().intValue()) {
                if (ObjectUtil.isEmpty(account.getToweraccountid())) {
                    return "铁塔电费台账";
                } else {
                    return "铁塔对账台账";
                }
            } else {
                return "自有电费台账";
            }
        } else {
            return "";
        }
    }

    private static String getYmmcAccountEs(PowerAccountEs powerAccountEs) {
        Ammeterorprotocol ammeterorprotocol = ammeterorprotocolService.get(powerAccountEs.getAmmeterid());
        if (ObjectUtil.isNotEmpty(ammeterorprotocol) &&
                ObjectUtil.isNotEmpty(ammeterorprotocol.getElectrotype()) &&
                ammeterorprotocol.getElectrotype().compareTo(1400L) > 0) {
            if (2 == ammeterorprotocol.getProperty().intValue()) {
                if (1 == powerAccountEs.getAccountestype().intValue()) {
                    return "铁塔预估电费台账";
                } else {
                    return "铁塔挂账电费台账";
                }
            } else {
                switch (powerAccountEs.getAccountestype().intValue()) {
                    case 1:
                        return "自有预估电费台账";
                    case 2:
                        return "自有挂账收款电费台账";
                    default:
                        return "自有预付电费台账";
                }
            }
        } else {
            return "";
        }
    }


    private static AccountCondition  accountCondition (String accountno,Account account,Integer type){
        AccountCondition accountCondition = new AccountCondition();
        if(ObjectUtil.isNotNull(account.getAmmeterid())){
            accountCondition.setAmmeterid(account.getAmmeterid());
        } else {
            String ammeterCode = account.getAmmetercode()!=null?account.getAmmetercode():account.getAmmetername();
            // 通过电表code去查询电表id
            Ammeterorprotocol ammeterorprotocol = ammeterorprotocolService.getAmmeterByCode(ammeterCode);
            if(ObjectUtil.isNotNull(ammeterorprotocol)){
                accountCondition.setAmmeterid(ammeterorprotocol.getId());
            } else {
                accountCondition.setAmmetercode(ammeterCode);
            }
        }
        if(StrUtil.isNotBlank(account.getAmmetername())){
            account.setAmmetercode(account.getAmmetername());
        }
        accountCondition.setAccountType(type);
        accountCondition.setVersion("ln");
        accountCondition.setAccountno(accountno);
        return accountCondition;
    }

    private static AccountCondition  accountTowerCondition(Account account){
        AccountCondition accountCondition = new AccountCondition();
        accountCondition.setAccountType(2);
        accountCondition.setPcid(account.getPcid());
        if (account.getAmmetercode() != null) {
            accountCondition.setAmmetercode(account.getAmmetercode());
        } else {
            accountCondition.setAmmetercode(account.getAmmetername());
        }
        return accountCondition;
    }
    static Map checkStationAudit(Boolean checkMssAccountbill, Account account, List<MssAccountbill> mssAccountbills, String ymmc) throws ParseException {
        String stationcodeId;
        String stationcode = "";
        String rJtlteCode = "";
        String rJtltetaCode = "";
        Date date = new Date();
        String wxdtsrjdl = null;
        //避免mysql存入的时候，将毫秒自动四舍五入，导致查询出问题
        date = DateUtils.round(date, Calendar.SECOND);
        /**电表编码白名单*/
        String whilteList = "PJZ,ZFZ";
        Double PUE = 1.3d;
        /**电量稽核台电日均波动阈值*/
        Double volatilityThreshold = 0.2D;

        /** 期号*/
        String accountno = account.getAccountno();
        //获取上一期的期号
        accountno = preAccountNo(accountno);

        /**获取上一期台账数据*/
        List<AccountBaseResult> accountBaseResults = new ArrayList();
        switch (ymmc) {
            case "自有电费台账":
            case "新增自有报账单":
            case "新增报账单（非电）": {
                AccountCondition accountCondition = accountCondition(accountno,account, 1);
                accountBaseResults = accountService
                        .selectSelfList(accountCondition);
                break;
            }
            case "自有预估电费台账": {
                accountBaseResults = yftzff(1, 1, account, accountno, accountBaseResults);
                break;
            }
            case "自有挂账收款电费台账": {
                accountBaseResults = yftzff(1, 2, account, accountno, accountBaseResults);
                break;
            }
            case "自有预付电费台账": {
                accountBaseResults = yftzff(1, 3, account, accountno, accountBaseResults);
                break;
            }
            case "铁塔电费台账":
            case "新增铁塔报账单": {
                AccountCondition accountCondition = accountCondition(accountno,account, 2);
                accountBaseResults = accountService
                        .selectSelfList(accountCondition);
                break;
            }
            case "铁塔预估电费台账": {

                accountBaseResults = yftzff(2, 1, account, accountno, accountBaseResults);
                break;
            }
            case "铁塔挂账电费台账": {
                accountBaseResults = yftzff(2, 2, account, accountno, accountBaseResults);
                break;
            }
            case "铁塔对账台账": {
                AccountCondition accountCondition = accountTowerCondition(account);
                accountBaseResults = accountMapper
                        .selectTowerCheckList(accountCondition);
                break;
            }
            default:
                break;
        }
//        AuditSyncFeedBack auditSyncFeedBack = new AuditSyncFeedBack();

        //稽核结果(结果0为否，1为异常(是))
        Map result = new HashMap<String, Object>();
        //获取电表编号
        Long ammeterid = account.getAmmeterid();
        /**检查当前台账数据是否是一表多站还是一站多表*/
        Map accountMap = powerStationInfoMapper.selectAccountState(ammeterid);
        List<Ammeterorprotocol> list = accountMapper.queryAmmeterByAmmeterId(account.getAmmeterid());
        //获取当前电表的基础信息
        if (list.size() == 0) {
            throw new RuntimeException("无对应的基础电表，本次稽核无效");
        }
        Ammeterorprotocol ammeterorprotocol = list.get(0);
        /**获取相关的局站数据*/
        stationcodeId = ammeterorprotocol.getStationcode();
        /**获取相关的局站名称*/
        String stationName = ammeterorprotocol.getStationName();
        /** 本期起始时间*/
        String startdate = account.getStartdate();
        /**本期截止时间*/
        String enddate = account.getEnddate();

        List<String> list2 = Arrays.asList("铁塔电费台账", "铁塔对账台账");

        /**检查是否是报账稽核*/
//        if (checkMssAccountbill) {
//            auditSyncFeedBack.setOperate("加入报账单及问题反馈");
//            // 查看配置是否开启报账稽核
//            SysConfig sysConfig = sysConfigMapper.getConfigByCode("bzjh");
//            // 只有自有和铁塔的基站才开启稽核
//            List<Long> codes = Arrays.asList(1411L, 1412L, 1431L, 1432L);
//            if("0".equals(sysConfig.getConfigValue()) || !codes.contains(ammeterorprotocol.getElectrotype()) ){
//                // 不开启
//                result.put("支付对象一致性", "是");
//            } else {
////                String sumwxdtsrjdl = powerStationInfoMapper
////                        .selectSumPowerStationQuaSta(stationcodeId, startdate, enddate);
////                result.put("台账总和", sumwxdtsrjdl);
//                /** 支付对象一致性 */
//                result = checkD(date, account, ammeterorprotocol, result, mssAccountbills);
//            }
//            result.put("一表多站/多站多表等", "否");
//            result.put("电表站址一致性", "是");
//            result.put("分摊比例准确性", "是");
//            result.put("分摊比例一致性", "是");
//            result.put("大数据电量异常", "是");
//            result.put("电表度数连续性", "是");
//            result.put("周期合理性", "是");
//            result.put("日均耗电量过低", "是");
//            result.put("电量波动异常", "是");
//            result.put("电价合理性", "是");
//            result.put("台账周期连续性", "是");
//            result.put("沉默电表", "否");
//
//        } else {
//            auditSyncFeedBack.setOperate("加入归集单及问题反馈");
            log.info("稽核流程开启,电表编号为：{}",ammeterid);
            /**本期电量*/
            BigDecimal totalusedreadings = ObjectUtil.isNull(account.getTotalusedreadings()) ? new BigDecimal(0) : account.getTotalusedreadings();
            SimpleDateFormat simpleFormat = new SimpleDateFormat("yyyyMMdd");
            /*天数差*/
            Date fromDate1 = simpleFormat.parse(startdate);
            Date toDate1 = simpleFormat.parse(enddate);
            long from1 = fromDate1.getTime();
            long to1 = toDate1.getTime();
            int days = (int) ((to1 - from1) / (1000 * 60 * 60 * 24));
            BigDecimal divide = new BigDecimal("0");

            BigDecimal totalUsePower = new BigDecimal("0");
            /**台账日均耗电量数值*/
            if (ObjectUtil.isNotNull(totalusedreadings) && totalusedreadings.compareTo(new BigDecimal(0)) != 0) {
                divide = totalusedreadings.divide(BigDecimal.valueOf(days + 1), 2,RoundingMode.HALF_UP);
                totalUsePower = totalusedreadings;
            } else if (ObjectUtil.isNotNull(account.getCurusedreadings()) && account.getCurusedreadings().compareTo(new BigDecimal(0)) != 0) {
                divide = account.getCurusedreadings().divide(BigDecimal.valueOf(days + 1), 2,RoundingMode.HALF_UP);
                totalUsePower = account.getCurusedreadings();
            }


            Ammeterorprotocol ammeterorprotocol1 = new Ammeterorprotocol();
            ammeterorprotocol1.setCountrys(new ArrayList());
            if(accountMap != null){
                // 1:电表
                if (Integer.valueOf(String.valueOf(accountMap.get("category"))) == 1) {
                    ammeterorprotocol1.setAmmetername(String.valueOf(accountMap.get("ammetername")));

                } else {
                    ammeterorprotocol1.setProtocolname(String.valueOf(accountMap.get("protocolname")));
                    ammeterorprotocol1.setType(1);
                }
            }

            Map map = powerStationInfoMapper.selectStationCodeById(Long.valueOf(stationcodeId));
            if (map != null) {
                stationcode = map.get("局站编码") == null ? "" : map.get("局站编码").toString();
            }
            String id5gr = powerStationInfoMapper.getId5gr(stationcodeId);
            /**根据局站id查询铁塔编码*/
            Map map1 = powerStationInfoMapper
                    .selectStationRJtlteBystationId(Long.valueOf(stationcodeId));
            if (map1 != null) {
                rJtlteCode = map1.get("集团LTE站址编码") == null ? "" : map1.get("集团LTE站址编码").toString();
                rJtltetaCode = map1.get("集团LTE铁塔站址编码") == null ? "" : map1.get("集团LTE铁塔站址编码").toString();
            }

            log.info("获取局站的对应编码");
            String resstationcode = powerStationInfoMapper.getResstationcode(stationcodeId);

            /**获取无线大数据平台推送的日均电量  修改为5gr站址编码*/
            wxdtsrjdl = powerStationInfoMapper
                    .selectPowerStationQuaSta(resstationcode, startdate, enddate);

            /**查询铁塔站址编码*/
            /** 稽核规则 */
            /** 电表与站址的关系 */
//            result = checkA(date, account, accountMap, rJtlteCode, rJtltetaCode, result);
            //默认通过
            result.put("一表多站/多站多表等", "否");
            /** 电表站址一致性 */
            result = checkC(date, account, result,  rJtlteCode, rJtltetaCode, stationName);

            /** 电价合理性 */
            result = checkB(date, account, ammeterorprotocol,  result);

            /** 台账周期连续性 */
            result = checkE(date, account, accountBaseResults, result,  rJtlteCode, rJtltetaCode);
            /** 电表读数连续性 */
            result = checkF(ymmc, date, account, accountBaseResults, result,  rJtlteCode, rJtltetaCode);
            /** 台账电量合理性 */
//            result = checkG(date, account, result);
            //默认通过
            result.put("大数据电量异常", "是");
            /**日均耗电量过低站址编码白名单*/
            boolean b = checkWhiteList(stationcode, whilteList);
            //跳过日均电量检查 add by qxm ********
            b = true;
            /**台账日均耗电量波动合理性*/
            result = checkH(date, account,b,totalUsePower,result
                    ,  rJtlteCode, rJtltetaCode,startdate,enddate,ammeterorprotocol);
            /** 台账日均耗电量合理性 */
            result = checkI(date, account, b, divide, result,
                    rJtlteCode, wxdtsrjdl, rJtltetaCode);

            /**检查是否铁塔电费台账或者铁塔对账台账*/
            if (list2.contains(ymmc)) {
                /** 分摊比例准确性 */
                result = checkJ(date, account, result, rJtlteCode, rJtltetaCode,ammeterorprotocol
                );
                /** 分摊比例一致性 */
//                result = checkN(date, account, result, rJtlteCode, rJtltetaCode, accountBaseResults);
            } else {
                result.put("分摊比例准确性", "是");
//                result.put("分摊比例一致性", "是");
            }


            /**台账周期异常*/
            result = checkL(date, account, days, result,  rJtlteCode,rJtltetaCode, accountBaseResults);
            /**沉默电表*/
            result = checkM(date, result, accountBaseResults);
            result.put("支付对象一致性", "是");
//        }


        /** 台账周期异常 */
        /** 电表台账的起、止时间，天数差大于N天（暂定200） */
        PowerAuditEntity powerAuditEntity = new PowerAuditEntity();
        /****电表ID   add by qinxinmin 2024-10-23   ******/
        powerAuditEntity.setPamId(ammeterid);
        /**
         * 地市
         */
        /**获取当前公司的编码*/
        powerAuditEntity.setCityCode(ammeterorprotocol.getCompany().toString());
        powerAuditEntity.setCity(ammeterorprotocol.getCompanyName());
        /**获取责任中心的编码*/
        powerAuditEntity.setCountyCompaniesCode(ammeterorprotocol.getCountry().toString());
        powerAuditEntity.setCountyCompanies(ammeterorprotocol.getCountryName());
        powerAuditEntity.setOperationsBranch(ammeterorprotocol.getSubstation());
        /**
         * 时间（月）
         */
        SimpleDateFormat format = new SimpleDateFormat("yyyyMM");
        powerAuditEntity.setCtgKey(account.getCtgKey());
        powerAuditEntity.setMonth(format.format(date.getTime()));
        powerAuditEntity.setAmmeterid(String.valueOf(accountMap.get("电表编号")));
        // 集团站址编码
        powerAuditEntity.setStationcode(rJtlteCode);
        // 铁塔站址编码
        powerAuditEntity.setTowerSiteCode(rJtltetaCode);

        powerAuditEntity.setMutiJtlteCodes(result.get("一表多站/多站多表等") == null ? null : String.valueOf(result.get("一表多站/多站多表等")));
        powerAuditEntity.setPaymentConsistence(result.get("支付对象一致性") == null ? null : String.valueOf(result.get("支付对象一致性")));
        powerAuditEntity.setAddressConsistence(result.get("电表站址一致性") == null ? null : String.valueOf(result.get("电表站址一致性")));
        powerAuditEntity.setRepeat(result.get("重复交叉缴费") == null ? null : String.valueOf(result.get("重复交叉缴费")));
        powerAuditEntity.setElectricityRationality(result.get("大数据电量异常") == null ? null : String.valueOf(result.get("大数据电量异常")));
        powerAuditEntity.setElectricityContinuity(result.get("电表度数连续性") == null ? null : String.valueOf(result.get("电表度数连续性")));
        powerAuditEntity.setElectricityMeter(result.get("沉默电表") == null ? null : String.valueOf(result.get("沉默电表")));
        powerAuditEntity.setPeriodicAnomaly(result.get("周期合理性") == null ? null : String.valueOf(result.get("周期合理性")));
        powerAuditEntity.setShareAccuracy(result.get("分摊比例准确性") == null ? null : String.valueOf(result.get("分摊比例准确性")));
//        powerAuditEntity.setConsistencyProportion(result.get("分摊比例一致性") == null ? null : String.valueOf(result.get("分摊比例一致性")));
        powerAuditEntity.setConsumeContinuity(result.get("日均耗电量过低") == null ? null : String.valueOf(result.get("日均耗电量过低")));
        powerAuditEntity.setFluctuateContinuity(result.get("电量波动异常") == null ? null : String.valueOf(result.get("电量波动异常")));
        powerAuditEntity.setElectricityPrices(result.get("电价合理性") == null ? null : String.valueOf(result.get("电价合理性")));
        powerAuditEntity.setReimbursementCycle(result.get("台账周期连续性") == null ? null : String.valueOf(result.get("台账周期连续性")));
        powerAuditEntity.setElectricityMeter(result.get("沉默电表") == null ? null : String.valueOf(result.get("沉默电表")));
        powerAuditEntity.setIfQkSuccess(1);
        powerAuditEntity.setCreateTimeStr(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date));
        powerAuditEntity.setIfSuccess(1);
        for (Object key : result.keySet()) {
            if (String.valueOf(key).equals("一表多站/多站多表等")) {
                if (result.get(key).equals("是")) {
                    powerAuditEntity.setIfSuccess(0);
                    powerAuditEntity.setIfQkSuccess(0);
                    break;
                }
            } else if (String.valueOf(key).equals("台账总和")) {
                continue;
            } else {
                if (result.get(key).equals("否")) {
                    powerAuditEntity.setIfSuccess(0);
                    List list1 = new ArrayList();
                    list1.add("电价合理性");
                    list1.add("电表站址一致性");
                    list1.add("台账周期连续性");
                    list1.add("电表度数连续性");
                    list1.add("电量波动异常");
                    if (list2.contains(ymmc)) {
                        list1.add("分摊比例准确性");
                    }
                    if (checkMssAccountbill) {
                        list1.add("支付对象一致性");
                    }
                    if (list1.contains(key)) {
                        powerAuditEntity.setIfQkSuccess(0);
                        break;
                    }
                }
            }
        }
        powerAuditEntity.setSiteType(1);
        powerAuditEntity.setAuditTime(date);
        powerAuditEntity.setPcid(String.valueOf(account.getPcid()));
        powerAuditEntity.setLedgerPeriod(account.getAccountno());

        if (CollectionUtil.isNotEmpty(mssAccountbills)) {
            List<MssAccountbill> collect = mssAccountbills.stream()
                    .filter(mssAccountbill -> mssAccountbill.getId().equals(account.getBillId())).collect(
                            Collectors.toList());
            if (collect.size() != 0) {
                powerAuditEntity.setMssAccountId(String.valueOf(collect.get(0).getId()));
                powerAuditEntity.setApplyTime(collect.get(0).getCreateDate() == null ? date : collect.get(0).getCreateDate());
            }
        }
        saveStorage(powerAuditEntity);
        // 保存稽核结果反馈至铁塔方
//        saveAuditSyncFeedBack(auditSyncFeedBack,account,result);
        result.put("稽核结果", powerAuditEntity);

        return result;
    }

//    private static void saveAuditSyncFeedBack(AuditSyncFeedBack auditSyncFeedBack, Account account, Map result) {
//        if(ObjectUtil.isNotNull(account.getToweraccountid())){
//            auditSyncFeedBack.setTowerId(account.getToweraccountid());
//            auditSyncFeedBack.setSyncFlag("0");
//            auditSyncFeedBack.setFeedBack(JSON.toJSONString(result));
//            auditSyncFeedBackMapper.insert(auditSyncFeedBack);
//        }
//    }

    public static Map<String, Object> successStationAudit(Boolean checkMssAccountbill, Account account,
                                                          List<MssAccountbill> mssAccountbills, String ymmc) {
        //稽核结果(结果0为否，1为异常(是))
        Map result = new HashMap<String, Object>();
        Date date = new Date();
        PowerAuditEntity powerAuditEntity = new PowerAuditEntity();
        /**
         * 时间（月）
         */
        SimpleDateFormat format = new SimpleDateFormat("yyyyMM");
        powerAuditEntity.setCtgKey(account.getCtgKey());
        powerAuditEntity.setPamId(account.getAmmeterid());
        powerAuditEntity.setAmmeterid(StrUtil.isBlank(account.getAmmetername())?
                account.getAmmetercode():account.getAmmetername());
        powerAuditEntity.setMonth(format.format(date.getTime()));
        powerAuditEntity.setMutiJtlteCodes("否");
        powerAuditEntity.setPaymentConsistence("是");
        powerAuditEntity.setAddressConsistence("是");
        powerAuditEntity.setRepeat("是");
        powerAuditEntity.setElectricityRationality("是");
        powerAuditEntity.setElectricityContinuity("是");
        powerAuditEntity.setElectricityMeter("是");
        powerAuditEntity.setPeriodicAnomaly("是");
        powerAuditEntity.setShareAccuracy("是");
        powerAuditEntity.setConsistencyProportion("是");
        powerAuditEntity.setConsumeContinuity("是");
        powerAuditEntity.setFluctuateContinuity("是");
        powerAuditEntity.setElectricityPrices("是");
        powerAuditEntity.setReimbursementCycle("是");
        powerAuditEntity.setElectricityMeter("否");
        powerAuditEntity.setIfQkSuccess(1);
        powerAuditEntity.setCreateTimeStr(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date));
        powerAuditEntity.setIfSuccess(1);
        powerAuditEntity.setSiteType(1);
        powerAuditEntity.setAuditTime(date);
        powerAuditEntity.setPcid(String.valueOf(account.getPcid()));
        powerAuditEntity.setLedgerPeriod(account.getAccountno());

        if (CollectionUtil.isNotEmpty(mssAccountbills)) {
            List<MssAccountbill> collect = mssAccountbills.stream()
                    .filter(mssAccountbill -> mssAccountbill.getId().equals(account.getBillId())).collect(
                            Collectors.toList());
            if (collect.size() != 0) {
                powerAuditEntity.setMssAccountId(String.valueOf(collect.get(0).getId()));
                powerAuditEntity.setApplyTime(collect.get(0).getCreateDate() == null ? date : collect.get(0).getCreateDate());
            }
        }
        saveStorage(powerAuditEntity);
        result.put("稽核结果", powerAuditEntity);
        return result;
    }

    public static Map<String, Object> successStationAuditnew(Boolean checkMssAccountbill, Account account,
                                                             List<MssAccountbill> mssAccountbills, String ymmc_in) throws ParseException{
        String ymmc="";String stationcodeId;
        Ammeterorprotocol ammeterorprotocol = ammeterorprotocolService.get(account.getAmmeterid());
        if (ObjectUtil.isNotEmpty(ammeterorprotocol) &&
                ObjectUtil.isNotEmpty(ammeterorprotocol.getElectrotype()) &&
                (ammeterorprotocol.getElectrotype() !=1411
                        &&ammeterorprotocol.getElectrotype() !=1412
                        &&ammeterorprotocol.getElectrotype() !=1421
                        &&ammeterorprotocol.getElectrotype() !=1422
                        &&ammeterorprotocol.getElectrotype() !=1431
                        &&ammeterorprotocol.getElectrotype() !=1432))
        {ymmc="自有电费台账";}
        else
            ymmc="";

        /** 期号*/
        String accountno = account.getAccountno();
        //获取上一期的期号
        accountno = preAccountNo(accountno);

        /**获取上一期台账数据*/
        List<AccountBaseResult> accountBaseResults = new ArrayList();

        switch (ymmc) {
            case "自有电费台账":
            case "新增自有报账单":
            case "新增报账单（非电）": {
                AccountCondition accountCondition = accountCondition(accountno,account, 1);
                accountBaseResults = accountService
                        .selectSelfList(accountCondition);
                break;
            }
            case "自有预估电费台账": {
                accountBaseResults = yftzff(1, 1, account, accountno, accountBaseResults);
                break;
            }
            case "自有挂账收款电费台账": {
                accountBaseResults = yftzff(1, 2, account, accountno, accountBaseResults);
                break;
            }
            case "自有预付电费台账": {
                accountBaseResults = yftzff(1, 3, account, accountno, accountBaseResults);
                break;
            }
            case "铁塔电费台账":
            case "新增铁塔报账单": {
                AccountCondition accountCondition = accountCondition(accountno,account, 2);
                accountBaseResults = accountService
                        .selectSelfList(accountCondition);
                break;
            }
            case "铁塔预估电费台账": {

                accountBaseResults = yftzff(2, 1, account, accountno, accountBaseResults);
                break;
            }
            case "铁塔挂账电费台账": {
                accountBaseResults = yftzff(2, 2, account, accountno, accountBaseResults);
                break;
            }
            case "铁塔对账台账": {
                AccountCondition accountCondition = accountTowerCondition(account);
                accountBaseResults = accountMapper
                        .selectTowerCheckList(accountCondition);
                break;
            }
            default:
                break;
        }
        //稽核结果(结果0为否，1为异常(是))
        Map result = new HashMap<String, Object>();
        //获取电表编号
        Long ammeterid = account.getAmmeterid();
        /**检查当前台账数据是否是一表多站还是一站多表*/
        //Map accountMap = powerStationInfoMapper.selectAccountState(ammeterid);
        List<Ammeterorprotocol> list = accountMapper.queryAmmeterByAmmeterId(account.getAmmeterid());
        //获取当前电表的基础信息
        if (list.size() == 0) {
            throw new RuntimeException("无对应的基础电表，本次稽核无效");
        }
        Ammeterorprotocol amme = list.get(0);
        /**获取相关的局站数据*/
        stationcodeId = amme.getStationcode();
        /**获取相关的局站名称*/
        String stationName = amme.getStationName();
        /** 本期起始时间*/
        String startdate = account.getStartdate();
        /**本期截止时间*/
        String enddate = account.getEnddate();

        Date date = new Date();



        PowerAuditEntity powerAuditEntity = new PowerAuditEntity();
        /**获取当前公司的编码*/
        powerAuditEntity.setCityCode(ammeterorprotocol.getCompany().toString());
        powerAuditEntity.setCity(ammeterorprotocol.getCompanyName());
        /**获取责任中心的编码*/
        powerAuditEntity.setCountyCompaniesCode(ammeterorprotocol.getCountry().toString());
        powerAuditEntity.setCountyCompanies(ammeterorprotocol.getCountryName());
        powerAuditEntity.setOperationsBranch(ammeterorprotocol.getSubstation());

        Integer directsupplyflag = ammeterorprotocol.getDirectsupplyflag();
        /** 电价合理性 */
        if (2 == directsupplyflag) {
            result = checkPricezg(date, account, ammeterorprotocol, result);
            result.put("一表多站/多站多表等", "否");
            result.put("电表站址一致性", "是");
            result.put("分摊比例准确性", "是");
            result.put("分摊比例一致性", "是");
            result.put("大数据电量异常", "是");
            result.put("电表度数连续性", "是");
            result.put("周期合理性", "是");
            result.put("日均耗电量过低", "是");
            result.put("电量波动异常", "是");
            /*            result.put("电价合理性", "是");*/
            result.put("台账周期连续性", "是");
            result.put("沉默电表", "否");

        } else {
            result.put("一表多站/多站多表等", "否");
            result.put("电表站址一致性", "是");
            result.put("分摊比例准确性", "是");
            result.put("分摊比例一致性", "是");
            result.put("大数据电量异常", "是");
            result.put("电表度数连续性", "是");
            result.put("周期合理性", "是");
            result.put("日均耗电量过低", "是");
            result.put("电量波动异常", "是");
            result.put("电价合理性", "是");
            result.put("台账周期连续性", "是");
            result.put("沉默电表", "否");
        }

        SimpleDateFormat format = new SimpleDateFormat("yyyyMM");
        powerAuditEntity.setCtgKey(account.getCtgKey());
        powerAuditEntity.setPamId(account.getAmmeterid());
        powerAuditEntity.setAmmeterid(StrUtil.isBlank(account.getAmmetername()) ?
                account.getAmmetercode() : account.getAmmetername());
        powerAuditEntity.setMonth(format.format(date.getTime()));
        powerAuditEntity.setMutiJtlteCodes("否");
        powerAuditEntity.setPaymentConsistence("是");
        powerAuditEntity.setAddressConsistence("是");
        powerAuditEntity.setRepeat("是");
        powerAuditEntity.setElectricityRationality("是");
        powerAuditEntity.setElectricityContinuity("是");
        powerAuditEntity.setElectricityMeter("是");
        powerAuditEntity.setPeriodicAnomaly("是");
        powerAuditEntity.setShareAccuracy("是");
        powerAuditEntity.setConsistencyProportion("是");
        powerAuditEntity.setConsumeContinuity("是");
        powerAuditEntity.setFluctuateContinuity("是");
        if("否".equals(String.valueOf(result.get("电价合理性")))){
            powerAuditEntity.setElectricityPrices("否");
            powerAuditEntity.setIfSuccess(0);
            powerAuditEntity.setIfQkSuccess(0);
        } else {
            powerAuditEntity.setElectricityPrices("是");
            powerAuditEntity.setIfSuccess(1);
            powerAuditEntity.setIfQkSuccess(1);
        }

        powerAuditEntity.setReimbursementCycle("是");
        powerAuditEntity.setElectricityMeter("否");

        powerAuditEntity.setCreateTimeStr(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date));

        powerAuditEntity.setSiteType(1);
        powerAuditEntity.setAuditTime(date);
        powerAuditEntity.setPcid(String.valueOf(account.getPcid()));
        powerAuditEntity.setLedgerPeriod(account.getAccountno());



        if (CollectionUtil.isNotEmpty(mssAccountbills)) {
            List<MssAccountbill> collect = mssAccountbills.stream()
                    .filter(mssAccountbill -> mssAccountbill.getId().equals(account.getBillId())).collect(
                            Collectors.toList());
            if (collect.size() != 0) {
                powerAuditEntity.setMssAccountId(String.valueOf(collect.get(0).getId()));
                powerAuditEntity.setApplyTime(collect.get(0).getCreateDate() == null ? date : collect.get(0).getCreateDate());
            }
        }

        saveStorage(powerAuditEntity);
        result.put("稽核结果", powerAuditEntity);
        return result;
    }

    /**
     * 保存稽核数据
     */
    static void saveStorage(PowerAuditEntity powerAuditEntity) {
        PowerAuditEntity temp = powerAuditMapper.getAuditByInfo(powerAuditEntity.getPcid(), powerAuditEntity.getMssAccountId());
        if (ObjectUtil.isEmpty(temp)) {
            powerAuditEntity.setId(IdWorker.getId());
            powerAuditMapper.insertPowerAudit(powerAuditEntity);
        } else {
            powerAuditEntity.setId(temp.getId());
            powerAuditMapper.updateForModel(powerAuditEntity);
        }
    }

    /**
     * 获取稽核数据上一期期号
     */
    static String preAccountNo(String accountno) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("yyyyMM");
        Date createTime = null;
        if (ObjectUtil.isNotEmpty(accountno)) {
            createTime = format.parse(accountno);
        } else {
            createTime = new Date();
        }
        Calendar c = Calendar.getInstance();
        c.setTime(createTime);
        c.add(Calendar.MONTH, -1);
        createTime = c.getTime();

        Calendar cal = Calendar.getInstance();
        cal.setTime(createTime);

        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH) + 1;
        if (month < 10) {
            return year + "0" + month;
        }
        return year + "" + month;
    }

    /**
     * 电表与站址的关系
     */
    public static Map checkA(Date date, Account account, Map accountMap,  String rJtlteCode, String rJtltetaCode, Map result) {
        List<JhAnomalyDetails> addList = new ArrayList<>();
        /**电表与站址的关系非一站一表，也不在白名单*/
        /**站址表id*/
        if (Integer.valueOf(String.valueOf(accountMap.get("一表多站"))) == 1 && Integer.valueOf(String.valueOf(accountMap.get("一站多表"))) == 1) {
            result.put("一表多站/多站多表等", "否");
        } else {
            List<String> list1 = powerStationInfoMapper
                    .selectAmmeterAccountWhiteList(account.getAmmeterid());
            if (list1.size() == 0) {
                result.put("一表多站/多站多表等", "是");
            } else {
                result.put("一表多站/多站多表等", "否");
            }
        }



        List<PowerStationInfoRJtlte> list = ammeterorprotocolService.getOneAmMoreSta(account.getAmmetercode());
        if (list.size() == 0) {
            getBaseEntity("一表多站", account.getAmmetercode(), rJtlteCode, rJtltetaCode, addList, account, date,result);
        } else {
            list.forEach(node -> {
                getBaseEntity("一表多站", account.getAmmetercode(), node.getJtlteCode(), node.getJtlteTacode(), addList, account, date,result);
            });
        }


        List<AmmeterorprotocolDto> oneStaMoreAmList = ammeterorprotocolService.getOneStaMoreAm(account.getAmmetercode());
        if(CollectionUtil.isNotEmpty(oneStaMoreAmList)){
            oneStaMoreAmList.forEach(node -> {
                getBaseEntity("一站多表", node.getCategory() == 1 ? node.getAmmetername()
                        : node.getProtocolname(), node.getJtlteCode(), node.getJtlteTacode(), addList, account, date,result);
            });
        }

        if (!addList.isEmpty()) {
            addList.forEach(node -> {
                iJhAnomalyDetailsService.insert(node);
            });
        }


        return result;

    }


    private static void getBaseEntity(String lx, String ammeterCode, String rJtlteCode, String rJtltetaCode, List<JhAnomalyDetails> addList
            , Account account, Date date,Map result) {
        JhAnomalyDetails jhAnomalyDetails = new JhAnomalyDetails();
        jhAnomalyDetails.setYclx("A");
        jhAnomalyDetails.setYclxmc("一表多站/多站多表等");
        /**设置报账单id*/
        jhAnomalyDetails.setBzdid(String.valueOf(account.getBillId()));
        jhAnomalyDetails.setTzid(String.valueOf(account.getPcid()));
        jhAnomalyDetails.setJhsj(date);
        /**设置台账期号*/
        jhAnomalyDetails.setTzqh(account.getAccountno());
        jhAnomalyDetails.setLx(lx);
        /**设置电表户名*/
        jhAnomalyDetails.setDbhm(ammeterCode);
        /**集团站址编码*/
        jhAnomalyDetails.setJtzzbm(rJtlteCode);
        /**铁塔站址编码*/
        jhAnomalyDetails.setTtzzbm(rJtltetaCode);
        if("否".equals(String.valueOf(result.get("一表多站/多站多表等")))){
            jhAnomalyDetails.setJhjg("0");
        } else {
            jhAnomalyDetails.setJhjg("1");
        }
        addList.add(jhAnomalyDetails);
    }

    /**
     * 电价合理性
     */
    private static Map checkB(Date date, Account account, Ammeterorprotocol ammeterorprotocol,  Map result) {
        Long id = ammeterorprotocol.getId();
        JhAnomalyDetails jhAnomalyDetails = new JhAnomalyDetails();
        jhAnomalyDetails.setYclx("B");
        jhAnomalyDetails.setYclxmc("电价合理性");
        /**设置报账单id*/
        jhAnomalyDetails.setBzdid(String.valueOf(account.getBillId()));
        jhAnomalyDetails.setTzid(String.valueOf(account.getPcid()));
        jhAnomalyDetails.setJhsj(date);

        Integer directsupplyflag = ammeterorprotocol.getDirectsupplyflag();

        List<String> list1 = powerStationInfoMapper
                .selectPriceAccountWhiteList(account.getAmmeterid());
        //增加白名单 qxm add
//        list1.add("测试：增加白名单");
        if (list1.size() == 0) {
            // 没在白名单内
            //直供电
            if (1 == directsupplyflag) {

                int i = account.getUnitpirce().compareTo(new BigDecimal(1.3));
                if (i == 1) {
                    //如果 a.compareTo(b) 返回 1，表示 a 大于 b
                    result.put("电价合理性", "否");
                } else {
                    result.put("电价合理性", "是");
                }
            }
            //转供电
            if (2 == directsupplyflag) {
                //协议单价
                if (ammeterorprotocol.getPrice() != null) {
                    BigDecimal price = ammeterorprotocol.getPrice().multiply(new BigDecimal("1.1479")).setScale(2, RoundingMode.HALF_UP);
                    BigDecimal tzPrice = account.getUnitpirce();
                    if (price.compareTo(BigDecimal.ZERO) == 0) {
                        result.put("电价合理性", "否");
                        //如果单价小于电表单价，则是正常的
                    } else if (price.compareTo(tzPrice) >= 0) {
                        result.put("电价合理性", "是");
                    } else {
                        result.put("电价合理性", "否");
                    }
                } else {
                    result.put("电价合理性", "否");
                }
            }
        } else {
            result.put("电价合理性", "是");

        }


        /**设置台账期号*/
        jhAnomalyDetails.setTzqh(account.getAccountno());
        /**设置电表户名*/
        jhAnomalyDetails.setDbhm(account.getAmmetercode());
        /**本次单价*/
        jhAnomalyDetails.setDj(account.getUnitpirce() == null ? null : account.getUnitpirce().toString());
        /**协议单价：电表单价*/
        jhAnomalyDetails.setXydj(ammeterorprotocol.getPrice() == null ? null : ammeterorprotocol.getPrice().toString());
        if("否".equals(String.valueOf(result.get("电价合理性")))){
            jhAnomalyDetails.setJhjg("1");
        } else {
            jhAnomalyDetails.setJhjg("0");
        }

        iJhAnomalyDetailsService.insert(jhAnomalyDetails);

        return result;
    }


    /**
     * 电价合理性直供电
     */
    private static Map checkPricezg(Date date, Account account, Ammeterorprotocol ammeterorprotocol,  Map result) {
        Long id = ammeterorprotocol.getId();
        JhAnomalyDetails jhAnomalyDetails = new JhAnomalyDetails();
        jhAnomalyDetails.setYclx("B");
        jhAnomalyDetails.setYclxmc("电价合理性");
        /**设置报账单id*/
        jhAnomalyDetails.setBzdid(String.valueOf(account.getBillId()));
        jhAnomalyDetails.setTzid(String.valueOf(account.getPcid()));
        jhAnomalyDetails.setJhsj(date);
        BigDecimal tzPrice=null;
        Integer directsupplyflag = ammeterorprotocol.getDirectsupplyflag();

        List<String> list1 = powerStationInfoMapper
                .selectPriceAccountWhiteList(account.getAmmeterid());
        if (list1.size() == 0) {
            // 没在白名单内
            //直供电
            if (1 == directsupplyflag) {

                int i = account.getUnitpirce().compareTo(new BigDecimal(1.3));
                if (i == 1) {
                    //如果 a.compareTo(b) 返回 1，表示 a 大于 b
                    result.put("电价合理性", "否");
                } else {
                    result.put("电价合理性", "是");
                }
            }
            //转供电
            if (2 == directsupplyflag) {
                //协议单价
                if (ammeterorprotocol.getPrice() != null) {
                    BigDecimal price = ammeterorprotocol.getPrice().setScale(2, RoundingMode.HALF_UP);
                    tzPrice= account.getTaxticketmoney().abs().add(account.getTicketmoney().abs()).subtract(account.getTaxamount().abs()).divide(account.getTotalusedreadings().abs(),2, RoundingMode.HALF_UP);
                    if (price.compareTo(BigDecimal.ZERO) == 0) {
                        result.put("电价合理性", "否");
                        //如果单价小于电表单价，则是正常的
                    } else if (price.compareTo(tzPrice) >= 0) {
                        result.put("电价合理性", "是");
                    } else {
                        result.put("电价合理性", "否");
                    }
                } else {
                    result.put("电价合理性", "否");
                }
            }
        } else {
            result.put("电价合理性", "是");

        }


        /**设置台账期号*/
        jhAnomalyDetails.setTzqh(account.getAccountno());
        /**设置电表户名*/
        jhAnomalyDetails.setDbhm(account.getAmmetercode());
        /**本次单价*/
        jhAnomalyDetails.setDj(tzPrice == null ? null : tzPrice.toString());
        /**协议单价：电表单价*/
        jhAnomalyDetails.setXydj(ammeterorprotocol.getPrice() == null ? null : ammeterorprotocol.getPrice().toString());
        if("否".equals(String.valueOf(result.get("电价合理性")))){
            jhAnomalyDetails.setJhjg("1");
        } else {
            jhAnomalyDetails.setJhjg("0");
        }

        iJhAnomalyDetailsService.insert(jhAnomalyDetails);

        return result;
    }

    /**
     * 电表站址一致性
     */
    private static Map checkC(Date date, Account account, Map result,
                              String rJtlteCode, String rJtltetaCode, String stationName) {
        /** 满足以下条件为异常情况：报账单台账中电表关联的站址与电表基础数据中关联的站址不一致 */
        JhAnomalyDetails jhAnomalyDetails = new JhAnomalyDetails();
        jhAnomalyDetails.setYclx("C");
        jhAnomalyDetails.setYclxmc("电表站址一致性");
        /**设置报账单id*/
        jhAnomalyDetails.setBzdid(String.valueOf(account.getBillId()));
        jhAnomalyDetails.setTzid(String.valueOf(account.getPcid()));
        jhAnomalyDetails.setJhsj(date);

        // 获取 电表基础数据中关联的站址
        if (StrUtil.isBlank(account.getStationName())) {
            result.put("电表站址一致性", "是");
        } else {
            if (account.getStationName().equals(stationName)) {
                result.put("电表站址一致性", "是");
            } else {
                result.put("电表站址一致性", "否");
            }
        }



        /**设置台账期号*/
        jhAnomalyDetails.setTzqh(account.getAccountno());
        /**设置电表户名*/
        jhAnomalyDetails.setDbhm(account.getAmmetercode());

        /**台账关联集团站址编码*/
        jhAnomalyDetails.setTzgljjtzzbm(rJtlteCode);
        /**电表基础信息关联的集团站址编码*/
        jhAnomalyDetails.setJtzzbm(rJtlteCode);
        /**台账关联铁塔站址编码*/
        jhAnomalyDetails.setTzglttzzbm(rJtltetaCode);
        /**电表基础信息关联铁塔站址编码*/
        jhAnomalyDetails.setTtzzbm(rJtltetaCode);
        if("否".equals(String.valueOf(result.get("电表站址一致性")))){
            jhAnomalyDetails.setJhjg("1");
        } else {
            jhAnomalyDetails.setJhjg("0");
        }

        iJhAnomalyDetailsService.insert(jhAnomalyDetails);

        return result;
    }

    /**
     * 支付对象一致性
     */
    private static Map checkD(Date date, Account account, Ammeterorprotocol ammeterorprotocol, Map result, List<MssAccountbill> mssAccountbills) {
        JhAnomalyDetails jhAnomalyDetails = new JhAnomalyDetails();
        jhAnomalyDetails.setYclx("D");
        jhAnomalyDetails.setYclxmc("支付对象一致性");
        /**设置报账单id*/
        jhAnomalyDetails.setBzdid(String.valueOf(account.getBillId()));
        jhAnomalyDetails.setTzid(String.valueOf(account.getPcid()));
        jhAnomalyDetails.setJhsj(date);
        /**设置电表户名*/
        jhAnomalyDetails.setDbhm(account.getAmmetercode());

        List<MssAccountbill> collect = mssAccountbills.stream()
                .filter(mssAccountbill -> mssAccountbill.getId().equals(account.getBillId())).collect(
                        Collectors.toList());

        if (collect.get(0).getSupplierCode() == null) {
            return result;
        } else {
            String userunit = ammeterorprotocol.getUserunit();
            if (!collect.get(0).getSupplierName().equals(userunit)) {
                result.put("支付对象一致性", "否");
            } else {
                result.put("支付对象一致性", "是");
            }
        }
        if (result.get("支付对象一致性").equals("否")) {
            /**设置台账期号*/
            jhAnomalyDetails.setTzqh(account.getAccountno());
            /**设置报账单期号*/
            jhAnomalyDetails.setBzdqh(collect.get(0).getHappenDate());
            /**设置电表户名*/
            jhAnomalyDetails.setDbhm(account.getAmmetercode());
            /**台账关联支付对象*/
            jhAnomalyDetails.setTzglzfdx(collect.get(0).getSupplierName());
            /**台账关联收款账号*/
            jhAnomalyDetails.setTzglskzh(collect.get(0).getSupplierBank());
            /**电表基础表的支付对象*/
            jhAnomalyDetails.setDbjcbzfdx(ammeterorprotocol.getUserunit());
            /**电表基础表的账号*/
            jhAnomalyDetails.setDbjcbskzh(ammeterorprotocol.getReceiptaccounts());
            iJhAnomalyDetailsService.insert(jhAnomalyDetails);
        }
        return result;

    }

    /**
     * 台账周期连续性
     */
    private static Map checkE(Date date, Account account, List<AccountBaseResult> accountBaseResults, Map result
            ,  String rJtlteCode, String rJtltetaCode) {
        String preStartDate = null;
        String preEndDate = null;
        String sqqh = null;
        String sqqd = null;
        String sqzd = null;
        JhAnomalyDetails jhAnomalyDetails = new JhAnomalyDetails();
        jhAnomalyDetails.setYclx("E");
        jhAnomalyDetails.setYclxmc("台账周期连续性");
        /**设置报账单id*/
        jhAnomalyDetails.setBzdid(String.valueOf(account.getBillId()));
        jhAnomalyDetails.setTzid(String.valueOf(account.getPcid()));
        jhAnomalyDetails.setJhsj(date);
        // 先判断是否有换表操作
        Ammeterorprotocol ammeterorprotocol = ammeterorprotocolService.get(account.getAmmeterid());

        if (ObjectUtil.isNotEmpty(ammeterorprotocol.getIschangeammeter()) && ObjectUtil.isNotEmpty(account.getIsnew()) &&
                ammeterorprotocol.getIschangeammeter() == 1&&(account.getIsnew().compareTo(BigDecimal.ONE) == 0)
        ) {

            // 若是进行了换表操作 , 获取到旧电表的id
            Long oldAmmeterId = ammeterorprotocol.getOldAmmeterId();
            Ammeterorprotocol oldAmmeterorprotocol = ammeterorprotocolService.get(oldAmmeterId);
            if (ObjectUtil.isNotNull(oldAmmeterorprotocol)) {
                // 获取最近一次有效台账
                Account account1 = new Account();
                account1.setAmmeterid(oldAmmeterorprotocol.getId());
                Map<String, Object> oldAmmeterorprotocolMap = accountMapper.selectLastacc(account1);
                if (oldAmmeterorprotocolMap != null &&
                        ObjectUtil.isNotNull(oldAmmeterorprotocolMap.get("enddate"))
                ) {
                    /** 换表前上期开始时间*/
                    preStartDate = oldAmmeterorprotocolMap.get("startdate").toString();
                    /** 换表前上期截止时间*/
                    preEndDate = oldAmmeterorprotocolMap.get("enddate").toString();
                    /** 换表前上期期号*/
                    sqqh = oldAmmeterorprotocolMap.get("accountno").toString();
                    /** 换表前上期起度*/
                    sqqd = oldAmmeterorprotocolMap.get("prevtotalreadings").toString();
                    /** 换表前上期止度*/
                    sqzd = oldAmmeterorprotocolMap.get("curtotalreadings").toString();
                    /** 换表后本期起始时间*/
                    String startdate = account.getStartdate();
                    checkTime(preEndDate, startdate, result);
                } else {
                    //无上期的报账单
                    result.put("台账周期连续性", "是");
                }
            } else {
                //判断是否有上期的报账单
                if (accountBaseResults.size() == 0) {
                    //无上期的报账单
                    result.put("台账周期连续性", "是");
                } else {
                    /** 上期开始时间*/
                    preStartDate = accountBaseResults.get(0).getStartdate();
                    /** 上期截止时间*/
                    preEndDate = accountBaseResults.get(0).getEnddate();
                    /** 换表前上期期号*/
                    sqqh = accountBaseResults.get(0).getAccountno();
                    /** 换表前上期起度*/
                    sqqd = accountBaseResults.get(0).getPrevtotalreadings().toString();
                    /** 换表前上期止度*/
                    sqzd = accountBaseResults.get(0).getCurtotalreadings().toString();
                    /** 本期起始时间*/
                    String startdate = account.getStartdate();
                    if(accountBaseResults.get(0).getCurtotalreadings().compareTo(new BigDecimal("0")) == 0){
                        result.put("台账周期连续性", "是");
                    } else {
                        checkTime(preEndDate, startdate, result);
                    }
                }
            }

        } else {
            //判断是否有上期的报账单
            if (accountBaseResults.size() == 0) {
                //无上期的报账单
                result.put("台账周期连续性", "是");
            } else {
                /** 上期开始时间*/
                preStartDate = accountBaseResults.get(0).getStartdate();
                /** 上期截止时间*/
                preEndDate = accountBaseResults.get(0).getEnddate();
                /** 换表前上期期号*/
                sqqh = accountBaseResults.get(0).getAccountno();
                /** 换表前上期起度*/
                sqqd = accountBaseResults.get(0).getPrevtotalreadings().toString();
                /** 换表前上期止度*/
                sqzd = accountBaseResults.get(0).getCurtotalreadings().toString();
                /** 本期起始时间*/
                String startdate = account.getStartdate();
                if(accountBaseResults.get(0).getCurtotalreadings().compareTo(new BigDecimal("0")) == 0){
                    result.put("台账周期连续性", "是");
                } else {
                    checkTime(preEndDate, startdate, result);
                }
            }
        }


        /**设置台账期号 改为上期期号*/
        jhAnomalyDetails.setTzqh(sqqh);
        /**设置电表户名*/
        jhAnomalyDetails.setDbhm(account.getAmmetercode());

        /**起始时间*/
        jhAnomalyDetails.setQssj(preStartDate);
        /**截至时间*/
        jhAnomalyDetails.setJzsj(preEndDate);

        /**本期启度 改为上期起度*/
        jhAnomalyDetails.setBqqd(sqqd);
        /**本期止度 改为本期起度*/
        jhAnomalyDetails.setBqzd(sqzd);

        /**集团站址编码*/
        jhAnomalyDetails.setJtzzbm(rJtlteCode);
        /**铁塔站址编码*/
        jhAnomalyDetails.setTtzzbm(rJtltetaCode);

        if("否".equals(String.valueOf(result.get("台账周期连续性")))){
            jhAnomalyDetails.setJhjg("1");
        } else {
            jhAnomalyDetails.setJhjg("0");
        }

        iJhAnomalyDetailsService.insert(jhAnomalyDetails);

        return result;
    }

    public static void checkTime(String preenddate, String startdate, Map result) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate endTime = LocalDate.parse(preenddate, formatter);
        LocalDate startTime = LocalDate.parse(startdate, formatter);
        long between = ChronoUnit.DAYS.between(endTime, startTime);
        //上期截止日期第二天是本期起始时间才算连续
        if (startTime.isAfter(endTime) && between == 1) {
            result.put("台账周期连续性", "是");
        } else {
            result.put("台账周期连续性", "否");
        }
    }

    /**
     * 电表度数连续性
     */
    private static Map checkF(String ymmc, Date date, Account account, List<AccountBaseResult> accountBaseResults
            , Map result,  String rJtlteCode, String rJtltetaCode) {
        JhAnomalyDetails jhAnomalyDetails = new JhAnomalyDetails();
        jhAnomalyDetails.setYclx("F");
        jhAnomalyDetails.setYclxmc("电表度数连续性");
        /**设置报账单id*/
        jhAnomalyDetails.setBzdid(String.valueOf(account.getBillId()));
        jhAnomalyDetails.setTzid(String.valueOf(account.getPcid()));
        jhAnomalyDetails.setJhsj(date);
        if ("自有预估电费台账".equals(ymmc) || "铁塔挂账电费台账".equals(ymmc) || "铁塔预估电费台账".equals(ymmc)) {
            result.put("电表度数连续性", "是");
        } else {
            //判断是否有上期的报账单
            if (accountBaseResults.size() == 0) {
                //无上期的报账单
                result.put("电表度数连续性", "是");
            } else {
                /** 本期报账的启度*/
                BigDecimal prevtotalreadings = account.getPrevtotalreadings();
                /** 上期报账的止度*/
                BigDecimal curtotalreadings = accountBaseResults.get(0).getCurtotalreadings();
                //判断本期报账的启度与上期报账的止度是否一致
                if(curtotalreadings.compareTo(new BigDecimal("0")) == 0){
                    result.put("电表度数连续性", "是");
                } else if (prevtotalreadings.compareTo(curtotalreadings) == 0) {
                    result.put("电表度数连续性", "是");
                } else {
                    result.put("电表度数连续性", "否");
                }
            }
        }

        /**设置台账期号*/
        jhAnomalyDetails.setTzqh(account.getAccountno());
        /**设置电表户名*/
        jhAnomalyDetails.setDbhm(account.getAmmetercode());

        /**集团站址编码*/
        jhAnomalyDetails.setJtzzbm(rJtlteCode);
        /**铁塔站址编码*/
        jhAnomalyDetails.setTtzzbm(rJtltetaCode);
        /**起始时间*/
        jhAnomalyDetails.setQssj(account.getStartdate());
        /**截至时间*/
        jhAnomalyDetails.setJzsj(account.getEnddate());
        if("否".equals(String.valueOf(result.get("电表度数连续性")))){
            jhAnomalyDetails.setJhjg("1");
        } else {
            jhAnomalyDetails.setJhjg("0");
        }
        /**本期启度*/
        jhAnomalyDetails.setBqqd(account.getPrevtotalreadings() == null ? null : account.getPrevtotalreadings().toString());
        /**本期止度*/
        jhAnomalyDetails.setBqzd(account.getCurtotalreadings() == null ? null : account.getCurtotalreadings().toString());
        iJhAnomalyDetailsService.insert(jhAnomalyDetails);

        return result;
    }

    /**
     * 台账电量合理性
     */
    private static Map checkG(Date date, Account account, Map result) {
        //获得id
        Long ammeterId = account.getAmmeterid();
        //  如果id是空的话，就直接返回异常
        if (Objects.isNull(ammeterId)) {
            result.put("大数据电量异常", "否");
            return result;
        }
        //通过id去获取对应的内容
        List<String> list1 = powerStationInfoMapper.selectAllCheckById(ammeterId);

//        List<String> list1 = powerStationInfoMapper.selectAllCheckByName(account.getAmmetername());
        if (list1.size() == 0) {
            /**未查到稽核数据*/
            result.put("大数据电量异常", "否");
        } else {
            String s = list1.get(0);
            if (s.equals("0")) {
                /**稽核异常未处理*/
                result.put("大数据电量异常", "是");

            } else {
                /**稽核异常已处理*/
                result.put("大数据电量异常", "否");

            }
        }
        //暂时默认通过
        result.put("大数据电量异常", "是");
        return result;
    }

    /**
     * 台账日均耗电量波动合理性
     */
    private static Map checkH( Date date, Account account,boolean b,BigDecimal totalUsePower, Map result,
                               String rJtlteCode, String rJtltetaCode,String startdate,
                               String enddate,Ammeterorprotocol ammeterorprotocol) {
        JhAnomalyDetails jhAnomalyDetails = new JhAnomalyDetails();
        jhAnomalyDetails.setYclx("H");
        jhAnomalyDetails.setYclxmc("电量波动异常");

        /**设置报账单id*/
        jhAnomalyDetails.setBzdid(String.valueOf(account.getBillId()));
        jhAnomalyDetails.setTzid(String.valueOf(account.getPcid()));
        jhAnomalyDetails.setJhsj(date);
        BigDecimal multiply = new BigDecimal("0");
        // 根据集团算出来的总用电量
        final BigDecimal[] totalQuaPower = {new BigDecimal(0)};
        if(b){
            result.put("电量波动异常","是");
        } else {
            // 铁塔和第三方基站
            List<Long> taCodes = Arrays.asList(1411L, 1412L, 1421L, 1422L);
            // 自有基站
            List<Long> zyCodes = Arrays.asList(1431L, 1432L);
            if(StrUtil.isNotBlank(ammeterorprotocol.getStationcodeintid()) &&
                    ObjectUtil.isNotNull(ammeterorprotocol.getElectrotype())){

                // 为铁塔和第三方基站 或者 自有基站里面的转供电 就进行电量稽核 否则直接置为正常
                if(
                        taCodes.contains(ammeterorprotocol.getElectrotype())
                                || (zyCodes.contains(ammeterorprotocol.getElectrotype()) &&
                                ammeterorprotocol.getDirectsupplyflag() == 2) ){
                    String beginYearStr = startdate.substring(0, 4);
                    String beginMonthStr = startdate.substring(4, 6);
                    String beginDayStr = startdate.substring(6, 8);
                    // 拿到年月
                    String beginYearMonthStr = beginYearStr+"-" + beginMonthStr;
                    DateTime beginMonthDate = DateUtil.parse(beginYearMonthStr, "yyyy-MM");

                    String endYearStr = enddate.substring(0, 4);
                    String endMonthStr = enddate.substring(4, 6);
                    String endDayStr = enddate.substring(6, 8);
                    String endYearMonthStr = endYearStr+"-" + endMonthStr;
                    DateTime endMonthDate = DateUtil.parse(endYearMonthStr, "yyyy-MM");

                    // 在 power_station_qua_jt5gr 表中查询符合年月的数据
                    List<AccountQua> accountQuaList = powerStationInfoMapper.getQuaJt5gr(beginMonthDate, endMonthDate,ammeterorprotocol.getStationcodeintid());

                    if(CollectionUtil.isEmpty(accountQuaList)){
                        result.put("电量波动异常","是");
                    } else {
                        Integer beginYearMonthInt = Integer.valueOf(startdate.substring(0, 6));
                        Integer endYearMonthInt = Integer.valueOf(enddate.substring(0, 6));

                        // 若是不同的年月份
                        if(beginYearMonthInt.compareTo(endYearMonthInt) != 0) {
                            // 不同月份，需要拿到每月的天数*集团对应月份的电量平均值
                            // 开始和结束设置 开始至月底的天数
                            int beginDays =(DateUtil.dayOfMonth(DateUtil.endOfMonth(beginMonthDate))- Integer.parseInt(beginDayStr))+1;
                            mutiBetweenPower(beginDays,beginYearMonthStr,accountQuaList,totalQuaPower);

                            // 结束开始至当天的天数
                            int endDays= Integer.parseInt(endDayStr);
                            mutiBetweenPower(endDays,endYearMonthStr,accountQuaList,totalQuaPower);

                            // 计算从开始年月到结束年月之间的年月
                            List<String> monthsBetween = getMonthsBetween(beginYearMonthStr, endYearMonthStr);
                            for (String yearMonth : monthsBetween) {
                                // 拿到年份，若是2024年以前，则使用202401的
                                int daysOfMonth = DateUtil.dayOfMonth(DateUtil.endOfMonth(DateUtil.parse(yearMonth,"yyyy-MM")));
                                mutiBetweenPower(daysOfMonth,yearMonth,accountQuaList,totalQuaPower);
                            }
                        }else {
                            int daysOfMonth = DateUtil.dayOfMonth(DateUtil.endOfMonth(DateUtil.parse(beginYearMonthStr,"yyyy-MM")));
                            // 同一月份直接计算
                            mutiBetweenPower(daysOfMonth,beginYearMonthStr,accountQuaList,totalQuaPower);
                        }

                        if(totalQuaPower[0].compareTo(new BigDecimal(0)) != 0){
                            // 根据集团站址月电量按台账起止月计算合计电量大于30%强控
                            // 若是台账的电量大于集团站址月电量的30%，则为异常  （台账-集团）/集团
                            multiply = ((totalUsePower.subtract(totalQuaPower[0])).divide(totalQuaPower[0], RoundingMode.HALF_UP)
                                    .setScale(2,RoundingMode.HALF_UP)).multiply(new BigDecimal(100));
                            // 若是负数，表示远小于集团的用电量
                            if(multiply.compareTo(new BigDecimal("0")) <= 0){
                                result.put("电量波动异常","是");
                            } else {
                                if(multiply.compareTo(new BigDecimal(30)) > 0){
                                    result.put("电量波动异常","否");
                                }else {
                                    result.put("电量波动异常","是");
                                }
                            }
                        } else {
                            result.put("电量波动异常","否");
                        }
                    }
                } else {
                    result.put("电量波动异常","是");
                }
            } else {
                // stationcodeintid为空，若是 electrotype 1411，1412，1421，1422，1431，1432 外让过
                if(taCodes.contains(ammeterorprotocol.getElectrotype())
                        || zyCodes.contains(ammeterorprotocol.getElectrotype())){
                    result.put("电量波动异常","否");
                } else {
                    result.put("电量波动异常","是");
                }
            }
        }


        /**设置台账期号*/
        jhAnomalyDetails.setTzqh(account.getAccountno());
        /**设置电表户名*/
        jhAnomalyDetails.setDbhm(account.getAmmetercode());

        if("否".equals(String.valueOf(result.get("电量波动异常")))){
            jhAnomalyDetails.setJhjg("1");
        } else {
            jhAnomalyDetails.setJhjg("0");
        }
        /**集团站址编码*/
        jhAnomalyDetails.setJtzzbm(rJtlteCode);
        /**铁塔站址编码*/
        jhAnomalyDetails.setTtzzbm(rJtltetaCode);

        /**台账日均电量*/
        jhAnomalyDetails.setTzrjdl(totalUsePower.setScale(2, RoundingMode.HALF_UP).toString());

        /**无线大数据平台推送的日均电量*/
        jhAnomalyDetails.setBzrjdl(totalQuaPower[0].setScale(2, RoundingMode.HALF_UP).toString());

        /**波动幅度*/
        jhAnomalyDetails.setBdfd(multiply+"%");

        iJhAnomalyDetailsService.insert(jhAnomalyDetails);

        return result;
    }

    private static void mutiBetweenPower(
            int daysOfMonth,
            String beginYearMonthStr,
            List<AccountQua> accountQuaList,
            BigDecimal[] totalQuaPower){
        String year = beginYearMonthStr.split("-")[0];

        if("2024".compareTo(year) > 0){
            // 判断是否小于2024年 小于24年就拿最小的
            AccountQua accountQua = accountQuaList.get(accountQuaList.size() -1);
            totalQuaPower[0] = totalQuaPower[0].add(new BigDecimal(accountQua.getAve()).multiply(new BigDecimal(daysOfMonth)));
        } else {
            beginYearMonthStr = beginYearMonthStr.replace("-","");
            String finalBeginYearMonthStr = beginYearMonthStr;
            List<AccountQua> collect = accountQuaList.stream().filter(q -> finalBeginYearMonthStr.equals(q.getFinanperiod())).collect(Collectors.toList());
            // 若是不为空，则直接取当期的电量
            if (!collect.isEmpty()) {
                totalQuaPower[0] = totalQuaPower[0].add(new BigDecimal(collect.get(0).getAve()).multiply(new BigDecimal(daysOfMonth)));
            } else {
                // 直接取最新一期的，因为查询是倒叙的，直接取第一个就是最新的
                AccountQua accountQua = accountQuaList.get(0);
                totalQuaPower[0] = totalQuaPower[0].add(new BigDecimal(accountQua.getAve()).multiply(new BigDecimal(daysOfMonth)));
            }
        }
    }


    public static List<String> getMonthsBetween(String startMonth, String endMonth) {
        DateTime begin = DateUtil.parse(startMonth, "yyyy-MM");
        DateTime end = DateUtil.parse(endMonth, "yyyy-MM");

        List<String> months = new ArrayList<>();

        while (!begin.isAfter(end)) {
            months.add(DateUtil.format(begin,"yyyy-MM"));
            begin = begin.offset(DateField.MONTH,1);
        }
        // 移除开始月份
        months.remove(startMonth);
        // 移除结束月份
        months.remove(endMonth);
        return months;
    }

    private static boolean checkWhiteList(String stationcode, String whilteList){
        List<String> strings = new ArrayList<>(Arrays.asList(whilteList.split(",")));
        return stationcode == null ? false : strings.stream().anyMatch(stationcode::contains);
    }

    /**
     * 台账日均耗电量合理性
     */
    private static Map checkI(Date date, Account account, boolean b,BigDecimal divide,
                              Map result,  String rJtlteCode, String wxdtsrjdl, String rJtltetaCode) {
        JhAnomalyDetails jhAnomalyDetails = new JhAnomalyDetails();
        jhAnomalyDetails.setYclx("I");
        jhAnomalyDetails.setYclxmc("日均耗电量过低");
        /**设置报账单id*/
        jhAnomalyDetails.setBzdid(String.valueOf(account.getBillId()));
        jhAnomalyDetails.setTzid(String.valueOf(account.getPcid()));
        jhAnomalyDetails.setJhsj(date);

        /**是否在白名单中*/
        if (b) {
            result.put("日均耗电量过低", "是");
        } else {

            // 电量值低于 0.5度
            if (divide.compareTo(new BigDecimal(0.5)) < 0) {
                result.put("日均耗电量过低", "否");
            } else {
                result.put("日均耗电量过低", "是");

            }
        }


        /**设置台账期号*/
        jhAnomalyDetails.setTzqh(account.getAccountno());
        /**设置电表户名*/
        jhAnomalyDetails.setDbhm(account.getAmmetercode());

        /**集团站址编码*/
        jhAnomalyDetails.setJtzzbm(rJtlteCode);
        /**铁塔站址编码*/
        jhAnomalyDetails.setTtzzbm(rJtltetaCode);

        /**台账日均电量*/
        jhAnomalyDetails.setTzrjdl(divide.toString());

        /**无线大数据平台推送的日均电量*/
        jhAnomalyDetails.setBzrjdl(wxdtsrjdl);

        if("否".equals(String.valueOf(result.get("日均耗电量过低")))){
            jhAnomalyDetails.setJhjg("1");
        } else {
            jhAnomalyDetails.setJhjg("0");
        }

        if (Double.valueOf(wxdtsrjdl) != 0D) {
            /**无线大数据平台推送的日均电量*/
            jhAnomalyDetails.setTzrjdl(wxdtsrjdl);
            /**波动幅度*/
            jhAnomalyDetails.setBdfd(divide.subtract(new BigDecimal(wxdtsrjdl)).abs().divide(new BigDecimal(wxdtsrjdl), BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).toString() + "%");
        }
        iJhAnomalyDetailsService.insert(jhAnomalyDetails);

        return result;
    }

    /**
     * 共享站分摊比例准确性 改为 共享站分摊比例准确性
     */
    private static Map checkJ(Date date, Account account, Map result, String rJtlteCode, String rJtltetaCode,
                              Ammeterorprotocol ammeterorprotocol) {
        JhAnomalyDetails jhAnomalyDetails = new JhAnomalyDetails();
        jhAnomalyDetails.setYclx("J");
        jhAnomalyDetails.setYclxmc("分摊比例准确性");
        /**设置报账单id*/
        jhAnomalyDetails.setBzdid(String.valueOf(account.getBillId()));
        jhAnomalyDetails.setTzid(String.valueOf(account.getPcid()));
        jhAnomalyDetails.setJhsj(date);

        // 台账中的电信分割比例 percent
        BigDecimal tzPercent = account.getPercent() == null ? new BigDecimal(0) : account.getPercent();
        // 协议中的电信分割比例
        BigDecimal xyPercent = ammeterorprotocol.getPercent() == null ? new BigDecimal(0) : ammeterorprotocol.getPercent();;

        // 台账中的电信分割比例要与协议管理中的分割比例一致
        boolean c = tzPercent.compareTo(xyPercent) == 0;
        if (!c) {
            result.put("分摊比例准确性", "否");
        }  else {
            result.put("分摊比例准确性", "是");
        }

        /**设置台账期号*/
        jhAnomalyDetails.setTzqh(account.getAccountno());
        /**设置电表户名*/
        jhAnomalyDetails.setDbhm(account.getAmmetercode());

        /**集团站址编码*/
        jhAnomalyDetails.setJtzzbm(rJtlteCode);
        /**铁塔站址编码*/
        jhAnomalyDetails.setTtzzbm(rJtltetaCode);

        /**比例1 改为铁塔推送电信比例*/
        jhAnomalyDetails.setBl1(account.getPercent() == null?"":account.getPercent().toString());

        if("否".equals(String.valueOf(result.get("分摊比例准确性")))){
            jhAnomalyDetails.setJhjg("1");
        } else {
            jhAnomalyDetails.setJhjg("0");
        }
        /**比例2 改为协议管理电信比例*/
        jhAnomalyDetails.setBl2(ammeterorprotocol.getPercent() == null?"":ammeterorprotocol.getPercent().toString());

        iJhAnomalyDetailsService.insert(jhAnomalyDetails);

        return result;
    }

    private static Map checkN(Date date, Account account, Map result, String rJtlteCode, String rJtltetaCode, List<AccountBaseResult> accountBaseResults) {

        JhAnomalyDetails jhAnomalyDetails = new JhAnomalyDetails();
        jhAnomalyDetails.setYclx("N");
        jhAnomalyDetails.setYclxmc("分摊比例一致性");
        /**设置报账单id*/
        jhAnomalyDetails.setBzdid(String.valueOf(account.getBillId()));
        jhAnomalyDetails.setTzid(String.valueOf(account.getPcid()));
        jhAnomalyDetails.setJhsj(date);

        if (accountBaseResults.size() == 0) {
            result.put("分摊比例一致性", "是");
        } else {
            BigDecimal prePercent = accountBaseResults.get(0).getPercent();
            BigDecimal nowPercent = account.getPercent();

            if (prePercent.compareTo(nowPercent) != 0) {
                result.put("分摊比例一致性", "否");
            } else {
                result.put("分摊比例一致性", "是");
            }
        }

        if (result.get("分摊比例一致性").equals("否")) {
            /**设置台账期号*/
            jhAnomalyDetails.setTzqh(account.getAccountno());
            /**设置电表户名*/
            jhAnomalyDetails.setDbhm(account.getAmmetercode());
            /**集团站址编码*/
            jhAnomalyDetails.setJtzzbm(rJtlteCode);
            /**铁塔站址编码*/
            jhAnomalyDetails.setTtzzbm(rJtltetaCode);
            /**电信比例*/
            jhAnomalyDetails.setDxbl(account.getPercent().toString());
            /**最近一次报账比例*/
            jhAnomalyDetails.setZjycbzbl(accountBaseResults.get(0).getPercent().toString());

            iJhAnomalyDetailsService.insert(jhAnomalyDetails);
        }

        return result;
    }

    @SuppressWarnings("checkstyle:SeparatorWrap")
    private static Map checkL(Date date, Account account, int days, Map result,  String rJtlteCode,String rJtltetaCode,
                              List<AccountBaseResult> accountBaseResults) throws ParseException {
        JhAnomalyDetails jhAnomalyDetails = new JhAnomalyDetails();
        jhAnomalyDetails.setYclx("L");
        jhAnomalyDetails.setYclxmc("周期合理性");
        /**设置报账单id*/
        jhAnomalyDetails.setBzdid(String.valueOf(account.getBillId()));
        jhAnomalyDetails.setTzid(String.valueOf(account.getPcid()));
        jhAnomalyDetails.setJhsj(date);
        if (days > 200) {
            result.put("周期合理性", "否");
        } else {
            result.put("周期合理性", "是");
        }




        /**设置台账期号*/
        jhAnomalyDetails.setTzqh(account.getAccountno());
        /**设置电表户名*/
        jhAnomalyDetails.setDbhm(account.getAmmetercode());

        /**集团站址编码*/
        jhAnomalyDetails.setJtzzbm(rJtlteCode);
        /**铁塔站址编码*/
        jhAnomalyDetails.setTtzzbm(rJtltetaCode);

        if("否".equals(String.valueOf(result.get("周期合理性")))){
            jhAnomalyDetails.setJhjg("1");
        } else {
            jhAnomalyDetails.setJhjg("0");
        }

//            AccountCondition accountCondition = new AccountCondition();
//            accountCondition.setAmmetercode(account.getAmmetercode());
//            accountCondition.setVersion("ln");
//            accountCondition.setAccountType(1);
//            List<AccountBaseResult> accountBaseResults = accountService
//                .selectSelfList(accountCondition);
        /**上一次的台账数据*/
        AccountBaseResult accountBaseResult = null;
        if (accountBaseResults.size() != 0) {
            accountBaseResult = accountBaseResults.stream()
                    .max(Comparator.comparing(AccountBaseResult::getAccountno)).get();
        }

        /**最近一次台账录入日期*/
        if (accountBaseResult != null) {
            jhAnomalyDetails.setZjycbzsj(accountBaseResult.getAccountno());
            jhAnomalyDetails.setZjbzqh(accountBaseResult.getInputname());
            /**间隔时间*/
            SimpleDateFormat simpleFormat = new SimpleDateFormat("yyyyMMdd");
            /*天数差*/
            Date fromDate1;
            if (accountBaseResult.getAccountno().length() == 6) {
                fromDate1 = format.parse(accountBaseResult.getAccountno());
            } else {
                fromDate1 = simpleFormat.parse(accountBaseResult.getAccountno());
            }

            Date toDate1 = date;
            long from1 = fromDate1.getTime();
            long to1 = toDate1.getTime();
            /**间隔期间*/
            int intervalTime = (int) ((to1 - from1) / (1000 * 60 * 60 * 24));

            jhAnomalyDetails.setJg(String.valueOf(intervalTime));

        }
        iJhAnomalyDetailsService.insert(jhAnomalyDetails);

        return result;
    }

    /**
     * 沉默电表
     */
    private static Map checkM(Date date, Map result, List<AccountBaseResult> accountBaseResults) throws ParseException {

        /**上一次的台账数据*/
        AccountBaseResult accountBaseResult = null;

        if (accountBaseResults.size() != 0) {
            accountBaseResult = accountBaseResults.stream()
                    .max(Comparator.comparing(AccountBaseResult::getAccountno)).get();

            /**间隔时间*/
            SimpleDateFormat simpleFormat = new SimpleDateFormat("yyyyMMdd");
            /*天数差*/
            Date fromDate1;
            if (accountBaseResult.getAccountno().length() == 6) {
                fromDate1 = format.parse(accountBaseResult.getAccountno());
            } else {
                fromDate1 = simpleFormat.parse(accountBaseResult.getAccountno());
            }

            Date toDate1 = date;
            long from1 = fromDate1.getTime();
            long to1 = toDate1.getTime();
            /**间隔期间*/
            int intervalTime = (int) ((to1 - from1) / (1000 * 60 * 60 * 24));

            if (intervalTime > 300) {
                result.put("沉默电表", "是");
            } else {
                result.put("沉默电表", "否");
            }

        } else {
            result.put("沉默电表", "否");
        }

        return result;
    }

    /**
     * 预付台账工具方法
     */
    private static List yftzff(Integer accountType, Integer type, Account account, String accountno, List accountBaseResults) {
        AccountEsResult accountEsResult = new AccountEsResult();
        accountEsResult.setAccountno(accountno);
        accountEsResult.setCompany(-1L);
        accountEsResult.setCompany(-1L);
        accountEsResult.setAccountType(accountType);
        accountEsResult.setAccountestype(type);
        if (account.getAmmetercode() != null) {
            accountEsResult.setAmmeterName(account.getAmmetercode());
        } else {
            accountEsResult.setAmmeterName(account.getAmmetername());
            account.setAmmetercode(account.getAmmetername());

        }

        List<AccountEsResult> accountEsResults = iPowerAccountEsService
                .selectAccountEsList(accountEsResult);
        List<AccountBaseResult> finalAccountBaseResults = accountBaseResults;
        accountEsResults.stream().forEach(accountEsResultq -> {
            AccountBaseResult accountBaseResult1 = new AccountBaseResult();
            try {
                accountBaseResult1 = StationAuditAspect
                        .copyProperties(accountEsResultq, accountBaseResult1);
                finalAccountBaseResults.add(accountBaseResult1);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        return finalAccountBaseResults;
    }

    /*********
     * 稽核单条台账
     * @param account 台账数据
     * @param ymmc 台账类型标识
     * <AUTHOR>
     * @date 2024-07-27
     * *******/
    private static Map powerAccountAudit(Account account, String ymmc) {
        loadBean();
        //临时取消 只能稽核状态为1的台账
//        if (1 != account.getStatus().intValue()) {
//            return null;
//        }
        try {
            if (null == ymmc) {
                ymmc = getYmmcAccount(account);
            }
            if (StrUtil.isNotBlank(ymmc)) {
                return checkStationAudit(false, account, null, ymmc);
            } else {
                return successStationAuditnew(false, account, null, ymmc);
            }
        } catch (ParseException e) {
            System.out.println("稽核台账" + account.getPcid() + "出错：" + e.getMessage());
        }
        return null;
    }

    /*********
     * 线程稽核单条台账
     * @param account 台账数据
     * <AUTHOR>
     * @date 2024-07-27
     * *******/
    private static void powerAccountAuditThread(Account account) {
        new Thread(() -> {
            powerAccountAudit(account, null);
        }).start();
    }


    /*********
     * 线程稽核台账列表
     * @param accountBaseResultList 台账数据列表
     * <AUTHOR>
     * @date 2024-07-27
     * *******/
    private static void powerAccountBaseAuditListThread(List<AccountBaseResult> accountBaseResultList) {
        if (CollectionUtil.isNotEmpty(accountBaseResultList) && accountBaseResultList.size() > 0) {
            for (AccountBaseResult accountBaseResult : accountBaseResultList) {
                Account account = new Account();
                BeanUtil.copyProperties(accountBaseResult, account);
                powerAccountAuditThread(account);
            }
        }
    }

    /*********
     * 稽核单条es台账
     * @param powerAccountEs es台账数据
     * <AUTHOR>
     * @date 2024-07-27
     * *******/
    private static void powerAccountAuditEs(PowerAccountEs powerAccountEs) {
        loadBean();
        if (1 != powerAccountEs.getStatus().intValue()) {
            return;
        }
        String ymmc = getYmmcAccountEs(powerAccountEs);
        Account account = new Account();
        BeanUtil.copyProperties(powerAccountEs, account);
        account.setAmmetername(powerAccountEs.getAmmeterName());
        powerAccountAudit(account, ymmc);
    }

    /*********
     * 线程稽核es台账
     * @param powerAccountEs es台账数据
     * <AUTHOR>
     * @date 2024-07-27
     * *******/
    private static void powerAccountAuditEsThread(PowerAccountEs powerAccountEs) {
//        new Thread(() -> {
        powerAccountAuditEs(powerAccountEs);
//        }).start();
    }

    /*********
     * 线程稽核es台账列表
     * @param powerAccountEsList es台账数据列表
     * <AUTHOR>
     * @date 2024-07-27
     * *******/
    private static void powerAccountAuditEsListThread(List<PowerAccountEs> powerAccountEsList) {
        if (CollectionUtil.isNotEmpty(powerAccountEsList) && powerAccountEsList.size() > 0) {
            for (PowerAccountEs powerAccountEs : powerAccountEsList) {
                powerAccountAuditEsThread(powerAccountEs);

            }
        }
    }

    /*********
     * 通过电表/协议id列表用线程稽核台账
     * @param ammeterIdList e电表/协议id列表
     * <AUTHOR>
     * @date 2024-07-30
     * *******/
    public static void doAuditThreadByAmmeterIds(List<Long> ammeterIdList) {
        loadBean();
        if (CollectionUtil.isNotEmpty(ammeterIdList) && ammeterIdList.size() > 0) {
            List<Long> pcidList = ammeterorprotocolMapper.getPowerAccountByAmmeter(ammeterIdList);
            if (CollectionUtil.isNotEmpty(pcidList)) {
                //稽核PowerAccount
                List<AccountBaseResult> accountBaseResultList = accountMapper.selectByIds(pcidList);
                if (CollectionUtil.isNotEmpty(accountBaseResultList)) {
                    powerAccountBaseAuditListThread(accountBaseResultList);
                }
            }
            //稽核PowerAccountEs
            List<PowerAccountEs> powerAccountEsList = ammeterorprotocolMapper.getPowerAccountEsByAmmeter(ammeterIdList);
            powerAccountAuditEsListThread(powerAccountEsList);
        }
    }

    /*********
     * 通过pcid列表用线程稽核台账
     * @param pcidList pcid列表
     * <AUTHOR>
     * @date 2024-07-30
     * *******/
    public static void doAuditAccountThreadByPcids(List<Long> pcidList) {
        loadBean();
        if (CollectionUtil.isNotEmpty(pcidList) && pcidList.size() > 0) {
            //稽核PowerAccount
            List<AccountBaseResult> accountBaseResultList = accountMapper.selectByIds(pcidList);
            if (CollectionUtil.isNotEmpty(accountBaseResultList)) {
                powerAccountBaseAuditListThread(accountBaseResultList);
            }
        }
    }

    /*********
     * 通过pcid列表用线程稽核es台账
     * @param pcidList es台账pcid列表
     * <AUTHOR>
     * @date 2024-07-30
     * *******/
    public static void doAuditAccountEsThreadByPcids(List<Long> pcidList) {
        loadBean();
        if (CollectionUtil.isNotEmpty(pcidList) && pcidList.size() > 0) {
            //稽核PowerAccountEs
            List<PowerAccountEs> powerAccountEsList = iPowerAccountEsService.getPowerAccountEsByPcids(pcidList);
            powerAccountAuditEsListThread(powerAccountEsList);
        }
    }
}
