package com.sccl.modules.mssaccount.mssaccountclearitemaccount.mapper;

import com.sccl.modules.mssaccount.mssaccountclearitemaccount.domain.MssAccountclearitemAccount;
import com.sccl.framework.mapper.BaseMapper;

import java.util.List;

/**
 * 预付冲销 挑对台账 数据层
 *
 * <AUTHOR>
 * @date 2019-11-25
 */
public interface MssAccountclearitemAccountMapper extends BaseMapper<MssAccountclearitemAccount> {


    List<MssAccountclearitemAccount> listBypcids(String[] toStrArray);

    List<MssAccountclearitemAccount> listBybillIds(String[] toStrArray);

    List<MssAccountclearitemAccount> listBybillId(Long id);
}