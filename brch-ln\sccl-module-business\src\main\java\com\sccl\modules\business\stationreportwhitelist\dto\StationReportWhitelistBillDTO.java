package com.sccl.modules.business.stationreportwhitelist.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/6 10:08
 * @describe 白名单流程单据
 */
@Getter
@Setter
public class StationReportWhitelistBillDTO {

    /**
     * 白名单流程单据id
     */
    private Long id;

    /**
     * 局(站)编码
     */
    @NotBlank(message = "局(站)编码不能为空")
    private String stationcode;

    /**
     * 流程单据状态
     */
    private Integer billStatus;

    /**
     * 流程实例ID
     */
    private Long procInstId;

    /**
     * 申请理由
     */
    @NotBlank(message = "申请理由不能为空")
    @Length(max = 60, message = "申请理由长度不能超过60个字符")
    private String applyArgument;

    /**
     * 附件
     */
    private String fj;

    /**
     * 电表列表
     */
    @NotNull(message = "电表id列表不能为空")
    private List<Long> meterIdList;

    /**
     * 白名单类型 1:一表多站 2:一站多表 3:单价
     */
    @NotNull(message = "白名单类型不能为空")
    private String type;

    /**
     * 附件业务id
     */
    private Long fileBusiId;

    @Override
    public String toString() {
    	return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }
}
