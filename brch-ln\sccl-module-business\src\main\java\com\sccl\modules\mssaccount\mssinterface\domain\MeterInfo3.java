package com.sccl.modules.mssaccount.mssinterface.domain;

import lombok.NoArgsConstructor;

import java.util.Date;

@NoArgsConstructor
public class MeterInfo3 extends MeterInfo2 {
    private Integer SyncFlag;
    private String failMag;
    private Long id;
    private String msgId;

    /**
     * 创建时间
     */
    private Date createTime;

    public MeterInfo3(Integer syncFlag, String failMag, Long id) {
        SyncFlag = syncFlag;
        this.failMag = failMag;
        this.id = id;
    }


    @Override
    public String toString() {
        return "MeterInfo3{" +
                "SyncFlag=" + SyncFlag +
                ", failMag='" + failMag + '\'' +
                ", id=" + id +
                '}';
    }

    public Integer getSyncFlag() {
        return SyncFlag;
    }

    public void setSyncFlag(Integer syncFlag) {
        SyncFlag = syncFlag;
    }

    public String getFailMag() {
        return failMag;
    }

    public void setFailMag(String failMag) {
        this.failMag = failMag;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }
}
