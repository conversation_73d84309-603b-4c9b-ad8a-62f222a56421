package com.sccl.modules.business.quotaconfig.mapper;

import com.sccl.modules.business.alertcontrol.domain.AlertControl;
import com.sccl.modules.business.quotaconfig.domain.QuotaConfig;
import com.sccl.framework.mapper.BaseMapper;

import java.util.List;

/**
 * 基站定额分公司设置 数据层
 * 
 * <AUTHOR>
 * @date 2023-06-07
 */
public interface QuotaConfigMapper extends BaseMapper<QuotaConfig>
{
    public String selectIds(QuotaConfig quotaConfig);
    public String selectCountryIds(QuotaConfig quotaConfig);
    public List<QuotaConfig> selectListCustomized(QuotaConfig quotaConfig);
}