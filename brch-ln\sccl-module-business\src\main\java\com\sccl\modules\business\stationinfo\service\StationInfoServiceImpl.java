package com.sccl.modules.business.stationinfo.service;

import cn.hutool.core.util.ObjectUtil;
import com.sccl.common.io.SetUtils;
import com.sccl.common.lang.StringUtils;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.service.IdGenerator;
import com.sccl.framework.utils.enumClass.CommonConstants;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.modules.business.account.domain.AccountBaseResult;
import com.sccl.modules.business.account.domain.Ltestation;
import com.sccl.modules.business.account.domain.NhSite;
import com.sccl.modules.business.accountbillitempre.service.IAccountbillitempreService;
import com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol;
import com.sccl.modules.business.ammeterorprotocol.mapper.AmmeterorprotocolMapper;
import com.sccl.modules.business.dataaudit.domain.PowerStationAnomaly;
import com.sccl.modules.business.dataaudit.mapper.PowerStationAnomalyMapper;
import com.sccl.modules.business.stationinfo.domain.CheckStationInfoDto;
import com.sccl.modules.business.stationinfo.domain.StationInfo;
import com.sccl.modules.business.stationinfo.domain.StationRecord;
import com.sccl.modules.business.stationinfo.dto.ResStationQueryDto;
import com.sccl.modules.business.stationinfo.mapper.StationInfoMapper;
import com.sccl.modules.business.stationinfo.vo.StationBatchStopVo;
import com.sccl.modules.system.attachments.domain.Attachments;
import com.sccl.modules.system.attachments.service.IAttachmentsService;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.service.IUserService;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;


/**
 * 局站 服务层实现
 *
 * <AUTHOR>
 * @date 2019-05-29
 */
@Slf4j
@Service
public class StationInfoServiceImpl extends BaseServiceImpl<StationInfo> implements IStationInfoService {
    @Autowired(required = false)
    private IStationRecordService iStationRecordService;
    @Autowired
    StationInfoMapper stationInfoMapper;
    @Autowired
    AmmeterorprotocolMapper AmmeterorprotocolMapper;
    @Autowired
    private IUserService userService;
    @Autowired(required = false)
    private IAccountbillitempreService accountbillitempreService;
    @Autowired
    private IAttachmentsService attachmentsService;
    @Autowired
    private PowerStationAnomalyMapper powerStationAnomalyMapper;

    @Value("${sccl.deployTo}")
    private String configVersion;
    String[] authEditArr = {"CITY_ADMIN", "CITY_WIRELESS_M", "LOCALNET_ADMIN"};//能耗费市管理员.市无线管理员,本地网能耗管理员
    String nowDate = "";

    @Override
    public int addStation(StationInfo stationInfo) {
        if (null == stationInfo.getWfStatus()) {
            stationInfo.setWfStatus(0L);//草稿
        }
        if (null == stationInfo.getId()) {
            stationInfo.setId(IdGenerator.getNextId());//保存
        }
        if (ObjectUtil.isEmpty(stationInfo.getStationtype()) || stationInfo.getStationtype().compareTo(10002L) != 0) {
            //非基站局站，5GR清空
            stationInfo.setStationcodeintid(null);
            stationInfo.setStationname5gr(null);
        }
        stationInfo.setCreatetime(new Date());
        int result = this.insert(stationInfo);
        //保存局站记录
        result = saveStationInfoRecord(stationInfo);

        return result;
    }

    @Override
    public StationInfo addStationpro(StationInfo stationInfo) {
        if (null == stationInfo.getWfStatus()) {
            stationInfo.setWfStatus(0L);//草稿
        }
        if (null == stationInfo.getId()) {
            stationInfo.setId(IdGenerator.getNextId());//保存
        }
        stationInfo.setCreatetime(new Date());
        stationInfo.setModifytime(new Date());
        int result = this.insert(stationInfo);
        result = saveStationInfoRecord(stationInfo);

        return stationInfo;
    }

    private int saveStationInfoRecord(StationInfo stationInfo) {
        //保存局站记录
        StationRecord stationRecord = new StationRecord();
        BeanUtils.copyProperties(stationInfo, stationRecord);
        stationRecord.setStationid(stationInfo.getId());
        stationRecord.setId(null);
        stationRecord.setWfStatus(stationInfo.getWfStatus());//新增设置状态为
        stationRecord.setModifytime(new Date());
        //当前登陆人为更新者
        User user = ShiroUtils.getUser();
        stationRecord.setCreateuser(user.getId());
        return iStationRecordService.insert(stationRecord);
    }

    @Override
    public int updateStation(StationInfo stationInfo) {
        //草稿状态的数据直接修改局站表
        if ((0L) == stationInfo.getWfStatus()) {
            this.update(stationInfo);
        }
        //修改已完成状态(2)的数据时，修改局站表状态为3(修改中)
        StationInfo stationInfo1 = this.get(stationInfo.getId());
        if ((2L) == stationInfo.getWfStatus()) {
            stationInfo1.setWfStatus(3L);//修改中
            stationInfo.setWfStatus(3L);//修改中
            this.update(stationInfo1);
            //局站异常管控-局站停用时，更新局站异常管控表状态
            if (stationInfo != null && "1".equals(stationInfo.getStatus())) {
                this.updateStationAnomalyStatus(stationInfo.getId(), CommonConstants.AMM_ANOMALY_STATUS_CG);
            }
        }
        if (ObjectUtil.isEmpty(stationInfo.getStationtype()) || stationInfo.getStationtype().compareTo(10002L) != 0) {
            //非基站局站，5GR清空
            stationInfo.setStationcodeintid(null);
            stationInfo.setStationname5gr(null);
        }
        //局站记录表新增一条，用于审核后更新局站表表
        int result = saveStationInfoRecord(stationInfo);
        return result;
    }

    public List<StationInfo> selectmotherList(StationInfo stationInfo) {
        if ("sc".equalsIgnoreCase(configVersion)) {
            stationInfo.setDeployTo("sc");
        } else {
            stationInfo.setDeployTo("ln");
        }
        return stationInfoMapper.getMotherList(stationInfo);
    }


    @Override
    public List<StationInfo> selectListByAmmeter(StationInfo stationInfo, String type) {
        Map<String, Object> paramMap = new HashMap<>(2);
        Long stationType = stationInfo.getStationtype();
        if (StringUtils.isEmpty(type) || "0".equals(type)) {//关联局站
            paramMap.put("stationtypes", this.setStationtypes(stationType));
            if (null != stationType && "1411".equals(stationType.toString()) || "1412".equals(stationType.toString())) {
                paramMap.put("propertyright", 3);//铁塔数据
            }
        } else if ("1".equals(type)) {//按照关联的用电类型查询局站
            paramMap.put("stationtype", this.setStationtype(stationType));
            paramMap.put("stationtypes", new ArrayList<Long>());
        } else if ("2".equals(type)) {//PUE管控关联局站
            paramMap.put("type", type);
        }
        paramMap.put("stationname", stationInfo.getStationname());
        paramMap.put("id", stationInfo.getId());
        paramMap.put("stationcode", stationInfo.getStationcode());
        paramMap.put("resstationcode", stationInfo.getResstationcode());
        // 当用电类型选择铁塔基站类（即1411,1412），控制所选移动基站类局站只能是“51”开头的铁塔站址的局站
        //用电类型是非铁塔类基站（即1421,1422,1431,1432），选择局站只能是站址编码是非“51”开头的移动基站类局站
        if ("sc".equalsIgnoreCase(configVersion)) {
            paramMap.put("electrotype", stationType);
            paramMap.put("deployTo", "sc");
        } else {
            paramMap.put("deployTo", "ln");
        }

        User user = ShiroUtils.getUser();
        List<IdNameVO> companies = new ArrayList<>();
        if (null != stationInfo.getCompany()) {
            IdNameVO company = new IdNameVO();
            company.setId(stationInfo.getCompany().toString());
            companies.add(company);
        }
        paramMap.put("companies", companies);
        if (("0".equals(type) && (stationType == 121L || stationType == 122L))
                || ("1".equals(type) && (stationType == 3L || stationType == 4L))) {
            paramMap.put("stationtype", 10003L);//idc局站类型手动 维护
            paramMap.put("createuser", -1);//idc局站类型手动 维护
        }
        return stationInfoMapper.selectListByAmmeter(paramMap);
    }


    @Override
    public List<StationInfo> getCheckListByAmmeter(StationInfo stationInfo, String type) {
        Map<String, Object> paramMap = new HashMap<>(2);
        Long stationType = stationInfo.getStationtype();
        log.info("stationType:{}", stationType);
        if (StringUtils.isEmpty(type) || "0".equals(type)) {//关联局站
            // 查询条件不对，按张广利说的改，文档：https://app.apifox.com/link/project/5264960/apis/api-*********
            Map<List<Long>, Integer> electrotypeMappings = createElectrotypeMappings();
            // 查找匹配的站点类型
            electrotypeMappings.forEach((key, value) -> {
                if (key.contains(stationType)) {
                    paramMap.put("stationtype", value);

                    // 根据电表类型增加查询条件
                    String resStationCodeSql = getResStationCodeSql(key);
                    if (resStationCodeSql != null) {
                        paramMap.put("resstationcodeSql", resStationCodeSql);
                    }
                }
            });
            paramMap.put("stationtypes", new ArrayList<Long>());
        } else if ("1".equals(type)) {//按照关联的用电类型查询局站
            paramMap.put("stationtype", this.setStationtypeForLn(stationType));
            paramMap.put("stationtypes", new ArrayList<Long>());
        } else if ("2".equals(type)) {//PUE管控关联局站
            paramMap.put("type", type);
        }
        paramMap.put("stationname", stationInfo.getStationname());
        paramMap.put("id", stationInfo.getId());
        paramMap.put("stationcode", stationInfo.getStationcode());
        paramMap.put("resstationcode", stationInfo.getResstationcode());

        List<IdNameVO> companies = new ArrayList<>();
        if (null != stationInfo.getCompany()) {
            IdNameVO company = new IdNameVO();
            company.setId(stationInfo.getCompany().toString());
            companies.add(company);
        }
        paramMap.put("companies", companies);
        paramMap.put("termname", stationInfo.getTermname());

        List<StationInfo> stationInfos = stationInfoMapper.selectCheckListByAmmeter(paramMap);

        //*****铁塔台账录入判断局站是否到期停租
        for (int i = 0; i < stationInfos.size(); i++) {
            StationInfo info = stationInfos.get(i);
            CheckStationInfoDto checkStationInfoDto = new CheckStationInfoDto();
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
            String format = simpleDateFormat.format(new Date());
            checkStationInfoDto.setServeenddate(format);
            checkStationInfoDto.setStationaddr_code(info.getResstationcode());
            checkStationInfoDto.setType("selectStation");
            Map<String, String> map = this.checkStationInfo(checkStationInfoDto);
            info.setMap(map);
            stationInfos.set(i, info);
        }
        return stationInfos;
    }

    // 创建电表类型与站点类型映射
    public Map<List<Long>, Integer> createElectrotypeMappings() {
        Map<List<Long>, Integer> electroypeToStationTypeMap = new HashMap<>();
        electroypeToStationTypeMap.put(Arrays.asList(111L, 112L, 113L), 10001);
        electroypeToStationTypeMap.put(Arrays.asList(131L, 132L, 133L), 10005);
        electroypeToStationTypeMap.put(Arrays.asList(2L), 20001);
        electroypeToStationTypeMap.put(Arrays.asList(4L), -1);
        electroypeToStationTypeMap.put(Arrays.asList(33L), -2);
        electroypeToStationTypeMap.put(Arrays.asList(31L), 20002);
        electroypeToStationTypeMap.put(Arrays.asList(121L), 10003);
        electroypeToStationTypeMap.put(Arrays.asList(122L), 10004);
        electroypeToStationTypeMap.put(Arrays.asList(1421L, 1422L, 1431L, 1432L), 10002);
        electroypeToStationTypeMap.put(Arrays.asList(1411L, 1412L), 10002);
        return electroypeToStationTypeMap;
    }
    // 根据电表类型返回对应的 SQL 查询条件
    private String getResStationCodeSql(List<Long> electroTypes) {
        Set<Long> condition1 = SetUtils.newHashSet(1421L, 1422L, 1431L, 1432L);
        Set<Long> condition2 = SetUtils.newHashSet(1411L, 1412L);

        if (!Collections.disjoint(electroTypes, condition1)) {
            return "(SELECT room_id FROM  enco_host_ln WHERE LENGTH(room_id)>5\n" +
                    "UNION ALL \n" +
                    "SELECT DISTINCT termname FROM  enco_host_ln WHERE LENGTH(termname)>5\n)";
        } else if (!Collections.disjoint(electroTypes, condition2)) {
            return "(SELECT stationaddr_code FROM power_tower_info)";
        }
        return null;
    }

    /**
     * *****验证归集单局站是否过期 -impl
     *
     * <AUTHOR>
     * @date 2022/5/17
     */
    @Override
    public Map<String, String> checkSheet(CheckStationInfoDto stationInfo) {
        Map<String, String> map1 = new HashMap<>();
        List<String> listIds = new ArrayList<String>();
        String ids = stationInfo.getIds();
        String[] split = ids.split(",");
        for (int i = 0; i < split.length; i++) {
            if (!StringUtils.isEmpty(split[i])) {
                Map<String, Object> map = new HashMap<>();
                map.put("parid", Long.valueOf(split[i]));
                map.put("status", -1);
                map.put("union", false);
                //根据parid查询归集单明细
                List<AccountBaseResult> list = accountbillitempreService.selectAccountByAutoMap(map);
                for (AccountBaseResult accountBaseResult : list) {
                    String stationaddresscode = accountBaseResult.getStationaddresscode();
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss:S");
                    String format = simpleDateFormat.format(new Date());
                    CheckStationInfoDto checkStationInfoDto = new CheckStationInfoDto();
                    checkStationInfoDto.setStationaddr_code(stationaddresscode);
                    checkStationInfoDto.setServeenddate(format);
                    checkStationInfoDto.setType("selfAccount");
                    checkStationInfoDto.setFileid(accountBaseResult.getPcid());//台账id
                    map1 = this.checkStationInfo(checkStationInfoDto);
                    if (map1.get("iftimeout").equals("3")) {
                        if (map1.get("show") != null) {
                            map1.put("code", "0");
                            map1.put("msg", accountBaseResult.getProjectname() + "局站已过期,请上传附件说明");
                            return map1;
                        }
                    }
                }
            }
        }
        return map1;
    }

    @Override
    public StationInfo select5grforinitid(String stationcode5gr) {
        StationInfo stationInfo = stationInfoMapper.select5grforinitid(stationcode5gr);
        return stationInfo;
    }

    @Override
    public List<Ltestation> getListLtestation(Ltestation stationInfo) {
        Map<String, Object> paramMap = new HashMap<>(2);
        User user = ShiroUtils.getUser();
        String quercompany = "";
        List<IdNameVO> companies = new ArrayList<>();
        if (null != user.getCompany()) {
            IdNameVO company = new IdNameVO();
            company.setName(user.getCompanies().get(0).getName());
            companies.add(company);
        }
        String companyname = user.getCompanies().get(0).getName();
        if ("sc".equalsIgnoreCase(configVersion))
            quercompany = companyname.replace("中国电信", "").replace("分公司", "");
        else
            quercompany = companyname.replace("辽宁-", "").replace("分公司", "");
        paramMap.put("citycode", quercompany);
        if (null != stationInfo.getJtname()) {
            paramMap.put("jtname", stationInfo.getJtname());//idc局站类型手动 维护
        }
        if (null != stationInfo.getTacode()) {
            paramMap.put("tacode", stationInfo.getTacode());//idc局站类型手动 维护
        }
        if (null != stationInfo.getEnbid()) {
            paramMap.put("enbid", stationInfo.getEnbid());//idc局站类型手动 维护
        }
        return stationInfoMapper.getListLtestation(paramMap);
    }

    //根据用电类型查看相对应得局站类型
    private List<Long> setStationtypes(Long stationType) {
        List<Long> stationTypes = new ArrayList<>();
        //（1）用电类型选择“111 A类机楼（机房），112 B类机楼（机房），113 C类机楼（机房） ”时，关联局站只能选择局站类型为“生产用房-通信机房”类的局站。
        //（2）用电类型选择“121 自建数据中心，122 合建数据中心”时，关联局站只能选择局站类型为“生产用房-数据中心-对外IDC机柜机房，生产用房-数据中心-自用业务平台和IT支撑用房”类的局站。
        //（3）用电类型选择“131 固网接入局所，132 固网户外机柜，133 其他接入（包括WLAN、楼道交换机、全球眼等）”时，关联局站只能选择局站类型为“生产用房-接入局所及室外机柜”类的局站。
        //（4）用电类型选择“1411 标准基站，1412 室内分布、室外站，1421 标准基站，1422 室内分布、室外站，1431 标准基站，1432 室内分布、室外站” 时，关联局站只能选择局站类型为“生产用房-移动基站”类的局站。
        //（5）用电类型选择“2 管理办公用电”时，关联局站只能选择局站类型为“非生产用房-管理用房”类的局站。
        //（6）用电类型选择“31 自有营业厅，32 呼叫中心，33 其他”时，关联局站只能选择局站类型为“非生产用房-渠道用房，非生产用房-其他”类的局站。
        //（7）用电类型选择“4 其他用电”时，关联局站只能选择局站类型为“生产用房-其他，非生产用房-其他”类的局站。
        if (null != stationType) {
            if ("111".equals(stationType.toString()) || "112".equals(stationType.toString()) || "113".equals(stationType.toString())) {
                stationTypes.add(10001L); //生产用房-通信机房
            } else if ("131".equals(stationType.toString()) || "132".equals(stationType.toString()) || "133".equals(stationType.toString())) {
                stationTypes.add(10005L);//生产用房-接入局所及室外机柜
            } else if ("121".equals(stationType.toString()) || "122".equals(stationType.toString())) {
                stationTypes.add(10003L);
                stationTypes.add(10004L);
            } else if ("1411".equals(stationType.toString()) || "1412".equals(stationType.toString()) || "1421".equals(stationType.toString()) || "1422".equals(stationType.toString()) || "1431".equals(stationType.toString()) || "1432".equals(stationType.toString())) {
                stationTypes.add(10002L);
//            }else if("2".equals(stationType.toString())){
//                stationTypes.add(20001L);
//            }else if("31".equals(stationType.toString()) || "32".equals(stationType.toString()) || "33".equals(stationType.toString())){
//                stationTypes.add(20002L);
//                stationTypes.add(-2L);
//            }else if("4".equals(stationType.toString())){
//                stationTypes.add(-1L);
//                stationTypes.add(-2L);
            }
        }
        return stationTypes;
    }

    public static void main(String[] args) {
        System.out.println("2003".substring(0, 3));
    }

    private List<Long> setStationtypesForLn(Long stationType) {
        List<Long> stationTypes = new ArrayList<>();
        if (null != stationType) {
            int length = stationType.toString().length();

            if ((length == 3) &&
                    ("111".equals(stationType.toString().substring(0, 3))
                            ||
                            "112".equals(stationType.toString().substring(0, 3))
                            ||
                            "113".equals(stationType.toString().substring(0, 3)))
            ) {
                stationTypes.add(10001L); //生产用房-通信机房
            } else if (
                    (length == 3) &&
                            ("131".equals(stationType.toString().substring(0, 3))
                                    ||
                                    "132".equals(stationType.toString().substring(0, 3))
                                    ||
                                    "133".equals(stationType.toString().substring(0, 3)))
            ) {
                stationTypes.add(10005L);//生产用房-接入局所及室外机柜
            } else if (
                    (length == 1) &&
                            "2".equals(stationType.toString().substring(0, 1))
            ) {
                stationTypes.add(20001L);//非生产用房，管理用房
            } else if (
                    (length == 1) &&
                            "4".equals(stationType.toString().substring(0, 1))
            ) {
                stationTypes.add(-1L);//生产用房-其它
            } else if (
                    (length == 2) &&
                            "33".equals(stationType.toString().substring(0, 2))
            ) {
                stationTypes.add(-2L);//非生产用房-其他
            } else if (
                    (length == 2) &&
                            "31".equals(stationType.toString().substring(0, 2))) {
                stationTypes.add(20002L);//非生产用房-渠道用房
            } else if (
                    (length == 3) &&
                            "121".equals(stationType.toString().substring(0, 3))
            ) {
                stationTypes.add(10003L);//生产用房-数据中心-对外IDC机柜机房
            } else if (
                    (length == 3) &&
                            "122".equals(stationType.toString().substring(0, 3))
            ) {
                stationTypes.add(10004L);//生产用房-数据中心-自用业务平台和IT支撑用房
            } else if (
                    (length == 4 || length == 2) &&
                            "14".equals(stationType.toString().substring(0, 2))
            ) {
                stationTypes.add(10002L);//生产用房-移动基站
            }
        }
        return stationTypes;
    }

    public static List<Long> setStationtypesPro(Long eletype) {
        List<Long> stationTypes = new ArrayList<>();
        //（1）用电类型选择“111 A类机楼（机房），112 B类机楼（机房），113 C类机楼（机房） ”时，关联局站只能选择局站类型为“生产用房-通信机房”类的局站。
        //（2）用电类型选择“121 自建数据中心，122 合建数据中心”时，关联局站只能选择局站类型为“生产用房-数据中心-对外IDC机柜机房，生产用房-数据中心-自用业务平台和IT支撑用房”类的局站。
        //（3）用电类型选择“131 固网接入局所，132 固网户外机柜，133 其他接入（包括WLAN、楼道交换机、全球眼等）”时，关联局站只能选择局站类型为“生产用房-接入局所及室外机柜”类的局站。
        //（4）用电类型选择“1411 标准基站，1412 室内分布、室外站，1421 标准基站，1422 室内分布、室外站，1431 标准基站，1432 室内分布、室外站” 时，关联局站只能选择局站类型为“生产用房-移动基站”类的局站。
        //（5）用电类型选择“2 管理办公用电”时，关联局站只能选择局站类型为“非生产用房-管理用房”类的局站。
        //（6）用电类型选择“31 自有营业厅，32 呼叫中心，33 其他”时，关联局站只能选择局站类型为“非生产用房-渠道用房，非生产用房-其他”类的局站。
        //（7）用电类型选择“4 其他用电”时，关联局站只能选择局站类型为“生产用房-其他，非生产用房-其他”类的局站。
        if (null != eletype) {
            if ("111".equals(eletype.toString()) || "112".equals(eletype.toString()) || "113".equals(eletype.toString())) {
                stationTypes.add(10001L);
            } else if ("121".equals(eletype.toString()) || "122".equals(eletype.toString())) {
                stationTypes.add(10003L);
                stationTypes.add(10004L);
            } else if ("131".equals(eletype.toString()) || "132".equals(eletype.toString()) || "133".equals(eletype.toString())) {
                stationTypes.add(10005L);
            } else if ("1411".equals(eletype.toString()) || "1412".equals(eletype.toString()) || "1421".equals(eletype.toString()) || "1422".equals(eletype.toString()) || "1431".equals(eletype.toString()) || "1432".equals(eletype.toString())) {
                stationTypes.add(10002L);
//            }else if("2".equals(eletype.toString())){
//                stationTypes.add(20001L);
//            }else if("31".equals(eletype.toString()) || "32".equals(eletype.toString()) || "33".equals(eletype.toString())){
//                stationTypes.add(20002L);
//                stationTypes.add(-2L);
//            }else if("4".equals(eletype.toString())){
//                stationTypes.add(-1L);
//                stationTypes.add(-2L);
            }
        }
        return stationTypes;
    }

    //根据用电类型查看相对应得局站类型
    private Long setStationtype(Long stationType) {
        if (null != stationType) {
            if ("1".equals(stationType.toString())) {
                return 10001L;
            }
            if ("2".equals(stationType.toString())) {
                return 10002L;
            }
            if ("3".equals(stationType.toString())) {
                return 10003L;
            }
            if ("4".equals(stationType.toString())) {
                return 10004L;
            }
            if ("5".equals(stationType.toString())) {
                return 10005L;
            }
            if ("6".equals(stationType.toString())) {
                return 20001L;
            }
            if ("7".equals(stationType.toString())) {
                return 20002L;
            }
            if ("8".equals(stationType.toString())) {
                return -1L;
            }
            if ("9".equals(stationType.toString())) {
                return -2L;
            }
        }
        return null;
    }

    private Long setStationtypeForLn(Long stationType) {
        if (null != stationType) {
            int length = stationType.toString().length();

            if ((length == 3) &&
                    ("111".equals(stationType.toString().substring(0, 3))
                            ||
                            "112".equals(stationType.toString().substring(0, 3))
                            ||
                            "113".equals(stationType.toString().substring(0, 3)))
            ) {
                return (10001L); //生产用房-通信机房
            } else if (
                    (length == 3) &&
                            ("131".equals(stationType.toString().substring(0, 3))
                                    ||
                                    "132".equals(stationType.toString().substring(0, 3))
                                    ||
                                    "133".equals(stationType.toString().substring(0, 3)))
            ) {
                return (10005L);//生产用房-接入局所及室外机柜
            } else if (
                    (length == 1) &&
                            "2".equals(stationType.toString().substring(0, 1))
            ) {
                return (20001L);//非生产用房，管理用房
            } else if (
                    (length == 1) &&
                            "4".equals(stationType.toString().substring(0, 1))
            ) {
                return (-1L);//生产用房-其它
            } else if (
                    (length == 2) &&
                            "33".equals(stationType.toString().substring(0, 2))
            ) {
                return (-2L);//非生产用房-其他
            } else if (
                    (length == 2) &&
                            "31".equals(stationType.toString().substring(0, 2))) {
                return (20002L);//非生产用房-渠道用房
            } else if (
                    (length == 3) &&
                            "121".equals(stationType.toString().substring(0, 3))
            ) {
                return (10003L);//生产用房-数据中心-对外IDC机柜机房
            } else if (
                    (length == 3) &&
                            "122".equals(stationType.toString().substring(0, 3))
            ) {
                return (10004L);//生产用房-数据中心-自用业务平台和IT支撑用房
            } else if (
                    (length == 2) &&
                            "14".equals(stationType.toString().substring(0, 2))
            ) {
                return (10002L);//生产用房-移动基站
            }
        }
        return null;
    }

    //获取修改前数据
    @Override
    public List<StationInfo> selectOld(Long id) {
        stationInfoMapper.selectOld(id);
        return null;
    }

    //获取资源
    public List<StationInfo> getResStation(StationInfo stationInfo) {
        if ("ln".equalsIgnoreCase(configVersion)) {
            return stationInfoMapper.getResStation(stationInfo);
        }
//        return stationInfoMapper.getResStationSC(stationInfo);
        return stationInfoMapper.getValidStationSC(stationInfo);
    }

    public List<String> getUserRoleAuth(Long id) {
        return stationInfoMapper.getUserRoleAuth(id);
    }

    //按局站名统计个数
    public int IsStationnameExist(StationInfo stationInfo) {
        return this.count(stationInfo);
    }

    //统计记录表最新字段中是否已存在 ，防止新增得和修改流程中得名称重复
    public int IsStationnameExistInRecord(StationInfo stationInfo) {
        return stationInfoMapper.countExitInRecord(stationInfo);
    }

    public int IsStationnameExistEdit(StationInfo stationInfo) {
        return stationInfoMapper.countExitSelf(stationInfo);
    }

    public int IsStationnameExistInRecordEdit(StationInfo stationInfo) {
        return stationInfoMapper.countExitSelfRecord(stationInfo);
    }

    /**
     * 自动生成局站编码
     *
     * @return
     */
    @Override
    public String getStationCode() {
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String date = format.format(new Date());
        if (StringUtils.isEmpty(nowDate)) {
            nowDate = date;
        }
        String stationCode = "";
        String proName = "";
        String cityName = "";
        User user = ShiroUtils.getUser();
        for (IdNameVO value : user.getCompanies()) {
            if (value.getName().contains("-")) {
                String name = value.getName();
                proName = this.getPinYinHeadChar(name.substring(0, name.indexOf("-", name.indexOf("-"))));
                if (name.indexOf("省公司") == -1) {
                    cityName = this.getPinYinHeadChar(name.substring(name.indexOf("-", name.indexOf("-")) + 1, name.lastIndexOf("分公司")));
                } else {
                    cityName = this.getPinYinHeadChar(name.substring(name.indexOf("-", name.indexOf("-")) + 1, name.lastIndexOf("公司")));
                }

                break;
            }
        }
        /*String oldStationCode = stationInfoMapper.seleectStationCode(proName+cityName+"JZ"+date);
        String newStrNum = "00001";
        if(StringUtils.isNotEmpty(oldStationCode)){
            int num = Integer.parseInt(oldStationCode.substring(oldStationCode.length()-5));
            if(num<99999){
                num = num+1;
                newStrNum= String.format("%05d", num);
            }
        }
        stationCode = proName+cityName+"JZ"+date+newStrNum;*/
        stationCode = proName + cityName + "JZ" + date;
        return stationCode;
    }

    /**
     * 得到中文首字母
     *
     * @param str
     * @return
     */
    public String getPinYinHeadChar(String str) {

        String convert = "";
        for (int j = 0; j < str.length(); j++) {
            char word = str.charAt(j);
            String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(word);
            if (pinyinArray != null) {
                convert += pinyinArray[0].charAt(0);
            } else {
                convert += word;
            }
        }
        return convert.toUpperCase();
    }

    /**
     * 根据局站获取关联的电表信息
     *
     * @param ammeterorprotocol
     * @return
     */
    public List<Ammeterorprotocol> getAmmeterListByStation(Ammeterorprotocol ammeterorprotocol) {
        return AmmeterorprotocolMapper.getAmmeterListByStation(ammeterorprotocol);
    }

    /**
     * 通过局站名获取数据
     *
     * @param name
     * @return
     */
    public StationInfo getStationByName(String name) {
        return stationInfoMapper.getStationByName(name);
    }

    /**
     * 判断登陆用户角色是否有修改局站的权限(false表示有权限，不disabled)
     *
     * @param id
     * @return
     */
    public boolean isAuthEditStation(Long id) {
        List<String> tempList = stationInfoMapper.getUserRoleAuth(id);
        for (String tempStr : tempList) {
            if (Arrays.binarySearch(authEditArr, tempStr) >= 0) {
                return false;
            }
        }
        return true;
    }

    /**
     * 查询数据是否处于代办里表中，确定能否提交流程
     */
    public List isInTodoList(Map<String, Object> params) {
        List<StationInfo> retlist = new ArrayList<StationInfo>();
        retlist = stationInfoMapper.isInTodoList(params);
        return retlist;
    }

    /**
     * 县能耗管理员时，获取所在部门上级下的所有country
     *
     * @return
     */
    public List getAllBelongCountry() {
        List<String> retList = new ArrayList<String>();
        User user = ShiroUtils.getUser();
        //当前登陆用户所有的部门
        List<IdNameVO> listCountry = user.getDepartments();
        //遍历获取上级后再获取其下所有部门
        for (IdNameVO idName : listCountry) {
            String perentid = stationInfoMapper.getOrgParentId(idName.getId());
            if (perentid != null && !"1".equals(getOrgtype(perentid))) {
                //用父节点获取其下所有子节点
                String ids = stationInfoMapper.selectIds(perentid);
                if (ids != "") {
                    ids = ids.substring(1);
                    String[] temp = ids.split(",");
                    for (int i = 0; i < temp.length; i++) {
                        retList.add(String.valueOf(temp[i]));
                    }
                }
            } else if (perentid != null && "1".equals(getOrgtype(perentid))) {//取自身下所有部门，防止自己父亲节点类型为1时遗漏
                String ids = stationInfoMapper.selectIds(idName.getId());
                if (ids != "") {
                    ids = ids.substring(1);
                    String[] temp = ids.split(",");
                    for (int i = 0; i < temp.length; i++) {
                        retList.add(String.valueOf(temp[i]));
                    }
                }
            }

        }
        return retList;
    }

    public String getOrgtype(String id) {
        return stationInfoMapper.getOrgtype(id);
    }

    public int isModifiedByNowUser(Long id, Long userid) {
        StationInfo stationInfo = new StationInfo();
        stationInfo.setId(id);
        stationInfo.setCreateuser(userid);
        int ret = stationInfoMapper.getRecordCount(stationInfo);
        return ret;
    }

    public List<StationInfo> getStationAddr(StationInfo stationInfo) {
        if ("ln".equalsIgnoreCase(configVersion)) {
            return stationInfoMapper.getStationAddr(stationInfo);
        }
        return stationInfoMapper.getStationAddrSC(stationInfo);
    }

    public List<StationInfo> getStationHousing(StationInfo stationInfo) {
        return stationInfoMapper.getStationHousing(stationInfo);
    }

    public List<StationInfo> selectObjectBy(StationInfo stationInfo) {
        return stationInfoMapper.selectObjectBy(stationInfo);
    }

    @Override
    public List<StationInfo> selectbaseList(StationInfo stationInfo) {
        return stationInfoMapper.selectbaseList(stationInfo);
    }

    @Override
    public List<StationInfo> getResStationAndRoom(ResStationQueryDto queryDto) {
        // 根据type参数判断调用哪个查询方法
        if ("room".equals(queryDto.getType())) {
            return stationInfoMapper.getResRoomList(queryDto);
        } else {
            // 默认查询局站，包括type为null、空字符串或"station"的情况
            return stationInfoMapper.getResStationList(queryDto);
        }
    }

    /**
     * *****验证局站信息 -impl
     *
     * <AUTHOR>
     * @date 2022/4/25
     */
    @Override
    public Map<String, String> checkStationInfo(CheckStationInfoDto stationInfo) {
        Map<String, String> map = new HashMap<>();
        String inputdate = stationInfo.getServeenddate();
        String id = stationInfo.getStationaddr_code();
        if (StringUtils.isEmpty(inputdate)) {
            map.put("iftimeout", "0");//未传入参数
            return map;
        }
        if (id == null || id.equals("0")) {
            map.put("iftimeout", "0");//未传入参数
            return map;
        }
        //StationInfo info = stationInfoMapper.selectStationFromTwo(id);
        StationInfo info = null;
        if (info == null) {
            map.put("iftimeout", "1");//未到期
            return map;
        }
        String serveenddate = info.getServeenddate();
        if (inputdate.compareTo(serveenddate) >= 0) {  //已到期
            if (!StringUtils.isEmpty(stationInfo.getType())) {
                if (!stationInfo.getType().equals("selectStation")) { //选择局站不校验
                    Attachments attachments = new Attachments();
                    attachments.setBusiId(stationInfo.getFileid());
                    List<Attachments> attachments1 = attachmentsService.selectList(attachments);
                    if (attachments1 == null || attachments1.size() == 0) {
                        map.put("show", "noshow");//已到期
                    }
                }
            }
            map.put("endtime", serveenddate);
            map.put("iftimeout", "3");//已到期
        } else if (inputdate.compareTo(serveenddate) < 0) {
            //到期时间增加一一个月
            String inputdateAddMonth = ConvertUtils.addMonth(inputdate, "yyyy-MM-dd HH:mm:ss", 1, "yyyy-MM-dd HH:mm:ss");
            if (inputdateAddMonth.compareTo(serveenddate) > 0) {
                map.put("endtime", serveenddate);
                map.put("iftimeout", "2");//到期时间小于一个月
            } else {
                map.put("endtime", serveenddate);
                map.put("iftimeout", "1");//未到期
            }
        }
        return map;
    }

    @Override
    public void set5grCodeForLn(StationInfo stationInfo) {
        if ("sc".equals(configVersion)) {
            return;
        }
        //5gr编码
        NhSite nite = stationInfoMapper.get5grCodeByStation(stationInfo.getId());
        stationInfo.setGrCode(Objects.isNull(nite) ? "" : nite.getCode());
    }

    public List<StationInfo> selectListWithArea(StationInfo stationInfo) {
        if ("sc".equals(configVersion)) {
            stationInfo.setDeployTo("sc");
        } else {
            stationInfo.setDeployTo("ln");
        }
        return stationInfoMapper.selectList(stationInfo);
    }

    /**
     * 局站异常管控-局站停用时，更新局站异常管控表状态
     * @param stationId  局站id
     * @param status     状态
     */
    private void updateStationAnomalyStatus(Long stationId, int status) {
        if (stationId != null) {
            PowerStationAnomaly entity = new PowerStationAnomaly();
            entity.setStationId(stationId);
            entity.setStatus(status);
            entity.initUpdate();
            powerStationAnomalyMapper.updateStatusByStationId(entity);
        }
    }

    /**
     * 批量停用
     * @param vo
     * @return
     */
    @Override
    public int batchStop(StationBatchStopVo vo) {

        String[] stationIds = StringUtils.split(vo.getId(), ",");
        for (String stationId : stationIds) {
            StationInfo stationInfo = this.get(Long.valueOf(stationId));
            if (stationInfo != null) {
                stationInfo.setWfStatus(3L);//修改中
                stationInfo.setStatus("1"); //停用
                stationInfo.setModifytime(new Date());
                this.update(stationInfo);
                //局站记录表新增一条，用于审核后更新局站表表
                saveStationInfoRecord(stationInfo);
                //局站异常管控-局站停用时，更新局站异常管控表状态
                this.updateStationAnomalyStatus(stationInfo.getId(), CommonConstants.AMM_ANOMALY_STATUS_CG);
            }
        }
        return 1;
    }
}
