package com.sccl.modules.business.stationreportwhitelist.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.google.common.collect.Lists;
import com.sccl.exception.BusinessException;
import com.sccl.modules.business.statinAudit.domain.SysOrganizations;
import com.sccl.modules.business.statinAudit.mapper.PowerStationInfoMapper;
import com.sccl.modules.business.stationreportwhitelist.domain.*;
import com.sccl.modules.business.stationreportwhitelist.dto.StationReportWhitelistDTO;
import com.sccl.modules.business.stationreportwhitelist.dto.StationReportWhitelistQuery;
import com.sccl.modules.business.stationreportwhitelist.enums.BillStatus;
import com.sccl.modules.business.stationreportwhitelist.enums.MyDict;
import com.sccl.modules.business.stationreportwhitelist.enums.PowerAmmeterorprotocolStatus;
import com.sccl.modules.business.stationreportwhitelist.mapper.PowerAmmeterorprotocolMapper;
import com.sccl.modules.business.stationreportwhitelist.mapper.PowerElectricClassificationMapper;
import com.sccl.modules.business.stationreportwhitelist.mapper.StationReportWhitelistMapper;
import com.sccl.modules.business.stationreportwhitelist.vo.OneTableMultiStationListCountVO;
import com.sccl.modules.business.stationreportwhitelist.vo.OneWatchHasManyStationsExport;
import com.sccl.modules.business.stationreportwhitelist.vo.RationalityOfUnitPriceExport;
import com.sccl.modules.business.stationreportwhitelist.vo.StationReportWhitelistVO;
import com.sccl.modules.uniflow.common.WFModel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Slf4j
@Service
@AllArgsConstructor
public class StationReportWhitelistServiceImpl extends ServiceImpl<StationReportWhitelistMapper, StationReportWhitelist> implements StationReportWhitelistService {

    private PowerStationInfoMapper stationInfoMapper;
    private PowerElectricClassificationMapper electricClassificationMapper;
    private PowerAmmeterorprotocolMapper powerAmmeterorprotocolMapper;

    @Override
    public IPage<StationReportWhitelistVO> selectList(Page<StationReportWhitelistVO> page, StationReportWhitelistQuery query) {
        MPJLambdaWrapper<StationReportWhitelist> wrapper = this.getWrapper(query);
        wrapper.isNotNull(StationReportWhitelist::getBillId);
        wrapper.orderByDesc(StationReportWhitelist::getId);
        Page<StationReportWhitelistVO> voPage = baseMapper.selectJoinPage(page, StationReportWhitelistVO.class, wrapper);
        for (StationReportWhitelistVO vo : voPage.getRecords()) {
            // 设置额外参数
            this.setAdditionalParameters(vo);
        }
        return voPage;
    }


    @Override
    public StationReportWhitelistVO findById(Long id) {
        StationReportWhitelistQuery query = new StationReportWhitelistQuery();
        query.setId(id);
        MPJLambdaWrapper<StationReportWhitelist> wrapper = this.getWrapper(query);
        StationReportWhitelistVO vo = baseMapper.selectJoinOne(StationReportWhitelistVO.class, wrapper);
        if (ObjectUtil.isNotNull(vo)) {
            // 设置额外参数
            this.setAdditionalParameters(vo);
        }
        return vo;
    }

    private void setAdditionalParameters(StationReportWhitelistVO vo) {
        // 获取公司名称
        this.getOrgName(vo);
        // 获取用电类型
        this.getElectrotypeName(vo);
        // 获取流程状态名称
        vo.setBillStatusName(BillStatus.getNameByCode(vo.getBillStatus()));
        // 电表状态名
        vo.setStatusName(PowerAmmeterorprotocolStatus.getNameByCode(vo.getStatus()));

        // 局站类型
        vo.setStationtypeName(MyDict.findDictLabel(MyDict.TYPE.BUR_STAND_TYPE, String.valueOf(vo.getStationtype())));
        // 获取真实局站编码
        if (vo.getStationcode() != null) {
            PowerStationInfo stationInfo = new PowerStationInfo().selectById(vo.getStationId());
            if (stationInfo != null) {
                vo.setStationcode(stationInfo.getStationcode());
            }
        }
        // 当前局站关联电表数
        vo.setStationMetersCount(new PowerAmmeterorprotocol().selectCount(Wrappers.<PowerAmmeterorprotocol>lambdaQuery()
                .eq(PowerAmmeterorprotocol::getStationcode, vo.getStationId())
        ));
        // 对外结算类型
        vo.setDirectsupplyflagName(MyDict.findDictLabel(MyDict.TYPE.directSupplyFlag, String.valueOf(vo.getDirectsupplyflag())));

        // 如果一表多站，获取局站数量
        OneTableMultiStationListCountVO oneTableMultiStationListCount = powerAmmeterorprotocolMapper.oneTableMultiStationListCount(vo.getMeterCode());
        if (ObjectUtil.isNotNull(oneTableMultiStationListCount)) {
            vo.setStationTotal(oneTableMultiStationListCount.getStationTotal());
            vo.setStationcodes(oneTableMultiStationListCount.getStationCodes());
        }


    }


    public MPJLambdaWrapper<StationReportWhitelist> getWrapper(StationReportWhitelistQuery query) {
        MPJLambdaWrapper<StationReportWhitelist> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(StationReportWhitelist.class);

        // 电表信息关联
        wrapper.selectAll(PowerAmmeterorprotocol.class);
        wrapper.selectAs(PowerAmmeterorprotocol::getStationcode, StationReportWhitelistVO::getStationId);
        wrapper.leftJoin(PowerAmmeterorprotocol.class, PowerAmmeterorprotocol::getId, StationReportWhitelist::getMeterId);
        wrapper.eq(query.getId() != null, StationReportWhitelist::getId, query.getId());
        // 白名单类型
        wrapper.eq(StrUtil.isNotBlank(query.getType()), StationReportWhitelist::getType, query.getType());
        // 项目名称查询
        wrapper.like(StrUtil.isNotBlank(query.getProjectname()), PowerAmmeterorprotocol::getProjectname, query.getProjectname());
        // 电表编号查询
        wrapper.eq(StrUtil.isNotBlank(query.getMeterCode()), PowerAmmeterorprotocol::getAmmetername, query.getMeterCode());
        // 局站名称查询
        wrapper.like(StrUtil.isNotBlank(query.getStationName()), PowerAmmeterorprotocol::getStationName, query.getStationName());
        // 分公司查询
        wrapper.eq(query.getCompany() != null, PowerAmmeterorprotocol::getCompany, query.getCompany());
        // 部门查询
        wrapper.eq(query.getCountry() != null, PowerAmmeterorprotocol::getCountry, query.getCountry());
        // 申请状态
        wrapper.leftJoin(StationReportWhitelistBill.class, StationReportWhitelistBill::getId, StationReportWhitelist::getBillId);
        wrapper.selectAll(StationReportWhitelistBill.class);
        wrapper.eq(query.getBillStatus() != null, StationReportWhitelistBill::getBillStatus, query.getBillStatus());

        // 白名单类型集合
//        wrapper.selectCollection(StationReportWhitelistType.class, StationReportWhitelistVO::getTypeList, map -> map
//                .result(StationReportWhitelistType::getWhitelistId)
//                .result(StationReportWhitelistType::getWhitelistType)
//        );
//        wrapper.leftJoin(StationReportWhitelistType.class, StationReportWhitelistType::getWhitelistId, StationReportWhitelist::getId);

        // 流程实例表信息
        wrapper.select(WhitelistWfProcInst::getBusiAlias).leftJoin(WhitelistWfProcInst.class, WhitelistWfProcInst::getId, StationReportWhitelistBill::getProcInstId);

        return wrapper;
    }

    /**
     * 获取公司名称
     *
     * @param vo 白名单
     */
    private void getOrgName(StationReportWhitelistVO vo) {
        // 分公司
        if (vo.getCompany() != null) {
            SysOrganizations orgCompany = stationInfoMapper.selectsysOrgNameById(vo.getCompany());
            if (orgCompany != null) {
                vo.setCompanyName(orgCompany.getOrgName());
            }
        }

        // 部门
        if (vo.getCountry() != null) {
            SysOrganizations orgCountry = stationInfoMapper.selectsysOrgNameById(vo.getCountry());
            if (orgCountry != null) {
                String countryName = "";
                SysOrganizations superOrg = stationInfoMapper.selectsysOrgNameById(Long.valueOf(orgCountry.getParentCompanyNo()));
                if (superOrg != null) {
                    countryName = StrUtil.format("{}-{}", superOrg.getOrgName(), orgCountry.getOrgName());
                } else {
                    countryName = orgCountry.getOrgName();
                }
                vo.setCountryName(countryName);
            }
        }
    }

    /**
     * 获取用电类型
     * 目前根据表结构有3层级，暂只查询2层
     *
     * @param vo 白名单
     */
    private void getElectrotypeName(StationReportWhitelistVO vo) {
        String electrotypeName = "";
        String electrotypeName2 = "";
        if (vo.getElectrotype() != null) {
            // 第一层
            PowerElectricClassification classification = electricClassificationMapper.selectById(vo.getElectrotype());
            electrotypeName = classification.getTypeName();
            if (ObjectUtil.isNotNull(classification) && classification.getParentId() != null) {
                //第二层
                PowerElectricClassification classification2 = electricClassificationMapper.selectById(classification.getParentId());
                electrotypeName2 = StrUtil.isNotBlank(classification2.getTypeName()) ? classification2.getTypeName() + "/" : "";
            }
        }
        vo.setElectrotypeName(electrotypeName2 + electrotypeName);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String add(StationReportWhitelistDTO dto) {
        // 电表不能重复
        StationReportWhitelist one = this.getOne(new LambdaQueryWrapper<StationReportWhitelist>()
                .eq(StationReportWhitelist::getMeterCode, dto.getMeterCode())
                .last("LIMIT 1")
        );
        if (one != null) {
            throw new BusinessException("电表编号已存在");
        }

        StationReportWhitelist whitelist = new StationReportWhitelist();
        BeanUtils.copyProperties(dto, whitelist);
        whitelist.setCreateTime(new DateTime());
        int insert = baseMapper.insert(whitelist);


        return String.valueOf(whitelist.getId());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int edit(StationReportWhitelistDTO dto) {

        return 0;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int del(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("id不能为空");
        }
        StationReportWhitelist whitelist = baseMapper.selectById(id);
        if (ObjectUtil.isNull(whitelist)) {
            return 0;
        }
        // 只能删除草稿、移除状态
//        if (!BillStatus.DRAFT.getCode().equals(whitelist.getBillStatus())
//                && !BillStatus.REMOVE.getCode().equals(whitelist.getBillStatus())) {
//            throw new BusinessException("只能删除草稿、移除状态的记录");
//        }

        // 删除白名单
        return baseMapper.deleteById(id);
    }

    /**
     * 流程回调
     *
     * @param wfModel 流程
     */
    @Override
    public void uniflowCallBack(WFModel wfModel) {
        log.info("白名单流程回调:{}-{}-{}", wfModel.getBusiId(), wfModel.getBusiAlias(), wfModel.getProcInstId());
//        StationReportWhitelist whitelist = new StationReportWhitelist();
//        whitelist.setId(Long.valueOf(wfModel.getBusiId()));
//        whitelist.setProcInstId(wfModel.getProcInstId());
//        // 流程开始
//        if (StringUtils.isNotEmpty(wfModel.getCallbackType())
//                && "PROCESS_STARTED".equals(wfModel.getCallbackType())
//                && wfModel.getVariables().containsKey("firstNode")
//                && wfModel.getVariables().get("firstNode").equals(true)) {
//            if ("ADD_WHITELIST".equals(wfModel.getBusiAlias())) {
//                // 修改单据状态为流程中
//                whitelist.setBillStatus(BillStatus.PROCESSING.getCode());
//            } else {
//                // 修改单据状态为修改流程中
//                whitelist.setBillStatus(BillStatus.MODIFY_PROCESSING.getCode());
//            }
//            this.updateById(whitelist);
//        } else if (StringUtils.isNotEmpty(wfModel.getCallbackType()) && "PROCESS_COMPLETED".equals(wfModel.getCallbackType())) {
//            // 修改单据状态为流程结束
//            try {
//                whitelist.setBillStatus(BillStatus.COMPLETED.getCode());
//                this.updateById(whitelist);
//            } catch (Exception e) {
//                log.info("白名单流程回调修改单据状态为已完成并更新数据异常");
//                OperLog model = new OperLog();
//                model.setOperName("WHITELIST");
//                model.setTitle("白名单流程回调修改单据状态为已完成并更新数据异常");
//                model.setMethod("updateDateByChange");
//                model.setErrorMsg("白名单id:" + wfModel.getBusiId() + " 流程：" + wfModel.getBusiAlias() + " 用户：" + wfModel.getApplyUserId());
//                model.setOperTime(new Date());
//                operLogMapper.insert(model);
//                throw e;
//            }
//        }
    }

    /**
     * 由于电表编号和协议，同一张表却关联两个字段，所以添加电表id，这里把历史数据赋值电表id
     */
    @Override
    public void initMeterId() {
        int page = 0;
        int limit = 100;
        int countNull = 0;
        while (true) {
            System.out.println(StrUtil.format("第{}页", page + 1));
            IPage<StationReportWhitelist> rows = new Page<>(page, limit);
            IPage<StationReportWhitelist> iPage = baseMapper.selectPage(rows, new LambdaQueryWrapper<StationReportWhitelist>()
                    .select(StationReportWhitelist::getId, StationReportWhitelist::getMeterCode)
                    .isNull(StationReportWhitelist::getMeterId)
            );

            for (StationReportWhitelist record : iPage.getRecords()) {
                System.out.println(record.getMeterCode());
                PowerAmmeterorprotocol articleOne = new PowerAmmeterorprotocol().selectOne(new LambdaQueryWrapper<PowerAmmeterorprotocol>()
                        .select(PowerAmmeterorprotocol::getId, PowerAmmeterorprotocol::getProtocolname, PowerAmmeterorprotocol::getAmmetername)
                        .eq(PowerAmmeterorprotocol::getProtocolname, record.getMeterCode())
                        .or(it -> it.eq(PowerAmmeterorprotocol::getAmmetername, record.getMeterCode()))
                        .last("LIMIT 1")
                );

                if (articleOne == null) {
                    countNull += 1;
                } else {
                    // 赋值电表id
//                    record.setMeterId(articleOne.getId());
//                    record.updateById();
                }
            }
            page += 1;
            if (iPage.getRecords().size() < limit) {
                System.out.println("更新完毕,脏数据:" + countNull);
                break;
            }
        }
    }

    @Override
    public List<OneWatchHasManyStationsExport> exportOneWatchHasManyStationsExport(Page<StationReportWhitelistVO> page, StationReportWhitelistQuery query) {
        MPJLambdaWrapper<StationReportWhitelist> wrapper = this.getWrapper(query);
        wrapper.isNotNull(StationReportWhitelist::getBillId);
        wrapper.orderByDesc(StationReportWhitelist::getId);
        List<StationReportWhitelistVO> list;
        List<OneWatchHasManyStationsExport> exportList = Lists.newArrayList();
        if (query.isExportAll()) {
            list = baseMapper.selectJoinList(StationReportWhitelistVO.class, wrapper);
        } else {
            list = baseMapper.selectJoinPage(page, StationReportWhitelistVO.class, wrapper).getRecords();
        }
        for (StationReportWhitelistVO vo : list) {
            // 设置额外参数
            this.setAdditionalParameters(vo);
            OneWatchHasManyStationsExport export = new OneWatchHasManyStationsExport();
            BeanUtils.copyProperties(vo, export);
            exportList.add(export);
        }
        return exportList;
    }

    @Override
    public List<RationalityOfUnitPriceExport> exportRationalityOfUnitPrice(Page<StationReportWhitelistVO> page, StationReportWhitelistQuery query) {
        MPJLambdaWrapper<StationReportWhitelist> wrapper = this.getWrapper(query);
        wrapper.isNotNull(StationReportWhitelist::getBillId);
        wrapper.orderByDesc(StationReportWhitelist::getId);
        List<StationReportWhitelistVO> list;
        List<RationalityOfUnitPriceExport> exportList = Lists.newArrayList();
        if (query.isExportAll()) {
            list = baseMapper.selectJoinList(StationReportWhitelistVO.class, wrapper);
        } else {
            list = baseMapper.selectJoinPage(page, StationReportWhitelistVO.class, wrapper).getRecords();
        }
        for (StationReportWhitelistVO vo : list) {
            // 设置额外参数
            this.setAdditionalParameters(vo);
            RationalityOfUnitPriceExport export = new RationalityOfUnitPriceExport();
            BeanUtils.copyProperties(vo, export);
            exportList.add(export);
        }
        return exportList;
    }
}
