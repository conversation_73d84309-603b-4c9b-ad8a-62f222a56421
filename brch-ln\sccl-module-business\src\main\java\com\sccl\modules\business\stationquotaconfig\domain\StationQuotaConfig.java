package com.sccl.modules.business.stationquotaconfig.domain;

import com.sccl.framework.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;


/**
 * 基站定额分公司设置表 station_quota_config
 * 
 * <AUTHOR>
 * @date 2021-08-12
 */
public class StationQuotaConfig extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
    /** 单位 */
    private Long company;
    /** 部门 */
    private Long country;
    /** 定额类型 */
    private String type;
    /** 站址编码 */
    private String resstationcode;
    /** 站址名称 */
    private String resstationname;
    /** 定额值 */
    private BigDecimal quotaValue;
    /** 上下限 */
    private BigDecimal percent;


	public void setCompany(Long company)
	{
		this.company = company;
	}

	public Long getCompany() 
	{
		return company;
	}

	public void setCountry(Long country)
	{
		this.country = country;
	}

	public Long getCountry() 
	{
		return country;
	}

	public void setType(String type)
	{
		this.type = type;
	}

	public String getType() 
	{
		return type;
	}

	public void setResstationcode(String resstationcode)
	{
		this.resstationcode = resstationcode;
	}

	public String getResstationcode() 
	{
		return resstationcode;
	}

	public void setResstationname(String resstationname)
	{
		this.resstationname = resstationname;
	}

	public String getResstationname() 
	{
		return resstationname;
	}

	public void setQuotaValue(BigDecimal quotaValue)
	{
		this.quotaValue = quotaValue;
	}

	public BigDecimal getQuotaValue() 
	{
		return quotaValue;
	}

	public void setPercent(BigDecimal percent)
	{
		this.percent = percent;
	}

	public BigDecimal getPercent() 
	{
		return percent;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("company", getCompany())
            .append("country", getCountry())
            .append("type", getType())
            .append("resstationcode", getResstationcode())
            .append("resstationname", getResstationname())
            .append("quotaValue", getQuotaValue())
            .append("percent", getPercent())
            .toString();
    }
}
