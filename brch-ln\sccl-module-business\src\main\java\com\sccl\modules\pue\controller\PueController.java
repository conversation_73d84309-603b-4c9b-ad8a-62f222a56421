package com.sccl.modules.pue.controller;

import com.sccl.common.utils.ShiroUtils;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.pue.domain.PueDetail;
import com.sccl.modules.pue.service.PueService;
import com.sccl.modules.system.role.domain.Role;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.service.IUserService;
import org.apache.commons.collections.MapUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.Set;

@RestController
@RequestMapping("/business/pue")
public class PueController extends BaseController {

    @Autowired
    private PueService pueService;
    @Autowired
    private IUserService userService;

    /**
     * PUE查询
     * @param pueDetail
     * @return
     */
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody PueDetail pueDetail){
        startPage();
        List<PueDetail> list = pueService.findList(pueDetail);
        return getDataTable(list);
    }

    /**
     * pue导出
     * @param param
     * @param response
     * @throws IOException
     */
    @PostMapping("/exportExcel")
    @ResponseBody
    public void exportExcel(@RequestBody Map<String,Object> param,
                            HttpServletResponse response) throws IOException {
        String filename = "";
        XSSFWorkbook wb = export(param);
        response.setContentType("application/vnd.ms-excel");
        response.addHeader("Content-Disposition","attachment;filename=".concat(String.valueOf(URLEncoder.encode(filename,"UTF-8"))));
        InputStream in = null;
        //临时缓冲区
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        //创建临时文件
        wb.write(out);
        byte[] bookByteArray = out.toByteArray();
        in = new ByteArrayInputStream(bookByteArray);
        OutputStream outputStream = response.getOutputStream();
        int bufferSize = 4096;
        byte[] buffer = new byte[bufferSize];
        BufferedOutputStream bos = null;
        BufferedInputStream bis = null;
        try{
            bos = new BufferedOutputStream(outputStream);
            bis = new BufferedInputStream(in);
            int n = (-1);
            while((n = bis.read(buffer,0,bufferSize)) > -1){
                bos.write(buffer,0,n);
            }
            response.flushBuffer();
        } finally {
            if(bis != null){
                bis.close();
            }
            if(bos != null){
                bos.close();
            }
            if(outputStream != null){
                outputStream.flush();
                outputStream.close();
            }
        }
    }

    public XSSFWorkbook export(Map<String,Object> param){
        XSSFWorkbook wb = new XSSFWorkbook();
        XSSFSheet sheet = wb.createSheet("sheet1");
        String exportType = MapUtils.getString(param, "exportType");
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isSubAdmin = false;
        for(Role role : roles){
            if(role.getCode().startsWith("SUB_")){
                isSubAdmin = true;
            }
        }
        if(isSubAdmin){
            List<IdNameVO> departments = user.getDepartments();
            param.put("country",departments.get(0).getId());
        }
        if("PUE排名".equals(exportType)){
            String heads[][] = {{"排名","地市","PUE平均值","月生产分摊电量","月主设备电量","PUE最大值","PUE最小值","环比","趋势"}};
            setHearder(heads,null,sheet,wb);
            sheet.setColumnWidth(0,256*50);//设置分公司列 固定宽度
            //查询列表数据
            List<Map<String,Object>> list = null;
            setBody(list,null,sheet,wb,heads.length);
        }else if("PUE分布".equals(exportType)){
            String heads[][] = {{"PUE区间","占比%","局站数量","局站分类1","局站分类2","局站分类3","环比","趋势"}};
            setHearder(heads,null,sheet,wb);
            sheet.setColumnWidth(0,256*50);//设置分公司列 固定宽度
            //查询列表数据
            List<Map<String,Object>> list = null;
            setBody(list,null,sheet,wb,heads.length);
        }else if("结果列表".equals(exportType)){
            String heads[][] = {{"所属运营分局","局站名称","局站编码","局站类型","用电类型","PUE平均值","月生产分摊电量","月主设备电量","最大值","最小值","环比","是否派单","是否回单"}};
            setHearder(heads,null,sheet,wb);
            sheet.setColumnWidth(0,256*50);//设置分公司列 固定宽度
            //查询列表数据
            List<Map<String,Object>> list = null;
            setBody(list,null,sheet,wb,heads.length);
        }
        return wb;
    }

    private void setHearder(String[][] heads, int[][] addMergedRegion, XSSFSheet sheet, XSSFWorkbook wb) {
        // 生成一种样式
        XSSFCellStyle style = wb.createCellStyle();
        // 设置样式
//		style.setFillForegroundColor(HSSFColor.WHITE.index);
//		style.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        style.setWrapText(true);
        // 生成一种字体
        XSSFFont font = wb.createFont();
        // 设置字体
        font.setFontName("宋体");
        // 设置字体大小
        font.setFontHeightInPoints((short) 10);
        // 字体加粗
        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        // 在样式中引用这种字体
        style.setFont(font);
        int count = 0;
        for (String head[] : heads) {
            XSSFRow row = sheet.createRow(count);
            for (int i = 0; i < head.length; i++) {
                XSSFCell cell = row.createCell(i);
                cell.setCellValue(head[i]);
                cell.setCellStyle(style);
                sheet.autoSizeColumn(i, true);// 根据字段长度自动调整列的宽度
            }
            count++;
        }
        //合并单元格
        for (int merged[] : addMergedRegion) {
            sheet.addMergedRegion(new CellRangeAddress(merged[0], merged[1], merged[2], merged[3]));
        }
    }

    private void setBody(List<Map<String, Object>> list, int[][] addMergedRegionbody, XSSFSheet sheet, XSSFWorkbook wb, int rowNum) {
        // 生成并设置另一个样式
        XSSFCellStyle style = wb.createCellStyle();
//		style.setFillForegroundColor(HSSFColor.WHITE.index);
//		style.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        style.setWrapText(true);
        // 生成另一种字体2
        XSSFFont font = wb.createFont();
        // 设置字体
        font.setFontName("宋体");
        // 设置字体大小
        font.setFontHeightInPoints((short) 10);
        // 在样式2中引用这种字体
        style.setFont(font);
        for (Map<String, Object> item : list) {
            XSSFRow row = sheet.createRow(rowNum++);
            XSSFCell cell = null;
            Set<Map.Entry<String, Object>> entries = item.entrySet();
            int i = 0;
            for (Map.Entry<String, Object> entry : entries) {
                if (!"company".equals(entry.getKey())) {
                    cell = row.createCell(i++);
                    cell.setCellValue(entry.getValue() == null ? "" : entry.getValue().toString());
                    cell.setCellStyle(style);
                }
            }
        }
        //合并单元格
        for (int merged[] : addMergedRegionbody) {
            sheet.addMergedRegion(new CellRangeAddress(merged[0], merged[1], merged[2], merged[3]));
        }
    }



}
