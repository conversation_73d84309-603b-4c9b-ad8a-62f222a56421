package com.sccl.modules.business.cost.vo;

import com.sccl.framework.aspectj.lang.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

/**
 * 局站业务电量查询详情 一览 结果列表
 */
@Data
public class StationElectricXqResultVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 局站编码
     */
    @Excel(name = "局站编码")
    private String stationCode;

    /**
     * 资源编码
     */
    private String pueCode;

    /**
     * 局站名称
     */
    @Excel(name = "局站名称")
    private String station;

    /**
     * 5gr站址编码
     */
    private String stationcode5gr;

    /**
     * 日期【yyyy年MM月dd日】
     */
    @Excel(name = "日期")
    private String rq;

    /**
     * 用电量
     */
    @Excel(name = "用电量")
    private String ywdl;
}
