package com.sccl.modules.business.syncresult.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;


/**
 * 同步结果表 syncresult
 * 
 * <AUTHOR>
 * @date 2023-02-27
 */
public class Syncresult extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
    /** 标题 */
    private String title;
    /** 同步类型  1计量  2抄表  3采集 */
    private Integer method;
    /** 成功/失败 */
    private String result;
    /** 操作人员 */
    private String operName;
    /** 明细 */
    private String msg;
	/**
	 * 失败原因
	 */
	private String failMsg;
	/** 明细数量 */
    private Integer num;
    /** 操作时间 */
    private Date operTime;

	public String getFailMsg() {
		return failMsg;
	}

	public void setFailMsg(String failMsg) {
		this.failMsg = failMsg;
	}

	public void setTitle(String title)
	{
		this.title = title;
	}

	public String getTitle() 
	{
		return title;
	}

	public void setMethod(Integer method)
	{
		this.method = method;
	}

	public Integer getMethod() 
	{
		return method;
	}

	public void setResult(String result)
	{
		this.result = result;
	}

	public String getResult() 
	{
		return result;
	}

	public void setOperName(String operName)
	{
		this.operName = operName;
	}

	public String getOperName() 
	{
		return operName;
	}


	public void setMsg(String msg)
	{
		this.msg = msg;
	}

	public String getMsg() 
	{
		return msg;
	}

	public void setNum(Integer num)
	{
		this.num = num;
	}

	public Integer getNum() 
	{
		return num;
	}

	public void setOperTime(Date operTime)
	{
		this.operTime = operTime;
	}

	public Date getOperTime() 
	{
		return operTime;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("title", getTitle())
            .append("method", getMethod())
            .append("result", getResult())
            .append("operName", getOperName())
            .append("delFlag", getDelFlag())
            .append("msg", getMsg())
            .append("num", getNum())
            .append("operTime", getOperTime())
            .toString();
    }
}
