package com.sccl.modules.business.statistical.tower;

import com.sccl.modules.business.statistical.framework.AbstractStatisticalIndexGroupHandler;

/**
 * 铁塔账单稽核结果统计指标类
 *
 * <AUTHOR>
 * @Date 2022/10/31 16:00
 * @Email <EMAIL>
 */
public class TowerAuditResultStatisticalIndexGroupHandler extends AbstractStatisticalIndexGroupHandler {
    private final Long company;
    private final Long country;

    public TowerAuditResultStatisticalIndexGroupHandler(Long company, Long country) {
        this.company = company;
        this.country = country;
    }

    @Override
    public String getGroupName() {
        return "铁塔账单稽核汇总";
    }

    @Override
    public String getOwnerAs() {
        if (country == null && company == null) {
            return "全省";
        }
        return (company == null ? "" : company) + (country == null ? "" : "_" + country);
    }
}
