package com.sccl.modules.business.stationaudit.pstationempty;

import com.enrising.dcarbon.audit.*;
import com.sccl.common.utils.SpringUtil;
import com.sccl.modules.business.account.domain.Account;
import com.sccl.modules.business.account.mapper.AccountMapper;
import com.sccl.modules.business.auditresult.mapper.AuditResultMapper;
import com.sccl.modules.business.noderesultstatistical.domain.AuditFlag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.util.*;

@Slf4j
public class StationEmptyCreator extends AbstractRefereeDatasourceCreator implements AuditFlag {
    @Override
    public Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> collect(Auditable auditable) {
        //数据源
        Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> datasource = new HashMap<>();

        //从Spring上下文取得mapper
        AccountMapper accountMapper = SpringUtil.getBean(AccountMapper.class);
        AuditResultMapper auditResultMapper = SpringUtil.getBean(AuditResultMapper.class);
        //可能上下文没有这个mapper
        if (accountMapper == null) {
            return null;
        }
        Account account = (Account) auditable;

        //根据 台账主键 获取对应的站址
        Long billId = account.getBillId();
        Long pcid = account.getPcid();

        StationEmptyRefereeContent content_temp = new StationEmptyRefereeContent();
        content_temp = accountMapper.getStationCode(pcid, billId);
        //为空直接结束
        if (content_temp == null) {
            datasource.put(StationEmptyRefereeContent.class, new ArrayList<>());
            return datasource;
        }
        String stationCode = content_temp.getStationCode();

        //创建 评审内容对象
        RefereeResult refereeResult = new RefereeResult("站址不存在", true, "成功");
        StationEmptyRefereeContent content = new StationEmptyRefereeContent(refereeResult, 1,
                                                                            pcid + ""
        );
        content.setBillId(billId);
        content.setPcid(pcid);
        BeanUtils.copyProperties(content_temp, content, "refereeResult", "pcid", "billId", "auditKey", "step");

        //稽核实体
        AuditResult auditResult = content.getAuditResult();

        //评判内容
        String refereeMessage = null;
        if (stationCode == null || stationCode.equals("")) {
            refereeMessage = String.format("台账%d 站址为空", pcid);
        }
        auditResult.setRefereeMessage(refereeMessage);

        //放到数据源
        datasource.put(StationEmptyRefereeContent.class, new ArrayList<>(Arrays.asList(auditResult)));
        return datasource;
    }

    @Override
    public AbstractReferee getReferee() {
        return new StationEmptyReferee("站址缺失");
    }
}
