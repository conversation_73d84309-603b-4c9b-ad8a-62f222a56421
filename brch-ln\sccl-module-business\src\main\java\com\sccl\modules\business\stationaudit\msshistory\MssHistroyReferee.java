package com.sccl.modules.business.stationaudit.msshistory;

import com.enrising.dcarbon.audit.AbstractReferee;
import com.enrising.dcarbon.audit.Auditable;
import com.enrising.dcarbon.audit.RefereeDatasource;
import com.enrising.dcarbon.audit.RefereeResult;
import com.sccl.common.utils.SpringUtil;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;
import com.sccl.modules.mssaccount.mssaccountbill.mapper.MssAccountbillMapper;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
public class MssHistroyReferee extends AbstractReferee {

    public MssHistroyReferee() {
        super("history");
    }

    @Override
    public RefereeResult doReferee(RefereeResult lastRefereeResult, Auditable currentAuditBill, Map<Class<?
            extends RefereeDatasource>, List<RefereeDatasource>> refereeDatasourceList) {

        MssAccountbillMapper mapper = SpringUtil.getBean(MssAccountbillMapper.class);
        //可能上下文没有这个mapper
        if (mapper == null) {
            return null;
        }

        HistoryResult result = new HistoryResult();
        result.setRefereeMessage("过去6月报账历史已出");
        result.setTrue(true);


    //历史 磁盘化
//        LocalDateTime now = LocalDateTime.now();
//        result.setFirstNodeTime(now);
//
        List<RefereeDatasource> list = refereeDatasourceList.get(MssAccountbill.class);
        result.setList(list);
//        List<MssHistory> mssHistories = list.stream().map(
//                refereeDatasource -> {
//                    MssHistory mssHistory = new MssHistory();
//                    mssHistory.setBillId(currentAuditBill.getId());
//                    mssHistory.setAuditTime(now);
//                    mssHistory.setMssId(((MssAccountbill) refereeDatasource).getId());
//                    mssHistory.setDelFlag(0);
//                    return mssHistory;
//                }
//        ).collect(Collectors.toList());
//        int n = mapper.insertListMsshistory(mssHistories);
//        if (n == list.size()) {
//            log.info("{}的报账单 过去6月报账历史 已保存在数据库");
//        } else {
//            log.info("{}的报账单 过去6月报账历史 已保存在数据库,但条数不对，请注意核查");
//        }

        return result;
    }
}
