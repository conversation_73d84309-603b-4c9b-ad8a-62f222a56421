package com.sccl.modules.mssaccount.dataanalysis.controller;

import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.mssaccount.dataanalysis.dto.GetByOrgIdDTO;
import com.sccl.modules.mssaccount.dataanalysis.service.StatisticalAnalysisService;
import com.sccl.modules.mssaccount.dataanalysis.vo.ElectricityBillResultVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 报账统计分析
 */
@Slf4j
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RequestMapping(value = "/statisticalAnalysis")
public class StatisticianController extends BaseController {
    // 全省(1-12月，支持单个月查询)报账电费、电量、电价同比/环比对比分析+累积；展示方式：数据表+统计图
    // 各市州(1-12月，支持单个月查询)报账电费、电量、电价同比/环比对比分析+累积；展示方式：数据表+统计图
    private final StatisticalAnalysisService statisticalAnalysisService;

    /**
     * 统计某个市州或全省
     */
    @PostMapping("/byOrgId")
    public AjaxResult getByOrgId(@RequestBody GetByOrgIdDTO dto) {
        List<ElectricityBillResultVO> list = statisticalAnalysisService.getByOrgId(dto);
        return AjaxResult.success(list);
    }

    /**
     * 导出
     */
    @PostMapping("/export")
    public void export(@RequestBody GetByOrgIdDTO dto, HttpServletResponse response) {
        statisticalAnalysisService.export(response, dto);
    }
}
