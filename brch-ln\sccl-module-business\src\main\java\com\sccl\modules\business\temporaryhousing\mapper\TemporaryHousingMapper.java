package com.sccl.modules.business.temporaryhousing.mapper;

import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.temporaryhousing.domain.TemporaryHousing;

import java.util.Map;

/**
 * __房屋临时表mapper__<br/>
 * 2019/10/31
 *
 * <AUTHOR>
 */
public interface TemporaryHousingMapper extends BaseMapper<TemporaryHousing> {

    /**
     * @Description: 删除全部数据
     * @author: dongk
     * @date: 2019/10/23
     * @param:
     * @return:
     */
    int deleteAll();

    /**
     * @Description: 加入正式铁塔表
     * @author: dongk
     * @date: 2019/10/24
     * @param:
     * @return:
     */
    int initHousingInfo();

    /**
     * @Description: 生成局站
     * @author: dongk
     * @date: 2019/10/31
     * @param:
     * @return:
     */
    void importHousing(Map<String,Object> map);

    /**
     * @Description: 删除重复数据
     * @author: dongk
     * @date: 2019/11/6
     * @param:
     * @return:
     */
    int deleteRepeat();

    /**
     * @Description: 生成局站
     * @author: dongk
     * @date: 2019/11/6
     * @param:
     * @return:
     */
    int insertHousingInfo();

}
