package com.sccl.modules.business.oilexpense.mapper;

import com.sccl.modules.business.energyaccount.domain.EnergyAccountVo;
import com.sccl.modules.business.oilexpense.domain.OilExpense;
import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.oilexpense.domain.OilExpenseRecord;
import com.sccl.modules.business.oilexpense.domain.OilExpenseVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 油机基础 数据层
 *
 * <AUTHOR>
 * @date 2021-12-14
 */
public interface OilExpenseMapper extends BaseMapper<OilExpense> {

    // 保存油机&局站关联表
    void saveMachineAndStation(@Param("stationId") Long stationId, @Param("machineId") Long machineId);

    List<String> queryMachineAndStation(@Param("machineId") Long machineId, @Param("type") Integer type);

    void updateMachineAndStation(@Param("machineId") Long machineId);

    OilExpense selectById(String id);

    OilExpense selectByOilEngineId(@Param("oilEngineId") String oilEngineId);

    OilExpense selectByCarPlate(String carplate);

    /*根据油机编号查询(除开当前项)*/
    OilExpense selectByOilExpenseIdNoMe(@Param("id")Long id,@Param("oilEngineId") String oilEngineId);


    List<OilExpense> selectListView(OilExpense oilExpense);

    List<OilExpenseVo> selectListVo(OilExpense oilExpense);

    List<OilExpenseVo> selectListAuditVo(OilExpenseVo oilExpense);

    List<OilExpenseVo> selectListHavedAuditVo(OilExpenseVo oilExpense);

    List<EnergyAccountVo> listAccountDetail(OilExpenseVo oilExpense);

    List<OilExpenseVo> selectListAuditVo4(OilExpenseVo oilExpense);

    List<OilExpenseVo> selectListAll(OilExpense oilExpense);

    void insertRecord(OilExpenseRecord oilExpenseRecord);

    /** 查询新数据 */
    OilExpenseRecord selectNewOilExpense(Long id);

    void delNewOilExpense(Long id);

    List<OilExpenseRecord> selectListViewOld(OilExpenseRecord oilExpense);
}
