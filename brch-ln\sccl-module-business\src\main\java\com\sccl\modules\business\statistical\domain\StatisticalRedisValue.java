package com.sccl.modules.business.statistical.domain;

import com.sccl.common.lang.StringUtils;
import lombok.Data;

/**
 * 统计指标Redis存储类型
 *
 * <AUTHOR>
 * @Date 2022/10/25 17:29
 */
@Data
public class StatisticalRedisValue {
    public static final String PREFIX = "STATISTICAL";

    private static final String BUCKET_NAME = "BUCKET";

    private static final String PARENT_BUCKET = "P-BUCKET";
    /**
     * 统计组别名
     */
    private String groupAlias;
    /**
     * 统计项标题
     */
    private String title;
    /**
     * 统计项内容
     */
    private String content;
    /**
     * 内容类型
     */
    private String contentType;
    /**
     * 统计时间
     */
    private String statisticalTime;
    /**
     * 所属
     */
    private String ownerAs;

    /**
     * 获取Redis key，包含所属、组别名、统计标题、统计时间
     */
    public String getKey() {
        if (StringUtils.isEmpty(ownerAs) || StringUtils.isEmpty(groupAlias) || StringUtils.isEmpty(title) || StringUtils.isEmpty(statisticalTime)) {
            throw new IllegalArgumentException("字段缺失：所属、组别名、统计标题、统计时间");
        }
        return String.format("%s:%s:%s:%s_%s", PREFIX, ownerAs, groupAlias, title, statisticalTime.replace(" ", "_"));
    }

    public String getKeysBucketName() {
        if (StringUtils.isEmpty(ownerAs) || StringUtils.isEmpty(groupAlias)) {
            throw new IllegalArgumentException("字段缺失：所属、组别名");
        }
        return String.format("%s:%s:%s:%s", PREFIX, BUCKET_NAME, ownerAs, groupAlias);
    }


    /**
     * 获取模糊匹配key
     */
    public String getPattern() {
        //System.out.println("pattern=" + pattern);
        return String.format("%s:%s:%s:%s_%s", PREFIX, replaceNull(ownerAs), replaceNull(groupAlias), replaceNull(title), replaceNull(statisticalTime).replace(" ", "_"));
    }

    public static String getParentBucketName() {
        return String.format("%s:%s", PREFIX, PARENT_BUCKET);
    }

    private String replaceNull(String value) {
        return StringUtils.isEmpty(value) ? ".*" : value;
    }
}
