package com.sccl.modules.business.statistical.account.domain;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 单价排名对象
 *
 * <AUTHOR>
 * @date 2022-12-05 12:11
 * @email <EMAIL>
 */
@Getter
@Setter
@ToString
public class UnitPriceRank{
    private String title;
    /**
     * 电表ID
     */
    private Long ammeterId;
    /**
     * 台账ID
     */
    private Long accountId;
    /**
     * 电表户号
     */
    private String ammeterCode;
    /**
     * 所属分公司
     */
    private String companyName;
    /**
     * 所属部门
     */
    private String countryName;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 用电类型
     */
    private String electrotype;
    /**
     * 父级用电类型
     */
    private String parentElectrotype;
    /**
     * 单价
     */
    private BigDecimal unitPrice;
    /**
     * 全省平均单价
     */
    private BigDecimal provinceAverageUnitPrice;
}
