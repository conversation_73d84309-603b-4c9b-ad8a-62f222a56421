package com.sccl.modules.business.stationequipment.domain;

import com.sccl.modules.autojob.util.convert.StringUtils;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;

@Data
public class AuditOverdue {
    /**
     * 组织编码
     */
    private String orgCode;
    private String orgCodeName;
    /**
     * 部门编码
     */
    private String country;
    private String countryName;
    /**
     * 电表局站id
     */
    private String stationcode;
    private String stationName;
    private String projectname;
    private String ammeterCode;
    private String maxenddate;
private String property;
    private Long pcid;
private String directsupplyflag;
private String electrotypename;
    private String pcidmap;

    public static void processPcid(ArrayList<AuditOverdue> auditOverdues) {
        auditOverdues.forEach(
                item -> {
                    ProcessPcidmap(item);
                }
        );
    }

    private static void ProcessPcidmap(AuditOverdue auditOverdue) {
        String map = auditOverdue.getPcidmap();
        if (StringUtils.isBlank(map)) {
            return;
        }
        String pcid = Arrays.stream(map.split(","))
                .map(item -> item.split("/"))
                .filter(item -> item.length == 2 && item[0].length() == 8)
                .max(Comparator.comparing(key -> Integer.parseInt(key[0])))
                .map(item -> item[1])
                .orElse("000");
        auditOverdue.setPcid(Long.valueOf(pcid));
    }
}
