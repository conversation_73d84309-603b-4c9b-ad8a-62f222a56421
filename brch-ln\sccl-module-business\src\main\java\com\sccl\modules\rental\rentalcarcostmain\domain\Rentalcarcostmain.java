package com.sccl.modules.rental.rentalcarcostmain.domain;

import com.sccl.modules.rental.rentalcarcost.domain.Rentalcarcost;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;


/**
 * 租赁费用主表 rentalcarcostmain
 * 
 * <AUTHOR>
 * @date 2019-08-29
 */
public class Rentalcarcostmain extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
    /** 申请名称 */
    private String setitle;
    /** 申请人id */
    private Long inputuserid;
    /** 申请时间 */
    private Date inputdate;
    /** 申请人名字 */
    private String inputusername;
    /**  */
    private Long iprocessinstid;
    /** 状态 */
    private String status;
    /** 分公司 */
    private Long company;
    /** 部门 */
    private Long country;
    /** 备注 */
    private String memo;
    /** 费用产生年 */
    private String year;
    /** 费用产生季度 */
    private String quarter;

    /** 详细信息 */
    private List<Rentalcarcost> rentalcarcostList;


	private String companyName;
	private String countryName;
	private String statusName;
	private Date inputdateStart;
	private Date inputdateEnd;

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public String getCountryName() {
		return countryName;
	}

	public void setCountryName(String countryName) {
		this.countryName = countryName;
	}

	public String getStatusName() {
		return statusName;
	}

	public void setStatusName(String statusName) {
		this.statusName = statusName;
	}

	public Date getInputdateStart() {
		return inputdateStart;
	}

	public void setInputdateStart(Date inputdateStart) {
		this.inputdateStart = inputdateStart;
	}

	public Date getInputdateEnd() {
		return inputdateEnd;
	}

	public void setInputdateEnd(Date inputdateEnd) {
		this.inputdateEnd = inputdateEnd;
	}

	public List<Rentalcarcost> getRentalcarcostList() {
		return rentalcarcostList;
	}

	public void setRentalcarcostList(List<Rentalcarcost> rentalcarcostList) {
		this.rentalcarcostList = rentalcarcostList;
	}

	public void setSetitle(String setitle)
	{
		this.setitle = setitle;
	}

	public String getSetitle() 
	{
		return setitle;
	}

	public void setInputuserid(Long inputuserid)
	{
		this.inputuserid = inputuserid;
	}

	public Long getInputuserid()
	{
		return inputuserid;
	}

	public void setInputdate(Date inputdate)
	{
		this.inputdate = inputdate;
	}

	public Date getInputdate() 
	{
		return inputdate;
	}

	public void setInputusername(String inputusername)
	{
		this.inputusername = inputusername;
	}

	public String getInputusername() 
	{
		return inputusername;
	}

	public void setIprocessinstid(Long iprocessinstid)
	{
		this.iprocessinstid = iprocessinstid;
	}

	public Long getIprocessinstid()
	{
		return iprocessinstid;
	}

	public void setStatus(String status)
	{
		this.status = status;
	}

	public String getStatus() 
	{
		return status;
	}

	public void setCompany(Long company)
	{
		this.company = company;
	}

	public Long getCompany()
	{
		return company;
	}

	public void setCountry(Long country)
	{
		this.country = country;
	}

	public Long getCountry()
	{
		return country;
	}

	public void setMemo(String memo)
	{
		this.memo = memo;
	}

	public String getMemo() 
	{
		return memo;
	}

	public void setYear(String year)
	{
		this.year = year;
	}

	public String getYear() 
	{
		return year;
	}

	public void setQuarter(String quarter)
	{
		this.quarter = quarter;
	}

	public String getQuarter() 
	{
		return quarter;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("setitle", getSetitle())
            .append("inputuserid", getInputuserid())
            .append("inputdate", getInputdate())
            .append("inputusername", getInputusername())
            .append("iprocessinstid", getIprocessinstid())
            .append("status", getStatus())
            .append("company", getCompany())
            .append("country", getCountry())
            .append("memo", getMemo())
            .append("year", getYear())
            .append("quarter", getQuarter())
            .toString();
    }
}
