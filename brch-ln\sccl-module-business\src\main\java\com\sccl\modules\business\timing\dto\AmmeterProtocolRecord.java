package com.sccl.modules.business.timing.dto;

import com.sccl.timing.finder.framework.CacheData;
import com.sccl.timing.finder.framework.TimeSeriesData;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-06-14 9:49
 * @email <EMAIL>
 */
@Getter
@Setter
public class AmmeterProtocolRecord implements TimeSeriesData {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 电表ID
     */
    private Long ammeterProtocolId;

    /**
     * 用电类型
     */
    private Long electrotype;

    /**
     * 对外结算类型
     */
    private Integer directsupplyflag;

    /**
     * 创建时间
     */
    private Date createTime;


    @Override
    public long id() {
        return id;
    }

    @Override
    public long timestamp() {
        if (createTime != null) {
            return createTime.getTime();
        }
        return 0;
    }

    @Override
    public String key() {
        return ammeterProtocolId + "";
    }

    @Override
    public CacheData storeValue() {
        return new AmmeterProtocolRecordCacheData(electrotype, directsupplyflag, createTime);
    }
}
