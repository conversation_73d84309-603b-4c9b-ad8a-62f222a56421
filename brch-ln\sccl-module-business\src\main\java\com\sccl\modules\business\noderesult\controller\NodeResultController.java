package com.sccl.modules.business.noderesult.controller;

import com.alibaba.fastjson.JSONObject;
import com.sccl.common.support.Convert;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.noderesult.domain.NodeResult;
import com.sccl.modules.business.noderesult.service.INodeResultService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 基站一站式稽核结果 信息操作处理
 *
 * <AUTHOR>
 * @date 2022-11-22
 */
@RestController
@RequestMapping("/business/nodeResult")
public class NodeResultController extends BaseController {
    private String prefix = "business/nodeResult";

    @Autowired
    private INodeResultService nodeResultService;

    @RequiresPermissions("business:nodeResult:view")
    @GetMapping()
    public String nodeResult() {
        return prefix + "/nodeResult";
    }

    /**
     * 查询基站一站式稽核结果列表
     */
    @RequiresPermissions("business:nodeResult:list")
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(NodeResult nodeResult) {
        startPage();
        List<NodeResult> list = nodeResultService.selectList(nodeResult);
        return getDataTable(list);
    }

    /**
     * 新增基站一站式稽核结果
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存基站一站式稽核结果
     */
    @RequiresPermissions("business:nodeResult:add")
    @Log(title = "基站一站式稽核结果", action = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody NodeResult nodeResult) {
        return toAjax(nodeResultService.insert(nodeResult));
    }

    /**
     * 修改基站一站式稽核结果
     */
    @GetMapping("/edit/{id}")
    public AjaxResult edit(@PathVariable("id") Long id) {
        NodeResult nodeResult = nodeResultService.get(id);

        Object object = JSONObject.toJSON(nodeResult);

        return this.success(object);
    }

    /**
     * 修改保存基站一站式稽核结果
     */
    @RequiresPermissions("business:nodeResult:edit")
    @Log(title = "基站一站式稽核结果", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody NodeResult nodeResult) {
        return toAjax(nodeResultService.update(nodeResult));
    }

    /**
     * 删除基站一站式稽核结果
     */
    @RequiresPermissions("business:nodeResult:remove")
    @Log(title = "基站一站式稽核结果", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(nodeResultService.deleteByIds(Convert.toStrArray(ids)));
    }


    /**
     * 查看基站一站式稽核结果
     */
    @RequiresPermissions("business:nodeResult:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id) {
        NodeResult nodeResult = nodeResultService.get(id);

        Object object = JSONObject.toJSON(nodeResult);

        return this.success(object);
    }

    @PostMapping("/getListByIds")
    @ResponseBody
    public AjaxResult ListByIds(@RequestBody List<Long> ids) {
        List<NodeResult> nodeResults = nodeResultService.get(ids);

        Object object = JSONObject.toJSON(nodeResults);

        return this.success(object);
    }


}
