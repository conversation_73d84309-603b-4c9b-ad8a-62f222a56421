package com.sccl.modules.mssaccount.msscostcenter.controller;

import com.alibaba.fastjson.JSONObject;
import com.sccl.common.support.Convert;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.mssaccount.msscostcenter.domain.MssCostcenter;
import com.sccl.modules.mssaccount.msscostcenter.service.IMssCostcenterService;
import com.sccl.modules.system.user.domain.User;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 记账成本中心 信息操作处理
 * 
 * <AUTHOR>
 * @date 2019-05-01
 */
@RestController
@RequestMapping("/mssaccount/mssCostcenter")
public class MssCostcenterController extends BaseController
{
    private String prefix = "mssaccount/mssCostcenter";
	
	@Autowired
	private IMssCostcenterService mssCostcenterService;
	
	@RequiresPermissions("mssaccount:mssCostcenter:view")
	@GetMapping()
	public String mssCostcenter()
	{
	    return prefix + "/mssCostcenter";
	}
	
	/**
	 * 查询记账成本中心列表
	 */
	@RequiresPermissions("mssaccount:mssCostcenter:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(MssCostcenter mssCostcenter)
	{
		//本单位下所有成本中心 session 中获得
//		mssCostcenter.setBukrs("A006");//测试
//		if (StringUtils.isBlank(mssCostcenter.getBukrs())) {
//			throw new RuntimeException(" -->请在报账基本信息指定:公司代码");
//		}

		User user = this.getCurrentUser();
		List<IdNameVO> companies = user.getCompanies();
		mssCostcenter.setRemark10(companies.get(0).getId());// 用 Remark10 来代替companies
		startPage();
        List<MssCostcenter> list = mssCostcenterService.selectByLikeAuto(mssCostcenter);
		return getDataTable(list);
	}
	
	/**
	 * 新增记账成本中心
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存记账成本中心
	 */
	@RequiresPermissions("mssaccount:mssCostcenter:add")
	//@Log(title = "记账成本中心", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody MssCostcenter mssCostcenter)
	{		
		return toAjax(mssCostcenterService.insert(mssCostcenter));
	}

	/**
	 * 修改记账成本中心
	 */
	@GetMapping("/edit/{costid}")
	public AjaxResult edit(@PathVariable("costid") Long costid)
	{
		MssCostcenter mssCostcenter = mssCostcenterService.get(costid);

		Object object = JSONObject.toJSON(mssCostcenter);

        return this.success(object);
	}
	
	/**
	 * 修改保存记账成本中心
	 */
	@RequiresPermissions("mssaccount:mssCostcenter:edit")
	//@Log(title = "记账成本中心", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody MssCostcenter mssCostcenter)
	{		
		return toAjax(mssCostcenterService.update(mssCostcenter));
	}
	
	/**
	 * 删除记账成本中心
	 */
	@RequiresPermissions("mssaccount:mssCostcenter:remove")
	//@Log(title = "记账成本中心", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(mssCostcenterService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看记账成本中心
     */
    @RequiresPermissions("mssaccount:mssCostcenter:view")
    @GetMapping("/view/{costid}")
    @ResponseBody
    public AjaxResult view(@PathVariable("costid") Long costid)
    {
		MssCostcenter mssCostcenter = mssCostcenterService.get(costid);

        Object object = JSONObject.toJSON(mssCostcenter);

        return this.success(object);
    }

}
