package com.sccl.modules.business.stationaudit.demo;

import com.enrising.dcarbon.audit.*;
import com.sccl.modules.autojob.util.id.SystemClock;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 演示稽核，仅做演示，不能运行
 *
 * <AUTHOR>
 * @Date 2022/11/11 11:32
 * @Email <EMAIL>
 */
@Slf4j
public class DemoAudit {
    public static void main(String[] args) {
        long start = SystemClock.now();
        AuditContext context =  multiChainAndMultiThreadContext();
        final int auditCount = 10;
        context.addProgressListener(progress -> log.info("{}", progress));
        List<MssAccountbill> waitAuditBills = new ArrayList<>();
        for (int i = 0; i < auditCount; i++) {
            MssAccountbill mssAccountbill = new MssAccountbill();
            mssAccountbill.setId((long) i);
            waitAuditBills.add(mssAccountbill);
        }

        context.addProgressListener(progress -> {
            if (progress.getFinishedPercent() == 100) {
                log.info("已处理完");
            }
        });


        Map<String, List<RefereeResult>> resultMap = new HashMap<>();

        /*=================同步稽核=================>*/
        //Map<Long, List<RefereeResult>> finalResultMap = resultMap;
        //waitAuditBills.forEach(bill -> {
        //    context.startAudit(bill);
        //    finalResultMap.put(bill.getId(), chain.collectResults());
        //});


        /*=======================Finished======================<*/

        /*=================异步稽核=================>*/
        //直接提交到上下文
        waitAuditBills.forEach(context::submit);
        log.info("提交完成");

//
//        //...可以处理其他事情
//
//        //过段时间后可以获取稽核结果
//        resultMap = context.getResultsMap();
//
        //也可阻塞等待最终完成后获取
        resultMap = context.getResultsMapSync();
        log.info("稽核完成，用时：{}ms", SystemClock.now() - start);
        resultMap
                .values()
                .forEach(System.out::println);

        /*=======================Finished======================<*/


    }

    /**
     * 单链单线程上下文，上下文只有一个评判链，只有一个异步线程
     *
     * @return com.sccl.modules.business.stationaudit.framework.StationAuditContext
     * <AUTHOR> Yongxiang
     * @date 2022/11/25 11:10
     */
    private static AuditContext singleChainContext() {
        //创建评判链
        RefereeChain chain = RefereeChain
                .builder()
                .addRefereeNode(new RefereeNode(new DemoCreator()))
                .build();
        return new AuditContext(chain);
    }

    /**
     * 多链单线程上下文，上下文使用评判链池，只有一个异步线程
     *
     * @return com.sccl.modules.business.stationaudit.framework.StationAuditContext
     * <AUTHOR> Yongxiang
     * @date 2022/11/25 11:10
     */
    private static AuditContext multiChainContext() {
        //创建评判链
        RefereeChain chain = RefereeChain
                .builder()
                .addRefereeNode(new RefereeNode(new DemoCreator()))
                .build();
        //创建评判链池
        RefereeChainPool chainPool = RefereeChainPool
                .builder(chain)
                .setPoolSize(5)
                .setMinIdle(3)
                .setGetChainTimeout(3,TimeUnit.SECONDS)
                .setKeepAliveTime(60, TimeUnit.SECONDS)
                .build();
        //创建稽核线程池
        ThreadPoolExecutor poolExecutor = new ThreadPoolExecutor(1, 1, 60, TimeUnit.SECONDS, new LinkedBlockingQueue<>(Integer.MAX_VALUE));
        return new AuditContext(chainPool, poolExecutor, 5000);
    }

    /**
     * 多链多线程上下文，上下文有多个评判链和多个异步线程
     *
     * @return com.sccl.modules.business.stationaudit.framework.StationAuditContext
     * <AUTHOR> Yongxiang
     * @date 2022/11/25 11:11
     */
    private static AuditContext multiChainAndMultiThreadContext() {
        //创建评判链
        RefereeChain chain = RefereeChain
                .builder()
                .addRefereeNode(new RefereeNode(new DemoCreator()))
                .build();
        //创建评判链池
        RefereeChainPool chainPool = RefereeChainPool
                .builder(chain)
                .setPoolSize(5)
                .setMinIdle(3)
                .setGetChainTimeout(3,TimeUnit.SECONDS)
                .setKeepAliveTime(60, TimeUnit.SECONDS)
                .build();
        //创建稽核线程池
        ThreadPoolExecutor poolExecutor = new ThreadPoolExecutor(5, 5, 60, TimeUnit.SECONDS, new LinkedBlockingQueue<>(Integer.MAX_VALUE));
        return new AuditContext(chainPool, poolExecutor, 5000);
    }


}
