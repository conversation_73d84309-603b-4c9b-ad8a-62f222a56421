package com.sccl.modules.business.oilreimbursement.service.imp;

import com.sccl.modules.business.energyaccountbillpre.domain.EnergyAccountbillitempre;
import com.sccl.modules.business.energyaccountbillpre.domain.EnergyAccountbillpre;
import com.sccl.modules.business.energyaccountbillpre.mapper.EnergyAccountbillpreMapper;
import com.sccl.modules.business.oilcardaccount.domain.OilCardAccount;
import com.sccl.modules.business.oilcardaccount.mapper.OilCardAccountMapper;
import com.sccl.modules.business.oilreimbursement.service.OilReimbursementService;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;
import com.sccl.modules.mssaccount.mssaccountbill.mapper.MssAccountbillMapper;
import com.sccl.modules.mssaccount.mssaccountbillitem.domain.MssAccountbillitem;
import com.sccl.modules.mssaccount.mssaccountbillitem.mapper.MssAccountbillitemMapper;
import com.sccl.modules.mssaccount.rbillitemaccount.domain.RBillitemAccount;
import com.sccl.modules.mssaccount.rbillitemaccount.mapper.RBillitemAccountMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.LinkedList;
import java.util.List;

/**
 * @Description
 * @Auther Huang Yongxiang
 * @Date 2021/12/21 10:47
 */
@Service
public class OilReimbursementServiceImp implements OilReimbursementService {
    @Autowired(required = false)
    EnergyAccountbillpreMapper energyAccountbillpreMapper;
    @Autowired(required = false)
    OilCardAccountMapper accountMapper;
    @Autowired(required = false)
    MssAccountbillMapper mssAccountbillMapper;
    @Autowired(required = false)
    MssAccountbillitemMapper mssAccountbillitemMapper;
    @Autowired(required = false)
    RBillitemAccountMapper rBillitemAccountMapper;
    private static final Logger logger = LoggerFactory.getLogger(OilReimbursementServiceImp.class);


    @Override
    public List<OilCardAccount> getAccountsOfEnergyAccountBillpre(Long pabrid) {
        EnergyAccountbillitempre energyAccountbillitempre = new EnergyAccountbillitempre();
        energyAccountbillitempre.setPabriid(pabrid);
        List<EnergyAccountbillitempre> energyAccountbillitempres = energyAccountbillpreMapper.selectPcid(energyAccountbillitempre);
        List<Long> ids = new LinkedList<>();
        for (EnergyAccountbillitempre e : energyAccountbillitempres) {
            ids.add(e.getPcid());
        }
        return accountMapper.selectAllById(ids);
    }

    @Override
    public int insertNewMssAccountBill(MssAccountbill mssAccountbill) {
        if (mssAccountbill == null || mssAccountbill.getId() == null) {
            logger.error("插入报账单基础信息失败，参数为null");
            return 0;
        }
        return mssAccountbillMapper.insertWithoutAutoId(mssAccountbill);
    }

    @Override
    public int associateMssAccount(EnergyAccountbillpre energyAccountbillpre) {
        if (energyAccountbillpre == null) {
            logger.error("关联报账基本消息表到归集单失败，参数为null");
            return 0;
        }
        return energyAccountbillpreMapper.updatePabid(energyAccountbillpre);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createNewMssAccountBill(EnergyAccountbillpre energyAccountbillmpre, MssAccountbill mssAccountbill) {
        String[] ids = new String[]{String.valueOf(mssAccountbill.getId())};
        List<MssAccountbill> mssAccountbills = mssAccountbillMapper.selectListByIds(ids);
        int inCount = 0;
        //如果之前没有生成过基础信息就直接插入
        if (mssAccountbills == null || mssAccountbills.size() == 0) {
            inCount = insertNewMssAccountBill(mssAccountbill);
        }
        //否则就更新
        else {
            inCount = mssAccountbillMapper.updateForModel(mssAccountbill);
        }
        int asCount = associateMssAccount(energyAccountbillmpre);
        if (inCount == 0 || asCount == 0) {
            throw new RuntimeException("新建报账单基础信息事务失败，事务回滚...");
        }
        return inCount > 0 && asCount > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createNewMssAccountIteBill(MssAccountbillitem mssAccountbillitem, List<RBillitemAccount> rBillitemAccounts) {
        int msCount = insertNewMssAccountIteBill(mssAccountbillitem);
        int rbCount = insertNewRBillitemAccounts(rBillitemAccounts);
        if (msCount == 0 || rbCount == 0) {
            throw new RuntimeException("新建报账单明细信息事务失败，事务回滚...");
        }
        return msCount > 0 && rbCount > 0;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMssAccountIteBill(List<MssAccountbillitem> mssAccountbillitem,  List<RBillitemAccount> rBillitemAccounts) {
        if (mssAccountbillitem == null || rBillitemAccounts == null) {
            return false;
        }
        String[] msIds = new String[mssAccountbillitem.size()];
        String[] rbIds = new String[rBillitemAccounts.size()];
        int i = 0;
        for (RBillitemAccount rb : rBillitemAccounts) {
            rbIds[i++] = rb.getId() + "";
        }
        i=0;
        for (MssAccountbillitem ms : mssAccountbillitem) {
            msIds[i++] = ms.getId() + "";
        }
        mssAccountbillitemMapper.deleteByIds(msIds);
        rBillitemAccountMapper.deleteByIds(rbIds);
        return true;
    }

    @Override
    public int updateMssAccountMoney(MssAccountbill mssAccountbill) {
        if (mssAccountbill == null || mssAccountbill.getId() == null || mssAccountbill.getSum() == null || mssAccountbill.getInputTaxSum() == null) {
            logger.error("更新金额失败，参数为null");
            return 0;
        }
        return mssAccountbillMapper.updateForModel(mssAccountbill);
    }

    @Override
    public int insertNewMssAccountIteBill(MssAccountbillitem mssAccountbillitem) {
        if (mssAccountbillitem == null) {
            logger.error("插入报账单明细失败，参数为null");
            return 0;
        }
        List<MssAccountbillitem> mssAccountbills = new LinkedList<>();
        mssAccountbills.add(mssAccountbillitem);
        return mssAccountbillitemMapper.insertList(mssAccountbills);
    }

    @Override
    public int saveMessAccountIteBill(MssAccountbillitem mssAccountbillitem) {
        if (mssAccountbillitem == null) {
            logger.error("更新报账单明细失败，参数为null");
            return 0;
        }
        return mssAccountbillitemMapper.updateByPrimaryKey(mssAccountbillitem);
    }

    @Override
    public int insertNewRBillitemAccounts(List<RBillitemAccount> rBillitemAccounts) {
        if (rBillitemAccounts == null) {
            logger.error("插入报账单&台账关联信息失败，参数为null");
            return 0;
        }
        return rBillitemAccountMapper.insertList(rBillitemAccounts);
    }

    @Override
    public List<RBillitemAccount> getRBillAccount(String[] billIds) {
        if (billIds == null || billIds.length == 0) {
            logger.error("查询报账明细-台账关联信息失败，参数为null");
            return null;
        }
        return rBillitemAccountMapper.selectListByBillId(billIds);
    }

    @Override
    public List<MssAccountbillitem> getMssAcountBillItem(String[] ids) {
        if (ids == null || ids.length == 0) {
            logger.error("查询报账明细信息失败，参数为null");
            return null;
        }
        return mssAccountbillitemMapper.selectListByIds(ids);
    }
}
