package com.sccl.modules.rental.rentalcar.mapper;

import com.sccl.modules.rental.rentalcar.domain.Rentalcar;
import com.sccl.framework.mapper.BaseMapper;

import java.util.List;

/**
 * 车辆  数据层
 * 
 * <AUTHOR>
 * @date 2019-08-23
 */
public interface RentalcarMapper extends BaseMapper<Rentalcar>
{


    void deleteByRcmids(String[] toStrArray);

    List<Rentalcar> selectAndNameByRcmid(Long rcmid);

    /**
     * @Description: 根据车牌号查月租金
     * @author: dongk
     * @date: 2019/9/4
     * @param:
     * @return:
     */
    Rentalcar selectByVin(Rentalcar vin);
}