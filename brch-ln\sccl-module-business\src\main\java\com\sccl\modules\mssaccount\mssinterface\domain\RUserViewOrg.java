package com.sccl.modules.mssaccount.mssinterface.domain;

import com.sccl.framework.web.domain.BaseEntity;

public class RUserViewOrg extends BaseEntity {
    private String hrLoginId;
    private String orgId;

    public RUserViewOrg(String hrLoginId, String orgId) {
        this.hrLoginId = hrLoginId;
        this.orgId = orgId;
    }

    public String getHrLoginId() {
        return hrLoginId;
    }

    public void setHrLoginId(String hrLoginId) {
        this.hrLoginId = hrLoginId;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    @Override
    public String toString() {
        return "RUserViewOrg{" +
                "hrLoginId='" + hrLoginId + '\'' +
                ", orgId='" + orgId + '\'' +
                '}';
    }
}
