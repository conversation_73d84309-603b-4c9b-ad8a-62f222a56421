package com.sccl.modules.business.jhanomalydetails.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.sccl.framework.aspectj.lang.annotation.Excel;
import lombok.Data;

@Data
public class JhAloneErrorDTO {

    /**
     * 电表户名/协议编码
     */
    @Excel(name = "电表户名/协议编码_tz_bz")
    private String dbhm;

    /**
     * 台账期号
     */
    @TableField("tzqh")
    @Excel(name = "台账期号_tz_bz")
    private String tzqh;

    /**
     * 集团站址编码
     */
    @TableField("jtzzbm")
    @Excel(name = "集团站址编码_tz_bz")
    private String jtzzbm;

    /**
     * 铁塔站址编码
     */
    @TableField("ttzzbm")
    @Excel(name = "铁塔站址编码_tz_bz")
    private String ttzzbm;


    /**
     * 起租单维护数
     */
    @TableField("qzdwhs")
    @Excel(name = "起租单维护数_tz_bz")
    private String qzdwhs;


    /**
     * 电信比例
     */
    @TableField("dxbl")
    @Excel(name = "电信比例_tz_bz")
    private String dxbl;
}
