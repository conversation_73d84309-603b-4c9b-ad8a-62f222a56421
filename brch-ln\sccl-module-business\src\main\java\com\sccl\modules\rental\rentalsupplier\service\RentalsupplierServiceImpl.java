package com.sccl.modules.rental.rentalsupplier.service;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.rental.rentalsupplier.mapper.RentalsupplierMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sccl.modules.rental.rentalsupplier.domain.Rentalsupplier;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 供应商 服务层实现
 * 
 * <AUTHOR>
 * @date 2019-08-23
 */
@Service
public class RentalsupplierServiceImpl extends BaseServiceImpl<Rentalsupplier> implements IRentalsupplierService
{

    @Autowired
    RentalsupplierMapper rentalsupplierMapper;

    @Override
    public int batchInsert(List<Rentalsupplier> list) {
        int num = 0;
        List<Rentalsupplier> addlist = new ArrayList<>();
        if(list != null && list.size() > 0){
            for(Rentalsupplier rentalsupplier : list){
                rentalsupplier.setInputdate(new Date());
                Rentalsupplier supplier = rentalsupplierMapper.selectByLifnr(rentalsupplier.getLifnr());
                if(supplier == null){
                    addlist.add(rentalsupplier);
                }
            }
        }
        if(addlist.size() > 0){
            num = rentalsupplierMapper.insertList(addlist);
        }

        return num;
    }
}
