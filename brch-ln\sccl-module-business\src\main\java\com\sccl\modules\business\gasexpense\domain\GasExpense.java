package com.sccl.modules.business.gasexpense.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;

import java.math.BigDecimal;


/**
 * 气基础表 gas_expense
 *
 * <AUTHOR>
 * @date 2022-01-07
 */
public class GasExpense extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 户号
     */
    private Long gasExpenseId;
    /**
     * 户名
     */
    private String gasExpenseName;
    /**
     * 所属区县
     */
    private Long country;
    /**
     * 分公司
     */
    private Long company;
    /**
     * 分局、支局
     */
    private String substation;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 管理负责人
     */
    private String master;
    /**
     * 单据状态
     */
    private Integer billStatus;
    /**
     * 关联局站编号
     */
    private String stationCode;
    /**
     * 在用1 停用0
     */
    private Integer status;

    // 翻表度数
    private BigDecimal maxdegree;

    // 倍率
    private BigDecimal magnification;

    public void setMaxdegree(BigDecimal maxdegree) {
        this.maxdegree = maxdegree;
    }

    public BigDecimal getMaxdegree() {
        return maxdegree;
    }

    public void setMagnification(BigDecimal magnification) {
        this.magnification = magnification;
    }

    public BigDecimal getMagnification() {
        return magnification;
    }


    public void setGasExpenseId(Long gasExpenseId) {
        this.gasExpenseId = gasExpenseId;
    }

    public Long getGasExpenseId() {
        return gasExpenseId;
    }

    public void setGasExpenseName(String gasExpenseName) {
        this.gasExpenseName = gasExpenseName;
    }

    public String getGasExpenseName() {
        return gasExpenseName;
    }

    public void setCountry(Long country) {
        this.country = country;
    }

    public Long getCountry() {
        return country;
    }

    public void setCompany(Long company) {
        this.company = company;
    }

    public Long getCompany() {
        return company;
    }

    public void setSubstation(String substation) {
        this.substation = substation;
    }

    public String getSubstation() {
        return substation;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAddress() {
        return address;
    }

    public void setMaster(String master) {
        this.master = master;
    }

    public String getMaster() {
        return master;
    }


    public void setBillStatus(Integer billStatus) {
        this.billStatus = billStatus;
    }

    public Integer getBillStatus() {
        return billStatus;
    }

    public void setStationCode(String stationCode) {
        this.stationCode = stationCode;
    }

    public String getStationCode() {
        return stationCode;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("gasExpenseId", getGasExpenseId())
                .append("gasExpenseName", getGasExpenseName())
                .append("country", getCountry())
                .append("company", getCompany())
                .append("substation", getSubstation())
                .append("projectName", getProjectName())
                .append("address", getAddress())
                .append("master", getMaster())
                .append("delFlag", getDelFlag())
                .append("billStatus", getBillStatus())
                .append("stationCode", getStationCode())
                .append("status", getStatus())
                .toString();
    }
}
