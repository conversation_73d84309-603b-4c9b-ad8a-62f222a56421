package com.sccl.modules.mssaccount.mssaccountbill.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sccl.common.lang.StringUtils;
import com.sccl.common.support.Convert;
import com.sccl.common.utils.DateUtils;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.common.web.controller.BaseController;
import com.sccl.common.web.service.BizCodeThService;
import com.sccl.framework.service.IdGenerator;
import com.sccl.framework.utils.ExcelUtil;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.accountbillpre.domain.Accountbillpre;
import com.sccl.modules.business.statinAudit.aspct.StationAuditAnnotation;
import com.sccl.modules.mssaccount.mssaccountbill.domain.*;
import com.sccl.modules.mssaccount.mssaccountbill.service.IMssAccountbillService;
import com.sccl.modules.mssaccount.mssaccountbill.service.MssAccountbillServiceImpl;
import com.sccl.modules.system.organization.service.IOrganizationService;
import com.sccl.modules.system.role.domain.Role;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.service.IUserService;
import com.sccl.modules.uniflow.wfprocinst.service.IWfProcInstService;
import com.sccl.modules.uniflow.wftask.domain.WfTask;
import com.sccl.modules.uniflow.wftask.service.IWfTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报账 信息操作处理
 *
 * <AUTHOR>
 * @date 2019-04-24
 */
@Slf4j
@RestController
@RequestMapping("/mssaccount/mssAccountbill")
public class MssAccountbillController extends BaseController {
    private String prefix = "mssaccount/mssAccountbill";

    @Autowired
    private IUserService userService;
    @Autowired
    private IOrganizationService organizationService;
    @Lazy
    @Autowired
    private IMssAccountbillService mssAccountbillService;
    @Autowired
    private BizCodeThService bizCodeThService;
    @Autowired
    private IWfProcInstService wfProcInstService;// 流程
    @Autowired
    private IWfTaskService wfTaskService;// 流程

    @RequiresPermissions("mssaccount:mssAccountbill:view")
    @GetMapping()
    public String mssAccountbill() {
        return prefix + "/mssAccountbill";
    }


    static String proRoles[] = {"PROVI_ENERGY_ADMIN", "PROVI_WIRELESS_ADMIN", "PROVI_AUDIT", "PROVI_FINANCE"};
    static String cityRoles[] = {"CITY_ADMIN", "CITY_ENERGY_ADMIN", "CITY_OM_LEADER", "CITY_WIRELESS_M", "CITY_FINANCE", "LOCALNET_ADMIN"};
    static String subRoles[] = {"SUB_AREA_LEADER", "SUB_ENERGY"};

    /**
     * 查询报账列表
     */
    @RequiresPermissions("mssaccount:mssAccountbill:list")
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(MssAccountbill mssAccountbill) {
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("PROVI_")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
            if (role.getCode().startsWith("SUB_")) {//县能耗费管理员
                isSubAdmin = true;
            }
        }
        if (isProAdmin) {//  查询权限设置 分公司
        } else if (isCityAdmin) {
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0)
                mssAccountbill.setCompanyCode(companies.get(0).getId());
        } else if (isSubAdmin) {
            List<IdNameVO> departments = user.getDepartments();
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0) {
                List<Map<String, Object>> countrys = organizationService.selectSubordinateOrgByRole(companies.get(0).getId(), "1");
                if (countrys != null && countrys.size() > 0) {
                    mssAccountbill.setCountrys(countrys);
                } else {
                    if (departments != null && departments.size() > 0)
                        mssAccountbill.setOrgid(Long.valueOf(departments.get(0).getId()));
                }
            }
        } else {
            mssAccountbill.setFillInAccount(user.getLoginId());
        }
        startPage();
        List<MssAccountbill> list = mssAccountbillService.selectListByAuto(mssAccountbill);//自定义查询
        return getDataTable(list);
    }

    /**
     * 修改预算标识
     */
    @GetMapping("/updateexcludeFlag")
    public String updateexcludeFlag(@RequestParam("excludeFlag") boolean excludeFlag) {
        MssAccountbillServiceImpl.excludeFlag = excludeFlag;
        return String.format("预算管控排除1，2，3月标识:%s", excludeFlag ? "打开" : "关闭");
    }

    /**
     * 新增保存报账
     */
    @RequiresPermissions("mssaccount:mssAccountbill:add")
    //@Log(title = "报账", action = BusinessType.INSERT)
    @PostMapping("/add")
    @StationAuditAnnotation
    @ResponseBody
    public AjaxResult addSave(@RequestBody MssAccountbill mssAccountbill) {
        AjaxResult json = new AjaxResult();
        Map<String, Object> map = new HashMap<>();
        try {
            map = mssAccountbillService.saveMssAccountbill(mssAccountbill);
        } catch (Exception e) {
            map.put("msg", e.getMessage());
            e.printStackTrace();
        }
        json.put("data", map);
        return json;
    }


    /**
     * 修改报账
     */
    @GetMapping("/edit/{id}")
    public AjaxResult edit(@PathVariable("id") Long id) {
        MssAccountbill mssAccountbill = mssAccountbillService.getByid(id);
        AjaxResult json = new AjaxResult();
        Map<String, Object> map = new HashMap<>();
        if (mssAccountbill != null) {
            map.put("data", mssAccountbill);
            map.put("code", 0);
        } else {
            map.put("code", -1);
        }
        json.put("data", map);
        return json;
    }

    /**
     * 流程过程中删除报账中的台账明细
     *
     * @param billId
     * @param accountIds
     * @return
     */
    @GetMapping("/removeForAccountIds")
    public String removeForAccountIds(
            @RequestParam(value = "billId") String billId,
            @RequestParam(value = "accountIds") String accountIds
    ) {
        String result = mssAccountbillService.removeForAccountIds(Long.valueOf(billId), accountIds);
        return result;
    }

    /**
     * 修改保存报账
     */
    @RequiresPermissions("mssaccount:mssAccountbill:edit")
    //@Log(title = "报账", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody MssAccountbill mssAccountbill) {
        AjaxResult json = new AjaxResult();
        Map<String, Object> map = new HashMap<>();
        try {
            map = mssAccountbillService.saveMssAccountbill(mssAccountbill);
        } catch (Exception e) {
            map.put("msg", e.getMessage());
            e.printStackTrace();
        }
        json.put("data", map);
        return json;
    }

    /**
     * 删除报账
     */
    @RequiresPermissions("mssaccount:mssAccountbill:remove")
    //@Log(title = "报账", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        int i = 0;
        try {
            List<MssAccountbill> mssAccountbills = mssAccountbillService.selectListByIds(Convert.toStrArray(ids));
            for (MssAccountbill mssAccountbill : mssAccountbills) {
                if (mssAccountbill != null && (mssAccountbill.getStatus() == 1 || mssAccountbill.getStatus() == -2)) {
                    mssAccountbillService.deleteById(mssAccountbill.getId());
                    killFlow(mssAccountbill);
                } else if (mssAccountbill.getStatus() == -3 || mssAccountbill.getStatus() == 8) {
                    mssAccountbillService.deleteByIdForError(mssAccountbill.getId());
                } else {
                    return this.error(1, "该状态不能能删除");
                }
                i++;
            }
//            i = mssAccountbillService.deleteByIdsAuto(ConvertFormat.toStrArray(ids));
            //删除对应的稽核结果
            int n = mssAccountbillService.deletePowerAuditByBillIds(Convert.toStrArray(ids));
            return this.success("删除(" + i + ")条");
        } catch (Exception e) {
            e.printStackTrace();
            return this.error(1, "删除失败:" + e.getMessage());
        }
    }

    /**
     * 删除报账
     */
    @RequiresPermissions("mssaccount:mssAccountbill:remove")
    //@Log(title = "报账", action = BusinessType.DELETE)
    @GetMapping("/removeById/{id}")
    @ResponseBody
    public AjaxResult removeById(@PathVariable("id") Long id) {
        try {
            MssAccountbill mssAccountbill = mssAccountbillService.get(id);
            if (mssAccountbill != null && (mssAccountbill.getStatus() == 1 || mssAccountbill.getStatus() == -2)) {
                mssAccountbillService.deleteById(id);
                killFlow(mssAccountbill);
                return this.success("删除(" + id + ")成功");
            } else if (mssAccountbill.getStatus() == -3 || mssAccountbill.getStatus() == 8) {
                mssAccountbillService.deleteByIdForError(id);
                return this.success("删除(" + id + ")成功");
            } else {
                return this.error(1, "该状态不能能删除");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return this.error(1, "删除失败:" + e.getMessage());
        }
    }

    // 终止流程
    private void killFlow(MssAccountbill mssAccountbill) throws Exception {
        if (mssAccountbill.getProcessinstid() != null) {
            WfTask wfTask = new WfTask();
            wfTask.setProcInstId(mssAccountbill.getProcessinstid().toString());// 流程实例id
            List<WfTask> wfTasks = wfTaskService.selectList(wfTask);
            if (wfTasks != null && wfTasks.size() > 0) {
                Map<String, Object> param = new HashMap<>();
                param.put("procTaskId", wfTasks.get(0).getId());
                param.put("procInstId", mssAccountbill.getProcessinstid());
                param.put("shardKey", "0000");
                param.put("stop", "sys");
                JSONObject jsonObject = wfProcInstService.stopTask(this.getCurrentUser(), param);
                System.out.println(jsonObject.toJSONString());
            }
        }
    }

    /**
     * 查看报账
     */
   // @RequiresPermissions("mssaccount:mssAccountbill:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id) {
        MssAccountbill mssAccountbill = mssAccountbillService.get(id);

        Object object = JSONObject.toJSON(mssAccountbill);

        return this.success(object);
    }

    /**
     * 报账 初始化数据
     */
    @RequiresPermissions("mssaccount:mssAccountbill:add")
    @GetMapping("/addData")
    @ResponseBody
    public AjaxResult addData() {
        User user = ShiroUtils.getUser();
        if (user == null) {
            return this.error(1, "登录信息已过期，请重新登录");
        } else {
            String deptName = null;
            String orgid = null;
            List<IdNameVO> departments = user.getDepartments();
            if (departments != null && departments.size() > 0) {
                deptName = departments.get(0).getName();
                orgid = departments.get(0).getId();
            } else {
                return this.error(1, "没有查询到部门信息");
            }
            MssAccountbill mssAccountbill = new MssAccountbill();
            long nextId = IdGenerator.getNextId();
            mssAccountbill.setId(nextId);
            mssAccountbill.setStatus(1);
            mssAccountbill.setOperatorid(BigDecimal.valueOf(user.getId()));
            mssAccountbill.setFillInAccount(user.getLoginId());
            mssAccountbill.setGuid(user.getHrLoginId());//报账 需要 用到 的account 为 hrlogingId 放到 guid里面
            mssAccountbill.setFillInName(user.getUserName());
//            mssAccountbill.setFillInCostCenterId(departments.get(0).getId());
            mssAccountbill.setFillInDep(deptName);
            mssAccountbill.setOrgid(Long.valueOf(orgid));
            mssAccountbill.setTelephone(user.getPhone());//
            mssAccountbill.setBusihappendtimeflag("2");
            mssAccountbill.setBudgetsetname(DateUtils.formatDate(new Date(), "yyyy-MM"));
            mssAccountbill.setHappenDate(DateUtils.formatDate(new Date(), "yyyy-MM-dd"));
//            mssAccountbill.setPaymentType(BigDecimal.valueOf(4));
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0) {
                mssAccountbill.setCompanyCode(companies.get(0).getId());
            } else {
                return this.error(1, "没有查询到公司信息");
            }
            try {
                MssAccountbill m = new MssAccountbill();
                m.setFillInAccount(user.getLoginId());
                m = mssAccountbillService.getByOldOne(m);
                if (m != null && StringUtils.isNotEmpty(m.getCompanyNameTxt()))
                    mssAccountbill.setCompanyNameTxt(m.getCompanyNameTxt());
                if (m != null && StringUtils.isNotEmpty(m.getFillInCostCenterId()))
                    mssAccountbill.setFillInCostCenterId(m.getFillInCostCenterId());
                if (m != null && StringUtils.isNotEmpty(m.getFillInCostCenterName()))
                    mssAccountbill.setFillInCostCenterName(m.getFillInCostCenterName());
            } catch (Exception e) {
                e.printStackTrace();
            }
//            mssAccountbill.setBizTypeCode("2");
//            mssAccountbill.setPickingMode("9");
            mssAccountbill.setIsStaffPayment("0");
            mssAccountbill.setIsExistKindGift("0");
            mssAccountbill.setPaytaxattr("2");
            mssAccountbill.setIsEmergency("0");//是否加急：" prop="isEmergency" 0 否 1是
            // 改默认为否，四川，辽宁都是
            mssAccountbill.setIsInputTax("0");
//            mssAccountbill.setInvoiceType("1");
            mssAccountbill.setAbstractValue(deptName + user.getUserName() + ".电费");
//            mssAccountbill.setAccountCode("007");// 自有报账 铁塔报账
            Object object = JSONObject.toJSON(mssAccountbill);
            return this.success(object);
        }
    }

    /**
     * 报账 辽宁初始化数据 增加存续还是有限判断 “自有产权基站/标准基站”“自有产权基站/室内分布、室外站”“第三方租赁基站/标准基站”“第三方租赁基站/室内分布、室外站”四类，用途为“移动基站电费”指向主体股份“A006”；
     * 用电类型为“通信机楼（机房）/A类机楼（机房）”“通信机楼（机房）/B类机楼（机房）”“通信机楼（机房）/C类机楼（机房）”“接入机房/固网户外机柜”“接入机房/固网接入局所”“接入机房/其他接入”六类，用途为“固网机房电费”指向主体存续“B006”。
     */
    @RequiresPermissions("mssaccount:mssAccountbill:add")
    @GetMapping("/addDataLn/{id}")
    @ResponseBody
    public AjaxResult addDataLn(@PathVariable("id") String id) {
        System.out.println(id);
        User user = ShiroUtils.getUser();
        String CompanyNameTxt = "";
        if (user == null) {
            return this.error(1, "登录信息已过期，请重新登录");
        } else {
            String deptName = null;
            String orgid = null;
            List<IdNameVO> departments = user.getDepartments();
            if (departments != null && departments.size() > 0) {
                deptName = departments.get(0).getName();
                orgid = departments.get(0).getId();
            } else {
                return this.error(1, "没有查询到部门信息");
            }
            MssAccountbill mssAccountbill = new MssAccountbill();
            long nextId = IdGenerator.getNextId();
            mssAccountbill.setId(nextId);
            mssAccountbill.setStatus(1);
            mssAccountbill.setOperatorid(BigDecimal.valueOf(user.getId()));
            mssAccountbill.setFillInAccount(user.getLoginId());
            mssAccountbill.setGuid(user.getHrLoginId());//报账 需要 用到 的account 为 hrlogingId 放到 guid里面
            mssAccountbill.setFillInName(user.getUserName());
//            mssAccountbill.setFillInCostCenterId(departments.get(0).getId());
            mssAccountbill.setFillInDep(deptName);
            mssAccountbill.setOrgid(Long.valueOf(orgid));
            mssAccountbill.setTelephone(user.getPhone());//
            mssAccountbill.setBusihappendtimeflag("2");
            mssAccountbill.setBudgetsetname(DateUtils.formatDate(new Date(), "yyyy-MM"));
            mssAccountbill.setHappenDate(DateUtils.formatDate(new Date(), "yyyy-MM-dd"));
//            mssAccountbill.setPaymentType(BigDecimal.valueOf(4));
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0) {
                mssAccountbill.setCompanyCode(companies.get(0).getId());
            } else {
                return this.error(1, "没有查询到公司信息");
            }
            try {
                MssAccountbill m = new MssAccountbill();
                m.setFillInAccount(user.getLoginId());
                m = mssAccountbillService.getByOldOne(m);
                CompanyNameTxt = mssAccountbillService.getmssbasecodebyPre(id);
                if (CompanyNameTxt == null) {
                    mssAccountbill.setCompanyNameTxt("");
                } else {
                    mssAccountbill.setCompanyNameTxt(CompanyNameTxt);
                }
                //AOO6
                //BOO6
                MssAccountbill mForSameCity = new MssAccountbill();
                mForSameCity.setFillInAccount(user.getLoginId());
                mForSameCity = mssAccountbillService.selectForSameCity(mForSameCity);
                if (m != null && StringUtils.isNotEmpty(m.getFillInCostCenterId())) {
                    mssAccountbill.setFillInCostCenterId(m.getFillInCostCenterId());
                } else {
                    mssAccountbill.setFillInCostCenterId(mForSameCity.getFillInCostCenterId());
                }
                if (m != null && StringUtils.isNotEmpty(m.getFillInCostCenterName())) {
                    mssAccountbill.setFillInCostCenterName(m.getFillInCostCenterName());
                } else {
                    mssAccountbill.setFillInCostCenterName(mForSameCity.getFillInCostCenterName());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
//            mssAccountbill.setBizTypeCode("2");
//            mssAccountbill.setPickingMode("9");
            mssAccountbill.setIsStaffPayment("0");
            mssAccountbill.setIsExistKindGift("0");
            mssAccountbill.setPaytaxattr("2");
            mssAccountbill.setIsEmergency("0");//是否加急：" prop="isEmergency" 0 否 1是
            // 改默认为否，四川，辽宁都是
            mssAccountbill.setIsInputTax("0");
//            mssAccountbill.setInvoiceType("1");
            mssAccountbill.setAbstractValue(deptName + user.getUserName() + ".电费");
//            mssAccountbill.setAccountCode("007");// 自有报账 铁塔报账
            Object object = JSONObject.toJSON(mssAccountbill);
            return this.success(object);
        }
    }

    /**
     * 报账 初始化数据
     */
    @RequiresPermissions("mssaccount:mssAccountbill:add")
    @GetMapping("/getCategorys")
    @ResponseBody
    public AjaxResult getCategorys() {
        AjaxResult json = new AjaxResult();
        Map<String, Object> categorys = new HashMap<>();
        categorys.put("billtype", bizCodeThService.getCategory("billtype"));
        categorys.put("paymentType", bizCodeThService.getCategory("paymentType"));
        categorys.put("invoiceType", bizCodeThService.getCategory("invoiceType"));
        categorys.put("bizTypeCode", bizCodeThService.getCategory("bizType"));// 特殊
        categorys.put("pickingMode", bizCodeThService.getCategory("pickingMode"));
        categorys.put("isStaffPayment", bizCodeThService.getCategory("isStaffPayment"));
        json.put("categorys", categorys);
        return json;
    }

    /**
     * 获取 session data
     */
    @GetMapping("/getSessiondata")
    @ResponseBody
    public AjaxResult getSessiondata() {
        AjaxResult json = new AjaxResult();
        Map<String, Object> map = new HashMap<>();
        User user = ShiroUtils.getUser();
        map.put("companies", user.getCompanies());
        map.put("departments", user.getDepartments());
        json.put("data", map);
        return json;
    }

    /**
     * 查询挑对报账列表 status in (4,7)
     */
    @RequiresPermissions("mssaccount:mssAccountbill:list")
    @RequestMapping("/listCheck")
    @ResponseBody
    public TableDataInfo listCheck(MssAccountbill mssAccountbill) {
        startPage();
        List<MssAccountbill> list = mssAccountbillService.selectListByCheck(mssAccountbill);//自定义查询
        return getDataTable(list);
    }

    @RequestMapping("/selectListByCheckSK")
    @ResponseBody
    public TableDataInfo selectListByCheckSK(MssAccountbill mssAccountbill) {
        startPage();
        List<MssAccountbill> list = mssAccountbillService.selectListByCheckSK(mssAccountbill);//自定义查询
        return getDataTable(list);
    }

    /**
     * 查询调账报账列表 status in (4,7)
     */
    @RequiresPermissions("mssaccount:mssAccountbill:list")
    @RequestMapping("/listAdjust")
    @ResponseBody
    public TableDataInfo listAdjust(MssAccountbill mssAccountbill) {
        startPage();
        List<MssAccountbill> list = mssAccountbillService.selectListByCheck(mssAccountbill);//自定义查询
        return getDataTable(list);
    }


    /**
     * 获取 归集单
     */
    @GetMapping("/getAccountBillpre/{id}")
    @ResponseBody
    public AjaxResult getAccountBillpre(@PathVariable("id") Long id) {
        AjaxResult json = new AjaxResult();
        Accountbillpre billpre = mssAccountbillService.getAccountBillpre(id);
        json.put("data", billpre);
        return json;
    }

   // @RequiresPermissions("business:mssAccountbill:selectListByIds")
    @RequestMapping("/selectListByIds")
    @ResponseBody
    public TableDataInfo selectListByIds(Accountbillpre pre) {
        startPage();
        List<MssAccountbill> list = new ArrayList<>();
        String ids = pre.getPabid();
        if (ids != null && ids.length() > 0) {
            list = mssAccountbillService.selectListByIds(Convert.toStrArray(ids));
        }
        return getDataTable(list);
    }

    @GetMapping("/getRoleByUser")
    @ResponseBody
    public AjaxResult getRoleByUser() {
        AjaxResult json = new AjaxResult();
        User user = ShiroUtils.getUser();
        json.put("data", user.getLoginId());
        return json;
    }

    @GetMapping("/addNewBillByExist")
    @ResponseBody //根据财辅退回的单子 生成新的报账单
    public AjaxResult addNewBillByExist(Long id) {
        AjaxResult json = new AjaxResult();
        Long billid = null;
        try {
            billid = mssAccountbillService.addNewBillByExist(id);
            json.put("id", billid);
        } catch (Exception e) {
            e.printStackTrace();
            json.put("mes", "系统异常请联系管理员" + e.getMessage());
        }
        return json;
    }

    @GetMapping("/addNewBillByExistExcludePcids")
    @ResponseBody //根据财辅退回的单子 生成新的报账单
    public String addNewBillByExistExcludePcids(@RequestParam(value = "billId") Long id,
                                                @RequestParam(value = "pcids") String pcids) {
        List<Long> accountIds = Arrays.stream(pcids.split("-")).map(Long::parseLong).collect(Collectors.toList());
        String result = "";
        try {
            result = mssAccountbillService.addNewBillByExistExcludePcids(id, accountIds);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }


    /**
     * 送财辅失败 仅保存 挑对信息后 重新送财辅
     */
    @RequiresPermissions("mssaccount:mssAccountbill:edit")
    @PostMapping("/saveChecks")
    @ResponseBody
    public AjaxResult saveChecks(@RequestBody MssAccountbill mssAccountbill) {
        AjaxResult json = new AjaxResult();
        Map<String, Object> map = new HashMap<>();
        try {
            map = mssAccountbillService.saveChecks(mssAccountbill);
        } catch (Exception e) {
            map.put("msg", "保存失败,请联系系统管理员");
            e.printStackTrace();
        }
        json.put("data", map);
        return json;
    }

    // 列表页面提交流程是 验证报账单关联台账问题
    @GetMapping("/saveCheckAccount/{id}")
    @ResponseBody
    public AjaxResult saveCheckAccount(@PathVariable("id") Long id) {
        AjaxResult json = new AjaxResult();
        Map<String, Object> map = new HashMap<>();
        try {
//            MssAccountbill mssAccountbill = mssAccountbillService.get(id);
//            if (mssAccountbill != null)
//                map = mssAccountbillService.saveCheckAccount(mssAccountbill);
            MssAccountbill mssAccountbill = mssAccountbillService.getByid(id);
            map = mssAccountbillService.checkMssAccountbill(mssAccountbill);

        } catch (Exception e) {
            map.put("msg", "提交失败,请联系系统管理员");
            e.printStackTrace();
        }
        json.put("data", map);
        return json;
    }

    //查询无线大数据平台异常基站数据
    @RequiresPermissions("mssaccount:mssAccountbill:list")
    @RequestMapping("/querystationError")
    @ResponseBody
    public TableDataInfo querystationError(Accountbillpre pre) {
        startPage();
        List<Map<String, Object>> list = mssAccountbillService.querystationError(pre);
        return getDataTable(list);
    }

    //查询无线大数据平台异常基站数据
    @RequiresPermissions("mssaccount:mssAccountbill:list")
    @RequestMapping("/saveStationRemark")
    @ResponseBody
    public AjaxResult saveStationRemark(Long id, String remark) {
        AjaxResult json = new AjaxResult();
        try {
            User user = ShiroUtils.getUser();
            mssAccountbillService.saveStationRemark(id, remark, user.getUserName());
            json.put("success", true);
        } catch (Exception e) {
            e.printStackTrace();
            json.put("success", false);
        }
        return json;
    }

    /**
     * 查询非电费报账单列表
     */
    @RequiresPermissions("mssaccount:mssAccountbill:nonElectriclist")
    @RequestMapping("/nonElectriclist")
    @ResponseBody
    public TableDataInfo nonElectriclist(@RequestBody MssAccountbill mssAccountbill) {
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("PROVI_")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
            if (role.getCode().startsWith("SUB_")) {//县能耗费管理员
                isSubAdmin = true;
            }
        }
        if (isProAdmin) {//  查询权限设置 分公司
        } else if (isCityAdmin) {
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0)
                mssAccountbill.setCompanyCode(companies.get(0).getId());
        } else if (isSubAdmin) {
            List<IdNameVO> departments = user.getDepartments();
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0) {
                List<Map<String, Object>> countrys = organizationService.selectSubordinateOrgByRole(companies.get(0).getId(), "1");
                if (countrys != null && countrys.size() > 0) {
                    mssAccountbill.setCountrys(countrys);
                } else {
                    if (departments != null && departments.size() > 0)
                        mssAccountbill.setOrgid(Long.valueOf(departments.get(0).getId()));
                }
            }
        } else {
            mssAccountbill.setFillInAccount(user.getLoginId());
        }
        startPage();
        List<MssAccountbill> list = mssAccountbillService.selectNonElectricListByAuto(mssAccountbill);//自定义查询
        return getDataTable(list);
    }

    /**
     * 新增保存非电费报账-- 水电气
     */
    @RequiresPermissions("mssaccount:mssAccountbill:addSaveNonElectricBill")
    //@Log(title = "报账", action = BusinessType.INSERT)
    @PostMapping("/addSaveNonElectricBill")
    @ResponseBody
    public AjaxResult addSaveNonElectricBill(@RequestBody MssAccountbill mssAccountbill) {
        AjaxResult json = new AjaxResult();
        Map<String, Object> map = new HashMap<>();
        try {
            map = mssAccountbillService.saveNonElectricMssAccountbill(mssAccountbill);
        } catch (Exception e) {
            map.put("msg", "保存失败,请联系系统管理员");
            e.printStackTrace();
        }
        json.put("data", map);
        return json;
    }

    /**
     * 新增保存非电费报账 -- 新增的热力、煤、用油
     *
     */
    @RequiresPermissions("mssaccount:mssAccountbill:addSaveNonElectricBill")
    //@Log(title = "报账", action = BusinessType.INSERT)
    @PostMapping("/addSaveNonElectricBillNew")
    @ResponseBody
    public AjaxResult addSaveNonElectricBillNew(@RequestBody MssAccountbill mssAccountbill) {
        AjaxResult json = new AjaxResult();
        Map<String, Object> map = new HashMap<>();
        try {
            map = mssAccountbillService.saveNonElectricMssAccountbillNew(mssAccountbill);
        } catch (Exception e) {
            map.put("msg", "保存失败,请联系系统管理员");
            e.printStackTrace();
        }
        json.put("data", map);
        return json;
    }

    /**
     * 新增保存非电费报账
     */
    @RequiresPermissions("mssaccount:mssAccountbill:editSaveNonElectricBill")
    //@Log(title = "报账", action = BusinessType.INSERT)
    @PostMapping("/editSaveNonElectricBill")
    @ResponseBody
    public AjaxResult editSaveNonElectricBill(@RequestBody MssAccountbill mssAccountbill) {
        AjaxResult json = new AjaxResult();
        Map<String, Object> map = new HashMap<>();
        try {
            map = mssAccountbillService.saveNonElectricMssAccountbill(mssAccountbill);
        } catch (Exception e) {
            map.put("msg", "保存失败,请联系系统管理员");
            e.printStackTrace();
        }
        json.put("data", map);
        return json;
    }

    /**
     * 报账统计查询
     * @param request
     * @return
     */
    @RequestMapping("/list/AccountBill")
    public TableDataInfo listStatisticalAccountBill(AccountBillRequest request) {
        // 参数处理
        handleParams(request);
        startPage();
        List<StatisticalAccountBillDTO> list = mssAccountbillService.listStatisticalAccountBill(request);
        list.forEach(account ->
            account.setAccountBillId(StringUtils.substringBefore(
                    account.getAccountBillId(), ".")));
        return getDataTable(list);
    }

    /**
     * 预付管理统计查询
     * @param request
     * @return
     */
    @RequestMapping("/list/PreAccountBill")
    public TableDataInfo listPreAccountBill(AccountBillRequest request) {
        // 参数处理
        handleParams(request);
        startPage();
        List<PrepaidAccountBillDTO> list = mssAccountbillService.listPreAccountBill(request);
        return getDataTable(list);
    }


    /**
     * 预付管理统计/报账统计导出
     * @param response
     * @param request
     */
    @RequestMapping("/export/accountBill")
    public void exportPreAccountBill(HttpServletResponse response, AccountBillRequest request) {
        // 参数处理
        handleParams(request);
        // 预付管理统计-按公司导出
        if (null != request.getExportType() && request.getExportType() == 1) {
            List<PrepaidAccountBillDTO> list = mssAccountbillService.listPreAccountBill(request);
            list.forEach(account -> account.setScale(account));
            ExcelUtil<PrepaidAccountBillDTO> excelUtil = new ExcelUtil<>(PrepaidAccountBillDTO.class);
            excelUtil.exportExcelToBrowser(response, list, "预付管理-预付报表");
        }
        // 预付管理统计-按供应商导出
        if (null != request.getExportType() && request.getExportType() == 2) {
            List<PrepaidAccountBillDTO> list = mssAccountbillService.listPreAccountBill(request);
            List<SupplierPrepaidAccountBillDTO> exports = new ArrayList<>(list.size());
            list.forEach(account -> {
                try {
                    SupplierPrepaidAccountBillDTO accountBill = new SupplierPrepaidAccountBillDTO();
                    account.setScale(account);
                    BeanUtils.copyProperties(accountBill, account);
                    exports.add(accountBill);
                } catch (Exception e) {
                    log.info("赋值对象错误：{}", e.getMessage());
                    e.printStackTrace();
                }
            });
            ExcelUtil<SupplierPrepaidAccountBillDTO> excelUtil = new ExcelUtil<>(SupplierPrepaidAccountBillDTO.class);
            excelUtil.exportExcelToBrowser(response, exports, "预付管理-预付报表");
        }
        // 报账统计导出
        if (null != request.getExportType() && request.getExportType() == 3) {
            List<StatisticalAccountBillDTO> list = mssAccountbillService.listStatisticalAccountBill(request);
            list.forEach(account -> {
                account.setAccountTotalMoney(account.getAccountTotalMoney().setScale(
                        2, RoundingMode.HALF_UP));
                account.setAccountBillId(StringUtils.substringBefore(
                                account.getAccountBillId(), "."));
            });
            ExcelUtil<StatisticalAccountBillDTO> excelUtil = new ExcelUtil<>(StatisticalAccountBillDTO.class);
            excelUtil.exportExcelToBrowser(response, list, "报账统计");
        }
    }

    /**
     * 参数处理
     * @param request
     */
    private void handleParams(AccountBillRequest request) {
        if (StringUtils.isNotEmpty(request.getCompany())) {
            List<String> contrys = Lists.newArrayList();
            // 模糊查询部门信息
            List<Map<String, Object>> list = organizationService.selectOrganizationByName(request.getKeyOrgNameWord());
            list.forEach(map -> contrys.add(map.get("id").toString()));
            request.setCountrys(contrys);
        }
        if (StringUtils.isNotEmpty(request.getStartDate())) {
            StringBuilder sb = new StringBuilder(request.getStartDate());
            sb.insert(4, "-");
            request.setStartDate(sb.toString());
        }
        if (StringUtils.isNotEmpty(request.getEndDate())) {
            StringBuilder sb = new StringBuilder(request.getEndDate());
            sb.insert(4, "-");
            request.setEndDate(sb.toString());
        }
        if (StringUtils.isEmpty(request.getStartDate())) {
            request.setStartDate(null);
        }
        if (StringUtils.isEmpty(request.getEndDate())) {
            request.setEndDate(null);
        }
        if (StringUtils.isNotEmpty(request.getBudgetsetname())) {
            StringBuilder sb = new StringBuilder(request.getBudgetsetname());
            sb.insert(4, "-");
            request.setBudgetsetname(sb.toString());
        }
        if (StringUtils.isEmpty(request.getBudgetsetname())) {
            request.setBudgetsetname(null);
        }
        if (StringUtils.isNotEmpty(request.getCompany()) && request.getCompany().equals("-1")) {
            request.setCompany(null);
        }
        if (null != request.getEnergyType() && request.getEnergyType().equals("-1")) {
            request.setEnergyType(null);
        }
    }
}
