package com.sccl.modules.business.powermodel.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sccl.framework.aspectj.lang.annotation.Excel;
import com.sccl.modules.autojob.util.convert.StringUtils;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 电量模型原始数据(PowerModleInit)实体类
 *
 * <AUTHOR>
 * @since 2022-10-20 10:44:40
 */
@Data
@NoArgsConstructor
public class PowerModleInit implements Serializable {
    private static final long serialVersionUID = 439904870318405350L;
    /**
     * 仅供重新稽核使用，无对应数据库字段映射
     * 要稽核的批量户号
     */
    List<String> accnos;
    /**
     * 用户名称
     */
    @Excel(name = "用户名称")
    private String username;
    /**
     * 录入人id
     */
    private String loginId;
    /**
     * 户号
     */
    @Excel(name = "户号")
    private String accountno;
    /**
     * 售电公司
     */
    @Excel(name = "售电公司")
    private String elesellcompany;
    /**
     * 地市级公司
     */
    @Excel(name = "地市")
    private String citycompany;
    /**
     * 县级公司
     */
    @Excel(name = "县级")
    private String countylevelcompany;
    /**
     * 电压等级
     */
    @Excel(name = "电压")
    private Double voltagelevel;
    /**
     * 结算品种 -1未知 1常规直购 2精准长协
     */
    @Excel(name = "结算品种")
    private String sttype;
    /**
     * 用户类别
     */
    @Excel(name = "用户类别")
    private String ueertype;
    /**
     * 年
     */
    @Excel(name = "年")
    private String year;
    /**
     * 月
     */
    @Excel(name = "月")
    private String month;
    /**
     * 用电量
     */
    @Excel(name = "用电量")
    private String powercSize;
    /**
     * 水电消纳计量点量
     */
    @Excel(name = "水电消纳计量点量")
    private String sizeHydropower;
    /**
     * 水电消纳用电量
     */
    @Excel(name = "水电消纳用电量")
    private String powerHydropower;
    /**
     * 电能替代用电量
     */
    @Excel(name = "电能替代用电量")
    private String powerDntd;
    /**
     * 工业电量
     */
    @Excel(name = "工业电量")
    private String powercIndustrySize;
    /**
     * 非工业电量
     */
    @Excel(name = "非工业电量")
    private String powercIndustrySizeNot;
    /**
     * 精准长协电量
     */
    @Excel(name = "精准长协电量")
    private String powerAccurateLong;
    /**
     * 常规长协电量
     */
    @Excel(name = "常规长协电量")
    private String powerRoutineLong;
    /**
     * 留存电量
     */
    @Excel(name = "留存电量")
    private String powerRetained;
    /**
     * 富余基数
     */
    @Excel(name = "富余基数")
    private String surplusBase;
    /**
     * (常规直购)总结算电量
     */
    @Excel(name = "(常规直购)总结算电量")
    private String powerTotalSize;
    /**
     * (常规直购)总结算均价
     */
    @Excel(name = "(常规直购)总结算均价")
    private BigDecimal averagepriceTotal;
    /**
     * (常规直购)直接交易结算电量
     */
    @Excel(name = "(常规直购)直接交易结算电量")
    private String powerTotalSize1;
    /**
     * (常规直购)直接交易结算均价
     */
    @Excel(name = "(常规直购)直接交易结算均价")
    private BigDecimal averagepriceTotal1;
    /**
     * (精准长协)总结算电量
     */
    @Excel(name = "(精准长协)总结算电量")
    private String powerTotalSize2;
    /**
     * (精准长协)总结算均价
     */
    @Excel(name = "(精准长协)总结算均价")
    private BigDecimal averagepriceTota2;
    /**
     * 用户实际承担考核费用
     */
    @Excel(name = "用户实际承担考核费用")
    private BigDecimal priceUserActual;
    /**
     * 主键Id
     */
    private Long id;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createtime;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatetime;
    /**
     * 删除标记 0正常 1删除
     */
    private Integer delflag;
    /**
     * 权限 相关的城市
     */
    private Long city;
    /**
     * 国网代购价
     */
    @Excel(name = "国网代购价")
    private BigDecimal indirectCountry;
    /**
     * 基金附加费
     */
    @Excel(name = "基金附加费")
    private BigDecimal appendFund;
    /**
     * 大工业容量/需量
     */
    @Excel(name = "大工业容量/需量")
    private BigDecimal capacityDemandBig;
    /**
     * 当月变压器最大需量
     */
    @Excel(name = "当月变压器最大需量")
    private BigDecimal capacityDemandBig2;
    /**
     * 变压器计算方式 标识 -1不存在值/1容量/2需量
     */
    private Integer capacityDemandBigFlag;
    /**
     * 输配电价
     */
    @Excel(name = "输配电价")
    private BigDecimal priceSp;
    /**
     * 峰电量1.6
     */
    @Excel(name = " 峰电量1.6")
    private String powerHigh;
    /**
     * 平电量1
     */
    @Excel(name = "平电量1")
    private String powerMiddle;
    /**
     * 谷电量0.4
     */
    @Excel(name = "谷电量0.4")
    private String powerLow;
    /**
     * 损益
     */
    @Excel(name = "损益")
    private BigDecimal profitLoss;
    /**
     * 水电合同电量(度)
     *
     * @return
     */
    @Excel(name = "水电合同电量(度)")
    private String powerContractHydropower;
    /**
     * 水电合同价
     */
    @Excel(name = "水电合同价")
    private BigDecimal contractHydropower;
    /**
     * 浮动水电电量
     */
    @Excel(name = "浮动水电电量")
    private String powerHydropowerChange;
    /**
     * 水电浮动价
     */
    @Excel(name = "水电浮动价")
    private BigDecimal priceContractHydropower;
    /**
     * 实际火电电量
     */
    @Excel(name = "实际火电电量")
    private String powerThermalpowerCalc;
    /**
     * 实际水电电量
     */
    @Excel(name = "实际水电电量")
    private String powerHydropowerCalc;
    /**
     * 火电价格
     */
    @Excel(name = "火电价格")
    private BigDecimal priceThermalpower;
    /**
     * 合同电量
     */
    @Excel(name = "合同电量")
    private String powerContract;
    /**
     * 国网代购测算值单价
     */
    @Excel(name = "国网代购测算值单价")
    private BigDecimal priceCountryProxy;
    /**
     * 国网代购分峰平谷单价
     */
    @Excel(name = "国网代购分峰平谷单价")//
    private BigDecimal priceCountryProxyTimeshar;
    /**
     * 电价代购峰值
     */
    @Excel(name = "电价代购峰值")
    private BigDecimal priceCountryProxyHigh;
//管理用电
    /**
     * 电价代购平值
     */
    @Excel(name = "电价代购平值")
    private BigDecimal priceCountryProxyMid;
    /**
     * 电价代购谷值
     */
    @Excel(name = "电价代购谷值")
    private BigDecimal priceCountryProxyLow;
    /**
     * 直购电(实际)
     */
    @Excel(name = "直购电(实际)")
    private BigDecimal priceDirectpurele;
    /**
     * 直购电理论值电价测算（分合同、浮动）
     */
    @Excel(name = "直购电理论值电价测算（分合同、浮动）")
    private BigDecimal priceDirectpureleTheoryEstimate;
    /**
     * 直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余）
     */
    @Excel(name = "直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余）")
    private BigDecimal priceDirectpureleTheoryEstimate2;
    /**
     * 直购电理论电价测算（不分合同内外、不含富余、分峰平谷）
     */
    @Excel(name = "直购电理论电价测算（不分合同内外、不含富余、分峰平谷）")
    private BigDecimal priceDirectpureleEstimate;
    /**
     * 直购电实际（含富余、并分峰平谷）
     */
    @Excel(name = "直购电实际（含富余、并分峰平谷）")
    private BigDecimal priceDirectpurele2;
    /**
     * 直购电交易电价与国网代购价差（不分峰平谷、不浮动）
     */
    @Excel(name = "直购电交易电价与国网代购价差（不分峰平谷、不浮动）")
    private BigDecimal priceDirectpurele3;
    /**
     * 直购电实际，无办公
     *
     * @return
     */
    @Excel(name = "直购电实际（无办公）")
    private BigDecimal priceDirectpurele4;
    /**
     * 直购电理论电价测算（不分合同内外、不含富余、分峰平谷）与国网代购分峰平谷之差
     *
     * @return
     */
    @Excel(name = "直购电理论电价测算（不分合同内外、不含富余、分峰平谷）与国网代购分峰平谷之差")
    private BigDecimal diff1;
    /**
     * 直购电收益
     *
     * @return
     */
    @Excel(name = "直购电收益")
    private BigDecimal profitDire;
    /**
     * 上报集团单价
     *
     * @return
     */
    @Excel(name = "上报集团单价")
    private BigDecimal priceToGroup;
    /**
     * 是否有办公
     *
     * @return
     */
    @Excel(name = "是否有办公 0 否/1 是")
    private Integer work;
    /**
     * 是否参加直购电
     *
     * @return
     */
    @Excel(name = "是否参加直购电 0 否/1 是")
    private Integer getDire;
    /**
     * 组织结构 power_city_organization org_code
     */
    private String orgCode;
    /**
     * 异常信息
     */
    private String exMsg;

    private boolean calcFlag;

    private String company;
    private String country;

    //结算数据2023 1 月版未用列
    //(留存)总结算电量-(留存)总结算均价-(保障性小水电)总结算电量-(保障性小水电)总结算均价
    //(水电消纳)总结算电量-(水电消纳)总结算均价
    //(电能替代)总结算电量-(电能替代)总结算均价-(常规长协)总结算电量-(常规长协)总结算均价
    //(精准长协)总结算电量-(精准长协)总结算均价
    private StringBuilder exMsgBuilder = new StringBuilder();
    /**
     * 基础表主键id
     */
    private Long init_id;
    /**
     * 线损
     */
    private BigDecimal lineLoss;

    public static void main(String[] args) {
        System.out.println(StringUtils.splitData("中国电信股份有限公司成都分公司", "司", "分"));
    }

    public static String getCityForOrgCode(PowerModleInit powerModleInit) {
        return StringUtils.splitData(powerModleInit.getUsername(), "司", "分");
    }

    public Integer getCapacityDemandBigFlag() {
        return capacityDemandBigFlag;
    }

    public void setCapacityDemandBigFlag(Integer capacityDemandBigFlag) {
        this.capacityDemandBigFlag = capacityDemandBigFlag;
    }

    public String appendExMsg(String msg) {
        return exMsgBuilder.append(msg).toString();
    }

    public String getExMsg() {
        return exMsgBuilder.toString();
    }

    public void setExMsg(String exMsg) {
        this.exMsgBuilder = new StringBuilder(exMsg == null ? "" : exMsg);
        this.exMsg = exMsg;
    }

    public boolean check1() {
        return (this.elesellcompany != null) && (this.year != null) && (this.month != null);
    }

    public boolean existKey(List<PowerModleInit> content) {
        return content.stream().anyMatch(
                init -> {
                    String username = init.getUsername();
                    String accountno = init.getAccountno();
                    String contional1 = this.getUsername();
                    String contional2 = this.getAccountno();
                    return username.equals(contional1) && accountno.equals(contional2);
                }
        );
    }

    public boolean checkBigWork() {
        return this.getCapacityDemandBig2() != null && this.getCapacityDemandBig() != null;
    }

    public void setLineLoss(BigDecimal lineLoss) {
        this.lineLoss = lineLoss;
    }

    public BigDecimal getLineLoss() {
        return lineLoss;
    }
}

