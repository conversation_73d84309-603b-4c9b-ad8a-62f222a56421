package com.sccl.modules.business.noderesult.domain;

public enum NodeType {
    StationEmpty(1, "站址编码缺失"),
    StaionGrade(2, "站址分级"),
    StationPowerChange(3, "电量波动"),
    StationAccountChange(4, "电费波动"),
    StationProtocolExpired(5, "电表协议缺失"),
    StationChangeSameMeter(6, "电表对应站址变化"),
    StationMeterChangeSameCode(7, "站址对应电表变动"),
    StationStop(8, "起租单失效"),
    QuotaCompare(9,"网关日均电量");

    private Integer code;
    private String des;


    NodeType(Integer code, String des) {
        this.code = code;
        this.des = des;
    }

    public Integer getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }
}
