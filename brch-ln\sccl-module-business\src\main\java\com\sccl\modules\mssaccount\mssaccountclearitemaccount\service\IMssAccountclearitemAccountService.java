package com.sccl.modules.mssaccount.mssaccountclearitemaccount.service;

import com.sccl.framework.service.IBaseService;
import com.sccl.modules.mssaccount.mssaccountclearitemaccount.domain.MssAccountclearitemAccount;

import java.util.List;

/**
 * 预付冲销 挑对台账 服务层
 *
 * <AUTHOR>
 * @date 2019-11-25
 */
public interface IMssAccountclearitemAccountService extends IBaseService<MssAccountclearitemAccount> {


    List<MssAccountclearitemAccount> listBybillId(Long id);

    List<MssAccountclearitemAccount> listBypcids(String[] toStrArray);

    List<MssAccountclearitemAccount> listBybillIds(String[] toStrArray);

}
