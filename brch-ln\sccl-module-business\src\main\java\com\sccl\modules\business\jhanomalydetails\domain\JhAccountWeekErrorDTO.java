package com.sccl.modules.business.jhanomalydetails.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.sccl.framework.aspectj.lang.annotation.Excel;
import lombok.Data;


@Data
public class JhAccountWeekErrorDTO {
    /**
     * 电表户名/协议编码
     */
    @Excel(name = "电表户名/协议编码_tz_bz")
    private String dbhm;

    /**
     * 台账期号
     */
    @TableField("tzqh")
    @Excel(name = "台账期号_tz")
    private String tzqh;

    /**
     * 集团站址编码
     */
    @TableField("jtzzbm")
    @Excel(name = "集团站址编码_tz")
    private String jtzzbm;

    /**
     * 铁塔站址编码
     */
    @TableField("ttzzbm")
    @Excel(name = "铁塔站址编码_tz")
    private String ttzzbm;

    /**
     * 最近一次报账时间
     */
    @TableField("zjycbzsj")
    @Excel(name = "最近一次报账时间_tz_bz")
    private String zjycbzsj;

    /**
     * 最近报账期号
     */
    @TableField("zjbzqh")
    @Excel(name = "最近报账期号_tz_bz")
    private String zjbzqh;

    /**
     * 间隔天数
     */
    @TableField("jg")
    @Excel(name = "间隔天数_tz_bz")
    private String jg;

}
