package com.sccl.modules.business.noderesult.domain;

import com.enrising.dcarbon.audit.RefereeDatasource;
import com.sccl.framework.web.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * 基站一站式稽核 原始数据表 node_result
 *
 * <AUTHOR>
 * @date 2022-11-22
 */
@Data
public class NodeResult extends BaseEntity implements RefereeDatasource {
    private static final long serialVersionUID = 1L;

    /**
     * 评判的报账单
     */
    private Long billid;
    /**
     * 评判的时间
     */
    private LocalDateTime audittime;
    /**
     * 节点类型
     * 1-报账历史/2-电量历史/3-转供电合同是否失效/4-站址连续报账记录存在（转6直2）/5-电表不同站址/6-站址不同电表7-站址设备变动/8-站址评级
     */
    private Integer nodetype;

    /**
     * 等级
     */
    private Integer level;
    /**
     * 详情
     */
    private String details;
    /**
     * 节点评判信息
     */
    private String auditmsg;
    /**
     * 创建时间
     */
    private LocalDateTime createtime;
    /**
     * 更新时间
     */
    private LocalDateTime updatetime;
    /**
     * 评判的台账id
     */
    private Long pcid;
    /**
     * 台账对应的电表id
     */
    private Long ammeterid;
    /**
     * 能耗站址id
     */
    private String stationcode;
    /**
     * 集团站址id
     */
    private String stationcode_source;
    /**
     * 比对的台账id
     */
    private Long pcid_compare;
    /**
     * 比对的电表id
     */
    private Long ammeterid_compare;
    /**
     * 比对的站址id
     */
    private String  stationcode_compare;

    /**
     * 比对的电量
     */
    private BigDecimal power_compare;
    /**
     * 比对的台账金额
     */
    private BigDecimal accountmoney_compare;
    /**
     * 本次台账电量 偏离上一次的比例
     */
    private BigDecimal widepower;
    /**
     * 本次台账金额 偏离上一次的比例
     */
    private BigDecimal wideaccount;
    /**
     * 比对的集团站址id
     */
    private String  station_source_compare;
    /**
     * 项目名称
     */
    private String projectname;
    /**
     * 局站名称
     */
    private String stationname;
    /**
     * 局站地址
     */
    private String stationaddress;
    /**
     * 台账对应电量
     */
    private BigDecimal power;
    /**
     * 报账->台账 对应的金额
     */
    private BigDecimal accountmoney;
    /**
     * 标准电量
     */
    private BigDecimal standardpower;
    /**
     * 评级标准
     */
    private Long gid;
    /**
     * 台账录入日期
     */
    private LocalDateTime inputdate;
    /**
     * 台账 时间间隔
     */
    private Integer diff;
    /**
     * 站址评价
     **/
    private String evaluate;

    /**
     * 实际偏离 标准 百分比
     *
     * @param billid
     */
    private BigDecimal wide;
    /**
     * 协议签订时间
     */
    private LocalDateTime protocolsigneddate;
    /**
     * 协议终止时间
     */
    private LocalDateTime protocolterminatedate;

    /**
     * 同一电表 的异常站址信息
     * @return
     */
    private String stationcodelast_samemeter;

    /**
     * 当前台账的电表 对应 站址的上期 电表情况
     * @return
     */
    private String meterlast_samestationcode;

    public Long getBillid() {
        return billid;
    }

    public void setBillid(Long billid) {
        this.billid = billid;
    }

    public Integer getNodetype() {
        return nodetype;
    }

    public void setNodetype(Integer nodetype) {
        this.nodetype = nodetype;
    }

    public String getAuditmsg() {
        return auditmsg;
    }

    public void setAuditmsg(String auditmsg) {
        this.auditmsg = auditmsg;
    }

    public Long getPcid() {
        return pcid;
    }

    public void setPcid(Long pcid) {
        this.pcid = pcid;
    }



    public BigDecimal getPower() {
        return power;
    }

    public void setPower(BigDecimal power) {
        this.power = power;
    }

    public BigDecimal getStandardpower() {
        return standardpower;
    }

    public void setStandardpower(BigDecimal standardpower) {
        this.standardpower = standardpower;
    }

    public Long getGid() {
        return gid;
    }

    public void setGid(Long gid) {
        this.gid = gid;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("billid", getBillid())
                .append("audittime", getAudittime())
                .append("nodetype", getNodetype())
                .append("auditmsg", getAuditmsg())
                .append("createtime", getCreatetime())
                .append("updatetime", getUpdatetime())
                .append("delFlag", getDelFlag())
                .append("pcid", getPcid())
                .append("stationcode", getStationcode())
                .append("power", getPower())
                .append("standardpower", getStandardpower())
                .append("gid", getGid())
                .append("inputdate", getInputdate())
                .toString();
    }
}
