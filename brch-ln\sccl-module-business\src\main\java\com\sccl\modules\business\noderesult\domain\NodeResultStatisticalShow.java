package com.sccl.modules.business.noderesult.domain;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 基站一站式 结果 展示
 */
@Data
public class NodeResultStatisticalShow {
    private String title;
    private Long billId;
    private LocalDateTime audittime;
//    private HashMap<String, Map<String, Integer>> resultMap;
    private HashMap<String, Object> resultMap;
}
