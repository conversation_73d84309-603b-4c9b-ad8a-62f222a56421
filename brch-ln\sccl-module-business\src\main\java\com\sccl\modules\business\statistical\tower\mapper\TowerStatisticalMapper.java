package com.sccl.modules.business.statistical.tower.mapper;

import com.sccl.modules.business.statistical.tower.query.ContributionPercentQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-11-30 14:39
 * @email <EMAIL>
 */
@Mapper
public interface TowerStatisticalMapper {
    Integer selectAccountedCount(@Param("towerIds") List<Long> towerIds);

    List<ContributionPercentQuery> selectContributionPercentQuery(@Param("queries") List<ContributionPercentQuery> queries, @Param("from") String from, @Param("to") String to, @Param("company") Long company, @Param("country") Long country, @Param("skip") Integer skip, @Param("size") Integer size, @Param("isTurn") Boolean isTurn);

    Integer countContributionPercentQuery(@Param("queries") List<ContributionPercentQuery> queries, @Param("from") String from, @Param("to") String to, @Param("company") Long company, @Param("country") Long country,  @Param("isTurn") Boolean isTurn);

}
