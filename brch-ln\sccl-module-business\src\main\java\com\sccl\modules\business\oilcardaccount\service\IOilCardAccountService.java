package com.sccl.modules.business.oilcardaccount.service;

import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.business.oilcardaccount.domain.OilCardAccount;
import com.sccl.framework.service.IBaseService;

/**
 * 油卡台账 服务层
 *
 * <AUTHOR>
 * @date 2021-12-20
 */
public interface IOilCardAccountService extends IBaseService<OilCardAccount> {

    /** 删除油卡台账 */
    AjaxResult removeOilCardAccount(String ids);

    /**
     * 验证是否存在
     * @param id 电表协议id
     * @return
     */

}
