package com.sccl.modules.mssaccount.msssupplier.service;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.mssaccount.msssupplier.mapper.MssSupplierMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sccl.modules.mssaccount.msssupplier.domain.MssSupplier;


/**
 * 供应商 服务层实现
 * 
 * <AUTHOR>
 * @date 2019-05-01
 */
@Service
public class MssSupplierServiceImpl extends BaseServiceImpl<MssSupplier> implements IMssSupplierService
{
    @Autowired
    private MssSupplierMapper mssSupplierMapper;

    /**
     * 根据供货商名称获取供货商信息
     * @param name 供货商名称
     */
    @Override
    public MssSupplier selectOneByName(String name) {
        return mssSupplierMapper.selectOneByName(name);
    }
}
