package com.sccl.modules.business.noderesultstatistical.controller;

import com.alibaba.fastjson.JSONObject;
import com.enrising.dcarbon.audit.RefereeDatasource;
import com.sccl.common.support.Convert;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.noderesultstatistical.domain.NodeResultStatistical;
import com.sccl.modules.business.noderesultstatistical.service.INodeResultStatisticalService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 统计指标 信息操作处理
 *
 * <AUTHOR>
 * @date 2022-11-24
 */
@RestController
@RequestMapping("/business/nodeResultStatistical")
public class NodeResultStatisticalController extends BaseController {
    private String prefix = "business/nodeResultStatistical";

    @Autowired
    private INodeResultStatisticalService nodeResultStatisticalService;

    @RequiresPermissions("business:nodeResultStatistical:view")
    @GetMapping()
    public String nodeResultStatistical() {
        return prefix + "/nodeResultStatistical";
    }

    /**
     * 查询统计指标列表
     */
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(NodeResultStatistical nodeResultStatistical) {
        startPage();
        List<NodeResultStatistical> list = nodeResultStatisticalService.selectList(nodeResultStatistical);
        return getDataTable(list);
    }

    /**
     * 根据节点key 查询对应的节点明细列表
     *
     * @param nodeResultStatistical
     * @return
     */
    @RequestMapping("/contentList")
    @ResponseBody
    public TableDataInfo contentList(String nodeKey) {

        startPage();
        List<? extends RefereeDatasource> list = nodeResultStatisticalService.contentList(nodeKey);
        if(CollectionUtils.isEmpty(list)){
            list=new ArrayList<>();
        }
        return getDataTable(list);
    }

    /**
     * 新增统计指标
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存统计指标
     */
    @RequiresPermissions("business:nodeResultStatistical:add")
    @Log(title = "统计指标", action = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody NodeResultStatistical nodeResultStatistical) {
        return toAjax(nodeResultStatisticalService.insert(nodeResultStatistical));
    }

    /**
     * 修改统计指标
     */
    @GetMapping("/edit/{id}")
    public AjaxResult edit(@PathVariable("id") Long id) {
        NodeResultStatistical nodeResultStatistical = nodeResultStatisticalService.get(id);

        Object object = JSONObject.toJSON(nodeResultStatistical);

        return this.success(object);
    }

    /**
     * 修改保存统计指标
     */
    @RequiresPermissions("business:nodeResultStatistical:edit")
    @Log(title = "统计指标", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody NodeResultStatistical nodeResultStatistical) {
        return toAjax(nodeResultStatisticalService.update(nodeResultStatistical));
    }

    /**
     * 删除统计指标
     */
    @RequiresPermissions("business:nodeResultStatistical:remove")
    @Log(title = "统计指标", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(nodeResultStatisticalService.deleteByIds(Convert.toStrArray(ids)));
    }


    /**
     * 查看统计指标
     */
    @RequiresPermissions("business:nodeResultStatistical:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id) {
        NodeResultStatistical nodeResultStatistical = nodeResultStatisticalService.get(id);

        Object object = JSONObject.toJSON(nodeResultStatistical);

        return this.success(object);
    }


}
