package com.sccl.modules.business.powermodel.service;

import com.sccl.modules.business.powermodel.entity.PowerModleBase;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;

/**
 * 电量模型基本表(PowerModleBase)表服务接口
 *
 * <AUTHOR>
 * @since 2022-10-19 17:44:41
 */
public interface PowerModleBaseService {



    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    PowerModleBase queryById(Long id);

    /**
     * 分页查询
     *
     * @param powerModleBase 筛选条件
     * @param pageRequest    分页对象
     * @return 查询结果
     */
    Page<PowerModleBase> queryByPage(PowerModleBase powerModleBase, PageRequest pageRequest);

    /**
     * 新增数据
     *
     * @param powerModleBase 实例对象
     * @return 实例对象
     */
    PowerModleBase insert(PowerModleBase powerModleBase);

    /**
     * 修改数据
     *
     * @param powerModleBase 实例对象
     * @return 实例对象
     */
    PowerModleBase update(PowerModleBase powerModleBase);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Long id);

    /**
     * 检查导入excel power_model_init
     * @param content
     * @return
     */
    List<PowerModleBase> checkImportExcel(List<PowerModleBase> content);

    /**
     * 批量插入 初始数据
     * @param content
     * @return
     */
    Integer batchAdd(List<PowerModleBase> content);
}
