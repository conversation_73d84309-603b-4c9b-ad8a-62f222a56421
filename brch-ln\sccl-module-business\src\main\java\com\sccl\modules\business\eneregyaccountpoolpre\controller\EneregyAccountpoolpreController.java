package com.sccl.modules.business.eneregyaccountpoolpre.controller;

import com.alibaba.fastjson.JSONObject;
import com.sccl.common.support.Convert;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.autojob.util.bean.ObjectUtil;
import com.sccl.modules.business.eneregyaccountpoolpre.domain.EneregyAccountpoolpre;
import com.sccl.modules.business.eneregyaccountpoolpre.domain.EneregyAccountpoolpreDto;
import com.sccl.modules.business.eneregyaccountpoolpre.service.IEneregyAccountpoolpreService;
import com.sccl.modules.system.user.domain.User;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 油费汇总单头 信息操作处理
 *
 * <AUTHOR> Yongxiang
 * @date 2022-03-31
 */
@RestController
@RequestMapping("/business/eneregyAccountpoolpre")
public class EneregyAccountpoolpreController extends BaseController
{
    private String prefix = "business/eneregyAccountpoolpre";

	@Autowired
	private IEneregyAccountpoolpreService eneregyAccountpoolpreService;

	@RequiresPermissions("business:eneregyAccountpoolpre:view")

	@GetMapping()
	public String eneregyAccountpoolpre()
	{
	    return prefix + "/eneregyAccountpoolpre";
	}

	/**
	 * 查询油费汇总单头列表
	 */
	//@RequiresPermissions("business:eneregyAccountpoolpre:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(EneregyAccountpoolpre eneregyAccountpoolpre)
	{
		eneregyAccountpoolpre.setDelFlag("0");
		if (!ObjectUtil.isNull(eneregyAccountpoolpre.getCompany()) && -1 == eneregyAccountpoolpre.getCompany()) {
			eneregyAccountpoolpre.setCompany(null);
		}
		if (!ObjectUtil.isNull(eneregyAccountpoolpre.getCountry()) && -1 == eneregyAccountpoolpre.getCountry()) {
			eneregyAccountpoolpre.setCountry(null);
		}
		startPage();
        List<EneregyAccountpoolpre> list = eneregyAccountpoolpreService.selectList(eneregyAccountpoolpre);
		return getDataTable(list);
	}

	/**
	 * 新增油费汇总单头e
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}

	/**
	 * 新增汇总单 - con
	 */
	//@RequiresPermissions("business:eneregyAccountpoolpre:add")
	@Log(title = "油费汇总单头", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody EneregyAccountpoolpreDto eneregyAccountpoolpre)
	{
		User currentUser = getCurrentUser();
		eneregyAccountpoolpre.setInputid(currentUser.getId());
		return eneregyAccountpoolpreService.insertAudit(eneregyAccountpoolpre);
	}

	/**
	 * 修改油费汇总单头
	 */
	@GetMapping("/edit/{pabrid}")
	public AjaxResult edit(@PathVariable("pabrid") Long pabrid)
	{
		EneregyAccountpoolpre eneregyAccountpoolpre = eneregyAccountpoolpreService.get(pabrid);

		Object object = JSONObject.toJSON(eneregyAccountpoolpre);

        return this.success(object);
	}

	/**
	 * 修改保存油费汇总单头
	 */
	@RequiresPermissions("business:eneregyAccountpoolpre:edit")
	@Log(title = "油费汇总单头", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody EneregyAccountpoolpre eneregyAccountpoolpre)
	{
		return toAjax(eneregyAccountpoolpreService.update(eneregyAccountpoolpre));
	}

	/**
	 * 删除汇总单 - con
	 */
	//@RequiresPermissions("business:eneregyAccountpoolpre:remove")
	@Log(title = "油费汇总单头", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{
		return eneregyAccountpoolpreService.myDeleteByIds(Convert.toStrArray(ids));
	}


    /**
     * 查看油费汇总单头
     */
    @RequiresPermissions("business:eneregyAccountpoolpre:view")
    @GetMapping("/view/{pabrid}")
    @ResponseBody
    public AjaxResult view(@PathVariable("pabrid") Long pabrid)
    {
		EneregyAccountpoolpre eneregyAccountpoolpre = eneregyAccountpoolpreService.get(pabrid);

        Object object = JSONObject.toJSON(eneregyAccountpoolpre);

        return this.success(object);
    }

}
