package com.sccl.modules.business.oilcardaccount.domain;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;

import java.util.Date;
import java.math.BigDecimal;


/**
 * 油卡台账表 oil_card_account
 *
 * <AUTHOR>
 * @date 2021-12-20
 */
@Getter
@Setter
public class OilCardAccount extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 所属分公司
     */
    private Long company;
    /**
     * 所属部门
     */
    private Long country;
    /**
     * 油卡数据库ID
     */
    private Long cardId;
    /**
     * 专用油卡管理负责人
     */
    private String cardMaster;
    /**
     * 购买日期
     */
    private Date buyDate;
    /**
     * 购买时间
     */
    private Date buyTime;
    /**
     * 购油量
     */
    private BigDecimal buyQuantity;
    /**
     * 购买油品类型
     */
    private String oilType;
    /**
     * 购油金额 / 充值金额
     */
    private BigDecimal cost;
    /**
     * 油卡余额
     */
    private BigDecimal balance;
    /**
     * 附件
     */
    private String attachment;
    /**
     * 油卡台账类型 1充值 2购油
     */
    private String accountType;

    private String oilCardId;

    private BigDecimal status;

    //购油台账剩余量
    private BigDecimal surplus;

    //购油单价
    private BigDecimal unitprice;

    private String accountnum;
    /**
     * 分公司
     */
    private String companyName;
    /**
     * 责任中心
     */
    private String countryName;
}
