package com.sccl.modules.business.mssaccountprepaid.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;
import java.math.BigDecimal;


/**
 * 关联交易客户代垫及收款表 mss_account_prepaid
 * 
 * <AUTHOR>
 * @date 2021-10-24
 */
public class MssAccountPrepaid extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
    /** 报账id */
    private Long accountId;
    /** 报账明细id */
    private Long accountItemId;
    /** 借方科目代码 */
    private String debitAccountCode;
    /** 借方科目名称 */
    private String debitAccountName;
    /** 税金 */
    private BigDecimal inputTaxSum;
    /** 税金 */
    private BigDecimal taxAdjustSum;
    /** 不含税 */
    private BigDecimal sum;
    /** 单位 */
    private Long companyCode;

	public void setCompanyCode(Long companyCode) {
		this.companyCode = companyCode;
	}
	public Long getCompanyCode() {
		return companyCode;
	}
	public Long getCountry() {
		return country;
	}

	public void setCountry(Long country) {
		this.country = country;
	}

	/** 单位 */
	private Long country;
    /** 余额 */
    private BigDecimal balance;
    /** 发生时间 */
    private Date inputdate;

	private String writeoffInstanceCode;
	private String abstractValue;
	private String billtype;


	public String getWriteoffInstanceCode() {
		return writeoffInstanceCode;
	}

	public void setWriteoffInstanceCode(String writeoffInstanceCode) {
		this.writeoffInstanceCode = writeoffInstanceCode;
	}

	public String getAbstractValue() {
		return abstractValue;
	}

	public void setAbstractValue(String abstractValue) {
		this.abstractValue = abstractValue;
	}

	public String getBilltype() {
		return billtype;
	}

	public void setBilltype(String billtype) {
		this.billtype = billtype;
	}

	public void setAccountId(Long accountId)
	{
		this.accountId = accountId;
	}

	public Long getAccountId() 
	{
		return accountId;
	}

	public void setAccountItemId(Long accountItemId)
	{
		this.accountItemId = accountItemId;
	}

	public Long getAccountItemId() 
	{
		return accountItemId;
	}

	public void setDebitAccountCode(String debitAccountCode)
	{
		this.debitAccountCode = debitAccountCode;
	}

	public String getDebitAccountCode() 
	{
		return debitAccountCode;
	}

	public void setDebitAccountName(String debitAccountName)
	{
		this.debitAccountName = debitAccountName;
	}

	public String getDebitAccountName() 
	{
		return debitAccountName;
	}

	public void setInputTaxSum(BigDecimal inputTaxSum)
	{
		this.inputTaxSum = inputTaxSum;
	}

	public BigDecimal getInputTaxSum() 
	{
		return inputTaxSum;
	}

	public void setTaxAdjustSum(BigDecimal taxAdjustSum)
	{
		this.taxAdjustSum = taxAdjustSum;
	}

	public BigDecimal getTaxAdjustSum() 
	{
		return taxAdjustSum;
	}

	public void setSum(BigDecimal sum)
	{
		this.sum = sum;
	}

	public BigDecimal getSum() 
	{
		return sum;
	}



	public void setBalance(BigDecimal balance)
	{
		this.balance = balance;
	}

	public BigDecimal getBalance() 
	{
		return balance;
	}

	public void setInputdate(Date inputdate)
	{
		this.inputdate = inputdate;
	}

	public Date getInputdate() 
	{
		return inputdate;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("accountId", getAccountId())
            .append("accountItemId", getAccountItemId())
            .append("debitAccountCode", getDebitAccountCode())
            .append("debitAccountName", getDebitAccountName())
            .append("inputTaxSum", getInputTaxSum())
            .append("taxAdjustSum", getTaxAdjustSum())
            .append("sum", getSum())
            .append("companyCode", getCompanyCode())
            .append("balance", getBalance())
            .append("inputdate", getInputdate())
            .toString();
    }
}
