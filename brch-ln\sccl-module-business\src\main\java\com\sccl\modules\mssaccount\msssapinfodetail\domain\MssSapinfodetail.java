package com.sccl.modules.mssaccount.msssapinfodetail.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.math.BigDecimal;


/**
 * 报账回传明细表 MSS_SAPINFODETAIL
 * 
 * <AUTHOR>
 * @date 2019-05-01
 */
public class MssSapinfodetail extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
	private Long msdid;
    public Long getMsdid() {
		return msdid;
	}

	public void setMsdid(Long msdid) {
		this.msdid = msdid;
	}

	/** 外键主表 */
    private Long msmid;
    /** 外围系统子单ID */
    private String othersystemdetailid;
    /** Sap凭证号 */
    private String sapcertificatecode;
    /** 年度 */
    private String accountyear;
    /** 公司代码 */
    private String sapcompanycode;
    /** 借贷标识 选项：1-借，0-贷 */
    private String isdebit;
    /** 行项号 */
    private String sapitemnum;
    /** 特殊总账标识 */
    private String specialtag;
    /** 科目 */
    private String preaccountcode;
    /** 科目名称 */
    private String preaccountname;
    /**  */
    private String status;


	public void setMsmid(Long msmid)
	{
		this.msmid = msmid;
	}

	public Long getMsmid()
	{
		return msmid;
	}

	public void setOthersystemdetailid(String othersystemdetailid)
	{
		this.othersystemdetailid = othersystemdetailid;
	}

	public String getOthersystemdetailid() 
	{
		return othersystemdetailid;
	}

	public void setSapcertificatecode(String sapcertificatecode)
	{
		this.sapcertificatecode = sapcertificatecode;
	}

	public String getSapcertificatecode() 
	{
		return sapcertificatecode;
	}

	public void setAccountyear(String accountyear)
	{
		this.accountyear = accountyear;
	}

	public String getAccountyear() 
	{
		return accountyear;
	}

	public void setSapcompanycode(String sapcompanycode)
	{
		this.sapcompanycode = sapcompanycode;
	}

	public String getSapcompanycode() 
	{
		return sapcompanycode;
	}

	public void setIsdebit(String isdebit)
	{
		this.isdebit = isdebit;
	}

	public String getIsdebit() 
	{
		return isdebit;
	}

	public void setSapitemnum(String sapitemnum)
	{
		this.sapitemnum = sapitemnum;
	}

	public String getSapitemnum() 
	{
		return sapitemnum;
	}

	public void setSpecialtag(String specialtag)
	{
		this.specialtag = specialtag;
	}

	public String getSpecialtag() 
	{
		return specialtag;
	}

	public void setPreaccountcode(String preaccountcode)
	{
		this.preaccountcode = preaccountcode;
	}

	public String getPreaccountcode() 
	{
		return preaccountcode;
	}

	public void setPreaccountname(String preaccountname)
	{
		this.preaccountname = preaccountname;
	}

	public String getPreaccountname() 
	{
		return preaccountname;
	}

	public void setStatus(String status)
	{
		this.status = status;
	}

	public String getStatus() 
	{
		return status;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("msdid", getMsdid())
            .append("msmid", getMsmid())
            .append("othersystemdetailid", getOthersystemdetailid())
            .append("sapcertificatecode", getSapcertificatecode())
            .append("accountyear", getAccountyear())
            .append("sapcompanycode", getSapcompanycode())
            .append("isdebit", getIsdebit())
            .append("sapitemnum", getSapitemnum())
            .append("specialtag", getSpecialtag())
            .append("preaccountcode", getPreaccountcode())
            .append("preaccountname", getPreaccountname())
            .append("status", getStatus())
            .toString();
    }
}
