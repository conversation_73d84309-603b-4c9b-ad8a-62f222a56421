package com.sccl.modules.business.msg.mapper;

import com.sccl.modules.business.msg.domain.Message;
import com.sccl.framework.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 铁塔稽核结果 数据层
 *
 * <AUTHOR>
 * @date 2021-08-16
 */
public interface MsgMapper extends BaseMapper<Message> {
    List<Message> selectListByCityBetween(@Param("city") String city, @Param("from") String from, @Param("to") String to);

    List<Message> selectListByCompanyAndCountryBetween(@Param("company") Long company, @Param("country") Long country, @Param("from") String from, @Param("to") String to);

    List<Message> selectListBetween(@Param("from") String from, @Param("to") String to, @Param("skip") Integer skip, @Param("size") Integer size);

    int countBetween(@Param("from") String from, @Param("to") String to);

    List<Message> selectLatestMessageByTowerId(List<String> towerId);

    List<Message> getLatestMessageByTowerKey(List<Long> towerKeyList);

}