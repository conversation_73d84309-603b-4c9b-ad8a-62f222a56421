package com.sccl.modules.protocolexpiration.ammeterorprotocol.service;

import com.sccl.framework.service.IBaseService;
import com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol;

import java.util.List;

/**
 * 基础数据-电管理（注意该中所有的引用类型，
都是指的power_category_type里面的type_code而非
ID，关注每个字段的注释） 服务层
 * 
 * <AUTHOR>
 * @date 2019-05-18
 */
public interface IProtocolexpirationService extends IBaseService<Ammeterorprotocol> {
    public List<Ammeterorprotocol> selectListBy(Ammeterorprotocol ammeterorprotocol);
	
}
