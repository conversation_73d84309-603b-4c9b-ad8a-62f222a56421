package com.sccl.modules.business.stationaudit.powerhistory;

import com.enrising.dcarbon.audit.AbstractReferee;
import com.enrising.dcarbon.audit.AbstractRefereeDatasourceCreator;
import com.enrising.dcarbon.audit.Auditable;
import com.enrising.dcarbon.audit.RefereeDatasource;
import com.sccl.common.utils.SpringUtil;
import com.sccl.modules.business.stationaudit.msshistory.MssHistory;
import com.sccl.modules.business.stationaudit.msshistory.MssHistroyReferee;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;
import com.sccl.modules.mssaccount.mssaccountbill.mapper.MssAccountbillMapper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PowerHistoryCreator extends AbstractRefereeDatasourceCreator {
    @Override
    public Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> collect(Auditable auditable) {
        //从Spring上下文取得mapper
        MssAccountbillMapper mapper = SpringUtil.getBean(MssAccountbillMapper.class);
        //可能上下文没有这个mapper
        if (mapper == null) {
            return null;
        }
        MssAccountbill accountBill = (MssAccountbill) auditable;
        //根据报账单号 获取 stationId最大 的 基站站址编号
        String StationCode = mapper.getStationCode(accountBill);

        //根据基站站址编号 查询最近6个月的 站址电量历史
        //todo: 过去6个月电量历史
        List<PowerHistory> historys = mapper.getHistroyForPower(StationCode, 6);

        historys.stream().filter(powerHistory -> powerHistory!=null)
            .forEach(mssHistory -> mssHistory.setNodetype(2));


        if (historys.size()==0) {
            PowerHistory history = new PowerHistory();
            history.setNodetype(2);
            history.setAuditmsg("当前站址没有电量历史在过去6月");
            historys.add(history);
        }
        Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> datasource = new HashMap<>();
        //可以添加多种不同类型的评判数据
        datasource.put(PowerHistory.class,new ArrayList<>(historys));

        return datasource;
    }

    @Override
    public AbstractReferee getReferee() {
        return new PowerHistoryReferee();
    }

}
