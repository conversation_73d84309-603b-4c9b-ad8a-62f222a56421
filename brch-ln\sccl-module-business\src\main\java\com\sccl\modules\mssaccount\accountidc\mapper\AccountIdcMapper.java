package com.sccl.modules.mssaccount.accountidc.mapper;

import com.sccl.modules.business.stationinfo.domain.StationInfo;
import com.sccl.modules.mssaccount.accountidc.domain.AccountIdc;
import com.sccl.framework.mapper.BaseMapper;

import java.util.List;

/**
 * 推送数据平台IDC电量电费 数据层
 * 
 * <AUTHOR>
 * @date 2020-06-22
 */
public interface AccountIdcMapper extends BaseMapper<AccountIdc>
{

	List<AccountIdc> getByBillid(Long billid);
    List<AccountIdc> getBypcid(Long pcid);
    StationInfo getStationInfoIdc(Long stationId);
}