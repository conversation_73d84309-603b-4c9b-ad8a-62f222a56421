package com.sccl.modules.business.toweraccount.domain;

import com.sccl.framework.web.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


/**
 * 铁塔接口表 toweraccount
 *
 * <AUTHOR>
 * @date 2021-08-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TowerData extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private String towerId;                  // id : 109850
    private String upTime;                   // 上传时间
    private String city;                     // 地市 : 眉山市
    private String district;                 // 区县 : 东坡区
    private String TowerSiteCode;            // 铁塔站址编码 : 511402908000000012
    private String TowerSiteName;            // 铁塔站址名称 : 洞子口R南林花园-MS314-MS-C-B
    private String ShardOperator;            // 共享运营商 : 电信
    private String PowerSupply;              // 供电方式 : 直供
    private String PayAccountNum;            // 缴费户号 : **********
    private String MeterCode;                // 电表编码 : **********
    private String UseStartTime;             // 用电起时间 : 2021/4/1
    private String UseEndTime;               // 用电止时间 : 2021/4/30
    private String UseStartDegree;           // 用电起度 : 87134
    private String UseEndDegree;             // 用电止度 : 90057
    private String Magnification;            // 倍率 : 1
    private String PowerLoss;                // 电损 : 0
    private String OtherFee;                 // 其他费用 : 0    private Integer EnerguUsed;
    private String EnergyUsed;               // 用电量 : 2923
    private String UnitPrice;                // 单价 : 0.********
    private String ActualPay;                // 实际支付电费 : 1798.23
    private String ifUseBranchMetering;      // 是否使用分路计量 : 否
    private String ApportionmentRatio;       // 分摊比例 : 100
    private String AmountToShare;            // 分摊金额 : 1798.23
    private String TaxFactor;                // 税负因子 : 1
    private String TelecomBillingAmount;     // 电信开票金额 : 1798.23
    private String SystemProtocolNUmber;     // 电信能耗系统协议编号 : MS-TT-DP-00116
    private String TelecomSiteName;          // 电信站址名称 : 洞子口R南林花园-MS314-MS-C-B
    private String Annex;                    // 附件 :
    private String TelecomTax;               // 电信税款 :
    private String TelecomPrice;             // 电信价款 :
    private String TelecomAudit;             // 电信稽核 :
    private String AuditTime;                // 稽核时间 :
    private String status;                   // 状态：0：无效，1：最新，2：已修改，3已处理
    private String quoteId;                  // 引用ID
    private String auditName;                // 稽核人员
    private String auditTimeBk;              // 稽核人员稽核时间
    private Long company;                // 电信对应单位
    private Long country;               // 电信对应部门
    private Long compareid;            //对应电表
    private Date upTimeStart;         //start
    private Date upTimeEnd;            //end
    private String isHighRailway;
    private String reimbursementWay;
    private String remark;

    public String getMeterCode() {
        return "转供".equals(PowerSupply) ? MeterCode : PayAccountNum;
    }


}
