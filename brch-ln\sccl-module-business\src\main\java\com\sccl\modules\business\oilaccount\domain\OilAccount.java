package com.sccl.modules.business.oilaccount.domain;

import com.sccl.common.constant.Constants;
import com.sccl.common.constant.ExcelColumn;
import com.sccl.common.constant.enums.OilCategoryEnum;
import com.sccl.common.constant.enums.OilTypeEnum;
import com.sccl.common.constant.enums.TicketTaxRateTypeEnum;
import com.sccl.common.lang.StringUtils;
import com.sccl.common.utils.BigDecimlUtil;
import com.sccl.framework.service.IdGenerator;
import com.sccl.modules.common.domain.BaseAccount;
import com.sccl.modules.common.domain.ConstantCheck;
import com.sccl.modules.system.user.domain.User;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.hibernate.validator.constraints.Length;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class OilAccount extends BaseAccount {
    private static final long serialVersionUID = 1L;

    private Byte oilAccountType;

    @ExcelColumn(value = "期号*", col = 1)
    private String accountNo;

    @ExcelColumn(value = "用能主体*", col = 2)
    private String oilUseBody;

    @ExcelColumn(value = "类型*", col = 4)
    private String oilImportType;

    private Byte oilType;

    @ExcelColumn(value = "用油类别*", col = 10)
    private String oilImportCategory;

    private Byte oilCategory;

    @ExcelColumn(value = "用量（L）*", col = 5)
    private BigDecimal oilAmount;

    private BigDecimal unitPrice;

    @ExcelColumn(value = "专票含税金额（元）*", col = 7)
    private BigDecimal taxTicketMoney = BigDecimal.ZERO;

    @ExcelColumn(value = "普票含税金额（元）*", col = 6)
    private BigDecimal ticketMoney = BigDecimal.ZERO;

    @ExcelColumn(value = "其他（元）", col = 9)
    private BigDecimal otherFee = BigDecimal.ZERO;

    private BigDecimal totalMoney = BigDecimal.ZERO;

    private BigDecimal prepayMoney;

    private BigDecimal paidMoney = BigDecimal.ZERO;

    @ExcelColumn(value = "费用发生日*", col = 3)
    private String feeStartDate;

    private BigDecimal taxAmount = BigDecimal.ZERO;

    @ExcelColumn(value = "专票税率*", col = 8)
    private BigDecimal taxImportRate;


    @ExcelColumn(value = "备注", col = 11)
    @Length(max = 500, message = "备注过长")
    private String remark;

    /**
     * 导入校验
     *
     * @return 校验结果
     */
    public String checkEmpty(boolean isImport) {
        // 校验结果
        StringBuilder result = new StringBuilder();
        // 期号校验
        ConstantCheck.accountNoCheck(this.accountNo, result);
        // 用能主体校验
        if (StringUtils.isEmpty(this.oilUseBody) || this.oilUseBody.length() > 100) {
            result.append("用能主体为空或过长，");
        }
        // 费用发生日校验
        ConstantCheck.feeStartDateCheck(this.accountNo, this.feeStartDate, result);
        // 用量校验
        if (null == this.oilAmount || this.oilAmount.compareTo(BigDecimal.ZERO) < 0) {
            result.append("用油量为空或为负数，");
        }
        // 普票含税金额校验、专票含税金额校验、专票税率校验
        ConstantCheck.taxTicketCheck(this.ticketMoney, this.taxTicketMoney, this.taxImportRate, isImport, result);
        // 用油类别校验
        if (StringUtils.isEmpty(this.oilImportCategory) ||
                !OilCategoryEnum.getInfoList().contains(this.oilImportCategory)) {
            result.append("用油类别为空或不正确，");
        }
        // 用油类型校验
        if (StringUtils.isEmpty(this.oilImportType) ||
                !OilTypeEnum.getInfoList().contains(this.oilImportType)) {
            result.append("用油类型为空或不正确，");
        }
        // 备注校验
        ConstantCheck.checkRemark(this.otherFee, this.remark, result);
        return result.toString();
    }

    /**
     * 导入类转换
     *
     * @param accounts
     */
    public static void convertOilAccount(List<OilAccount> accounts, User user) {
        for (OilAccount  account: accounts) {
            String percent = BigDecimlUtil.convertPercent(account.getTaxImportRate(), 2);
            account.setTaxRate(TicketTaxRateTypeEnum.getCodeByType(percent).byteValue());

            // 实缴费用
            account.setPaidMoney(account.getTicketMoney().add(account.getTaxTicketMoney()
            .add(account.getOtherFee())).setScale(2, RoundingMode.HALF_UP));
            // 单价计算
            account.setUnitPrice(account.getPaidMoney().divide(account.getOilAmount(),
                2, BigDecimal.ROUND_HALF_UP));
            // 专票税额
            account.setTaxAmount(account.getTaxTicketMoney().multiply(account.getTaxImportRate())
            .setScale(2, RoundingMode.HALF_UP));
            account.setOilAccountType(Constants.OIL_TYPE);
            account.assignmentOilAccount(account, user);
        }
    }

    /**
     * 实体类赋值
     * @param account
     * @param user
     */
    public void assignmentOilAccount(OilAccount account, User user) {
        if (StringUtils.isNotEmpty(user.getCompanies())) {
            account.setCompany(Long.parseLong(user.getCompanies().get(0).getId()));
        }
        if (StringUtils.isNotEmpty(user.getDepartments())) {
            account.setCountry(Long.parseLong(user.getDepartments().get(0).getId()));
            account.setOrgId(Long.parseLong(user.getDepartments().get(0).getId()));
        }
        account.setId(IdGenerator.getNextId());
        account.setInputerId(String.valueOf(user.getId()));
        if (account.getOilAccountType() == 1) {
            account.setOilType(OilTypeEnum.getCodeByType(account.getOilImportType()).byteValue());
            account.setOilCategory(OilCategoryEnum.getCodeByType(account.getOilImportCategory()).byteValue());
        }
    }

    /**
     * 和前端比较是否有修改
     * @param newAccount
     * @param oldAccount
     * @return
     */
    public static boolean isEqual(OilAccount newAccount, OilAccount oldAccount) {
        boolean isCompare = true;
        // 把小数位设定成和数据一样
        newAccount.setTicketMoney(newAccount.getTicketMoney().setScale(2, RoundingMode.HALF_UP));
        newAccount.setTaxTicketMoney(newAccount.getTaxTicketMoney().setScale(2, RoundingMode.HALF_UP));
        newAccount.setOilAmount(newAccount.getOilAmount().setScale(2, RoundingMode.HALF_UP));
        newAccount.setUnitPrice(newAccount.getUnitPrice().setScale(2, RoundingMode.HALF_UP));
        newAccount.setOtherFee(newAccount.getOtherFee().setScale(2, RoundingMode.HALF_UP));
        newAccount.setPaidMoney(newAccount.getPaidMoney().setScale(2, RoundingMode.HALF_UP));
        newAccount.setTaxAmount(newAccount.getTaxAmount().setScale(2, RoundingMode.HALF_UP));
        if (newAccount.getOilAccountType() == 1) {
            isCompare = new EqualsBuilder()
                    .append(newAccount.getOilUseBody(), oldAccount.getOilUseBody())
                    .append(newAccount.getFeeStartDate(), oldAccount.getFeeStartDate())
                    .append(newAccount.getOilAmount(), oldAccount.getOilAmount())
                    .append(newAccount.getUnitPrice(), oldAccount.getUnitPrice())
                    .append(newAccount.getTicketMoney(), oldAccount.getTicketMoney())
                    .append(newAccount.getTaxTicketMoney(), oldAccount.getTaxTicketMoney())
                    .append(newAccount.getTaxRate(), oldAccount.getTaxRate())
                    .append(newAccount.getTaxAmount(), oldAccount.getTaxAmount())
                    .append(newAccount.getOtherFee(), oldAccount.getOtherFee())
                    .append(newAccount.getPaidMoney(), oldAccount.getPaidMoney())
                    .append(newAccount.getOilType(), oldAccount.getOilType())
                    .append(newAccount.getOilCategory(), oldAccount.getOilCategory())
                    .append(newAccount.getRemark(), oldAccount.getRemark())
                    .isEquals();
        }
        if (newAccount.getOilAccountType() == 2) {
            isCompare = new EqualsBuilder()
                    .append(newAccount.getOilUseBody(), oldAccount.getOilUseBody())
                    .append(newAccount.getFeeStartDate(), oldAccount.getFeeStartDate())
                    .append(newAccount.getOilAmount(), oldAccount.getOilAmount())
                    .append(newAccount.getUnitPrice(), oldAccount.getUnitPrice())
                    .append(newAccount.getPaidMoney(), oldAccount.getPaidMoney())
                    .append(newAccount.getRemark(), oldAccount.getRemark())
                    .isEquals();
        }
        return isCompare;
    }
}
