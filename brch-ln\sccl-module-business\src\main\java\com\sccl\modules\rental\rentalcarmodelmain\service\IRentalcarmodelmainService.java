package com.sccl.modules.rental.rentalcarmodelmain.service;

import java.util.List;

import com.sccl.framework.service.IBaseService;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.rental.rentalcarmodelmain.domain.Rentalcarmodelmain;
import com.sccl.modules.uniflow.common.WFModel;

/**
 * 车辆 （车型主单） 服务层
 * 
 * <AUTHOR>
 * @date 2019-08-23
 */
public interface IRentalcarmodelmainService extends IBaseService<Rentalcarmodelmain>
{

    /**
     * 流程引擎回调（通过MQ）
     *
     * @param wfModel 流程消息对象
     */
    public void uniflowCallBack(WFModel wfModel);

    List<Rentalcarmodelmain> selectListByIds(String[] toStrArray);

    AjaxResult saveRentalcarmodelmain(Rentalcarmodelmain rentalcarmodelmain);

    int deleteAndItemByIds(String[] toStrArray);
}
