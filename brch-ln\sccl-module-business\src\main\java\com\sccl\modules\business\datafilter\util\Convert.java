package com.sccl.modules.business.datafilter.util;

import com.sccl.common.lang.StringUtils;
import com.sccl.common.utils.RegexUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.IOUtils;
import sun.misc.BASE64Encoder;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.security.MessageDigest;
import java.text.Format;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

@Slf4j
public class Convert {
    //日期型正则
    private static Pattern date1 = Pattern.compile("^\\d{4}/\\d{1,2}/\\d{1,2}");
    private static Pattern date2 = Pattern.compile("^\\d{4}-\\d{1,2}-\\d{1,2}");
    private static Pattern date3 = Pattern.compile("^\\d{4}.\\d{1,2}.\\d{1,2}");

    //数字正则表达
    private static Pattern data = Pattern.compile("^(\\-|\\+)?\\d+(\\.\\d+)?$");
    //小数正则式表达
    private static Pattern decimal = Pattern.compile("^-?(\\d+)(\\.\\d+)$");

    /**
     * 将二进制字符串转为byte数组
     *
     * @param str 二进制字符串
     * @return
     */
    public static byte[] FromBase64String(String str) {
        if ("".equalsIgnoreCase(str) || str == null) {
            System.out.println("Str is empty or null");
            return null;
        }
        byte[] res = Base64.decodeBase64(str);
        return res;
    }

    /**
     * 将二进制编码转为GBK编码的字符串
     *
     * @param data 二进制数组
     * @return 字符串
     */
    public static String FromBase64ByteArray(byte[] data) {
        if (data.length == 0 || data == null) {
            System.out.println("data is empty or null");
            return null;
        }
        String res = null;
        try {
            res = new String(data, "GBK");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return res;
    }

    /**
     * 将byte数组写到指定路径文件
     *
     * @param path 路径
     * @param data 数据
     */
    public static void Bytes2File(String path, byte[] data) {
        try {
            OutputStream out = new FileOutputStream(new File(path));
            out.write(data);
            out.flush();
            out.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 使用ZIP算法进行解压.
     *
     * @param sourceZipFileBytes ZIP文件字节数组.
     * @return 解压后的文件Map集合.
     * @throws Exception 解压过程中可能发生的异常，若发生异常，返回Map集合长度为0.
     */
    public static Map<String, byte[]> decompressByZip(byte[] sourceZipFileBytes) {
        // 变量定义.
        String zipEntryName = null;
        ZipEntry singleZipEntry = null;
        ZipInputStream sourceZipZis = null;
        BufferedInputStream sourceZipBis = null;
        ByteArrayInputStream sourceZipBais = null;
        Map<String, byte[]> targetFilesFolderMap = null;

        try {
            // 解压变量初始化.
            targetFilesFolderMap = new HashMap<>();
            sourceZipBais = new ByteArrayInputStream(sourceZipFileBytes);
            sourceZipBis = new BufferedInputStream(sourceZipBais);
            sourceZipZis = new ZipInputStream(sourceZipBis);
            // 条目解压缩至Map中.
            while ((singleZipEntry = sourceZipZis.getNextEntry()) != null) {
                zipEntryName = singleZipEntry.getName();
                targetFilesFolderMap.put(zipEntryName, IOUtils.toByteArray(sourceZipZis));
            }
            sourceZipBais.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return targetFilesFolderMap;
    }

    /**
     * 读取txt文件到字符串
     *
     * @param path 路径
     * @return 读取到的字符串
     */
    public static String readTextToString(String path) {
        File f = new File(path);
        try {
            FileInputStream in = new FileInputStream(path);

            byte[] b = new byte[(int) f.length()];
            in.read(b);
            String temp = new String(b);
            return temp;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 尝试对日期字符串进行转化，可指定尝试的转化格式
     *
     * @param dateString 日期字符串
     * @param formats    日期字符串可能的格式，不指定时会使用常用的格式对其进行尝试转化
     * @return java.util.Date
     * <AUTHOR> Yongxiang
     * @date 2022/1/12 9:54
     */
    public static Date convertDate(String dateString, String... formats) {
        if (StringUtils.isEmpty(dateString)) {
            log.error("日期字符串为null，转换失败");
            return null;
        }
        if (formats == null || formats.length == 0) {
            log.warn("日期格式指定为空，将尝试使用常见日期格式对其转化");
            formats = new String[]{"yyyy/MM/dd", "yyyy-MM-dd", "yyyy.MM.dd", "yyyyMMdd", "yyyy-MM-dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss", "yyyy.MM.dd HH:mm:ss"};
        }
        Date parse = null;
        for (String format : formats) {
            try {
                if ("yyyyMMdd".equals(format) && !RegexUtil.isMatch(dateString, "^\\d{8}$")) {
                    continue;
                }
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
                parse = simpleDateFormat.parse(dateString);
                if (parse != null) {
                    //log.info("使用{}格式对日期字符串：{}转化成功", format, dateString);
                    break;
                }
            } catch (ParseException e) {
                log.debug("尝试使用{}格式对日期字符串：{}进行转化失败，将使用下一个格式尝试转化", format, dateString);
            }
        }
        if (parse == null) {
            log.error("日期字符串：{}尝试转化失败", dateString);
            return null;
        }
        return parse;
    }

    public static void main(String[] args) {
        String d = "20211225";
        Date date = convertDate(d);
        System.out.println(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date));
    }

    /**
     * 读取指定路径下的ZIP二进制流的base64编码文件，在内存解压并返回解压后的数据
     *
     * @param path 路径
     * @return 数据
     */
    public static ArrayList<String> TxtZipByteCodesToString(String path) {
        String by = readTextToString(path);
        byte[] bytes = FromBase64String(by);
        Map<String, byte[]> map = decompressByZip(bytes);
        ArrayList<String> res = new ArrayList<>();
        Set<String> set = map.keySet();
        Iterator<String> it = set.iterator();
        while (it.hasNext()) {
            res.add(FromBase64ByteArray(map.get(it.next())));
        }
        return res;
    }

    /**
     * 对字符串进行SHA1加密处理
     *
     * @param str 要加密的字符串
     * @return java.lang.String
     * <AUTHOR> Yongxiang
     * @date 2021/10/12 15:30
     */
    public static String getSha1(String str) throws Exception {
        MessageDigest sha = null;
        try {
            sha = MessageDigest.getInstance("SHA");
        } catch (Exception e) {
            System.out.println(e.toString());
            e.printStackTrace();
            return "";
        }
        byte[] strByte = str.getBytes("UTF-8");
        byte[] md5Bytes = sha.digest(strByte);
        StringBuilder hexValue = new StringBuilder();
        for (byte md5Byte : md5Bytes) {
            int val = ((int) md5Byte) & 0xff;
            if (val < 16) {
                hexValue.append("0");
            }
            hexValue.append(Integer.toHexString(val));
        }
        return hexValue.toString();
    }

    public static String getSign(String signType, String accessKeyId, String accessKeySecret, String timestamp, String params) {
        String sign = "";

        try {
            if (params.length() > 512) {
                params = params.substring(0, 512);
            }

            String stringToSign = accessKeyId + accessKeySecret + timestamp + params;
            Mac mac = Mac.getInstance("Hmac" + signType);
            mac.init(new SecretKeySpec(accessKeySecret.getBytes("UTF-8"), "Hmac" + signType));
            byte[] signData = mac.doFinal(stringToSign.getBytes("UTF-8"));
            sign = (new BASE64Encoder()).encode(signData);
        } catch (Exception var9) {
            var9.printStackTrace();
        }

        return sign;
    }

    /**
     * @param by zip的二进制字符流
     * @return java.util.ArrayList<java.lang.String>
     * @description 将zip的二进制字符流内存解压成GBK字符串
     * <AUTHOR> Yongxiang
     * @date 2021/7/29 15:14
     */
    public static ArrayList<String> stringZipByteCodesToString(String by) {
        byte[] bytes = FromBase64String(by);
        Map<String, byte[]> map = decompressByZip(bytes);
        ArrayList<String> res = new ArrayList<>();
        Set<String> set = map.keySet();
        Iterator<String> it = set.iterator();
        while (it.hasNext()) {
            res.add(FromBase64ByteArray(map.get(it.next())));
        }
        return res;
    }

    /**
     * 将Date型时间转化为字符串型yyyy-MM-dd HH:mm:ss
     *
     * @param time Date型时间
     */
    public static String setTime(Date time) {
        Format format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return format.format(new Date());
    }

    /**
     * @param list
     * @return java.lang.String[]
     * @description 字符串型list转字符串数组
     * <AUTHOR> Yongxiang
     * @date 2021/8/2 10:21
     */
    public static String[] listToStringArr(List<String> list) {
        String[] strings = new String[list.size()];
        for (int i = 0; i < list.size(); i++) {
            strings[i] = list.get(i);
        }
        return strings;
    }

    public static String convertFileToBase64(String imgPath) {
        byte[] data = null;
        // 读取图片字节数组
        try {
            InputStream in = new FileInputStream(imgPath);
            System.out.println("文件大小（字节）=" + in.available());
            data = new byte[in.available()];
            in.read(data);
            in.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        BASE64Encoder base64Encodera = new BASE64Encoder();
        return base64Encodera.encode(data);
    }

    /**
     * @param dateStr 日期字符串
     * @param addnum  增加的天数，为负表示减少天数
     * @param format  返回的格式化日期字符串
     * @return java.lang.String
     * @description 对日期字符串加减addnum天
     * <AUTHOR> Yongxiang
     * @date 2021/8/5 14:56
     */
    public static String addDate(String dateStr, int addnum, String format) {
        if (StringUtils.isEmpty(dateStr)) {
            return "";
        }
        Matcher matcher1 = date1.matcher(dateStr);
        Matcher matcher2 = date2.matcher(dateStr);
        Matcher matcher3 = date3.matcher(dateStr);
        String match1 = "yyyy/MM/dd";
        String match2 = "yyyy-MM-dd";
        String match3 = "yyyy.MM.dd";
        if (!matcher1.matches() && !matcher2.matches() && !matcher3.matches()) {
            System.out.println("日期:" + dateStr + "不合法，转换失败！");
            return "";
        }

        Date date = null;
        SimpleDateFormat simpleDateFormat = null;
        if (matcher1.matches()) {
            simpleDateFormat = new SimpleDateFormat(match1);
            try {
                date = simpleDateFormat.parse(dateStr);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        } else if (matcher2.matches()) {
            simpleDateFormat = new SimpleDateFormat(match2);
            try {
                date = simpleDateFormat.parse(dateStr);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        } else if (matcher3.matches()) {
            simpleDateFormat = new SimpleDateFormat(match3);
            try {
                date = simpleDateFormat.parse(dateStr);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }

        SimpleDateFormat fm = new SimpleDateFormat(format);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, addnum);
        return fm.format(calendar.getTime());

    }


}
