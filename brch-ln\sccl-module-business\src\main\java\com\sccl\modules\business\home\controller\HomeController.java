package com.sccl.modules.business.home.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.home.service.HomeService;
@RestController
@RequestMapping("/business/home")
public class HomeController extends BaseController{
	
	@Autowired
	private HomeService homeService;
	
	@RequestMapping("/getAmmeterorRank")
	@ResponseBody
	public TableDataInfo getAmmeterorRank()
	{
		startPage();
        List<Map<String, Object>> list = homeService.getAmmeterorRank();
		return getDataTable(list);
	}
	
	@RequestMapping("/getUserTask")
	@ResponseBody
	public TableDataInfo getUserTask()
	{
		startPage();
        List<Map<String, Object>> list = homeService.getUserTask();
		return getDataTable(list);
	}
	
	@RequestMapping("/getAlarmCount")
	@ResponseBody
	public Map<String, Object> getAlarmCount()
	{
        Map<String, Object> map = homeService.getAlarmCount();
		return map;
	}

	/**
	 * 查询首页数据
	 * @return 首页数据
	 */
	@GetMapping("/getHomePageData")
	public HashMap<String, Object> getHomePageData() {
		return homeService.getHomePageData();
	}
}
