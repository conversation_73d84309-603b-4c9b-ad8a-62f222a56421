package com.sccl.modules.business.stationaudit.pcomparequtoa;

import com.enrising.dcarbon.audit.AbstractRefereeContent;
import com.enrising.dcarbon.audit.RefereeDatasource;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class quotaCompareContent extends AbstractRefereeContent implements RefereeDatasource {
    private Long billId;
    private Long pcid;

    /**
     * 对应资源系统局站编码
     */
    private String resstationcode;

    private String startdate;
    private String enddate;
    /**
     * 报账平均电量
     */
    private BigDecimal avgForBill;
    /**
     * 网关统计平均dianl
     */
    private BigDecimal avgForGateway;

    /**
     * 评判信息
     */
    private String exmsg;

}
