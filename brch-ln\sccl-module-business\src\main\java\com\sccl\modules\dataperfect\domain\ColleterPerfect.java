package com.sccl.modules.dataperfect.domain;

public class ColleterPerfect {
    private Long id;
    private String stationcode;
    private String type;
    private String avgpower;
    private String budget;
    private Integer delFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getStationcode() {
        return stationcode;
    }

    public void setStationcode(String stationcode) {
        this.stationcode = stationcode;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAvgpower() {
        return avgpower;
    }

    public void setAvgpower(String avgpower) {
        this.avgpower = avgpower;
    }

    public String getBudget() {
        return budget;
    }

    public void setBudget(String budget) {
        this.budget = budget;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }
}

