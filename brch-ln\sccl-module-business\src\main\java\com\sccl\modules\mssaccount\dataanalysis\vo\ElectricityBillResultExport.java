package com.sccl.modules.mssaccount.dataanalysis.vo;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sccl.framework.aspectj.lang.annotation.Excel;
import com.sccl.framework.serialize.PercentageBigDecimalSerialize;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 报账单统计分析-导出
 */
@Data
public class ElectricityBillResultExport {

    /**
     * 所属公司
     */
    @Excel(name = "所属公司")
    private String orgName;

    /**
     * 时间期号
     */
    @Excel(name = "时间期号")
    private String index;


    /**
     * 总用电量(千瓦时)
     */
    @Excel(name = "电量(千瓦时)")
    private BigDecimal kwh;

    /**
     * 同比-总用电量(千瓦时)
     */
    @Excel(name = "电量同比")
    @JsonSerialize(using = PercentageBigDecimalSerialize.class)
    private String yearOverYearTotalKWh;

    /**
     * 环比-总用电量(千瓦时)
     */
    @Excel(name = "电量环比")
    @JsonSerialize(using = PercentageBigDecimalSerialize.class)
    private String yearOverMonthTotalKWh;


    /**
     * 平均电单价
     */
    @Excel(name = "单价(元)")
    private BigDecimal unitPrice;

    /**
     * 同比-平均电单价
     */
    @Excel(name = "单价同比")
    @JsonSerialize(using = PercentageBigDecimalSerialize.class)
    private String yearOverYearUnitPrice;

    /**
     * 环比-平均电单价
     */
    @Excel(name = "单价环比")
    @JsonSerialize(using = PercentageBigDecimalSerialize.class)
    private String yearOverMonthUnitPrice;

    /**
     * 总电费(元)
     */
    @Excel(name = "电费(元)")
    private BigDecimal electricityCost;

    /**
     * 同比-总电费(元)
     */
    @Excel(name = "电费同比")
    @JsonSerialize(using = PercentageBigDecimalSerialize.class)
    private String yearOverYearElectricityCost;

    /**
     * 环比-总电费(元)
     */
    @Excel(name = "电费环比")
    @JsonSerialize(using = PercentageBigDecimalSerialize.class)
    private String yearOverMonthElectricityCost;

}