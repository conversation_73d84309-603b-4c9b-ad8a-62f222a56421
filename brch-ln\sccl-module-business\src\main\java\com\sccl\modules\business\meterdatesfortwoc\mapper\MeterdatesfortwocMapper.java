package com.sccl.modules.business.meterdatesfortwoc.mapper;

import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.meterdatesfortwoc.domain.Meterdatesfortwoc;
import com.sccl.modules.business.twoc.domain.TwoCFlag;
import com.sccl.modules.mssaccount.mssinterface.domain.MeterInfoDb;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 双碳接口同步电实际用电量数据 数据层
 *
 * <AUTHOR>
 * @date 2023-04-14
 */
public interface MeterdatesfortwocMapper extends BaseMapper<Meterdatesfortwoc> {


    Integer countForMeterinfoAllFail();

    List<? extends TwoCFlag> selectMeterInfo(@Param("id") Long id, @Param("offset") int offset,
                                             @Param("size") int syncsum);

    int updateForMeterInfoAll(Meterdatesfortwoc infoDb);

    int insertListMeterinfoDb(@Param("list") List<MeterInfoDb> dbs);

    int updateGroupTypeBitch(@Param("time") String time);
}