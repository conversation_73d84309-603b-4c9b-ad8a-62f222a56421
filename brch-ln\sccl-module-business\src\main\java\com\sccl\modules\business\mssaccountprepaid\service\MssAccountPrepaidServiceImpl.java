package com.sccl.modules.business.mssaccountprepaid.service;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.business.mssaccountprepaid.mapper.MssAccountPrepaidMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sccl.modules.business.mssaccountprepaid.domain.MssAccountPrepaid;

import java.math.BigDecimal;


/**
 * 关联交易客户代垫及收款 服务层实现
 * 
 * <AUTHOR>
 * @date 2021-10-24
 */
@Service
public class MssAccountPrepaidServiceImpl extends BaseServiceImpl<MssAccountPrepaid> implements IMssAccountPrepaidService
{
    @Autowired
    MssAccountPrepaidMapper mssAccountPrepaidMapper;
    @Override
    public BigDecimal computeBalance(MssAccountPrepaid mssAccountPrepaid) {
        BigDecimal   balance;
        String flag=mssAccountPrepaidMapper.ifBalanceorg(mssAccountPrepaid);
        if (flag.equals("0"))
            balance=mssAccountPrepaidMapper.computeBalance(mssAccountPrepaid);
        else
            balance=mssAccountPrepaidMapper.computeBalanceorg(mssAccountPrepaid);
        if (balance==null)
            balance=new BigDecimal(0);
        return balance;

    }
}
