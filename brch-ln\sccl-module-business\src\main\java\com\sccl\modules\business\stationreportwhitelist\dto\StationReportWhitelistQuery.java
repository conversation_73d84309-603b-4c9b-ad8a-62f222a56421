package com.sccl.modules.business.stationreportwhitelist.dto;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 2024/4/9 16:23
 * @describe 一站多表白名单
 */
@Getter
@Setter
public class StationReportWhitelistQuery extends Page<StationReportWhitelistQuery> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 白名单类型 1:一表多站 2:一站多表 3:单价
     */
    private String whitelistType;

    /**
     * 项目名称
     */
    private String projectname;

    /**
     * 电表编号
     */
    private String meterCode;

    /**
     * 局站名称
     */
    private String stationName;

    /**
     * 分公司
     */
    private Long company;

    /**
     * 部门
     */
    private Long country;

    /**
     * 流程状态
     * 0:草稿、1:流程中 2:已完成 3:修改中 4:修改流程中 5:移除
     */
    private Integer billStatus;

    /**
     * 白名单类型1:一表多站 2:一站多表 3:单价
     */
    private String type;

    /**
     * 是否导出全部
     */
    private boolean isExportAll;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
