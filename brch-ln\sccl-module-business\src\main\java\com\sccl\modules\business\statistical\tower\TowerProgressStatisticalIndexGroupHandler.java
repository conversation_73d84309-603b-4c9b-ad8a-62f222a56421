package com.sccl.modules.business.statistical.tower;

import com.sccl.common.utils.DateUtils;
import com.sccl.modules.business.statistical.framework.AbstractStatisticalIndexGroupHandler;

import java.util.Date;

/**
 * 铁塔对账进度指标处理器
 *
 * <AUTHOR>
 * @Date 2022/10/31 9:31
 * @Email <EMAIL>
 */
public class TowerProgressStatisticalIndexGroupHandler extends AbstractStatisticalIndexGroupHandler {
    private final String city;
    private Date pushDate;
    private String handleType;
    private Integer pushCount;
    private final boolean isIn;

    public TowerProgressStatisticalIndexGroupHandler(String city, Date pushDate, Integer pushCount, String handleType) {
        this.city = city;
        this.pushDate = pushDate;
        this.pushCount = pushCount;
        this.handleType = handleType;
        isIn = true;
    }

    public TowerProgressStatisticalIndexGroupHandler(String city) {
        this.city = city;
        isIn = false;
    }

    @Override
    public String getGroupName() {
        return "铁塔数据同步实时进度";
    }

    @Override
    public String getOwnerAs() {
        return isIn ? String.format("%s_%s_%d_%s", city, DateUtils.formatDate(pushDate), pushCount, handleType) : ".*" + city + ".*";
    }


}
