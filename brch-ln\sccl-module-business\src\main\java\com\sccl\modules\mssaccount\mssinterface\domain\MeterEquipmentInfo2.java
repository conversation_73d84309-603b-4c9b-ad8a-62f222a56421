package com.sccl.modules.mssaccount.mssinterface.domain;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 计量设备同步模板
 */

public class MeterEquipmentInfo2 implements Serializable {
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getCountyCode() {
        return countyCode;
    }

    public void setCountyCode(String countyCode) {
        this.countyCode = countyCode;
    }

    public String getCountyName() {
        return countyName;
    }

    public void setCountyName(String countyName) {
        this.countyName = countyName;
    }

    public String getEnergyMeterCode() {
        return energyMeterCode;
    }

    public void setEnergyMeterCode(String energyMeterCode) {
        this.energyMeterCode = energyMeterCode;
    }

    public String getEnergyMeterName() {
        return energyMeterName;
    }

    public void setEnergyMeterName(String energyMeterName) {
        this.energyMeterName = energyMeterName;
    }

    public String getEnergyType() {
        return energyType;
    }

    public void setEnergyType(String energyType) {
        this.energyType = energyType;
    }

    public String getTypeStationCode() {
        return typeStationCode;
    }

    public void setTypeStationCode(String typeStationCode) {
        this.typeStationCode = typeStationCode;
    }

    public String getContractPrice() {
        return contractPrice;
    }

    public void setContractPrice(String contractPrice) {
        this.contractPrice = contractPrice;
    }

    public String getUsageCopy() {
        return usageCopy;
    }

    public void setUsageCopy(String usageCopy) {
        this.usageCopy = usageCopy;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStationCode() {
        return stationCode;
    }

    public void setStationCode(String stationCode) {
        this.stationCode = stationCode;
    }

    public String getStationName() {
        return stationName;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }

    public String getStationLocation() {
        return stationLocation;
    }

    public void setStationLocation(String stationLocation) {
        this.stationLocation = stationLocation;
    }

    public String getStationStatus() {
        return stationStatus;
    }

    public void setStationStatus(String stationStatus) {
        this.stationStatus = stationStatus;
    }

    public String getStationType() {
        return stationType;
    }

    public void setStationType(String stationType) {
        this.stationType = stationType;
    }

    public String getLargeIndustrialElectricityFlag() {
        return largeIndustrialElectricityFlag;
    }

    public void setLargeIndustrialElectricityFlag(String largeIndustrialElectricityFlag) {
        this.largeIndustrialElectricityFlag = largeIndustrialElectricityFlag;
    }

    public String getIcityFlag() {
        return icityFlag;
    }

    public void setIcityFlag(String icityFlag) {
        this.icityFlag = icityFlag;
    }

    public String getEnergySupplyWay() {
        return energySupplyWay;
    }

    public void setEnergySupplyWay(String energySupplyWay) {
        this.energySupplyWay = energySupplyWay;
    }

    public String getPowerGridEnergyMeterCode() {
        return powerGridEnergyMeterCode;
    }

    public void setPowerGridEnergyMeterCode(String powerGridEnergyMeterCode) {
        this.powerGridEnergyMeterCode = powerGridEnergyMeterCode;
    }

    public String getSiteCode() {
        return siteCode;
    }

    public void setSiteCode(String siteCode) {
        this.siteCode = siteCode;
    }

    public MeterEquipmentInfo2() {
    }

    public MeterEquipmentInfo2(String type, String provinceCode, String cityCode, String cityName, String countyCode,
                               String countyName, String energyMeterCode, String energyMeterName, String energyType,
                               String typeStationCode, String contractPrice, String usageCopy, String status,
                               String stationCode, String stationName, String stationLocation, String stationStatus,
                               String stationType, String largeIndustrialElectricityFlag, String icityFlag,
                               String energySupplyWay, String powerGridEnergyMeterCode, String siteCode) {
        this.type = type;
        this.provinceCode = provinceCode;
        this.cityCode = cityCode;
        this.cityName = cityName;
        this.countyCode = countyCode;
        this.countyName = countyName;
        this.energyMeterCode = energyMeterCode;
        this.energyMeterName = energyMeterName;
        this.energyType = energyType;
        this.typeStationCode = typeStationCode;
        this.contractPrice = contractPrice;
        this.usageCopy = usageCopy;
        this.status = status;
        this.stationCode = stationCode;
        this.stationName = stationName;
        this.stationLocation = stationLocation;
        this.stationStatus = stationStatus;
        this.stationType = stationType;
        this.largeIndustrialElectricityFlag = largeIndustrialElectricityFlag;
        this.icityFlag = icityFlag;
        this.energySupplyWay = energySupplyWay;
        this.powerGridEnergyMeterCode = powerGridEnergyMeterCode;
        this.siteCode = siteCode;
    }

    /**
     * 同步类型
     */
    private String type;
    /**
     * 省级编码
     */
    private String provinceCode;
    /**
     * 市局组织编码
     */
    private String cityCode;
    /**
     * 市局名称
     */
    private String cityName;
    /**
     * 区县组织编码
     */
    private String countyCode;
    /**
     * 区县组织名称
     */
    private String countyName;
    /**
     * 计量设备编码
     */
    private String energyMeterCode;
    /**
     * 计量设备名称
     */
    private String energyMeterName;
    /**
     * 能源类型编码
     */
    private String energyType;
    /**
     * 计量设备实体形式
     */
    private String typeStationCode;
    /**
     * 能源单价
     */
    private String contractPrice;
    /**
     * 报账用途
     */
    private String usageCopy;
    /**
     * 计量设备状态
     */
    private String status;
    /**
     * 局站编码
     */
    private String stationCode;
    /**
     * 局站名称
     */
    private String stationName;
    /**
     * 局站地址
     */
    private String stationLocation;
    /**
     * 局站状态
     */
    private String stationStatus;
    /**
     * 局站类型
     */
    private String stationType;
    /**
     * 大工业标识
     */
    private String largeIndustrialElectricityFlag;
    private String icityFlag;
    /**
     * 结算类型
     */
    private String energySupplyWay;
    /**
     * 电网公司电表编码
     */
    private String powerGridEnergyMeterCode;
    /**
     * 站址编码
     */
    private String siteCode;
}
