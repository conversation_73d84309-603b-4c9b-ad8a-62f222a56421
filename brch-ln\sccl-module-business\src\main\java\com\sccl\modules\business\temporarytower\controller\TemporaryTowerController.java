package com.sccl.modules.business.temporarytower.controller;

import com.sccl.common.lang.StringUtils;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.utils.ExcelUtil;
import com.sccl.modules.business.temporarytower.domain.TemporaryTower;
import com.sccl.modules.business.temporarytower.service.ITemporaryTowerService;
import com.sccl.modules.system.attachments.domain.UpLoadFile;
import com.sccl.modules.system.role.domain.Role;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * __临时铁塔数据控制器__<br/>
 * 2019/10/23
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/business/temporarytower")
public class TemporaryTowerController extends BaseController {

    private String prefix = "business/temporarytower";

    @RequiresPermissions("business:temporarytower:view")
    @GetMapping()
    public String stationInfo()
    {
        return prefix + "/temporarytower";
    }

    @Autowired
    private ITemporaryTowerService service;

    @RequestMapping(value = "/uploadExcel")
    @ResponseBody
    public Map<String, Object> uploadExcel(HttpServletRequest request, HttpServletResponse response, UpLoadFile uploadFile)
            throws Exception {
        response.setContentType("text/html;charset=utf-8");
        Map<String, Object> map = new HashMap<>();
        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        Iterator<String> iterator = multiRequest.getFileNames();
        String str = "";
        List<TemporaryTower> list = new ArrayList<>();
        while (iterator.hasNext()) {
            String name = iterator.next();
            List<MultipartFile> files = new LinkedList<>();
            files = multiRequest.getFiles(name);
            // 一次遍历所有文件
            for (MultipartFile file : files) {
                if (file == null) {
                    continue;
                }

                //如果文件大小为0则不上传
                long fileSize = file.getSize();
                if (fileSize <= 0L) {
                    throw new Exception("文件名称：【" + file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("\\") + 1, file.getOriginalFilename().length()) + "】的文件大小为0,请核对!");
                }
                list = service.importExcel("sheet1",file.getInputStream());

            }

        }



        if(list != null && list.size() > 0){
            str += "成功解析【" + list.size() +"】条数据；";
            int listSize=list.size();
            int toIndex=1000;
            for(int i = 0;i<list.size();i+=1000) {
                //作用为toIndex最后没有1000条数据则剩余几条newList中就装几条
                if (i + 1000 > listSize) {
                    toIndex = listSize - i;
                }
                List newList = list.subList(i, i + toIndex);
                service.insertList(newList); //保存到临时表
            }

            //删除重复数据
            service.deleteRepeat();
            //加入到正式铁塔
            int num = 0;
            num = service.insertTowerInfo();
            str += "成功加入【" + num +"】条数据到铁塔表；";

            //加入到正式局站
            Map<String,Object> m = new HashMap<>();
            m = service.importTower();

            str += "成功加入【" + m.get("insertNum") +"】条数据到局站表；";
            //删除临时表
            service.deleteAll();
            map.put("str",str);
        }

        if(StringUtils.isEmpty(str)){
            map.put("str","导入失败");
        }else {
            map.put("str",str);
        }

        return map;
    }
    /**
     * *****局站管理 - 导入全量铁塔
     * <AUTHOR>
     * @date 2022/4/21
     */
    @RequestMapping(value = "/uploadExcelAll")
    @ResponseBody
    public Map<String, Object> uploadExcelAll(HttpServletRequest request, HttpServletResponse response, UpLoadFile uploadFile)
            throws Exception{
response.setContentType("text/html;charset=utf-8");
    Map<String, Object> map = new HashMap<>();
    MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
    Iterator<String> iterator = multiRequest.getFileNames();
    String str = "";
    List<TemporaryTower> list = new ArrayList<>();
        while (iterator.hasNext()) {
        String name = iterator.next();
        List<MultipartFile> files = new LinkedList<>();
        files = multiRequest.getFiles(name);
        // 一次遍历所有文件
        for (MultipartFile file : files) {
            if (file == null) {
                continue;
            }

            //如果文件大小为0则不上传
            long fileSize = file.getSize();
            if (fileSize <= 0L) {
                throw new Exception("文件名称：【" + file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("\\") + 1, file.getOriginalFilename().length()) + "】的文件大小为0,请核对!");
            }
            list = service.importExcelstop("sheet1",file.getInputStream());

        }

    }



        if(list != null && list.size() > 0){
        str += "成功解析【" + list.size() +"】条数据；";
        int listSize=list.size();
        int toIndex=1000;
        for(int i = 0;i<list.size();i+=1000) {
            //作用为toIndex最后没有1000条数据则剩余几条newList中就装几条
            if (i + 1000 > listSize) {
                toIndex = listSize - i;
            }
            List newList = list.subList(i, i + toIndex);
            service.insertList(newList);
        }

        //删除历史数据
        service.deletetaAll();
        //加入到正式铁塔
        int num = 0;
        num = service.insertTowerInfoAll();
        str += "成功加入【" + num +"】条数据到铁塔全量表；";

        //加入到正式局站
        //Map<String,Object> m = new HashMap<>();
        //m = service.importTowerTwo();

        //str += "成功加入【" + m.get("insertNum") +"】条数据到局站停租表；";
        //删除临时表
        service.deleteAll();
        map.put("str",str);
    }

        if(StringUtils.isEmpty(str)){
        map.put("str","导入失败");
    }else {
        map.put("str",str);
    }

        return map;
}
    /**
     * *****局站管理 - 导入已过期
     * <AUTHOR>
     * @date 2022/4/21
     */
    @RequestMapping(value = "/uploadExcelTwo")
    @ResponseBody
    public Map<String, Object> uploadExcelTwo(HttpServletRequest request, HttpServletResponse response, UpLoadFile uploadFile)
            throws Exception {
        response.setContentType("text/html;charset=utf-8");
        Map<String, Object> map = new HashMap<>();
        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        Iterator<String> iterator = multiRequest.getFileNames();
        String str = "";
        List<TemporaryTower> list = new ArrayList<>();
        while (iterator.hasNext()) {
            String name = iterator.next();
            List<MultipartFile> files = new LinkedList<>();
            files = multiRequest.getFiles(name);
            // 一次遍历所有文件
            for (MultipartFile file : files) {
                if (file == null) {
                    continue;
                }

                //如果文件大小为0则不上传
                long fileSize = file.getSize();
                if (fileSize <= 0L) {
                    throw new Exception("文件名称：【" + file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("\\") + 1, file.getOriginalFilename().length()) + "】的文件大小为0,请核对!");
                }
                list = service.importExcelstop("sheet1",file.getInputStream());

            }

        }



        if(list != null && list.size() > 0){
            str += "成功解析【" + list.size() +"】条数据；";
            int listSize=list.size();
            int toIndex=1000;
            for(int i = 0;i<list.size();i+=1000) {
                //作用为toIndex最后没有1000条数据则剩余几条newList中就装几条
                if (i + 1000 > listSize) {
                    toIndex = listSize - i;
                }
                List newList = list.subList(i, i + toIndex);
                service.insertList(newList);
            }

            //删除重复数据
            service.deleteTwoAll();
            //加入到正式铁塔
            int num = 0;
            num = service.insertTowerInfoTwo();
            str += "成功加入【" + num +"】条数据到铁塔停租表；";

            //加入到正式局站
            //Map<String,Object> m = new HashMap<>();
            //m = service.importTowerTwo();

            //str += "成功加入【" + m.get("insertNum") +"】条数据到局站停租表；";
            //删除临时表
            service.deleteAll();
            map.put("str",str);
        }

        if(StringUtils.isEmpty(str)){
            map.put("str","导入失败");
        }else {
            map.put("str",str);
        }

        return map;
    }

    @PostMapping("/getUserRole")
    @ResponseBody
    public Map<String, Object> getUserRole()
    {
        List<Role> role = service.getUserRole();
        Map<String, Object> map = new HashMap<>();
        if(role == null){
            map.put("index",false);
        }else {
            map.put("index",true);
            map.put("role",role);
        }
        return map;
    }
}
