package com.sccl.modules.business.home.mapper;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.sccl.framework.mapper.BaseMapper;
import com.sccl.framework.web.domain.IdNameVO;

@Mapper
public interface HomeMapper extends BaseMapper<Object>{
	public List<Map<String, Object>> queryAmmeterorRank(@Param("companyList")List<IdNameVO> companyList);

	public List<Map<String, Object>> getUserTask(String loginId);
	
	public Map<String, Object> getUserMonthDoneTask(String userId);

	public List<Map<String, Object>> getAlarmAccount(@Param("companies")List<IdNameVO> companies, @Param("deportments")List<IdNameVO> deportments, @Param("apoint")int apoint);

	public List<Map<String, Object>> getAlarmBill(@Param("companies")List<IdNameVO> companies, @Param("deportments")List<IdNameVO> deportments);

	public List<Map<String, Object>> getAlarmProtocol(@Param("companies")List<IdNameVO> companies, @Param("deportments")List<IdNameVO> deportments);
	
	public int getAlerttimepoint(@Param("companies")List<IdNameVO> companies, @Param("deportments")List<IdNameVO> deportments);

	List<HashMap<String, Object>> getUserTaskList(String loginId);

	List<HashMap<String, Object>> countAmmeterOrProtocol(@Param("companyId") Long companyId);

	List<HashMap<String, Object>> countPowerStation(@Param("companyId") Long companyId);

	/****
	 *统计首页用电量数据
	 ****/
	List<HashMap<String, Object>> countPowerConsumption(@Param("companyId") Long companyId,
														@Param("year") String year,
														@Param("indicatorCodeList") List<String> indicatorCodeList);

	BigDecimal getEnergyFactor(@Param("energyTypeId") Long energyTypeId, @Param("year") String year);

	BigDecimal selectProductionDataYear(@Param("companyId") Long companyId, @Param("year") String year);
}
