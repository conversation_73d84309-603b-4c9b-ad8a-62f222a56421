package com.sccl.modules.business.stationaudit.pstationstop;

import com.enrising.dcarbon.audit.*;
import com.sccl.common.utils.SpringUtil;
import com.sccl.modules.business.account.domain.Account;
import com.sccl.modules.mssaccount.mssaccountbill.mapper.MssAccountbillMapper;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
public class StationStopCreator extends AbstractRefereeDatasourceCreator {
    @Override
    public Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> collect(Auditable auditable) {
        //数据源
        Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> datasource = new HashMap<>();

        //从Spring上下文取得mapper
        MssAccountbillMapper mapper = SpringUtil.getBean(MssAccountbillMapper.class);
        //可能上下文没有这个mapper
        if (mapper == null) {
            return null;
        }
        Account account = (Account) auditable;
        //根据 台账主键 获取对应的台账 信息
        Long pcid = account.getPcid();

        log.info("判定用电类型");
        String elecType = mapper.getElecType(pcid);
        if (!"141".equals(elecType)) {
            log.info("pcid:{}对应用电类型不是铁塔,起租单失效判定节点不执行", pcid);
            return datasource;
        }

        StaionStopRefreeContent content = mapper.getStationStop(pcid);
        //构造auditResult
        RefereeResult refereeResult = new RefereeResult("起租单失效判定", true, "成功");
        if (content == null) {
            content = mapper.getStationStop2(pcid);
            content.setExmsg("当前站址 未在起租单中找到");
        }
        content.setAuditKey(pcid + "");
        content.setRefereeResult(refereeResult);
        content.setStep(8);

        AuditResult auditResult = content.getAuditResult();

        //可以添加多种不同类型的评判数据
        //2添加到数据源
        datasource.put(StaionStopRefreeContent.class, new ArrayList<>(Arrays.asList(auditResult)));
        return datasource;
    }

    @Override
    public AbstractReferee getReferee() {
        return new StationStopReferee("起租单失效判定");
    }

}
