package com.sccl.modules.business.stationaudit.pstationgrade;


import com.enrising.dcarbon.audit.AbstractRefereeContent;
import com.enrising.dcarbon.audit.RefereeDatasource;
import com.enrising.dcarbon.audit.RefereeResult;
import com.sccl.modules.business.stationaudit.pstationempty.StationEmptyRefereeContent;
import lombok.Data;

import java.math.BigDecimal;
@Data
public class StationGradeExRefereeContent  extends AbstractRefereeContent implements RefereeDatasource {
    private Long  billId;
    private Long  pcid;
    private Long  ammeterid;
    private String stationCode;
    /**
     * 项目名称
     */
    private String projectname;
    /**
     * 局站名称
     */
    private String stationname;
    /**
     * 局站地址
     */
    private String stationaddress;
    /**
     * 台账对应电量
     */
    private BigDecimal power;
    /**
     * 台账 时间间隔
     */
    private Integer diff;
    /**
     * 标准电量
     */
    private BigDecimal standardpower;
    /**
     * 评级标准
     */
    private Long gid;
    /**
     * 站址评价
     **/
    private String evaluate;
    /**
     * 实际偏离 标准 百分比
     *
     * @param billid
     */
    private BigDecimal wide;



    public Long getPcid() {
        return pcid;
    }

    public void setPcid(Long pcid) {
        this.pcid = pcid;
    }



    public String getStationCode() {
        return stationCode;
    }

    public void setStationCode(String stationCode) {
        this.stationCode = stationCode;
    }

    public String getProjectname() {
        return projectname;
    }

    public void setProjectname(String projectname) {
        this.projectname = projectname;
    }

    public String getStationname() {
        return stationname;
    }

    public void setStationname(String stationname) {
        this.stationname = stationname;
    }

    public String getStationaddress() {
        return stationaddress;
    }

    public void setStationaddress(String stationaddress) {
        this.stationaddress = stationaddress;
    }

    public BigDecimal getPower() {
        return power;
    }

    public void setPower(BigDecimal power) {
        this.power = power;
    }

    public Integer getDiff() {
        return diff;
    }

    public void setDiff(Integer diff) {
        this.diff = diff;
    }

    public BigDecimal getStandardpower() {
        return standardpower;
    }

    public void setStandardpower(BigDecimal standardpower) {
        this.standardpower = standardpower;
    }

    public Long getGid() {
        return gid;
    }

    public void setGid(Long gid) {
        this.gid = gid;
    }

    public String getEvaluate() {
        return evaluate;
    }

    public void setEvaluate(String evaluate) {
        this.evaluate = evaluate;
    }

    public BigDecimal getWide() {
        return wide;
    }

    public void setWide(BigDecimal wide) {
        this.wide = wide;
    }

    public StationGradeExRefereeContent(RefereeResult refereeResult, int step, String auditKey) {
        super(refereeResult, step, auditKey);
    }



    public StationGradeExRefereeContent() {

    }
}
