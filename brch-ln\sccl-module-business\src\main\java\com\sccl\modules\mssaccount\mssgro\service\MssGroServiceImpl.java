package com.sccl.modules.mssaccount.mssgro.service;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.mssaccount.mssgro.mapper.MssGroMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sccl.modules.mssaccount.mssgro.domain.MssGro;

import java.util.List;


/**
 * 财务辅助提供的组织级次 预算责任中心 服务层实现
 * 
 * <AUTHOR>
 * @date 2019-05-01
 */
@Service
public class MssGroServiceImpl extends BaseServiceImpl<MssGro> implements IMssGroService
{
    @Autowired
    MssGroMapper mssGroMapper;
    @Override
    public List<MssGro> selectByLikeAuto(MssGro mssGro) {
        return mssGroMapper.selectByLikeAuto(mssGro);
    }


    /**
     * 切换新表 dwd_mss_hana_org_d
     *
     * */
    @Override
    public List<MssGro> selectByLikeAutoNew(MssGro mssGro) {
        return mssGroMapper.selectByLikeAutoNew(mssGro);
    }
}
