package com.sccl.modules.dataperfect.domain;

import com.sccl.modules.business.timing.dto.EnergyQuantity;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@Slf4j
public class StaResult {
    private String resstationcode;
    private String startdate;
    private String enddate;
    private String avgForBill;
    private String avgForGateway;
    private String diff;

    // 添加相应的 getter 和 setter 方法

    public static List<StaResult> generateStaResults(List<Tempt> tempts, Map<String, List<EnergyQuantity>> staMap) {
        return tempts.stream()
          .map(tempt -> {
              String resstationcode = tempt.getResstationcode();
              String startdate = tempt.getStartdate();
              String enddate = tempt.getEnddate();

              StaResult staResult = new StaResult();

              staResult.setResstationcode(resstationcode);
              staResult.setStartdate(startdate);
              staResult.setEnddate(enddate);
              staResult.setAvgForBill(tempt.getAvgForBill() + "");
              Float avgForBill = tempt.getAvgForBill();


              if (staMap.containsKey(resstationcode)
                &&
                CollectionUtils.isNotEmpty(staMap.get(resstationcode))
              ) {
                  List<EnergyQuantity> energyQuantities = staMap.get(resstationcode);

                  log.info("过滤resstationcode={}redis数据在能耗台账起止时间范围", resstationcode);
                  energyQuantities = energyQuantities.stream()
                    .filter(energyQuantity -> {
                        String date = energyQuantity.getDate();
                        return date.compareTo(startdate) >= 0 && date.compareTo(enddate) <= 0;
                    }).collect(Collectors.toList());
                  log.info("过滤resstationcode={}redis结束", resstationcode);

                  if (CollectionUtils.isEmpty(energyQuantities)) {
                      staResult.setAvgForGateway("对应时间段不存在网管数据");
                      return staResult;
                  }

                  float avgForGateway = calculateAvgForGateway(energyQuantities, startdate, enddate);
                  String diff = calculateDiff(avgForBill, avgForGateway);
                  staResult.setAvgForGateway(String.valueOf(avgForGateway));
                  staResult.setDiff(diff);
              } else  {
                  staResult.setAvgForGateway("对应时间段不存在网管数据");
              }

              return staResult;
          })
          .collect(Collectors.toList());
    }

    private static float calculateAvgForGateway(List<EnergyQuantity> energyQuantities, String startdate, String enddate) {
        return (float) energyQuantities.stream()
          .filter(energyQuantity -> {
              String date = energyQuantity.getDate();
              return date.compareTo(startdate) >= 0 && date.compareTo(enddate) <= 0;
          })
          .mapToDouble(energyQuantity -> (energyQuantity.get_4g() + energyQuantity.get_5g()) * 1.6)
          .average()
          .orElse(0.0f);
    }

    public static String calculateDiff(float avgForBill, float avgForGateway) {
        if (avgForBill == 0 || avgForGateway == 0) {
            return "存在0";
        } else {
            float diff = Math.abs(avgForBill - avgForGateway);
            float ratio = diff / avgForGateway * 100;
            return String.format("%.2f", ratio); // 保留两位小数
        }
    }

    public static void main(String[] args) {
        System.out.println("20130701".compareTo("30000000"));
        System.out.println("20130701".compareTo("20121299"));
    }

    public String getResstationcode() {
        return resstationcode;
    }

    public void setResstationcode(String resstationcode) {
        this.resstationcode = resstationcode;
    }

    public String getStartdate() {
        return startdate;
    }

    public void setStartdate(String startdate) {
        this.startdate = startdate;
    }

    public String getEnddate() {
        return enddate;
    }

    public void setEnddate(String enddate) {
        this.enddate = enddate;
    }

    public String getAvgForBill() {
        return avgForBill;
    }

    public void setAvgForBill(String avgForBill) {
        this.avgForBill = avgForBill;
    }

    public String getAvgForGateway() {
        return avgForGateway;
    }

    public void setAvgForGateway(String avgForGateway) {
        this.avgForGateway = avgForGateway;
    }

    public String getDiff() {
        return diff;
    }

    public void setDiff(String diff) {
        this.diff = diff;
    }
}

