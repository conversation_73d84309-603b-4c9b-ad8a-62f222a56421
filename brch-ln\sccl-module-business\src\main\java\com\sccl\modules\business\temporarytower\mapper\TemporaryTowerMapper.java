package com.sccl.modules.business.temporarytower.mapper;

import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.temporarytower.domain.TemporaryTower;
import com.sccl.modules.system.role.domain.Role;

import java.util.List;
import java.util.Map;

/**
 * __临时铁塔数据__<br/>
 * 2019/10/23
 *
 * <AUTHOR>
 */
public interface TemporaryTowerMapper extends BaseMapper<TemporaryTower> {

    /**
     * @Description: 删除全部数据
     * @author: dongk
     * @date: 2019/10/23
     * @param:
     * @return:
     */
    int deleteAll();

    /**
     * @Description: 加入正式铁塔表
     * @author: dongk
     * @date: 2019/10/24
     * @param:
     * @return:
     */
    int initTowerInfo();

    /**
     * @Description: 生成局站
     * @author: dongk
     * @date: 2019/10/31
     * @param:
     * @return:
     */
    void importTower(Map<String,Object> map);

    void importTowerTwo(Map<String,Object> map);

    /**
     * @Description: 删除重复数据
     * @author: dongk
     * @date: 2019/11/6
     * @param:
     * @return:
     */
    int deleteRepeat();

    public int deleteTwoAll();

    public int deletetaAll();

    /**
     * @Description: 加入正式铁塔表
     * @author: dongk
     * @date: 2019/11/6
     * @param:
     * @return:
     */
    int insertTowerInfo();
    int insertTowerInfoTwo();
    int insertTowerInfoAll();

    /**
     * @Description: 获取输入用户id的权限
     * @author: dongk
     * @date: 2019/12/2
     * @param:
     * @return:
     */
    List<Role> getUserRole(Long userId);



}
