package com.sccl.modules.business.statisticalanalysis.domain;

/**
 * 电表协议状态（在用，停用，新增）统计分析
 */
public class AmmeterProtocolNumber {
    private String companyName;// 分公司
    private String country;// 部门

    // 电表数量
    private Integer aEnableNum;// 在用电表数量
    private Integer aDisableNum;// 停用电表数量
    private Integer aNewNum;// 新增电表数量
    // 协议数量
    private Integer pEnableNum;// 在用协议数量
    private Integer pDisableNum;// 停用协议数量
    private Integer pNewNum;// 新增协议数量

    //
    private Integer numSum;

    // 查询时间 格式为: yyyyMMdd
    private String startDate;// 起始日期
    private String endDate;// 截止日期

    // getter && setter
    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public Integer getAEnableNum() {
        return aEnableNum;
    }

    public void setAEnableNum(Integer aEnableNum) {
        this.aEnableNum = aEnableNum;
    }

    public Integer getADisableNum() {
        return aDisableNum;
    }

    public void setADisableNum(Integer aDisableNum) {
        this.aDisableNum = aDisableNum;
    }

    public Integer getANewNum() {
        return aNewNum;
    }

    public void setANewNum(Integer aNewNum) {
        this.aNewNum = aNewNum;
    }

    public Integer getPEnableNum() {
        return pEnableNum;
    }

    public void setPEnableNum(Integer pEnableNum) {
        this.pEnableNum = pEnableNum;
    }

    public Integer getPDisableNum() {
        return pDisableNum;
    }

    public void setPDisableNum(Integer pDisableNum) {
        this.pDisableNum = pDisableNum;
    }

    public Integer getPNewNum() {
        return pNewNum;
    }

    public void setPNewNum(Integer pNewNum) {
        this.pNewNum = pNewNum;
    }

    public Integer getNumSum() {
        return numSum;
    }

    public void setNumSum(Integer numSum) {
        this.numSum = numSum;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }
}
