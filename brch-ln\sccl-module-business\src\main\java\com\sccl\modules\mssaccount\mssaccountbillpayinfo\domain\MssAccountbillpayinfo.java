package com.sccl.modules.mssaccount.mssaccountbillpayinfo.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;
import java.math.BigDecimal;


/**
 * 收款表 MSS_ACCOUNTBILLPAYINFO
 * 
 * <AUTHOR>
 * @date 2019-05-01
 */
public class MssAccountbillpayinfo extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
    /** 主单id */
    private Long writeoffInstanceId;
    /** 收款方名称 */
    private String employeename;
    /** 银行账号 */
    private String employeebankac;
    /** 银行名称 */
    private String bank;
    /** 所属银行代码 */
    private String bankcode;
    /** 收款方编码 */
    private String payeecode;
    /** 账户类型 1-对外对公，3-对外对私 */
    private String accounttype;
    /** 外部收款方类型 银行账户类型外部收款方类型,用于区分D-客户, K-供应商 */
    private String payeetype;
    /** 开户行号 */
    private String rowno;
    /** 开户行地址  如：中国邮政储蓄银行珠海分行 */
    private String bankaddress;
    /** 所在省 */
    private String province;
    /** 所在市 */
    private String city;
    /** 币种 */
    private String currency;
    /** 付款金额 */
    private BigDecimal sum;
    /** 状态0 删除 1正常 */
    private String status;
    /**  */
    private Date inputdate;
    /** 账户类型名称 */
    private String accountname;

	public void setWriteoffInstanceId(Long writeoffInstanceId)
	{
		this.writeoffInstanceId = writeoffInstanceId;
	}

	public Long getWriteoffInstanceId() 
	{
		return writeoffInstanceId;
	}


	public void setEmployeename(String employeename)
	{
		this.employeename = employeename;
	}

	public String getEmployeename() 
	{
		return employeename;
	}

	public void setEmployeebankac(String employeebankac)
	{
		this.employeebankac = employeebankac;
	}

	public String getEmployeebankac() 
	{
		return employeebankac;
	}

	public void setBank(String bank)
	{
		this.bank = bank;
	}

	public String getBank() 
	{
		return bank;
	}

	public void setBankcode(String bankcode)
	{
		this.bankcode = bankcode;
	}

	public String getBankcode() 
	{
		return bankcode;
	}

	public void setPayeecode(String payeecode)
	{
		this.payeecode = payeecode;
	}

	public String getPayeecode() 
	{
		return payeecode;
	}

	public void setAccounttype(String accounttype)
	{
		this.accounttype = accounttype;
	}

	public String getAccounttype() 
	{
		return accounttype;
	}

	public void setPayeetype(String payeetype)
	{
		this.payeetype = payeetype;
	}

	public String getPayeetype() 
	{
		return payeetype;
	}

	public void setRowno(String rowno)
	{
		this.rowno = rowno;
	}

	public String getRowno() 
	{
		return rowno;
	}

	public void setBankaddress(String bankaddress)
	{
		this.bankaddress = bankaddress;
	}

	public String getBankaddress() 
	{
		return bankaddress;
	}

	public void setProvince(String province)
	{
		this.province = province;
	}

	public String getProvince() 
	{
		return province;
	}

	public void setCity(String city)
	{
		this.city = city;
	}

	public String getCity() 
	{
		return city;
	}

	public void setCurrency(String currency)
	{
		this.currency = currency;
	}

	public String getCurrency() 
	{
		return currency;
	}

	public void setSum(BigDecimal sum)
	{
		this.sum = sum;
	}

	public BigDecimal getSum() 
	{
		return sum;
	}

	public void setStatus(String status)
	{
		this.status = status;
	}

	public String getStatus() 
	{
		return status;
	}

	public void setInputdate(Date inputdate)
	{
		this.inputdate = inputdate;
	}

	public Date getInputdate() 
	{
		return inputdate;
	}

	public void setAccountname(String accountname)
	{
		this.accountname = accountname;
	}

	public String getAccountname() 
	{
		return accountname;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("writeoffInstanceId", getWriteoffInstanceId())
            .append("id", getId())
            .append("employeename", getEmployeename())
            .append("employeebankac", getEmployeebankac())
            .append("bank", getBank())
            .append("bankcode", getBankcode())
            .append("payeecode", getPayeecode())
            .append("accounttype", getAccounttype())
            .append("payeetype", getPayeetype())
            .append("rowno", getRowno())
            .append("bankaddress", getBankaddress())
            .append("province", getProvince())
            .append("city", getCity())
            .append("currency", getCurrency())
            .append("sum", getSum())
            .append("status", getStatus())
            .append("inputdate", getInputdate())
            .append("accountname", getAccountname())
            .toString();
    }
}
