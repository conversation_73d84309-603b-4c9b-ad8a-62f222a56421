package com.sccl.modules.business.powerauditstaiongrade.entity;

import lombok.Data;

import java.util.List;

@Data
public class StaionAnalysis {
    private String company;
    private String companyname;
    private String country;
    private String countryname;
    /**
     * 时间
     */
    private String evaluationDate;
    /**
     * 评级内容
     */
    private String evaluationContent;
    /**
     * 数量
     */
    private String sum;
    /**
     * 明细对应key
     */
    private String key;

    public static StaionAnalysis createKeyOb(String keyCompany, String keyCountry, String keyTime, String keyEvaluationContent) {
        StaionAnalysis staionAnalysis = new StaionAnalysis();
        staionAnalysis.setCompany(keyCompany);
        staionAnalysis.setCountry(keyCountry);
        staionAnalysis.setEvaluationDate(keyTime);
        staionAnalysis.setEvaluationContent(keyEvaluationContent);
        return staionAnalysis;
    }

    public static void addEvaluationKey(List<StaionAnalysis> staionAnalysisList) {
        staionAnalysisList.forEach(
          item -> {
              String company = item.getCompany();
              String country = item.getCountry();
              String content = item.getEvaluationContent().replaceAll("\"", "");
              String evaluationDate = item.getEvaluationDate();
              String key = String.format("stationgrade-%s-%s-%s-%s", company, country, evaluationDate, content);
              item.setKey(key);
          }
        );
    }
}
