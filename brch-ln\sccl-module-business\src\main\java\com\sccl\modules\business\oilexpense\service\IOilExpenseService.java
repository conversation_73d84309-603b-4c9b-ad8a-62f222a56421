package com.sccl.modules.business.oilexpense.service;

import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.business.energyaccount.domain.EnergyAccount;
import com.sccl.modules.business.energyaccount.domain.EnergyAccountVo;
import com.sccl.modules.business.oilexpense.domain.OilExpense;
import com.sccl.framework.service.IBaseService;
import com.sccl.modules.business.oilexpense.domain.OilExpenseRecord;
import com.sccl.modules.business.oilexpense.domain.OilExpenseVo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 油机基础 服务层
 *
 * <AUTHOR>
 * @date 2021-12-14
 */
public interface IOilExpenseService extends IBaseService<OilExpense> {

    //修改油机系统库存
    int updateSysstock(EnergyAccount e,int i);

    //修改油机信息
    AjaxResult editSave(OilExpense oilExpense);

    /** 删除油机基础 */
    AjaxResult removeOilExpense(String ids);

    List<OilExpense> selectListView(OilExpense oilExpense);

    List<OilExpenseVo> selectListVo(OilExpense oilExpense);

    List<OilExpenseVo> selectListAuditVo(OilExpenseVo oilExpense);

    List<OilExpenseVo> selectListHavedAuditVo(OilExpenseVo oilExpense);

    List<EnergyAccountVo> listAccountDetail(OilExpenseVo oilExpense);

    List<OilExpenseVo> selectListAuditVo4(OilExpenseVo oilExpense);

    List<OilExpenseVo> selectListAll(OilExpense oilExpense);

    List<OilExpenseRecord> selectListViewOld(OilExpenseRecord oilExpense);

    /**
     * 根据用户角色获取公司和责任中心
     */
    public AjaxResult getUserByUserRole();
    /**
     * 根据当前节点ID和用户角色查询下属的组织机构
     * @param orgCode 节点编码
     * @return 当前节点下的所有组织（只含一级）
     */
    public List<Map<String, Object>> selectSubordinateOrgByRole(String orgCode, String type);


}
