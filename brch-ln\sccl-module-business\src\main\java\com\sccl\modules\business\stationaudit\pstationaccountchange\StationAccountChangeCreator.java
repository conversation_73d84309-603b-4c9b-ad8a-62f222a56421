package com.sccl.modules.business.stationaudit.pstationaccountchange;

import com.enrising.dcarbon.audit.*;
import com.sccl.common.utils.SpringUtil;
import com.sccl.modules.business.account.domain.Account;

import com.sccl.modules.mssaccount.mssaccountbill.mapper.MssAccountbillMapper;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

public class StationAccountChangeCreator extends AbstractRefereeDatasourceCreator {
    @Override
    public Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> collect(Auditable auditable) {
        //数据源
        Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> datasource = new HashMap<>();

        //从Spring上下文取得mapper
        MssAccountbillMapper mapper = SpringUtil.getBean(MssAccountbillMapper.class);
        //可能上下文没有这个mapper
        if (mapper == null) {
            return null;
        }
        Account account = (Account) auditable;
        //根据 台账主键 获取对应的台账 信息
        Long pcid = account.getPcid();
        List<StationAccountChangeRefereeContent> nodeResults = mapper.getAccountChange(pcid);

        //去除null数据
        nodeResults = nodeResults.stream().filter(Objects::nonNull).collect(Collectors.toList());


        //获取 本次台账 对应站址 的 上一次台账信息
        nodeResults = nodeResults.stream().map(
                nodeResult -> {
                    String stationcode = nodeResult.getStationCode();
                    String now = nodeResult.getStartdate();
                    StationAccountChangeRefereeContent nodeResultLast = mapper.getAccountChangeLast(stationcode, now);

                    //null 截断
                    if (nodeResultLast == null) {
                        return nodeResult;
                    }
                    //set 比对属性
                    if (nodeResultLast != null) {
                        nodeResult.setAmmeterid_compare(nodeResultLast.getAmmeterid());
                        nodeResult.setAmmetername_compare(nodeResultLast.getAmmetername());
                        nodeResult.setPcid_compare(nodeResultLast.getPcid());
                        nodeResult.setAccountmoney_compare(nodeResultLast.getAccountmoney());
                        nodeResult.setStationcode_compare(nodeResultLast.getStationCode());
                        nodeResult.setProjectname_compare(nodeResultLast.getProjectname());
                        nodeResult.setStationname_compare(nodeResultLast.getStationname());
                        nodeResult.setStationaddress_compare(nodeResultLast.getStationaddress());
                    }
                    if (nodeResultLast.getAccountmoney() != null && nodeResultLast.getAccountmoney().compareTo(BigDecimal.ZERO) != 0) {
                        nodeResult.setWideaccount(
                                nodeResult.getAccountmoney().subtract(nodeResult.getAccountmoney_compare()).
                                        divide(nodeResult.getAccountmoney_compare(), 2)
                                          .multiply(BigDecimal.valueOf(100))
                        );

                    }
                    return nodeResult;
                }

        ).filter(Objects::nonNull).collect(Collectors.toList());

        //转为auditResult 稽核对象
        List<AuditResult> auditResults = nodeResults.stream().map(
                n -> {
                    //创建 评审内容对象
                    RefereeResult refereeResult = new RefereeResult("电费波动", true, "成功");
                    n.setRefereeResult(refereeResult);
                    AuditResult auditResult = n.getAuditResult();
                    auditResult.setAuditKey(String.valueOf(n.getPcid()));
                    auditResult.setStep(4);
                    auditResult.setNodeTopic("电费波动");
                    auditResult.setRefereeMessage("电费波动结果已出");
                    return auditResult;
                }
        ).collect(Collectors.toList());
        //可以添加多种不同类型的评判数据
        //2添加到数据源
        datasource.put(StationAccountChangeRefereeContent.class, new ArrayList<>(auditResults));
        return datasource;
    }

    @Override
    public AbstractReferee getReferee() {
        return new StationAccountChangeReferee("电费波动");
    }

}
