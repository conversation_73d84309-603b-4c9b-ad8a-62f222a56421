package com.sccl.modules.business.statistical.tower.query;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 分摊比例查询结果
 *
 * <AUTHOR>
 * @date 2022-12-07 9:52
 * @email <EMAIL>
 */
@Getter
@Setter
@ToString
public class ContributionPercentQuery {
    /**
     * 站址编码
     */
    private String towerSiteCode;
    /**
     * 用电类型 转供、直供
     */
    private String powerSupply;
    /**
     * 户号，转直不同
     */
    private String meterCode;
    /**
     * 分摊比例
     */
    private BigDecimal contributionPercent;
}
