package com.sccl.modules.mssaccount.mssinterface.domain;

public enum BillPollState {
    SUCESS(1, "轮询成功"), FAIL(2, "轮询失败失败"),
    WRITEDBSUCCESS(3, "保存DB成功"), WRITEDBFAIL(4, "保存DB失败");
    private Integer state;
    private String desc;

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    BillPollState(Integer state, String desc) {
        this.state = state;
        this.desc = desc;
    }
}
