package com.sccl.modules.mssaccount.mssinterface.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 同步智能电表数据响应VO
 *
 * <AUTHOR>
 * @date 2025-01-25
 */
@Data
public class SyncSmartMeterDataResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 采集时间（单个日期时使用）
     */
    private String collectTime;

    /**
     * 查询到的数据条数
     */
    private Integer queryCount;

    /**
     * 修正的数据条数
     */
    private Integer updateCount;

    /**
     * 新增的数据条数
     */
    private Integer insertCount;

    /**
     * 推送的数据条数
     */
    private Integer pushCount;

    /**
     * 推送结果
     */
    private String pushResult;

    /**
     * 处理状态（success/failed）
     */
    private String status;

    /**
     * 处理消息
     */
    private String message;

    /**
     * 批量处理结果（多个日期时使用）
     */
    private List<BatchSyncResult> batchResults;

    /**
     * 批量处理总计
     */
    private BatchSyncSummary summary;

    /**
     * 批量同步结果详情
     */
    @Data
    public static class BatchSyncResult implements Serializable {
        private String collectTime;
        private Integer queryCount;
        private Integer updateCount;
        private Integer insertCount;
        private Integer pushCount;
        private String pushResult;
        private String status;
        private String message;
    }

    /**
     * 批量同步汇总信息
     */
    @Data
    public static class BatchSyncSummary implements Serializable {
        private Integer totalDays;
        private Integer successDays;
        private Integer failedDays;
        private Integer totalQueryCount;
        private Integer totalUpdateCount;
        private Integer totalInsertCount;
        private Integer totalPushCount;
    }
}
