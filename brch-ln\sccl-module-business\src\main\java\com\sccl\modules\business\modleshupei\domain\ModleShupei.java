package com.sccl.modules.business.modleshupei.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.math.BigDecimal;


/**
 * 单价输配电价表 power_modle_shupei
 * 
 * <AUTHOR>
 * @date 2023-03-03
 */
public class ModleShupei extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
    /** 电压等级 */
    private Integer voltagelevel;
    /** 工业用途 */
    private String industrialuse;
    /** 输配电价 */
    private BigDecimal pricesp;


	public void setVoltagelevel(Integer voltagelevel)
	{
		this.voltagelevel = voltagelevel;
	}

	public Integer getVoltagelevel() 
	{
		return voltagelevel;
	}

	public void setIndustrialuse(String industrialuse)
	{
		this.industrialuse = industrialuse;
	}

	public String getIndustrialuse() 
	{
		return industrialuse;
	}

	public void setPricesp(BigDecimal pricesp)
	{
		this.pricesp = pricesp;
	}

	public BigDecimal getPricesp() 
	{
		return pricesp;
	}


	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("voltagelevel", getVoltagelevel())
            .append("industrialuse", getIndustrialuse())
            .append("pricesp", getPricesp())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
