package com.sccl.modules.business.item.controller;

import java.util.List;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.sccl.common.support.Convert;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.item.domain.Item;
import com.sccl.modules.business.item.service.IItemService;

/**
 * 项目 信息操作处理
 * 
 * <AUTHOR>
 * @date 2019-03-04
 */
@Controller
@RequestMapping("/business/item")
public class ItemController extends BaseController
{
    private String prefix = "business/item";
	
	@Autowired
	private IItemService itemService;
	
	@RequiresPermissions("business:item:view")
	@GetMapping()
	public String item()
	{
	    return prefix + "/item";
	}
	
	/**
	 * 查询项目列表
	 */
	@RequiresPermissions("business:item:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(Item item)
	{
		startPage();
        List<Item> list = itemService.selectList(item);
		return getDataTable(list);
	}
	
	/**
	 * 新增项目
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存项目
	 */
	@RequiresPermissions("business:item:add")
	//@Log(title = "项目", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody Item item)
	{		
		return toAjax(itemService.insert(item));
	}

	/**
	 * 修改项目
	 */
	@GetMapping("/edit/{id}")
	public Item edit(@PathVariable("id") Long id)
	{
	    return itemService.get(id);
	}
	
	/**
	 * 修改保存项目
	 */
	@RequiresPermissions("business:item:edit")
	//@Log(title = "项目", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody Item item)
	{		
		return toAjax(itemService.update(item));
	}
	
	/**
	 * 删除项目
	 */
	@RequiresPermissions("business:item:remove")
	//@Log(title = "项目", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(itemService.deleteByIds(Convert.toStrArray(ids)));
	}
	
}
