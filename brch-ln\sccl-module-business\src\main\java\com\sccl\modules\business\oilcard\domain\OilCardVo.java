package com.sccl.modules.business.oilcard.domain;

import com.sccl.framework.web.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2021/12/17 10:55
 **/
@Getter
@Setter
public class OilCardVo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 油卡编号
     */
    private String oilCardId;
    /**
     * 所属区县
     */
    private Long country;
    /**
     * 余额
     */
    private BigDecimal residue;
    /**
     * 充值金额
     */
    private BigDecimal recharge;
    /**
     * 分公司
     */
    private Long company;
    /**
     * 分局、支局
     */
    private String substation;
    /**
     * 油卡使用者
     */
    private String cardMaster;
    /**
     * 单据状态
     */
    private Integer billStatus;

    private String companyName;

    private String countryName;

    private Integer status;
    private Long procInstId;
    private Long busiKey;
    private String busiAlias;
    private String accountnum;
    private Date createtime;
}
