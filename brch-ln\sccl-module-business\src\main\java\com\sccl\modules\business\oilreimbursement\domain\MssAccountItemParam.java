package com.sccl.modules.business.oilreimbursement.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description 报账单明细参数类
 * @Auther <PERSON>
 * @Date 2021/12/22 10:12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MssAccountItemParam {
    /*=======================明细、台账关联信息======================>*/
    /**
     * 报账单id
     */
    private Long billId;
    /**
     * 台账id
     */
    private Long accountId;
    /**
     * 台账金额
     */
    private Double accountMoney;
    /**
     * 关联金额
     */
    private Double money;
    /**
     * 关联税额
     */
    private Double taxmoney;
    /**
     * 是否全部归属报账明细 默认是, 1 是 0 否
     */
    private Long ifall;
    /*=============================================<*/

    /*=======================报账单明细信息======================>*/
    /**
     * 预算责任中心ID
     */
    private String responseCenterId;
    /**
     * SAP成本中心
     */
    private String costCenterCode;
    /**
     * SAP成本中心名称
     */
    private String costCenterName;
    /**
     * 创建日期
     */
    private Date createDate;
    /**
     * 被后续操作的金额
     */
    private BigDecimal beAfterOperateSum;
    /**
     * 用途id 1：经营管理用 2：支付代垫外单位或员工电费
     * 3：收到外单位或员工还代垫电费 4：职工宿舍用
     */
    private String usageId;
    /**
     * 预算项目类型（1--成本，2--现金流，3--成本+现金流）
     */
    private String budgetItemType;
    /**
     * 单价
     */
    private BigDecimal price;
    /**
     * 数量
     */
    private BigDecimal amount;
    /**
     * 是否摊销（1标识为是）
     */
    private String isAmortize;
    /**
     * sysdate
     */
    private Date timestamp;
    /**
     * 预算责任中心编码
     */
    private String responseCenterCode;
    /**
     * 还款金额
     */
    private BigDecimal payoffSum;
    /**
     * 核定还款金额
     */
    private BigDecimal auditPayoffSum;
    /**
     * 主单id
     */
    private BigDecimal writeoffInstanceId;
    /**
     * 合计金额
     */
    private BigDecimal sum;
    /**
     * 摘要
     */
    private String abstractValue;
    /**
     * 删除标识：1为正常 -1为删除
     */
    private String status;
    /*=============================================<*/
}
