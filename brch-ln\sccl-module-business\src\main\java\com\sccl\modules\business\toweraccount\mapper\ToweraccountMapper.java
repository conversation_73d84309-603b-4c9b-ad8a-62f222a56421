package com.sccl.modules.business.toweraccount.mapper;

import com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol;
import com.sccl.modules.business.msg.domain.Message;
import com.sccl.modules.business.toweraccount.domain.TowerData;
import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.toweraccount.domain.mssbusi;

import java.util.List;

/**
 * 铁塔接口 数据层
 * 
 * <AUTHOR>
 * @date 2021-08-12
 */
public interface ToweraccountMapper extends BaseMapper<TowerData>
{
    public List<TowerData> selectByList(TowerData towerdata);
    public List<TowerData> selectListByIds(String[] ids);
    public List<TowerData> selectListByOrgid(String  orgid);
    public List<String> selectListtowidsByIds(String[] ids);
    public Message selectLstMsg(Long id);
    List<mssbusi> selectmssbusi(TowerData towerdata);
}