package com.sccl.modules.business.timing.api;

import com.enrising.dcarbon.collect.CollectionUtil;
import com.enrising.dcarbon.redis.RedisUtil;
import com.enrising.dcarbon.string.StringUtils;
import com.sccl.modules.business.timing.dto.AmmeterProtocolRecordCacheData;
import com.sccl.modules.business.timing.dto.EnergyQuantity;
import com.sccl.modules.business.timing.dto.STAEnergyConsumptionIndex;
import com.sccl.timing.finder.framework.TimeSeriesDataFinder;
import com.sccl.timing.finder.util.ProtoStuffUtil;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;

import java.util.*;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-08-15 10:37
 * @email <EMAIL>
 */
public class PowerStationQuotaSTAAPI {
    private static final TimeSeriesDataFinder<STAEnergyConsumptionIndex> finder = new TimeSeriesDataFinder<>("sta");

    @SuppressWarnings("unchecked")
    public static Map<String, List<EnergyQuantity>> listByStationCodes(List<String> resStationCodes, long start, long end) {
        Map<String, List<EnergyQuantity>> r = new HashMap<>();
        CollectionUtil.batchedCollection(resStationCodes, 1000, keys -> {
            List<Object> res = RedisUtil
                    .getRedisTemplate()
                    .executePipelined((RedisCallback<byte[]>) connection -> {
                        for (String key : keys) {
                            connection
                                    .listCommands()
                                    .lRange(defaultRawKey(finder.formatKey(key)), finder.offset(start), finder.offset(end));
                        }
                        return null;
                    }, new RedisSerializer<Object>() {
                        @Override
                        public byte[] serialize(Object o) throws SerializationException {
                            return new byte[0];
                        }

                        @Override
                        public Object deserialize(byte[] bytes) throws SerializationException {
                            try {
                                return ProtoStuffUtil.deserialize(bytes, EnergyQuantity.class);
                            } catch (Exception ignored) {
                            }
                            return new EnergyQuantity(null, -1f, -1f);
                        }
                    });
            if (keys.size() != res.size()) {
                throw new RuntimeException("桶数目和值数目不匹配");
            }
            for (int i = 0; i < res.size(); i++) {
                if (res.get(i) != null && res.get(i) instanceof List) {
                    r.put(keys.get(i), (List<EnergyQuantity>) res.get(i));
                }
            }
        });
        return r;
    }

    @SuppressWarnings("unchecked")
    private static byte[] defaultRawKey(String key) {
        return Objects.requireNonNull(((RedisSerializer<String>) RedisUtil
                .getRedisTemplate()
                .getKeySerializer()).serialize(key));
    }

    private static boolean isInPattern(String key, List<String> patterns) {
        if (StringUtils.isEmpty(key)) {
            return false;
        }
        for (String p : patterns) {
            if (key.contains(p)) {
                return true;
            }
        }
        return false;
    }
}
