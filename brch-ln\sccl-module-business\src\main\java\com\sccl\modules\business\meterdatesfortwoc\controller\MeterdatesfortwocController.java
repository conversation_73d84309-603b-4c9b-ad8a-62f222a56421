package com.sccl.modules.business.meterdatesfortwoc.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.business.meterdatesfortwoc.domain.Meterdatesfortwoc;
import com.sccl.modules.business.meterdatesfortwoc.service.IMeterdatesfortwocService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 双碳接口同步电实际用电量数据 信息操作处理
 * 
 * <AUTHOR>
 * @date 2023-04-14
 */
@RestController
@RequestMapping("/business/meterdatesfortwoc")
public class MeterdatesfortwocController extends BaseController
{
    private String prefix = "business/meterdatesfortwoc";
	
	@Autowired
	private IMeterdatesfortwocService meterdatesfortwocService;
	
	@RequiresPermissions("business:meterdatesfortwoc:view")
	@GetMapping()
	public String meterdatesfortwoc()
	{
	    return prefix + "/meterdatesfortwoc";
	}
	
	/**
	 * 查询双碳接口同步电实际用电量数据列表
	 */
	@RequiresPermissions("business:meterdatesfortwoc:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(Meterdatesfortwoc meterdatesfortwoc)
	{
		startPage();
        List<Meterdatesfortwoc> list = meterdatesfortwocService.selectList(meterdatesfortwoc);
		return getDataTable(list);
	}
	
	/**
	 * 新增双碳接口同步电实际用电量数据
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存双碳接口同步电实际用电量数据
	 */
	@RequiresPermissions("business:meterdatesfortwoc:add")
	@Log(title = "双碳接口同步电实际用电量数据", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody Meterdatesfortwoc meterdatesfortwoc)
	{		
		return toAjax(meterdatesfortwocService.insert(meterdatesfortwoc));
	}

	/**
	 * 修改双碳接口同步电实际用电量数据
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		Meterdatesfortwoc meterdatesfortwoc = meterdatesfortwocService.get(id);

		Object object = JSONObject.toJSON(meterdatesfortwoc);

        return this.success(object);
	}
	
	/**
	 * 修改保存双碳接口同步电实际用电量数据
	 */
	@RequiresPermissions("business:meterdatesfortwoc:edit")
	@Log(title = "双碳接口同步电实际用电量数据", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody Meterdatesfortwoc meterdatesfortwoc)
	{		
		return toAjax(meterdatesfortwocService.update(meterdatesfortwoc));
	}
	
	/**
	 * 删除双碳接口同步电实际用电量数据
	 */
	@RequiresPermissions("business:meterdatesfortwoc:remove")
	@Log(title = "双碳接口同步电实际用电量数据", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(meterdatesfortwocService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看双碳接口同步电实际用电量数据
     */
    @RequiresPermissions("business:meterdatesfortwoc:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		Meterdatesfortwoc meterdatesfortwoc = meterdatesfortwocService.get(id);

        Object object = JSONObject.toJSON(meterdatesfortwoc);

        return this.success(object);
    }

}
