package com.sccl.modules.mssaccount.certificatetitle.service;

import com.sccl.modules.mssaccount.certificatetitle.domain.CertificateTitle;
import com.sccl.framework.service.IBaseService;

import java.util.List;

/**
 * view_certificate_title 服务层
 * 
 * <AUTHOR>
 * @date 2019-09-20
 */
public interface ICertificateTitleService extends IBaseService<CertificateTitle>
{


    List<CertificateTitle> selectCertificateTitleList(CertificateTitle certificateTitle);
}
