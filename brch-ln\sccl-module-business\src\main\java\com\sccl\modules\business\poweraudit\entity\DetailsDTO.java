package com.sccl.modules.business.poweraudit.entity;

import com.sccl.framework.aspectj.lang.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: 芮永恒
 * @CreateTime: 2024-03-04  13:52
 * @Description:  详单数据DTO
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DetailsDTO extends PowerAuditEntity{
    /**
     * 台账期号
     */
    private String accountNo;

    /**
     * 台账开始时间到结束时间的天数
     */
    private String days;

    /***  电价合理性 */

    /***  台账单价 */
    private String meterPrice;

    /***  基础信息单价 */
    private String accountPrice;


    /***  台账周期连续性 */
    /*** 台账周期连续性 错误类型 */
    private String periodErrorType;
    /**
     * 上次起始时间
     */
    @Excel(name = "上次起始时间")
    private String lastStartTime;

    /**
     * 上次截止时间
     */
    @Excel(name = "上次截止时间")
    private String lastStopTime;

    /**
     * 本次起始时间
     */
    @Excel(name = "本次起始时间")
    private String startTime;

    /**
     * 本次截止时间
     */
    @Excel(name = "本次截止时间")
    private String stopTime;


    /***  电表度数连续性 */

    /***  电表度数连续性 错误类型 */
    private String degreeErrorType;
    /**
     * 上次起始度数
     */
    @Excel(name = "上次起始度数")
    private String lastStartDegrees;
    /**
     * 上次截止度数
     */
    @Excel(name = "上次截止度数")
    private String lastStopDegrees;
    /**
     * 本次起始度数
     */
    @Excel(name = "本次起始度数")
    private String startDegrees;
    /**
     * 本次截止度数
     */
    @Excel(name = "本次截止度数")
    private String stopDegrees;



    /***  台账电量合理性 */

    /**
     * 上期报账台账电量
     */
    @Excel(name = "上期报账台账电量")
    private String lastDegrees;

    /**
     * 本期报账台账电量
     */
    @Excel(name = "本期报账台账电量")
    private String degrees;



    /***  台账日均电量波动合理性（5gr） */

    /***  台账日均电量 */
    @Excel(name = "台账日均电量")
    private String useDay;

    /***  集团5gr日均电量（标准电量） */
    @Excel(name = "集团5gr日均电量（标准电量）")
    private String degreesDay;

    /**
     * 波动幅度
     */
    @Excel(name = "波动幅度")
    private String degreesFluctuate;


    /***  分摊比例准确性  */

    /**
     * 维护共享家数
     */
    @Excel(name = "维护共享家数")
    private String shareNum;

    /***  协议管理能耗比例 */
    @Excel(name = "协议管理能耗比例")
    private String meterPercent;

    /**
     * 电信
     */
    @Excel(name = "电信")
    private String dxApportionmentratio;

    /**
     * 移动
     */
    @Excel(name = "移动")
    private String mobileApportionmentratio;

    /**
     * 联通
     */
    @Excel(name = "联通")
    private String unicomApportionmentratio;

    /**
     * 拓展
     */
    @Excel(name = "拓展")
    private String expandApportionmentratio;

    /**
     * 能源
     */
    @Excel(name = "能源")
    private String energyApportionmentratio;

    /**
     * 合计
     */
    @Excel(name = "合计")
    private String totalApportionmentratio;

    /***  台账周期  */

    /**
     *  本次台账录入时间
     */
    private String auditTimeNow;

    /**
     * 上次台账录入时间
     */
    private String auditTimeLast;

    /**
     * 报账周期差异（天）
     */
    private String differencesDay;



    /**
     * 类型
     */
    @Excel(name = "类型")
    private String type;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private String status;

    /**
     * 异常项
     */
    @Excel(name = "异常项")
    private String abnormal;

    /**
     * 电表负责人
     */
    @Excel(name = "电表负责人")
    private String headPeople;

    /**
     * 集团5gr
     */
    private String stationcodeintid;


}
