package com.sccl.modules.rental.rentalcarmain.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;
import com.sccl.modules.rental.rentalcar.domain.Rentalcar;
import com.sccl.modules.rental.rentalcar.service.IRentalcarService;
import com.sccl.modules.rental.rentalcarorder.domain.Rentalcarorder;
import com.sccl.modules.rental.rentalordercarmodel.domain.RentalorderCarmodel;
import com.sccl.modules.system.role.domain.Role;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.service.IUserService;
import com.sccl.modules.uniflow.wfprocinst.service.IWfProcInstService;
import com.sccl.modules.uniflow.wftask.domain.WfTask;
import com.sccl.modules.uniflow.wftask.service.IWfTaskService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.rental.rentalcarmain.domain.Rentalcarmain;
import com.sccl.modules.rental.rentalcarmain.service.IRentalcarmainService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 车辆 所属 租赁项目 信息操作处理
 *
 * <AUTHOR>
 * @date 2019-08-23
 */
@RestController
@RequestMapping("/rental/rentalcarmain")
public class RentalcarmainController extends BaseController {
    private String prefix = "rental/rentalcarmain";

    @Autowired
    private IRentalcarmainService rentalcarmainService;
    @Autowired
    private IRentalcarService rentalcarService;
    @Autowired
    private IUserService userService;
    @Autowired
    private IWfProcInstService wfProcInstService;// 流程
    @Autowired
    private IWfTaskService wfTaskService;// 流程

    //    @RequiresPermissions("rental:rentalcarmain:view")
    @RequiresPermissions("rental:rentalcar:view")
    @GetMapping()
    public String rentalcarmain() {
        return prefix + "/rentalcarmain";
    }

    /**
     * 查询车辆 所属 租赁项目列表
     */
    @RequiresPermissions("rental:rentalcar:list")
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(Rentalcarmain rentalcarmain) {

        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("PROVI_")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
            if (role.getCode().startsWith("SUB_")) {//县能耗费管理员
                isSubAdmin = true;
            }
        }
        if (isProAdmin) {//  查询权限设置 分公司
        } else if (isCityAdmin) {
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0)
                rentalcarmain.setCompany(companies.get(0).getId());
        } else if (isSubAdmin) {
            List<IdNameVO> departments = user.getDepartments();
            if (departments != null && departments.size() > 0)
                rentalcarmain.setCountry(departments.get(0).getId());
        } else {
            rentalcarmain.setInputuserid(user.getId());
        }
        startPage();
        List<Rentalcarmain> list = rentalcarmainService.selectList(rentalcarmain);
        for (Rentalcarmain r : list) {
            if (!"1".equals(r.getStatus())) {
                r.set_disabled(true);
            } else {
                r.set_disabled(false);

            }
        }
        return getDataTable(list);
    }

    /**
     * 新增车辆 所属 租赁项目
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存车辆 所属 租赁项目
     */
    @RequiresPermissions("rental:rentalcar:add")
    //@Log(title = "车辆 所属 租赁项目", action = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody Rentalcarmain rentalcarmain) {
        return rentalcarmainService.saveRentalcarmain(rentalcarmain);
    }

    /**
     * 修改车辆 所属 租赁项目
     */
    @GetMapping("/edit/{rcmid}")
    public AjaxResult edit(@PathVariable("rcmid") Long rcmid) {
        Rentalcarmain rentalcarmain = rentalcarmainService.get(rcmid);

        Object object = JSONObject.toJSON(rentalcarmain);

        return this.success(object);
    }

    /**
     * 修改保存车辆 所属 租赁项目
     */
    @RequiresPermissions("rental:rentalcar:edit")
    //@Log(title = "车辆 所属 租赁项目", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody Rentalcarmain rentalcarmain) {
        return toAjax(rentalcarmainService.update(rentalcarmain));
    }

    /**
     * 删除车辆 所属 租赁项目
     */
    @RequiresPermissions("rental:rentalcar:remove")
    //@Log(title = "车辆 所属 租赁项目", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        int i = 0;
        try {
            List<Rentalcarmain> rRentalcarmains = rentalcarmainService.selectListByIds(Convert.toStrArray(ids));
            for (Rentalcarmain b : rRentalcarmains) {
                if (!"1".equals(b.getStatus())) {
                    return this.error(1, b.getSetitle() + "(" + b.getRcmid() + ")" + "草稿状态才能删除");
                }
                // 终止流程
                killFlow(b);
            }
            i = rentalcarmainService.deleteAndItemByIds(Convert.toStrArray(ids));
            return this.success("删除(" + i + ")条");
        } catch (Exception e) {
            e.printStackTrace();
            return this.error(1, "删除失败:" + e.getMessage());
        }
    }

    // 终止流程
    private void killFlow(Rentalcarmain b) throws Exception {
        if (b.getIprocessinstid() != null) {
            WfTask wfTask = new WfTask();
            wfTask.setProcInstId(b.getIprocessinstid().toString());// 流程实例id
            List<WfTask> wfTasks = wfTaskService.selectList(wfTask);
            if (wfTasks != null && wfTasks.size() > 0) {
                Map<String, Object> param = new HashMap<>();
                param.put("procTaskId", wfTasks.get(0).getId());
                param.put("procInstId", b.getIprocessinstid());
                param.put("shardKey", "0000");
                param.put("stop", "sys");
                JSONObject jsonObject = wfProcInstService.stopTask(this.getCurrentUser(), param);
                System.out.println(jsonObject.toJSONString());
            }
        }
    }

    /**
     * 查看车辆 所属 租赁项目
     */
    @RequiresPermissions("rental:rentalcar:view")
    @GetMapping("/view/{rcmid}")
    @ResponseBody
    public AjaxResult view(@PathVariable("rcmid") Long rcmid) {
        Rentalcarmain rentalcarmain = new Rentalcarmain();
        rentalcarmain.setRcmid(rcmid);
        List<Rentalcarmain> rentalcarmains = rentalcarmainService.selectList(rentalcarmain);
        if (rentalcarmains != null && rentalcarmains.size()>0) {
            rentalcarmain = rentalcarmains.get(0);
            List<Rentalcar> rentalcarList = rentalcarService.selectAndNameByRcmid(rcmid);
            if (rentalcarList != null && rentalcarList.size() > 0)
                rentalcarmain.setRentalcarList(rentalcarList);
            else
                rentalcarmain.setRentalcarList(new ArrayList<Rentalcar>());
            Object object = JSONObject.toJSON(rentalcarmain);

            return this.success(object);
        }else{
            return this.error("没查询到 申请信息");
        }
    }

}
