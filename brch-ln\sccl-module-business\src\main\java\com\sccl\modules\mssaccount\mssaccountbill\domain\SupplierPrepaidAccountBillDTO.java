package com.sccl.modules.mssaccount.mssaccountbill.domain;

import com.sccl.framework.aspectj.lang.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 预付管理
 * @date 2024/10/21  14:30
 */
@Data
public class SupplierPrepaidAccountBillDTO {

    /**
     * 序号
     */
    @Excel(name = "序号")
    private String accountNo;

    /**
     * 供应商名称
     */
    @Excel(name = "供应商名称")
    private String supplierName;

    /**
     * 公司
     */
    @Excel(name = "所属公司")
    private String company;

    /**
     * 公司编号
     */
    private String companyNo;

    /**
     * 预付金额
     */
    @Excel(name = "预付金额(元)")
    private BigDecimal preMoney;

    /**
     * 已挑对金额
     */
    @Excel(name = "已挑对金额(元)")
    private BigDecimal checkMoney;

    /**
     * 差额
     */
    @Excel(name = "差额(元)")
    private BigDecimal balanceMoney;
}
