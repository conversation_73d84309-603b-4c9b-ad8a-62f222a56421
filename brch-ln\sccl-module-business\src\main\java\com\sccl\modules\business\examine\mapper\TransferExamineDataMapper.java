package com.sccl.modules.business.examine.mapper;

import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.examine.domain.TransferExamineData;
import com.sccl.modules.business.examine.vo.TransferExamineDataVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 转供电考核明细表 数据层
 * 
 * <AUTHOR>
 * @date 2025-02-21
 */
@Mapper
public interface TransferExamineDataMapper extends BaseMapper<TransferExamineData>
{
    /**
     * 根据主键id数组批量更新
     * @param entity
     * @return
     */
    int updateForModelBatch(TransferExamineData entity);

    /**
     * 根据统计时间批量删除
     * @param tjsj  统计时间【yyyy-MM】
     * @return
     */
    int delByTjsj(@Param("tjsj") String tjsj);

    /**
     * 根据账期查询数据
     * @param tjsj 账期yyyy-MM
     * @return
     */
    List<TransferExamineDataVo> selectByTjsj(@Param("tjsj") String tjsj);
}