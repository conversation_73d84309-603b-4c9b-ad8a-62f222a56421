package com.sccl.modules.rental.rentalsupplier.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;


/**
 * 供应商表 rentalsupplier
 * 
 * <AUTHOR>
 * @date 2019-08-23
 */
public class Rentalsupplier extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	/** 供应商编码 */
	private String lifnr;

	public String getLifnr() {
		return lifnr;
	}

	public void setLifnr(String lifnr) {
		this.lifnr = lifnr;
	}

	/** 供应商帐户组 */
    private String ktokk;
    /** 公司全称 */
    private String name1;
    /** 公司英文名称 */
    private String name2;
    /** 公司简称 */
    private String sort1;
    /** 街道 */
    private String street;
    /** 邮政编码 */
    private String pstlz;
    /** 城市 */
    private String city1;
    /** 国家代码 */
    private String land1;
    /**  */
    private String status;
    /** import date */
    private Date inputdate;


	public void setKtokk(String ktokk)
	{
		this.ktokk = ktokk;
	}

	public String getKtokk() 
	{
		return ktokk;
	}

	public void setName1(String name1)
	{
		this.name1 = name1;
	}

	public String getName1() 
	{
		return name1;
	}

	public void setName2(String name2)
	{
		this.name2 = name2;
	}

	public String getName2() 
	{
		return name2;
	}

	public void setSort1(String sort1)
	{
		this.sort1 = sort1;
	}

	public String getSort1() 
	{
		return sort1;
	}

	public void setStreet(String street)
	{
		this.street = street;
	}

	public String getStreet() 
	{
		return street;
	}

	public void setPstlz(String pstlz)
	{
		this.pstlz = pstlz;
	}

	public String getPstlz() 
	{
		return pstlz;
	}

	public void setCity1(String city1)
	{
		this.city1 = city1;
	}

	public String getCity1() 
	{
		return city1;
	}

	public void setLand1(String land1)
	{
		this.land1 = land1;
	}

	public String getLand1() 
	{
		return land1;
	}

	public void setStatus(String status)
	{
		this.status = status;
	}

	public String getStatus() 
	{
		return status;
	}

	public void setInputdate(Date inputdate)
	{
		this.inputdate = inputdate;
	}

	public Date getInputdate() 
	{
		return inputdate;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("lifnr", getLifnr())
            .append("ktokk", getKtokk())
            .append("name1", getName1())
            .append("name2", getName2())
            .append("sort1", getSort1())
            .append("street", getStreet())
            .append("pstlz", getPstlz())
            .append("city1", getCity1())
            .append("land1", getLand1())
            .append("status", getStatus())
            .append("inputdate", getInputdate())
            .toString();
    }
}
