package com.sccl.modules.rental.rentalcarmodelmain.domain;

import com.sccl.modules.rental.rentalcarmodel.domain.Rentalcarmodel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;

import java.util.Date;
import java.math.BigDecimal;
import java.util.List;


/**
 * 车辆 （车型主单）表 rentalcarmodelmain
 *
 * <AUTHOR>
 * @date 2019-08-23
 */
public class Rentalcarmodelmain extends BaseEntity {
    private List<Rentalcarmodel> rentalcarmodelList;

    public List<Rentalcarmodel> getRentalcarmodelList() {
        return rentalcarmodelList;
    }

    public void setRentalcarmodelList(List<Rentalcarmodel> rentalcarmodelList) {
        this.rentalcarmodelList = rentalcarmodelList;
    }

    private static final long serialVersionUID = 1L;
    private Long rmmid;

    public Long getRmmid() {
        return rmmid;
    }

    public void setRmmid(Long rmmid) {
        this.rmmid = rmmid;
    }

    /**
     *
     */
    private String setitle;
    /**
     *
     */
    private Long inputuserid;
    /**
     *
     */
    private Date inputdate;
    /**
     *
     */
    private String inputusername;
    /**
     *
     */
    private Long iprocessinstid;
    /**
     *
     */
    private String status;
    /**
     *
     */
    private String company;
    /**
     *
     */
    private String country;
    /**
     *
     */
    private String memo;
    /**
     *
     */
    private String spusers;

    private String companyName;
    private String countryName;
    private String statusName;

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public void setSetitle(String setitle) {
        this.setitle = setitle;
    }

    public String getSetitle() {
        return setitle;
    }

    public void setInputuserid(Long inputuserid) {
        this.inputuserid = inputuserid;
    }

    public Long getInputuserid() {
        return inputuserid;
    }

    public void setInputdate(Date inputdate) {
        this.inputdate = inputdate;
    }

    public Date getInputdate() {
        return inputdate;
    }

    public void setInputusername(String inputusername) {
        this.inputusername = inputusername;
    }

    public String getInputusername() {
        return inputusername;
    }

    public void setIprocessinstid(Long iprocessinstid) {
        this.iprocessinstid = iprocessinstid;
    }

    public Long getIprocessinstid() {
        return iprocessinstid;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getCompany() {
        return company;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getCountry() {
        return country;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getMemo() {
        return memo;
    }

    public void setSpusers(String spusers) {
        this.spusers = spusers;
    }

    public String getSpusers() {
        return spusers;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("rmmid", getRmmid())
                .append("setitle", getSetitle())
                .append("inputuserid", getInputuserid())
                .append("inputdate", getInputdate())
                .append("inputusername", getInputusername())
                .append("iprocessinstid", getIprocessinstid())
                .append("status", getStatus())
                .append("company", getCompany())
                .append("country", getCountry())
                .append("memo", getMemo())
                .append("spusers", getSpusers())
                .toString();
    }

    private Boolean _disabled;

    public Boolean get_disabled() {
        return _disabled;
    }

    public void set_disabled(Boolean _disabled) {
        this._disabled = _disabled;
    }

}
