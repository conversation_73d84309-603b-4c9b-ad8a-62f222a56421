package com.sccl.modules.business.stationreportwhitelist.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/4/22 16:32
 * @describe 流程状态
 */
@Getter
@AllArgsConstructor
public enum MeterCategory {
    //电表
    ELECTRIC_METER("1", "电表"),
    //支出无表协议
    OUT_WITHOUT_PROTOCOL("2", "支出无表协议"),
    //支出有表协议
    OUT_WITH_PROTOCOL("3", "支出有表协议"),
    //收入无表协议
    IN_WITHOUT_PROTOCOL("4", "收入无表协议"),
    //收入有表协议
    IN_WITH_PROTOCOL("5", "收入有表协议");

    /**
     * 状态码
     */
    private final String code;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 根据code获取name
     */
    public static String getNameByCode(Integer code) {
        for (MeterCategory billStatus : MeterCategory.values()) {
            if (billStatus.getCode().equals(code)) {
                return billStatus.getName();
            }
        }
        return null;
    }
}
