package com.sccl.modules.rental.rentalcarorder.domain;

import com.sccl.modules.rental.rentalordercarmodel.domain.RentalorderCarmodel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;


/**
 * 车辆租赁申请表 rentalcarorder
 * 
 * <AUTHOR>
 * @date 2019-08-23
 */
public class Rentalcarorder extends BaseEntity
{
	private List<RentalorderCarmodel> rentalorderCarmodels;

	public List<RentalorderCarmodel> getRentalorderCarmodels() {
		return rentalorderCarmodels;
	}

	public void setRentalorderCarmodels(List<RentalorderCarmodel> rentalorderCarmodels) {
		this.rentalorderCarmodels = rentalorderCarmodels;
	}

	private static final long serialVersionUID = 1L;
	private Long rcoid;

	public Long getRcoid() {
		return rcoid;
	}

	public void setRcoid(Long rcoid) {
		this.rcoid = rcoid;
	}

	/** 编号 */
    private String rcoNo;
    /** 申请事项 */
    private String rcoTitle;
    /** 备注说明 */
    private String memo;
    /** 申请时间 */
    private Date inputdate;
    /** 申请人id */
    private Long inputuserid;
    /** 单据状态 */
    private String status;
    /** 流程号 */
    private Long iprocessinstid;
    /** 单位 */
    private String company;
    /** 部门 */
    private String country;
    /** 申请人联系电话 */
    private String inputuserphone;
    /** 联系人 */
    private String contractname;
    /** 联系人联系电话 */
    private String contractphone;
    /** 申请人姓名 */
    private String inputusername;

	private String companyName;
	private String countryName;
	private String statusName;
	private Date inputdateStart;
	private Date inputdateEnd;

	public Date getInputdateStart() {
		return inputdateStart;
	}

	public void setInputdateStart(Date inputdateStart) {
		this.inputdateStart = inputdateStart;
	}

	public Date getInputdateEnd() {
		return inputdateEnd;
	}

	public void setInputdateEnd(Date inputdateEnd) {
		this.inputdateEnd = inputdateEnd;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public String getCountryName() {
		return countryName;
	}

	public void setCountryName(String countryName) {
		this.countryName = countryName;
	}

	public String getStatusName() {
		return statusName;
	}

	public void setStatusName(String statusName) {
		this.statusName = statusName;
	}

	public void setRcoNo(String rcoNo)
	{
		this.rcoNo = rcoNo;
	}

	public String getRcoNo() 
	{
		return rcoNo;
	}

	public void setRcoTitle(String rcoTitle)
	{
		this.rcoTitle = rcoTitle;
	}

	public String getRcoTitle() 
	{
		return rcoTitle;
	}

	public void setMemo(String memo)
	{
		this.memo = memo;
	}

	public String getMemo() 
	{
		return memo;
	}

	public void setInputdate(Date inputdate)
	{
		this.inputdate = inputdate;
	}

	public Date getInputdate() 
	{
		return inputdate;
	}

	public void setInputuserid(Long inputuserid)
	{
		this.inputuserid = inputuserid;
	}

	public Long getInputuserid()
	{
		return inputuserid;
	}

	public void setStatus(String status)
	{
		this.status = status;
	}

	public String getStatus() 
	{
		return status;
	}

	public void setIprocessinstid(Long iprocessinstid)
	{
		this.iprocessinstid = iprocessinstid;
	}

	public Long getIprocessinstid()
	{
		return iprocessinstid;
	}

	public void setCompany(String company)
	{
		this.company = company;
	}

	public String getCompany()
	{
		return company;
	}

	public void setCountry(String country)
	{
		this.country = country;
	}

	public String getCountry()
	{
		return country;
	}

	public void setInputuserphone(String inputuserphone)
	{
		this.inputuserphone = inputuserphone;
	}

	public String getInputuserphone() 
	{
		return inputuserphone;
	}

	public void setContractname(String contractname)
	{
		this.contractname = contractname;
	}

	public String getContractname() 
	{
		return contractname;
	}

	public void setContractphone(String contractphone)
	{
		this.contractphone = contractphone;
	}

	public String getContractphone() 
	{
		return contractphone;
	}

	public void setInputusername(String inputusername)
	{
		this.inputusername = inputusername;
	}

	public String getInputusername() 
	{
		return inputusername;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("rcoid", getRcoid())
            .append("rcoNo", getRcoNo())
            .append("rcoTitle", getRcoTitle())
            .append("memo", getMemo())
            .append("inputdate", getInputdate())
            .append("inputuserid", getInputuserid())
            .append("status", getStatus())
            .append("iprocessinstid", getIprocessinstid())
            .append("company", getCompany())
            .append("country", getCountry())
            .append("inputuserphone", getInputuserphone())
            .append("contractname", getContractname())
            .append("contractphone", getContractphone())
            .append("inputusername", getInputusername())
            .toString();
    }

	private Boolean _disabled;

	public Boolean get_disabled() {
		return _disabled;
	}

	public void set_disabled(Boolean _disabled) {
		this._disabled = _disabled;
	}
}
