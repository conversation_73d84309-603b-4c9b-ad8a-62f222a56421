package com.sccl.modules.mssaccount.mssgro.service;

import com.sccl.modules.mssaccount.mssgro.domain.MssGro;
import com.sccl.framework.service.IBaseService;

import java.util.List;

/**
 * 财务辅助提供的组织级次 预算责任中心 服务层
 * 
 * <AUTHOR>
 * @date 2019-05-01
 */
public interface IMssGroService extends IBaseService<MssGro>
{


    List<MssGro> selectByLikeAuto(MssGro mssGro);

    List<MssGro> selectByLikeAutoNew(MssGro mssGro);
}
