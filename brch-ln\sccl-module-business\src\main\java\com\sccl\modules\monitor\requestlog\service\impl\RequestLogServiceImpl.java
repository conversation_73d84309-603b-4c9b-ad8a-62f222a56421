package com.sccl.modules.monitor.requestlog.service.impl;

import com.sccl.modules.monitor.requestlog.domain.RequestLog;
import com.sccl.modules.monitor.requestlog.mapper.RequestLogMapper;
import com.sccl.modules.monitor.requestlog.service.IRequestLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 请求日志Service实现类
 */
@Slf4j
@Service
public class RequestLogServiceImpl implements IRequestLogService {

    @Autowired
    private RequestLogMapper requestLogMapper;

    @Override
    @Async
    public void asyncSave(RequestLog requestLog) {
        try {
            requestLogMapper.insert(requestLog);
        } catch (Exception e) {
            log.error("保存请求日志失败", e);
        }
    }
} 