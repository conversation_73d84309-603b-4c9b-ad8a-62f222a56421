package com.sccl.modules.business.stationinfo.controller;

import java.util.List;

import com.sccl.modules.business.stationinfo.domain.PowerStationInfoRJtlte;
import com.sccl.modules.business.stationinfo.service.IPowerStationInfoRJtlteService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 集团LTE和局站对应 信息操作处理
 * 
 * <AUTHOR>
 * @date 2021-05-13
 */
@RestController
@RequestMapping("/ammeterorprotocol/powerStationInfoRJtlte")
public class PowerStationInfoRJtlteController extends BaseController
{
    private String prefix = "ammeterorprotocol/powerStationInfoRJtlte";
	
	@Autowired
	private IPowerStationInfoRJtlteService powerStationInfoRJtlteService;
	
	@RequiresPermissions("ammeterorprotocol:powerStationInfoRJtlte:view")
	@GetMapping()
	public String powerStationInfoRJtlte()
	{
	    return prefix + "/powerStationInfoRJtlte";
	}
	
	/**
	 * 查询集团LTE和局站对应列表
	 */
	@RequiresPermissions("ammeterorprotocol:powerStationInfoRJtlte:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(PowerStationInfoRJtlte powerStationInfoRJtlte)
	{
		startPage();
        List<PowerStationInfoRJtlte> list = powerStationInfoRJtlteService.selectList(powerStationInfoRJtlte);
		return getDataTable(list);
	}
	
	/**
	 * 新增集团LTE和局站对应
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存集团LTE和局站对应
	 */
	@RequiresPermissions("ammeterorprotocol:powerStationInfoRJtlte:add")
	@Log(title = "集团LTE和局站对应", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody PowerStationInfoRJtlte powerStationInfoRJtlte)
	{		
		return toAjax(powerStationInfoRJtlteService.insert(powerStationInfoRJtlte));
	}

	/**
	 * 修改集团LTE和局站对应
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		PowerStationInfoRJtlte powerStationInfoRJtlte = powerStationInfoRJtlteService.get(id);

		Object object = JSONObject.toJSON(powerStationInfoRJtlte);

        return this.success(object);
	}
	
	/**
	 * 修改保存集团LTE和局站对应
	 */
	@RequiresPermissions("ammeterorprotocol:powerStationInfoRJtlte:edit")
	@Log(title = "集团LTE和局站对应", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody PowerStationInfoRJtlte powerStationInfoRJtlte)
	{		
		return toAjax(powerStationInfoRJtlteService.update(powerStationInfoRJtlte));
	}
	
	/**
	 * 删除集团LTE和局站对应
	 */
	@RequiresPermissions("ammeterorprotocol:powerStationInfoRJtlte:remove")
	@Log(title = "集团LTE和局站对应", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(powerStationInfoRJtlteService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看集团LTE和局站对应
     */
    @RequiresPermissions("ammeterorprotocol:powerStationInfoRJtlte:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		PowerStationInfoRJtlte powerStationInfoRJtlte = powerStationInfoRJtlteService.get(id);

        Object object = JSONObject.toJSON(powerStationInfoRJtlte);

        return this.success(object);
    }

}
