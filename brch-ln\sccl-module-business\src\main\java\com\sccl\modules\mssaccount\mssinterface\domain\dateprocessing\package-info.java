/**
 * 日期处理相关的领域对象包
 * 
 * <p>本包包含用于处理日期范围分析和能耗分配的领域对象：</p>
 * 
 * <ul>
 *   <li>{@link com.sccl.modules.mssaccount.mssinterface.domain.dateprocessing.DateRange} - 日期范围封装</li>
 *   <li>{@link com.sccl.modules.mssaccount.mssinterface.domain.dateprocessing.DateRangeAnalysis} - 日期范围分析结果</li>
 *   <li>{@link com.sccl.modules.mssaccount.mssinterface.domain.dateprocessing.EnergyDistribution} - 能耗分配结果</li>
 *   <li>{@link com.sccl.modules.mssaccount.mssinterface.domain.dateprocessing.DateProcessingResult} - 最终处理结果</li>
 * </ul>
 * 
 * <h3>主要功能</h3>
 * <p>这些类主要用于支持智能采集数据同步过程中的日期处理逻辑：</p>
 * <ol>
 *   <li>分析日期范围，区分有效和无效日期</li>
 *   <li>计算能耗重新分配</li>
 *   <li>生成最终的处理结果</li>
 * </ol>
 * 
 * <h3>设计原则</h3>
 * <ul>
 *   <li><strong>不可变性</strong>：所有对象都是不可变的，确保线程安全</li>
 *   <li><strong>数据验证</strong>：构造时进行参数验证，确保数据有效性</li>
 *   <li><strong>职责单一</strong>：每个类只负责特定的数据封装和计算</li>
 *   <li><strong>易于测试</strong>：提供清晰的接口和方法，便于单元测试</li>
 * </ul>
 * 
 * <h3>使用示例</h3>
 * <pre>{@code
 * // 创建日期范围
 * DateRange range = new DateRange(startDate, endDate);
 * 
 * // 分析日期范围
 * DateRangeAnalysis analysis = new DateRangeAnalysis(validDates, excludedDates, totalDays);
 * 
 * // 计算能耗分配
 * EnergyDistribution distribution = new EnergyDistribution(
 *     energyData, acData, oepgData, pvpgData, 
 *     deviceData, productionData, managementData, businessData, otherData);
 * 
 * // 生成最终结果
 * DateProcessingResult result = new DateProcessingResult(collectMeterList, validDays, excludedDays);
 * }</pre>
 * 
 * <AUTHOR>
 * @since 2025-07-22
 * @version 1.0
 */
package com.sccl.modules.mssaccount.mssinterface.domain.dateprocessing;
