package com.sccl.modules.business.meterinfo.controller;

import com.alibaba.fastjson.JSONObject;
import com.sccl.common.lang.StringUtils;
import com.sccl.common.support.Convert;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.common.exception.base.BaseException;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.meterinfo.domain.Meterinfo;
import com.sccl.modules.business.meterinfo.service.IMeterinfoService;
import com.sccl.modules.system.attachments.domain.UpLoadFile;
import com.sccl.modules.system.role.domain.Role;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.service.IUserService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 计量设备 信息操作处理
 *
 * <AUTHOR>
 * @date 2023-03-15
 */
@RestController
@RequestMapping("/business/meterinfo")
public class MeterinfoController extends BaseController {
    private String prefix = "business/meterinfo";

    @Autowired
    private IMeterinfoService meterinfoService;
    @Autowired
    private IUserService userService;

    @RequiresPermissions("business:meterinfo:view")
    @GetMapping()
    public String meterinfo() {
        return prefix + "/meterinfo";
    }

    /**
     * 查询计量设备列表
     */
    @RequiresPermissions("business:meterinfo:list")
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(Meterinfo meterinfo) {
        //权限判定 组装
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("PROVI_")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
        }
        if (!isCityAdmin && !isProAdmin) {
            throw new BaseException("权限不足");
        }
        startPage();
        List<Meterinfo> list = meterinfoService.selectList(meterinfo);
        return getDataTable(list);
    }

    /**
     * 新增计量设备
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存计量设备
     */
    @RequiresPermissions("business:meterinfo:add")
    @Log(title = "计量设备", action = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody Meterinfo meterinfo) {
        return toAjax(meterinfoService.insert(meterinfo));
    }

    /**
     * 修改计量设备
     */
    @GetMapping("/edit/{id}")
    public AjaxResult edit(@PathVariable("id") Long id) {
        //权限判定 组装
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("PROVI_")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
        }
        if (!isCityAdmin && !isProAdmin) {
            throw new BaseException("权限不足");
        }
        Meterinfo meterinfo = meterinfoService.get(id);

        Object object = JSONObject.toJSON(meterinfo);

        return this.success(object);
    }

    /**
     * 修改保存计量设备
     */
    @RequiresPermissions("business:meterinfo:edit")
    @Log(title = "计量设备", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody Meterinfo meterinfo) {
        //权限判定 组装
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("PROVI_")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
        }
        if (!isCityAdmin && !isProAdmin) {
            throw new BaseException("权限不足");
        }
        return toAjax(meterinfoService.update(meterinfo));
    }

    /**
     * 删除计量设备
     */
    @RequiresPermissions("business:meterinfo:remove")
    @Log(title = "计量设备", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(meterinfoService.deleteByIds(Convert.toStrArray(ids)));
    }


    /**
     * 查看计量设备
     */
    @RequiresPermissions("business:meterinfo:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id) {
        Meterinfo meterinfo = meterinfoService.get(id);

        Object object = JSONObject.toJSON(meterinfo);

        return this.success(object);
    }


    @PostMapping(value = "/uploadExcel")
    @ResponseBody
    public Map<String, Object> uploadExcel(HttpServletRequest request, HttpServletResponse response,
                                           UpLoadFile uploadFile)
            throws Exception {
        response.setContentType("text/html;charset=utf-8");
        Map<String, Object> map = new HashMap<>();
        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        Iterator<String> iterator = multiRequest.getFileNames();
        String str = "";
        List<Meterinfo> list = new ArrayList<>();
        while (iterator.hasNext()) {
            String name = iterator.next();
            List<MultipartFile> files = new LinkedList<>();
            files = multiRequest.getFiles(name);
            // 一次遍历所有文件
            for (MultipartFile file : files) {
                if (file == null) {
                    continue;
                }

                //如果文件大小为0则不上传
                long fileSize = file.getSize();
                if (fileSize <= 0L) {
                    throw new Exception("文件名称：【" + file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("\\") + 1, file.getOriginalFilename().length()) + "】的文件大小为0,请核对!");
                }
                list = meterinfoService.importExcel("sheet1", file.getInputStream());

            }

        }


        if (list != null && list.size() > 0) {
            str += "成功解析【" + list.size() + "】条数据；";
            int listSize = list.size();
            int toIndex = 1000;
            for (int i = 0; i < list.size(); i += 1000) {
                //作用为toIndex最后没有1000条数据则剩余几条newList中就装几条
                if (i + 1000 > listSize) {
                    toIndex = listSize - i;
                }
                List newList = list.subList(i, i + toIndex);
                int n = meterinfoService.insertListForTemporary(newList);//保存到临时表
            }

            //加入到正式表
            int num = 0;
            num = meterinfoService.insertInfo();
            str += "成功加入【" + num + "】条数据到；计量设备集团全量表";

            //删除临时表
            meterinfoService.deleteRepeat();
            map.put("str", str);
        }
        if (StringUtils.isEmpty(str)) {
            map.put("str", "导入失败");
        } else {
            map.put("str", str);
        }

        return map;
    }

}
