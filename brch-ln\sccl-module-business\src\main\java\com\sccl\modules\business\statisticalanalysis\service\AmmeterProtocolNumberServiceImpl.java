package com.sccl.modules.business.statisticalanalysis.service;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.business.statisticalanalysis.domain.AmmeterProtocolNumber;
import com.sccl.modules.business.statisticalanalysis.mapper.AmmeterProtocolNumberMapper;
import com.sccl.modules.business.statisticalanalysis.service.IAmmeterProtocolNumberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AmmeterProtocolNumberServiceImpl extends BaseServiceImpl<AmmeterProtocolNumber> implements IAmmeterProtocolNumberService {
    @Autowired
    private AmmeterProtocolNumberMapper numberMapper;

    @Override
    public List<AmmeterProtocolNumber> findGroupByCompany(AmmeterProtocolNumber ammeterProtocolNumber) {
        List<AmmeterProtocolNumber> list = numberMapper.selectGroupByCompany(ammeterProtocolNumber);
        for (AmmeterProtocolNumber a : list) {
            a.setNumSum(a.getAEnableNum() + a.getADisableNum() + a.getPEnableNum() + a.getPDisableNum());
        }
        return list;
    }

    @Override
    public List<AmmeterProtocolNumber> findGroupByCountry(AmmeterProtocolNumber ammeterProtocolNumber) {
        List<AmmeterProtocolNumber> list = numberMapper.selectGroupByCountry(ammeterProtocolNumber);
        for (AmmeterProtocolNumber a : list) {
            a.setNumSum(a.getAEnableNum() + a.getADisableNum() + a.getPEnableNum() + a.getPDisableNum());
        }
        return list;
    }
}
