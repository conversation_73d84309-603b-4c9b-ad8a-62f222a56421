package com.sccl.modules.monitor.requestlog.controller;

import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.business.cache.utils.RedisUtil;
import com.sccl.modules.system.config.domain.Config;
import com.sccl.modules.system.config.service.IConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.TimeUnit;

/**
 * 请求日志控制器
 */
@RestController
@RequestMapping("/monitor/requestlog")
public class RequestLogController extends BaseController {

    // Redis缓存键名
    private static final String REDIS_REQUEST_LOG_ENABLED = "request_log_enabled";
    // Redis缓存过期时间（1小时）
    private static final long REDIS_EXPIRE_TIME = 3600L;
    // 系统参数键名
    private static final String REQUEST_LOG_ENABLED = "request.log.enabled";
    @Autowired
    private IConfigService configService;

    /**
     * 获取请求日志开关状态
     */
    @GetMapping("/status")
    public AjaxResult getStatus() {
        try {
            // 先从Redis获取
            String enabled = RedisUtil.getStr(REDIS_REQUEST_LOG_ENABLED);

            // 如果Redis中没有，则从数据库获取
            if (enabled == null) {
                Config config = configService.getByConfigKey(REQUEST_LOG_ENABLED);
                if (config != null) {
                    enabled = config.getConfigValue();
                }
            }

            return success("true".equalsIgnoreCase(enabled));
        } catch (Exception e) {
            return error("获取请求日志开关状态失败");
        }
    }

    /**
     * 修改请求日志开关状态
     */
    @Log(title = "请求日志开关", action = BusinessType.UPDATE)
    @PutMapping("/status/{status}")
    public AjaxResult updateStatus(@PathVariable("status") boolean status) {
        try {
            // 更新数据库配置
            Config config = configService.getByConfigKey(REQUEST_LOG_ENABLED);
            if (config == null) {
                return error("请求日志开关配置不存在");
            }

            config.setConfigValue(String.valueOf(status));
            configService.updateForModel(config);

            // 更新Redis缓存
            RedisUtil.set(REDIS_REQUEST_LOG_ENABLED, String.valueOf(status), REDIS_EXPIRE_TIME, TimeUnit.SECONDS);

            return success();
        } catch (Exception e) {
            return error("修改请求日志开关状态失败");
        }
    }
}
