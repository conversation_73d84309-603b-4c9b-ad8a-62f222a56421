package com.sccl.modules.business.stationaudit.pcomparequtoa;

import com.enrising.dcarbon.audit.*;
import com.sccl.common.utils.BigDecimlUtil;
import com.sccl.common.utils.SpringUtil;
import com.sccl.modules.autojob.util.convert.StringUtils;
import com.sccl.modules.business.account.domain.Account;
import com.sccl.modules.mssaccount.mssaccountbill.mapper.MssAccountbillMapper;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
public class quotaCompareCreator extends AbstractRefereeDatasourceCreator {
    @Override
    public Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> collect(Auditable auditable) {
        //数据源
        Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> datasource = new HashMap<>();

        //从Spring上下文取得mapper
        MssAccountbillMapper mapper = SpringUtil.getBean(MssAccountbillMapper.class);
        //可能上下文没有这个mapper
        if (mapper == null) {
            return datasource;
        }
        Account account = (Account) auditable;
        //根据 台账主键 获取对应的平均电量比对信息
        Long pcid = account.getPcid();
        quotaCompareContent content = mapper.getCompareQuota(pcid);

        log.info("判空");

        if (content == null) {
            return datasource;
        }

        log.info("开始评判");
        String resstationcode = content.getResstationcode();
        if (StringUtils.isBlank(content.getResstationcode())) {
            content.setExmsg(String.format("对应台账:能耗系统-资源局站编码基础数据缺失"));
        } else if (StringUtils.isBlank(content.getStartdate()) || StringUtils.isBlank(content.getEnddate())) {
            content.setExmsg(String.format("对应台账:能耗系统-起始、截至时间 基础数据缺失"));
        } else if (content.getAvgForBill() == null || (content.getAvgForBill().compareTo(BigDecimal.ZERO) == 0)) {
            content.setExmsg(String.format("对应资源系统局站编码:-能耗系统无对应日均电量"));
        } else if (content.getAvgForGateway() == null || (content.getAvgForGateway().compareTo(BigDecimal.ZERO) == 0)) {
            content.setExmsg(String.format("对应资源系统局站编码:-网管无对应日均电量"));
        } else if (BigDecimlUtil.isDiffGreaterThanOrEqualTo(content.getAvgForBill(), content.getAvgForGateway(),
                                                            new BigDecimal(20)
        )) {
            content.setExmsg("对应资源系统局站编码:能耗系统：网管日均电量超过20%");
        }
        //构造auditResult
        RefereeResult refereeResult = new RefereeResult("网管日均电量比对", true, "成功");
        content.setAuditKey(pcid + "");
        content.setRefereeResult(refereeResult);
        content.setStep(9);

        AuditResult auditResult = content.getAuditResult();


        //可以添加多种不同类型的评判数据
        //2添加到数据源
        datasource.put(quotaCompareContent.class, new ArrayList<>(Arrays.asList(auditResult)));
        return datasource;
    }

    @Override
    public AbstractReferee getReferee() {
        return new quotaCompareReferen("网管日均电量比对");
    }

}
