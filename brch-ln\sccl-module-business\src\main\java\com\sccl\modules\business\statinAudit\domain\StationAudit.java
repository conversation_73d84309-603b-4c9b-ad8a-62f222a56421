package com.sccl.modules.business.statinAudit.domain;

import java.util.Date;
import lombok.Data;

/**
 * @Author: 李佳杰
 * @CreateTime: 2024-02-19  13:57
 * @Description: 台账稽核异常原因数据
 * @Version: 1.0
 */
@Data
public class StationAudit {
    /** 电表/协议编号 */
    String dbbm;
    /** 局站编码 */
    String jzbm;
    /** 铁塔站址编码 */
    String ttzzbm;
    /** 报账单Id */
    String bzdid;
    /** 报账申请日期 */
    String bzsqrq;
    /** 一表多站/一站多表 */
    String pdybdz;
    /** 电表站址一致性 */
    String dbzzyzx;
    /** 电表与支付对象一致性 */
    String dbzfdxyzx;
    /** 重复交叉缴费 */
    String cfjxjf;
    /** 电表读数不连续 */
    String dbdsblx;
    /** 台账电量合理性 */
    String tzdlhlx;
    /** 台账日均电量合理性 */
    String tzrjdlhlx;
    /** 共享站分摊比例准确性 */
    String gxzftblzqx;
    /** 独站分摊比例准确性 */
    String dzftblzqx;
    /** 台账周期异常 */
    String tzzqyc;
    /** 沉默电表 */
    String cmdb;
    /** 期号 */
    String qh;
    /** 创建时间 */
    Date createTime;
    /** 稽核结果 */
    String jhjg;
}
