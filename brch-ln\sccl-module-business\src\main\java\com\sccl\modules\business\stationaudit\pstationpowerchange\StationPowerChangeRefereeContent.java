package com.sccl.modules.business.stationaudit.pstationpowerchange;


import com.enrising.dcarbon.audit.AbstractRefereeContent;
import com.enrising.dcarbon.audit.RefereeDatasource;
import com.enrising.dcarbon.audit.RefereeResult;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
@Data
public class StationPowerChangeRefereeContent extends AbstractRefereeContent implements RefereeDatasource {
    private Long  billId;

    /**
     * 本次台账id
     */
    private Long  pcid;

    /**
     * 台账录入日期
     */
    private String startdate;
    /**
     * 台账截至日期
     */
    private String enddate;
    /**
     * 本地电表id
     */
    private Long  ammeterid;
    /**
     * 电表名称
     */
    private String   ammetername;
    /**
     * 本次站址id
     */
    private String stationCode;
    /**
     * 项目名称
     */
    private String projectname;
    /**
     * 局站名称
     */
    private String stationname;
    /**
     * 局站地址
     */
    private String stationaddress;
    /**
     * 台账对应电量
     */
    private BigDecimal power;


    /**
     * 比对的台账id
     */
    private Long pcid_compare;
    /**
     * 比对的电表id
     */
    private Long ammeterid_compare;
    private String ammetername_compare;
    /**
     * 比对的站址id
     */
    private String  stationcode_compare;

    /**
     * 比对的电量
     */
    private BigDecimal power_compare;

    /**
     * 本次台账电量 偏离上一次的比例
     */
    private BigDecimal widepower;

    /**
     * 比对的集团站址id
     */
    private String  station_source_compare;

    /**
     * 项目名称
     */
    private String projectname_compare;
    /**
     * 局站名称
     */
    private String stationname_compare;
    /**
     * 局站地址
     */
    private String stationaddress_compare;


    public Long getPcid() {
        return pcid;
    }

    public void setPcid(Long pcid) {
        this.pcid = pcid;
    }



    public String getStationCode() {
        return stationCode;
    }

    public void setStationCode(String stationCode) {
        this.stationCode = stationCode;
    }

    public String getProjectname() {
        return projectname;
    }

    public void setProjectname(String projectname) {
        this.projectname = projectname;
    }

    public String getStationname() {
        return stationname;
    }

    public void setStationname(String stationname) {
        this.stationname = stationname;
    }

    public String getStationaddress() {
        return stationaddress;
    }

    public void setStationaddress(String stationaddress) {
        this.stationaddress = stationaddress;
    }

    public BigDecimal getPower() {
        return power;
    }

    public void setPower(BigDecimal power) {
        this.power = power;
    }



    public StationPowerChangeRefereeContent(RefereeResult refereeResult, int step, String auditKey) {
        super(refereeResult, step, auditKey);
    }



    public StationPowerChangeRefereeContent() {

    }
}
