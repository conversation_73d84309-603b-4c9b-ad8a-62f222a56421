package com.sccl.modules.business.stationreportwhitelist.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.Lists;
import com.sccl.modules.business.stationreportwhitelist.domain.MpAttachments;
import com.sccl.modules.business.stationreportwhitelist.domain.StationReportWhitelistBill;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/6 16:12
 * @describe 单据详情
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StationReportWhitelistBillVO extends StationReportWhitelistBill {

    /**
     * 局(站)id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long stationId;

    /**
     * 局站名称
     */
    private String stationname;

    /**
     * 局站类型
     */
    private String stationtype;

    /**
     * 局站类型名称
     */
    private String stationtypeName;
    /**
     * 分公司
     */
    private Long company;

    /**
     * 分公司名称
     */
    private String companyName;

    /**
     * 部门
     */
    private Long country;

    /**
     * 部门名称
     */
    private String countryName;

    /**
     * 局站关联电表数量
     */
    private Long stationMetersCount;


    /**
     * 流程单据状态名称
     */
    private String billStatusName;

    /**
     * 流程业务模块编码
     */
    private String busiAlias;

    /**
     * 电表列表
     */
    private List<PowerAmmeterorprotocolVO> meterList;

    /**
     * 附件列表
     */
    private List<MpAttachments> fileList;

    public List<PowerAmmeterorprotocolVO> getMeterList() {
        if (meterList == null) {
            meterList = Lists.newArrayList();
        }
        return meterList;
    }
}

