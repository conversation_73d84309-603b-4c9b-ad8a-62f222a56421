package com.sccl.modules.business.pylonBG.service;

import com.sccl.framework.service.IBaseService;
import com.sccl.modules.business.account.domain.Account;
import com.sccl.modules.business.account.domain.AccountBaseResult;
import com.sccl.modules.business.account.domain.AccountCondition;
import com.sccl.modules.business.accountbillitempre.domain.Accountbillitempre;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * __description__<br/>
 * 2019/7/18
 *
 * <AUTHOR>
 */
public interface IPylonBGService extends IBaseService<Account> {

    /**
     * @Description: 铁塔包干录入界面列表
     * @author: dongk
     * @date: 2019/7/18
     * @param:
     * @return:
     */
    List<AccountBaseResult> selectByParams(AccountCondition condition);

    /**
     * @Description: 保存更新数据
     * @author: dongk
     * @date: 2019/7/22
     * @param:
     * @return:
     */
    Map<String,Object> updateData(List<Account> accountList);

    /**
     * @Description: 删除
     * @author: dongk
     * @date: 2019/7/22
     * @param:
     * @return:
     */
    int deleteAccountByIds(String[] pcids);

    /**
     * @Description: 通过公司部门id查询单价，然后开始时间跟结束时间对比返回单价
     * @author: dongk
     * @date: 2019/7/22
     * @param:
     * @return:
     */
    void selectlumpprice(Long orgid,Long company,AccountBaseResult baseResult);

    /**
     * @Description: 铁塔包干台账合计
     * @author: dongk
     * @date: 2019/7/23
     * @param:
     * @return:
     */
    AccountBaseResult accountTotal(AccountCondition condition);

    /**
     * @Description: 查询归集单下面所有台账
     * @author: dongk
     * @date: 2019/7/23
     * @param:
     * @return:
     */
    List<AccountBaseResult> selectListByPre(Accountbillitempre accountbillitempre);

    /**
     * @Description: 包干查询功能列表
     * @author: dongk
     * @date: 2019/7/24
     * @param:
     * @return:
     */
    List<AccountBaseResult> selectQuery(AccountCondition condition);

    /**
     * @Description: 铁塔包干台账合计
     * @author: dongk
     * @date: 2019/7/25
     * @param:
     * @return:
     */
    AccountBaseResult queryTotal(AccountCondition condition);

}
