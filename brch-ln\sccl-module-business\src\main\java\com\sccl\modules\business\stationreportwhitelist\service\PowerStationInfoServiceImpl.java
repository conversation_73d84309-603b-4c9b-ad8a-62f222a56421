package com.sccl.modules.business.stationreportwhitelist.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.google.common.collect.Lists;
import com.sccl.modules.business.statinAudit.domain.SysOrganizations;
import com.sccl.modules.business.statinAudit.mapper.PowerStationInfoMapper;
import com.sccl.modules.business.stationreportwhitelist.domain.PowerStationInfo;
import com.sccl.modules.business.stationreportwhitelist.dto.PowerStationInfoQuery;
import com.sccl.modules.business.stationreportwhitelist.enums.MyDict;
import com.sccl.modules.business.stationreportwhitelist.enums.PowerAmmeterorprotocolStatus;
import com.sccl.modules.business.stationreportwhitelist.mapper.MpPowerStationInfoMapper;
import com.sccl.modules.business.stationreportwhitelist.vo.PowerStationInfoVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PowerStationInfoServiceImpl extends ServiceImpl<MpPowerStationInfoMapper, PowerStationInfo> implements PowerStationInfoService {

    private final PowerStationInfoMapper stationInfoMapper;

    @Override
    public Page<PowerStationInfoVO> oneStopIsMoreThanOneWatch(Page<PowerStationInfoVO> page, PowerStationInfoQuery query) {
        QueryWrapper<PowerStationInfoQuery> wrapper = new QueryWrapper<>();
        wrapper.like(StrUtil.isNotBlank(query.getStationcode()), "psi.stationcode", query.getStationcode());
        wrapper.like(StrUtil.isNotBlank(query.getStationname()), "psi.stationname", query.getStationname());
        wrapper.eq(query.getCompany() != null, "psi.company", query.getCompany());
        wrapper.eq(query.getCountry() != null, "psi.country", query.getCountry());
        wrapper.eq("pa.status", PowerAmmeterorprotocolStatus.IN_USE.getCode());
        wrapper.isNotNull("pa.stationcode");
        wrapper.groupBy("pa.stationcode");
        wrapper.having("COUNT(0) > 1");
        Page<PowerStationInfoVO> voPage = baseMapper.oneStopIsMoreThanOneWatch(page, wrapper);
        List<Long> idList = voPage.getRecords().stream().map(PowerStationInfo::getId).collect(Collectors.toList());
        if (idList.isEmpty()) {
            return voPage;
        }
        List<PowerStationInfo> infos = baseMapper.selectBatchIds(idList);
        for (PowerStationInfoVO vo : voPage.getRecords()) {
            for (PowerStationInfo info : infos) {
                if (ObjectUtil.equal(vo.getId(), info.getId())) {
                    BeanUtils.copyProperties(info, vo);
                    this.setAdditionalParameters(vo);
                }
            }
        }

        return voPage;
    }

    @Override
    public List<PowerStationInfoVO> oneTableMultiStationList(Page<PowerStationInfoVO> page, PowerStationInfoQuery query) {
        // 将ids用逗号分隔
        if (StrUtil.isBlank(query.getStationCodes())) {
            return Lists.newArrayList();
        }
        List<String> idList = StrUtil.split(query.getStationCodes(), ",");
        // 查询局站列表
        MPJLambdaWrapper<PowerStationInfo> wrapper = new MPJLambdaWrapper<>();
        wrapper.in( PowerStationInfo::getId, idList);
        wrapper.eq(StrUtil.isNotBlank(query.getStationcode()), PowerStationInfo::getStationcode, query.getStationcode());
        List<PowerStationInfoVO> stationInfos = baseMapper.selectJoinList(PowerStationInfoVO.class, wrapper);
        stationInfos.forEach(this::setAdditionalParameters);
        return stationInfos;
    }

    private void setAdditionalParameters(PowerStationInfoVO vo) {
        getOrgName(vo);
        vo.setStationtypeName(MyDict.findDictLabel(MyDict.TYPE.BUR_STAND_TYPE, vo.getStationtype()));
//         对外结算类型
//        vo.setDirectsupplyflagName(MyDict.findDictLabel(MyDict.TYPE.directSupplyFlag,String.valueOf(vo.getDirectsupplyflag())));
        // 产权
        vo.setPropertyrightName(MyDict.findDictLabel(MyDict.TYPE.propertyRight,String.valueOf(vo.getPropertyright())));
    }

    /**
     * 获取公司名称
     *
     * @param vo 白名单
     */
    private void getOrgName(PowerStationInfoVO vo) {
        // 分公司
        if (vo.getCompany() != null) {
            SysOrganizations orgCompany = stationInfoMapper.selectsysOrgNameById(vo.getCompany());
            if (orgCompany != null) {
                vo.setCompanyName(orgCompany.getOrgName());
            }
        }

        // 部门
        if (vo.getCountry() != null) {
            SysOrganizations orgCountry = stationInfoMapper.selectsysOrgNameById(vo.getCountry());
            if (orgCountry != null) {
                String countryName = "";
                SysOrganizations superOrg = stationInfoMapper.selectsysOrgNameById(Long.valueOf(orgCountry.getParentCompanyNo()));
                if (superOrg != null) {
                    countryName = StrUtil.format("{}-{}", superOrg.getOrgName(), orgCountry.getOrgName());
                } else {
                    countryName = orgCountry.getOrgName();
                }
                vo.setCountryName(countryName);
            }
        }
    }

    public MPJLambdaWrapper<PowerStationInfo> getWrapper(PowerStationInfoQuery query) {
        MPJLambdaWrapper<PowerStationInfo> wrapper = new MPJLambdaWrapper<>();
        return wrapper;
    }
}
