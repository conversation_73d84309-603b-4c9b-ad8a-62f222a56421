package com.sccl.modules.business.towersharerelate.controller;

import java.util.List;

import com.sccl.modules.business.toweraccount.domain.TowerData;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.business.towersharerelate.domain.Towersharerelate;
import com.sccl.modules.business.towersharerelate.service.ITowersharerelateService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 电费实际共享关系 信息操作处理
 * 
 * <AUTHOR>
 * @date 2021-08-14
 */
@RestController
@RequestMapping("/business/towersharerelate")
public class TowersharerelateController extends BaseController
{
    private String prefix = "business/towersharerelate";
	
	@Autowired
	private ITowersharerelateService towersharerelateService;
	
	@RequiresPermissions("business:towersharerelate:view")
	@GetMapping()
	public String towersharerelate()
	{
	    return prefix + "/towersharerelate";
	}
	
	/**
	 * 查询电费实际共享关系列表
	 */
	@RequiresPermissions("business:towersharerelate:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(Towersharerelate towersharerelate)
	{
		//setFilterByRole(towersharerelate);
/*		if (towersharerelate.getCompany()==null)
			towersharerelate.setCompany(-1L);*/
		startPage();
        List<Towersharerelate> list = towersharerelateService.selectList(towersharerelate);
		return getDataTable(list);
	}
	
	/**
	 * 新增电费实际共享关系
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存电费实际共享关系
	 */
	@RequiresPermissions("business:towersharerelate:add")
	@Log(title = "电费实际共享关系", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody Towersharerelate towersharerelate)
	{		
		return toAjax(towersharerelateService.insert(towersharerelate));
	}

	/**
	 * 修改电费实际共享关系
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		Towersharerelate towersharerelate = towersharerelateService.get(id);

		Object object = JSONObject.toJSON(towersharerelate);

        return this.success(object);
	}
	
	/**
	 * 修改保存电费实际共享关系
	 */
	@RequiresPermissions("business:towersharerelate:edit")
	@Log(title = "电费实际共享关系", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody Towersharerelate towersharerelate)
	{		
		return toAjax(towersharerelateService.update(towersharerelate));
	}
	
	/**
	 * 删除电费实际共享关系
	 */
	@RequiresPermissions("business:towersharerelate:remove")
	@Log(title = "电费实际共享关系", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(towersharerelateService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看电费实际共享关系
     */
    @RequiresPermissions("business:towersharerelate:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		Towersharerelate towersharerelate = towersharerelateService.get(id);

        Object object = JSONObject.toJSON(towersharerelate);

        return this.success(object);
    }

	private void setFilterByRole(Towersharerelate towersharerelate) {
		//业务逻辑，不能放在service中，会影响前端分页
		//根据用户责任中心查询分公司和责任中心
		//默认查询第一个公司和用户的第一个责任中心

		if (towersharerelate.getCompany()==null && towersharerelate.getCountry()==null) {
			towersharerelate.setCompany(-1L);
			towersharerelate.setCountry(-1L);
		} else  if (null != towersharerelate.getCompany() && towersharerelate.getCountry()==-1L){
			towersharerelate.setCompany(towersharerelate.getCompany());
			towersharerelate.setCountry(null);
		}

	}

}
