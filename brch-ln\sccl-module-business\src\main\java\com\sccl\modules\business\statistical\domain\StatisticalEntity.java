package com.sccl.modules.business.statistical.domain;

import com.sccl.framework.web.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;


/**
 * 统计指标表 power_statistical
 *
 * <AUTHOR>
 * @date 2022-10-25
 */
@Getter
@Setter
public class StatisticalEntity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 统计组ID
     */
    private Long groupId;
    /**
     * 统计组别名
     */
    private String groupAlias;
    /**
     * 统计项标题
     */
    private String title;
    /**
     * 统计项内容
     */
    private String content;
    /**
     * 内容类型
     */
    private String contentType;
    /**
     * 统计时间
     */
    private Date statisticalTime;
    /**
     * 所属
     */
    private String ownerAs;
    /**
     * 删除标识
     */
    private Integer del_flag;
}
