package com.sccl.modules.business.stationaudit.pstationpowerchange;

import com.enrising.dcarbon.audit.*;
import com.sccl.common.utils.SpringUtil;
import com.sccl.modules.business.account.domain.Account;

import com.sccl.modules.mssaccount.mssaccountbill.mapper.MssAccountbillMapper;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

public class StationPowerChangeCreator extends AbstractRefereeDatasourceCreator {
    @Override
    public Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> collect(Auditable auditable) {
        //数据源
        Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> datasource = new HashMap<>();

        //从Spring上下文取得mapper
        MssAccountbillMapper mapper = SpringUtil.getBean(MssAccountbillMapper.class);
        //可能上下文没有这个mapper
        if (mapper == null) {
            return null;
        }
        Account account = (Account) auditable;
        //根据 台账主键 获取对应的台账 信息
        Long pcid = account.getPcid();
        List<StationPowerChangeRefereeContent> nodeResults = mapper.getPowerChagenNew(pcid);

        //去除null数据
        nodeResults = nodeResults.stream().filter(Objects::nonNull).collect(Collectors.toList());


        //获取 本次台账 对应站址 的 上一次台账信息
        nodeResults = nodeResults.stream().map(
                nodeResult -> {
                    String stationcode = nodeResult.getStationCode();
                    String startdate = nodeResult.getStartdate();
                    StationPowerChangeRefereeContent nodeResultLast = mapper.getPowerChagenNewLast(stationcode,
                                                                                                   startdate);
                    //set 比对属性
                    if (nodeResultLast != null) {
                        nodeResult.setAmmeterid_compare(nodeResultLast.getAmmeterid());
                        nodeResult.setAmmetername_compare(nodeResultLast.getAmmetername());
                        nodeResult.setPcid_compare(nodeResultLast.getPcid());
                        nodeResult.setPower_compare(nodeResultLast.getPower());
                        nodeResult.setStationcode_compare(nodeResultLast.getStationCode());
                        nodeResult.setProjectname_compare(nodeResultLast.getProjectname());
                        nodeResult.setStationname_compare(nodeResultLast.getStationname());
                        nodeResult.setStationaddress_compare(nodeResultLast.getStationaddress());
                        if (nodeResult.getPower() != null && nodeResultLast.getPower() != null && nodeResultLast.getPower().compareTo(BigDecimal.ZERO) != 0) {
                            nodeResult.setWidepower(
                                    nodeResult.getPower().subtract(nodeResult.getPower_compare()).
                                            divide(nodeResult.getPower_compare(), 2)
                                              .multiply(BigDecimal.valueOf(100))
                            );
                        }
                    }
                    return nodeResult;
                }

        ).filter(Objects::nonNull).collect(Collectors.toList());

        //转为auditResult 稽核对象
        List<AuditResult> auditResults = nodeResults.stream().map(
                n -> {
                    //创建 评审内容对象
                    RefereeResult refereeResult = new RefereeResult("电量波动", true, "成功");
                    n.setRefereeResult(refereeResult);
                    AuditResult auditResult = n.getAuditResult();
                    auditResult.setAuditKey(String.valueOf(n.getPcid()));
                    auditResult.setStep(3);
                    auditResult.setNodeTopic("电量波动");
                    auditResult.setRefereeMessage("电量波动结果已出");
                    return auditResult;
                }
        ).collect(Collectors.toList());
        //可以添加多种不同类型的评判数据
        //2添加到数据源
        datasource.put(StationPowerChangeRefereeContent.class, new ArrayList<>(auditResults));
        return datasource;
    }

    @Override
    public AbstractReferee getReferee() {
        return new StationPowerChangeReferee("电量波动");
    }

}
