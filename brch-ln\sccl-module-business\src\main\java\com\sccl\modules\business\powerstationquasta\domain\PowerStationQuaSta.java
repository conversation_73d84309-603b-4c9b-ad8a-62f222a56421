package com.sccl.modules.business.powerstationquasta.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;


/**
 * 站址能耗指标表 power_station_qua_sta
 * 
 * <AUTHOR>
 * @date 2021-09-14
 */
public class PowerStationQuaSta extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
    /** 地市 */
    private String citycode;
    /** 区县 */
    private String subcode;
    /**  */
    private String resstationcode;
    /** 名称 */
    private String resstationname;
    /** 站址 */
    private String stationcode;
    /** 日期 */
    private String currentdate;
    /** 电量 */
    private String stationquantity;
    /** 4G电量 */
    private String station4gquantity;
    /** 5G电量 */
    private String station5gquantity;
    /** 0,1 */
    private Integer status;
    /** 接口标识ID */
    private String msgId;
    /**  */
    private String country;
    /**  */
    private String company;
    /** 反馈人 */
    private String remarker;


	public void setCitycode(String citycode)
	{
		this.citycode = citycode;
	}

	public String getCitycode() 
	{
		return citycode;
	}

	public void setSubcode(String subcode)
	{
		this.subcode = subcode;
	}

	public String getSubcode() 
	{
		return subcode;
	}

	public void setResstationcode(String resstationcode)
	{
		this.resstationcode = resstationcode;
	}

	public String getResstationcode() 
	{
		return resstationcode;
	}

	public void setResstationname(String resstationname)
	{
		this.resstationname = resstationname;
	}

	public String getResstationname() 
	{
		return resstationname;
	}

	public void setStationcode(String stationcode)
	{
		this.stationcode = stationcode;
	}

	public String getStationcode() 
	{
		return stationcode;
	}

	public void setCurrentdate(String currentdate)
	{
		this.currentdate = currentdate;
	}

	public String getCurrentdate() 
	{
		return currentdate;
	}

	public void setStationquantity(String stationquantity)
	{
		this.stationquantity = stationquantity;
	}

	public String getStationquantity() 
	{
		return stationquantity;
	}

	public void setStation4gquantity(String station4gquantity)
	{
		this.station4gquantity = station4gquantity;
	}

	public String getStation4gquantity() 
	{
		return station4gquantity;
	}

	public void setStation5gquantity(String station5gquantity)
	{
		this.station5gquantity = station5gquantity;
	}

	public String getStation5gquantity() 
	{
		return station5gquantity;
	}


	public void setStatus(Integer status)
	{
		this.status = status;
	}

	public Integer getStatus() 
	{
		return status;
	}


	public void setMsgId(String msgId)
	{
		this.msgId = msgId;
	}

	public String getMsgId() 
	{
		return msgId;
	}

	public void setCountry(String country)
	{
		this.country = country;
	}

	public String getCountry() 
	{
		return country;
	}

	public void setCompany(String company)
	{
		this.company = company;
	}

	public String getCompany() 
	{
		return company;
	}


	public void setRemarker(String remarker)
	{
		this.remarker = remarker;
	}

	public String getRemarker() 
	{
		return remarker;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("citycode", getCitycode())
            .append("subcode", getSubcode())
            .append("resstationcode", getResstationcode())
            .append("resstationname", getResstationname())
            .append("stationcode", getStationcode())
            .append("currentdate", getCurrentdate())
            .append("stationquantity", getStationquantity())
            .append("station4gquantity", getStation4gquantity())
            .append("station5gquantity", getStation5gquantity())
            .append("delFlag", getDelFlag())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .append("msgId", getMsgId())
            .append("country", getCountry())
            .append("company", getCompany())
            .append("remark", getRemark())
            .append("remarker", getRemarker())
            .toString();
    }
}
