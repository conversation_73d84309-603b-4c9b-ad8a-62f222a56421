package com.sccl.modules.mssaccount.mssabccustomer.service;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.mssaccount.mssabccustomer.mapper.MssAbccustomerMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sccl.modules.mssaccount.mssabccustomer.domain.MssAbccustomer;


/**
 * 客户 服务层实现
 * 
 * <AUTHOR>
 * @date 2019-05-01
 */
@Service
public class MssAbccustomerServiceImpl extends BaseServiceImpl<MssAbccustomer> implements IMssAbccustomerService
{
    @Autowired
    MssAbccustomerMapper mssAbccustomerMapper;
    @Override
    public boolean isRelateabccustomer(String kunnr) {
        int rtl=mssAbccustomerMapper.isRelate(kunnr);
        if (rtl>0)
            return  true;
        else
            return false;
    }
}
