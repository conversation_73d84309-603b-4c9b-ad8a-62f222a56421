package com.sccl.modules.business.energyaccountpoolitempre.domain;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.math.BigDecimal;


/**
 * 油费汇总单明细表 energy_accountpoolitempre
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
@Getter
@Setter
public class EnergyAccountpoolitempre extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	/** 明细表主键id */
	private Long pabriid;
    /** 汇总单主表id */
    private Long pabrid;
    /** 台账id */
    private Long pcid;
    /** 台账金额 */
    private BigDecimal accountmoney;
    /** 状态 */
    private String status;
    /** 对应财务abc */
    private BigDecimal curusedreadings;
    /** 选择的类型 */
    private Integer placeid;

    private String  del_flag;


	public void setParid(Long pabrid)
	{
		this.pabrid = pabrid;
	}

	public Long getParid()
	{
		return pabrid;
	}

	public void setPcid(Long pcid)
	{
		this.pcid = pcid;
	}

	public Long getPcid()
	{
		return pcid;
	}

	public void setAccountmoney(BigDecimal accountmoney)
	{
		this.accountmoney = accountmoney;
	}

	public BigDecimal getAccountmoney()
	{
		return accountmoney;
	}

	public void setStatus(String status)
	{
		this.status = status;
	}

	public String getStatus()
	{
		return status;
	}

	public void setCurusedreadings(BigDecimal curusedreadings)
	{
		this.curusedreadings = curusedreadings;
	}

	public BigDecimal getCurusedreadings()
	{
		return curusedreadings;
	}

	public void setPlaceid(Integer placeid)
	{
		this.placeid = placeid;
	}

	public Integer getPlaceid()
	{
		return placeid;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("pabriid", getPabriid())
            .append("parid", getParid())
            .append("pcid", getPcid())
            .append("accountmoney", getAccountmoney())
            .append("status", getStatus())
            .append("curusedreadings", getCurusedreadings())
            .append("placeid", getPlaceid())
            .toString();
    }

	public Long getPabriid() {
		return pabriid;
	}

	public void setPabriid(Long pabriid) {
		this.pabriid = pabriid;
	}
}
