package com.sccl.modules.business.tabcreaterecord.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;


/**
 * 操作记录表 tab_create_record
 * 
 * <AUTHOR>
 * @date 2022-12-08
 */
public class TabCreateRecord extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
    /** 表名 */
    private String tableName;
    /** 操作类型 1新增/2删除/3修改/4查询 */
    private Integer optype;
    /** 创建时间 */
    private Date createtime;
    /** 更新时间 */
    private Date updatetime;


	public void setTableName(String tableName)
	{
		this.tableName = tableName;
	}

	public String getTableName() 
	{
		return tableName;
	}

	public void setOptype(Integer optype)
	{
		this.optype = optype;
	}

	public Integer getOptype() 
	{
		return optype;
	}

	public void setCreatetime(Date createtime)
	{
		this.createtime = createtime;
	}

	public Date getCreatetime() 
	{
		return createtime;
	}

	public void setUpdatetime(Date updatetime)
	{
		this.updatetime = updatetime;
	}

	public Date getUpdatetime() 
	{
		return updatetime;
	}


	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("tableName", getTableName())
            .append("optype", getOptype())
            .append("createtime", getCreatetime())
            .append("updatetime", getUpdatetime())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
