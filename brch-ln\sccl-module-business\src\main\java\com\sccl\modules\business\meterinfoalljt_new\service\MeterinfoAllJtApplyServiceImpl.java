package com.sccl.modules.business.meterinfoalljt_new.service;

import com.sccl.common.lang.StringUtils;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.utils.enumClass.CommonConstants;
import com.sccl.modules.business.meterinfoalljt_new.domain.MeterinfoAllJtApply;
import com.sccl.modules.business.meterinfoalljt_new.mapper.MeterinfoAllJtApplyMapper;
import com.sccl.modules.business.meterinfoalljt_new.vo.MeterinfoAllJtApplyDelVo;
import com.sccl.modules.business.meterinfoalljt_new.vo.MeterinfoAllJtApplyResultVo;
import com.sccl.modules.business.meterinfoalljt_new.vo.MeterinfoAllJtApplySaveVo;
import com.sccl.modules.business.meterinfoalljt_new.vo.MeterinfoAllJtApplySearchVo;
import com.sccl.modules.system.user.domain.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;


/**
 * 在网表计数据清单-新增申请 服务层实现
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@Service
@Slf4j
public class MeterinfoAllJtApplyServiceImpl extends BaseServiceImpl<MeterinfoAllJtApply> implements IMeterinfoAllJtApplyService {

    @Autowired
    private MeterinfoAllJtApplyMapper meterinfoAllJtApplyMapper;
    /**
     * 一览查询
     * @param searchVo
     * @return
     */
    @Override
    public List<MeterinfoAllJtApplyResultVo> list(MeterinfoAllJtApplySearchVo searchVo) {
        List<MeterinfoAllJtApplyResultVo> list = meterinfoAllJtApplyMapper.list(searchVo);
        if (list != null && list.size() > 0 && (!"1".equals(searchVo.getAdmin()))) {
            list.stream().forEach(item -> {
                item.set_disabled(true);
                if (String.valueOf(CommonConstants.METER_APPLY_STATUS_CG).equals(item.getStatus())) {
                    item.set_disabled(false);
                }
            });
        }
        return list;
    }

    /**
     * 待添加电表一览查询
     * @param searchVo
     * @return
     */
    @Override
    public List<MeterinfoAllJtApplyResultVo> ammeterList(MeterinfoAllJtApplySearchVo searchVo) {
        return meterinfoAllJtApplyMapper.ammeterList(searchVo);
    }

    /**
     * 添加保存
     * @param entity
     * @return
     */
    @Override
    public String save(MeterinfoAllJtApplySaveVo entity) {
        //执行添加
        meterinfoAllJtApplyMapper.save(entity);
        return "";
    }

    /**
     * 提交
     * @param entity
     * @return
     */
    @Override
    public String submit(MeterinfoAllJtApplyDelVo entity) {
        User user = ShiroUtils.getUser();
        if (user == null) {
            return "登录用户不存在，提交失败";
        }

        String[] ids = StringUtils.split(entity.getIds(), ",");
        int cnt = meterinfoAllJtApplyMapper.checkByIds(ids);
        if (cnt > 0) {
            return "存在已提交或已处理的数据，无法进行提交操作！";
        }
        MeterinfoAllJtApply meterinfoApply = new MeterinfoAllJtApply();
        meterinfoApply.setIds(ids);
        meterinfoApply.setStatus(CommonConstants.METER_APPLY_STATUS_YTJ);//单据状态 已提交
        meterinfoApply.setSubmitBy(user.getLoginId());                   //提交人
        meterinfoApply.setSubmitTime(new Date());                        //提交时间
        meterinfoApply.initUpdate();
        meterinfoAllJtApplyMapper.updateForModelBatch(meterinfoApply);
        return "";
    }

    /**
     * 删除
     * @param entity
     * @return
     */
    @Override
    public String del(MeterinfoAllJtApplyDelVo entity) {
        String[] ids = StringUtils.split(entity.getIds(), ",");
        int cnt = meterinfoAllJtApplyMapper.checkByIds(ids);
        if (cnt > 0) {
            return "存在已提交或已处理的数据，无法进行删除操作！";
        }
        meterinfoAllJtApplyMapper.deleteByIds(ids);
        return "";
    }

    /**
     * 根据网表计清单，更新数据状态为已处理
     * @return
     */
    @Override
    public String completed() {
        try {
            log.info("根据网表计清单，更新数据状态为已处理开始");
            int cnt = meterinfoAllJtApplyMapper.completed();
            log.info("根据网表计清单，更新数据状态为已处理结束，成功处理" + cnt + "数据");
        } catch (Exception e) {
            log.error("根据网表计清单，更新数据状态为已处理异常" + e.getMessage());
            e.printStackTrace();
        }
        return "";
    }
}
