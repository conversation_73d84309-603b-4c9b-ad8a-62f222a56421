package com.sccl.modules.business.stationinfo.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * 局站表 power_station_info
 *
 * <AUTHOR>
 * @date 2019-05-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StationInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 集团站址编码（有效性站址编码不变的key)
     */
    private String stationcodeintid;

    /**
     * 5GR站址名称
     */
    private String stationname5gr;

    /**
     * 流程id
     */
    private Long processinstId;
    /**
     * 流程状态( -1：无效  0：草稿 1：流程中 2：申请流程归档完成)
     */
    private Long wfStatus;
    /**
     * 局站类型，在power_category_type表中type_category="BUR_STAND_TYPE"
     */
    private Long stationtype;
    /**
     * 局站编码
     */
    private String stationcode;
    /**
     * 局站名称
     */
    private String stationname;
    /**
     * 是否附属，在power_category_type表中type_category="isSubsidiary"
     */
    private String issub;
    /**
     * 对应母站房
     */
    private String motherstation;
    /**
     * 用途，在power_category_type表中type_category="useType"
     */
    private Long useway;
    /**
     * 产权，在power_category_type表中type_category="propertyRight"
     */
    private Long propertyright;
    /**
     * 责任人（）
     */
    private Long responsibleman;
    /**
     * 责任中心
     */
    private Long country;
    /**
     * 分公司
     */
    private Long company;
    /**
     * 使用权，在power_category_type表中type_category="useRight"
     */
    private Long useright;
    /**
     * 建筑结构，在power_category_type表中type_category="buildingStructure"
     */
    private Long buildingstructure;
    /**
     * 员工人数
     */
    private Long staffnumber;
    /**
     * 状态(0闲置、1停用、2在用)
     */
    private String status;
    /**
     * 面积
     */
    private BigDecimal area;
    /**
     * 地址
     */
    private String address;
    /**
     * 使用单位
     */
    private String usedepartment;
    /**
     * 管理部门
     */
    private String managedepartment;
    /**
     * 房产证号
     */
    private String certificateno;
    /**
     * 房屋价值
     */
    private BigDecimal houseprice;
    /**
     * 是否共享
     */
    private String isshare;
    /**
     * 共享单位名称
     */
    private String sheredepartname;
    /**
     * 电流
     */
    private String electricity;
    /**
     * 环境
     */
    private String environment;
    /**
     * 对应资源系统局站名称
     */
    private String resstationname;
    /**
     * 对应资源系统局站编码
     */
    private String resstationcode;
    /**
     * 局站等级 ，在power_category_type表中type_category="stationLevel"
     */
    private Long stationlevel;
    /**
     * 是否有空调
     */
    private String isaircondition;
    /**
     * 是否达到大工业用电申请标准
     */
    private String issatisfybigfactories;
    /**
     * 是否申请大工业用电
     */
    private String isaskbigfactories;
    /**
     * 是否是大工业用电
     */
    private String isbigfactories;
    /**
     * 是否电力直接交易
     */
    private String istradeelectric;
    /**
     * 大工业高峰电价
     */
    private BigDecimal bigfactorypeakprice;
    /**
     * 大工业平段电价
     */
    private BigDecimal bigfactoryplainprice;
    /**
     * 大工业低谷电价
     */
    private BigDecimal bigfactoryvalleyprice;
    /**
     * 是否一般工商用电
     */
    private String isnormalbussuse;
    /**
     * 一般工商及其他电价
     */
    private BigDecimal normalbusprice;
    /**
     * 直售电电价
     */
    private BigDecimal directsaleprice;
    /**
     * 变压器编号
     */
    private String transformerno;
    /**
     * 变压器容量
     */
    private BigDecimal transformercapacity;
    /**
     * 功率因素
     */
    private String powerfactor;
    /**
     * 创建人
     */
    private Long createuser;
    /**
     * 创建时间
     */
    private Date createtime;
    /**
     * 成本中心
     */
    private String costcenter;
    /**
     * 修改时间
     */
    private Date modifytime;
    private Boolean _disabled;
    private String companyname;
    private String countryname;
    private String responsiblemanname;
    private String stationtypename;
    private String statusname;
    private String propertyrightname;
    private String cityName;//资源局站名称
    private String cityCode;//资源局站编码
    private String motherstationname;
    private String costcentername;
    private String busiAlias;
    private String ownername;
    private boolean isSubAdmin;
    private boolean isAdmin;
    private boolean isCityAdmin;
    private boolean isProAdmin;
    private List<String> listIds;
    private String stationAddrCode;//站址编码
    private String stationAddrName;//站址名称
    private BigDecimal elecconsum;//基站耗电
    private String devicetype;//基站设备类型
    private String grCode;//5gr编码code
    private String deployTo;

    /**
     * 资源局站id
     */
    private String roomId;
    private String termname;
    private String nmcode;//enbid
    private Map<String, String> map;
    private String servestartdate;//起始时间
    private String serveenddate;//起始时间


}
