package com.sccl.modules.dataperfect.domain;

import com.sccl.modules.dataperfect.CollecterPerfectService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


public class AccountTime {


    private Long rid;
    private String stationaddresscode;
    private String startdate;
    private String enddate;

    public String getStationaddresscode() {
        return stationaddresscode;
    }

    public AccountTime(Long rid,String stationaddresscode, String startdate, String enddate) {
        this.rid=rid;
        this.stationaddresscode = stationaddresscode;
        this.startdate = startdate;
        this.enddate = enddate;
    }

    public AccountTime() {
    }

    public void setStationaddresscode(String stationaddresscode) {
        this.stationaddresscode = stationaddresscode;
    }

    public String getStartdate() {
        return startdate;
    }

    public void setStartdate(String startdate) {
        this.startdate = startdate;
    }

    public String getEnddate() {
        return enddate;
    }

    public void setEnddate(String enddate) {
        this.enddate = enddate;
    }
    public Long getRid() {
        return rid;
    }

    public void setRid(Long rid) {
        this.rid = rid;
    }

    public static void main(String[] args) {

    }
}
