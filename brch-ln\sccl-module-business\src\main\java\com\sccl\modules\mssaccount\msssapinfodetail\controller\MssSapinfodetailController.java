package com.sccl.modules.mssaccount.msssapinfodetail.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.mssaccount.msssapinfodetail.domain.MssSapinfodetail;
import com.sccl.modules.mssaccount.msssapinfodetail.service.IMssSapinfodetailService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 报账回传明细 信息操作处理
 * 
 * <AUTHOR>
 * @date 2019-05-01
 */
@RestController
@RequestMapping("/mssaccount/mssSapinfodetail")
public class MssSapinfodetailController extends BaseController
{
    private String prefix = "mssaccount/mssSapinfodetail";
	
	@Autowired
	private IMssSapinfodetailService mssSapinfodetailService;
	
	@RequiresPermissions("mssaccount:mssSapinfodetail:view")
	@GetMapping()
	public String mssSapinfodetail()
	{
	    return prefix + "/mssSapinfodetail";
	}
	
	/**
	 * 查询报账回传明细列表
	 */
	@RequiresPermissions("mssaccount:mssSapinfodetail:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(MssSapinfodetail mssSapinfodetail)
	{
		startPage();
        List<MssSapinfodetail> list = mssSapinfodetailService.selectList(mssSapinfodetail);
		return getDataTable(list);
	}
	
	/**
	 * 新增报账回传明细
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存报账回传明细
	 */
	@RequiresPermissions("mssaccount:mssSapinfodetail:add")
	//@Log(title = "报账回传明细", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody MssSapinfodetail mssSapinfodetail)
	{		
		return toAjax(mssSapinfodetailService.insert(mssSapinfodetail));
	}

	/**
	 * 修改报账回传明细
	 */
	@GetMapping("/edit/{msdid}")
	public AjaxResult edit(@PathVariable("msdid") Long msdid)
	{
		MssSapinfodetail mssSapinfodetail = mssSapinfodetailService.get(msdid);

		Object object = JSONObject.toJSON(mssSapinfodetail);

        return this.success(object);
	}
	
	/**
	 * 修改保存报账回传明细
	 */
	@RequiresPermissions("mssaccount:mssSapinfodetail:edit")
	//@Log(title = "报账回传明细", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody MssSapinfodetail mssSapinfodetail)
	{		
		return toAjax(mssSapinfodetailService.update(mssSapinfodetail));
	}
	
	/**
	 * 删除报账回传明细
	 */
	@RequiresPermissions("mssaccount:mssSapinfodetail:remove")
	//@Log(title = "报账回传明细", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(mssSapinfodetailService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看报账回传明细
     */
    @RequiresPermissions("mssaccount:mssSapinfodetail:view")
    @GetMapping("/view/{msdid}")
    @ResponseBody
    public AjaxResult view(@PathVariable("msdid") Long msdid)
    {
		MssSapinfodetail mssSapinfodetail = mssSapinfodetailService.get(msdid);

        Object object = JSONObject.toJSON(mssSapinfodetail);

        return this.success(object);
    }

}
