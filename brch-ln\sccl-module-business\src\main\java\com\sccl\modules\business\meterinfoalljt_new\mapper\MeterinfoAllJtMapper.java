package com.sccl.modules.business.meterinfoalljt_new.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sccl.modules.business.meterinfoalljt_new.domain.MeterinfoAllJt;
import com.sccl.modules.business.meterinfoalljt_new.dto.MeterinfoAllJtQueryDTO;
import com.sccl.modules.business.meterinfoalljt_new.vo.MeterinfoAllJtVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 表计清单查询 数据层
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface MeterinfoAllJtMapper extends BaseMapper<MeterinfoAllJt> {

    /**
     * 查询表计清单列表
     *
     * @param queryDTO 查询条件
     * @return 表计清单列表
     */
    List<MeterinfoAllJtVO> selectMeterinfoAllJtList(@Param("query") MeterinfoAllJtQueryDTO queryDTO);

    /**
     * 根据ID查询表计清单详情
     *
     * @param id 主键ID
     * @return 表计清单详情
     */
    MeterinfoAllJtVO selectMeterinfoAllJtById(@Param("id") Long id);
}
