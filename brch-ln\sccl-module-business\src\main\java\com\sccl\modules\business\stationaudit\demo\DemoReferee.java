package com.sccl.modules.business.stationaudit.demo;

import com.enrising.dcarbon.audit.*;
import com.sccl.modules.autojob.util.id.IdGenerator;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * 演示评判器
 *
 * <AUTHOR>
 * @Date 2022/11/11 11:23
 * @Email <EMAIL>
 */
@Slf4j
public class DemoReferee extends AbstractReferee {
    public DemoReferee() {
        super("testReferee");
    }

    @Override
    public RefereeResult doReferee(RefereeResult lastRefereeResult, Auditable auditable, Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> refereeDatasourceList) {
        //获取已完成的节点的结果
        List<RefereeResult> refereeResults = RefereeChain.currentRefereeChain
                .get()
                .collectResults();

        //获取链中的某个节点
        RefereeNode node = RefereeChain.currentRefereeChain
                .get()
                .findNode("node");
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            log.warn("稽核中断");
            //e.printStackTrace();
        }
        RefereeResult refereeResult = new RefereeResult();
        refereeResult.setRefereeMessage(IdGenerator.getNextIdAsString());
        return refereeResult;
    }
}
