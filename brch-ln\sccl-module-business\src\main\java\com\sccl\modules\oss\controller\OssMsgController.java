package com.sccl.modules.oss.controller;

import com.enrising.dcarbon.message.MessageManager;
import com.enrising.dcarbon.redis.RedisUtil;
import com.enrising.dcarbon.regex.RegexUtil;
import com.enrising.dcarbon.string.StringUtils;
import com.sccl.common.spring.SpringUtils;
import com.sccl.modules.oss.collect.OssMsgCollectorTemplate;
import com.sccl.modules.oss.collect.OssMsgCollectorTrigger;
import com.sccl.modules.oss.config.OssConstant;
import com.sccl.modules.oss.service.impl.OssMsgServiceImp;
import com.sccl.timing.finder.util.servlet.InetUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * oss存储表 前端控制器
 * </p>
 *
 * <AUTHOR> <PERSON>
 * @since 2023-06-29
 */
@RestController
@RequestMapping("/oss-msg-entity")
@Slf4j
public class OssMsgController {
    @Autowired
    private OssMsgServiceImp service;


    @PostMapping(value = "/del_oss_by_ids", produces = "application/json;charset=UTF-8")
    public String deleteByIds(@RequestBody(required = false) List<Long> ids, @RequestParam(required = false, value = "PROGRESS_ID") String progressID) {
        if (ids == null || ids.isEmpty()) {
            return MessageManager.getJsonMessage(MessageManager.Code.BAD_REQUEST, "参数为空");
        }
        if (!StringUtils.isEmpty(progressID)) {
            service.getCurrentRequestContext().setValue(progressID);
        }
        service.deleteByIds(ids.stream().map(item -> "" + item).collect(Collectors.toList()));
        return null;
    }

    @GetMapping(value = "/get_del_progress", produces = "application/json;charset=UTF-8")
    public String getProgress(@RequestParam(required = false, value = "PROGRESS_ID") String progressID) {
        if (StringUtils.isEmpty(progressID)) {
            return MessageManager.getJsonMessage(MessageManager.Code.BAD_REQUEST, "参数为空");
        }
        return RedisUtil.getStr(OssConstant.OSS_DEL_KEY_PREFIX + progressID);
    }

    @GetMapping(value = "/del_id_between", produces = "application/json;charset=UTF-8")
    public String delByIDBetween(@RequestParam(value = "START", required = false) String start, @RequestParam(value = "END", required = false) String end, @RequestParam(value = "execFlag", required = false) boolean execFlag, @RequestParam(required = false, value = "PROGRESS_ID") String progressID) {
        if (StringUtils.isEmpty(start) || StringUtils.isEmpty(end)) {
            return MessageManager.getJsonMessage(MessageManager.Code.BAD_REQUEST, "空参数");
        }
        if (!RegexUtil.isMatch(start, RegexUtil.Type.DATE_TIME_YYYY_MM_DD_HH_MM_SS) || !RegexUtil.isMatch(end, RegexUtil.Type.DATE_TIME_YYYY_MM_DD_HH_MM_SS)) {
            return MessageManager.getJsonMessage(MessageManager.Code.BAD_REQUEST, "时间不符合格式");
        }
        if (!StringUtils.isEmpty(progressID)) {
            service.getCurrentRequestContext().setValue(progressID);
        }
        if (service.delByStartAndEnd(start, end, execFlag)) {
            return null;
        }
        return MessageManager.getJsonMessage(MessageManager.Code.ERROR, "系统异常");
    }


    @GetMapping(value = "/delrediskey", produces = "application/json;charset=UTF-8")
    public String delrediskey(@RequestParam(required = false, value = "rediskey") String rediskey) {
        if (StringUtils.isEmpty(rediskey)) {
            return MessageManager.getJsonMessage(MessageManager.Code.BAD_REQUEST, "参数为空");
        }
        StringRedisTemplate stringRedisTemplate = getStringRedisTemplate();
        Boolean flag = stringRedisTemplate.delete(rediskey);
        return flag ? String.format("成功删除了 redis->key:%s", rediskey) : "删除失败";
    }

    @GetMapping(value = "/getObjectForRedisKey", produces = "application/json;charset=UTF-8")
    public Object getObjectForRedisKey(@RequestParam(required = false, value = "rediskey") String rediskey) {
        if (StringUtils.isEmpty(rediskey)) {
            return MessageManager.getJsonMessage(MessageManager.Code.BAD_REQUEST, "参数为空");
        }
        StringRedisTemplate stringRedisTemplate = getStringRedisTemplate();
        String result = stringRedisTemplate.boundValueOps(rediskey).get();
        return result;
    }

    @GetMapping(value = "/getTTL", produces = "application/json;charset=UTF-8")
    public Long getTTL(@RequestParam(required = false, value = "rediskey") String rediskey) {
        if (StringUtils.isEmpty(rediskey)) {
            return -2L;
        }
        StringRedisTemplate stringRedisTemplate = getStringRedisTemplate();
        return stringRedisTemplate.getExpire(rediskey, TimeUnit.SECONDS);
    }

    @GetMapping(value = "/setRedisValue", produces = "application/json;charset=UTF-8")
    public String setRedisValue(
            @RequestParam(required = false, value = "rediskey") String rediskey,
            @RequestParam(required = false, value = "redisvalue") String redisvalue) {
        if (StringUtils.isEmpty(rediskey) || StringUtils.isEmpty(redisvalue)) {
            return "请指定rediskey or redisvalue";
        }
        StringRedisTemplate stringRedisTemplate = getStringRedisTemplate();
        stringRedisTemplate
                .opsForValue()
                .set(rediskey, redisvalue);
        return "设置结束";
    }

    @GetMapping(value = "/ossinsert", produces = "application/json;charset=UTF-8")
    public void ossinsert() {
        log.info("你好oss sync");
        if (!com.sccl.modules.business.cache.utils.RedisUtil.lock(OssConstant.OSS_RUNNING_LOCK, System.currentTimeMillis(), 60 * 1000, 1000 * 60 * 10, 1000 * 10)) {
            log.info("机器{}获取OSS同步锁失败，将不会同步", InetUtil.getLocalhostIp());
            return;
        }
        OssMsgCollectorTemplate template = new OssMsgCollectorTemplate();
        OssMsgCollectorTrigger trigger = new OssMsgCollectorTrigger(template, 10, TimeUnit.SECONDS);
        trigger.register();
    }

    @GetMapping(value = "/ossinsertexcluderedis", produces = "application/json;charset=UTF-8")
    public void ossinsertexcluderedis() {
        log.info("你好oss sync");
        log.info("不再获取redis 锁");
        OssMsgCollectorTemplate template = new OssMsgCollectorTemplate();
        OssMsgCollectorTrigger trigger = new OssMsgCollectorTrigger(template, 10, TimeUnit.SECONDS);
        trigger.register();
    }

    @GetMapping(value = "/ossinsertexcluderedis2", produces = "application/json;charset=UTF-8")
    public void ossinsertexcluderedis2(@RequestParam(value = "intervalTime", required = false) Long intervalTime) {
        log.info("你好oss sync");
        log.info("不再获取redis 锁");

        OssMsgCollectorTemplate template = new OssMsgCollectorTemplate();
        OssMsgCollectorTrigger trigger = new OssMsgCollectorTrigger(template, intervalTime, TimeUnit.MILLISECONDS);
        trigger.register();
    }

    public static StringRedisTemplate getStringRedisTemplate() {
        return (StringRedisTemplate) SpringUtils.getBean("stringRedisTemplate");
    }


}
