package com.sccl.modules.business.jhanomalydetails.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.sccl.framework.aspectj.lang.annotation.Excel;
import lombok.Data;


/**
 *
 * */

@Data
public class JhReasonableErrorDTO {


    /** 报账单期号 */
    @Excel(name = "报账单期号_bz")
    private String bzdqh;
    /** 报账单id */
    @Excel(name = "报账单id_bz")
    private String bzdid;

    /**
     * 电表户名/协议编码
     */
    @Excel(name = "电表户名/协议编码_tz_bz")
    private String dbhm;


    /**
     * 台账期号
     */
    @TableField("tzqh")
    @Excel(name = "报账单id_tz_bz")
    private String tzqh;

    /**
     * 本次单价（含税，元）
     */
    @TableField("dj")
    @Excel(name = "本次单价（含税，元）_tz_bz")
    private String dj;

    /**
     * 协议单价/电表单价
     */
    @TableField("xydj")
    @Excel(name = "协议单价/电表单价_tz_bz")
    private String xydj;



}
