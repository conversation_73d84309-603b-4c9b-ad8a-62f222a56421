package com.sccl.modules.business.lnidc.service.lmpl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.utils.ExcelUtil;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.modules.business.lnidc.domain.IdcMonitorMonthly;
import com.sccl.modules.business.lnidc.domain.IdcMonitorStatistics;
import com.sccl.modules.business.lnidc.domain.IdcMonitorStatisticsBo;
import com.sccl.modules.business.lnidc.domain.IdcMonitorStatisticsVo;
import com.sccl.modules.business.lnidc.mapper.IdcMonitorMonthlyMapper;
import com.sccl.modules.business.lnidc.mapper.IdcMonitorStatisticsMapper;
import com.sccl.modules.business.lnidc.service.IdcMontitorStatisticsService;
import com.sccl.modules.system.role.domain.Role;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.service.UserServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class IdcMontitorStatisticsServicelmpl extends BaseServiceImpl<IdcMonitorStatistics> implements IdcMontitorStatisticsService {

    private final IdcMonitorStatisticsMapper idcMonitorStatisticsMapper;

    private final IdcMonitorMonthlyMapper idcMonitorMonthlyMapper;



    @Override
    public List<IdcMonitorStatisticsVo> selfIdcEnergyList(IdcMonitorStatisticsBo bo) {

        bo.setYear(StrUtil.isBlank(bo.getYear()) ? String.valueOf(DateUtil.year(new Date())) : bo.getYear());
        return idcMonitorStatisticsMapper.selfIdcEnergyList(bo);
    }




    @Override
    public void editIdcEnergy(IdcMonitorStatisticsBo bo) {
        String year = bo.getYear();
        List<Long> statisticsIds = bo.getItems().stream().map(IdcMonitorStatisticsBo.idcEnergy::getId).collect(Collectors.toList());
        List<IdcMonitorMonthly> monthlyList = idcMonitorMonthlyMapper.getListByIds(year, statisticsIds);

        if (CollectionUtil.isEmpty(monthlyList)) {
            List<IdcMonitorMonthly> addList = new ArrayList<>();
            bo.getItems().forEach(node -> {
                IdcMonitorMonthly idcMonitorMonthly = new IdcMonitorMonthly();
                idcMonitorMonthly.setYear(year);
                idcMonitorMonthly.setStatisticsId(node.getId());
                setValueByMonth(idcMonitorMonthly, node.getValue());
                addList.add(idcMonitorMonthly);
            });
            idcMonitorMonthlyMapper.insertList(addList);
        } else {
            List<IdcMonitorMonthly> addList = new ArrayList<>();
            List<IdcMonitorMonthly> updateList = new ArrayList<>();
            bo.getItems().forEach(node -> {
                List<IdcMonitorMonthly> collect = monthlyList.stream().filter(item -> node.getId().equals(item.getStatisticsId()))
                        .collect(Collectors.toList());
                if (CollectionUtil.isEmpty(collect)) {
                    IdcMonitorMonthly idcMonitorMonthly = new IdcMonitorMonthly();
                    idcMonitorMonthly.setYear(year);
                    idcMonitorMonthly.setStatisticsId(node.getId());
                    setValueByMonth(idcMonitorMonthly, node.getValue());
                    addList.add(idcMonitorMonthly);
                } else {
                    IdcMonitorMonthly idcMonitorMonthly = collect.get(0);
                    setValueByMonth(idcMonitorMonthly, node.getValue());
                    updateList.add(idcMonitorMonthly);
                }
            });
            if (CollectionUtil.isNotEmpty(addList)) {
                idcMonitorMonthlyMapper.insertList(addList);
            }
            if (CollectionUtil.isNotEmpty(updateList)) {
                idcMonitorMonthlyMapper.batchUpdateMonthlyData(updateList);

            }
        }
    }

    @Override
    public void exportIdcEnergy(HttpServletResponse response, IdcMonitorStatisticsBo bo) {
        String year = bo.getYear();
        List<IdcMonitorStatisticsVo> idcMonitorStatisticsVos = selfIdcEnergyList(bo);
        ExcelUtil<IdcMonitorStatisticsVo> excelUtil = new ExcelUtil<IdcMonitorStatisticsVo>(IdcMonitorStatisticsVo.class);
        excelUtil.exportExcelToBrowser(response, idcMonitorStatisticsVos, year + "年IDC服务器能耗数字化监控月度统计表");
    }

    private static void setValueByMonth(IdcMonitorMonthly idcMonitorMonthly, String value) {
        int month = DateUtil.month(new Date()) + 1;
        switch (month) {
            case 1:
                idcMonitorMonthly.setJanuary(value);
                break;
            case 2:
                idcMonitorMonthly.setFebruary(value);
                break;
            case 3:
                idcMonitorMonthly.setMarch(value);
                break;
            case 4:
                idcMonitorMonthly.setApril(value);
                break;
            case 5:
                idcMonitorMonthly.setMay(value);
                break;
            case 6:
                idcMonitorMonthly.setJune(value);
                break;
            case 7:
                idcMonitorMonthly.setJuly(value);
                break;
            case 8:
                idcMonitorMonthly.setAugust(value);
                break;
            case 9:
                idcMonitorMonthly.setSeptember(value);
                break;
            case 10:
                idcMonitorMonthly.setOctober(value);
                break;
            case 11:
                idcMonitorMonthly.setNovember(value);
                break;
            case 12:
                idcMonitorMonthly.setDecember(value);
                break;
            default:
                break;
        }


    }
}
