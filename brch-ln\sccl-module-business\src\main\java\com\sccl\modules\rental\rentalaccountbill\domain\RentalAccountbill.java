package com.sccl.modules.rental.rentalaccountbill.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;
import java.math.BigDecimal;


/**
 * 车辆（报账）表 rental_accountbill
 * 
 * <AUTHOR>
 * @date 2019-08-23
 */
public class RentalAccountbill extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
    /** 支付方式:：1-分公司委托划扣、2-分公司主动支付、3-省公司委托划扣、4-省公司主动支付、5--汇票支付、6-分公司收款、7-省公司收款、8-不涉及银行收付 */
    private BigDecimal paymentType;
    /** 状态1,'草稿',2,'待办',3,'生成报帐单',4,'生成凭证',7,'完成',5,'财务通过',-1,'报帐单删除',-2,'退单',-4,'等待生成,-3,'送财辅失败',8 '财辅退单' */
    private BigDecimal status;
    /** 客户名称 */
    private String clientName;
    /** 客户编码 */
    private String clientCode;
    /** 供应商名称 */
    private String supplierName;
    /** 供应商编码 */
    private String supplierCode;
    /** 供应商开会银行 */
    private String supplierBank;
    /** 商开会银行帐号 */
    private String supplierAccount;
    /** 预算组项目id */
    private String budgetItemGroupId;
    /** 打包报帐档案id */
    private String packupArchiveId;
    /** 业务类型 */
    private BigDecimal bizType;
    /** 是否还款 */
    private BigDecimal isPay;
    /** 是否关联交易 */
    private String conTrans;
    /** 是否打包 */
    private BigDecimal isToPack;
    /** 预算事项code */
    private String budgetCode;
    /** 预算事项名称 */
    private String budgetName;
    /** 预算项目 code */
    private String budgetItemCode;
    /** 预算项目 名称 */
    private String budgetItemName;
    /** 所属市局id */
    private String cityId;
    /** 所属市局名称 */
    private String cityName;
    /** 所属利润中心id */
    private String profitCenterCode;
    /** 所属利润中心名称 */
    private String profitCenterName;
    /** 所属责任中心id */
    private String costCenterId;
    /** 所属责任中心名称 */
    private String costCenterName;
    /** 创建日期 */
    private Date createDate;
    /** 公司代码 (公司主体、账套) */
    private String companyCode;
    /** 是否归口 */
    private BigDecimal isCategory;
    /** 贷方会计科目 */
    private String creditAccountCode;
    /** 贷方会计科目名称 */
    private String creditAccountName;
    /** 是否预付款 */
    private String isPrepay;
    /** 纳税调整类别 */
    private String taxAdjustType;
    /** 纳税调整金额 */
    private BigDecimal taxAdjustSum;
    /** 纳税调整说明 */
    private String taxAdjustComments;
    /** 查看考核表url */
    private String assessDataUrl;
    /** 省级编码 */
    private String provinceCode;
    /** 挑对模式 */
    private String pickingMode;
    /** 项目预算编码（以前的活动预算） */
    private String budgetGroupItemCode;
    /** 预算活动组名称 */
    private String budgetGroupItemName;
    /** 预算责任中心编码 */
    private String responseCenterCode;
    /** 预算责任中心名称 */
    private String responseCenterName;
    /** 显示查看业务数据（为空时不显示） */
    private String attchmentgAdd;
    /** 是否需纳税调整 */
    private String isTaxAdjust;
    /** 摘要 */
    private String abstractVal;
    /** 填报人所属利润中心id */
    private String fillInProfitCenterId;
    /** 填报人所属责任中心id */
    private String fillInCostCenterId;
    /** 填报人所属利润中心名称 */
    private String fillInProfitCenterName;
    /** 填报人所属责任中心名称 */
    private String fillInCostCenterName;
    /** 是否补录报帐 */
    private BigDecimal replenishFillIn;
    /** 填报人角色id */
    private String fillInRoleId;
    /** 被后续操作的金额 */
    private BigDecimal beAfterOperateSum;
    /** 费用形式 */
    private String feeType;
    /** 费用发生日 */
    private String happenDate;
    /** SAP记帐日期 */
    private Date sapRemarkDate;
    /** 清帐金额 */
    private BigDecimal clearSum;
    /** 预提成本报账、全额还款、全额付款、摊销的标识 */
    private String preusedAndShareLabel;
    /** 本年度核定摊销金额/预提成本报账的预提总金额 */
    private BigDecimal auditShareSum;
    /** 首次摊销金额 */
    private BigDecimal firstShareSum;
    /** 摊销费用科目编码 */
    private String shareAccountCode;
    /** 摊销费用科目名称 */
    private String shareAccountName;
    /** 合同编号 */
    private String contractno;
    /** 移动类型 */
    private String moveTypeCode;
    /** 移动类型说明 */
    private String moveTypeName;
    /** 领料用途 */
    private String usage;
    /** 领料用途明细 */
    private String usagedetail;
    /** 预算期间名称 */
    private String budgetsetname;
    /** 是否可修改 */
    private String disposemode;
    /** 标识是否能够修改会计科目 */
    private String iscanupdate;
    /** 用途明细编码 */
    private String usagedetailCode;
    /** 档案入口类型（标识成本报帐还是建管费报帐） */
    private BigDecimal archiveEntryType;
    /** 预算期间ID */
    private String budgetsetid;
    /** 现金流预算事项编码 */
    private String cashBudgetInstanceCode;
    /** 现金流预算事项名称 */
    private String cashBudgetInstanceName;
    /** 借款事项编码 */
    private String loanBudgetInstanceCode;
    /** 借款事项名称 */
    private String loanBudgetInstanceName;
    /** 经办人电话 */
    private String telephone;
    /** 一次性供应商城市 */
    private String onceSupplierCity;
    /** 一次性供应商名称 */
    private String onceSupplierName;
    /** 是否一次性供应商 */
    private String isOnceSupplier;
    /** 是否统付 */
    private String isUnifyPay;
    /** 贷方会计所属公司编码 */
    private String creditAccountCompanyCode;
    /** 薪酬清册编码 */
    private String salaryCode;
    /** 是否省公司支付 */
    private String isProvincePay;
    /** 合同名称 */
    private String contractName;
    /** 合同金额 */
    private BigDecimal contractSum;
    /** 业务编码 */
    private String bizTypeCode;
    /** 入口编码(经济事项id) */
    private String bizEntryCode;
    /** 合同执行进度说明 */
    private String contractDescription;
    /** 付款条款摘要 */
    private String payoffDescription;
    /** 项目名称 */
    private String projectName;
    /** 供应商公司编码 */
    private String supplierCompanyCode;
    /** 银行信息 */
    private String bankCode;
    /** 是否拆分贷方 */
    private String isSplitCredit;
    /** 不见单编码 */
    private String unDisplayCode;
    /** 是否建管费 */
    private String buildFee;
    /** 报备信息id */
    private String writeoffCheckInfoId;
    /** 是否稽核 */
    private String isImageAudited;
    /** 合同清账标识 */
    private String clearAccount;
    /** 是否存在合同清账 */
    private String existClearAccount;
    /** 结转单id */
    private String carryoverInstanceid;
    /** 核定原因 */
    private String auditReason;
    /** 合同发票金额 */
    private BigDecimal contractInvoiceSum;
    /** sap凭证号 */
    private String sapCertificateCode;
    /** 调账类型 */
    private String adjustType;
    /** 是否付款 */
    private String isPayment;
    /** 是否加急 */
    private String isEmergency;
    /** 摊销收益期间开始年 */
    private BigDecimal shareBeginYear;
    /** 摊销收益期间开始月 */
    private BigDecimal shareBeginMonth;
    /** 摊销收益期间结束年 */
    private BigDecimal shareEndYear;
    /** 摊销收益期间结束月 */
    private BigDecimal shareEndMonth;
    /** 营业类型 */
    private String businessType;
    /** 代办点 */
    private String location;
    /** 审定费用发生日 */
    private Date auditHappenDate;
    /** 公司名称文本 人工成本优化审批需要 */
    private String companyNameTxt;
    /** 是否电信代开发票 */
    private String isGdtelInvoice;
    /** 关联交易类型编码 */
    private String relativeTypeCode;
    /** 关联交易类型名称 */
    private String relativeTypeName;
    /** 是否新合同 */
    private BigDecimal isNewContract;
    /** 发出商品金额 */
    private BigDecimal outGoodsSum;
    /** 增值税金额 */
    private BigDecimal payableTaxfeeSum;
    /** 入口名称（经济事项名称） */
    private String bizEntryName;
    /** 往来交易类型 */
    private String tradeType;
    /** 币种 */
    private String currency;
    /** 是否在SAP系统直接制证 */
    private BigDecimal isSapFlag;
    /**  */
    private String cashItemName;
    /** 人工成本---代扣分公司编码 */
    private String withholdingCityId;
    /** 人工成本---代扣分公司名称 */
    private String withholdingCityName;
    /** 业务事项id */
    private String bizItemInstId;
    /** 会计科目编码 */
    private String accountCode;
    /** 会计科目名称 */
    private String accountName;
    /** 会计所属公司编码 */
    private String accountCompanyCode;
    /** 是否借方、贷方（1标识为是借方、0标识是贷方） */
    private String isDebitCredit;
    /** 业务事项类型 */
    private String bizItemType;
    /** 是否员工代垫（0为否、1为是） */
    private String isStaffPayment;
    /** 是否结转 0或者空 为未结转 1:列并付凭证结转 2:付款凭证记账日期结转 */
    private String isCarryover;
    /** sysdate */
    private Date timestamp;
    /** 税费主单ID */
    private String taxFeeMainId;
    /** 风险等级标识 */
    private String riskLevelFlag;
    /** 是否合同首次付款标识 */
    private String isContractFirstPay;
    /** 外围系统主单id */
    private String otherSysMainId;
    /** CBS集成报账税金（CBS集成专用） */
    private BigDecimal proxyTaxAmount;
    /** （营改增）票据类型 */
    private String invoiceType;
    /** （营改增）是否存在实物赠送 */
    private String isExistKindGift;
    /** （营改增）实物赠送金额 */
    private BigDecimal kindGiftSum;
    /** （营改增）实物赠送税额 */
    private BigDecimal kindGiftTaxSum;
    /** （营改增）是否涉及进项税转出 */
    private String isInputTax;
    /** （营改增）进项税金额 */
    private BigDecimal inputTaxSum;
    /** （营改增）是否认证标识（1：待认证、2：已认证、3：认证不通过） */
    private String isVatAuthenticated;
    /** （营改增）进项税转出金额 */
    private BigDecimal inputTaxTurnSum;
    /** （营改增）进项税转出业务类型 */
    private String inputTaxTurnBizType;
    /** 是否例外付款 */
    private String isExcpayment;
    /** （合同预审）合同是否多次使用 */
    private String contractUsedTimes;
    /** 供应商是否一般纳税人 */
    private String isGeneralPayer;
    /** 是否走影像（仅限能耗使用，表示预提业务） */
    private BigDecimal isNeedImage;
    /** 能耗业务数据显示连接 */
    private String showDataUrl;
    /** 报帐档案id */
    private String writeoffArchiveId;
    /** 报帐单号 */
    private String writeoffInstanceCode;
    /** 报帐单类型 */
    private String formTypeCode;
    /** 合计金额 */
    private BigDecimal sum;
    /** 财务核定金额 */
    private BigDecimal auditSum;
    /** 还款金额 */
    private BigDecimal payoffSum;
    /** 核定还款金额 */
    private BigDecimal auditPayoffSum;
    /** 填报人帐号 */
    private String fillInAccount;
    /** 填报人名称 */
    private String fillInName;
    /** 填报人所属部门 */
    private String fillInDep;
    /** 附单据张数 */
    private BigDecimal formAmount;
    /** 纳税属性 */
    private String paytaxattr;
    /** 业务发生时间点标记 */
    private String busihappendtimeflag;
    /** 原用户id */
    private BigDecimal operatorid;
    /** 流程id */
    private BigDecimal processinstid;
    /**  */
    private BigDecimal selectpreid;
    /** 主表guid */
    private String guid;
    /** 合并主单 */
    private BigDecimal parentid;
    /**  */
    private BigDecimal exceptiontype;
    /** 制证会计XX@sc */
    private String sapcreator;
    /** 公司代码 */
    private String sapcompanycode;
    /** 年度 */
    private String year;
    /** 单据类型 1 报销 2 挂账 3挂账支付 4预付 5预付冲销 6借款冲销7前期预付冲销8收款9预估10预估冲销 */
    private BigDecimal billtype;
    /**  */
    private String iresult;
    /** 供应商类型  1供应商 2客户 */
    private String suppliertype;
    /** 所在机构 */
    private BigDecimal orgid;
    /** 能耗类型1水 2 气 3 油 null 电 */
    private BigDecimal energytype;
    /**  */
    private BigDecimal company;

	public void setPaymentType(BigDecimal paymentType)
	{
		this.paymentType = paymentType;
	}

	public BigDecimal getPaymentType() 
	{
		return paymentType;
	}

	public void setStatus(BigDecimal status)
	{
		this.status = status;
	}

	public BigDecimal getStatus() 
	{
		return status;
	}

	public void setClientName(String clientName)
	{
		this.clientName = clientName;
	}

	public String getClientName() 
	{
		return clientName;
	}

	public void setClientCode(String clientCode)
	{
		this.clientCode = clientCode;
	}

	public String getClientCode() 
	{
		return clientCode;
	}

	public void setSupplierName(String supplierName)
	{
		this.supplierName = supplierName;
	}

	public String getSupplierName() 
	{
		return supplierName;
	}

	public void setSupplierCode(String supplierCode)
	{
		this.supplierCode = supplierCode;
	}

	public String getSupplierCode() 
	{
		return supplierCode;
	}

	public void setSupplierBank(String supplierBank)
	{
		this.supplierBank = supplierBank;
	}

	public String getSupplierBank() 
	{
		return supplierBank;
	}

	public void setSupplierAccount(String supplierAccount)
	{
		this.supplierAccount = supplierAccount;
	}

	public String getSupplierAccount() 
	{
		return supplierAccount;
	}

	public void setBudgetItemGroupId(String budgetItemGroupId)
	{
		this.budgetItemGroupId = budgetItemGroupId;
	}

	public String getBudgetItemGroupId() 
	{
		return budgetItemGroupId;
	}

	public void setPackupArchiveId(String packupArchiveId)
	{
		this.packupArchiveId = packupArchiveId;
	}

	public String getPackupArchiveId() 
	{
		return packupArchiveId;
	}

	public void setBizType(BigDecimal bizType)
	{
		this.bizType = bizType;
	}

	public BigDecimal getBizType() 
	{
		return bizType;
	}

	public void setIsPay(BigDecimal isPay)
	{
		this.isPay = isPay;
	}

	public BigDecimal getIsPay() 
	{
		return isPay;
	}

	public void setConTrans(String conTrans)
	{
		this.conTrans = conTrans;
	}

	public String getConTrans() 
	{
		return conTrans;
	}

	public void setIsToPack(BigDecimal isToPack)
	{
		this.isToPack = isToPack;
	}

	public BigDecimal getIsToPack() 
	{
		return isToPack;
	}

	public void setBudgetCode(String budgetCode)
	{
		this.budgetCode = budgetCode;
	}

	public String getBudgetCode() 
	{
		return budgetCode;
	}

	public void setBudgetName(String budgetName)
	{
		this.budgetName = budgetName;
	}

	public String getBudgetName() 
	{
		return budgetName;
	}

	public void setBudgetItemCode(String budgetItemCode)
	{
		this.budgetItemCode = budgetItemCode;
	}

	public String getBudgetItemCode() 
	{
		return budgetItemCode;
	}

	public void setBudgetItemName(String budgetItemName)
	{
		this.budgetItemName = budgetItemName;
	}

	public String getBudgetItemName() 
	{
		return budgetItemName;
	}

	public void setCityId(String cityId)
	{
		this.cityId = cityId;
	}

	public String getCityId() 
	{
		return cityId;
	}

	public void setCityName(String cityName)
	{
		this.cityName = cityName;
	}

	public String getCityName() 
	{
		return cityName;
	}

	public void setProfitCenterCode(String profitCenterCode)
	{
		this.profitCenterCode = profitCenterCode;
	}

	public String getProfitCenterCode() 
	{
		return profitCenterCode;
	}

	public void setProfitCenterName(String profitCenterName)
	{
		this.profitCenterName = profitCenterName;
	}

	public String getProfitCenterName() 
	{
		return profitCenterName;
	}

	public void setCostCenterId(String costCenterId)
	{
		this.costCenterId = costCenterId;
	}

	public String getCostCenterId() 
	{
		return costCenterId;
	}

	public void setCostCenterName(String costCenterName)
	{
		this.costCenterName = costCenterName;
	}

	public String getCostCenterName() 
	{
		return costCenterName;
	}

	public void setCreateDate(Date createDate)
	{
		this.createDate = createDate;
	}

	public Date getCreateDate() 
	{
		return createDate;
	}

	public void setCompanyCode(String companyCode)
	{
		this.companyCode = companyCode;
	}

	public String getCompanyCode() 
	{
		return companyCode;
	}

	public void setIsCategory(BigDecimal isCategory)
	{
		this.isCategory = isCategory;
	}

	public BigDecimal getIsCategory() 
	{
		return isCategory;
	}

	public void setCreditAccountCode(String creditAccountCode)
	{
		this.creditAccountCode = creditAccountCode;
	}

	public String getCreditAccountCode() 
	{
		return creditAccountCode;
	}

	public void setCreditAccountName(String creditAccountName)
	{
		this.creditAccountName = creditAccountName;
	}

	public String getCreditAccountName() 
	{
		return creditAccountName;
	}


	public void setIsPrepay(String isPrepay)
	{
		this.isPrepay = isPrepay;
	}

	public String getIsPrepay() 
	{
		return isPrepay;
	}

	public void setTaxAdjustType(String taxAdjustType)
	{
		this.taxAdjustType = taxAdjustType;
	}

	public String getTaxAdjustType() 
	{
		return taxAdjustType;
	}

	public void setTaxAdjustSum(BigDecimal taxAdjustSum)
	{
		this.taxAdjustSum = taxAdjustSum;
	}

	public BigDecimal getTaxAdjustSum() 
	{
		return taxAdjustSum;
	}

	public void setTaxAdjustComments(String taxAdjustComments)
	{
		this.taxAdjustComments = taxAdjustComments;
	}

	public String getTaxAdjustComments() 
	{
		return taxAdjustComments;
	}

	public void setAssessDataUrl(String assessDataUrl)
	{
		this.assessDataUrl = assessDataUrl;
	}

	public String getAssessDataUrl() 
	{
		return assessDataUrl;
	}

	public void setProvinceCode(String provinceCode)
	{
		this.provinceCode = provinceCode;
	}

	public String getProvinceCode() 
	{
		return provinceCode;
	}

	public void setPickingMode(String pickingMode)
	{
		this.pickingMode = pickingMode;
	}

	public String getPickingMode() 
	{
		return pickingMode;
	}

	public void setBudgetGroupItemCode(String budgetGroupItemCode)
	{
		this.budgetGroupItemCode = budgetGroupItemCode;
	}

	public String getBudgetGroupItemCode() 
	{
		return budgetGroupItemCode;
	}

	public void setBudgetGroupItemName(String budgetGroupItemName)
	{
		this.budgetGroupItemName = budgetGroupItemName;
	}

	public String getBudgetGroupItemName() 
	{
		return budgetGroupItemName;
	}

	public void setResponseCenterCode(String responseCenterCode)
	{
		this.responseCenterCode = responseCenterCode;
	}

	public String getResponseCenterCode() 
	{
		return responseCenterCode;
	}

	public void setResponseCenterName(String responseCenterName)
	{
		this.responseCenterName = responseCenterName;
	}

	public String getResponseCenterName() 
	{
		return responseCenterName;
	}

	public void setAttchmentgAdd(String attchmentgAdd)
	{
		this.attchmentgAdd = attchmentgAdd;
	}

	public String getAttchmentgAdd() 
	{
		return attchmentgAdd;
	}

	public void setIsTaxAdjust(String isTaxAdjust)
	{
		this.isTaxAdjust = isTaxAdjust;
	}

	public String getIsTaxAdjust() 
	{
		return isTaxAdjust;
	}

	public void setAbstract(String abstractVal)
	{
		this.abstractVal = abstractVal;
	}

	public String getAbstractVal()
	{
		return abstractVal;
	}

	public void setFillInProfitCenterId(String fillInProfitCenterId)
	{
		this.fillInProfitCenterId = fillInProfitCenterId;
	}

	public String getFillInProfitCenterId() 
	{
		return fillInProfitCenterId;
	}

	public void setFillInCostCenterId(String fillInCostCenterId)
	{
		this.fillInCostCenterId = fillInCostCenterId;
	}

	public String getFillInCostCenterId() 
	{
		return fillInCostCenterId;
	}

	public void setFillInProfitCenterName(String fillInProfitCenterName)
	{
		this.fillInProfitCenterName = fillInProfitCenterName;
	}

	public String getFillInProfitCenterName() 
	{
		return fillInProfitCenterName;
	}

	public void setFillInCostCenterName(String fillInCostCenterName)
	{
		this.fillInCostCenterName = fillInCostCenterName;
	}

	public String getFillInCostCenterName() 
	{
		return fillInCostCenterName;
	}

	public void setReplenishFillIn(BigDecimal replenishFillIn)
	{
		this.replenishFillIn = replenishFillIn;
	}

	public BigDecimal getReplenishFillIn() 
	{
		return replenishFillIn;
	}

	public void setFillInRoleId(String fillInRoleId)
	{
		this.fillInRoleId = fillInRoleId;
	}

	public String getFillInRoleId() 
	{
		return fillInRoleId;
	}

	public void setBeAfterOperateSum(BigDecimal beAfterOperateSum)
	{
		this.beAfterOperateSum = beAfterOperateSum;
	}

	public BigDecimal getBeAfterOperateSum() 
	{
		return beAfterOperateSum;
	}

	public void setFeeType(String feeType)
	{
		this.feeType = feeType;
	}

	public String getFeeType() 
	{
		return feeType;
	}

	public void setHappenDate(String happenDate)
	{
		this.happenDate = happenDate;
	}

	public String getHappenDate() 
	{
		return happenDate;
	}

	public void setSapRemarkDate(Date sapRemarkDate)
	{
		this.sapRemarkDate = sapRemarkDate;
	}

	public Date getSapRemarkDate() 
	{
		return sapRemarkDate;
	}

	public void setClearSum(BigDecimal clearSum)
	{
		this.clearSum = clearSum;
	}

	public BigDecimal getClearSum() 
	{
		return clearSum;
	}

	public void setPreusedAndShareLabel(String preusedAndShareLabel)
	{
		this.preusedAndShareLabel = preusedAndShareLabel;
	}

	public String getPreusedAndShareLabel() 
	{
		return preusedAndShareLabel;
	}

	public void setAuditShareSum(BigDecimal auditShareSum)
	{
		this.auditShareSum = auditShareSum;
	}

	public BigDecimal getAuditShareSum() 
	{
		return auditShareSum;
	}

	public void setFirstShareSum(BigDecimal firstShareSum)
	{
		this.firstShareSum = firstShareSum;
	}

	public BigDecimal getFirstShareSum() 
	{
		return firstShareSum;
	}

	public void setShareAccountCode(String shareAccountCode)
	{
		this.shareAccountCode = shareAccountCode;
	}

	public String getShareAccountCode() 
	{
		return shareAccountCode;
	}

	public void setShareAccountName(String shareAccountName)
	{
		this.shareAccountName = shareAccountName;
	}

	public String getShareAccountName() 
	{
		return shareAccountName;
	}

	public void setContractno(String contractno)
	{
		this.contractno = contractno;
	}

	public String getContractno() 
	{
		return contractno;
	}

	public void setMoveTypeCode(String moveTypeCode)
	{
		this.moveTypeCode = moveTypeCode;
	}

	public String getMoveTypeCode() 
	{
		return moveTypeCode;
	}

	public void setMoveTypeName(String moveTypeName)
	{
		this.moveTypeName = moveTypeName;
	}

	public String getMoveTypeName() 
	{
		return moveTypeName;
	}

	public void setUsage(String usage)
	{
		this.usage = usage;
	}

	public String getUsage() 
	{
		return usage;
	}

	public void setUsagedetail(String usagedetail)
	{
		this.usagedetail = usagedetail;
	}

	public String getUsagedetail() 
	{
		return usagedetail;
	}

	public void setBudgetsetname(String budgetsetname)
	{
		this.budgetsetname = budgetsetname;
	}

	public String getBudgetsetname() 
	{
		return budgetsetname;
	}

	public void setDisposemode(String disposemode)
	{
		this.disposemode = disposemode;
	}

	public String getDisposemode() 
	{
		return disposemode;
	}

	public void setIscanupdate(String iscanupdate)
	{
		this.iscanupdate = iscanupdate;
	}

	public String getIscanupdate() 
	{
		return iscanupdate;
	}

	public void setUsagedetailCode(String usagedetailCode)
	{
		this.usagedetailCode = usagedetailCode;
	}

	public String getUsagedetailCode() 
	{
		return usagedetailCode;
	}

	public void setArchiveEntryType(BigDecimal archiveEntryType)
	{
		this.archiveEntryType = archiveEntryType;
	}

	public BigDecimal getArchiveEntryType() 
	{
		return archiveEntryType;
	}

	public void setBudgetsetid(String budgetsetid)
	{
		this.budgetsetid = budgetsetid;
	}

	public String getBudgetsetid() 
	{
		return budgetsetid;
	}

	public void setCashBudgetInstanceCode(String cashBudgetInstanceCode)
	{
		this.cashBudgetInstanceCode = cashBudgetInstanceCode;
	}

	public String getCashBudgetInstanceCode() 
	{
		return cashBudgetInstanceCode;
	}

	public void setCashBudgetInstanceName(String cashBudgetInstanceName)
	{
		this.cashBudgetInstanceName = cashBudgetInstanceName;
	}

	public String getCashBudgetInstanceName() 
	{
		return cashBudgetInstanceName;
	}

	public void setLoanBudgetInstanceCode(String loanBudgetInstanceCode)
	{
		this.loanBudgetInstanceCode = loanBudgetInstanceCode;
	}

	public String getLoanBudgetInstanceCode() 
	{
		return loanBudgetInstanceCode;
	}

	public void setLoanBudgetInstanceName(String loanBudgetInstanceName)
	{
		this.loanBudgetInstanceName = loanBudgetInstanceName;
	}

	public String getLoanBudgetInstanceName() 
	{
		return loanBudgetInstanceName;
	}

	public void setTelephone(String telephone)
	{
		this.telephone = telephone;
	}

	public String getTelephone() 
	{
		return telephone;
	}

	public void setOnceSupplierCity(String onceSupplierCity)
	{
		this.onceSupplierCity = onceSupplierCity;
	}

	public String getOnceSupplierCity() 
	{
		return onceSupplierCity;
	}

	public void setOnceSupplierName(String onceSupplierName)
	{
		this.onceSupplierName = onceSupplierName;
	}

	public String getOnceSupplierName() 
	{
		return onceSupplierName;
	}

	public void setIsOnceSupplier(String isOnceSupplier)
	{
		this.isOnceSupplier = isOnceSupplier;
	}

	public String getIsOnceSupplier() 
	{
		return isOnceSupplier;
	}

	public void setIsUnifyPay(String isUnifyPay)
	{
		this.isUnifyPay = isUnifyPay;
	}

	public String getIsUnifyPay() 
	{
		return isUnifyPay;
	}

	public void setCreditAccountCompanyCode(String creditAccountCompanyCode)
	{
		this.creditAccountCompanyCode = creditAccountCompanyCode;
	}

	public String getCreditAccountCompanyCode() 
	{
		return creditAccountCompanyCode;
	}

	public void setSalaryCode(String salaryCode)
	{
		this.salaryCode = salaryCode;
	}

	public String getSalaryCode() 
	{
		return salaryCode;
	}

	public void setIsProvincePay(String isProvincePay)
	{
		this.isProvincePay = isProvincePay;
	}

	public String getIsProvincePay() 
	{
		return isProvincePay;
	}

	public void setContractName(String contractName)
	{
		this.contractName = contractName;
	}

	public String getContractName() 
	{
		return contractName;
	}

	public void setContractSum(BigDecimal contractSum)
	{
		this.contractSum = contractSum;
	}

	public BigDecimal getContractSum() 
	{
		return contractSum;
	}

	public void setBizTypeCode(String bizTypeCode)
	{
		this.bizTypeCode = bizTypeCode;
	}

	public String getBizTypeCode() 
	{
		return bizTypeCode;
	}

	public void setBizEntryCode(String bizEntryCode)
	{
		this.bizEntryCode = bizEntryCode;
	}

	public String getBizEntryCode() 
	{
		return bizEntryCode;
	}

	public void setContractDescription(String contractDescription)
	{
		this.contractDescription = contractDescription;
	}

	public String getContractDescription() 
	{
		return contractDescription;
	}

	public void setPayoffDescription(String payoffDescription)
	{
		this.payoffDescription = payoffDescription;
	}

	public String getPayoffDescription() 
	{
		return payoffDescription;
	}

	public void setProjectName(String projectName)
	{
		this.projectName = projectName;
	}

	public String getProjectName() 
	{
		return projectName;
	}

	public void setSupplierCompanyCode(String supplierCompanyCode)
	{
		this.supplierCompanyCode = supplierCompanyCode;
	}

	public String getSupplierCompanyCode() 
	{
		return supplierCompanyCode;
	}

	public void setBankCode(String bankCode)
	{
		this.bankCode = bankCode;
	}

	public String getBankCode() 
	{
		return bankCode;
	}

	public void setIsSplitCredit(String isSplitCredit)
	{
		this.isSplitCredit = isSplitCredit;
	}

	public String getIsSplitCredit() 
	{
		return isSplitCredit;
	}

	public void setUnDisplayCode(String unDisplayCode)
	{
		this.unDisplayCode = unDisplayCode;
	}

	public String getUnDisplayCode() 
	{
		return unDisplayCode;
	}

	public void setBuildFee(String buildFee)
	{
		this.buildFee = buildFee;
	}

	public String getBuildFee() 
	{
		return buildFee;
	}

	public void setWriteoffCheckInfoId(String writeoffCheckInfoId)
	{
		this.writeoffCheckInfoId = writeoffCheckInfoId;
	}

	public String getWriteoffCheckInfoId() 
	{
		return writeoffCheckInfoId;
	}

	public void setIsImageAudited(String isImageAudited)
	{
		this.isImageAudited = isImageAudited;
	}

	public String getIsImageAudited() 
	{
		return isImageAudited;
	}

	public void setClearAccount(String clearAccount)
	{
		this.clearAccount = clearAccount;
	}

	public String getClearAccount() 
	{
		return clearAccount;
	}

	public void setExistClearAccount(String existClearAccount)
	{
		this.existClearAccount = existClearAccount;
	}

	public String getExistClearAccount() 
	{
		return existClearAccount;
	}

	public void setCarryoverInstanceid(String carryoverInstanceid)
	{
		this.carryoverInstanceid = carryoverInstanceid;
	}

	public String getCarryoverInstanceid() 
	{
		return carryoverInstanceid;
	}

	public void setAuditReason(String auditReason)
	{
		this.auditReason = auditReason;
	}

	public String getAuditReason() 
	{
		return auditReason;
	}

	public void setContractInvoiceSum(BigDecimal contractInvoiceSum)
	{
		this.contractInvoiceSum = contractInvoiceSum;
	}

	public BigDecimal getContractInvoiceSum() 
	{
		return contractInvoiceSum;
	}

	public void setSapCertificateCode(String sapCertificateCode)
	{
		this.sapCertificateCode = sapCertificateCode;
	}

	public String getSapCertificateCode() 
	{
		return sapCertificateCode;
	}

	public void setAdjustType(String adjustType)
	{
		this.adjustType = adjustType;
	}

	public String getAdjustType() 
	{
		return adjustType;
	}

	public void setIsPayment(String isPayment)
	{
		this.isPayment = isPayment;
	}

	public String getIsPayment() 
	{
		return isPayment;
	}

	public void setIsEmergency(String isEmergency)
	{
		this.isEmergency = isEmergency;
	}

	public String getIsEmergency() 
	{
		return isEmergency;
	}

	public void setShareBeginYear(BigDecimal shareBeginYear)
	{
		this.shareBeginYear = shareBeginYear;
	}

	public BigDecimal getShareBeginYear() 
	{
		return shareBeginYear;
	}

	public void setShareBeginMonth(BigDecimal shareBeginMonth)
	{
		this.shareBeginMonth = shareBeginMonth;
	}

	public BigDecimal getShareBeginMonth() 
	{
		return shareBeginMonth;
	}

	public void setShareEndYear(BigDecimal shareEndYear)
	{
		this.shareEndYear = shareEndYear;
	}

	public BigDecimal getShareEndYear() 
	{
		return shareEndYear;
	}

	public void setShareEndMonth(BigDecimal shareEndMonth)
	{
		this.shareEndMonth = shareEndMonth;
	}

	public BigDecimal getShareEndMonth() 
	{
		return shareEndMonth;
	}

	public void setBusinessType(String businessType)
	{
		this.businessType = businessType;
	}

	public String getBusinessType() 
	{
		return businessType;
	}

	public void setLocation(String location)
	{
		this.location = location;
	}

	public String getLocation() 
	{
		return location;
	}

	public void setAuditHappenDate(Date auditHappenDate)
	{
		this.auditHappenDate = auditHappenDate;
	}

	public Date getAuditHappenDate() 
	{
		return auditHappenDate;
	}

	public void setCompanyNameTxt(String companyNameTxt)
	{
		this.companyNameTxt = companyNameTxt;
	}

	public String getCompanyNameTxt() 
	{
		return companyNameTxt;
	}

	public void setIsGdtelInvoice(String isGdtelInvoice)
	{
		this.isGdtelInvoice = isGdtelInvoice;
	}

	public String getIsGdtelInvoice() 
	{
		return isGdtelInvoice;
	}

	public void setRelativeTypeCode(String relativeTypeCode)
	{
		this.relativeTypeCode = relativeTypeCode;
	}

	public String getRelativeTypeCode() 
	{
		return relativeTypeCode;
	}

	public void setRelativeTypeName(String relativeTypeName)
	{
		this.relativeTypeName = relativeTypeName;
	}

	public String getRelativeTypeName() 
	{
		return relativeTypeName;
	}

	public void setIsNewContract(BigDecimal isNewContract)
	{
		this.isNewContract = isNewContract;
	}

	public BigDecimal getIsNewContract() 
	{
		return isNewContract;
	}

	public void setOutGoodsSum(BigDecimal outGoodsSum)
	{
		this.outGoodsSum = outGoodsSum;
	}

	public BigDecimal getOutGoodsSum() 
	{
		return outGoodsSum;
	}

	public void setPayableTaxfeeSum(BigDecimal payableTaxfeeSum)
	{
		this.payableTaxfeeSum = payableTaxfeeSum;
	}

	public BigDecimal getPayableTaxfeeSum() 
	{
		return payableTaxfeeSum;
	}

	public void setBizEntryName(String bizEntryName)
	{
		this.bizEntryName = bizEntryName;
	}

	public String getBizEntryName() 
	{
		return bizEntryName;
	}

	public void setTradeType(String tradeType)
	{
		this.tradeType = tradeType;
	}

	public String getTradeType() 
	{
		return tradeType;
	}

	public void setCurrency(String currency)
	{
		this.currency = currency;
	}

	public String getCurrency() 
	{
		return currency;
	}

	public void setIsSapFlag(BigDecimal isSapFlag)
	{
		this.isSapFlag = isSapFlag;
	}

	public BigDecimal getIsSapFlag() 
	{
		return isSapFlag;
	}

	public void setCashItemName(String cashItemName)
	{
		this.cashItemName = cashItemName;
	}

	public String getCashItemName() 
	{
		return cashItemName;
	}

	public void setWithholdingCityId(String withholdingCityId)
	{
		this.withholdingCityId = withholdingCityId;
	}

	public String getWithholdingCityId() 
	{
		return withholdingCityId;
	}

	public void setWithholdingCityName(String withholdingCityName)
	{
		this.withholdingCityName = withholdingCityName;
	}

	public String getWithholdingCityName() 
	{
		return withholdingCityName;
	}

	public void setBizItemInstId(String bizItemInstId)
	{
		this.bizItemInstId = bizItemInstId;
	}

	public String getBizItemInstId() 
	{
		return bizItemInstId;
	}

	public void setAccountCode(String accountCode)
	{
		this.accountCode = accountCode;
	}

	public String getAccountCode() 
	{
		return accountCode;
	}

	public void setAccountName(String accountName)
	{
		this.accountName = accountName;
	}

	public String getAccountName() 
	{
		return accountName;
	}

	public void setAccountCompanyCode(String accountCompanyCode)
	{
		this.accountCompanyCode = accountCompanyCode;
	}

	public String getAccountCompanyCode() 
	{
		return accountCompanyCode;
	}

	public void setIsDebitCredit(String isDebitCredit)
	{
		this.isDebitCredit = isDebitCredit;
	}

	public String getIsDebitCredit() 
	{
		return isDebitCredit;
	}

	public void setBizItemType(String bizItemType)
	{
		this.bizItemType = bizItemType;
	}

	public String getBizItemType() 
	{
		return bizItemType;
	}

	public void setIsStaffPayment(String isStaffPayment)
	{
		this.isStaffPayment = isStaffPayment;
	}

	public String getIsStaffPayment() 
	{
		return isStaffPayment;
	}

	public void setIsCarryover(String isCarryover)
	{
		this.isCarryover = isCarryover;
	}

	public String getIsCarryover() 
	{
		return isCarryover;
	}

	public void setTimestamp(Date timestamp)
	{
		this.timestamp = timestamp;
	}

	public Date getTimestamp() 
	{
		return timestamp;
	}

	public void setTaxFeeMainId(String taxFeeMainId)
	{
		this.taxFeeMainId = taxFeeMainId;
	}

	public String getTaxFeeMainId() 
	{
		return taxFeeMainId;
	}

	public void setRiskLevelFlag(String riskLevelFlag)
	{
		this.riskLevelFlag = riskLevelFlag;
	}

	public String getRiskLevelFlag() 
	{
		return riskLevelFlag;
	}

	public void setIsContractFirstPay(String isContractFirstPay)
	{
		this.isContractFirstPay = isContractFirstPay;
	}

	public String getIsContractFirstPay() 
	{
		return isContractFirstPay;
	}

	public void setOtherSysMainId(String otherSysMainId)
	{
		this.otherSysMainId = otherSysMainId;
	}

	public String getOtherSysMainId() 
	{
		return otherSysMainId;
	}

	public void setProxyTaxAmount(BigDecimal proxyTaxAmount)
	{
		this.proxyTaxAmount = proxyTaxAmount;
	}

	public BigDecimal getProxyTaxAmount() 
	{
		return proxyTaxAmount;
	}

	public void setInvoiceType(String invoiceType)
	{
		this.invoiceType = invoiceType;
	}

	public String getInvoiceType() 
	{
		return invoiceType;
	}

	public void setIsExistKindGift(String isExistKindGift)
	{
		this.isExistKindGift = isExistKindGift;
	}

	public String getIsExistKindGift() 
	{
		return isExistKindGift;
	}

	public void setKindGiftSum(BigDecimal kindGiftSum)
	{
		this.kindGiftSum = kindGiftSum;
	}

	public BigDecimal getKindGiftSum() 
	{
		return kindGiftSum;
	}

	public void setKindGiftTaxSum(BigDecimal kindGiftTaxSum)
	{
		this.kindGiftTaxSum = kindGiftTaxSum;
	}

	public BigDecimal getKindGiftTaxSum() 
	{
		return kindGiftTaxSum;
	}

	public void setIsInputTax(String isInputTax)
	{
		this.isInputTax = isInputTax;
	}

	public String getIsInputTax() 
	{
		return isInputTax;
	}

	public void setInputTaxSum(BigDecimal inputTaxSum)
	{
		this.inputTaxSum = inputTaxSum;
	}

	public BigDecimal getInputTaxSum() 
	{
		return inputTaxSum;
	}

	public void setIsVatAuthenticated(String isVatAuthenticated)
	{
		this.isVatAuthenticated = isVatAuthenticated;
	}

	public String getIsVatAuthenticated() 
	{
		return isVatAuthenticated;
	}

	public void setInputTaxTurnSum(BigDecimal inputTaxTurnSum)
	{
		this.inputTaxTurnSum = inputTaxTurnSum;
	}

	public BigDecimal getInputTaxTurnSum() 
	{
		return inputTaxTurnSum;
	}

	public void setInputTaxTurnBizType(String inputTaxTurnBizType)
	{
		this.inputTaxTurnBizType = inputTaxTurnBizType;
	}

	public String getInputTaxTurnBizType() 
	{
		return inputTaxTurnBizType;
	}

	public void setIsExcpayment(String isExcpayment)
	{
		this.isExcpayment = isExcpayment;
	}

	public String getIsExcpayment() 
	{
		return isExcpayment;
	}

	public void setContractUsedTimes(String contractUsedTimes)
	{
		this.contractUsedTimes = contractUsedTimes;
	}

	public String getContractUsedTimes() 
	{
		return contractUsedTimes;
	}

	public void setIsGeneralPayer(String isGeneralPayer)
	{
		this.isGeneralPayer = isGeneralPayer;
	}

	public String getIsGeneralPayer() 
	{
		return isGeneralPayer;
	}

	public void setIsNeedImage(BigDecimal isNeedImage)
	{
		this.isNeedImage = isNeedImage;
	}

	public BigDecimal getIsNeedImage() 
	{
		return isNeedImage;
	}

	public void setShowDataUrl(String showDataUrl)
	{
		this.showDataUrl = showDataUrl;
	}

	public String getShowDataUrl() 
	{
		return showDataUrl;
	}

	public void setWriteoffArchiveId(String writeoffArchiveId)
	{
		this.writeoffArchiveId = writeoffArchiveId;
	}

	public String getWriteoffArchiveId() 
	{
		return writeoffArchiveId;
	}

	public void setWriteoffInstanceCode(String writeoffInstanceCode)
	{
		this.writeoffInstanceCode = writeoffInstanceCode;
	}

	public String getWriteoffInstanceCode() 
	{
		return writeoffInstanceCode;
	}

	public void setFormTypeCode(String formTypeCode)
	{
		this.formTypeCode = formTypeCode;
	}

	public String getFormTypeCode() 
	{
		return formTypeCode;
	}

	public void setSum(BigDecimal sum)
	{
		this.sum = sum;
	}

	public BigDecimal getSum() 
	{
		return sum;
	}

	public void setAuditSum(BigDecimal auditSum)
	{
		this.auditSum = auditSum;
	}

	public BigDecimal getAuditSum() 
	{
		return auditSum;
	}

	public void setPayoffSum(BigDecimal payoffSum)
	{
		this.payoffSum = payoffSum;
	}

	public BigDecimal getPayoffSum() 
	{
		return payoffSum;
	}

	public void setAuditPayoffSum(BigDecimal auditPayoffSum)
	{
		this.auditPayoffSum = auditPayoffSum;
	}

	public BigDecimal getAuditPayoffSum() 
	{
		return auditPayoffSum;
	}

	public void setFillInAccount(String fillInAccount)
	{
		this.fillInAccount = fillInAccount;
	}

	public String getFillInAccount() 
	{
		return fillInAccount;
	}

	public void setFillInName(String fillInName)
	{
		this.fillInName = fillInName;
	}

	public String getFillInName() 
	{
		return fillInName;
	}

	public void setFillInDep(String fillInDep)
	{
		this.fillInDep = fillInDep;
	}

	public String getFillInDep() 
	{
		return fillInDep;
	}

	public void setFormAmount(BigDecimal formAmount)
	{
		this.formAmount = formAmount;
	}

	public BigDecimal getFormAmount() 
	{
		return formAmount;
	}

	public void setPaytaxattr(String paytaxattr)
	{
		this.paytaxattr = paytaxattr;
	}

	public String getPaytaxattr() 
	{
		return paytaxattr;
	}

	public void setBusihappendtimeflag(String busihappendtimeflag)
	{
		this.busihappendtimeflag = busihappendtimeflag;
	}

	public String getBusihappendtimeflag() 
	{
		return busihappendtimeflag;
	}

	public void setOperatorid(BigDecimal operatorid)
	{
		this.operatorid = operatorid;
	}

	public BigDecimal getOperatorid() 
	{
		return operatorid;
	}

	public void setProcessinstid(BigDecimal processinstid)
	{
		this.processinstid = processinstid;
	}

	public BigDecimal getProcessinstid() 
	{
		return processinstid;
	}

	public void setSelectpreid(BigDecimal selectpreid)
	{
		this.selectpreid = selectpreid;
	}

	public BigDecimal getSelectpreid() 
	{
		return selectpreid;
	}

	public void setGuid(String guid)
	{
		this.guid = guid;
	}

	public String getGuid() 
	{
		return guid;
	}

	public void setParentid(BigDecimal parentid)
	{
		this.parentid = parentid;
	}

	public BigDecimal getParentid() 
	{
		return parentid;
	}

	public void setExceptiontype(BigDecimal exceptiontype)
	{
		this.exceptiontype = exceptiontype;
	}

	public BigDecimal getExceptiontype() 
	{
		return exceptiontype;
	}

	public void setSapcreator(String sapcreator)
	{
		this.sapcreator = sapcreator;
	}

	public String getSapcreator() 
	{
		return sapcreator;
	}

	public void setSapcompanycode(String sapcompanycode)
	{
		this.sapcompanycode = sapcompanycode;
	}

	public String getSapcompanycode() 
	{
		return sapcompanycode;
	}

	public void setYear(String year)
	{
		this.year = year;
	}

	public String getYear() 
	{
		return year;
	}

	public void setBilltype(BigDecimal billtype)
	{
		this.billtype = billtype;
	}

	public BigDecimal getBilltype() 
	{
		return billtype;
	}

	public void setIresult(String iresult)
	{
		this.iresult = iresult;
	}

	public String getIresult() 
	{
		return iresult;
	}

	public void setSuppliertype(String suppliertype)
	{
		this.suppliertype = suppliertype;
	}

	public String getSuppliertype() 
	{
		return suppliertype;
	}

	public void setOrgid(BigDecimal orgid)
	{
		this.orgid = orgid;
	}

	public BigDecimal getOrgid() 
	{
		return orgid;
	}

	public void setEnergytype(BigDecimal energytype)
	{
		this.energytype = energytype;
	}

	public BigDecimal getEnergytype() 
	{
		return energytype;
	}

	public void setCompany(BigDecimal company)
	{
		this.company = company;
	}

	public BigDecimal getCompany() 
	{
		return company;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("paymentType", getPaymentType())
            .append("status", getStatus())
            .append("clientName", getClientName())
            .append("clientCode", getClientCode())
            .append("supplierName", getSupplierName())
            .append("supplierCode", getSupplierCode())
            .append("supplierBank", getSupplierBank())
            .append("supplierAccount", getSupplierAccount())
            .append("budgetItemGroupId", getBudgetItemGroupId())
            .append("packupArchiveId", getPackupArchiveId())
            .append("bizType", getBizType())
            .append("isPay", getIsPay())
            .append("conTrans", getConTrans())
            .append("isToPack", getIsToPack())
            .append("budgetCode", getBudgetCode())
            .append("budgetName", getBudgetName())
            .append("budgetItemCode", getBudgetItemCode())
            .append("budgetItemName", getBudgetItemName())
            .append("cityId", getCityId())
            .append("cityName", getCityName())
            .append("profitCenterCode", getProfitCenterCode())
            .append("profitCenterName", getProfitCenterName())
            .append("costCenterId", getCostCenterId())
            .append("costCenterName", getCostCenterName())
            .append("createDate", getCreateDate())
            .append("companyCode", getCompanyCode())
            .append("isCategory", getIsCategory())
            .append("creditAccountCode", getCreditAccountCode())
            .append("creditAccountName", getCreditAccountName())
            .append("id", getId())
            .append("isPrepay", getIsPrepay())
            .append("taxAdjustType", getTaxAdjustType())
            .append("taxAdjustSum", getTaxAdjustSum())
            .append("taxAdjustComments", getTaxAdjustComments())
            .append("assessDataUrl", getAssessDataUrl())
            .append("provinceCode", getProvinceCode())
            .append("pickingMode", getPickingMode())
            .append("budgetGroupItemCode", getBudgetGroupItemCode())
            .append("budgetGroupItemName", getBudgetGroupItemName())
            .append("responseCenterCode", getResponseCenterCode())
            .append("responseCenterName", getResponseCenterName())
            .append("attchmentgAdd", getAttchmentgAdd())
            .append("isTaxAdjust", getIsTaxAdjust())
            .append("abstract", getAbstractVal())
            .append("fillInProfitCenterId", getFillInProfitCenterId())
            .append("fillInCostCenterId", getFillInCostCenterId())
            .append("fillInProfitCenterName", getFillInProfitCenterName())
            .append("fillInCostCenterName", getFillInCostCenterName())
            .append("replenishFillIn", getReplenishFillIn())
            .append("fillInRoleId", getFillInRoleId())
            .append("beAfterOperateSum", getBeAfterOperateSum())
            .append("feeType", getFeeType())
            .append("happenDate", getHappenDate())
            .append("sapRemarkDate", getSapRemarkDate())
            .append("clearSum", getClearSum())
            .append("preusedAndShareLabel", getPreusedAndShareLabel())
            .append("auditShareSum", getAuditShareSum())
            .append("firstShareSum", getFirstShareSum())
            .append("shareAccountCode", getShareAccountCode())
            .append("shareAccountName", getShareAccountName())
            .append("contractno", getContractno())
            .append("moveTypeCode", getMoveTypeCode())
            .append("moveTypeName", getMoveTypeName())
            .append("usage", getUsage())
            .append("usagedetail", getUsagedetail())
            .append("budgetsetname", getBudgetsetname())
            .append("disposemode", getDisposemode())
            .append("iscanupdate", getIscanupdate())
            .append("usagedetailCode", getUsagedetailCode())
            .append("archiveEntryType", getArchiveEntryType())
            .append("budgetsetid", getBudgetsetid())
            .append("cashBudgetInstanceCode", getCashBudgetInstanceCode())
            .append("cashBudgetInstanceName", getCashBudgetInstanceName())
            .append("loanBudgetInstanceCode", getLoanBudgetInstanceCode())
            .append("loanBudgetInstanceName", getLoanBudgetInstanceName())
            .append("telephone", getTelephone())
            .append("onceSupplierCity", getOnceSupplierCity())
            .append("onceSupplierName", getOnceSupplierName())
            .append("isOnceSupplier", getIsOnceSupplier())
            .append("isUnifyPay", getIsUnifyPay())
            .append("creditAccountCompanyCode", getCreditAccountCompanyCode())
            .append("salaryCode", getSalaryCode())
            .append("isProvincePay", getIsProvincePay())
            .append("contractName", getContractName())
            .append("contractSum", getContractSum())
            .append("bizTypeCode", getBizTypeCode())
            .append("bizEntryCode", getBizEntryCode())
            .append("contractDescription", getContractDescription())
            .append("payoffDescription", getPayoffDescription())
            .append("projectName", getProjectName())
            .append("supplierCompanyCode", getSupplierCompanyCode())
            .append("bankCode", getBankCode())
            .append("isSplitCredit", getIsSplitCredit())
            .append("unDisplayCode", getUnDisplayCode())
            .append("buildFee", getBuildFee())
            .append("writeoffCheckInfoId", getWriteoffCheckInfoId())
            .append("isImageAudited", getIsImageAudited())
            .append("clearAccount", getClearAccount())
            .append("existClearAccount", getExistClearAccount())
            .append("carryoverInstanceid", getCarryoverInstanceid())
            .append("auditReason", getAuditReason())
            .append("contractInvoiceSum", getContractInvoiceSum())
            .append("sapCertificateCode", getSapCertificateCode())
            .append("adjustType", getAdjustType())
            .append("isPayment", getIsPayment())
            .append("isEmergency", getIsEmergency())
            .append("shareBeginYear", getShareBeginYear())
            .append("shareBeginMonth", getShareBeginMonth())
            .append("shareEndYear", getShareEndYear())
            .append("shareEndMonth", getShareEndMonth())
            .append("businessType", getBusinessType())
            .append("location", getLocation())
            .append("auditHappenDate", getAuditHappenDate())
            .append("companyNameTxt", getCompanyNameTxt())
            .append("isGdtelInvoice", getIsGdtelInvoice())
            .append("relativeTypeCode", getRelativeTypeCode())
            .append("relativeTypeName", getRelativeTypeName())
            .append("isNewContract", getIsNewContract())
            .append("outGoodsSum", getOutGoodsSum())
            .append("payableTaxfeeSum", getPayableTaxfeeSum())
            .append("bizEntryName", getBizEntryName())
            .append("tradeType", getTradeType())
            .append("currency", getCurrency())
            .append("isSapFlag", getIsSapFlag())
            .append("cashItemName", getCashItemName())
            .append("withholdingCityId", getWithholdingCityId())
            .append("withholdingCityName", getWithholdingCityName())
            .append("bizItemInstId", getBizItemInstId())
            .append("accountCode", getAccountCode())
            .append("accountName", getAccountName())
            .append("accountCompanyCode", getAccountCompanyCode())
            .append("isDebitCredit", getIsDebitCredit())
            .append("bizItemType", getBizItemType())
            .append("isStaffPayment", getIsStaffPayment())
            .append("isCarryover", getIsCarryover())
            .append("timestamp", getTimestamp())
            .append("taxFeeMainId", getTaxFeeMainId())
            .append("riskLevelFlag", getRiskLevelFlag())
            .append("isContractFirstPay", getIsContractFirstPay())
            .append("otherSysMainId", getOtherSysMainId())
            .append("proxyTaxAmount", getProxyTaxAmount())
            .append("invoiceType", getInvoiceType())
            .append("isExistKindGift", getIsExistKindGift())
            .append("kindGiftSum", getKindGiftSum())
            .append("kindGiftTaxSum", getKindGiftTaxSum())
            .append("isInputTax", getIsInputTax())
            .append("inputTaxSum", getInputTaxSum())
            .append("isVatAuthenticated", getIsVatAuthenticated())
            .append("inputTaxTurnSum", getInputTaxTurnSum())
            .append("inputTaxTurnBizType", getInputTaxTurnBizType())
            .append("isExcpayment", getIsExcpayment())
            .append("contractUsedTimes", getContractUsedTimes())
            .append("isGeneralPayer", getIsGeneralPayer())
            .append("isNeedImage", getIsNeedImage())
            .append("showDataUrl", getShowDataUrl())
            .append("writeoffArchiveId", getWriteoffArchiveId())
            .append("writeoffInstanceCode", getWriteoffInstanceCode())
            .append("formTypeCode", getFormTypeCode())
            .append("sum", getSum())
            .append("auditSum", getAuditSum())
            .append("payoffSum", getPayoffSum())
            .append("auditPayoffSum", getAuditPayoffSum())
            .append("fillInAccount", getFillInAccount())
            .append("fillInName", getFillInName())
            .append("fillInDep", getFillInDep())
            .append("formAmount", getFormAmount())
            .append("paytaxattr", getPaytaxattr())
            .append("busihappendtimeflag", getBusihappendtimeflag())
            .append("operatorid", getOperatorid())
            .append("processinstid", getProcessinstid())
            .append("selectpreid", getSelectpreid())
            .append("guid", getGuid())
            .append("parentid", getParentid())
            .append("exceptiontype", getExceptiontype())
            .append("sapcreator", getSapcreator())
            .append("sapcompanycode", getSapcompanycode())
            .append("year", getYear())
            .append("billtype", getBilltype())
            .append("iresult", getIresult())
            .append("suppliertype", getSuppliertype())
            .append("orgid", getOrgid())
            .append("energytype", getEnergytype())
            .append("company", getCompany())
            .toString();
    }
}
