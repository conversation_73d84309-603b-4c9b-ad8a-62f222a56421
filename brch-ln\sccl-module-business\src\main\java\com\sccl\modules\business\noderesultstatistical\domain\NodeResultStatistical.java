package com.sccl.modules.business.noderesultstatistical.domain;

import com.sccl.framework.web.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;


/**
 * 统计指标表 node_result_statistical
 *
 * <AUTHOR>
 * @date 2022-11-24
 */
@Data
public class NodeResultStatistical extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 统计组ID
     */
    private Long groupId;
    /**
     * 统计组别名
     */
    private String groupAlias;
    /**
     * 统计项标题
     */
    private String title;
    /**
     * 报账id
     */
    private Long billid;
    /**
     * 节点类型
     */
    private Integer nodeType;
    /**
     * 类型细分
     */
    private String level;
    /**
     * 统计项内容
     */
    private String content;
    /**
     * 内容类型
     */
    private String contentType;
    /**
     * 统计时间
     */
    private Date statisticalTime;
    /**
     * 所属
     */
    private String ownerAs;


    /**
     * 流程id
     */
    private Long processinstId;

    /**
     * 流程类型
     */
    private String busiAlias;
    /**
     * 单据状态  0：草稿 1：流程中 2：申请流程归档完成
     */
    private Integer billStatus;

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public String getGroupAlias() {
        return groupAlias;
    }

    public void setGroupAlias(String groupAlias) {
        this.groupAlias = groupAlias;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Long getBillid() {
        return billid;
    }

    public void setBillid(Long billid) {
        this.billid = billid;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public Date getStatisticalTime() {
        return statisticalTime;
    }

    public void setStatisticalTime(Date statisticalTime) {
        this.statisticalTime = statisticalTime;
    }

    public String getOwnerAs() {
        return ownerAs;
    }

    public void setOwnerAs(String ownerAs) {
        this.ownerAs = ownerAs;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("groupId", getGroupId())
                .append("groupAlias", getGroupAlias())
                .append("title", getTitle())
                .append("billid", getBillid())
                .append("nodeType", getNodeType())
                .append("level", getLevel())
                .append("content", getContent())
                .append("contentType", getContentType())
                .append("statisticalTime", getStatisticalTime())
                .append("ownerAs", getOwnerAs())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("delFlag", getDelFlag())
                .toString();
    }

    public Integer getNodeType() {
        return nodeType;
    }

    public void setNodeType(Integer nodeType) {
        this.nodeType = nodeType;
    }

    public Long getProcessinstId() {
        return processinstId;
    }

    public void setProcessinstId(Long processinstId) {
        this.processinstId = processinstId;
    }

    public String getBusiAlias() {
        return busiAlias;
    }

    public void setBusiAlias(String busiAlias) {
        this.busiAlias = busiAlias;
    }

    public Integer getBillStatus() {
        return billStatus;
    }

    public void setBillStatus(Integer billStatus) {
        this.billStatus = billStatus;
    }
}
