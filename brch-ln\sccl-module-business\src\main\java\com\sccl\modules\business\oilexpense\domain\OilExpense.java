package com.sccl.modules.business.oilexpense.domain;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 油机基础表 oil_expense
 *
 * <AUTHOR>
 * @date 2021-12-14
 */
@Getter
@Setter
public class OilExpense extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Date createtime;
    /**
     * 油机编号
     */
    private String oilEngineId;
    /**
     * 油机户名
     */
    private String oilEngineName;
    /**
     * 所属部门
     */
    private Long country;
    /**
     * 分公司
     */
    private Long company;
    /**
     * 分局支局
     */
    private String substation;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 型号
     */
    private String type;
    /**
     * 用油类别
     */
    private String oilType;
    /**
     * 总容量
     */
    private BigDecimal capacity;
    /**
     * 库存量
     */
    private BigDecimal stock;
    /**
     * 系统库存量
     */
    private BigDecimal sysstock;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 油机性质 0 固定 1 移动
     */
    private Integer quality;
    /**
     * 发电功率
     */
    private BigDecimal power;
    /**
     * 单位功率油耗
     */
    private BigDecimal unitOilCost;
    /**
     * 管理负责人
     */
    private String master;
    /**
     * 单据状态
     */
    private Integer billStatus;
    /**
     * 局站编码
     */
    private String stationCode;

    private Integer status;


    /**
     * 车牌号
     */
    private String carplate;

    private String summaruser;

    private Long procInstId;
    private Integer accountnum;
}
