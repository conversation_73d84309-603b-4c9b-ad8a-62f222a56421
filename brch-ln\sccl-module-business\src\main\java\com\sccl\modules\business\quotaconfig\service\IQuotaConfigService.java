package com.sccl.modules.business.quotaconfig.service;

import com.sccl.modules.business.alertcontrol.domain.AlertControl;
import com.sccl.modules.business.quotaconfig.domain.QuotaConfig;
import com.sccl.framework.service.IBaseService;

import java.util.List;

/**
 * 基站定额分公司设置 服务层
 * 
 * <AUTHOR>
 * @date 2023-06-07
 */
public interface IQuotaConfigService extends IBaseService<QuotaConfig>
{

    public String selectOrgAllChidren(QuotaConfig quotaConfig);
    public String selectCountryAllChidren(QuotaConfig quotaConfig);
    public List<QuotaConfig> selectListCustomized(QuotaConfig quotaConfig, String ids, String countryIds);
}
