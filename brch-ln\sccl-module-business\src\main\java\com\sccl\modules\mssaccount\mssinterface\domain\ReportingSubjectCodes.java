package com.sccl.modules.mssaccount.mssinterface.domain;

import com.sccl.modules.autojob.util.convert.StringUtils;

import java.util.HashMap;
import java.util.Map;

public class ReportingSubjectCodes {
    private static final Map<String, String> container = new HashMap<>();

    private static final String ERROR_SUBJECT_CODE = "0000";

    static {
        container.put("四川省本部", "1300");
        container.put("四川省公司本部", "1300");
        container.put("成都", "1301");
        container.put("自贡", "1303");
        container.put("攀枝花", "1304");
        container.put("泸州", "1305");
        container.put("德阳", "1306");
        container.put("绵阳", "1307");
        container.put("广元", "1308");
        container.put("遂宁", "1309");
        container.put("内江", "1310");
        container.put("乐山", "1311");
        container.put("南充", "1312");
        container.put("眉山", "1313");
        container.put("宜宾", "1314");
        container.put("广安", "1315");
        container.put("达州", "1316");
        container.put("雅安", "1317");
        container.put("巴中", "1318");
        container.put("资阳", "1319");
        container.put("阿坝", "1320");
        container.put("甘孜", "1321");
        container.put("凉山", "1322");
        container.put("辽宁省本部", "2600");
        container.put("辽宁省公司本部", "2600");
        container.put("沈阳", "2601");
        container.put("大连", "2602");
        container.put("鞍山", "2603");
        container.put("抚顺", "2604");
        container.put("本溪", "2605");
        container.put("丹东", "2606");
        container.put("锦州", "2607");
        container.put("营口", "2608");
        container.put("阜新", "2609");
        container.put("辽阳", "2610");
        container.put("铁岭", "2611");
        container.put("朝阳", "2612");
        container.put("盘锦", "2613");
        container.put("葫芦岛", "2614");
    }

    public static String getSubjectCode(String city) {
        if (StringUtils.isEmpty(city)) {
            return null;
        }
        if (city.endsWith("市")) {
            city = city.substring(0, city.indexOf("市"));
        }
        if (city.startsWith("四川")) {
            city = "四川省本部";
        }
        if (city.startsWith("辽宁")) {
            city = "辽宁省本部";
        }
        if (!container.containsKey(city)) {
            return ERROR_SUBJECT_CODE;
        }
        return container.get(city);
    }

}
