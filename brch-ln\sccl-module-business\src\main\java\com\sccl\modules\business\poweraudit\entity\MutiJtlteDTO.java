package com.sccl.modules.business.poweraudit.entity;

import com.sccl.framework.aspectj.lang.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: 芮永恒
 * @CreateTime: 2024-03-01  15:50
 * @Description: 电表与站址关系
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MutiJtlteDTO {

    /**
     * 地市
     */
    @Excel(name = "所属部门")
    private String city;

    /**
     * 区县
     */
//    @Excel(name = "区县公司")
    private String countyCompanies;

    /**
     * 运营分局
     */
    @Excel(name = "运营分局")
    private String operationsBranch;

    /**
     * 电表协议编号
     */
    @Excel(name = "电表户名/协议号码")
    private String ammeterid;

    /**
     * 台账期号
     */
    @Excel(name = "台账期号")
    private String accountNo;

    /**
     * 一表多站/一站多表
     */
    @Excel(name = "类型")
    private String errorType = "一站多表/多站多表";

    /***  台账单价 */
//    @Excel(name = "台账单价")
    private String meterPrice;

    /***  基础信息单价 */
//    @Excel(name = "基础信息单价")
    private String accountPrice;

    /**
     * 局站编码
     */
    @Excel(name = "集团站址编码")
    private String stationcode;

    /**
     * 铁塔站址编码
     */
    @Excel(name = "铁塔站址编码")
    private String towerSiteCode;


}
