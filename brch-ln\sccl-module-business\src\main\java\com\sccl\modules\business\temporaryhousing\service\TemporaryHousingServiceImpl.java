package com.sccl.modules.business.temporaryhousing.service;

import com.sccl.common.lang.StringUtils;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.business.temporaryhousing.domain.TemporaryHousing;
import com.sccl.modules.business.temporaryhousing.mapper.TemporaryHousingMapper;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * __房屋临时表__<br/>
 * 2019/10/31
 *
 * <AUTHOR>
 */
@Service
public class TemporaryHousingServiceImpl extends BaseServiceImpl<TemporaryHousing> implements ITemporaryHousingService {

    @Autowired
    TemporaryHousingMapper mapper;

    @Override
    public int deleteAll() {
        return mapper.deleteAll();
    }

    @Override
    public List<TemporaryHousing> importExcel(String sheetName, InputStream input,Integer type) throws Exception {
        List<TemporaryHousing> list = new ArrayList<>();

        Workbook workbook = WorkbookFactory.create(input);
        Sheet sheet = workbook.getSheet(sheetName);
        if (StringUtils.isNotEmpty(sheetName))
        {
            // 如果指定sheet名,则取指定sheet中的内容.
            sheet = workbook.getSheet(sheetName);
        }
        if (sheet == null)
        {
            // 如果传入的sheet名不存在则默认指向第1个sheet.
            sheet = workbook.getSheetAt(0);
        }
        int rows = sheet.getPhysicalNumberOfRows();

        if (rows > 0)
        {
            Row row0 = sheet.getRow(0);
            for (int i = 1; i < rows; i++){
                TemporaryHousing housing = new TemporaryHousing();
                housing.setType(type);
                Row rowo = sheet.getRow(0);
                Row row = sheet.getRow(i);
                int cellNum = sheet.getRow(0).getPhysicalNumberOfCells();
                for (int j = 0; j < cellNum; j++){
                    Cell cello = rowo.getCell(j);
                    Cell cell = row.getCell(j);
                    if (cell == null)
                    {
                        continue;
                    }else{
                        if("房屋编号".equals(cello.getStringCellValue())){
                            housing.setHousingcode(cell.getStringCellValue());
                        }
                        if("房屋名称".equals(cello.getStringCellValue())){
                            housing.setHousingname(cell.getStringCellValue());
                        }
                        if("所有权单位编号".equals(cello.getStringCellValue()) || "承租单位编码".equals(cello.getStringCellValue())){
                            housing.setOwnershipunitcode(cell.getStringCellValue());
                        }
                        if("所有权单位名称".equals(cello.getStringCellValue()) || "承租单位名称".equals(cello.getStringCellValue())){
                            housing.setOwnershipunitname(cell.getStringCellValue());
                        }
                        if("房屋座落详细地址".equals(cello.getStringCellValue())){
                            housing.setHouselocateddetailaddress(cell.getStringCellValue());
                        }
                        if("所有权单位属性".equals(cello.getStringCellValue()) || "所属帐套".equals(cello.getStringCellValue())){
                            housing.setOwnershipunitattribute(cell.getStringCellValue());
                        }
                        if("所在行政区划编码".equals(cello.getStringCellValue()) || "行政区划编码".equals(cello.getStringCellValue())){
                            housing.setAdministrativedivisioncode(cell.getStringCellValue());
                        }
                        if("所在行政区划".equals(cello.getStringCellValue()) || "行政区划名称".equals(cello.getStringCellValue())){
                            housing.setAdministrativedivision(cell.getStringCellValue());
                        }
                        if("结构".equals(cello.getStringCellValue()) || "建筑结构".equals(cello.getStringCellValue())){
                            housing.setStructure(cell.getStringCellValue());
                        }
                        if("总建筑面积".equals(cello.getStringCellValue())){
                            housing.setGrossfloorarea(cell.getStringCellValue());
                        }
                        if("办公面积".equals(cello.getStringCellValue())){
                            housing.setOfficespace(cell.getStringCellValue());
                        }
                        if("机房面积".equals(cello.getStringCellValue())){
                            housing.setRoomarea(cell.getStringCellValue());
                        }
                        if("出租面积".equals(cello.getStringCellValue())){
                            housing.setRentarea(cell.getStringCellValue());
                        }
                        if("营业厅面积".equals(cello.getStringCellValue())){
                            housing.setBusinesshallarea(cell.getStringCellValue());
                        }
                        if("闲置面积".equals(cello.getStringCellValue())){
                            housing.setSparearea(cell.getStringCellValue());
                        }
                    }
                }
                if(!StringUtils.isEmpty(housing.getHousingcode())
                        && !StringUtils.isEmpty(housing.getHousingname())
                        && !StringUtils.isEmpty(housing.getOwnershipunitcode())
                        && !StringUtils.isEmpty(housing.getOwnershipunitname())
                        && !StringUtils.isEmpty(housing.getHouselocateddetailaddress())){
                    list.add(housing);
                }
            }
        }

        return list;
    }

    @Override
    public int initHousingInfo() {
        return mapper.initHousingInfo();
    }

    @Override
    public Map<String,Object> importHousing() {
        Map<String,Object> map = new HashMap<>();
        map.put("updateNum",null);
        map.put("insertNum",null);
        mapper.importHousing(map);
        return map;
    }

    @Override
    public int deleteRepeat() {
        return mapper.deleteRepeat();
    }

    @Override
    public int insertHousingInfo() {
        return mapper.insertHousingInfo();
    }
}
