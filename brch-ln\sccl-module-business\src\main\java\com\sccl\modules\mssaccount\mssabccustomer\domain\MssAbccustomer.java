package com.sccl.modules.mssaccount.mssabccustomer.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;
import java.math.BigDecimal;


/**
 * 客户表 MSS_ABCCUSTOMER
 * 
 * <AUTHOR>
 * @date 2019-05-01
 */
public class MssAbccustomer extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
	private Long customerId;
    public Long getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Long customerId) {
		this.customerId = customerId;
	}

	/**  */
    private String infStatus;
    /**  */
    private Date infDate;
    /** 客户编号 */
    private String kunnr;
    /**  */
    private String ktokd;
    /** 名称1 */
    private String name1;
    /** 国家代码 */
    private String land1;
    /** 地址 */
    private String stras;
    /** 城市 */
    private String ort01;
    /** 邮政编码 */
    private String pstlz;
    /** 联系人 */
    private String remark2;
    /** 地区（省份编码） */
    private String region;
    /** 语言代码 */
    private String spras;
    /** 电话 */
    private String telf1;
    /** 传真 */
    private String teltx;
    /** 移动电话 */
    private String mobNumber;
    /** EMAIL */
    private String smtpAddr;
    /** 供应商 */
    private String lifnr;
    /** 贸易伙伴 */
    private String vbund;
    /** 纳税人识别号 */
    private String stceg;
    /** 行业 */
    private String brsch;
    /** 增值税纳税人 */
    private String vatpayor;
    /** 关联方类型 */
    private String isRelated;
    /** 虚拟客户 */
    private String remark4;


	public void setInfStatus(String infStatus)
	{
		this.infStatus = infStatus;
	}

	public String getInfStatus() 
	{
		return infStatus;
	}

	public void setInfDate(Date infDate)
	{
		this.infDate = infDate;
	}

	public Date getInfDate() 
	{
		return infDate;
	}

	public void setKunnr(String kunnr)
	{
		this.kunnr = kunnr;
	}

	public String getKunnr() 
	{
		return kunnr;
	}

	public void setKtokd(String ktokd)
	{
		this.ktokd = ktokd;
	}

	public String getKtokd() 
	{
		return ktokd;
	}

	public void setName1(String name1)
	{
		this.name1 = name1;
	}

	public String getName1() 
	{
		return name1;
	}

	public void setLand1(String land1)
	{
		this.land1 = land1;
	}

	public String getLand1() 
	{
		return land1;
	}

	public void setStras(String stras)
	{
		this.stras = stras;
	}

	public String getStras() 
	{
		return stras;
	}

	public void setOrt01(String ort01)
	{
		this.ort01 = ort01;
	}

	public String getOrt01() 
	{
		return ort01;
	}

	public void setPstlz(String pstlz)
	{
		this.pstlz = pstlz;
	}

	public String getPstlz() 
	{
		return pstlz;
	}

	public void setRemark2(String remark2)
	{
		this.remark2 = remark2;
	}

	public String getRemark2() 
	{
		return remark2;
	}

	public void setRegion(String region)
	{
		this.region = region;
	}

	public String getRegion() 
	{
		return region;
	}

	public void setSpras(String spras)
	{
		this.spras = spras;
	}

	public String getSpras() 
	{
		return spras;
	}

	public void setTelf1(String telf1)
	{
		this.telf1 = telf1;
	}

	public String getTelf1() 
	{
		return telf1;
	}

	public void setTeltx(String teltx)
	{
		this.teltx = teltx;
	}

	public String getTeltx() 
	{
		return teltx;
	}

	public void setMobNumber(String mobNumber)
	{
		this.mobNumber = mobNumber;
	}

	public String getMobNumber() 
	{
		return mobNumber;
	}

	public void setSmtpAddr(String smtpAddr)
	{
		this.smtpAddr = smtpAddr;
	}

	public String getSmtpAddr() 
	{
		return smtpAddr;
	}

	public void setLifnr(String lifnr)
	{
		this.lifnr = lifnr;
	}

	public String getLifnr() 
	{
		return lifnr;
	}

	public void setVbund(String vbund)
	{
		this.vbund = vbund;
	}

	public String getVbund() 
	{
		return vbund;
	}

	public void setStceg(String stceg)
	{
		this.stceg = stceg;
	}

	public String getStceg() 
	{
		return stceg;
	}

	public void setBrsch(String brsch)
	{
		this.brsch = brsch;
	}

	public String getBrsch() 
	{
		return brsch;
	}

	public void setVatpayor(String vatpayor)
	{
		this.vatpayor = vatpayor;
	}

	public String getVatpayor() 
	{
		return vatpayor;
	}

	public void setIsRelated(String isRelated)
	{
		this.isRelated = isRelated;
	}

	public String getIsRelated() 
	{
		return isRelated;
	}

	public void setRemark4(String remark4)
	{
		this.remark4 = remark4;
	}

	public String getRemark4() 
	{
		return remark4;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("customerId", getCustomerId())
            .append("infStatus", getInfStatus())
            .append("infDate", getInfDate())
            .append("kunnr", getKunnr())
            .append("ktokd", getKtokd())
            .append("name1", getName1())
            .append("land1", getLand1())
            .append("stras", getStras())
            .append("ort01", getOrt01())
            .append("pstlz", getPstlz())
            .append("remark2", getRemark2())
            .append("region", getRegion())
            .append("spras", getSpras())
            .append("telf1", getTelf1())
            .append("teltx", getTeltx())
            .append("mobNumber", getMobNumber())
            .append("smtpAddr", getSmtpAddr())
            .append("lifnr", getLifnr())
            .append("vbund", getVbund())
            .append("stceg", getStceg())
            .append("brsch", getBrsch())
            .append("vatpayor", getVatpayor())
            .append("isRelated", getIsRelated())
            .append("remark4", getRemark4())
            .toString();
    }
}
