package com.sccl.modules.business.syncresult.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.business.syncresult.domain.Syncresult;
import com.sccl.modules.business.syncresult.service.ISyncresultService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 同步结果 信息操作处理
 * 
 * <AUTHOR>
 * @date 2023-02-27
 */
@RestController
@RequestMapping("/business/syncresult")
public class SyncresultController extends BaseController
{
    private String prefix = "business/syncresult";
	
	@Autowired
	private ISyncresultService syncresultService;
	
	@RequiresPermissions("business:syncresult:view")
	@GetMapping()
	public String syncresult()
	{
	    return prefix + "/syncresult";
	}
	
	/**
	 * 查询同步结果列表
	 */
	@RequiresPermissions("business:syncresult:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(Syncresult syncresult)
	{
		startPage();
        List<Syncresult> list = syncresultService.selectList(syncresult);
		return getDataTable(list);
	}
	
	/**
	 * 新增同步结果
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存同步结果
	 */
	@RequiresPermissions("business:syncresult:add")
	@Log(title = "同步结果", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody Syncresult syncresult)
	{		
		return toAjax(syncresultService.insert(syncresult));
	}

	/**
	 * 修改同步结果
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		Syncresult syncresult = syncresultService.get(id);

		Object object = JSONObject.toJSON(syncresult);

        return this.success(object);
	}
	
	/**
	 * 修改保存同步结果
	 */
	@RequiresPermissions("business:syncresult:edit")
	@Log(title = "同步结果", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody Syncresult syncresult)
	{		
		return toAjax(syncresultService.update(syncresult));
	}
	
	/**
	 * 删除同步结果
	 */
	@RequiresPermissions("business:syncresult:remove")
	@Log(title = "同步结果", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(syncresultService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看同步结果
     */
    @RequiresPermissions("business:syncresult:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		Syncresult syncresult = syncresultService.get(id);

        Object object = JSONObject.toJSON(syncresult);

        return this.success(object);
    }

}
