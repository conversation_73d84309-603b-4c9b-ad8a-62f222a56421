package com.sccl.modules.business.equipmentdict.service;

import com.sccl.common.utils.ObjectStoreUtils;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.utils.ExcelUtil;
import com.sccl.framework.utils.FileUploadUtils;
import com.sccl.modules.autojob.util.convert.DateUtils;
import com.sccl.modules.business.equipmentdict.domain.EquipmentDict;
import com.sccl.modules.business.equipmentdict.mapper.EquipmentDictMapper;
import com.sccl.modules.system.attachments.domain.Attachments;
import com.sccl.modules.system.attachments.mapper.AttachmentsMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.InputStream;
import java.util.Collections;
import java.util.List;
import java.util.Map;


/**
 * 铁塔站址设备字典 服务层实现
 *
 * <AUTHOR>
 * @date 2022-08-09
 */
@Service
public class EquipmentDictServiceImpl extends BaseServiceImpl<EquipmentDict> implements IEquipmentDictService {
    @Autowired(required = false)
    private EquipmentDictMapper mapper;

    @Autowired(required = false)
    private AttachmentsMapper attachmentsMapper;

    @Override
    public EquipmentDict getLatestByGroupId(long groupId) {
        return mapper.getLatest(groupId);
    }

    @Override
    public List<EquipmentDict> getAllLatestByGroupId(List<Long> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return Collections.emptyList();
        }
        return mapper.getLatests(groupIds);
    }

    @Override
    public List<EquipmentDict> listLatest() {
        return mapper.listAll();
    }

    @Override
    public List<EquipmentDict> listLatestConditional(EquipmentDict equipmentDict) {
        return mapper.listAllConditional(equipmentDict);
    }

    @Override
    public List<EquipmentDict> listHistoryByGroupId(long groupId) {
        return mapper.listHistory(groupId);
    }

    @Override
    public int deleteByGroupId(long groupId) {
        return mapper.deleteByGroupId(groupId);
    }

    @Override
    public Attachments exportExcel(List<EquipmentDict> equipmentDictList, Map<String, String> columnMap, Map<String, String> promptMap) {
        ExcelUtil<EquipmentDict> excelUtil = new ExcelUtil<>(EquipmentDict.class);
        String fileName = "设备模型表" + DateUtils.getTime().replace(" ", "_");
        try {
            InputStream inputStream = excelUtil.exportExcel(equipmentDictList, columnMap, promptMap,"设备模型表");
            Attachments attachments = new Attachments();
            String filedIdName = FileUploadUtils.encodingFilename(fileName, ".xls");
            String filedId = filedIdName.substring(0, filedIdName.lastIndexOf("."));
            attachments.setFileName(fileName);
            attachments.setMongodbFileId(filedId);
            attachments.setBusiAlias("附件(设备模型导出)");
            attachments.setCategoryCode("file");
            attachments.setYear(Integer.valueOf(com.sccl.common.utils.DateUtils.getYear()));
            attachments.setDelFlag("0");
            attachments.setCollection("附件(设备模型导出)");
            attachments.setBusiId(ShiroUtils.getUserId());
            if (ObjectStoreUtils.upload2OSS(filedId + ".xls", inputStream) && attachmentsMapper.insert(attachments) == 1) {
                String url = ObjectStoreUtils.shareFileByKey(filedId + ".xls");
                attachments.setUploadUrl(url);
                return attachments;
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
