package com.sccl.modules.business.stationequipment.domain;

import lombok.Data;
import org.apache.ibatis.type.Alias;

import java.text.DecimalFormat;

/**
 * 大数据基站异常
 */
@Data
@Alias("PowerAPPDbycPro")
public class PowerAPPDbyc {
    private Long id;
    private String ammeterid;
    private String protocolname;
    private String company;
    private String companyName;
    private String country;
    private String countryName;
    private String stationcode;
    private String stationname;
    private String accountno;
    /**
     * 上行业务信息实际平均占用prb资源个数
     */
   private String  sum_sx_prb;
    /**
     * 上行业务信息实际平均占用prb资源个数(qci1)
     */
   private String  sum_sx_prb_qci1;
    /**
     * 下行业务信息实际平均占用prb资源个数
     */
   private String  sum_xx_prb;
    /**
     * 下行业务信息实际平均占用prb资源个数(qci1)
     */
   private String  sum_xx_prb_qci;


    public static String round(String originalString, int decimalPlaces) {
        double originalNumber = Double.parseDouble(originalString);
        String pattern = getDecimalPattern(decimalPlaces);
        DecimalFormat decimalFormat = new DecimalFormat(pattern);
        return decimalFormat.format(originalNumber);
    }

    private static String getDecimalPattern(int decimalPlaces) {
        StringBuilder pattern = new StringBuilder("#");
        if (decimalPlaces > 0) {
            pattern.append(".");
            for (int i = 0; i < decimalPlaces; i++) {
                pattern.append("#");
            }
        }
        return pattern.toString();
    }

}

