package com.sccl.modules.business.order.controller;

import java.util.List;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.sccl.common.support.Convert;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.order.domain.Order;
import com.sccl.modules.business.order.service.IOrderService;

/**
 * 订单 信息操作处理
 * 
 * <AUTHOR>
 * @date 2019-03-05
 */
@RestController
@RequestMapping("/business/order")
public class OrderController extends BaseController
{
    private String prefix = "business/order";
	
	//@Autowired
	private IOrderService orderService;
	
	@RequiresPermissions("business:order:view")
	@GetMapping()
	public String order()
	{
	    return prefix + "/order";
	}
	
	/**
	 * 查询订单列表
	 */
	@RequiresPermissions("business:order:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(Order order)
	{
		startPage();
        List<Order> list = orderService.selectList(order);
		return getDataTable(list);
	}
	
	/**
	 * 新增订单
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存订单
	 */
	@RequiresPermissions("business:order:add")
	//@Log(title = "订单", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody Order order)
	{		
		return toAjax(orderService.insert(order));
	}

	/**
	 * 修改订单
	 */
	@GetMapping("/edit/{id}")
	public Order edit(@PathVariable("id") Long id)
	{
	    return orderService.get(id);
	}
	
	/**
	 * 修改保存订单
	 */
	@RequiresPermissions("business:order:edit")
	//@Log(title = "订单", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody Order order)
	{		
		return toAjax(orderService.update(order));
	}
	
	/**
	 * 删除订单
	 */
	@RequiresPermissions("business:order:remove")
	//@Log(title = "订单", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(orderService.deleteByIds(Convert.toStrArray(ids)));
	}

	/** 
	* mq 消费者回调
	* @param msg 消费对象
	*/
/*	@PostMapping("/callBackMQ")
	public void callBackMQ(@RequestBody RabbitmqMessage msg) throws Exception{
		System.out.println("-----------------lt:"+msg.getPayload().toString());
//		esConsume.callBack(msg.getPayload());
	}*/



}
