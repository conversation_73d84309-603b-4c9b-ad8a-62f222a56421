package com.sccl.modules.business.modelegetprice.service;

import com.sccl.modules.business.modelegetprice.domain.ModeleGetprice;
import com.sccl.framework.service.IBaseService;

import java.util.List;

/**
 * 来自官网的单价数据 服务层
 * 
 * <AUTHOR>
 * @date 2023-03-03
 */
public interface IModeleGetpriceService extends IBaseService<ModeleGetprice>
{


    ModeleGetprice selectByLatest(ModeleGetprice modeleGetprice);

    int updatebitch(List<ModeleGetprice> modeleGetprices);
}
