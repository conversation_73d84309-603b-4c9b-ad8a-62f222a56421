package com.sccl.modules.business.stationreportwhitelist.vo;

import com.sccl.modules.business.stationreportwhitelist.domain.PowerStationInfo;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

/**
 * <AUTHOR>
 * @date 2024/5/9 17:43
 * @describe 局站信息
 */
@Getter
@Setter
public class PowerStationInfoVO extends PowerStationInfo {

    /**
     * 分公司名称
     */
    private String companyName;

    /**
     * 部门名称
     */
    private String countryName;

    /**
     * 局站类型名称
     */
    private String stationtypeName;

    /**
     * 站点电表总数
     */
    private Integer meterTotel;

    /**
     * 对外结算类型名称
     */
    private String directsupplyflagName;

    /**
     * 产权名称
     */
    private String propertyrightName;

    @Override
    public String toString() {
    	return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }
}
