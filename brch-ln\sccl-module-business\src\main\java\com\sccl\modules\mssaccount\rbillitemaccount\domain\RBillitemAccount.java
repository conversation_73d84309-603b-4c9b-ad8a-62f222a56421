package com.sccl.modules.mssaccount.rbillitemaccount.domain;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;


/**
 * 报账明细 台账 关联表 mss_r_billitem_account
 *
 * <AUTHOR>
 * @date 2019-06-01
 */
@AllArgsConstructor
@NoArgsConstructor
public class RBillitemAccount extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 报账单id
     */
    private Long billId;
    /**
     * 报账明细id
     */
    private Long billitemId;
    /**
     * 台账id
     */
    private Long accountId;

    public String getAmmeteruse() {
        return ammeteruse;
    }

    public void setAmmeteruse(String ammeteruse) {
        this.ammeteruse = ammeteruse;
    }

    /**
     * 台账对应电表用途
     */
    private String ammeteruse;

    /**
     * 台账金额
     */
    private Double accountMoney;
    /**
     * 关联金额
     */
    private Double money;
    /**
     * 关联税额
     */
    private Double taxmoney;
    /**
     * 是否全部归属报账明细 默认是, 1 是 0 否
     */
    private Long ifall;


    public Long getBillId() {
        return billId;
    }

    public void setBillId(Long billId) {
        this.billId = billId;
    }

    public void setBillitemId(Long billitemId) {
        this.billitemId = billitemId;
    }

    public Long getBillitemId() {
        return billitemId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountMoney(Double accountMoney) {
        this.accountMoney = accountMoney;
    }

    public Double getAccountMoney() {
        return accountMoney;
    }

    public void setMoney(Double money) {
        this.money = money;
    }

    public Double getMoney() {
        return money;
    }

    public void setIfall(Long ifall) {
        this.ifall = ifall;
    }

    public Long getIfall() {
        return ifall;
    }

    public Double getTaxmoney() {
        return taxmoney;
    }

    public void setTaxmoney(Double taxmoney) {
        this.taxmoney = taxmoney;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("billitemId", getBillitemId())
                .append("accountId", getAccountId())
                .append("accountMoney", getAccountMoney())
                .append("money", getMoney())
                .append("taxmoney", getTaxmoney())
                .append("ifall", getIfall())
                .toString();
    }
}
