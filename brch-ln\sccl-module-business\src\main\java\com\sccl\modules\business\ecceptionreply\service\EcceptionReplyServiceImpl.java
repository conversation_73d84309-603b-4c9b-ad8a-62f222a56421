package com.sccl.modules.business.ecceptionreply.service;

import com.enrising.dcarbon.id.IdGenerator;
import com.sccl.common.lang.StringUtils;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.modules.business.ecceptiondetail.domain.EcceptionDetail;
import com.sccl.modules.business.ecceptiondetail.mapper.EcceptionDetailMapper;
import com.sccl.modules.business.ecceptionprocess.mapper.EcceptionProcessMapper;
import com.sccl.modules.business.ecceptionreply.domain.EcceptionReply;
import com.sccl.modules.business.ecceptionreply.mapper.EcceptionReplyMapper;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.uniflow.common.WFModel;
import com.sccl.timing.finder.util.QueryParamsBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;


/**
 * 公共异常回复 服务层实现
 *
 * <AUTHOR>
 * @date 2023-03-27
 */
@Service
@Slf4j
public class EcceptionReplyServiceImpl extends BaseServiceImpl<EcceptionReply> implements IEcceptionReplyService {
    @Autowired(required = false)
    private EcceptionProcessMapper processMapper;

    @Autowired(required = false)
    private EcceptionDetailMapper detailMapper;

    @Autowired(required = false)
    private EcceptionReplyMapper replyMapper;
    /*=================异常回复参数key=================>*/
    /**
     * 异常主表ID
     */
    private static final String EXCEPTION_ID = "exceptionCommonId";
    /**
     * 异常来源
     */
    private static final String EXCEPTION_SOURCE = "exceptionSource";
    /**
     * 审批意见
     */
    private static final String ADVICE = "advice";
    /**
     * 回复选项
     */
    private static final String REPLY_OPTION = "replyOption";

    /*=======================Finished======================<*/
    public void uniflowCallBack(WFModel wfModel) {
        if (wfModel == null || wfModel.getVariables() == null || wfModel.getBusiId() == null) {
            throw new RuntimeException("异常流程处理发生异常，回调参数为空");
        }
        Map<String, Object> params = wfModel.getVariables();
        //流程开始（发起流程）
        if (StringUtils.isNotEmpty(wfModel.getCallbackType()) && "PROCESS_STARTED".equals(wfModel.getCallbackType())) {
            Long detailID = Long.parseLong(wfModel.getBusiId());
            EcceptionDetail param = new EcceptionDetail();
            param.setId(detailID);
            param.setProcessinstid(wfModel.getProcInstId() + "");
            param.setBillstatus("1");
            param.setBusialias(wfModel.getBusiAlias());
            detailMapper.updateByPrimaryKey(param);
            log.info("异常处理流程发起");
        }
        //流程中
        else if ("MID_NODE".equals(wfModel.getCallbackType())) {
            EcceptionReply reply = buildNewOne(wfModel);
            mapper.insert(reply);
            log.info("{}回复异常流程：{}", wfModel.getApplyUserId(), wfModel
                    .getWfTask()
                    .getNodeId());
        }
        //流程完成（流程走完了）
        else if ("PROCESS_COMPLETED".equals(wfModel.getCallbackType())) {
            if (params.containsKey(ADVICE)) {
                EcceptionReply reply = buildNewOne(wfModel);
                mapper.insert(reply);
            }
            EcceptionDetail param = new EcceptionDetail();
            param.setId(Long.parseLong(wfModel.getBusiId()));
            param.setBillstatus("2");
            detailMapper.updateByPrimaryKey(param);
            log.info("异常流程已完成");
        }
        //流程终止（删除该流程）
        else if ("PROCESS_CANCELLED".equals(wfModel.getCallbackType())) {
            //删除流程主表信息
            processMapper.deleteByPrimaryKey(QueryParamsBuilder
                    .newInstance()
                    .addParams("id", params.get(EXCEPTION_ID))
                    .getParams());
            //删除流程回复
            replyMapper.deleteByExceptionCommonID(Collections.singletonList((String) params.get(EXCEPTION_ID)));
            //删除异常明细
            detailMapper.stopProcess(Collections.singletonList(Long.parseLong(wfModel.getBusiAlias())));
            log.info("异常流程已终止");
        }
    }

    private static EcceptionReply buildNewOne(WFModel wfModel) {
        Map<String, Object> params = wfModel.getVariables();
        EcceptionReply reply = new EcceptionReply();
        reply.setId(IdGenerator.getNextIdAsLong());
        reply.setExceptionCommonId((String) params.get(EXCEPTION_ID));
        reply.setProcessid(wfModel.getProcInstId() + "");
        reply.setExceptionid(wfModel.getBusiId());
        reply.setExceptionsource((String) params.get(EXCEPTION_SOURCE));
        reply.setTaskid(wfModel.getProcTaskId() + "");
        reply.setUserid(wfModel.getApplyUserId() + "");
        reply.setReplyMsg((String) params.get(ADVICE));
        reply.setReplyOption((String) params.get(REPLY_OPTION));
        User applyUser = ShiroUtils.getUser();
        if (applyUser != null) {
            List<IdNameVO> companies = applyUser.getCompanies();
            if (companies != null && companies.size() > 0) reply.setCompany(companies
                    .get(0)
                    .getId());
            List<IdNameVO> departments = applyUser.getDepartments();
            if (departments != null && departments.size() > 0) reply.setCountry(departments
                    .get(0)
                    .getId());

        }
        return reply;
    }

    @Override
    public int updateHistoryForModel(EcceptionReply delParm) {
        return replyMapper.updateHistoryForModel(delParm);
    }
}
