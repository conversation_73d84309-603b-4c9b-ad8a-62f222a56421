package com.sccl.modules.business.examine.mapper;

import com.sccl.modules.business.examine.vo.BudgetExamineResultVo;
import com.sccl.modules.business.examine.vo.BudgetExamineSearchVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 预算考核
 *
 * <AUTHOR>
 * @date 2025-02-12
 */
@Mapper
public interface BudgetExamineMapper
{
    /**
     * 一览查询
     * @param searchVo
     * @return
     */
    List<BudgetExamineResultVo> list(BudgetExamineSearchVo searchVo);
}