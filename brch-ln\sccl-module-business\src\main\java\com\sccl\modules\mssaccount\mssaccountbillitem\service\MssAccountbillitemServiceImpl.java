package com.sccl.modules.mssaccount.mssaccountbillitem.service;

import com.sccl.common.lang.StringUtils;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.mssaccount.mssaccountbillitem.domain.MssAccountbillitem;

import com.sccl.modules.mssaccount.rbillitemaccount.domain.RBillitemAccount;
import com.sccl.modules.mssaccount.rbillitemaccount.mapper.RBillitemAccountMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 报账明细 服务层实现
 *
 * <AUTHOR>
 * @date 2019-04-24
 */
@Service
public class MssAccountbillitemServiceImpl extends BaseServiceImpl<MssAccountbillitem> implements IMssAccountbillitemService {
    @Autowired
    RBillitemAccountMapper rBillitemAccountMapper;

    public int deleteDBAndRAccount(Long id) {
        rBillitemAccountMapper.deleteRbillitemAccountByItemId(id);
        return deleteDB(id);
    }

}
