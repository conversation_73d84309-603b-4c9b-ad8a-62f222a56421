package com.sccl.modules.business.towersharerelate.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;


/**
 * 电费实际共享关系表 towersharerelate
 * 
 * <AUTHOR>
 * @date 2021-08-14
 */
public class Towersharerelate extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
    /** 铁塔唯一id */
    private String towerid;
    /** 导入时间 */
    private String uptime;
    /** 铁塔地市:眉山市 */
    private String city;
    /** 铁塔区县:东坡区 */
    private String district;
    /** 铁塔站址编码:511402908000000012 */
    private String towersitecode;
    /** 铁塔站址名称:洞子口R南林花园-MS314-MS-C-B */
    private String towersitename;
    /** 站址经度 */
    private String longitude;
    /** 站址纬度 */
    private String latitude;
    /** 移动实际结算分摊比例 */
    private String cmsharescale;
    /** 电信实际结算分摊比例 */
    private String ctsharescale;
    /** 联通实际结算分摊比例 */
    private String cusharescale;
    /** 拓展实际结算分摊比例 */
    private String exsharescale;
    /** 能源实际结算分摊比例 */
    private String ensharescale;
    /** 微站实际结算分摊比例 */
    private String microsharescale;
    /** 是否验收 */
    private String isaccept;
    /** 验收附件 */
    private String acceptattach;
    /** 验收时间 */
    private String acceptdate;
    /**  */
    private String status;
    /** 电信对应单位 */
    private Long company;
    /** 电信对应部门 */
    private Long country;


	public void setTowerid(String towerid)
	{
		this.towerid = towerid;
	}

	public String getTowerid() 
	{
		return towerid;
	}

	public void setUptime(String uptime)
	{
		this.uptime = uptime;
	}

	public String getUptime() 
	{
		return uptime;
	}

	public void setCity(String city)
	{
		this.city = city;
	}

	public String getCity() 
	{
		return city;
	}

	public void setDistrict(String district)
	{
		this.district = district;
	}

	public String getDistrict() 
	{
		return district;
	}

	public void setTowersitecode(String towersitecode)
	{
		this.towersitecode = towersitecode;
	}

	public String getTowersitecode() 
	{
		return towersitecode;
	}

	public void setTowersitename(String towersitename)
	{
		this.towersitename = towersitename;
	}

	public String getTowersitename() 
	{
		return towersitename;
	}

	public void setLongitude(String longitude)
	{
		this.longitude = longitude;
	}

	public String getLongitude() 
	{
		return longitude;
	}

	public void setLatitude(String latitude)
	{
		this.latitude = latitude;
	}

	public String getLatitude() 
	{
		return latitude;
	}

	public void setCmsharescale(String cmsharescale)
	{
		this.cmsharescale = cmsharescale;
	}

	public String getCmsharescale() 
	{
		return cmsharescale;
	}

	public void setCtsharescale(String ctsharescale)
	{
		this.ctsharescale = ctsharescale;
	}

	public String getCtsharescale() 
	{
		return ctsharescale;
	}

	public void setCusharescale(String cusharescale)
	{
		this.cusharescale = cusharescale;
	}

	public String getCusharescale() 
	{
		return cusharescale;
	}

	public void setExsharescale(String exsharescale)
	{
		this.exsharescale = exsharescale;
	}

	public String getExsharescale() 
	{
		return exsharescale;
	}

	public void setEnsharescale(String ensharescale)
	{
		this.ensharescale = ensharescale;
	}

	public String getEnsharescale() 
	{
		return ensharescale;
	}

	public void setMicrosharescale(String microsharescale)
	{
		this.microsharescale = microsharescale;
	}

	public String getMicrosharescale() 
	{
		return microsharescale;
	}

	public void setIsaccept(String isaccept)
	{
		this.isaccept = isaccept;
	}

	public String getIsaccept() 
	{
		return isaccept;
	}

	public void setAcceptattach(String acceptattach)
	{
		this.acceptattach = acceptattach;
	}

	public String getAcceptattach() 
	{
		return acceptattach;
	}

	public void setAcceptdate(String acceptdate)
	{
		this.acceptdate = acceptdate;
	}

	public String getAcceptdate() 
	{
		return acceptdate;
	}

	public void setStatus(String status)
	{
		this.status = status;
	}

	public String getStatus() 
	{
		return status;
	}


	public void setCompany(Long company)
	{
		this.company = company;
	}

	public Long getCompany() 
	{
		return company;
	}

	public void setCountry(Long country)
	{
		this.country = country;
	}

	public Long getCountry() 
	{
		return country;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("towerid", getTowerid())
            .append("uptime", getUptime())
            .append("city", getCity())
            .append("district", getDistrict())
            .append("towersitecode", getTowersitecode())
            .append("towersitename", getTowersitename())
            .append("longitude", getLongitude())
            .append("latitude", getLatitude())
            .append("cmsharescale", getCmsharescale())
            .append("ctsharescale", getCtsharescale())
            .append("cusharescale", getCusharescale())
            .append("exsharescale", getExsharescale())
            .append("ensharescale", getEnsharescale())
            .append("microsharescale", getMicrosharescale())
            .append("isaccept", getIsaccept())
            .append("acceptattach", getAcceptattach())
            .append("acceptdate", getAcceptdate())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .append("company", getCompany())
            .append("country", getCountry())
            .toString();
    }
}
