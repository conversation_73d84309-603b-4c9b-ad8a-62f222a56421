package com.sccl.modules.rental.rentalsupplier.service;

import com.sccl.modules.rental.rentalsupplier.domain.Rentalsupplier;
import com.sccl.framework.service.IBaseService;

import java.util.List;

/**
 * 供应商 服务层
 * 
 * <AUTHOR>
 * @date 2019-08-23
 */
public interface IRentalsupplierService extends IBaseService<Rentalsupplier>
{

    /**
     * @Description: 批量新增供应商
     * @author: dongk
     * @date: 2019/8/26
     * @param:
     * @return:
     */
    int batchInsert(List<Rentalsupplier> list);

}
