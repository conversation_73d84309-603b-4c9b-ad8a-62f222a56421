package com.sccl.modules.business.stationreportwhitelist.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/5/6 10:08
 * @describe 白名单流程单据
 */
@Getter
@Setter
@TableName("station_report_whitelist_bill")
public class StationReportWhitelistBill extends Model<StationReportWhitelistBill> {

    /**
     * 白名单流程单据id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 局(站)编码
     */
    private String stationcode;

    /**
     * 流程单据状态
     */
    private Integer billStatus;

    /**
     * 流程实例ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long procInstId;

    /**
     * 申请理由
     */
    private String applyArgument;

    /**
     * 附件
     */
    private String fj;

    /**
     * 白名单类型
     */
    private String whitelistType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 附件业务id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long fileBusiId;
}
