package com.sccl.modules.business.timing.api;

import com.enrising.dcarbon.bean.SpringUtil;
import com.enrising.dcarbon.collect.CollectionUtil;
import com.enrising.dcarbon.redis.RedisUtil;
import com.sccl.modules.business.ammeterorprotocol.mapper.AmmeterorprotocolMapper;
import com.sccl.modules.business.timing.dto.AmmeterProtocolRecord;
import com.sccl.modules.business.timing.dto.AmmeterProtocolRecordCacheData;
import com.sccl.timing.finder.framework.TimeSeriesDataFinder;
import com.sccl.timing.finder.util.ProtoStuffUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-06-21 10:17
 * @email <EMAIL>
 */
@Slf4j
public class AmmeterProtocolRecordAPI {
    public static final TimeSeriesDataFinder<AmmeterProtocolRecord> finder = new TimeSeriesDataFinder<>("protocol");

    /**
     * 查询指定电表最近的一条记录
     *
     * @param ammeterID 电表ID
     * @param timestamp 小于这个时间的最大的一条记录
     * @return com.sccl.modules.business.timing.dto.AmmeterProtocolRecordCacheData
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/6/21 11:20
     */
    public static AmmeterProtocolRecordCacheData getLatest(String ammeterID, long timestamp) {
        List<AmmeterProtocolRecordCacheData> dataList = finder.range(ammeterID, finder
                .getConfig()
                .getDefaultStartOffset(), timestamp, true, AmmeterProtocolRecordCacheData.class);
        if (dataList == null || dataList.size() == 0) {
            return null;
        }
        for (int i = dataList.size() - 1; i >= 0; i--) {
            if (dataList.get(i) != null) {
                return dataList.get(i);
            }
        }
        return null;
    }

    @SuppressWarnings("unchecked")
    public static Map<String, AmmeterProtocolRecordCacheData> getLatests(List<String> ammeterIDs, long timestamp) {
        Map<String, AmmeterProtocolRecordCacheData> r = new HashMap<>();

        CollectionUtil.batchedCollection(ammeterIDs, 1000, keys -> {
            List<Object> res = RedisUtil
                    .getRedisTemplate()
                    .executePipelined((RedisCallback<byte[]>) connection -> {
                        for (String key : keys) {
                            connection
                                    .listCommands()
                                    .lRange(defaultRawKey(finder.formatKey(key)), finder.offset(finder
                                            .getConfig()
                                            .getDefaultStartOffset()), finder.offset(timestamp));
                        }
                        return null;
                    }, new RedisSerializer<Object>() {
                        @Override
                        public byte[] serialize(Object o) throws SerializationException {
                            return new byte[0];
                        }

                        @Override
                        public Object deserialize(byte[] bytes) throws SerializationException {
                            try {
                                return ProtoStuffUtil.deserialize(bytes, AmmeterProtocolRecordCacheData.class);
                            } catch (Exception ignored) {
                            }
                            return new AmmeterProtocolRecordCacheData(-1L, -1, null);
                        }
                    });
            if (keys.size() != res.size()) {
                throw new RuntimeException("桶数目和值数目不匹配");
            }
            for (int i = 0; i < res.size(); i++) {
                if (res.get(i) != null && res.get(i) instanceof List) {
                    List<AmmeterProtocolRecordCacheData> dataList = (List<AmmeterProtocolRecordCacheData>) res.get(i);
                    for (int j = dataList.size() - 1; j >= 0; j--) {
                        if (dataList.get(j) != null) {
                            r.put(keys.get(i), dataList.get(j));
                            break;
                        }
                    }
                }
            }
        });
        List<String> miss = ammeterIDs
                .stream()
                .filter(item -> !r.containsKey(item))
                .collect(Collectors.toList());
        for (Map.Entry<String, AmmeterProtocolRecordCacheData> entry : r.entrySet()) {
            if ("-1".equalsIgnoreCase(entry
                    .getValue()
                    .getElectrotype())) {
                miss.add(entry.getKey());
            }
        }
        log.info("存在{}个电表没有在缓存找到记录", miss.size());
        if (miss.size() == 0) {
            return r;
        }
        Optional<AmmeterorprotocolMapper> mapperOptional = SpringUtil.getBeanOptional(AmmeterorprotocolMapper.class);
        mapperOptional.ifPresent(mapper -> {
            List<ElectricityEntry> electrotype = mapper.selectElectricityType(miss);
            log.info("成功从DB加载{}条记录", electrotype.size());
            electrotype.forEach(item -> {
                r.put(item.getId() + "", new AmmeterProtocolRecordCacheData(item.getElectrotype(), item.getDirectsupplyflag(), item.getCreateTime()));
            });
        });

        return r;
    }

    @SuppressWarnings("unchecked")
    public static Map<String, List<AmmeterProtocolRecordCacheData>> getAll(List<String> ammeterIDs, long start, long end) {
        Map<String, List<AmmeterProtocolRecordCacheData>> r = new HashMap<>();
        CollectionUtil.batchedCollection(ammeterIDs, 1000, keys -> {
            List<Object> res = RedisUtil
                    .getRedisTemplate()
                    .executePipelined((RedisCallback<byte[]>) connection -> {
                        for (String key : keys) {
                            connection
                                    .listCommands()
                                    .lRange(defaultRawKey(finder.formatKey(key)), finder.offset(start), finder.offset(end));
                        }
                        return null;
                    }, new RedisSerializer<Object>() {
                        @Override
                        public byte[] serialize(Object o) throws SerializationException {
                            return new byte[0];
                        }

                        @Override
                        public Object deserialize(byte[] bytes) throws SerializationException {
                            try {
                                return ProtoStuffUtil.deserialize(bytes, AmmeterProtocolRecordCacheData.class);
                            } catch (Exception ignored) {
                            }
                            return new AmmeterProtocolRecordCacheData(-1L, -1, null);
                        }
                    });
            if (keys.size() != res.size()) {
                throw new RuntimeException("桶数目和值数目不匹配");
            }
            for (int i = 0; i < res.size(); i++) {
                if (res.get(i) != null && res.get(i) instanceof List) {
                    r.put(keys.get(i), (List<AmmeterProtocolRecordCacheData>) res.get(i));
                }
            }
        });
        return r;
    }


    @SuppressWarnings("unchecked")
    private static byte[] defaultRawKey(String key) {
        return Objects.requireNonNull(((RedisSerializer<String>) RedisUtil
                .getRedisTemplate()
                .getKeySerializer()).serialize(key));
    }
}
