package com.sccl.modules.business.examine.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 预算考核 结果列表
 */
@Data
public class BudgetExamineResultVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 所属分公司
     */
    private String companyName;

    /**
     * 使用预算-1月
     */
    private String used1;

    /**
     * 使用预算-2月
     */
    private String used2;

    /**
     * 使用预算-3月
     */
    private String used3;

    /**
     * 使用预算-4月
     */
    private String used4;

    /**
     * 使用预算-5月
     */
    private String used5;

    /**
     * 使用预算-6月
     */
    private String used6;

    /**
     * 使用预算-7月
     */
    private String used7;

    /**
     * 使用预算-8月
     */
    private String used8;

    /**
     * 使用预算-9月
     */
    private String used9;

    /**
     * 使用预算-10月
     */
    private String used10;

    /**
     * 使用预算-11月
     */
    private String used11;

    /**
     * 使用预算-12月
     */
    private String used12;

    /**
     * 使用预算-12月(去年)
     */
    private String usedPre12;

    /**
     * 累计使用预算
     */
    private String usedTotal;

    /**
     * 同比增幅
     */
    private String com;

    /**
     * 年度预算
     */
    private String budgetAmount;

    /**
     * 预算使用比增幅
     */
    private String useRate;

    /**
     * 月度得分
     */
    private String monthScore;

    /**
     * 年度得分
     */
    private String yearScore;
}
