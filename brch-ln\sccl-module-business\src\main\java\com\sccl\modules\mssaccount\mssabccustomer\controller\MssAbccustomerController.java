package com.sccl.modules.mssaccount.mssabccustomer.controller;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.sccl.modules.business.mssaccountprepaid.domain.MssAccountPrepaid;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.mssaccount.mssabccustomer.domain.MssAbccustomer;
import com.sccl.modules.mssaccount.mssabccustomer.service.IMssAbccustomerService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 客户 信息操作处理
 * 
 * <AUTHOR>
 * @date 2019-05-01
 */
@RestController
@RequestMapping("/mssaccount/mssAbccustomer")
public class MssAbccustomerController extends BaseController
{
    private String prefix = "mssaccount/mssAbccustomer";
	
	@Autowired
	private IMssAbccustomerService mssAbccustomerService;
	
	@RequiresPermissions("mssaccount:mssAbccustomer:view")
	@GetMapping()
	public String mssAbccustomer()
	{
	    return prefix + "/mssAbccustomer";
	}
	
	/**
	 * 查询客户列表
	 */
	@RequiresPermissions("mssaccount:mssAbccustomer:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(MssAbccustomer mssAbccustomer)
	{
		mssAbccustomer.setInfStatus("1");
		startPage();
        List<MssAbccustomer> list = mssAbccustomerService.selectByLike(mssAbccustomer);
		return getDataTable(list);
	}
	
	/**
	 * 新增客户
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存客户
	 */
	@RequiresPermissions("mssaccount:mssAbccustomer:add")
	//@Log(title = "客户", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody MssAbccustomer mssAbccustomer)
	{		
		return toAjax(mssAbccustomerService.insert(mssAbccustomer));
	}

	/**
	 * 修改客户
	 */
	@GetMapping("/edit/{customerId}")
	public AjaxResult edit(@PathVariable("customerId") Long customerId)
	{
		MssAbccustomer mssAbccustomer = mssAbccustomerService.get(customerId);

		Object object = JSONObject.toJSON(mssAbccustomer);

        return this.success(object);
	}
	
	/**
	 * 修改保存客户
	 */
	@RequiresPermissions("mssaccount:mssAbccustomer:edit")
	//@Log(title = "客户", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody MssAbccustomer mssAbccustomer)
	{		
		return toAjax(mssAbccustomerService.update(mssAbccustomer));
	}
	
	/**
	 * 删除客户
	 */
	@RequiresPermissions("mssaccount:mssAbccustomer:remove")
	//@Log(title = "客户", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(mssAbccustomerService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看客户
     */
    @RequiresPermissions("mssaccount:mssAbccustomer:view")
    @GetMapping("/view/{customerId}")
    @ResponseBody
    public AjaxResult view(@PathVariable("customerId") Long customerId)
    {
		MssAbccustomer mssAbccustomer = mssAbccustomerService.get(customerId);

        Object object = JSONObject.toJSON(mssAbccustomer);

        return this.success(object);
    }



}
