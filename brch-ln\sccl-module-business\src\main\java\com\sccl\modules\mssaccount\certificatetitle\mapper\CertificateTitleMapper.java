package com.sccl.modules.mssaccount.certificatetitle.mapper;

import com.sccl.modules.mssaccount.certificatetitle.domain.CertificateTitle;
import com.sccl.framework.mapper.BaseMapper;

import java.util.List;

/**
 * view_certificate_title 数据层
 * 
 * <AUTHOR>
 * @date 2019-09-20
 */
public interface CertificateTitleMapper extends BaseMapper<CertificateTitle>
{


    List<CertificateTitle> selectCertificateTitleList(CertificateTitle certificateTitle);


    List<CertificateTitle> selectCertificateTitleListold(CertificateTitle certificateTitle);
}