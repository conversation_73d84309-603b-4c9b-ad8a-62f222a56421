package com.sccl.modules.mssaccount.msssapinfomain.service;

import com.sccl.common.lang.StringUtils;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.service.IdGenerator;
import com.sccl.modules.mssaccount.msssapinfodetail.domain.MssSapinfodetail;
import com.sccl.modules.mssaccount.msssapinfodetail.mapper.MssSapinfodetailMapper;
import com.sccl.modules.mssaccount.msssapinfomain.mapper.MssSapinfomainMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sccl.modules.mssaccount.msssapinfomain.domain.MssSapinfomain;

import java.util.*;


/**
 * 报账回传 服务层实现
 *
 * <AUTHOR>
 * @date 2019-05-01
 */
@Service
public class MssSapinfomainServiceImpl extends BaseServiceImpl<MssSapinfomain> implements IMssSapinfomainService {
    @Autowired
    private MssSapinfomainMapper mssSapinfomainMapper;
    @Autowired
    private MssSapinfodetailMapper mssSapinfodetailMapper;

    @Override
    public void saveSap(Map<String, Object> map) throws Exception {
        if ("S".equals(map.get("TYPE")) || "s".equals(map.get("TYPE"))) {
            LinkedList<Map<String, Object>> items = (LinkedList<Map<String, Object>>) map.get("items");
            if (items != null && items.size() > 0) {
                for (Map<String, Object> item : items) {// 只有一条！！！
                    if (item.get("instanceCode") != null &&
                            !StringUtils.isEmpty(item.get("instanceCode").toString())) {
                        Map<String, Object> query = new HashMap<>();
                        query.put("findBy", true);
                        query.put("instancecode", item.get("instanceCode").toString());
                        List<MssSapinfomain> mssSapinfomains = mssSapinfomainMapper.selectByMap(query);
                        if (mssSapinfomains != null && mssSapinfomains.size() > 0) {
                            // 存在sap记录, 更新状态
                            for (MssSapinfomain main : mssSapinfomains) {
                                Map<String, Object> querydetail = new HashMap<>();
                                querydetail.put("findBy", true);
                                querydetail.put("msmid", main.getMsmid());
                                List<MssSapinfodetail> mssSapinfodetails = mssSapinfodetailMapper.selectByMap(querydetail);
                                if (mssSapinfodetails != null && mssSapinfodetails.size() > 0) {
                                    for (MssSapinfodetail model : mssSapinfodetails) {
//                                        model.setStatus("0");
//                                        mssSapinfodetailMapper.updateForModel(model);
                                        Map<String, Object> deletemap = new HashMap<>();
                                        deletemap.put("msdid", model.getMsdid());
                                        mssSapinfodetailMapper.deleteByPrimaryKeyDB(deletemap);
                                    }
                                }
//                                main.setStatus("0");
//                                mssSapinfomainMapper.updateForModel(main);
                                Map<String, Object> deletemap = new HashMap<>();
                                deletemap.put("msmid", main.getMsmid());
                                mssSapinfomainMapper.deleteByPrimaryKeyDB(deletemap);
                            }
                        }
                        // 存入凭证
                        insertNewSap(item);
                    }
                }
            }
        }
    }

    private void insertNewSap(Map<String, Object> item) {
        List<MssSapinfomain> mains = new ArrayList<>();
        Long mainid = IdGenerator.getNextId();
        MssSapinfomain main = new MssSapinfomain();
        main.setMsmid(mainid);
        main.setInstancecode(item.get("instanceCode") == null ? "" : item.get("instanceCode").toString());
        main.setSapcertificatecode(item.get("sapCertificateCode") == null ? "" : item.get("sapCertificateCode").toString());
        main.setAccountyear(item.get("accountYear") == null ? "" : item.get("accountYear").toString());
        main.setSapcompanycode(item.get("sapCompanyCode") == null ? "" : item.get("sapCompanyCode").toString());
        main.setSapcreator(item.get("sapCreator") == null ? "" : item.get("sapCreator").toString());
        main.setAccountmonth(item.get("accountMonth") == null ? "" : item.get("accountMonth").toString());
        main.setStatus("1");
        LinkedList<Map<String, Object>> detailItems = (LinkedList<Map<String, Object>>) item.get("certificateDetailItems");
        List<MssSapinfodetail> details = new ArrayList<>();
        for (Map<String, Object> detailItem : detailItems) {
            MssSapinfodetail detail = new MssSapinfodetail();
            detail.setMsdid(IdGenerator.getNextId());
            detail.setMsmid(mainid);
            detail.setOthersystemdetailid(detailItem.get("otherSystemDetailId") == null ? "" : detailItem.get("otherSystemDetailId").toString());
            detail.setSapcertificatecode(detailItem.get("sapCertificateCode") == null ? "" : detailItem.get("sapCertificateCode").toString());
            detail.setAccountyear(detailItem.get("accountYear") == null ? "" : detailItem.get("accountYear").toString());
            detail.setSapcompanycode(detailItem.get("sapCompanyCode") == null ? "" : detailItem.get("sapCompanyCode").toString());
            detail.setIsdebit(detailItem.get("isDebit") == null ? "" : detailItem.get("isDebit").toString());
            detail.setSapitemnum(detailItem.get("sapItemNum") == null ? "" : detailItem.get("sapItemNum").toString());
            detail.setSpecialtag(detailItem.get("specialTag") == null ? "" : detailItem.get("specialTag").toString());
            detail.setPreaccountcode(detailItem.get("preAccountCode") == null ? "" : detailItem.get("preAccountCode").toString());
            detail.setPreaccountname(detailItem.get("preAccountName") == null ? "" : detailItem.get("preAccountName").toString());
            detail.setStatus("1");
            details.add(detail);
        }
        main.setDetails(details);
        mains.add(main);

        ///////////////////////////
        mssSapinfomainMapper.insertList(mains);
        for (MssSapinfomain m : mains) {// 只有一条！！！
            mssSapinfodetailMapper.insertList(m.getDetails());
        }
    }


}
