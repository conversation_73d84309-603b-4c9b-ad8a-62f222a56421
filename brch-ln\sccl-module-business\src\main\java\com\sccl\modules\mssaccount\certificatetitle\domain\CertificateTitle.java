package com.sccl.modules.mssaccount.certificatetitle.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;

import java.math.BigDecimal;


/**
 * view_certificate_title表 view_certificate_title
 *
 * <AUTHOR>
 * @date 2019-09-20
 */
public class CertificateTitle {
    private static final long serialVersionUID = 1L;

    private BigDecimal sum;
    private String sapitemmun;
    private String id;
    private String creatorName;
    /**
     *
     */
    private String code;
    /**
     *
     */
    private String certificateArchiveId;
    /**
     *
     */
    private String certificateDate;
    /**
     *
     */
    private String ledgerDate;
    /**
     *
     */
    private Double type;
    /**
     *
     */
    private String currency;
    /**
     *
     */
    private Double attachmentNum;
    /**
     *
     */
    private String companyCode;
    /**
     *
     */
    private String companyName;
    /**
     *
     */
    private String titleText;
    /**
     *
     */
    private String sapCertificateCode;
    /**
     *
     */
    private String creatorAccount;
    /**
     *
     */
    private Double status;
    /**
     *
     */
    private BigDecimal debitAmount;
    /**
     *
     */
    private BigDecimal creditAmount;
    /**
     *
     */
    private String paymentType;
    /**
     *
     */
    private String counteractNo;
    /**
     *
     */
    private String infoFrom;
    /**
     *
     */
    private String mergeornot;
    /**
     *
     */
    private String mmCertificateCode;
    /**
     *
     */
    private String tYear;
    /**
     *
     */
    private String tMonth;
    /**
     *
     */
    private String sapCreatorAccount;
    /**
     *
     */
    private String sapCounteractAccount;
    /**
     *
     */
    private String bizType;
    /**
     *
     */
    private String tradeType;
    /**
     *
     */
    private String isDoubleCertificate;
    /**
     *
     */
    private String sapCertificateDate;
    /**
     *
     */
    private String certificateIsexit;
    /**
     *
     */
    private String countercertificateIsexit;
    /**
     *
     */
    private String isAutoStart;
    /**
     *
     */
    private String isImageAudit;
    /**
     *
     */
    private String isExitBill;
    /**
     *
     */
    private String isCompletedSend;
    /**
     *
     */
    private String isAmortizeRelation;
    /**
     *
     */
    private String isFirstAmortize;
    /**
     *
     */
    private String isFirstTitle;
    /**
     *
     */
    private String carryoverTitleid;
    /**
     *
     */
    private String accountYear;
    /**
     *
     */
    private String isPayment;
    /**
     *
     */
    private String isEmergent;
    /**
     *
     */
    private String clearDate;
    /**
     *
     */
    private String clearCompanycode;
    /**
     *
     */
    private String clearYear;
    /**
     *
     */
    private String clearSapcode;
    /**
     *
     */
    private String clearFlag;
    /**
     *
     */
    private String clearAccount;
    /**
     *
     */
    private String isCompletedSnRetrieve;
    /**
     *
     */
    private String isEmployeeCheck;
    /**
     *
     */
    private String reimburseCode;
    /**
     *
     */
    private String reimburseName;
    /**
     *
     */
    private String billId;
    /**
     *
     */
    private String billClass;
    /**
     *
     */
    private String isNeedClear;
    /**
     *
     */
    private String isCanClear;
    /**
     *
     */
    private String carryoverType;
    /**
     *
     */
    private String accountPeriod;
    /**
     *
     */
    private String generateTimestamp;
    /**
     *
     */
    private String contractAuditStatus;
    /**
     *
     */
    private String excpaymentAbs;
    /**
     *
     */
    private String counteractNoDate;
    /**
     *
     */
    private String whichSystemCreate;
    /**
     *
     */
    private String provinceCode;
    /**
     *
     */
    private String msg;
    /**
     *
     */
    private String counteractCompanyCode;
    /**
     *
     */
    private String counteractYear;
    /**
     *
     */
    private String writeoffType;
    /**
     *
     */
    private String writeoffinstanceCode;
    /**
     *
     */
    private String isTobepaid;
    /**
     *
     */
    private String sapCostTime;


    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("code", getCode())
                .append("certificateArchiveId", getCertificateArchiveId())
                .append("certificateDate", getCertificateDate())
                .append("ledgerDate", getLedgerDate())
                .append("type", getType())
                .append("currency", getCurrency())
                .append("attachmentNum", getAttachmentNum())
                .append("companyCode", getCompanyCode())
                .append("companyName", getCompanyName())
                .append("titleText", getTitleText())
                .append("sapCertificateCode", getSapCertificateCode())
                .append("creatorAccount", getCreatorAccount())
                .append("creatorName", getCreatorName())
                .append("status", getStatus())
                .append("debitAmount", getDebitAmount())
                .append("creditAmount", getCreditAmount())
                .append("paymentType", getPaymentType())
                .append("counteractNo", getCounteractNo())
                .append("infoFrom", getInfoFrom())
                .append("mergeornot", getMergeornot())
                .append("mmCertificateCode", getMmCertificateCode())
                .append("tYear", getTYear())
                .append("tMonth", getTMonth())
                .append("sapCreatorAccount", getSapCreatorAccount())
                .append("sapCounteractAccount", getSapCounteractAccount())
                .append("bizType", getBizType())
                .append("tradeType", getTradeType())
                .append("isDoubleCertificate", getIsDoubleCertificate())
                .append("sapCertificateDate", getSapCertificateDate())
                .append("certificateIsexit", getCertificateIsexit())
                .append("countercertificateIsexit", getCountercertificateIsexit())
                .append("isAutoStart", getIsAutoStart())
                .append("isImageAudit", getIsImageAudit())
                .append("isExitBill", getIsExitBill())
                .append("isCompletedSend", getIsCompletedSend())
                .append("isAmortizeRelation", getIsAmortizeRelation())
                .append("isFirstAmortize", getIsFirstAmortize())
                .append("isFirstTitle", getIsFirstTitle())
                .append("carryoverTitleid", getCarryoverTitleid())
                .append("accountYear", getAccountYear())
                .append("isPayment", getIsPayment())
                .append("isEmergent", getIsEmergent())
                .append("clearDate", getClearDate())
                .append("clearCompanycode", getClearCompanycode())
                .append("clearYear", getClearYear())
                .append("clearSapcode", getClearSapcode())
                .append("clearFlag", getClearFlag())
                .append("clearAccount", getClearAccount())
                .append("isCompletedSnRetrieve", getIsCompletedSnRetrieve())
                .append("isEmployeeCheck", getIsEmployeeCheck())
                .append("reimburseCode", getReimburseCode())
                .append("reimburseName", getReimburseName())
                .append("billId", getBillId())
                .append("billClass", getBillClass())
                .append("isNeedClear", getIsNeedClear())
                .append("isCanClear", getIsCanClear())
                .append("carryoverType", getCarryoverType())
                .append("accountPeriod", getAccountPeriod())
                .append("generateTimestamp", getGenerateTimestamp())
                .append("contractAuditStatus", getContractAuditStatus())
                .append("excpaymentAbs", getExcpaymentAbs())
                .append("counteractNoDate", getCounteractNoDate())
                .append("whichSystemCreate", getWhichSystemCreate())
                .append("provinceCode", getProvinceCode())
                .append("msg", getMsg())
                .append("counteractCompanyCode", getCounteractCompanyCode())
                .append("counteractYear", getCounteractYear())
                .append("writeoffType", getWriteoffType())
                .append("writeoffinstanceCode", getWriteoffinstanceCode())
                .append("isTobepaid", getIsTobepaid())
                .append("sapCostTime", getSapCostTime())
                .toString();
    }

    public BigDecimal getSum() {
        return sum;
    }

    public void setSum(BigDecimal sum) {
        this.sum = sum;
    }

    public String getSapitemmun() {
        return sapitemmun;
    }

    public void setSapitemmun(String sapitemmun) {
        this.sapitemmun = sapitemmun;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCertificateArchiveId() {
        return certificateArchiveId;
    }

    public void setCertificateArchiveId(String certificateArchiveId) {
        this.certificateArchiveId = certificateArchiveId;
    }

    public String getCertificateDate() {
        return certificateDate;
    }

    public void setCertificateDate(String certificateDate) {
        this.certificateDate = certificateDate;
    }

    public String getLedgerDate() {
        return ledgerDate;
    }

    public void setLedgerDate(String ledgerDate) {
        this.ledgerDate = ledgerDate;
    }

    public Double getType() {
        return type;
    }

    public void setType(Double type) {
        this.type = type;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Double getAttachmentNum() {
        return attachmentNum;
    }

    public void setAttachmentNum(Double attachmentNum) {
        this.attachmentNum = attachmentNum;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getTitleText() {
        return titleText;
    }

    public void setTitleText(String titleText) {
        this.titleText = titleText;
    }

    public String getSapCertificateCode() {
        return sapCertificateCode;
    }

    public void setSapCertificateCode(String sapCertificateCode) {
        this.sapCertificateCode = sapCertificateCode;
    }

    public String getCreatorAccount() {
        return creatorAccount;
    }

    public void setCreatorAccount(String creatorAccount) {
        this.creatorAccount = creatorAccount;
    }

    public Double getStatus() {
        return status;
    }

    public void setStatus(Double status) {
        this.status = status;
    }

    public BigDecimal getDebitAmount() {
        return debitAmount;
    }

    public void setDebitAmount(BigDecimal debitAmount) {
        this.debitAmount = debitAmount;
    }

    public BigDecimal getCreditAmount() {
        return creditAmount;
    }

    public void setCreditAmount(BigDecimal creditAmount) {
        this.creditAmount = creditAmount;
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public String getCounteractNo() {
        return counteractNo;
    }

    public void setCounteractNo(String counteractNo) {
        this.counteractNo = counteractNo;
    }

    public String getInfoFrom() {
        return infoFrom;
    }

    public void setInfoFrom(String infoFrom) {
        this.infoFrom = infoFrom;
    }

    public String getMergeornot() {
        return mergeornot;
    }

    public void setMergeornot(String mergeornot) {
        this.mergeornot = mergeornot;
    }

    public String getMmCertificateCode() {
        return mmCertificateCode;
    }

    public void setMmCertificateCode(String mmCertificateCode) {
        this.mmCertificateCode = mmCertificateCode;
    }

    public String getTYear() {
        return tYear;
    }

    public void setTYear(String tYear) {
        this.tYear = tYear;
    }

    public String getTMonth() {
        return tMonth;
    }

    public void setTMonth(String tMonth) {
        this.tMonth = tMonth;
    }

    public String getSapCreatorAccount() {
        return sapCreatorAccount;
    }

    public void setSapCreatorAccount(String sapCreatorAccount) {
        this.sapCreatorAccount = sapCreatorAccount;
    }

    public String getSapCounteractAccount() {
        return sapCounteractAccount;
    }

    public void setSapCounteractAccount(String sapCounteractAccount) {
        this.sapCounteractAccount = sapCounteractAccount;
    }

    public String getBizType() {
        return bizType;
    }

    public void setBizType(String bizType) {
        this.bizType = bizType;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public String getIsDoubleCertificate() {
        return isDoubleCertificate;
    }

    public void setIsDoubleCertificate(String isDoubleCertificate) {
        this.isDoubleCertificate = isDoubleCertificate;
    }

    public String getSapCertificateDate() {
        return sapCertificateDate;
    }

    public void setSapCertificateDate(String sapCertificateDate) {
        this.sapCertificateDate = sapCertificateDate;
    }

    public String getCertificateIsexit() {
        return certificateIsexit;
    }

    public void setCertificateIsexit(String certificateIsexit) {
        this.certificateIsexit = certificateIsexit;
    }

    public String getCountercertificateIsexit() {
        return countercertificateIsexit;
    }

    public void setCountercertificateIsexit(String countercertificateIsexit) {
        this.countercertificateIsexit = countercertificateIsexit;
    }

    public String getIsAutoStart() {
        return isAutoStart;
    }

    public void setIsAutoStart(String isAutoStart) {
        this.isAutoStart = isAutoStart;
    }

    public String getIsImageAudit() {
        return isImageAudit;
    }

    public void setIsImageAudit(String isImageAudit) {
        this.isImageAudit = isImageAudit;
    }

    public String getIsExitBill() {
        return isExitBill;
    }

    public void setIsExitBill(String isExitBill) {
        this.isExitBill = isExitBill;
    }

    public String getIsCompletedSend() {
        return isCompletedSend;
    }

    public void setIsCompletedSend(String isCompletedSend) {
        this.isCompletedSend = isCompletedSend;
    }

    public String getIsAmortizeRelation() {
        return isAmortizeRelation;
    }

    public void setIsAmortizeRelation(String isAmortizeRelation) {
        this.isAmortizeRelation = isAmortizeRelation;
    }

    public String getIsFirstAmortize() {
        return isFirstAmortize;
    }

    public void setIsFirstAmortize(String isFirstAmortize) {
        this.isFirstAmortize = isFirstAmortize;
    }

    public String getIsFirstTitle() {
        return isFirstTitle;
    }

    public void setIsFirstTitle(String isFirstTitle) {
        this.isFirstTitle = isFirstTitle;
    }

    public String getCarryoverTitleid() {
        return carryoverTitleid;
    }

    public void setCarryoverTitleid(String carryoverTitleid) {
        this.carryoverTitleid = carryoverTitleid;
    }

    public String getAccountYear() {
        return accountYear;
    }

    public void setAccountYear(String accountYear) {
        this.accountYear = accountYear;
    }

    public String getIsPayment() {
        return isPayment;
    }

    public void setIsPayment(String isPayment) {
        this.isPayment = isPayment;
    }

    public String getIsEmergent() {
        return isEmergent;
    }

    public void setIsEmergent(String isEmergent) {
        this.isEmergent = isEmergent;
    }

    public String getClearDate() {
        return clearDate;
    }

    public void setClearDate(String clearDate) {
        this.clearDate = clearDate;
    }

    public String getClearCompanycode() {
        return clearCompanycode;
    }

    public void setClearCompanycode(String clearCompanycode) {
        this.clearCompanycode = clearCompanycode;
    }

    public String getClearYear() {
        return clearYear;
    }

    public void setClearYear(String clearYear) {
        this.clearYear = clearYear;
    }

    public String getClearSapcode() {
        return clearSapcode;
    }

    public void setClearSapcode(String clearSapcode) {
        this.clearSapcode = clearSapcode;
    }

    public String getClearFlag() {
        return clearFlag;
    }

    public void setClearFlag(String clearFlag) {
        this.clearFlag = clearFlag;
    }

    public String getClearAccount() {
        return clearAccount;
    }

    public void setClearAccount(String clearAccount) {
        this.clearAccount = clearAccount;
    }

    public String getIsCompletedSnRetrieve() {
        return isCompletedSnRetrieve;
    }

    public void setIsCompletedSnRetrieve(String isCompletedSnRetrieve) {
        this.isCompletedSnRetrieve = isCompletedSnRetrieve;
    }

    public String getIsEmployeeCheck() {
        return isEmployeeCheck;
    }

    public void setIsEmployeeCheck(String isEmployeeCheck) {
        this.isEmployeeCheck = isEmployeeCheck;
    }

    public String getReimburseCode() {
        return reimburseCode;
    }

    public void setReimburseCode(String reimburseCode) {
        this.reimburseCode = reimburseCode;
    }

    public String getReimburseName() {
        return reimburseName;
    }

    public void setReimburseName(String reimburseName) {
        this.reimburseName = reimburseName;
    }

    public String getBillId() {
        return billId;
    }

    public void setBillId(String billId) {
        this.billId = billId;
    }

    public String getBillClass() {
        return billClass;
    }

    public void setBillClass(String billClass) {
        this.billClass = billClass;
    }

    public String getIsNeedClear() {
        return isNeedClear;
    }

    public void setIsNeedClear(String isNeedClear) {
        this.isNeedClear = isNeedClear;
    }

    public String getIsCanClear() {
        return isCanClear;
    }

    public void setIsCanClear(String isCanClear) {
        this.isCanClear = isCanClear;
    }

    public String getCarryoverType() {
        return carryoverType;
    }

    public void setCarryoverType(String carryoverType) {
        this.carryoverType = carryoverType;
    }

    public String getAccountPeriod() {
        return accountPeriod;
    }

    public void setAccountPeriod(String accountPeriod) {
        this.accountPeriod = accountPeriod;
    }

    public String getGenerateTimestamp() {
        return generateTimestamp;
    }

    public void setGenerateTimestamp(String generateTimestamp) {
        this.generateTimestamp = generateTimestamp;
    }

    public String getContractAuditStatus() {
        return contractAuditStatus;
    }

    public void setContractAuditStatus(String contractAuditStatus) {
        this.contractAuditStatus = contractAuditStatus;
    }

    public String getExcpaymentAbs() {
        return excpaymentAbs;
    }

    public void setExcpaymentAbs(String excpaymentAbs) {
        this.excpaymentAbs = excpaymentAbs;
    }

    public String getCounteractNoDate() {
        return counteractNoDate;
    }

    public void setCounteractNoDate(String counteractNoDate) {
        this.counteractNoDate = counteractNoDate;
    }

    public String getWhichSystemCreate() {
        return whichSystemCreate;
    }

    public void setWhichSystemCreate(String whichSystemCreate) {
        this.whichSystemCreate = whichSystemCreate;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getCounteractCompanyCode() {
        return counteractCompanyCode;
    }

    public void setCounteractCompanyCode(String counteractCompanyCode) {
        this.counteractCompanyCode = counteractCompanyCode;
    }

    public String getCounteractYear() {
        return counteractYear;
    }

    public void setCounteractYear(String counteractYear) {
        this.counteractYear = counteractYear;
    }

    public String getWriteoffType() {
        return writeoffType;
    }

    public void setWriteoffType(String writeoffType) {
        this.writeoffType = writeoffType;
    }

    public String getWriteoffinstanceCode() {
        return writeoffinstanceCode;
    }

    public void setWriteoffinstanceCode(String writeoffinstanceCode) {
        this.writeoffinstanceCode = writeoffinstanceCode;
    }

    public String getIsTobepaid() {
        return isTobepaid;
    }

    public void setIsTobepaid(String isTobepaid) {
        this.isTobepaid = isTobepaid;
    }

    public String getSapCostTime() {
        return sapCostTime;
    }

    public void setSapCostTime(String sapCostTime) {
        this.sapCostTime = sapCostTime;
    }

    private String isdebit;

    public String getIsdebit() {
        return isdebit;
    }

    public void setIsdebit(String isdebit) {
        this.isdebit = isdebit;
    }

    private String ledgerCategory;

    public String getLedgerCategory() {
        return ledgerCategory;
    }

    public void setLedgerCategory(String ledgerCategory) {
        this.ledgerCategory = ledgerCategory;
    }

    private String economicitemCode;

    public String getEconomicitemCode() {
        return economicitemCode;
    }

    public void setEconomicitemCode(String economicitemCode) {
        this.economicitemCode = economicitemCode;
    }

    private String fillInDept;// 用作查询 条件是否本部门

    public String getFillInDept() {
        return fillInDept;
    }

    public void setFillInDept(String fillInDept) {
        this.fillInDept = fillInDept;
    }
}
