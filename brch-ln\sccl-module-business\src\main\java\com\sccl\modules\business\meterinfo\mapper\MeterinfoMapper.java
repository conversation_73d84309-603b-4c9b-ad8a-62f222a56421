package com.sccl.modules.business.meterinfo.mapper;

import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.meterinfo.domain.Meterinfo;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;

/**
 * 计量设备 数据层
 *
 * <AUTHOR>
 * @date 2023-03-15
 */
public interface MeterinfoMapper extends BaseMapper<Meterinfo>
{


    int insertInfo();

    int insertListFortemporary(List newList);

    void deleteRepeat();

    Integer countForMeterinfoAll();

    Integer countForMeterinfoAllFail();

    HashMap<String, Object> getMeterInfoByStationCode(@Param("meterCode") String meterCode);

    List<HashMap<String, Object>> getMeterInfoListByStationCode(@Param("meterCode") String meterCode);
}
