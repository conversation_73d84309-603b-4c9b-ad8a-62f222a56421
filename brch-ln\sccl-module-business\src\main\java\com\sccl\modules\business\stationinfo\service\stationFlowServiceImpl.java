package com.sccl.modules.business.stationinfo.service;

import cn.hutool.core.collection.CollectionUtil;
import com.sccl.common.lang.StringUtils;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.utils.enumClass.CommonConstants;
import com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol;
import com.sccl.modules.business.ammeterorprotocol.domain.AmmeterorprotocolRecord;
import com.sccl.modules.business.ammeterorprotocol.mapper.AmmeterorprotocolMapper;
import com.sccl.modules.business.ammeterorprotocol.mapper.AmmeterorprotocolRecordMapper;
import com.sccl.modules.business.dataaudit.domain.PowerStationAnomaly;
import com.sccl.modules.business.dataaudit.mapper.PowerStationAnomalyMapper;
import com.sccl.modules.business.order.domain.Order;
import com.sccl.modules.business.order.service.IOrderService;
import com.sccl.modules.business.statinAudit.util.StationAuditUtil;
import com.sccl.modules.business.stationinfo.domain.StationInfo;
import com.sccl.modules.business.stationinfo.domain.StationRecord;
import com.sccl.modules.business.stationinfo.mapper.StationInfoMapper;
import com.sccl.modules.business.stationinfo.mapper.StationRecordMapper;
import com.sccl.modules.mssaccount.mssinterface.service.MssInterfaceServiceImpl;
import com.sccl.modules.system.role.domain.Role;
import com.sccl.modules.system.user.service.IUserService;
import com.sccl.modules.uniflow.common.WFModel;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 局站流程回调
 */
@Service
public class stationFlowServiceImpl extends BaseServiceImpl<Order> implements IOrderService {
    @Autowired
    StationInfoMapper stationInfoMapper;
    @Autowired
    StationRecordMapper stationRecordMapper;
    @Autowired
    AmmeterorprotocolRecordMapper ammeterorprotocolRecordMapper;
    @Autowired
    AmmeterorprotocolMapper ammeterorprotocolMapper;
    @Autowired
    private MssInterfaceServiceImpl mssInterfaceServiceImpl;
    @Autowired
    private PowerStationAnomalyMapper powerStationAnomalyMapper;
    @Autowired
    private IUserService userService;

    @Value("${sccl.deployTo}")
    private String configVersion;

    /**
     * 流程引擎回调（通过MQ）
     *
     * @param wfModel 流程消息对象
     */
    @Override
    public void uniflowCallBack(WFModel wfModel) throws Exception {
        System.out.println("-----------------lt:" + wfModel.toString());
        if(wfModel.getVariables().containsKey("firstNode") && wfModel.getVariables().get("firstNode").equals(true)){
            if(wfModel.getBusiAlias().equals("ADD_STATION")){
                updateStatus(wfModel.getBusiId(),1L);//修改单据状态为流程中
            }else{
                updateStatus(wfModel.getBusiId(),4L);//修改单据状态为修改流程中
                //局站异常管控-局站停用时，更新局站异常管控表状态
                updateStationAnomalyStatus(wfModel.getBusiId(), CommonConstants.AMM_ANOMALY_STATUS_LCZ, wfModel.getProcInstId());
            }
        }else if(StringUtils.isNotEmpty(wfModel.getCallbackType()) && "PROCESS_COMPLETED".equals(wfModel.getCallbackType())){
            updateStation(wfModel.getBusiId(),wfModel.getApplyUserId(),2L);//修改单据状态为已完成并更新数据
            //局站异常管控-局站停用时，更新局站异常管控表状态
            updateStationAnomalyStatus(wfModel.getBusiId(), CommonConstants.AMM_ANOMALY_STATUS_YWC, wfModel.getProcInstId());
        }else if(StringUtils.isNotEmpty(wfModel.getCallbackType()) && "PROCESS_CANCELLED".equals(wfModel.getCallbackType())){
            //只有驳回才更改状态，不同意时还是在流程中
            Map<String,Object> parms = new HashMap<>();
            parms.put("id",wfModel.getBusiId());
            StationInfo stationInfo=stationInfoMapper.selectByPrimaryKey(parms);
            if(stationInfo.getWfStatus()!=null&&stationInfo.getWfStatus() ==1){
                //草稿状态流程中退回时，改回流程中
                updateStatus(wfModel.getBusiId(),0L);
            }else if(stationInfo.getWfStatus()!=null&&stationInfo.getWfStatus() ==4){
                //修改中状态流程中退回时，改回修改中
                updateStatus(wfModel.getBusiId(),3L);
                //局站异常管控-局站停用时，更新局站异常管控表状态
                updateStationAnomalyStatus(wfModel.getBusiId(), CommonConstants.AMM_ANOMALY_STATUS_CG, wfModel.getProcInstId());
            }
        }
    }

    /**
     * 更新局站信息表 流程状态
     * @param id
     * @param wfStatus
     */
    private void updateStatus(String id,Long wfStatus){
        Map<String,Object> parms = new HashMap<>();
        parms.put("id",id);
        StationInfo stationInfo=stationInfoMapper.selectByPrimaryKey(parms);
        stationInfo.setWfStatus(wfStatus);
        stationInfoMapper.updateForModel(stationInfo);
    }

    public void updateStation(String id,String userId,Long wfStatus) throws Exception {
        StationRecord stationRecord = new StationRecord();
        stationRecord.setStationid(Long.parseLong(id));
        //流程发起人的userid
        stationRecord.setCreateuser(new Long(userId));
        stationRecord.setDeployTo(configVersion);
        stationRecord=stationRecordMapper.getByStationId(stationRecord);
        //更新记录表该修改用户的最新条数据状态为2,用于修改时确定展示
        stationRecord.setWfStatus(wfStatus);
        stationRecordMapper.updateForModel(stationRecord);
        //删除记录表中同stationid且状态不是2的数据(其他用户修改但未提交流程的数据删除掉，修改时显示最新条生效数据)
        stationRecordMapper.deleteStstionRecord(Long.parseLong(id));
        //更新局站表信息（用记录表最新记录来更新）
        StationInfo stationInfo=new StationInfo();
        BeanUtils.copyProperties(stationRecord, stationInfo);
        stationInfo.setWfStatus(wfStatus);
        stationInfo.setId(stationRecord.getStationid());
        stationInfoMapper.updateByPrimaryKey(stationInfo);

        //维护电表管理的局站
        AmmeterorprotocolRecord ammeterorprotocolRecord = new AmmeterorprotocolRecord();
        ammeterorprotocolRecord.setStationcode(stationInfo.getId()+"");
        List<AmmeterorprotocolRecord> records = ammeterorprotocolRecordMapper.selectList(ammeterorprotocolRecord);
        for (AmmeterorprotocolRecord record:records) {
            AmmeterorprotocolRecord record1 = new AmmeterorprotocolRecord();
            record1.setId(record.getId());
            record1.setStationName(stationInfo.getStationname());
            record1.setStationcode(stationInfo.getId()+"");
            record1.setStationaddress(stationInfo.getAddress());
            record1.setStationstatus(Integer.parseInt(stationInfo.getStatus()));
            record1.setStationtype(Integer.parseInt(stationInfo.getStationtype()+""));
            record1.setStationaddresscode(stationInfo.getResstationcode());
            ammeterorprotocolRecordMapper.updateForModel(record1);
            //更新计量设备数据
//            mssInterfaceServiceImpl.selectmeterEquipmentInfors(+record.getAmmeterprotocolid()+"/"+"update");
        }
        //维护电表管理的局站
        Ammeterorprotocol ammeterorprotocol = new Ammeterorprotocol();
        ammeterorprotocol.setStationcode(stationInfo.getId()+"");
        List<Ammeterorprotocol> lists = ammeterorprotocolMapper.selectList(ammeterorprotocol);
        for (Ammeterorprotocol value:lists) {
            Ammeterorprotocol result = new Ammeterorprotocol();
            result.setId(value.getId());
            result.setStationName(stationInfo.getStationname());
            result.setStationcode(stationInfo.getId()+"");
            result.setStationaddress(stationInfo.getAddress());
            result.setStationstatus(Integer.parseInt(stationInfo.getStatus()));
            result.setStationtype(Integer.parseInt(stationInfo.getStationtype()+""));
            result.setStationaddresscode(stationInfo.getResstationcode());
            ammeterorprotocolMapper.updateForModel(result);
        }

        /****重新稽核台账开始   add by qinxinmin 2025-03-05   ******/
        boolean isJHSD = false;
        List<Role> roles = userService.selectUserRole(Long.parseLong(userId));
        for (Role role : roles) {
            if (role.getCode().equalsIgnoreCase("JH_SD")) {
                //JH_SD("JH_SD", "稽核试点");
                isJHSD = true;
                break;
            }
        }
        if (isJHSD) {
            List<Long> ammeterIds = lists.stream().map(Ammeterorprotocol::getId).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(ammeterIds)) {
                //重新稽核相关台账
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        StationAuditUtil.doAuditThreadByAmmeterIds(ammeterIds);
                    }
                });
            }
        }
        /****重新稽核台账完成    add by qinxinmin 2025-03-05   ******/
    }

    /**
     * 局站异常管控-局站停用时，更新局站异常管控表状态
     * @param stationId  局站id
     * @param status     状态
     * @param procInstId 流程id
     */
    private void updateStationAnomalyStatus(String stationId, int status, Long procInstId) {
        if (stationId != null) {
            Long stationIdL = Long.valueOf(stationId);
            String statusStation = stationRecordMapper.getStatusByStationId(stationIdL);
            if ("1".equals(statusStation)) {
                PowerStationAnomaly entity = new PowerStationAnomaly();
                entity.setStationId(stationIdL);
                entity.setStatus(status);
                entity.setProCessId(procInstId);
                entity.initUpdate();
                powerStationAnomalyMapper.updateStatusByStationId(entity);
            }
        }
    }
}
