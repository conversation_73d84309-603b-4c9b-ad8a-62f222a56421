package com.sccl.modules.mssaccount.mssaccountbill.domain;

import com.sccl.framework.aspectj.lang.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 报账统计
 * @date 2024/10/21  14:27
 */
@Data
public class StatisticalAccountBillDTO {

    /**
     * 序号
     */
    @Excel(name = "序号")
    private String accountNo;

    /**
     * 账期
     */
    @Excel(name = "账期")
    private String accountPeriod;

    /**
     * 账单Id
     */
    @Excel(name = "账单Id")
    private String accountBillId;

    /**
     * 财辅报账编码
     */
    @Excel(name = "财辅报账编码")
    private String writeOffInstanceCode;

    /**
     * 事项名称
     */
    @Excel(name = "事项名称")
    private String eventName;

    /**
     * 报账总金额（元）
     */
    @Excel(name = "报账总金额（元）")
    private BigDecimal accountTotalMoney;

    /**
     * 用量
     */
    @Excel(name = "用量")
    private BigDecimal accountAmount;

    /**
     * 能耗类型
     */
    @Excel(name = "能耗类型")
    private String energyType;

    /**
     * 报账单位
     */
    @Excel(name = "报账单位")
    private String companyName;

    /**
     * 报账人员
     */
    @Excel(name = "报账人员")
    private String accountUserName;

    /**
     * 账单类型
     */
    @Excel(name = "账单类型")
    private String accountBillType;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private String status;

}
