package com.sccl.modules.business.stationreportwhitelist.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;

/**
 * 所有类型表
 */
@Getter
@Setter
@TableName(value = "power_category_type")
public class PowerCategoryType extends Model<PowerCategoryType> {

    /**
     * 主键
     */
    private Long id;

    /**
     * 在电表和机房表中的引用字段
     */
    private String typeCategory;

    /**
     * 类型名称
     */
    private String typeName;

    /**
     * 类型编码
     */
    private String typeCode;

    /**
     * 类型描述
     */
    private String typeDesc;

}
