package com.sccl.modules.protocolexpiration.ammeterorprotocol.service;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.business.ammeterorprotocol.mapper.AmmeterorprotocolMapper;
import com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 基础数据-电管理（注意该中所有的引用类型，
都是指的power_category_type里面的type_code而非
ID，关注每个字段的注释） 服务层实现
 * 
 * <AUTHOR>
 * @date 2019-05-18
 */
@Service
public class ProtocolexpirationServiceImpl extends BaseServiceImpl<Ammeterorprotocol> implements IProtocolexpirationService
{
    @Autowired
    AmmeterorprotocolMapper ammeterorprotocolMapper1;

    public List<Ammeterorprotocol> selectListBy(Ammeterorprotocol ammeterorprotocol){
        return ammeterorprotocolMapper1.selectListBy(ammeterorprotocol);
    }
}
