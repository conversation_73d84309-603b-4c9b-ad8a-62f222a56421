package com.sccl.modules.oss.collect;

import com.enrising.dcarbon.bean.SpringUtil;
import com.enrising.dcarbon.collector.AbstractCollectedDatasourceHandlerNode;
import com.enrising.dcarbon.collector.AbstractCollectorTemplate;
import com.enrising.dcarbon.collector.CollectedDatasource;
import com.sccl.modules.oss.entity.OssMsgEntity;
import com.sccl.modules.oss.mapper.OssMsgMapper;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-07-06 14:58
 * @email <EMAIL>
 */
@Slf4j
public class RepeatHandler extends AbstractCollectedDatasourceHandlerNode {
    @Override
    public List<CollectedDatasource> doHandle(List<CollectedDatasource> datasourceList) {
        List<OssMsgEntity> entities = AbstractCollectorTemplate.convert(datasourceList, OssMsgEntity.class);
        int size = entities.size();
        Optional<OssMsgMapper> mapperOptional = SpringUtil.getBeanOptional(OssMsgMapper.class);
        mapperOptional.ifPresent(mapper -> {
            List<String> exists = mapper.selectExists(entities
                    .stream()
                    .map(OssMsgEntity::getOssId)
                    .collect(Collectors.toList()));
            if (exists == null || entities.size() == 0) {
                return;
            }
            datasourceList.removeIf(item -> {
                OssMsgEntity it = (OssMsgEntity) item;
                return exists.contains(it.getOssId());
            });
        });
        log.info("找到{}条已存在的OSS数据，已移除", size - datasourceList.size());
        return null;
    }
}
