package com.sccl.modules.business.statistical.framework.repository;

import com.sccl.modules.business.statistical.framework.StatisticalIndex;
import com.sccl.modules.business.statistical.framework.codec.StatisticalDeserializer;
import com.sccl.modules.business.statistical.framework.codec.StatisticalSerializer;

import java.util.List;

/**
 * 统计指标存储库
 *
 * <AUTHOR>
 * @Date 2022/10/25 17:19
 */
public interface StatisticalRepository<R> {
    boolean save(List<StatisticalIndex> statisticalIndices, StatisticalSerializer<R> serializer);

    List<StatisticalIndex> query(StatisticalDeserializer<R> deserializer, R query);

    boolean update(R param, R query);

    boolean delete(R query);
}
