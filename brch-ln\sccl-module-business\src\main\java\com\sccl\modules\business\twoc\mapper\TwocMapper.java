package com.sccl.modules.business.twoc.mapper;

import com.sccl.modules.business.twoc.domain.TwoCFlag;
import com.sccl.modules.business.twoc.domain.Twoc;
import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.mssaccount.mssinterface.domain.MachineRoomEnergyUseEntity;
import com.sccl.modules.mssaccount.mssinterface.domain.StationEnergyUseEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 计量设备 数据层
 * 
 * <AUTHOR>
 * @date 2023-04-13
 */
public interface TwocMapper extends BaseMapper<Twoc>
{


    Integer countForMeterinfoAllFail();

    Integer countForMeterinfoAll();

    List<? extends TwoCFlag> selectMeterInfoFail(@Param("id") Long id, @Param("offset") int offset,
                                                 @Param("size") int syncsum);

    List<? extends TwoCFlag> selectMeterInfo(@Param("id") Long id, @Param("offset") int offset,
                                             @Param("size") int syncsum);

    int updateForMeterInfo(Twoc infoDb);

    int updateForMeterInfoAll(Twoc infoDb);

    int deleteMachineRoomInfo(@Param("premonth") String premonth);
    int deleteStaionInfo(@Param("premonth") String premonth);

    int CreateMachineRoomInfo(@Param("premonth") String premonth, @Param("roomids") List<String> roomids, @Param("provincecode") String provincecode);
    int CreateStationInfo(@Param("premonth")String promonth, @Param("stationids") List<String> stationids, @Param("provincecode") String provincecode);

    List<MachineRoomEnergyUseEntity> getMachineRoomInfo(@Param("premonth") String premonth);
    List<StationEnergyUseEntity> getStaionInfo(@Param("premonth") String premonth);
}