package com.sccl.modules.business.heataccount.controller;

import com.sccl.common.constant.Constants;
import com.sccl.common.support.Convert;
import com.sccl.common.utils.DateUtils;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.utils.ExcelUtil;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.autojob.util.convert.StringUtils;
import com.sccl.modules.business.heataccount.domain.HeatAccount;
import com.sccl.modules.business.heataccount.domain.HeatAccountRequest;
import com.sccl.modules.business.heataccount.service.HeatAccountService;
import com.sccl.modules.system.organization.service.IOrganizationService;
import com.sccl.modules.system.role.domain.Role;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.service.IUserService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 热力控制层
 * @date 2024/8/27  11:24
 */
@RestController
@RequestMapping("/business/heat/account")
public class HeatAccountController extends BaseController {

   @Resource(name = "heatAccountServiceImpl")
   private HeatAccountService accountService;

    @Resource(name = "userServiceImpl")
    private IUserService userService;

    @Resource(name = "organizationServiceImpl")
    private IOrganizationService organizationService;


    /**
     * 新增或修改热力台账
     * @param accounts
     * @return
     */
    @PostMapping("/addOrUpdate")
    public AjaxResult batchAddOrUpdateHeatAccount(@RequestBody List<HeatAccount> accounts) {
        // 获取登录用户信息
        User user = ShiroUtils.getUser();
        if (null == user) {
            return error("当前系统无登录用户信息，请检查！！！");
        }
        Map<String, Integer> map = new HashMap<>(4);
        map.put("num", accountService.batchAddOrUpdateHeatAccount(accounts, user));
        map.put("code", 0);
        return success(map);
    }

    /**
     * 查询热力台账列表
     */
    @RequestMapping("/list")
    public TableDataInfo selectHeatAccountPageList(HeatAccountRequest request) {
        // 获取查询权限
        getSelectAuthority(request);
        // 参数处理
        if (null != request.getAccountno() && String.valueOf(Constants.All_CODE).equals(request.getAccountno())) {
            request.setAccountno(null);
        }
        // 台账查询
        if (null != request.getQuery() && request.getQuery()) {
            if (StringUtils.isEmpty(request.getStartAccountNo())) {
                request.setStartAccountNo(null);
            }
            if (StringUtils.isEmpty(request.getEndAccountNo())) {
                request.setEndAccountNo(null);
            }
            if (request.getStartAccountNo() != null && request.getEndAccountNo() == null) {
                request.setCurrentAccountNo(DateUtils.getDate("yyyyMM"));
            }
        }
        startPage();
        List<HeatAccount> list = accountService.listHeatAccount(request);
        return getDataTable(list);
    }

    /**
     * 删除热力台账
     */
    @DeleteMapping("/delete")
    public AjaxResult deleteHeatAccount(@RequestBody HashMap<String, String> map) {
        return toAjax(accountService.deleteByIds(Convert.toStrArray(map.get("ids"))));
    }

    /**
     * 热力台账批量导入
     */
    @PostMapping("/import")
    public Map<String, Object> importHeatAccount(HttpServletRequest request, HttpServletResponse response) throws Exception{
        return accountService.importHeatAccount(request, response);
    }

    /**
     * 热力台账excel模板下载
     */
    @GetMapping("/template/load")
    public void loadCoalTemplateExcel(HttpServletResponse response) throws Exception {
        InputStream in = getClass().getClassLoader().getResourceAsStream("heat.xlsx");
        if (null == in) {
            throw new Exception("导入模板下载失败，请联系管理员！！！");
        }
        ExcelUtil.getTemplateExcel(response, in, "热力台账导入模板.xlsx");
    }


    /**
     * 获取查询权限
     *
     * @param request
     */
    private void getSelectAuthority(HeatAccountRequest request){
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
            if (role.getCode().startsWith("SUB_")) {//县能耗费管理员
                isSubAdmin = true;
            }
        }
       if (isCityAdmin) {
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0)
                request.setCompany(companies.get(0).getId());
        } else if (isSubAdmin) {
            List<IdNameVO> departments = user.getDepartments();
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0) {
                List<Map<String, Object>> countrys = organizationService.selectSubordinateOrgByRole(companies.get(0).getId(), "1");
                if (countrys != null && countrys.size() > 0) {
                    request.setCountrys(countrys);
                } else {
                    if (departments != null && departments.size() > 0 && StringUtils.isEmpty(request.getCountry()))
                        request.setCountry(departments.get(0).getId());
                }
            }
        } else {
            request.setFillInAccount(user.getLoginId());
        }
    }
}
