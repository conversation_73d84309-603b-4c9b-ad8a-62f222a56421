package com.sccl.modules.business.energyaccount.service;

import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.business.account.domain.Account;
import com.sccl.modules.business.energyaccount.domain.EnergyAccount;
import com.sccl.framework.service.IBaseService;
import com.sccl.modules.business.energyaccount.domain.EnergyAccountVo;
import com.sccl.modules.business.oilexpense.domain.Audit;

import java.math.BigDecimal;
import java.util.List;

/**
 * 油水气出入台账 服务层
 *
 * <AUTHOR>
 * @date 2021-12-15
 */
public interface IEnergyAccountService extends IBaseService<EnergyAccount> {

    List<EnergyAccount> listAccount(EnergyAccount account);

    void saveOilAccount(EnergyAccount energyAccount);

    AjaxResult addsave(List<EnergyAccount> energyAccounts);

    AjaxResult addPower(List<EnergyAccount> energyAccounts);

    //批量删除加油台账
    AjaxResult deleteAccountByIds(String toStrArray);

    AjaxResult inserts(EnergyAccount e);
    AjaxResult auditunit(EnergyAccount e);

    //批量删除发电台账
    AjaxResult deleteOilGeneraAccountByIds(String ids);

    BigDecimal selectListSum(Audit audit);
}
