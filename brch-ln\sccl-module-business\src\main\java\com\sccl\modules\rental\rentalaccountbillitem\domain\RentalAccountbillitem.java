package com.sccl.modules.rental.rentalaccountbillitem.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;
import java.math.BigDecimal;


/**
 * 车辆 （报账明细）表 rental_accountbillitem
 * 
 * <AUTHOR>
 * @date 2019-08-23
 */
public class RentalAccountbillitem extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
    /** ICT编码 */
    private String ictCode;
    /** ICT名称 */
    private String ictName;
    /** 装移机明细 */
    private String zyjFeeDetailName;
    /** WBS */
    private String wbsNo;
    /** 贷方会计科目 */
    private String creditAccountCode;
    /** 贷方会计科目名称 */
    private String creditAccountName;
    /** 借方科目代码 */
    private String debitAccountCode;
    /** 借方科目名称 */
    private String debitAccountName;
    /** 预算责任中心ID */
    private String responseCenterId;
    /** 预算责任中心名称 */
    private String responseCenterName;
    /** SAP成本中心 */
    private String costCenterCode;
    /** SAP成本中心名称 */
    private String costCenterName;
    /** 利润中心 */
    private String profitsCenterCode;
    /** 利润中心名称 */
    private String profitsCenterName;
    /** 科目明细代码 */
    private String ledgerCategoryDetail;
    /** 内部订单号 */
    private String orderNo;
    /** 转型业务代码 */
    private String transferCode;
    /** 转型业务名称 */
    private String transferName;
    /** 网络元素编号 */
    private String networkElementCode;
    /** 网络元素名称 */
    private String networkElementName;
    /** 客户编号 */
    private String clientCode;
    /** 客户名称 */
    private String clientName;
    /** 产品编号 */
    private String productCode;
    /** 产品名称 */
    private String productName;
    /** 产品组 */
    private String productGroupCode;
    /** 产品组名称 */
    private String productGroupName;
    /** 客户组 */
    private String clientGroupCode;
    /** 客户组名称 */
    private String clientGroupName;
    /** 报帐单子类型 */
    private String childFormType;
    /** 创建日期 */
    private Date createDate;
    /** 被后续操作的金额 */
    private BigDecimal beAfterOperateSum;
    /** GROUP_ITEM_ID */
    private String groupItemId;
    /** 是否特殊库存 */
    private String specialtystorage;
    /** 实耗数量 */
    private BigDecimal realusetotal;
    /** 套餐 */
    private String setlunchtype;
    /** 品牌 */
    private String trademarktype;
    /** 村村通 */
    private String isvillagetouch;
    /** 现金流预算事项编码 */
    private String cashBudgetInstanceCode;
    /** 现金流预算事项名称 */
    private String cashBudgetInstanceName;
    /** wbs项目名称 */
    private String wbsName;
    /** 一次性供应商城市 */
    private String onceSupplierCity;
    /** 一次性供应商名称 */
    private String onceSupplierName;
    /** 是否一次性供应商 */
    private String isOnceSupplier;
    /** 借方会计所属公司编码 */
    private String debitAccountCompanyCode;
    /** 贷方会计所属公司编码 */
    private String creditAccountCompanyCode;
    /** c网业务范围 */
    private String cwebBiztype;
    /** 是否纳税调整 */
    private String isTaxAdjust;
    /** 纳税调整类型 */
    private String taxAdjustType;
    /** 纳税调整金额 */
    private BigDecimal taxAdjustSum;
    /** 项目名称 */
    private String itemName;
    /** 记账码 */
    private String ledgerNo;
    /** 纳税调整说明 */
    private String taxAdjustComments;
    /** 费用明细编码 */
    private String feeDetailCode;
    /** 用途id */
    private String usageId;
    /** 用途名称 */
    private String usageName;
    /** 预算/列账属性类型(1、涉及成本预算、2、不涉及成本预算（非统付）、3、不涉及成本预算（统付）、4、建管费) */
    private String budgetType;
    /** 预算项目ID */
    private String budgetItemId;
    /** 预算项目名称 */
    private String budgetItemName;
    /** 预算活动组ID */
    private String budgetGroupItemId;
    /** 预算活动组名称 */
    private String budgetGroupItemName;
    /** 会计科目编码 */
    private String accountCode;
    /** 会计科目名称 */
    private String accountName;
    /** 会计所属公司编码 */
    private String accountCompanyCode;
    /** 特殊总账标识 */
    private String isSpecialGl;
    /** 统驭科目编码 */
    private String reconciliationAccountCode;
    /** 统驭科目名称 */
    private String reconciliationAccountName;
    /** 是否反记账 */
    private String isAntiPosting;
    /** 综合订单号 */
    private String generalOrderNo;
    /** 人员类别 */
    private String employeeType;
    /** 归口报账单位id */
    private String categoryOrgId;
    /** 电信卡大类 */
    private String telcardType;
    /** 电信卡明细类 */
    private String detailedType;
    /** 印制方式 */
    private String printType;
    /** 单价 */
    private BigDecimal price;
    /** 数量 */
    private BigDecimal amount;
    /** 单位 */
    private String unit;
    /** 使用地点类别 */
    private String locationType;
    /** 耗用类型 */
    private String utype;
    /** 使用类别 */
    private String dtype;
    /** 耗油种类 */
    private String oiltype;
    /** 电信卡用途 */
    private String telcardUsage;
    /** 实物编号或卡片号 */
    private String estateCode;
    /** 实物名称 */
    private String estateName;
    /** 物业类别 */
    private String estateType;
    /** 研发项目 */
    private String rndProject;
    /** 是否代送盘（1标识为是） */
    private String isSubstituteBank;
    /** 是否借方、贷方（1为借方、0为贷方） */
    private String isDebitCredit;
    /** 福利类型ID(包括险种、内退生活费、住房费用等) */
    private String salaryBoonTypeId;
    /** 人员类型ID */
    private String staffTypeId;
    /** 业务类型编码（车辆使用费明细、业务招待费类型、研发类型） */
    private String bizFeeDetailCode;
    /** 业务类型名称 */
    private String bizFeeDetailName;
    /** 所属市局(分公司)code */
    private String cityCode;
    /** 所属市局(分公司)名称 */
    private String cityName;
    /** 培训费开支事项 */
    private String expenditureItems;
    /** 是否培训 */
    private String isTravel;
    /** 业务费用明细 */
    private String bizFeeCode;
    /** 费用明细名称 */
    private String feeDetailName;
    /** 预算项目 code */
    private String budgetItemCode;
    /** 是否摊销（1标识为是） */
    private String isAmortize;
    /** 预算项目类型（1--成本，2--现金流，3--成本+现金流） */
    private String budgetItemType;
    /** 子单排序 */
    private BigDecimal showOrder;
    /** sysdate */
    private Date timestamp;
    /** 税费子单ID */
    private String taxFeeDetailId;
    /** 预算责任中心编码 */
    private String responseCenterCode;
    /** 项目预算编码（以前的活动预算） */
    private String budgetGroupItemCode;
    /** "科目预算标识----1：重点科目2：日常科目3：零星科目" */
    private String budgetItemFlag;
    /** OTHER_SYSTEM_DETAIL_ID */
    private String otherSystemDetailId;
    /** 关联供应商表ID */
    private String mssInstToSupplierId;
    /** 预留字段1 */
    private String reservedOne;
    /** 预留字段2 */
    private String reservedTow;
    /** 预留字段3 */
    private String reservedThree;
    /** 还款金额 */
    private BigDecimal payoffSum;
    /** 核定还款金额 */
    private BigDecimal auditPayoffSum;
    /** 法律事务号 */
    private String lawNo;
    /** 主单id */
    private BigDecimal writeoffInstanceId;
    /** 对应凭证id */
    private String certificationId;
    /** 合计金额 */
    private BigDecimal sum;
    /** 财务核定金额 */
    private BigDecimal auditSum;
    /** 摘要 */
    private String abstractVal;
    /** 预算事项编码（不用存储） */
    private String budgetInstanceCode;
    /** 预算事项名称（不用存储） */
    private String budgetInstanceName;
    /** 修理费明细代码 */
    private String fixFeeDetailCode;
    /** 修理费明细 */
    private String fixFeeDetailName;
    /** 装移机明细代码 */
    private String zyjFeeDetailCode;
    /**  */
    private String status;
    /** 字表id */
    private String detailGuid;


	public void setIctCode(String ictCode)
	{
		this.ictCode = ictCode;
	}

	public String getIctCode() 
	{
		return ictCode;
	}

	public void setIctName(String ictName)
	{
		this.ictName = ictName;
	}

	public String getIctName() 
	{
		return ictName;
	}

	public void setZyjFeeDetailName(String zyjFeeDetailName)
	{
		this.zyjFeeDetailName = zyjFeeDetailName;
	}

	public String getZyjFeeDetailName() 
	{
		return zyjFeeDetailName;
	}

	public void setWbsNo(String wbsNo)
	{
		this.wbsNo = wbsNo;
	}

	public String getWbsNo() 
	{
		return wbsNo;
	}

	public void setCreditAccountCode(String creditAccountCode)
	{
		this.creditAccountCode = creditAccountCode;
	}

	public String getCreditAccountCode() 
	{
		return creditAccountCode;
	}

	public void setCreditAccountName(String creditAccountName)
	{
		this.creditAccountName = creditAccountName;
	}

	public String getCreditAccountName() 
	{
		return creditAccountName;
	}

	public void setDebitAccountCode(String debitAccountCode)
	{
		this.debitAccountCode = debitAccountCode;
	}

	public String getDebitAccountCode() 
	{
		return debitAccountCode;
	}

	public void setDebitAccountName(String debitAccountName)
	{
		this.debitAccountName = debitAccountName;
	}

	public String getDebitAccountName() 
	{
		return debitAccountName;
	}

	public void setResponseCenterId(String responseCenterId)
	{
		this.responseCenterId = responseCenterId;
	}

	public String getResponseCenterId() 
	{
		return responseCenterId;
	}

	public void setResponseCenterName(String responseCenterName)
	{
		this.responseCenterName = responseCenterName;
	}

	public String getResponseCenterName() 
	{
		return responseCenterName;
	}

	public void setCostCenterCode(String costCenterCode)
	{
		this.costCenterCode = costCenterCode;
	}

	public String getCostCenterCode() 
	{
		return costCenterCode;
	}

	public void setCostCenterName(String costCenterName)
	{
		this.costCenterName = costCenterName;
	}

	public String getCostCenterName() 
	{
		return costCenterName;
	}

	public void setProfitsCenterCode(String profitsCenterCode)
	{
		this.profitsCenterCode = profitsCenterCode;
	}

	public String getProfitsCenterCode() 
	{
		return profitsCenterCode;
	}

	public void setProfitsCenterName(String profitsCenterName)
	{
		this.profitsCenterName = profitsCenterName;
	}

	public String getProfitsCenterName() 
	{
		return profitsCenterName;
	}

	public void setLedgerCategoryDetail(String ledgerCategoryDetail)
	{
		this.ledgerCategoryDetail = ledgerCategoryDetail;
	}

	public String getLedgerCategoryDetail() 
	{
		return ledgerCategoryDetail;
	}

	public void setOrderNo(String orderNo)
	{
		this.orderNo = orderNo;
	}

	public String getOrderNo() 
	{
		return orderNo;
	}

	public void setTransferCode(String transferCode)
	{
		this.transferCode = transferCode;
	}

	public String getTransferCode() 
	{
		return transferCode;
	}

	public void setTransferName(String transferName)
	{
		this.transferName = transferName;
	}

	public String getTransferName() 
	{
		return transferName;
	}

	public void setNetworkElementCode(String networkElementCode)
	{
		this.networkElementCode = networkElementCode;
	}

	public String getNetworkElementCode() 
	{
		return networkElementCode;
	}

	public void setNetworkElementName(String networkElementName)
	{
		this.networkElementName = networkElementName;
	}

	public String getNetworkElementName() 
	{
		return networkElementName;
	}

	public void setClientCode(String clientCode)
	{
		this.clientCode = clientCode;
	}

	public String getClientCode() 
	{
		return clientCode;
	}

	public void setClientName(String clientName)
	{
		this.clientName = clientName;
	}

	public String getClientName() 
	{
		return clientName;
	}

	public void setProductCode(String productCode)
	{
		this.productCode = productCode;
	}

	public String getProductCode() 
	{
		return productCode;
	}

	public void setProductName(String productName)
	{
		this.productName = productName;
	}

	public String getProductName() 
	{
		return productName;
	}

	public void setProductGroupCode(String productGroupCode)
	{
		this.productGroupCode = productGroupCode;
	}

	public String getProductGroupCode() 
	{
		return productGroupCode;
	}

	public void setProductGroupName(String productGroupName)
	{
		this.productGroupName = productGroupName;
	}

	public String getProductGroupName() 
	{
		return productGroupName;
	}

	public void setClientGroupCode(String clientGroupCode)
	{
		this.clientGroupCode = clientGroupCode;
	}

	public String getClientGroupCode() 
	{
		return clientGroupCode;
	}

	public void setClientGroupName(String clientGroupName)
	{
		this.clientGroupName = clientGroupName;
	}

	public String getClientGroupName() 
	{
		return clientGroupName;
	}

	public void setChildFormType(String childFormType)
	{
		this.childFormType = childFormType;
	}

	public String getChildFormType() 
	{
		return childFormType;
	}

	public void setCreateDate(Date createDate)
	{
		this.createDate = createDate;
	}

	public Date getCreateDate() 
	{
		return createDate;
	}

	public void setBeAfterOperateSum(BigDecimal beAfterOperateSum)
	{
		this.beAfterOperateSum = beAfterOperateSum;
	}

	public BigDecimal getBeAfterOperateSum() 
	{
		return beAfterOperateSum;
	}

	public void setGroupItemId(String groupItemId)
	{
		this.groupItemId = groupItemId;
	}

	public String getGroupItemId() 
	{
		return groupItemId;
	}

	public void setSpecialtystorage(String specialtystorage)
	{
		this.specialtystorage = specialtystorage;
	}

	public String getSpecialtystorage() 
	{
		return specialtystorage;
	}

	public void setRealusetotal(BigDecimal realusetotal)
	{
		this.realusetotal = realusetotal;
	}

	public BigDecimal getRealusetotal() 
	{
		return realusetotal;
	}

	public void setSetlunchtype(String setlunchtype)
	{
		this.setlunchtype = setlunchtype;
	}

	public String getSetlunchtype() 
	{
		return setlunchtype;
	}

	public void setTrademarktype(String trademarktype)
	{
		this.trademarktype = trademarktype;
	}

	public String getTrademarktype() 
	{
		return trademarktype;
	}

	public void setIsvillagetouch(String isvillagetouch)
	{
		this.isvillagetouch = isvillagetouch;
	}

	public String getIsvillagetouch() 
	{
		return isvillagetouch;
	}

	public void setCashBudgetInstanceCode(String cashBudgetInstanceCode)
	{
		this.cashBudgetInstanceCode = cashBudgetInstanceCode;
	}

	public String getCashBudgetInstanceCode() 
	{
		return cashBudgetInstanceCode;
	}

	public void setCashBudgetInstanceName(String cashBudgetInstanceName)
	{
		this.cashBudgetInstanceName = cashBudgetInstanceName;
	}

	public String getCashBudgetInstanceName() 
	{
		return cashBudgetInstanceName;
	}

	public void setWbsName(String wbsName)
	{
		this.wbsName = wbsName;
	}

	public String getWbsName() 
	{
		return wbsName;
	}

	public void setOnceSupplierCity(String onceSupplierCity)
	{
		this.onceSupplierCity = onceSupplierCity;
	}

	public String getOnceSupplierCity() 
	{
		return onceSupplierCity;
	}

	public void setOnceSupplierName(String onceSupplierName)
	{
		this.onceSupplierName = onceSupplierName;
	}

	public String getOnceSupplierName() 
	{
		return onceSupplierName;
	}

	public void setIsOnceSupplier(String isOnceSupplier)
	{
		this.isOnceSupplier = isOnceSupplier;
	}

	public String getIsOnceSupplier() 
	{
		return isOnceSupplier;
	}

	public void setDebitAccountCompanyCode(String debitAccountCompanyCode)
	{
		this.debitAccountCompanyCode = debitAccountCompanyCode;
	}

	public String getDebitAccountCompanyCode() 
	{
		return debitAccountCompanyCode;
	}

	public void setCreditAccountCompanyCode(String creditAccountCompanyCode)
	{
		this.creditAccountCompanyCode = creditAccountCompanyCode;
	}

	public String getCreditAccountCompanyCode() 
	{
		return creditAccountCompanyCode;
	}

	public void setCwebBiztype(String cwebBiztype)
	{
		this.cwebBiztype = cwebBiztype;
	}

	public String getCwebBiztype() 
	{
		return cwebBiztype;
	}

	public void setIsTaxAdjust(String isTaxAdjust)
	{
		this.isTaxAdjust = isTaxAdjust;
	}

	public String getIsTaxAdjust() 
	{
		return isTaxAdjust;
	}

	public void setTaxAdjustType(String taxAdjustType)
	{
		this.taxAdjustType = taxAdjustType;
	}

	public String getTaxAdjustType() 
	{
		return taxAdjustType;
	}

	public void setTaxAdjustSum(BigDecimal taxAdjustSum)
	{
		this.taxAdjustSum = taxAdjustSum;
	}

	public BigDecimal getTaxAdjustSum() 
	{
		return taxAdjustSum;
	}

	public void setItemName(String itemName)
	{
		this.itemName = itemName;
	}

	public String getItemName() 
	{
		return itemName;
	}

	public void setLedgerNo(String ledgerNo)
	{
		this.ledgerNo = ledgerNo;
	}

	public String getLedgerNo() 
	{
		return ledgerNo;
	}

	public void setTaxAdjustComments(String taxAdjustComments)
	{
		this.taxAdjustComments = taxAdjustComments;
	}

	public String getTaxAdjustComments() 
	{
		return taxAdjustComments;
	}

	public void setFeeDetailCode(String feeDetailCode)
	{
		this.feeDetailCode = feeDetailCode;
	}

	public String getFeeDetailCode() 
	{
		return feeDetailCode;
	}

	public void setUsageId(String usageId)
	{
		this.usageId = usageId;
	}

	public String getUsageId() 
	{
		return usageId;
	}

	public void setUsageName(String usageName)
	{
		this.usageName = usageName;
	}

	public String getUsageName() 
	{
		return usageName;
	}

	public void setBudgetType(String budgetType)
	{
		this.budgetType = budgetType;
	}

	public String getBudgetType() 
	{
		return budgetType;
	}

	public void setBudgetItemId(String budgetItemId)
	{
		this.budgetItemId = budgetItemId;
	}

	public String getBudgetItemId() 
	{
		return budgetItemId;
	}

	public void setBudgetItemName(String budgetItemName)
	{
		this.budgetItemName = budgetItemName;
	}

	public String getBudgetItemName() 
	{
		return budgetItemName;
	}

	public void setBudgetGroupItemId(String budgetGroupItemId)
	{
		this.budgetGroupItemId = budgetGroupItemId;
	}

	public String getBudgetGroupItemId() 
	{
		return budgetGroupItemId;
	}

	public void setBudgetGroupItemName(String budgetGroupItemName)
	{
		this.budgetGroupItemName = budgetGroupItemName;
	}

	public String getBudgetGroupItemName() 
	{
		return budgetGroupItemName;
	}

	public void setAccountCode(String accountCode)
	{
		this.accountCode = accountCode;
	}

	public String getAccountCode() 
	{
		return accountCode;
	}

	public void setAccountName(String accountName)
	{
		this.accountName = accountName;
	}

	public String getAccountName() 
	{
		return accountName;
	}

	public void setAccountCompanyCode(String accountCompanyCode)
	{
		this.accountCompanyCode = accountCompanyCode;
	}

	public String getAccountCompanyCode() 
	{
		return accountCompanyCode;
	}

	public void setIsSpecialGl(String isSpecialGl)
	{
		this.isSpecialGl = isSpecialGl;
	}

	public String getIsSpecialGl() 
	{
		return isSpecialGl;
	}

	public void setReconciliationAccountCode(String reconciliationAccountCode)
	{
		this.reconciliationAccountCode = reconciliationAccountCode;
	}

	public String getReconciliationAccountCode() 
	{
		return reconciliationAccountCode;
	}

	public void setReconciliationAccountName(String reconciliationAccountName)
	{
		this.reconciliationAccountName = reconciliationAccountName;
	}

	public String getReconciliationAccountName() 
	{
		return reconciliationAccountName;
	}

	public void setIsAntiPosting(String isAntiPosting)
	{
		this.isAntiPosting = isAntiPosting;
	}

	public String getIsAntiPosting() 
	{
		return isAntiPosting;
	}

	public void setGeneralOrderNo(String generalOrderNo)
	{
		this.generalOrderNo = generalOrderNo;
	}

	public String getGeneralOrderNo() 
	{
		return generalOrderNo;
	}

	public void setEmployeeType(String employeeType)
	{
		this.employeeType = employeeType;
	}

	public String getEmployeeType() 
	{
		return employeeType;
	}

	public void setCategoryOrgId(String categoryOrgId)
	{
		this.categoryOrgId = categoryOrgId;
	}

	public String getCategoryOrgId() 
	{
		return categoryOrgId;
	}

	public void setTelcardType(String telcardType)
	{
		this.telcardType = telcardType;
	}

	public String getTelcardType() 
	{
		return telcardType;
	}

	public void setDetailedType(String detailedType)
	{
		this.detailedType = detailedType;
	}

	public String getDetailedType() 
	{
		return detailedType;
	}

	public void setPrintType(String printType)
	{
		this.printType = printType;
	}

	public String getPrintType() 
	{
		return printType;
	}

	public void setPrice(BigDecimal price)
	{
		this.price = price;
	}

	public BigDecimal getPrice() 
	{
		return price;
	}

	public void setAmount(BigDecimal amount)
	{
		this.amount = amount;
	}

	public BigDecimal getAmount() 
	{
		return amount;
	}

	public void setUnit(String unit)
	{
		this.unit = unit;
	}

	public String getUnit() 
	{
		return unit;
	}

	public void setLocationType(String locationType)
	{
		this.locationType = locationType;
	}

	public String getLocationType() 
	{
		return locationType;
	}

	public void setUtype(String utype)
	{
		this.utype = utype;
	}

	public String getUtype() 
	{
		return utype;
	}

	public void setDtype(String dtype)
	{
		this.dtype = dtype;
	}

	public String getDtype() 
	{
		return dtype;
	}

	public void setOiltype(String oiltype)
	{
		this.oiltype = oiltype;
	}

	public String getOiltype() 
	{
		return oiltype;
	}

	public void setTelcardUsage(String telcardUsage)
	{
		this.telcardUsage = telcardUsage;
	}

	public String getTelcardUsage() 
	{
		return telcardUsage;
	}

	public void setEstateCode(String estateCode)
	{
		this.estateCode = estateCode;
	}

	public String getEstateCode() 
	{
		return estateCode;
	}

	public void setEstateName(String estateName)
	{
		this.estateName = estateName;
	}

	public String getEstateName() 
	{
		return estateName;
	}

	public void setEstateType(String estateType)
	{
		this.estateType = estateType;
	}

	public String getEstateType() 
	{
		return estateType;
	}

	public void setRndProject(String rndProject)
	{
		this.rndProject = rndProject;
	}

	public String getRndProject() 
	{
		return rndProject;
	}

	public void setIsSubstituteBank(String isSubstituteBank)
	{
		this.isSubstituteBank = isSubstituteBank;
	}

	public String getIsSubstituteBank() 
	{
		return isSubstituteBank;
	}

	public void setIsDebitCredit(String isDebitCredit)
	{
		this.isDebitCredit = isDebitCredit;
	}

	public String getIsDebitCredit() 
	{
		return isDebitCredit;
	}

	public void setSalaryBoonTypeId(String salaryBoonTypeId)
	{
		this.salaryBoonTypeId = salaryBoonTypeId;
	}

	public String getSalaryBoonTypeId() 
	{
		return salaryBoonTypeId;
	}

	public void setStaffTypeId(String staffTypeId)
	{
		this.staffTypeId = staffTypeId;
	}

	public String getStaffTypeId() 
	{
		return staffTypeId;
	}

	public void setBizFeeDetailCode(String bizFeeDetailCode)
	{
		this.bizFeeDetailCode = bizFeeDetailCode;
	}

	public String getBizFeeDetailCode() 
	{
		return bizFeeDetailCode;
	}

	public void setBizFeeDetailName(String bizFeeDetailName)
	{
		this.bizFeeDetailName = bizFeeDetailName;
	}

	public String getBizFeeDetailName() 
	{
		return bizFeeDetailName;
	}

	public void setCityCode(String cityCode)
	{
		this.cityCode = cityCode;
	}

	public String getCityCode() 
	{
		return cityCode;
	}

	public void setCityName(String cityName)
	{
		this.cityName = cityName;
	}

	public String getCityName() 
	{
		return cityName;
	}

	public void setExpenditureItems(String expenditureItems)
	{
		this.expenditureItems = expenditureItems;
	}

	public String getExpenditureItems() 
	{
		return expenditureItems;
	}

	public void setIsTravel(String isTravel)
	{
		this.isTravel = isTravel;
	}

	public String getIsTravel() 
	{
		return isTravel;
	}

	public void setBizFeeCode(String bizFeeCode)
	{
		this.bizFeeCode = bizFeeCode;
	}

	public String getBizFeeCode() 
	{
		return bizFeeCode;
	}

	public void setFeeDetailName(String feeDetailName)
	{
		this.feeDetailName = feeDetailName;
	}

	public String getFeeDetailName() 
	{
		return feeDetailName;
	}

	public void setBudgetItemCode(String budgetItemCode)
	{
		this.budgetItemCode = budgetItemCode;
	}

	public String getBudgetItemCode() 
	{
		return budgetItemCode;
	}

	public void setIsAmortize(String isAmortize)
	{
		this.isAmortize = isAmortize;
	}

	public String getIsAmortize() 
	{
		return isAmortize;
	}

	public void setBudgetItemType(String budgetItemType)
	{
		this.budgetItemType = budgetItemType;
	}

	public String getBudgetItemType() 
	{
		return budgetItemType;
	}

	public void setShowOrder(BigDecimal showOrder)
	{
		this.showOrder = showOrder;
	}

	public BigDecimal getShowOrder() 
	{
		return showOrder;
	}

	public void setTimestamp(Date timestamp)
	{
		this.timestamp = timestamp;
	}

	public Date getTimestamp() 
	{
		return timestamp;
	}

	public void setTaxFeeDetailId(String taxFeeDetailId)
	{
		this.taxFeeDetailId = taxFeeDetailId;
	}

	public String getTaxFeeDetailId() 
	{
		return taxFeeDetailId;
	}

	public void setResponseCenterCode(String responseCenterCode)
	{
		this.responseCenterCode = responseCenterCode;
	}

	public String getResponseCenterCode() 
	{
		return responseCenterCode;
	}

	public void setBudgetGroupItemCode(String budgetGroupItemCode)
	{
		this.budgetGroupItemCode = budgetGroupItemCode;
	}

	public String getBudgetGroupItemCode() 
	{
		return budgetGroupItemCode;
	}

	public void setBudgetItemFlag(String budgetItemFlag)
	{
		this.budgetItemFlag = budgetItemFlag;
	}

	public String getBudgetItemFlag() 
	{
		return budgetItemFlag;
	}

	public void setOtherSystemDetailId(String otherSystemDetailId)
	{
		this.otherSystemDetailId = otherSystemDetailId;
	}

	public String getOtherSystemDetailId() 
	{
		return otherSystemDetailId;
	}

	public void setMssInstToSupplierId(String mssInstToSupplierId)
	{
		this.mssInstToSupplierId = mssInstToSupplierId;
	}

	public String getMssInstToSupplierId() 
	{
		return mssInstToSupplierId;
	}

	public void setReservedOne(String reservedOne)
	{
		this.reservedOne = reservedOne;
	}

	public String getReservedOne() 
	{
		return reservedOne;
	}

	public void setReservedTow(String reservedTow)
	{
		this.reservedTow = reservedTow;
	}

	public String getReservedTow() 
	{
		return reservedTow;
	}

	public void setReservedThree(String reservedThree)
	{
		this.reservedThree = reservedThree;
	}

	public String getReservedThree() 
	{
		return reservedThree;
	}

	public void setPayoffSum(BigDecimal payoffSum)
	{
		this.payoffSum = payoffSum;
	}

	public BigDecimal getPayoffSum() 
	{
		return payoffSum;
	}

	public void setAuditPayoffSum(BigDecimal auditPayoffSum)
	{
		this.auditPayoffSum = auditPayoffSum;
	}

	public BigDecimal getAuditPayoffSum() 
	{
		return auditPayoffSum;
	}

	public void setLawNo(String lawNo)
	{
		this.lawNo = lawNo;
	}

	public String getLawNo() 
	{
		return lawNo;
	}

	public void setWriteoffInstanceId(BigDecimal writeoffInstanceId)
	{
		this.writeoffInstanceId = writeoffInstanceId;
	}

	public BigDecimal getWriteoffInstanceId() 
	{
		return writeoffInstanceId;
	}

	public void setCertificationId(String certificationId)
	{
		this.certificationId = certificationId;
	}

	public String getCertificationId() 
	{
		return certificationId;
	}

	public void setSum(BigDecimal sum)
	{
		this.sum = sum;
	}

	public BigDecimal getSum() 
	{
		return sum;
	}

	public void setAuditSum(BigDecimal auditSum)
	{
		this.auditSum = auditSum;
	}

	public BigDecimal getAuditSum() 
	{
		return auditSum;
	}

	public void setAbstractVal(String abstractVal)
	{
		this.abstractVal = abstractVal;
	}

	public String getAbstractVal()
	{
		return abstractVal;
	}

	public void setBudgetInstanceCode(String budgetInstanceCode)
	{
		this.budgetInstanceCode = budgetInstanceCode;
	}

	public String getBudgetInstanceCode() 
	{
		return budgetInstanceCode;
	}

	public void setBudgetInstanceName(String budgetInstanceName)
	{
		this.budgetInstanceName = budgetInstanceName;
	}

	public String getBudgetInstanceName() 
	{
		return budgetInstanceName;
	}

	public void setFixFeeDetailCode(String fixFeeDetailCode)
	{
		this.fixFeeDetailCode = fixFeeDetailCode;
	}

	public String getFixFeeDetailCode() 
	{
		return fixFeeDetailCode;
	}

	public void setFixFeeDetailName(String fixFeeDetailName)
	{
		this.fixFeeDetailName = fixFeeDetailName;
	}

	public String getFixFeeDetailName() 
	{
		return fixFeeDetailName;
	}

	public void setZyjFeeDetailCode(String zyjFeeDetailCode)
	{
		this.zyjFeeDetailCode = zyjFeeDetailCode;
	}

	public String getZyjFeeDetailCode() 
	{
		return zyjFeeDetailCode;
	}

	public void setStatus(String status)
	{
		this.status = status;
	}

	public String getStatus() 
	{
		return status;
	}

	public void setDetailGuid(String detailGuid)
	{
		this.detailGuid = detailGuid;
	}

	public String getDetailGuid() 
	{
		return detailGuid;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("ictCode", getIctCode())
            .append("ictName", getIctName())
            .append("zyjFeeDetailName", getZyjFeeDetailName())
            .append("wbsNo", getWbsNo())
            .append("creditAccountCode", getCreditAccountCode())
            .append("creditAccountName", getCreditAccountName())
            .append("debitAccountCode", getDebitAccountCode())
            .append("debitAccountName", getDebitAccountName())
            .append("responseCenterId", getResponseCenterId())
            .append("responseCenterName", getResponseCenterName())
            .append("costCenterCode", getCostCenterCode())
            .append("costCenterName", getCostCenterName())
            .append("profitsCenterCode", getProfitsCenterCode())
            .append("profitsCenterName", getProfitsCenterName())
            .append("ledgerCategoryDetail", getLedgerCategoryDetail())
            .append("orderNo", getOrderNo())
            .append("transferCode", getTransferCode())
            .append("transferName", getTransferName())
            .append("networkElementCode", getNetworkElementCode())
            .append("networkElementName", getNetworkElementName())
            .append("clientCode", getClientCode())
            .append("clientName", getClientName())
            .append("productCode", getProductCode())
            .append("productName", getProductName())
            .append("productGroupCode", getProductGroupCode())
            .append("productGroupName", getProductGroupName())
            .append("clientGroupCode", getClientGroupCode())
            .append("clientGroupName", getClientGroupName())
            .append("childFormType", getChildFormType())
            .append("createDate", getCreateDate())
            .append("beAfterOperateSum", getBeAfterOperateSum())
            .append("groupItemId", getGroupItemId())
            .append("specialtystorage", getSpecialtystorage())
            .append("realusetotal", getRealusetotal())
            .append("setlunchtype", getSetlunchtype())
            .append("trademarktype", getTrademarktype())
            .append("isvillagetouch", getIsvillagetouch())
            .append("cashBudgetInstanceCode", getCashBudgetInstanceCode())
            .append("cashBudgetInstanceName", getCashBudgetInstanceName())
            .append("wbsName", getWbsName())
            .append("onceSupplierCity", getOnceSupplierCity())
            .append("onceSupplierName", getOnceSupplierName())
            .append("isOnceSupplier", getIsOnceSupplier())
            .append("debitAccountCompanyCode", getDebitAccountCompanyCode())
            .append("creditAccountCompanyCode", getCreditAccountCompanyCode())
            .append("cwebBiztype", getCwebBiztype())
            .append("isTaxAdjust", getIsTaxAdjust())
            .append("taxAdjustType", getTaxAdjustType())
            .append("taxAdjustSum", getTaxAdjustSum())
            .append("itemName", getItemName())
            .append("ledgerNo", getLedgerNo())
            .append("taxAdjustComments", getTaxAdjustComments())
            .append("feeDetailCode", getFeeDetailCode())
            .append("usageId", getUsageId())
            .append("usageName", getUsageName())
            .append("budgetType", getBudgetType())
            .append("budgetItemId", getBudgetItemId())
            .append("budgetItemName", getBudgetItemName())
            .append("budgetGroupItemId", getBudgetGroupItemId())
            .append("budgetGroupItemName", getBudgetGroupItemName())
            .append("accountCode", getAccountCode())
            .append("accountName", getAccountName())
            .append("accountCompanyCode", getAccountCompanyCode())
            .append("isSpecialGl", getIsSpecialGl())
            .append("reconciliationAccountCode", getReconciliationAccountCode())
            .append("reconciliationAccountName", getReconciliationAccountName())
            .append("isAntiPosting", getIsAntiPosting())
            .append("generalOrderNo", getGeneralOrderNo())
            .append("employeeType", getEmployeeType())
            .append("categoryOrgId", getCategoryOrgId())
            .append("telcardType", getTelcardType())
            .append("detailedType", getDetailedType())
            .append("printType", getPrintType())
            .append("price", getPrice())
            .append("amount", getAmount())
            .append("unit", getUnit())
            .append("locationType", getLocationType())
            .append("utype", getUtype())
            .append("dtype", getDtype())
            .append("oiltype", getOiltype())
            .append("telcardUsage", getTelcardUsage())
            .append("estateCode", getEstateCode())
            .append("estateName", getEstateName())
            .append("estateType", getEstateType())
            .append("rndProject", getRndProject())
            .append("isSubstituteBank", getIsSubstituteBank())
            .append("isDebitCredit", getIsDebitCredit())
            .append("salaryBoonTypeId", getSalaryBoonTypeId())
            .append("staffTypeId", getStaffTypeId())
            .append("bizFeeDetailCode", getBizFeeDetailCode())
            .append("bizFeeDetailName", getBizFeeDetailName())
            .append("cityCode", getCityCode())
            .append("cityName", getCityName())
            .append("expenditureItems", getExpenditureItems())
            .append("isTravel", getIsTravel())
            .append("bizFeeCode", getBizFeeCode())
            .append("feeDetailName", getFeeDetailName())
            .append("budgetItemCode", getBudgetItemCode())
            .append("isAmortize", getIsAmortize())
            .append("budgetItemType", getBudgetItemType())
            .append("showOrder", getShowOrder())
            .append("timestamp", getTimestamp())
            .append("taxFeeDetailId", getTaxFeeDetailId())
            .append("responseCenterCode", getResponseCenterCode())
            .append("budgetGroupItemCode", getBudgetGroupItemCode())
            .append("budgetItemFlag", getBudgetItemFlag())
            .append("otherSystemDetailId", getOtherSystemDetailId())
            .append("mssInstToSupplierId", getMssInstToSupplierId())
            .append("reservedOne", getReservedOne())
            .append("reservedTow", getReservedTow())
            .append("reservedThree", getReservedThree())
            .append("payoffSum", getPayoffSum())
            .append("auditPayoffSum", getAuditPayoffSum())
            .append("lawNo", getLawNo())
            .append("writeoffInstanceId", getWriteoffInstanceId())
            .append("certificationId", getCertificationId())
            .append("sum", getSum())
            .append("auditSum", getAuditSum())
            .append("abstractVal", getAbstractVal())
            .append("budgetInstanceCode", getBudgetInstanceCode())
            .append("budgetInstanceName", getBudgetInstanceName())
            .append("fixFeeDetailCode", getFixFeeDetailCode())
            .append("fixFeeDetailName", getFixFeeDetailName())
            .append("zyjFeeDetailCode", getZyjFeeDetailCode())
            .append("status", getStatus())
            .append("detailGuid", getDetailGuid())
            .toString();
    }
}
