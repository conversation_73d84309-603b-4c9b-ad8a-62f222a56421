package com.sccl.modules.business.stationinfo.service;

import cn.hutool.core.collection.CollUtil;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.business.stationinfo.domain.PowerStationInfoRJtlte;
import com.sccl.modules.business.stationinfo.mapper.PowerStationInfoRJtlteMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 集团LTE和局站对应 服务层实现
 *
 * <AUTHOR>
 * @date 2021-05-13
 */
@Service
@Slf4j
public class PowerStationInfoRJtlteServiceImpl extends BaseServiceImpl<PowerStationInfoRJtlte> implements IPowerStationInfoRJtlteService {
    @Autowired
    private PowerStationInfoRJtlteMapper powerStationInfoRJtlteMapper;

    @Override
    public int addStationMap(PowerStationInfoRJtlte powerStationInfoRJtlte) {
        Map<String, Object> paramMap = new HashMap<>(2);
        log.info("powerStationInfoRJtlte={}", powerStationInfoRJtlte);
        paramMap.put("stationid", powerStationInfoRJtlte.getStationid());
        paramMap.put("billid", powerStationInfoRJtlte.getBillId());

        //paramMap.put("sendstatus",powerStationInfoRJtlte.getSendstatus());
        List<PowerStationInfoRJtlte> retl = powerStationInfoRJtlteMapper.getListjtltermap(paramMap);
        if (CollUtil.isNotEmpty(retl)) {
            for (PowerStationInfoRJtlte stationInfoRJtlte : retl) {
                powerStationInfoRJtlte.setId(stationInfoRJtlte.getId());
                powerStationInfoRJtlteMapper.updateForModel(powerStationInfoRJtlte);
            }
        } else {
            powerStationInfoRJtlteMapper.insert(powerStationInfoRJtlte);
        }
        return 0;
    }

    @Override
    public String relevanceStationRJt(PowerStationInfoRJtlte psij) {
        Long stationid = psij.getStationid();

        log.info("解除中间表 对应stationid={}存在关联数据", stationid);
        int n = powerStationInfoRJtlteMapper.disassociate(stationid);

        log.info("关联已解除，开始产生新的关联");
        int n2 = powerStationInfoRJtlteMapper.newAssociation(psij);

        return String.format("stationid=%s 与5g站址：%s的已关联", stationid, psij.getJtlteCode());
    }
}
