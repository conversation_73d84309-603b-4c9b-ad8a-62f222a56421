package com.sccl.modules.mssaccount.mssinterface.domain;

import lombok.Data;

import java.io.Serializable;

/**
 * 推送到大数据平台的 电价明细清单 信息
 */
@Data
public class PowerElePriceItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 省份
     */
    private String provinceCode;
    /**
     * 市局组织编码/财辅组织
     */
    private String cityCode;//
    /**
     * 市局组织名称/财辅组织
     */
    private String cityName;
    /**
     * 区县组织编码/财辅组织
     */
    private String countyCode;
    /**
     * 区县组织名称/财辅组织
     */
    private String countyName;
    /**
     * 账期，格式202201
     */
    private String budgetSet;
    /**
     * 局站编码
     */
    private String stationCode;
    /**
     * 局站名称
     */
    private String stationName;
    /**
     * 电表编码
     */
    private String energyMeterCode;
    /**
     * 电表名称
     */
    private String energyMeterName;
    /**
     * 不含税单价
     */
    private String electricityPrice;

    public PowerElePriceItem() {
    }
}
