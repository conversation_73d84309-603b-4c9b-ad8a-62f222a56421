package com.sccl.modules.mssaccount.mssinterface.domain;

import com.sccl.framework.aspectj.lang.annotation.Log;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

//电表信息
@EqualsAndHashCode
@ToString
public class MeterInfo2 implements Serializable {


    //    参数名	类型	描述	值条件	备注
    private String energyType;
    private String typeStationCode;
    //    {"code":"-1","msg":"子资源模型中的报账用途[usage]字段不能为空,且长度为(1),本次数据同步失败!"}
    private String contractPrice;
    private String provinceCode;
    //    cityCode	String	市局组织编码	必填	财辅组织
    private String cityCode;
    //    cityName	String	市局组织名称	必填	财辅组织
    private String cityName;
    //    countyCode	String	区县组织编码	必填	财辅组织
    private String countyCode;
    //    countyName	String	区县组织名称	必填	财辅组织
    private String countyName;
    //    energyMeterCode	String	电表编码	必填
    private String energyMeterCode;
    //    energyMeterName	String	电表名称	必填
    private String energyMeterName;
    //    status	String	电表状态	必填	状态：1:在用  2:停用  3:闲置
    private String status;
    //    usage	String	电表用途	必填
    private String usage;
    //    type	String	同步类型	必填 1，2，3 新增，修改，作废
    private String type;
    //    stationCode	String	局站编码	必填
    private String stationCode;
    //    stationName	String	局站名称	必填
    private String stationName;
    //    stationLocation	String	局站地址	必填
    private String stationLocation;
    //    stationStatus	String	局站状态	必填
    private String stationStatus;
    //    stationType	String	局站类型	必填
    private String stationType;
    //    largeIndustrialElectricityFlag	String	大工业标识	必填	1：大工业用电，0：非大工业用电
    private String largeIndustrialElectricityFlag;
    //    energySupplyWay	String	对外结算类型(供电方式)	必填	供电方式 1：直供 2：转供 3：外租
    private String energySupplyWay;
    private String siteCode;
    //电网公司电表编码
    private String powerGridEnergyMeterCode;

    public MeterInfo2() {
    }

    public MeterInfo2(String energyType, String typeStationCode, String contractPrice, String provinceCode,
                      String cityCode, String cityName, String countyCode, String countyName, String energyMeterCode,
                      String energyMeterName, String status, String usage, String type, String stationCode,
                      String stationName, String stationLocation, String stationStatus, String stationType,
                      String largeIndustrialElectricityFlag, String energySupplyWay, String siteCode,
                      String powerGridEnergyMeterCode) {
        this.energyType = energyType;
        this.typeStationCode = typeStationCode;
        this.contractPrice = contractPrice;
        this.provinceCode = provinceCode;
        this.cityCode = cityCode;
        this.cityName = cityName;
        this.countyCode = countyCode;
        this.countyName = countyName;
        this.energyMeterCode = energyMeterCode;
        this.energyMeterName = energyMeterName;
        this.status = status;
        this.usage = usage;
        this.type = type;
        this.stationCode = stationCode;
        this.stationName = stationName;
        this.stationLocation = stationLocation;
        this.stationStatus = stationStatus;
        this.stationType = stationType;
        this.largeIndustrialElectricityFlag = largeIndustrialElectricityFlag;
        this.energySupplyWay = energySupplyWay;
        this.siteCode = siteCode;
        this.powerGridEnergyMeterCode = powerGridEnergyMeterCode;
    }

    public static void main(String[] args) {

        System.out.println(processStationType("131"));

    }

    public static List<MeterInfo2> deduplicateMeterInfoList(List<MeterInfo2> meterInfo2s) {
        HashSet<String> uniqueKeys = new HashSet<>();
        List<MeterInfo2> resultList = new ArrayList<>();
        meterInfo2s.forEach(
          item -> {
              String key = item.getProvinceCode() + item.getCityCode() +
                item.getCountyCode() + item.getEnergyMeterCode();
              if (!uniqueKeys.contains(key)) {
                  resultList.add(item);
                  uniqueKeys.add(key);
              }
          }
        );
        return resultList;
    }

    public static void ProcessData(MeterInfo2 item) {
        item.setPowerGridEnergyMeterCode(processData2(item.getPowerGridEnergyMeterCode()));
        item.setStationType(processStationType(item.getStationType()));
    }

    private static String processStationType(String str) {
        if (str.length() < 4) {
            StringBuilder sb = new StringBuilder(str);
            while (sb.length() < 4) {
                sb.append("0");
            }
            return sb.toString();
        }
        return str;
    }


    private static String processData2(String data) {
        return (data != null) && (!data.isEmpty()) ? data : "";
    }

    public String getEnergyType() {
        return energyType;
    }

    public void setEnergyType(String energyType) {
        this.energyType = energyType;
    }

    public String getTypeStationCode() {
        return typeStationCode;
    }

    public void setTypeStationCode(String typeStationCode) {
        this.typeStationCode = typeStationCode;
    }

    public String getContractPrice() {
        return contractPrice;
    }

    public void setContractPrice(String contractPrice) {
        this.contractPrice = contractPrice;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getCountyCode() {
        return countyCode;
    }

    public void setCountyCode(String countyCode) {
        this.countyCode = countyCode;
    }

    public String getCountyName() {
        return countyName;
    }

    public void setCountyName(String countyName) {
        this.countyName = countyName;
    }

    public String getEnergyMeterCode() {
        return energyMeterCode;
    }

    public void setEnergyMeterCode(String energyMeterCode) {
        this.energyMeterCode = energyMeterCode;
    }

    public String getEnergyMeterName() {
        return energyMeterName;
    }

    public void setEnergyMeterName(String energyMeterName) {
        this.energyMeterName = energyMeterName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getUsage() {
        return usage;
    }

    public void setUsage(String usage) {
        this.usage = usage;
    }
//    provinceCode	String	省级编码	必填

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStationCode() {
        return stationCode;
    }

    public void setStationCode(String stationCode) {
        this.stationCode = stationCode;
    }

    public String getStationName() {
        return stationName;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }

    public String getStationLocation() {
        return stationLocation;
    }

    public void setStationLocation(String stationLocation) {
        this.stationLocation = stationLocation;
    }

    public String getStationStatus() {
        return stationStatus;
    }

    public void setStationStatus(String stationStatus) {
        this.stationStatus = stationStatus;
    }

    public String getStationType() {
        return stationType;
    }

    public void setStationType(String stationType) {
        this.stationType = stationType;
    }

    public String getLargeIndustrialElectricityFlag() {
        return largeIndustrialElectricityFlag;
    }

    public void setLargeIndustrialElectricityFlag(String largeIndustrialElectricityFlag) {
        this.largeIndustrialElectricityFlag = largeIndustrialElectricityFlag;
    }

    public String getEnergySupplyWay() {
        return energySupplyWay;
    }

    public void setEnergySupplyWay(String energySupplyWay) {
        this.energySupplyWay = energySupplyWay;
    }

    public String getSiteCode() {
        return siteCode;
    }

    public void setSiteCode(String siteCode) {
        this.siteCode = siteCode;
    }

    public String getPowerGridEnergyMeterCode() {
        return powerGridEnergyMeterCode;
    }

    public void setPowerGridEnergyMeterCode(String powerGridEnergyMeterCode) {
        this.powerGridEnergyMeterCode = powerGridEnergyMeterCode;
    }

    public void checkCompany() {
        if (this.getSiteCode() == null) {
            setSiteCode("");
        }
        if (this.getPowerGridEnergyMeterCode() == null) {
            setPowerGridEnergyMeterCode("");
        }
        if (this.getStationLocation() == null) {
            setStationLocation("未知");
        }
        if (this.getContractPrice() == null) {
            setContractPrice("0");
        }
        if (this.getCountyName() == null) {
            setCountyName("未知");
        }
        if (this.getStationName() == null) {
            setStationName("未知");
        }
    }


}
