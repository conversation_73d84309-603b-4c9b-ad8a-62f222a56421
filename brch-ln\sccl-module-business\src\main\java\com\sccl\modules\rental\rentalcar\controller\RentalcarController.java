package com.sccl.modules.rental.rentalcar.controller;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;

import java.util.Map;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.rental.rentalcar.domain.Rentalcar;
import com.sccl.modules.rental.rentalcar.service.IRentalcarService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 车辆  信息操作处理
 *
 * <AUTHOR>
 * @date 2019-08-23
 */
@RestController
@RequestMapping("/rental/rentalcar")
public class RentalcarController extends BaseController {
    private String prefix = "rental/rentalcar";

    @Autowired
    private IRentalcarService rentalcarService;

    @RequiresPermissions("rental:rentalcar:view")
    @GetMapping()
    public String rentalcar() {
        return prefix + "/rentalcar";
    }

    /**
     * 查询车辆 列表
     */
    @RequiresPermissions("rental:rentalcar:list")
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(Rentalcar rentalcar) {
        startPage();
        List<Rentalcar> list = rentalcarService.selectList(rentalcar);
        return getDataTable(list);
    }

    /**
     * 新增车辆
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存车辆
     */
    @RequiresPermissions("rental:rentalcar:add")
    //@Log(title = "车辆 ", action = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody Rentalcar rentalcar) {
        return toAjax(rentalcarService.insert(rentalcar));
    }

    /**
     * 修改车辆
     */
    @GetMapping("/edit/{rcid}")
    public AjaxResult edit(@PathVariable("rcid") Long rcid) {
        Rentalcar rentalcar = rentalcarService.get(rcid);

        Object object = JSONObject.toJSON(rentalcar);

        return this.success(object);
    }

    /**
     * 修改保存车辆
     */
    @RequiresPermissions("rental:rentalcar:edit")
    //@Log(title = "车辆 ", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody Rentalcar rentalcar) {
        return toAjax(rentalcarService.update(rentalcar));
    }

    /**
     * 删除车辆
     */
    @RequiresPermissions("rental:rentalcar:remove")
    //@Log(title = "车辆 ", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {

        return toAjax(rentalcarService.deleteByIdsDB(Convert.toStrArray(ids)));
    }


    /**
     * 查看车辆
     */
    @RequiresPermissions("rental:rentalcar:view")
    @GetMapping("/view/{rcid}")
    @ResponseBody
    public AjaxResult view(@PathVariable("rcid") Long rcid) {
        Rentalcar rentalcar = rentalcarService.get(rcid);

        Object object = JSONObject.toJSON(rentalcar);

        return this.success(object);
    }

    /**
     * @Description: 根据车牌号查询月租金
     * @author: dongk
     * @date: 2019/9/4
     * @param:
     * @return:
     */
	@PostMapping( "/selectByVin")
	@ResponseBody
	public Map<String, Object> selectByVin(Rentalcar vin)
	{
		Rentalcar obj = rentalcarService.selectByVin(vin);
		Map<String, Object> map = new HashMap<>();
		if(obj != null){
			map.put("index",true);
			map.put("obj",obj);
		}else {
			map.put("index",false);
		}
		return map;
	}

}
