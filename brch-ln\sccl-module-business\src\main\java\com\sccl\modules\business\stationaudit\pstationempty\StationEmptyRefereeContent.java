package com.sccl.modules.business.stationaudit.pstationempty;

import com.enrising.dcarbon.audit.AbstractRefereeContent;
import com.enrising.dcarbon.audit.RefereeDatasource;
import com.enrising.dcarbon.audit.RefereeResult;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StationEmptyRefereeContent extends AbstractRefereeContent implements RefereeDatasource, Serializable {
    private Long billId;
    private Long pcid;

    private String ammetername;

    private String stationCode;
    private String stationname;
    private String resstationname;
    private String address;


    public StationEmptyRefereeContent(RefereeResult refereeResult, int step, String auditKey) {
        super(refereeResult, step, auditKey);
    }


}
