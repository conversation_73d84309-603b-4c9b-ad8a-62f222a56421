package com.sccl.modules.business.meterdatesfortwoc.domain;

import cn.hutool.core.bean.BeanUtil;
import com.sccl.framework.web.domain.BaseEntity;
import com.sccl.modules.business.twoc.domain.TwoCFlag;
import com.sccl.modules.mssaccount.mssinterface.domain.WriteoffDetailInfo2;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;


/**
 * 双碳接口同步电实际用电量数据表 meterdatesfortwoc
 *
 * <AUTHOR>
 * @date 2023-04-14
 */
public class Meterdatesfortwoc extends BaseEntity implements TwoCFlag {
    private static final long serialVersionUID = 1L;

    /**
     * 地市编码
     */
    private String subjectcode;
    /**
     * 用能月份 YYYYMM
     */
    private String statisperiod;
    /**
     * 归属类型 1:集团存续 2:股份上市
     */
    private String grouptype;
    /**
     * 财辅系统报账单号
     */
    private String billcode;
    /**
     * 用量属性 1：预估量 2：实际使用量
     */
    private String amounttype;
    /**
     * 电表编码
     */
    private String energymetercode;
    /**
     * 电表名称
     */
    private String energymetername;
    /**
     * 用电开始时间 YYYYMMDD 用电区间不可重叠，具体看协议
     */
    private String electricitystartdate;
    /**
     * 用电截止时间 YYYYMMDD 同上
     */
    private String electricityenddate;
    /**
     * 本次电量
     */
    private String thisquantityofelectricity;
    /**
     * 本次电费
     */
    private String thiselectricitycharge;
    /**
     * 设备电费
     */
    private String powerconsumption;
    /**
     * it设备电费
     */
    private String itdevicepowertotal;
    /**
     * 回收电费标识 ：1为回收电费 0为非回收电费标识
     */
    private String recoveryelectricityflag;
    /**
     * 合同电价
     */
    private String contractprice;
    /**
     * 创建时间
     */
    private Date createtime;
    /**
     * 同步标志 0未同步，1同步成功 2同步失败
     */
    private Integer syncflag;
    /**
     *
     */
    private String failmag;

    public static Meterdatesfortwoc convert(WriteoffDetailInfo2 writeoffDetailInfo2) {
        Meterdatesfortwoc infodb = new Meterdatesfortwoc();
        BeanUtil.copyProperties(writeoffDetailInfo2, infodb, true);
        infodb.setStatisperiod(writeoffDetailInfo2.getBudgetSet());
        infodb.setGrouptype("1");
        infodb.setAmounttype("2");
        infodb.setItdevicepowertotal("0");
        return infodb;


    }

    public String getSubjectcode() {
        return subjectcode;
    }

    public void setSubjectcode(String subjectcode) {
        this.subjectcode = subjectcode;
    }

    public String getStatisperiod() {
        return statisperiod;
    }

    public void setStatisperiod(String statisperiod) {
        this.statisperiod = statisperiod;
    }

    public String getGrouptype() {
        return grouptype;
    }

    public void setGrouptype(String grouptype) {
        this.grouptype = grouptype;
    }

    public String getBillcode() {
        return billcode;
    }

    public void setBillcode(String billcode) {
        this.billcode = billcode;
    }

    public String getAmounttype() {
        return amounttype;
    }

    public void setAmounttype(String amounttype) {
        this.amounttype = amounttype;
    }

    public String getEnergymetercode() {
        return energymetercode;
    }

    public void setEnergymetercode(String energymetercode) {
        this.energymetercode = energymetercode;
    }

    public String getEnergymetername() {
        return energymetername;
    }

    public void setEnergymetername(String energymetername) {
        this.energymetername = energymetername;
    }

    public String getElectricitystartdate() {
        return electricitystartdate;
    }

    public void setElectricitystartdate(String electricitystartdate) {
        this.electricitystartdate = electricitystartdate;
    }

    public String getElectricityenddate() {
        return electricityenddate;
    }

    public void setElectricityenddate(String electricityenddate) {
        this.electricityenddate = electricityenddate;
    }

    public String getThisquantityofelectricity() {
        return thisquantityofelectricity;
    }

    public void setThisquantityofelectricity(String thisquantityofelectricity) {
        this.thisquantityofelectricity = thisquantityofelectricity;
    }

    public String getThiselectricitycharge() {
        return thiselectricitycharge;
    }

    public void setThiselectricitycharge(String thiselectricitycharge) {
        this.thiselectricitycharge = thiselectricitycharge;
    }

    public String getPowerconsumption() {
        return powerconsumption;
    }

    public void setPowerconsumption(String powerconsumption) {
        this.powerconsumption = powerconsumption;
    }

    public String getItdevicepowertotal() {
        return itdevicepowertotal;
    }

    public void setItdevicepowertotal(String itdevicepowertotal) {
        this.itdevicepowertotal = itdevicepowertotal;
    }

    public String getRecoveryelectricityflag() {
        return recoveryelectricityflag;
    }

    public void setRecoveryelectricityflag(String recoveryelectricityflag) {
        this.recoveryelectricityflag = recoveryelectricityflag;
    }

    public String getContractprice() {
        return contractprice;
    }

    public void setContractprice(String contractprice) {
        this.contractprice = contractprice;
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public Integer getSyncflag() {
        return syncflag;
    }

    public void setSyncflag(Integer syncflag) {
        this.syncflag = syncflag;
    }

    public String getFailmag() {
        return failmag;
    }

    public void setFailmag(String failmag) {
        this.failmag = failmag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("subjectcode", getSubjectcode())
                .append("statisperiod", getStatisperiod())
                .append("grouptype", getGrouptype())
                .append("billcode", getBillcode())
                .append("amounttype", getAmounttype())
                .append("energymetercode", getEnergymetercode())
                .append("energymetername", getEnergymetername())
                .append("electricitystartdate", getElectricitystartdate())
                .append("electricityenddate", getElectricityenddate())
                .append("thisquantityofelectricity", getThisquantityofelectricity())
                .append("thiselectricitycharge", getThiselectricitycharge())
                .append("powerconsumption", getPowerconsumption())
                .append("itdevicepowertotal", getItdevicepowertotal())
                .append("recoveryelectricityflag", getRecoveryelectricityflag())
                .append("contractprice", getContractprice())
                .append("delFlag", getDelFlag())
                .append("createtime", getCreatetime())
                .append("syncflag", getSyncflag())
                .append("failmag", getFailmag())
                .toString();
    }
}
