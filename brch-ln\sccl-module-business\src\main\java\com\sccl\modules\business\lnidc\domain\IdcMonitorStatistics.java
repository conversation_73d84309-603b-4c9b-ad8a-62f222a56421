package com.sccl.modules.business.lnidc.domain;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.sccl.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode
@TableName(value = "idc_monitor_statistics")
public class IdcMonitorStatistics extends BaseEntity  {

    private static final long serialVersionUID = 1L;

    private Long id = IdWorker.getId();

    /**
     * 地市
     */
    private Long company;

    /** 责任中心 */
    private Long country;

    /**
     * 地市名称
     */
    private String companyName;

    /**
     * 本省idc名称
     */
    private String idcName;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备ip地址
     */
    private String ipAddr;

    /**
     * 设备厂家
     */
    private String deviceFactory;

    /**
     * 设备型号
     */
    private String deviceType;

    /**
     * 所属系统或平台
     */
    private String ownPlatform;

    /**
     * 维护部门
     */
    private String preserveDept;

    /**
     * 维护人
     */
    private String preservePerson;

    /**
     * 设备入网时间
     */
    private String deviceAccessTime;

    /**
     * 额定功率（w）
     */
    private String ratedPower;

    /**
     * 查询实际功率
     */
    private String realPower;


}
