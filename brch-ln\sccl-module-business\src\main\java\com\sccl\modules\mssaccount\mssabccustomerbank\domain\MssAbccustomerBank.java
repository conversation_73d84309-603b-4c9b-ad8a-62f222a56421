package com.sccl.modules.mssaccount.mssabccustomerbank.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;
import java.math.BigDecimal;


/**
 * 客户银行表 MSS_ABCCUSTOMER_BANK
 * 
 * <AUTHOR>
 * @date 2019-05-01
 */
public class MssAbccustomerBank extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
	private BigDecimal iId;
    public BigDecimal getIId() {
		return iId;
	}

	public void setIId(BigDecimal iId) {
		this.iId = iId;
	}

	/**  */
    private BigDecimal pId;
    /** 客户编号 */
    private String kunnr;
    /** 银行国家代码 */
    private String banks;
    /** 银行代码 */
    private String bankl;
    /** 银行名称 */
    private String zbanka;
    /** 银行帐户号码 */
    private String bankn;
    /** 银行户主（开户名） */
    private String koinh;
    /** 对公or对私 */
    private String publicPrivate;
    /**  */
    private Date infDate;
    /** 0 del 1 insert */
    private String infStatus;

    private String provz;// 省份
    private String city;//地市
    private String bgrup;

	public String getProvz() {
		return provz;
	}

	public void setProvz(String provz) {
		this.provz = provz;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getBgrup() {
		return bgrup;
	}

	public void setBgrup(String bgrup) {
		this.bgrup = bgrup;
	}

	public void setPId(BigDecimal pId)
	{
		this.pId = pId;
	}

	public BigDecimal getPId() 
	{
		return pId;
	}


	public void setKunnr(String kunnr)
	{
		this.kunnr = kunnr;
	}

	public String getKunnr() 
	{
		return kunnr;
	}

	public void setBanks(String banks)
	{
		this.banks = banks;
	}

	public String getBanks() 
	{
		return banks;
	}

	public void setBankl(String bankl)
	{
		this.bankl = bankl;
	}

	public String getBankl() 
	{
		return bankl;
	}

	public void setZbanka(String zbanka)
	{
		this.zbanka = zbanka;
	}

	public String getZbanka() 
	{
		return zbanka;
	}

	public void setBankn(String bankn)
	{
		this.bankn = bankn;
	}

	public String getBankn() 
	{
		return bankn;
	}

	public void setKoinh(String koinh)
	{
		this.koinh = koinh;
	}

	public String getKoinh() 
	{
		return koinh;
	}

	public void setPublicPrivate(String publicPrivate)
	{
		this.publicPrivate = publicPrivate;
	}

	public String getPublicPrivate() 
	{
		return publicPrivate;
	}

	public void setInfDate(Date infDate)
	{
		this.infDate = infDate;
	}

	public Date getInfDate() 
	{
		return infDate;
	}

	public void setInfStatus(String infStatus)
	{
		this.infStatus = infStatus;
	}

	public String getInfStatus() 
	{
		return infStatus;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("pId", getPId())
            .append("iId", getIId())
            .append("kunnr", getKunnr())
            .append("banks", getBanks())
            .append("bankl", getBankl())
            .append("zbanka", getZbanka())
            .append("bankn", getBankn())
            .append("koinh", getKoinh())
            .append("publicPrivate", getPublicPrivate())
            .append("infDate", getInfDate())
            .append("infStatus", getInfStatus())
            .append("provz", getProvz())
            .append("city", getCity())
            .append("bgrup", getBgrup())
            .toString();
    }
	private BigDecimal sum;//获取前台数据
	public BigDecimal getSum() {
		return sum;
	}

	public void setSum(BigDecimal sum) {
		this.sum = sum;
	}
}
