package com.sccl.modules.mssaccount.mssinterface.domain;

import java.io.Serializable;

//电表报账单明细信息

public class WriteoffDetailInfo2 implements Serializable {


    //    private String scene="1";
    private String budgetSet;
    //电表编码	必填
    private String energyMeterCode;
    private String energyMeterName;
    private String electricityStartDate;
    private String electricityEndDate;
    private String thisQuantityOfElectricity;
    private String thisElectricityCharge;
    private String powerConsumption;
    private String contractPrice;
    private String recoveryElectricityFlag;
    private String thisElectricityPrice;
    private String thisElectricityTax;
    //账期	非必填	格式：YYYYMM  （当type为1和2时必填）
    private String ccoer = "";
    private String cdcf = "";
    private String generatorCode = "";
    private String electricPowerGeneration = "";
    public WriteoffDetailInfo2() {
    }

    public WriteoffDetailInfo2(String budgetSet, String energyMeterCode, String energyMeterName,
                               String electricityStartDate, String electricityEndDate,
                               String thisQuantityOfElectricity, String thisElectricityCharge,
                               String powerConsumption, String contractPrice, String recoveryElectricityFlag,
                               String thisElectricityPrice, String thisElectricityTax, String ccoer, String cdcf,
                               String generatorCode, String electricPowerGeneration) {
        this.budgetSet = budgetSet;
        this.energyMeterCode = energyMeterCode;
        this.energyMeterName = energyMeterName;
        this.electricityStartDate = electricityStartDate;
        this.electricityEndDate = electricityEndDate;
        this.thisQuantityOfElectricity = thisQuantityOfElectricity;
        this.thisElectricityCharge = thisElectricityCharge;
        this.powerConsumption = powerConsumption;
        this.contractPrice = contractPrice;
        this.recoveryElectricityFlag = recoveryElectricityFlag;
        this.thisElectricityPrice = thisElectricityPrice;
        this.thisElectricityTax = thisElectricityTax;
        this.ccoer = ccoer;
        this.cdcf = cdcf;
        this.generatorCode = generatorCode;
        this.electricPowerGeneration = electricPowerGeneration;
    }

    public String getBudgetSet() {
        return budgetSet;
    }

    public void setBudgetSet(String budgetSet) {
        this.budgetSet = budgetSet;
    }

    public String getCcoer() {
        return ccoer;
    }

    public void setCcoer(String ccoer) {
        this.ccoer = ccoer;
    }

    public String getCdcf() {
        return cdcf;
    }

    public void setCdcf(String cdcf) {
        this.cdcf = cdcf;
    }

    public String getGeneratorCode() {
        return generatorCode;
    }

    public void setGeneratorCode(String generatorCode) {
        this.generatorCode = generatorCode;
    }

    public String getElectricPowerGeneration() {
        return electricPowerGeneration;
    }

    public void setElectricPowerGeneration(String electricPowerGeneration) {
        this.electricPowerGeneration = electricPowerGeneration;
    }

    public String getRecoveryElectricityFlag() {
        return recoveryElectricityFlag;
    }

    public void setRecoveryElectricityFlag(String recoveryElectricityFlag) {
        this.recoveryElectricityFlag = recoveryElectricityFlag;
    }

    public String getThisElectricityPrice() {
        return thisElectricityPrice;
    }

    public void setThisElectricityPrice(String thisElectricityPrice) {
        this.thisElectricityPrice = thisElectricityPrice;
    }

    public String getThisElectricityTax() {
        return thisElectricityTax;
    }

    public void setThisElectricityTax(String thisElectricityTax) {
        this.thisElectricityTax = thisElectricityTax;
    }




    public String getEnergyMeterCode() {
        return energyMeterCode;
    }

    public void setEnergyMeterCode(String energyMeterCode) {
        this.energyMeterCode = energyMeterCode;
    }

    public String getEnergyMeterName() {
        return energyMeterName;
    }

    public void setEnergyMeterName(String energyMeterName) {
        this.energyMeterName = energyMeterName;
    }

    public String getElectricityStartDate() {
        return electricityStartDate;
    }

    public void setElectricityStartDate(String electricityStartDate) {
        this.electricityStartDate = electricityStartDate;
    }


    public void setTotalQuantityOfElectricity(String totalQuantityOfElectricity) {
    }

    public String getContractPrice() {
        return contractPrice;
    }

    public void setContractPrice(String contractPrice) {
        this.contractPrice = contractPrice;
    }

    public String getPowerConsumption() {
        return powerConsumption;
    }

    public void setPowerConsumption(String powerConsumption) {
        this.powerConsumption = powerConsumption;
    }

    public String getThisElectricityCharge() {
        return thisElectricityCharge;
    }

    public void setThisElectricityCharge(String thisElectricityCharge) {
        this.thisElectricityCharge = thisElectricityCharge;
    }

    public String getThisQuantityOfElectricity() {
        return thisQuantityOfElectricity;
    }

    public void setThisQuantityOfElectricity(String thisQuantityOfElectricity) {
        this.thisQuantityOfElectricity = thisQuantityOfElectricity;
    }

    public String getElectricityEndDate() {
        return electricityEndDate;
    }

    public void setElectricityEndDate(String electricityEndDate) {
        this.electricityEndDate = electricityEndDate;
    }

    @Override
    public String toString() {
        return "WriteoffDetailInfo2{" +
                "budgetSet='" + budgetSet + '\'' +
                ", energyMeterCode='" + energyMeterCode + '\'' +
                ", energyMeterName='" + energyMeterName + '\'' +
                ", electricityStartDate='" + electricityStartDate + '\'' +
                ", electricityEndDate='" + electricityEndDate + '\'' +
                ", thisQuantityOfElectricity='" + thisQuantityOfElectricity + '\'' +
                ", thisElectricityCharge='" + thisElectricityCharge + '\'' +
                ", powerConsumption='" + powerConsumption + '\'' +
                ", contractPrice='" + contractPrice + '\'' +
                ", recoveryElectricityFlag='" + recoveryElectricityFlag + '\'' +
                ", thisElectricityPrice='" + thisElectricityPrice + '\'' +
                ", thisElectricityTax='" + thisElectricityTax + '\'' +
                ", ccoer='" + ccoer + '\'' +
                ", cdcf='" + cdcf + '\'' +
                ", generatorCode='" + generatorCode + '\'' +
                ", electricPowerGeneration='" + electricPowerGeneration + '\'' +
                '}';
    }
}
