package com.sccl.modules.business.energyaccountpoolitempre.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.business.energyaccountpoolitempre.domain.EnergyAccountpoolitempre;
import com.sccl.modules.business.energyaccountpoolitempre.service.IEnergyAccountpoolitempreService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 油费汇总单明细 信息操作处理
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
@RestController
@RequestMapping("/business/energyAccountpoolitempre")
public class EnergyAccountpoolitempreController extends BaseController
{
    private String prefix = "business/energyAccountpoolitempre";

	@Autowired
	private IEnergyAccountpoolitempreService energyAccountpoolitempreService;

	@RequiresPermissions("business:energyAccountpoolitempre:view")
	@GetMapping()
	public String energyAccountpoolitempre()
	{
	    return prefix + "/energyAccountpoolitempre";
	}

	/**
	 * 查询油费汇总单明细列表
	 */
	//@RequiresPermissions("business:energyAccountpoolitempre:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(EnergyAccountpoolitempre energyAccountpoolitempre)
	{
		startPage();
        List<EnergyAccountpoolitempre> list = energyAccountpoolitempreService.selectList(energyAccountpoolitempre);
		return getDataTable(list);
	}

	/**
	 * 新增油费汇总单明细
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}

	/**
	 * 新增保存油费汇总单明细
	 */
	@RequiresPermissions("business:energyAccountpoolitempre:add")
	@Log(title = "油费汇总单明细", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody EnergyAccountpoolitempre energyAccountpoolitempre)
	{
		return toAjax(energyAccountpoolitempreService.insert(energyAccountpoolitempre));
	}

	/**
	 * 修改油费汇总单明细
	 */
	@GetMapping("/edit/{pabriid}")
	public AjaxResult edit(@PathVariable("pabriid") Long pabriid)
	{
		EnergyAccountpoolitempre energyAccountpoolitempre = energyAccountpoolitempreService.get(pabriid);

		Object object = JSONObject.toJSON(energyAccountpoolitempre);

        return this.success(object);
	}

	/**
	 * 修改保存油费汇总单明细
	 */
	@RequiresPermissions("business:energyAccountpoolitempre:edit")
	@Log(title = "油费汇总单明细", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody EnergyAccountpoolitempre energyAccountpoolitempre)
	{
		return toAjax(energyAccountpoolitempreService.update(energyAccountpoolitempre));
	}

	/**
	 * 删除油费汇总单明细
	 */
	@RequiresPermissions("business:energyAccountpoolitempre:remove")
	@Log(title = "油费汇总单明细", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{
		return toAjax(energyAccountpoolitempreService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看油费汇总单明细
     */
    @RequiresPermissions("business:energyAccountpoolitempre:view")
    @GetMapping("/view/{pabriid}")
    @ResponseBody
    public AjaxResult view(@PathVariable("pabriid") Long pabriid)
    {
		EnergyAccountpoolitempre energyAccountpoolitempre = energyAccountpoolitempreService.get(pabriid);

        Object object = JSONObject.toJSON(energyAccountpoolitempre);

        return this.success(object);
    }

}
