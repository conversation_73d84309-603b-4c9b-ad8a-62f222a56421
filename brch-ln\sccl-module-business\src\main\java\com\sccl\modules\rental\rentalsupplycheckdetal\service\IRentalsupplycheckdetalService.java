package com.sccl.modules.rental.rentalsupplycheckdetal.service;

import com.sccl.modules.rental.rentalsupplycheckdetal.domain.Rentalsupplycheckdetal;
import com.sccl.framework.service.IBaseService;

import java.util.List;

/**
 * 供应商 检验 服务层
 * 
 * <AUTHOR>
 * @date 2019-09-06
 */
public interface IRentalsupplycheckdetalService extends IBaseService<Rentalsupplycheckdetal>
{
    List<Rentalsupplycheckdetal> selectByParams(Rentalsupplycheckdetal rentalsupplycheckdetal);
	
}
