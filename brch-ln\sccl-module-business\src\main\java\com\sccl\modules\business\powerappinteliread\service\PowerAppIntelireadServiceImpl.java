package com.sccl.modules.business.powerappinteliread.service;

import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.modules.business.accountEs.domain.PowerAccountEs;
import com.sccl.modules.business.accountEs.mapper.PowerAccountEsMapper;
import com.sccl.modules.business.powerappinteliread.mapper.PowerAppIntelireadMapper;
import com.sccl.modules.system.user.domain.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sccl.modules.business.powerappinteliread.domain.PowerAppInteliread;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 远程抄记录 服务层实现
 * 
 * <AUTHOR>
 * @date 2022-03-08
 */
@Service
public class PowerAppIntelireadServiceImpl extends BaseServiceImpl<PowerAppInteliread> implements IPowerAppIntelireadService
{
    @Autowired
    private PowerAppIntelireadMapper powerAppIntelireadMapper;
    @Override
    public int insertAccount(List<PowerAccountEs> accountEsList) {
        int num = 0;
        User user = ShiroUtils.getUser();
        List<IdNameVO> companies = user.getCompanies();
        //如果没有传入分公司和责任中心，则默认第一个
        List<IdNameVO> departments = user.getDepartments();
        for(PowerAccountEs account : accountEsList){
            if(user != null){
                //设置最后修改人
                PowerAppInteliread powerAppInteliread=new PowerAppInteliread();
                powerAppInteliread.setCreateUser(user.getId());

/*                if(account.getPcid() != null){
                    num += powerAppIntelireadMapper.updateByPrimaryKeySelective(account);
                }else {*/
                    //设置创建人
                if(account.getPcid() != null)
                {powerAppInteliread.setPcid(account.getPcid());}
                if(account.getAmmeterid()!= null)
                {powerAppInteliread.setAmmeterid(account.getAmmeterid());}
                if (companies != null && companies.size() > 0)

                powerAppInteliread.setCompany(Long.parseLong(companies.get(0).getId()));
                powerAppInteliread.setCountry(Long.parseLong(departments.get(0).getId()));
                powerAppInteliread.setCreateTime(new Date());
                powerAppInteliread.setStatus(1);
                powerAppInteliread.setBillStatus(0);
/*                    if(account.getAccountmoney() != null && account.getAccountmoney().compareTo(new BigDecimal(0)) != 0){
                        account.setEffective(1);
                    }*/

                    num += powerAppIntelireadMapper.insert(powerAppInteliread);
               /* }*/
            }
        }
        return num;
    }

    @Override
    public List<PowerAppInteliread> selectByList(PowerAppInteliread powerAppInteliread) {
        return powerAppIntelireadMapper.selectByList(powerAppInteliread);
    }

    @Override
    public List<PowerAppInteliread> selectByNeed(PowerAppInteliread powerAppInteliread) {
        return powerAppIntelireadMapper.selectByNeed(powerAppInteliread);
    }

    @Override
    public List<PowerAppInteliread> listDetail(PowerAppInteliread powerAppInteliread) {
        return powerAppIntelireadMapper.selectListDetail(powerAppInteliread);
    }

    @Override
    public PowerAppInteliread viewDetail(Long id) {

        return powerAppIntelireadMapper.viewDetail(id);
    }

    @Override
    public int insertireaddata(PowerAppInteliread powerAppInteliread) {
        int num = 0;

        AjaxResult map = new AjaxResult();
        map.put("success", "1");
        User user = ShiroUtils.getUser();
        List<IdNameVO> companies = user.getCompanies();
        //如果没有传入分公司和责任中心，则默认第一个
        List<IdNameVO> departments = user.getDepartments();
        if (powerAppInteliread.getIntelireadid()==null)
       /* for (PowerAppInteliread item : powerAppIntelireadList) {*/ {
            if (user != null) {
                //设置最后修改人
                PowerAppInteliread pr = new PowerAppInteliread();
                powerAppInteliread.setCreateUser(user.getId());
                if (powerAppInteliread.getPcid() != null) {
                    pr.setPcid(powerAppInteliread.getPcid());
                }
                if (powerAppInteliread.getAmmeterid() != null) {
                    pr.setAmmeterid(powerAppInteliread.getAmmeterid());
                }
                if (companies != null && companies.size() > 0)
                    pr.setCompany(Long.parseLong(companies.get(0).getId()));
                if (powerAppInteliread.getCountry() == null)
                    pr.setCountry(Long.parseLong(departments.get(0).getId()));
                else
                    pr.setCountry(powerAppInteliread.getCountry());
                pr.setCreateTime(new Date());
                pr.setStatus(1);
                pr.setBillStatus(0);


                num += powerAppIntelireadMapper.insert(pr);

            }
        }
        else
        {
            num=-1;
        }

       /* }*/
        return num;
    }
}
