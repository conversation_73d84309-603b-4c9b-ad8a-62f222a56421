package com.sccl.modules.rental.rentalcarorder.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.common.exception.base.BaseException;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.service.IdGenerator;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.rental.rentalcarorder.domain.Rentalcarorder;
import com.sccl.modules.rental.rentalcarorder.mapper.RentalcarorderMapper;
import com.sccl.modules.rental.rentalordercarmodel.domain.RentalorderCarmodel;
import com.sccl.modules.rental.rentalordercarmodel.mapper.RentalorderCarmodelMapper;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.uniflow.common.WFModel;


/**
 * 车辆租赁申请 服务层实现
 *
 * <AUTHOR>
 * @date 2019-08-23
 */
@Service
public class RentalcarorderServiceImpl extends BaseServiceImpl<Rentalcarorder> implements IRentalcarorderService {
    @Autowired
    RentalcarorderMapper rentalcarorderMapper;
    @Autowired
    RentalorderCarmodelMapper rentalorderCarmodelMapper;
    private static final Logger logger = LoggerFactory.getLogger(RentalcarorderServiceImpl.class);

    @Override
    public List<Rentalcarorder> selectListByIds(String[] toStrArray) {
        return rentalcarorderMapper.selectListByIds(toStrArray);
    }

    @Override
    public AjaxResult saveRentalcarorder(Rentalcarorder rentalcarorder) {
        AjaxResult rs = new AjaxResult();
        if (rentalcarorder.getRcoid() == null) {
            insertRentalcarorder(rentalcarorder, rs);
        } else {
            updateRentalcarorder(rentalcarorder, rs);
        }
        return rs;
    }

    private void insertRentalcarorder(Rentalcarorder rentalcarorder, AjaxResult rs) {
        long nextId = IdGenerator.getNextId();
        User user = ShiroUtils.getUser();
        Long userid = user.getId();
        String company = user.getCompanies().get(0).getId();
        String country = user.getDepartments().get(0).getId();
        String name = user.getUserName();
        Date date = new Date();
        rentalcarorder.setRcoid(nextId);
        rentalcarorder.setInputusername(name);
        rentalcarorder.setInputuserid(userid);
        rentalcarorder.setCompany(company);
        rentalcarorder.setCountry(country);
        rentalcarorder.setInputdate(date);
        rentalcarorder.setStatus("1");
        rentalcarorderMapper.insert(rentalcarorder);
        List<RentalorderCarmodel> rentalorderCarmodels = rentalcarorder.getRentalorderCarmodels();
        for (RentalorderCarmodel m : rentalorderCarmodels) {
            m.setRcoid(nextId);
            m.setId(IdGenerator.getNextId());
        }
        if (rentalorderCarmodels != null && rentalorderCarmodels.size() > 0)
            rentalorderCarmodelMapper.insertList(rentalorderCarmodels);
        rentalcarorder.setRentalorderCarmodels(rentalorderCarmodels);
        rs.put("data", rentalcarorder);
    }

    private void updateRentalcarorder(Rentalcarorder rentalcarorder, AjaxResult rs) {
        rentalcarorderMapper.updateForModel(rentalcarorder);
        Long rcoid = rentalcarorder.getRcoid();
        User user = ShiroUtils.getUser();
        List<RentalorderCarmodel> rentalorderCarmodels = rentalcarorder.getRentalorderCarmodels();
        List<RentalorderCarmodel> newlist = new ArrayList<>();
        for (RentalorderCarmodel m : rentalorderCarmodels) {
            if (m.getId() == null) {
                m.setId(IdGenerator.getNextId());
                m.setRcoid(rcoid);
                newlist.add(m);
            } else {
                rentalorderCarmodelMapper.updateForModel(m);//更新旧的
            }
        }
        // 插入 新的
        if (newlist.size() > 0)
            rentalorderCarmodelMapper.insertList(newlist);
        rentalcarorder.setRentalorderCarmodels(rentalorderCarmodels);
        rs.put("data", rentalcarorder);
    }


    /**
     * 流程引擎回调（通过MQ）
     *
     * @param wfModel 流程消息对象
     */
//    @Override
    public void uniflowCallBack(WFModel wfModel) {
//        System.out.println("-----------------lt:" + wfModel.toString());
        logger.debug("-----------------lt:" + wfModel.toString());
        if ("PROCESS_STARTED".equals(wfModel.getCallbackType())) {//更新流程Id  流程 提交
            try {
//                草稿	1，代办	2,退回	3，删除	4,完成	5
                setStatus(wfModel, "2");
                logger.debug("车辆租赁信息录入审批新增" + wfModel.getBusiId());
            } catch (NumberFormatException e) {
                e.printStackTrace();
                logger.error("提交流程失败:" + e.getMessage());
                throw new BaseException("提交流程失败:" + e.getMessage());//
            }
        } else if ("PROCESS_COMPLETED".equals(wfModel.getCallbackType())) {//
//            流程完成后执行的回调  //                草稿	1，代办	2,退回	3，删除	4,完成	5
            setStatus(wfModel, "5");
        } else if ("TURNBACK_TO_START".equals(wfModel.getCallbackType())) {//流程 退回
            //                草稿	1，代办	2,退回	3，删除	4,完成	5
            setStatus(wfModel, "1");// 改为草稿 可重新提交
        } else if ("PROCESS_CANCELLED".equals(wfModel.getCallbackType())) {//流程 终止
            //                草稿	1，代办	2,退回	3，删除	4,完成	5
            setStatus(wfModel, "1");
        }
    }

    private void setStatus(WFModel wfModel, String status) {
        Rentalcarorder m = new Rentalcarorder();
        m.setIprocessinstid(wfModel.getProcInstId());
        m.setRcoid(Long.valueOf(wfModel.getBusiId()));
        m.setStatus(status);// 流程中
        rentalcarorderMapper.updateForModel(m);
//        List<Rentalcarorder> rentalcarorders = rentalcarorderMapper.selectList(m);
        // 没有状态修改

    }

    @Override
    public int deleteAndItemByIds(String[] toStrArray) {
        rentalorderCarmodelMapper.deleteByRcoids(toStrArray);
        return rentalcarorderMapper.deleteByIdsDB(toStrArray);
    }

}
