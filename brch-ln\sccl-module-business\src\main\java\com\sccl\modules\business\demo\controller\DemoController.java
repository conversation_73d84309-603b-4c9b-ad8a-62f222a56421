package com.sccl.modules.business.demo.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import com.alibaba.fastjson.JSONObject;
import com.sccl.common.support.Convert;
import com.sccl.common.utils.DateUtils;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.datasource.DynamicDataSourceContextHolder;
import com.sccl.framework.utils.ExcelUtil;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.demo.service.DemoServiceImpl;
import com.sccl.modules.domain.demo.Demo;
import com.sccl.modules.system.attachments.domain.UpLoadFile;
import com.sccl.modules.system.user.domain.User;

import io.swagger.annotations.ApiOperation;

/**
 * 测试 信息操作处理
 * 
 * <AUTHOR>
 * @date 2019-04-02
 */
@RestController
@RequestMapping("/api/demo")
public class DemoController extends BaseController
{
    private String prefix = "business/demo";
    private static final Logger logger = LoggerFactory.getLogger(DemoController.class);
	
	@Autowired
	private DemoServiceImpl demoService;
	
	@RequiresPermissions("business:demo:view")
	@GetMapping()
	public String demo()
	{
	    return prefix + "/demo";
	}
	
	/**
	 * 查询测试列表
	 */
	//@RequiresPermissions("business:demo:list")
	@ApiOperation(value="获取所有列表",notes="listAll")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list()
	{
		startPage();
        List<Demo> list = new ArrayList<>();
		return getDataTable(list);
	}
	
	/**
	 * 新增测试
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存测试
	 */
	//@RequiresPermissions("business:demo:add")
	//@Log(title = "测试", action = BusinessType.INSERT)
	@PostMapping(value="/add",consumes = MediaType.APPLICATION_JSON_VALUE)
	@ResponseBody
	public AjaxResult addSave(@RequestBody Demo demo)
	{		
		return toAjax(demoService.insert(demo));
	}

	/**
	 * 修改测试
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		Demo demo = demoService.get(id);

		Object object = JSONObject.toJSON(demo);

        return this.success(object);
	}
	
	/**
	 * 修改保存测试
	 */
	//@RequiresPermissions("business:demo:edit")
	//@Log(title = "测试", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody Demo demo)
	{		
		return toAjax(demoService.update(demo));
	}
	
	/**
	 * 删除测试
	 */
	//@RequiresPermissions("business:demo:remove")
	//@Log(title = "测试", action = BusinessType.DELETE)
	@PostMapping( value = "/remove",consumes = MediaType.APPLICATION_JSON_VALUE)
	@ResponseBody
	public AjaxResult remove(@RequestBody HashMap<String, String> map)
	{		
		return toAjax(demoService.deleteByIdsDB(Convert.toStrArray(map.get("ids"))));
	}


    /**
     * 查看测试
     */
    //@RequiresPermissions("business:demo:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		Demo demo = demoService.get(id);

        Object object = JSONObject.toJSON(demo);

        return this.success(object);
    }
    
    /**
     * 混合访问Oracle
     */
    @GetMapping("/view/oracle")
    @ResponseBody
    public AjaxResult visitOracle()
    {
    	Map<String, Object> map=new HashMap<String, Object>();
    	List<Demo> list = demoService.listAll();
    	map.put("mysql1", list);
    	DynamicDataSourceContextHolder.setDB("RES");
    	List<Map<String, String>> listOracle = demoService.visitOracle();
    	map.put("oracle", listOracle);
        Object object = JSONObject.toJSON(map);

        return this.success(object);
    }
    
    //@Log(title = "测试", action = BusinessType.DELETE)
	@PostMapping( value = "/delete")
	@ResponseBody
	public AjaxResult removeTest(@RequestParam("ids") String ids)
	{		
    	System.out.println(ids);
		return toAjax(1);
	}
    
    /**
     * 执行文件上传 支持多附件
     *
     * @param request    request对象
     * @param response   response对象
     * @param uploadFile 文件对象
     * @return java.util.Map<java.lang.String   ,   java.lang.Object>
     * <AUTHOR>
     * @Date 2019/2/1 11:09
     */
    @RequestMapping(value = "/uploadExcel")
    @ResponseBody
    public Map<String, Object> uploadMultiFile(HttpServletRequest request, HttpServletResponse response, UpLoadFile uploadFile)
            throws Exception {
        response.setContentType("text/html;charset=utf-8");

        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        String busiAlias = ServletRequestUtils.getStringParameter(request, "id", "");
        String busiId = ServletRequestUtils.getStringParameter(request, "name", "");
        String categoryCode = ServletRequestUtils.getStringParameter(request, "code", "");

        if (logger.isDebugEnabled()) {
            logger.debug("Excel上传，busiId:{} ;busiAlias:{};categoryCode:{}", busiId, busiAlias, categoryCode);
        }
        String attachmentId = "";
        List<Object> retAttachments = new ArrayList<>();
        Iterator<String> iterator = multiRequest.getFileNames();
        while (iterator.hasNext()) {
            String name = iterator.next();
            List<MultipartFile> files = new LinkedList<>();
            files = multiRequest.getFiles(name);
            if (logger.isDebugEnabled()) {
                logger.debug("单个Excel，files:{}", files);
            }
            // 一次遍历所有文件
            for (MultipartFile file : files) {
                if (file == null) {
                    continue;
                }

                //如果文件大小为0则不上传
                long fileSize = file.getSize();
                if (fileSize <= 0L) {
                    throw new Exception("文件名称：【" + file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("\\") + 1, file.getOriginalFilename().length()) + "】的文件大小为0,请核对!");
                }
                
                ExcelUtil<IdNameVO> excelUtil=new ExcelUtil<IdNameVO>(IdNameVO.class);
                List<IdNameVO> list=excelUtil.importExcel("sheet1", file.getInputStream());
            }

        }
        Map<String, Object> map = new HashMap<>(8);
        map.put("success", "true");
        map.put("mssages", "上传文件成功");
        map.put("id", attachmentId);
        map.put("rows", retAttachments);
        return map;
    }

}
