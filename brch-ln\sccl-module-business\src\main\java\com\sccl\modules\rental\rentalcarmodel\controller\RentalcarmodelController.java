package com.sccl.modules.rental.rentalcarmodel.controller;

import java.io.*;
import java.net.URLEncoder;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.modules.rental.rentalcarmain.domain.Rentalcarmain;
import com.sccl.modules.system.role.domain.Role;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.service.IUserService;
import com.sccl.modules.uniflow.wfprocinst.service.IWfProcInstService;
import com.sccl.modules.uniflow.wftask.service.IWfTaskService;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.rental.rentalcarmodel.domain.Rentalcarmodel;
import com.sccl.modules.rental.rentalcarmodel.service.IRentalcarmodelService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

import javax.servlet.http.HttpServletResponse;

/**
 * 车辆 （model） 信息操作处理
 *
 * <AUTHOR>
 * @date 2019-08-23
 */
@RestController
@RequestMapping("/rental/rentalcarmodel")
public class RentalcarmodelController extends BaseController {

    @Autowired
    private IUserService userService;

    private String prefix = "rental/rentalcarmodel";

    @Autowired
    private IRentalcarmodelService rentalcarmodelService;

    @RequiresPermissions("rental:rentalcarmodel:view")
    @GetMapping()
    public String rentalcarmodel() {
        return prefix + "/rentalcarmodel";
    }

    /**
     * 查询车辆 （model）列表
     */
    @RequiresPermissions("rental:rentalcarmodel:list")
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(Rentalcarmodel rentalcarmodel) {
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("PROVI_")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
            if (role.getCode().startsWith("SUB_")) {//县能耗费管理员
                isSubAdmin = true;
            }
        }
//        if (isProAdmin) {//  查询权限设置 分公司
//        } else if (isCityAdmin) {
//            List<IdNameVO> companies = user.getCompanies();
//            if (companies != null && companies.size() > 0)
//                rentalcarmodel.setCompany(companies.get(0).getId());
//        } else if (isSubAdmin) {
//            List<IdNameVO> departments = user.getDepartments();
//            if (departments != null && departments.size() > 0)
//                rentalcarmodel.setCountry(departments.get(0).getId());
//        } else {
//            rentalcarmodel.setInputuserid(user.getId().toString());
//        }
        startPage();
        List<Rentalcarmodel> list = rentalcarmodelService.selectList(rentalcarmodel);
        if (!"1".equals(rentalcarmodel.getDisabled()))
            for (Rentalcarmodel r : list) {
                if (!"1".equals(r.getStatus())) {
                    r.set_disabled(true);
                } else {
                    r.set_disabled(false);

                }
            }
        return getDataTable(list);
    }

    /**
     * 新增车辆 （model）
     */
//    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存车辆 （model）
     */
    @RequiresPermissions("rental:rentalcarmodel:add")
    //@Log(title = "车辆 （model）", action = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody Rentalcarmodel rentalcarmodel) {
        return toAjax(rentalcarmodelService.insert(rentalcarmodel));
    }

    /**
     * 修改车辆 （model）
     */
    @GetMapping("/edit/{modelid}")
    public AjaxResult edit(@PathVariable("modelid") Long modelid) {
        Rentalcarmodel rentalcarmodel = rentalcarmodelService.get(modelid);

        Object object = JSONObject.toJSON(rentalcarmodel);

        return this.success(object);
    }

    /**
     * 修改保存车辆 （model）
     */
    @RequiresPermissions("rental:rentalcarmodel:edit")
    //@Log(title = "车辆 （model）", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody Rentalcarmodel rentalcarmodel) {
        return toAjax(rentalcarmodelService.update(rentalcarmodel));
    }

    /**
     * 删除车辆 （model）
     */
    @RequiresPermissions("rental:rentalcarmodel:remove")
    //@Log(title = "车辆 （model）", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        int i = 0;
        try {
            List<Rentalcarmodel> rentalcarmodels = rentalcarmodelService.selectListByIds(Convert.toStrArray(ids));
            for (Rentalcarmodel b : rentalcarmodels) {
                if (!"1".equals(b.getStatus())) {
                    return this.error(1, "(" + b.getRmmid() + ")" + "草稿状态才能删除");
                }
                // 终止流程
//                killFlow(b);
            }
            i = rentalcarmodelService.deleteByIdsDB(Convert.toStrArray(ids));
            return this.success("删除(" + i + ")条");
        } catch (Exception e) {
            e.printStackTrace();
            return this.error(1, "删除失败:" + e.getMessage());
        }
    }


    /**
     * 删除车辆 （model）
     */
    @RequiresPermissions("rental:rentalcarmodel:remove")
    //@Log(title = "车辆 （model）", action = BusinessType.DELETE)
    @GetMapping("/removeByid/{modelid}")
    @ResponseBody
    public AjaxResult removeByid(@PathVariable("modelid") Long modelid) {
        return toAjax(rentalcarmodelService.removeByid(modelid));
    }

    /**
     * 查看车辆 （model）
     */
    @RequiresPermissions("rental:rentalcarmodel:view")
    @GetMapping("/view/{modelid}")
    @ResponseBody
    public AjaxResult view(@PathVariable("modelid") Long modelid) {
        Rentalcarmodel rentalcarmodel = rentalcarmodelService.get(modelid);

        Object object = JSONObject.toJSON(rentalcarmodel);

        return this.success(object);
    }

    /**
     * 查询车辆 （model）列表 用于统计查询不分页
     */
    @RequestMapping("/statisticalHearder")
    @ResponseBody
    public TableDataInfo statisticalHearder(Rentalcarmodel rentalcarmodel) {
        rentalcarmodel.setStatus("5");//已完成
        List<Rentalcarmodel> list = rentalcarmodelService.selectList(rentalcarmodel);
        return getDataTable(list);
    }

}
