package com.sccl.modules.business.stationreportwhitelist.vo;

import com.sccl.modules.business.stationreportwhitelist.domain.PowerAmmeterorprotocol;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/4/22 18:33
 * @describe 电表管理，用于mybatis-plus基本功能
 */
@Getter
@Setter
public class PowerAmmeterorprotocolVO extends PowerAmmeterorprotocol {

    /**
     * 部门名称
     */
    private String countryName;

    /**
     * 分公司名称
     */
    private String companyName;

    /**
     * 用电类型名称
     */
    private String electrotypeName;

    /**
     * 直供电标志名称
     * 1/2 直供电/转供电
     */
    private String directsupplyflagName;

    /**
     * 站址产权归属(1自留2铁塔)
     */
    private String propertyName;

    /**
     * 关联站点总数
     */
    private Integer stationTotal;

    /**
     * 是否已加入白名单
     */
    private Boolean isWhitelist;

    /**
     * 一表多站 站点编码列表
     */
    private String stationCodes;

    @Override
    public String toString() {
        return super.toString();
    }
}
