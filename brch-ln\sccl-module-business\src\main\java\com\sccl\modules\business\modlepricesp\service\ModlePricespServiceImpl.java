package com.sccl.modules.business.modlepricesp.service;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.business.modlepricesp.domain.ModlePricesp;
import com.sccl.modules.business.modlepricesp.mapper.ModlePricespMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 单价输配电价 服务层实现
 *
 * <AUTHOR>
 * @date 2023-03-08
 */
@Service
public class ModlePricespServiceImpl extends BaseServiceImpl<ModlePricesp> implements IModlePricespService {
    @Autowired
    private ModlePricespMapper pricespMapper;

    @Override
    public String updatebitch(List<ModlePricesp> modlePricesps) {
        int n = pricespMapper.updatebitch(modlePricesps);
        return String.format("更新成功，更新了%s条", n);
    }
}
