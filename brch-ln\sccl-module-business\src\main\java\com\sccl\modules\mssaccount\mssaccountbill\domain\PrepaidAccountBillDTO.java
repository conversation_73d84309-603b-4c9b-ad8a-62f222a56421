package com.sccl.modules.mssaccount.mssaccountbill.domain;

import com.sccl.framework.aspectj.lang.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @description: 预付管理
 * @date 2024/10/21  14:30
 */
@Data
public class PrepaidAccountBillDTO {

    /**
     * 序号
     */
    @Excel(name = "序号")
    private String accountNo;

    /**
     * 公司
     */
    @Excel(name = "所属公司")
    private String company;

    /**
     * 公司编号
     */
    private String companyNo;

    /**
     * 预付金额
     */
    @Excel(name = "预付金额(元)")
    private BigDecimal preMoney;

    /**
     * 已挑对金额
     */
    @Excel(name = "已挑对金额(元)")
    private BigDecimal checkMoney;

    /**
     * 差额
     */
    @Excel(name = "差额(元)")
    private BigDecimal balanceMoney;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 小数位设定
     * @param account
     */
    public void setScale(PrepaidAccountBillDTO account) {
        account.setPreMoney(account.getPreMoney().setScale(2, RoundingMode.HALF_UP));
        account.setCheckMoney(account.getCheckMoney().setScale(2, RoundingMode.HALF_UP));
        account.setBalanceMoney(account.getBalanceMoney().setScale(2, RoundingMode.HALF_UP));
    }
}
