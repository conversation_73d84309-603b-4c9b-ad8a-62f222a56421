package com.sccl.modules.business.meterinfoalljt_new.service;

import com.sccl.framework.service.IBaseService;
import com.sccl.modules.business.meterinfoalljt_new.domain.MeterinfoAllJtApply;
import com.sccl.modules.business.meterinfoalljt_new.vo.MeterinfoAllJtApplyDelVo;
import com.sccl.modules.business.meterinfoalljt_new.vo.MeterinfoAllJtApplyResultVo;
import com.sccl.modules.business.meterinfoalljt_new.vo.MeterinfoAllJtApplySaveVo;
import com.sccl.modules.business.meterinfoalljt_new.vo.MeterinfoAllJtApplySearchVo;

import java.util.List;

/**
 * 在网表计数据清单-新增申请 服务层
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
public interface IMeterinfoAllJtApplyService extends IBaseService<MeterinfoAllJtApply>
{

    /**
     * 一览查询
     * @param searchVo
     * @return
     */
    List<MeterinfoAllJtApplyResultVo> list(MeterinfoAllJtApplySearchVo searchVo);

    /**
     * 待添加电表一览查询
     * @param searchVo
     * @return
     */
    List<MeterinfoAllJtApplyResultVo> ammeterList(MeterinfoAllJtApplySearchVo searchVo);

    /**
     * 添加保存
     * @param entity
     * @return
     */
    String save(MeterinfoAllJtApplySaveVo entity);

    /**
     * 提交
     * @param entity
     * @return
     */
    String submit(MeterinfoAllJtApplyDelVo entity);

    /**
     * 删除
     * @param entity
     * @return
     */
    String del(MeterinfoAllJtApplyDelVo entity);

    /**
     * 根据网表计清单，更新数据状态为已处理
     * @return
     */
    String completed();
}
