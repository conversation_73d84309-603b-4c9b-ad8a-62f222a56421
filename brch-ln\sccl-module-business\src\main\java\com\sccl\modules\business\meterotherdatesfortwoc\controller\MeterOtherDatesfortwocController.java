package com.sccl.modules.business.meterotherdatesfortwoc.controller;

import com.alibaba.fastjson.JSONObject;
import com.sccl.common.support.Convert;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.meterotherdatesfortwoc.domain.MeterOtherDatesfortwoc;
import com.sccl.modules.business.meterotherdatesfortwoc.service.IMeterOtherDatesfortwocService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 双碳接口其它能耗数据 信息操作处理
 *
 * <AUTHOR>
 * @date 2023-04-17
 */
@RestController
@RequestMapping("/business/meterOtherDatesfortwoc")
public class MeterOtherDatesfortwocController extends BaseController {
    private String prefix = "business/meterOtherDatesfortwoc";

    @Autowired
    private IMeterOtherDatesfortwocService meterOtherDatesfortwocService;

    @RequiresPermissions("business:meterOtherDatesfortwoc:view")
    @GetMapping()
    public String meterOtherDatesfortwoc() {
        return prefix + "/meterOtherDatesfortwoc";
    }

    /**
     * 查询双碳接口其它能耗数据列表
     */
    @RequiresPermissions("business:meterOtherDatesfortwoc:list")
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(MeterOtherDatesfortwoc meterOtherDatesfortwoc) {
        startPage();
        List<MeterOtherDatesfortwoc> list = meterOtherDatesfortwocService.selectList(meterOtherDatesfortwoc);
        return getDataTable(list);
    }

    /**
     * 新增双碳接口其它能耗数据
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存双碳接口其它能耗数据
     */
    @Log(title = "双碳接口其它能耗数据", action = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody MeterOtherDatesfortwoc meterOtherDatesfortwoc) {
        return toAjax(meterOtherDatesfortwocService.insert(meterOtherDatesfortwoc));
    }

    /**
     * 批量保存双碳其它能耗数据
     *
     * @param meterOtherDatesfortwoc
     * @return
     */
    @PostMapping("/addbitch")
    @ResponseBody
    public AjaxResult addSaveBitch(@RequestBody List<MeterOtherDatesfortwoc> meterOtherDatesfortwocs) {
        return toAjax(meterOtherDatesfortwocService.insertList(meterOtherDatesfortwocs));
    }

    /**
     * 修改双碳接口其它能耗数据
     */
    @GetMapping("/edit/{id}")
    public AjaxResult edit(@PathVariable("id") Long id) {
        MeterOtherDatesfortwoc meterOtherDatesfortwoc = meterOtherDatesfortwocService.get(id);

        Object object = JSONObject.toJSON(meterOtherDatesfortwoc);

        return this.success(object);
    }

    /**
     * 修改保存双碳接口其它能耗数据
     */
    @RequiresPermissions("business:meterOtherDatesfortwoc:edit")
    @Log(title = "双碳接口其它能耗数据", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody MeterOtherDatesfortwoc meterOtherDatesfortwoc) {
        return toAjax(meterOtherDatesfortwocService.update(meterOtherDatesfortwoc));
    }

    /**
     * 删除双碳接口其它能耗数据
     */
    @RequiresPermissions("business:meterOtherDatesfortwoc:remove")
    @Log(title = "双碳接口其它能耗数据", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(meterOtherDatesfortwocService.deleteByIds(Convert.toStrArray(ids)));
    }


    /**
     * 查看双碳接口其它能耗数据
     */
    @RequiresPermissions("business:meterOtherDatesfortwoc:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id) {
        MeterOtherDatesfortwoc meterOtherDatesfortwoc = meterOtherDatesfortwocService.get(id);

        Object object = JSONObject.toJSON(meterOtherDatesfortwoc);

        return this.success(object);
    }

}
