package com.sccl.modules.business.oilaccount.mapper;

import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.oilaccount.domain.OilAccount;
import com.sccl.modules.business.oilaccount.domain.OilAccountRequest;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2024/8/27  11:16
 */
public interface OilAccountMapper extends BaseMapper<OilAccount> {

    List<OilAccount> listOilAccount(OilAccountRequest request);

    List<OilAccount> listOilAccountByCondition(OilAccountRequest request);

    List<OilAccount> selectByIds(@Param("ids") List<Long> ids);

    /**
     * @Description: 归集单处-油费、取暖费、煤炭退回台账
     * @author: chenyuan<PERSON>
     * @date: 2024/9/13
     * @param:
     * @return:
     */
    int updateStatus(Map<String, Object> map);

    BigDecimal selectAccountMoney(@Param("id") Long id);

    int updateNewAccountStatusByType(@Param("id") Long id);
    int batchUpdateOilAccount(@Param("accounts") List<OilAccount> accounts);

    int updateStatuByParid(Map<String, Object> map);
}
