package com.sccl.modules.business.stationinfovalidity.service;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.business.stationinfovalidity.domain.StationInfoValidity;
import com.sccl.modules.business.stationinfovalidity.mapper.StationInfoValidityMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 电站址有效 服务层实现
 * 
 * <AUTHOR>
 * @date 2023-03-31
 */
@Service
public class StationInfoValidityServiceImpl extends BaseServiceImpl<StationInfoValidity> implements IStationInfoValidityService
{
    @Autowired
    private StationInfoValidityMapper stationInfoValidityMapper;
    @Override
    public List<StationInfoValidity> getStationInfoValidity(StationInfoValidity parm) {
        return stationInfoValidityMapper.getStationInfoValidity(parm);
    }
}
