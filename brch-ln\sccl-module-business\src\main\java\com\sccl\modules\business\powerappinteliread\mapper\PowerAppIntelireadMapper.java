package com.sccl.modules.business.powerappinteliread.mapper;

import com.sccl.modules.business.powerappinteliread.domain.PowerAppInteliread;
import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.toweraccount.domain.TowerData;

import java.util.List;

/**
 * 远程抄记录 数据层
 * 
 * <AUTHOR>
 * @date 2022-03-08
 */
public interface PowerAppIntelireadMapper extends BaseMapper<PowerAppInteliread>
{

    public List<PowerAppInteliread> selectByList(PowerAppInteliread powerAppInteliread);
    public PowerAppInteliread viewDetail(Long id);
    public List<PowerAppInteliread> selectByNeed(PowerAppInteliread powerAppInteliread);
    public List<PowerAppInteliread> selectListDetail(PowerAppInteliread powerAppInteliread);
}