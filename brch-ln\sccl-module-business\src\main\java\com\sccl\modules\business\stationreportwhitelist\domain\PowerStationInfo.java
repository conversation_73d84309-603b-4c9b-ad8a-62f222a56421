package com.sccl.modules.business.stationreportwhitelist.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/5/8 15:00
 * @describe 局站信息表
 */
@Getter
@Setter
@TableName("power_station_info")
public class PowerStationInfo extends Model<PowerStationInfo> {

    /**
     * 局站id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 局站名称
     */
    private String stationcode;

    /**
     * 局站名称
     */
    private String stationname;

    /**
     * 局站类型
     */
    private String stationtype;

    /**
     * 分公司
     */
    private Long company;

    /**
     * 部门
     */
    private Long country;

    /**
     * 站址编码
     */
    private String resstationcode;

    /**
     * 产权
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long propertyright;

    /**
     * 5gr编码
     */
    private String stationcodeintid;

    /**
     * 5gr名称
     */
    private String stationname5gr;


}
