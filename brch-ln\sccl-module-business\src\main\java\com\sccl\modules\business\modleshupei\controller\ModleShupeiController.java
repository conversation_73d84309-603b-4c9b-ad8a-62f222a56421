package com.sccl.modules.business.modleshupei.controller;

import java.util.Date;
import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.business.modleshupei.domain.ModleShupei;
import com.sccl.modules.business.modleshupei.service.IModleShupeiService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 单价输配电价 信息操作处理
 * 
 * <AUTHOR>
 * @date 2023-03-03
 */
@RestController
@RequestMapping("/business/modleShupei")
public class ModleShupeiController extends BaseController
{
    private String prefix = "business/modleShupei";
	
	@Autowired
	private IModleShupeiService modleShupeiService;
	
	@RequiresPermissions("business:modleShupei:view")
	@GetMapping()
	public String modleShupei()
	{
	    return prefix + "/modleShupei";
	}
	
	/**
	 * 查询单价输配电价列表
	 */
	@RequiresPermissions("business:modleShupei:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(ModleShupei modleShupei)
	{
		startPage();
        List<ModleShupei> list = modleShupeiService.selectList(modleShupei);
		return getDataTable(list);
	}
	
	/**
	 * 新增单价输配电价
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存单价输配电价
	 */
	@Log(title = "单价输配电价", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody ModleShupei modleShupei)
	{
		modleShupei.setDelFlag("0");
		modleShupei.setCreateTime(new Date());
		modleShupei.setUpdateTime(new Date());
		return toAjax(modleShupeiService.insert(modleShupei));
	}

	/**
	 * 修改单价输配电价
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		ModleShupei modleShupei = modleShupeiService.get(id);

		Object object = JSONObject.toJSON(modleShupei);

        return this.success(object);
	}
	
	/**
	 * 修改保存单价输配电价
	 */
	@RequiresPermissions("business:modleShupei:edit")
	@Log(title = "单价输配电价", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody ModleShupei modleShupei)
	{		
		return toAjax(modleShupeiService.update(modleShupei));
	}
	
	/**
	 * 删除单价输配电价
	 */
	@RequiresPermissions("business:modleShupei:remove")
	@Log(title = "单价输配电价", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(modleShupeiService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看单价输配电价
     */
    @RequiresPermissions("business:modleShupei:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		ModleShupei modleShupei = modleShupeiService.get(id);

        Object object = JSONObject.toJSON(modleShupei);

        return this.success(object);
    }

}
