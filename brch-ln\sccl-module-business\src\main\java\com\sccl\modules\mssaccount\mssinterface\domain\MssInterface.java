package com.sccl.modules.mssaccount.mssinterface.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;
import java.math.BigDecimal;


/**
 * MSS财务接口表 MSS_INTERFACE
 * 
 * <AUTHOR>
 * @date 2019-04-24
 */
public class MssInterface extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
	private Long miid;
    public Long getMiid() {
		return miid;
	}

	public void setMiid(Long miid) {
		this.miid = miid;
	}

	/**  */
    private String inftype;
    /**  */
    private String guid;
    /**  */
    private String ip;
    /**  */
    private Date startdate;
    /**  */
    private Date enddate;
    /**  */
    private String sendxml;
    /**  */
    private String returnxml;
    /**  */
    private String whcbno;
    /**  */
    private String mssno;
    /**  */
    private String processinstid;
    /**  */
    private String status;
    /**  */
    private String md5;


	public void setInftype(String inftype)
	{
		this.inftype = inftype;
	}

	public String getInftype() 
	{
		return inftype;
	}

	public void setGuid(String guid)
	{
		this.guid = guid;
	}

	public String getGuid() 
	{
		return guid;
	}

	public void setIp(String ip)
	{
		this.ip = ip;
	}

	public String getIp() 
	{
		return ip;
	}

	public void setStartdate(Date startdate)
	{
		this.startdate = startdate;
	}

	public Date getStartdate() 
	{
		return startdate;
	}

	public void setEnddate(Date enddate)
	{
		this.enddate = enddate;
	}

	public Date getEnddate() 
	{
		return enddate;
	}

	public void setSendxml(String sendxml)
	{
		this.sendxml = sendxml;
	}

	public String getSendxml() 
	{
		return sendxml;
	}

	public void setReturnxml(String returnxml)
	{
		this.returnxml = returnxml;
	}

	public String getReturnxml() 
	{
		return returnxml;
	}

	public void setWhcbno(String whcbno)
	{
		this.whcbno = whcbno;
	}

	public String getWhcbno() 
	{
		return whcbno;
	}

	public void setMssno(String mssno)
	{
		this.mssno = mssno;
	}

	public String getMssno() 
	{
		return mssno;
	}

	public void setProcessinstid(String processinstid)
	{
		this.processinstid = processinstid;
	}

	public String getProcessinstid() 
	{
		return processinstid;
	}

	public void setStatus(String status)
	{
		this.status = status;
	}

	public String getStatus() 
	{
		return status;
	}

	public void setMd5(String md5)
	{
		this.md5 = md5;
	}

	public String getMd5() 
	{
		return md5;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("miid", getMiid())
            .append("inftype", getInftype())
            .append("guid", getGuid())
            .append("ip", getIp())
            .append("startdate", getStartdate())
            .append("enddate", getEnddate())
            .append("sendxml", getSendxml())
            .append("returnxml", getReturnxml())
            .append("whcbno", getWhcbno())
            .append("mssno", getMssno())
            .append("processinstid", getProcessinstid())
            .append("status", getStatus())
            .append("md5", getMd5())
            .toString();
    }
}
