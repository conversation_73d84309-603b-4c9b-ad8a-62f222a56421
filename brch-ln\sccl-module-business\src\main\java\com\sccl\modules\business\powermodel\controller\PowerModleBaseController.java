package com.sccl.modules.business.powermodel.controller;

import com.sccl.framework.utils.ExcelUtil;
import com.sccl.modules.autojob.util.convert.MessageMaster;
import com.sccl.modules.business.budget.domain.Budget;
import com.sccl.modules.business.powermodel.entity.PowerModleBase;
import com.sccl.modules.business.powermodel.service.PowerModleBaseService;
import com.sccl.modules.system.attachments.domain.UpLoadFile;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 电量模型基本表(PowerModleBase)表控制层
 *
 * <AUTHOR>
 * @since 2022-10-19 17:44:40
 */
@RestController
@RequestMapping("powerModleBase")
@Slf4j
public class PowerModleBaseController {

    /**
     * 服务对象
     */
    @Autowired
    private PowerModleBaseService powerModleBaseService;

    /**
     * 分页查询
     *
     * @param powerModleBase 筛选条件
     * @param pageRequest    分页对象
     * @return 查询结果
     */
    @GetMapping
    public ResponseEntity<Page<PowerModleBase>> queryByPage(PowerModleBase powerModleBase, PageRequest pageRequest) {
        return ResponseEntity.ok(this.powerModleBaseService.queryByPage(powerModleBase, pageRequest));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("{id}")
    public ResponseEntity<PowerModleBase> queryById(@PathVariable("id") Long id) {
        return ResponseEntity.ok(this.powerModleBaseService.queryById(id));
    }

    /**
     * 新增数据
     *
     * @param powerModleBase 实体
     * @return 新增结果
     */
    @PostMapping
    public ResponseEntity<PowerModleBase> add(PowerModleBase powerModleBase) {
        return ResponseEntity.ok(this.powerModleBaseService.insert(powerModleBase));
    }

    /**
     * 编辑数据
     *
     * @param powerModleBase 实体
     * @return 编辑结果
     */
    @PutMapping
    public ResponseEntity<PowerModleBase> edit(PowerModleBase powerModleBase) {
        return ResponseEntity.ok(this.powerModleBaseService.update(powerModleBase));
    }

    /**
     * 删除数据
     *
     * @param id 主键
     * @return 删除是否成功
     */
    @DeleteMapping
    public ResponseEntity<Boolean> deleteById(Long id) {
        return ResponseEntity.ok(this.powerModleBaseService.deleteById(id));
    }



}

