package com.sccl.modules.mssaccount.mssaccountbill.frame;

import com.sccl.modules.autojob.util.convert.StringUtils;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.StringJoiner;
@Data
public class RemoveVerify {
    private List<RemoveVerifyRule> rules=new ArrayList<>();

    public String Verify(Long billId, List<Long> accountIds) {
        StringJoiner joiner = new StringJoiner("\n", "错误，当前报账单不允许删除台账明细-->\n", "");
        rules.forEach(
                removeVerifyRule ->
                {
                    String result = removeVerifyRule.Verify(billId, accountIds);
                    if (StringUtils.isNotBlank(result)) {
                        joiner.add(result);
                    }


                }
        );
        return joiner.toString();
    }

    public RemoveVerify addRule(RemoveVerifyRule rule) {
        rules.add(rule);
        return this;
    }


}
