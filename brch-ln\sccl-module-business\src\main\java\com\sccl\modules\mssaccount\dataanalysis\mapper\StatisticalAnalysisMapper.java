package com.sccl.modules.mssaccount.dataanalysis.mapper;

import com.sccl.modules.mssaccount.dataanalysis.dto.StatisticsListDTO;
import com.sccl.modules.mssaccount.dataanalysis.dto.StatisticsMeterListDTO;
import com.sccl.modules.mssaccount.dataanalysis.vo.PowerStationInfoDetailListVO;
import com.sccl.modules.mssaccount.dataanalysis.vo.PowerStationInfoMeterDetailListVO;
import com.sccl.modules.mssaccount.dataanalysis.vo.StatisticsListVO;
import com.sccl.modules.mssaccount.dataanalysis.vo.StatisticsMeterListVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 电表 局站 统计分析
 */
@Mapper
public interface StatisticalAnalysisMapper {

    /**
     * 查询全省局站统计分析
     * @param dto 查询参数
     * @return 统计结果
     */
    StatisticsListVO statisticsList(StatisticsListDTO dto);

    /**
     * 局站类型列表
     * @param dto 查询参数
     * @return 结果
     */
    List<PowerStationInfoDetailListVO> stationStatisticsDetailList(StatisticsListDTO dto);




    /**
     * 查询全省电表统计分析
     * @param dto 查询参数
     * @return 统计结果
     */
    StatisticsMeterListVO statisticsMeterList(StatisticsMeterListDTO dto);


    /**
     * 局站类型列表
     * @param dto 查询参数
     * @return 结果
     */
    List<PowerStationInfoMeterDetailListVO> stationStatisticsMeterDetailList(StatisticsMeterListDTO dto);

}
