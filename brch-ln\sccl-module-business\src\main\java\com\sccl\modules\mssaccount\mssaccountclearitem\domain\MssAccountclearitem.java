package com.sccl.modules.mssaccount.mssaccountclearitem.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;
import java.math.BigDecimal;


/**
 * 挑对报账单表 MSS_ACCOUNTCLEARITEM
 * 
 * <AUTHOR>
 * @date 2019-05-01
 */
public class MssAccountclearitem extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
	private Long clearid;
    public Long getClearid() {
		return clearid;
	}

	public void setClearid(Long clearid) {
		this.clearid = clearid;
	}

	/**  */
    private Long writeoffFirstId;
    /**  */
    private Long writeoffFollowId;
    /** 1,初始,2待清帐,3已清帐,-1删除,-2异常 */
    private String status;
    /** 发起清帐接口关联id */
    private String guid;
    /**  */
    private Date inputdate;
    /**  */
    private String sapcertificatecode;
    /** 凭证行项号 */
    private String sapitemmun;
    /**  */
    private String tyear;
    /**  */
    private String companycode;
    /** 科目编码 */
    private String accountcode;
    /** 1-总账、2-供应商、3-客户 */
    private String accounttype;
    /** 利润中心组 */
    private String profitcentergroupcode;
    /** 外围系统子单Id */
    private String othersystemdetailid;
    /** 挑对金额 */
    private BigDecimal pickingsum;
    /** 报账金额 */
    private BigDecimal writeoffsum;
    /** 进项税金额 */
    private BigDecimal inputtaxsum;
    /** 借贷标识 选项：1-借，0-贷 */
    private String isdebit;
    /** 是否最后一次报账 */
    private String islastwriteoff;
    /**  */
    private Long orgid;
	/** 可挑对金额 */
	private BigDecimal pickingsumuse;

	public BigDecimal getPickingsumuse() {
		return pickingsumuse;
	}

	public void setPickingsumuse(BigDecimal pickingsumuse) {
		this.pickingsumuse = pickingsumuse;
	}

	public void setWriteoffFirstId(Long writeoffFirstId)
	{
		this.writeoffFirstId = writeoffFirstId;
	}

	public Long getWriteoffFirstId()
	{
		return writeoffFirstId;
	}

	public void setWriteoffFollowId(Long writeoffFollowId)
	{
		this.writeoffFollowId = writeoffFollowId;
	}

	public Long getWriteoffFollowId()
	{
		return writeoffFollowId;
	}

	public void setStatus(String status)
	{
		this.status = status;
	}

	public String getStatus() 
	{
		return status;
	}

	public void setGuid(String guid)
	{
		this.guid = guid;
	}

	public String getGuid() 
	{
		return guid;
	}

	public void setInputdate(Date inputdate)
	{
		this.inputdate = inputdate;
	}

	public Date getInputdate() 
	{
		return inputdate;
	}

	public void setSapcertificatecode(String sapcertificatecode)
	{
		this.sapcertificatecode = sapcertificatecode;
	}

	public String getSapcertificatecode() 
	{
		return sapcertificatecode;
	}

	public void setSapitemmun(String sapitemmun)
	{
		this.sapitemmun = sapitemmun;
	}

	public String getSapitemmun() 
	{
		return sapitemmun;
	}

	public void setTyear(String tyear)
	{
		this.tyear = tyear;
	}

	public String getTyear() 
	{
		return tyear;
	}

	public void setCompanycode(String companycode)
	{
		this.companycode = companycode;
	}

	public String getCompanycode() 
	{
		return companycode;
	}

	public void setAccountcode(String accountcode)
	{
		this.accountcode = accountcode;
	}

	public String getAccountcode() 
	{
		return accountcode;
	}

	public void setAccounttype(String accounttype)
	{
		this.accounttype = accounttype;
	}

	public String getAccounttype() 
	{
		return accounttype;
	}

	public void setProfitcentergroupcode(String profitcentergroupcode)
	{
		this.profitcentergroupcode = profitcentergroupcode;
	}

	public String getProfitcentergroupcode() 
	{
		return profitcentergroupcode;
	}

	public void setOthersystemdetailid(String othersystemdetailid)
	{
		this.othersystemdetailid = othersystemdetailid;
	}

	public String getOthersystemdetailid() 
	{
		return othersystemdetailid;
	}

	public void setPickingsum(BigDecimal pickingsum)
	{
		this.pickingsum = pickingsum;
	}

	public BigDecimal getPickingsum() 
	{
		return pickingsum;
	}

	public void setWriteoffsum(BigDecimal writeoffsum)
	{
		this.writeoffsum = writeoffsum;
	}

	public BigDecimal getWriteoffsum() 
	{
		return writeoffsum;
	}

	public void setInputtaxsum(BigDecimal inputtaxsum)
	{
		this.inputtaxsum = inputtaxsum;
	}

	public BigDecimal getInputtaxsum() 
	{
		return inputtaxsum;
	}

	public void setIsdebit(String isdebit)
	{
		this.isdebit = isdebit;
	}

	public String getIsdebit() 
	{
		return isdebit;
	}

	public void setIslastwriteoff(String islastwriteoff)
	{
		this.islastwriteoff = islastwriteoff;
	}

	public String getIslastwriteoff() 
	{
		return islastwriteoff;
	}

	public void setOrgid(Long orgid)
	{
		this.orgid = orgid;
	}

	public Long getOrgid()
	{
		return orgid;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("clearid", getClearid())
            .append("writeoffFirstId", getWriteoffFirstId())
            .append("writeoffFollowId", getWriteoffFollowId())
            .append("status", getStatus())
            .append("guid", getGuid())
            .append("inputdate", getInputdate())
            .append("sapcertificatecode", getSapcertificatecode())
            .append("sapitemmun", getSapitemmun())
            .append("tyear", getTyear())
            .append("companycode", getCompanycode())
            .append("accountcode", getAccountcode())
            .append("accounttype", getAccounttype())
            .append("profitcentergroupcode", getProfitcentergroupcode())
            .append("othersystemdetailid", getOthersystemdetailid())
            .append("pickingsum", getPickingsum())
            .append("writeoffsum", getWriteoffsum())
            .append("inputtaxsum", getInputtaxsum())
            .append("isdebit", getIsdebit())
            .append("islastwriteoff", getIslastwriteoff())
            .append("orgid", getOrgid())
            .toString();
    }
}
