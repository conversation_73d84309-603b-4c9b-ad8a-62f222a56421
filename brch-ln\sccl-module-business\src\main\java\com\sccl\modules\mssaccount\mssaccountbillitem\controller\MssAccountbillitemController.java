package com.sccl.modules.mssaccount.mssaccountbillitem.controller;

import java.math.BigDecimal;
import java.util.List;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.mssaccount.mssaccountbillitem.domain.MssAccountbillitem;
import com.sccl.modules.mssaccount.mssaccountbillitem.service.IMssAccountbillitemService;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 报账明细 信息操作处理
 *
 * <AUTHOR>
 * @date 2019-04-24
 */
@RestController
@RequestMapping("/mssaccount/mssAccountbillitem")
public class MssAccountbillitemController extends BaseController {
    private String prefix = "mssaccount/mssAccountbillitem";

    @Autowired
    private IMssAccountbillitemService mssAccountbillitemService;

    @RequiresPermissions("mssaccount:mssAccountbillitem:view")
    @GetMapping()
    public String mssAccountbillitem() {
        return prefix + "/mssAccountbillitem";
    }

    /**
     * 查询报账明细列表
     */
     @RequiresPermissions("mssaccount:mssAccountbillitem:list")
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(MssAccountbillitem mssAccountbillitem) {
        if (mssAccountbillitem.getWriteoffInstanceId() == null)
            return null;
        startPage();
        List<MssAccountbillitem> list = mssAccountbillitemService.selectList(mssAccountbillitem);
        return getDataTable(list);
    }

    /**
     * 查询报账明细列表
     */
     @RequiresPermissions("mssaccount:mssAccountbillitem:list")
    @RequestMapping("/listByBill/{id}")
    @ResponseBody
    public AjaxResult listByBill(@PathVariable("id") String id) {
        System.out.println(id);
        if (id == null)
            return null;
        List<MssAccountbillitem> list = mssAccountbillitemService.selectBy("writeoffInstanceId", id);
        Object object = JSONObject.toJSON(list);
        return this.success(object);
    }

    /**
     * 新增报账明细
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存报账明细
     */
    @RequiresPermissions("mssaccount:mssAccountbillitem:add")
    //@Log(title = "报账明细", action = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody MssAccountbillitem mssAccountbillitem) {
        return toAjax(mssAccountbillitemService.insert(mssAccountbillitem));
    }

    /**
     * 修改报账明细
     */
    @GetMapping("/edit/{id}")
    public AjaxResult edit(@PathVariable("id") Long id) {
        MssAccountbillitem mssAccountbillitem = mssAccountbillitemService.get(id);

        Object object = JSONObject.toJSON(mssAccountbillitem);

        return this.success(object);
    }

    /**
     * 修改保存报账明细
     */
    @RequiresPermissions("mssaccount:mssAccountbillitem:edit")
    //@Log(title = "报账明细", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody MssAccountbillitem mssAccountbillitem) {
        return toAjax(mssAccountbillitemService.update(mssAccountbillitem));
    }

    /**
     * 删除报账明细
     */
    @RequiresPermissions("mssaccount:mssAccountbillitem:remove")
    //@Log(title = "报账明细", action = BusinessType.DELETE)
    @GetMapping("/removeByid/{id}")
    @ResponseBody
    public AjaxResult removeByid(@PathVariable("id") Long id) {

        return toAjax(mssAccountbillitemService.deleteDBAndRAccount(id));
    }

    @RequiresPermissions("mssaccount:mssAccountbillitem:remove")
    //@Log(title = "报账明细", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        // return
        // toAjax(mssAccountbillitemService.deleteByIds(ConvertFormat.toStrArray(ids)));
        return toAjax(mssAccountbillitemService.deleteByIdsDB(Convert.toStrArray(ids)));
    }

    /**
     * 查看报账明细
     */
    @RequiresPermissions("mssaccount:mssAccountbillitem:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id) {
        MssAccountbillitem mssAccountbillitem = mssAccountbillitemService.get(id);

        Object object = JSONObject.toJSON(mssAccountbillitem);

        return this.success(object);
    }

}
