package com.sccl.modules.mssaccount.msscostcenter.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;
import java.math.BigDecimal;


/**
 * 记账成本中心表 MSS_COSTCENTER
 * 
 * <AUTHOR>
 * @date 2019-05-01
 */
public class MssCostcenter extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
	private Long costid;
    public Long getCostid() {
		return costid;
	}

	public void setCostid(Long costid) {
		this.costid = costid;
	}


	/** 成本中心 */
    private String kostl;
    /** 控制范围 */
    private String kokrs;
    /** 有效起始日 */
    private String datab;
    /** 有效期至 */
    private String datbi;
    /** 新有效起始日 */
    private String datab2;
    /** 新有效期至 */
    private String datbi2;
    /** 名称 */
    private String ktext;
    /** 描述 */
    private String ltext;
    /** 负责人 */
    private String verak;
    /** 部门 */
    private String abtei;
    /** 成本中心类型 */
    private String kosar;
    /** 层次结构范围 */
    private String khinr;
    /** 公司代码 */
    private String bukrs;
    /** 业务范围 */
    private String gsber;
    /** 功能范围 */
    private String funcArea;
    /** 货币 */
    private String waersq;
    /** 利润中心 */
    private String prctr;
    /** 是否虚拟成本中心 */
    private String isVirtual;
    /** 状态 */
    private String status;
    /**  */
    private String flowSign;
    /** E码 */
    private String remark1;
    /** 新名称 */
    private String remark2;
    /** 新描述 */
    private String remark3;
    /**  */
    private String remark4;
    /**  */
    private String remark5;
    /**  */
    private String remark6;
    /**  */
    private String remark7;
    /**  */
    private String remark8;
    /**  */
    private String remark9;
    /**  */
    private String remark10;
    /**  */
    private BigDecimal infId;
    /**  */
    private Date infDate;
    /**  */
    private String infStatus;

	public void setKostl(String kostl)
	{
		this.kostl = kostl;
	}

	public String getKostl() 
	{
		return kostl;
	}

	public void setKokrs(String kokrs)
	{
		this.kokrs = kokrs;
	}

	public String getKokrs() 
	{
		return kokrs;
	}

	public void setDatab(String datab)
	{
		this.datab = datab;
	}

	public String getDatab() 
	{
		return datab;
	}

	public void setDatbi(String datbi)
	{
		this.datbi = datbi;
	}

	public String getDatbi() 
	{
		return datbi;
	}

	public void setDatab2(String datab2)
	{
		this.datab2 = datab2;
	}

	public String getDatab2() 
	{
		return datab2;
	}

	public void setDatbi2(String datbi2)
	{
		this.datbi2 = datbi2;
	}

	public String getDatbi2() 
	{
		return datbi2;
	}

	public void setKtext(String ktext)
	{
		this.ktext = ktext;
	}

	public String getKtext() 
	{
		return ktext;
	}

	public void setLtext(String ltext)
	{
		this.ltext = ltext;
	}

	public String getLtext() 
	{
		return ltext;
	}

	public void setVerak(String verak)
	{
		this.verak = verak;
	}

	public String getVerak() 
	{
		return verak;
	}

	public void setAbtei(String abtei)
	{
		this.abtei = abtei;
	}

	public String getAbtei() 
	{
		return abtei;
	}

	public void setKosar(String kosar)
	{
		this.kosar = kosar;
	}

	public String getKosar() 
	{
		return kosar;
	}

	public void setKhinr(String khinr)
	{
		this.khinr = khinr;
	}

	public String getKhinr() 
	{
		return khinr;
	}

	public void setBukrs(String bukrs)
	{
		this.bukrs = bukrs;
	}

	public String getBukrs() 
	{
		return bukrs;
	}

	public void setGsber(String gsber)
	{
		this.gsber = gsber;
	}

	public String getGsber() 
	{
		return gsber;
	}

	public void setFuncArea(String funcArea)
	{
		this.funcArea = funcArea;
	}

	public String getFuncArea() 
	{
		return funcArea;
	}

	public void setWaersq(String waersq)
	{
		this.waersq = waersq;
	}

	public String getWaersq() 
	{
		return waersq;
	}

	public void setPrctr(String prctr)
	{
		this.prctr = prctr;
	}

	public String getPrctr() 
	{
		return prctr;
	}

	public void setIsVirtual(String isVirtual)
	{
		this.isVirtual = isVirtual;
	}

	public String getIsVirtual() 
	{
		return isVirtual;
	}

	public void setStatus(String status)
	{
		this.status = status;
	}

	public String getStatus() 
	{
		return status;
	}

	public void setFlowSign(String flowSign)
	{
		this.flowSign = flowSign;
	}

	public String getFlowSign() 
	{
		return flowSign;
	}

	public void setRemark1(String remark1)
	{
		this.remark1 = remark1;
	}

	public String getRemark1() 
	{
		return remark1;
	}

	public void setRemark2(String remark2)
	{
		this.remark2 = remark2;
	}

	public String getRemark2() 
	{
		return remark2;
	}

	public void setRemark3(String remark3)
	{
		this.remark3 = remark3;
	}

	public String getRemark3() 
	{
		return remark3;
	}

	public void setRemark4(String remark4)
	{
		this.remark4 = remark4;
	}

	public String getRemark4() 
	{
		return remark4;
	}

	public void setRemark5(String remark5)
	{
		this.remark5 = remark5;
	}

	public String getRemark5() 
	{
		return remark5;
	}

	public void setRemark6(String remark6)
	{
		this.remark6 = remark6;
	}

	public String getRemark6() 
	{
		return remark6;
	}

	public void setRemark7(String remark7)
	{
		this.remark7 = remark7;
	}

	public String getRemark7() 
	{
		return remark7;
	}

	public void setRemark8(String remark8)
	{
		this.remark8 = remark8;
	}

	public String getRemark8() 
	{
		return remark8;
	}

	public void setRemark9(String remark9)
	{
		this.remark9 = remark9;
	}

	public String getRemark9() 
	{
		return remark9;
	}

	public void setRemark10(String remark10)
	{
		this.remark10 = remark10;
	}

	public String getRemark10() 
	{
		return remark10;
	}

	public void setInfId(BigDecimal infId)
	{
		this.infId = infId;
	}

	public BigDecimal getInfId() 
	{
		return infId;
	}

	public void setInfDate(Date infDate)
	{
		this.infDate = infDate;
	}

	public Date getInfDate() 
	{
		return infDate;
	}

	public void setInfStatus(String infStatus)
	{
		this.infStatus = infStatus;
	}

	public String getInfStatus() 
	{
		return infStatus;
	}


	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("kostl", getKostl())
            .append("kokrs", getKokrs())
            .append("datab", getDatab())
            .append("datbi", getDatbi())
            .append("datab2", getDatab2())
            .append("datbi2", getDatbi2())
            .append("ktext", getKtext())
            .append("ltext", getLtext())
            .append("verak", getVerak())
            .append("abtei", getAbtei())
            .append("kosar", getKosar())
            .append("khinr", getKhinr())
            .append("bukrs", getBukrs())
            .append("gsber", getGsber())
            .append("funcArea", getFuncArea())
            .append("waersq", getWaersq())
            .append("prctr", getPrctr())
            .append("isVirtual", getIsVirtual())
            .append("status", getStatus())
            .append("flowSign", getFlowSign())
            .append("remark1", getRemark1())
            .append("remark2", getRemark2())
            .append("remark3", getRemark3())
            .append("remark4", getRemark4())
            .append("remark5", getRemark5())
            .append("remark6", getRemark6())
            .append("remark7", getRemark7())
            .append("remark8", getRemark8())
            .append("remark9", getRemark9())
            .append("remark10", getRemark10())
            .append("infId", getInfId())
            .append("infDate", getInfDate())
            .append("infStatus", getInfStatus())
            .append("costid", getCostid())
            .toString();
    }
}
