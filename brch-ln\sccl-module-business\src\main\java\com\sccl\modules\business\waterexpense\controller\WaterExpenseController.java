package com.sccl.modules.business.waterexpense.controller;

import com.alibaba.fastjson.JSONObject;
import com.sccl.common.support.Convert;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.oilexpense.mapper.OilExpenseMapper;
import com.sccl.modules.business.waterexpense.domain.WaterExpense;
import com.sccl.modules.business.waterexpense.service.IWaterExpenseService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 水基础 信息操作处理
 *
 * <AUTHOR>
 * @date 2022-01-06
 */
@RestController
@RequestMapping("/business/waterExpense")
public class WaterExpenseController extends BaseController {
    private final String prefix = "business/waterExpense";

    @Autowired
    private IWaterExpenseService waterExpenseService;

    @Resource
    OilExpenseMapper oilExpenseMapper;

    @RequiresPermissions("business:waterExpense:view")
    @GetMapping()
    public String waterExpense() {
        return prefix + "/waterExpense";
    }

    /**
     * 查询水基础列表
     */
//    @RequiresPermissions("business:waterExpense:list")
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(WaterExpense waterExpense) {
        if (waterExpense.getCompany() == -1) {
            waterExpense.setCompany(null);
        }
        if (waterExpense.getCountry() == -1) {
            waterExpense.setCountry(null);
        }
        startPage();
        List<WaterExpense> list = waterExpenseService.selectList(waterExpense);
        return getDataTable(list);
    }

    /**
     * 新增水基础
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存水基础
     */
//    @RequiresPermissions("business:waterExpense:add")
    @Log(title = "水基础", action = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody WaterExpense waterExpense) {
        try {
            waterExpenseService.insert(waterExpense);
            if (waterExpense.getStationCode() != null && !waterExpense.getStationCode().equals("")) {
                String[] split = waterExpense.getStationCode().split(",");
                for (String stationId : split) {
                    oilExpenseMapper.saveMachineAndStation(Long.valueOf(stationId), waterExpense.getId());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("添加失败");
        }
        return AjaxResult.success("操作成功");
    }

    /**
     * 修改水基础
     */
    @GetMapping("/edit/{id}")
    public AjaxResult edit(@PathVariable("id") Long id) {
        WaterExpense waterExpense = waterExpenseService.get(id);
        Object object = JSONObject.toJSON(waterExpense);
        return this.success(object);
    }

    /**
     * 修改保存水基础
     */
//    @RequiresPermissions("business:waterExpense:edit")
    @Log(title = "水基础", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody WaterExpense waterExpense) {
        if (waterExpense.getStationCode() != null && !waterExpense.getStationCode().equals("")) {
            oilExpenseMapper.updateMachineAndStation(waterExpense.getId()); // 删除
            String[] split = waterExpense.getStationCode().split(",");
            for (String stationId : split) {
                oilExpenseMapper.saveMachineAndStation(Long.valueOf(stationId), waterExpense.getId());
            }
        }
        return toAjax(waterExpenseService.updateForModel(waterExpense));
    }

    /**
     * 删除水基础
     */
//    @RequiresPermissions("business:waterExpense:remove")
    @Log(title = "水基础", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(waterExpenseService.deleteByIds(Convert.toStrArray(ids)));
    }


    /**
     * 查看水基础
     */
//    @RequiresPermissions("business:waterExpense:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id) {
        WaterExpense waterExpense = waterExpenseService.get(id);
        Object object = JSONObject.toJSON(waterExpense);
        return this.success(object);
    }

}
