package com.sccl.modules.business.stationequipment.mapper;

import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.budget.domain.PageBean;
import com.sccl.modules.business.stationequipment.domain.StationEquipment;
import com.sccl.modules.business.stationequipment.domain.TowerStationEquipment2;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 铁塔站址设备 数据层
 *
 * <AUTHOR>
 * @date 2022-08-09
 */
public interface StationEquipmentMapper extends BaseMapper<StationEquipment> {
    /**
     * 查询指定组ID的最新的一条记录
     *
     * @param groupId 设备组ID
     * @return com.sccl.modules.business.equipmentdict.domain.EquipmentDict
     * <AUTHOR>
     * @date 2022/8/9 15:39
     */
    StationEquipment getLatest(@Param("groupId") long groupId);

    /**
     * 批量获取指定GroupId的所有最新版本的设备
     *
     * @param groupIds 要查询的组Id
     * @return java.util.List<com.sccl.modules.business.equipmentdict.domain.EquipmentDict>
     * <AUTHOR>
     * @date 2022/8/10 10:20
     */
    List<TowerStationEquipment2> getLatests(List<Long> groupIds);

    /**
     * 获取所有设备表，该方法返回的设备是最新版本的设备
     *
     * @return java.util.List<com.sccl.modules.business.equipmentdict.domain.EquipmentDict>
     * <AUTHOR> Yongxiang
     * @date 2022/8/9 16:37
     */
    List<StationEquipment> listAll();

    List<TowerStationEquipment2> listAllConditional(TowerStationEquipment2 stationEquipment);

    /**
     * 获取某个设备的所有历史版本
     *
     * @param groupId 设备组Id
     * @return java.util.List<com.sccl.modules.business.equipmentdict.domain.EquipmentDict>
     * <AUTHOR> Yongxiang
     * @date 2022/8/9 16:39
     */
    List<StationEquipment> listHistory(@Param("groupId") long groupId);

    /**
     * 逻辑删除某设备的所有信息
     *
     * @param groupId 组Id
     * @return int
     * <AUTHOR> Yongxiang
     * @date 2022/8/9 17:18
     */
    int deleteByGroupId(@Param("groupId") long groupId);


    int insertList2(List<TowerStationEquipment2> content);

    List<TowerStationEquipment2> selectList1(TowerStationEquipment2 towerStationEquipment2s
    );

    List<TowerStationEquipment2> selectList2(TowerStationEquipment2 towerStationEquipment2s);

    List<TowerStationEquipment2> selectList3(TowerStationEquipment2 towerStationEquipment2s);

    Long selectList3num(TowerStationEquipment2 towerStationEquipment2s);

    /**
     * @param id 根据 报账id 查询最近 一条站址设备信息
     * @return
     */
    TowerStationEquipment2 getNewestStation(Long id);

    /**
     * 获取 集团下发异常所有
     *
     * @param pageBean
     * @return
     */
    List<TowerStationEquipment2> getAll(@Param("item") PageBean pageBean);

    /**
     * 根据 能耗stationId 推导 集团事前站址设备
     *
     * @param stationEquipment
     * @param billId
     * @return
     */
    List<StationEquipment> selectListForStationId(@Param("stationCode") String stationCode,
                                                  @Param("billId") Long billId);

    int deleteRepeat();

    int insertTowerInfo();

    int deleteAll();

    int insertList2_Temporary(List newList);

    String selCity(String orgcode);

    String selCity2(@Param("orgcode") String orgcode);
}