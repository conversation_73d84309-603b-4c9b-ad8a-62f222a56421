package com.sccl.modules.business.stationinfo.service;

import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.business.stationinfo.domain.StationRecord;
import com.sccl.modules.business.stationinfo.mapper.StationRecordMapper;
import com.sccl.modules.system.user.domain.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


/**
 * 局站历史 服务层实现
 * 
 * <AUTHOR>
 * @date 2019-05-31
 */
@Service
public class StationRecordServiceImpl extends BaseServiceImpl<StationRecord> implements IStationRecordService
{
    @Autowired
    StationRecordMapper stationRecordMapper;

    @Value("${sccl.deployTo}")
    private String configVersion;

    /**
     * 通过通过当前登陆用户取对应局站最新一条数据
     * @param id
     * @return
     */
    public StationRecord getStationNewInfo(String id){
        StationRecord stationRecord = new StationRecord();
        stationRecord.setStationid(Long.parseLong(id));
        if ("sc".equalsIgnoreCase(configVersion)) {
            stationRecord.setDeployTo("sc");
        } else {
            stationRecord.setDeployTo("ln");
        }
        User user = ShiroUtils.getUser();
        stationRecord.setCreateuser(user.getId());
        stationRecord=stationRecordMapper.getByStationId(stationRecord);
        return stationRecord;
    }

    /**
     * 通过传入的userid取最新一条该user数据
     * @param id
     * @param userid
     * @return
     */
    public StationRecord getStationNewInfoView(String id,String userid){
        StationRecord stationRecord = new StationRecord();
        stationRecord.setStationid(Long.parseLong(id));
        stationRecord.setCreateuser(new Long(userid));
        if ("sc".equalsIgnoreCase(configVersion)) {
            stationRecord.setDeployTo("sc");
        } else {
            stationRecord.setDeployTo("ln");
        }
        stationRecord=stationRecordMapper.getByStationId(stationRecord);
        return stationRecord;
    }
    /**
     * 只通过id取最新一条对应局站的数据
     * @param id
     * @return
     */
    public StationRecord getStationNewInfoById(String id){
        StationRecord stationRecord = new StationRecord();
        stationRecord.setStationid(Long.parseLong(id));
        if ("sc".equalsIgnoreCase(configVersion)) {
            stationRecord.setDeployTo("sc");
        } else {
            stationRecord.setDeployTo("ln");
        }
        stationRecord=stationRecordMapper.getByStationId(stationRecord);
        return stationRecord;
    }

    public int deleteByStationId(String[] ids){
        return stationRecordMapper.deleteByStationId(ids);
    }

    @Override
    public List<StationRecord> selectObjectBy(Map<String,Object> params){
        if ("sc".equalsIgnoreCase(configVersion)) {
            params.put("deployTo", "sc");
        } else {
            params.put("deployTo", "ln");
        }
        return stationRecordMapper.selectObjectBy(params);
    }
}
