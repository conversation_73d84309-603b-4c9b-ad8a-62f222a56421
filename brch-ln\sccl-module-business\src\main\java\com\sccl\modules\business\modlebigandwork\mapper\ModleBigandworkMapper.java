package com.sccl.modules.business.modlebigandwork.mapper;

import com.sccl.modules.business.modlebigandwork.domain.ModleBigandwork;
import com.sccl.framework.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 单价-大工业-办公  数据层
 * 
 * <AUTHOR>
 * @date 2023-03-13
 */
public interface ModleBigandworkMapper extends BaseMapper<ModleBigandwork>
{


    int insertListFortemporary(List newList);

    void deleteRepeat();

    int insertInfo();

    List<String> selectAll();

    List<ModleBigandwork> selectListByAccountNo(List<String> accountNos);

    int updatebitch(@Param("list") List<ModleBigandwork> modleBigandworks);
}