package com.sccl.modules.inspection.inspectioninfo.mapper;

import com.sccl.modules.inspection.inspectioninfo.domain.InspectionInfo;
import com.sccl.framework.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 巡检记录 数据层
 * 
 * <AUTHOR>
 * @date 2019-05-27
 */
public interface InspectionInfoMapper extends BaseMapper<InspectionInfo>
{

	public List<InspectionInfo> selectListByCondition (InspectionInfo inspectionInfo);

	public int updateForInspectByModel(InspectionInfo inspectionInfo);

	public List getAllAccountno(@Param(value="company") String company);
}