package com.sccl.modules.business.powerauditstaiongrade.entity;

import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class TowerResultSummaryPro {
    private String company;
    private String companyName;
    private String country;
    private String countryName;
    /**
     * 1月
     */
    private String time1;
    /**
     * 稽核数量
     */
    private String towernum1;
    /**
     * 稽核通过比例
     */
    private String scale1;
    private String time2;
    private String towernum2;
    private String scale2;
    private String time3;
    private String towernum3;
    private String scale3;
    private String time4;
    private String towernum4;
    private String scale4;
    private String time5;
    private String towernum5;
    private String scale5;
    private String time6;
    private String towernum6;
    private String scale6;
    private String time7;
    private String towernum7;
    private String scale7;
    private String time8;
    private String towernum8;
    private String scale8;
    private String time9;
    private String towernum9;
    private String scale9;
    private String time10;
    private String towernum10;
    private String scale10;
    private String time11;
    private String towernum11;
    private String scale11;
    private String time12;
    private String towernum12;
    private String scale12;


    public static List<TowerResultSummaryPro> mergeTowerResultSummaries(List<TowerResultSummary> list) {
        List<TowerResultSummaryPro> summaryPros = new ArrayList<>();
        Map<String, TowerResultSummaryPro> map = new HashMap<>();

        for (TowerResultSummary summary : list) {
            String key = summary.getCompany() + "_" + summary.getCountry();
            String time = summary.getTime();
            TowerResultSummaryPro summaryPro = map.get(key);

            if (summaryPro == null) {
                summaryPro = new TowerResultSummaryPro();
                summaryPro.setCompany(summary.getCompany());
                summaryPro.setCompanyName(summary.getCompanyName());
                summaryPro.setCountry(summary.getCountry());
                summaryPro.setCountryName(summary.getCountryName());
                map.put(key, summaryPro);
                summaryPros.add(summaryPro);
            }

            int month = getMonthFromTime(time);
            //setMonthData(summaryPro, month, time, summary.getTowernum(), summary.getScale());
        }

        return summaryPros;
    }

    private static int getMonthFromTime(String time) {
        // 解析时间字符串，获取月份，这里简化为假设时间字符串的格式为'yyyy-MM'
        String[] parts = time.split("-");
        if (parts.length == 2) {
            return Integer.parseInt(parts[1]);
        }
        return 0;
    }

    private static void setMonthData(TowerResultSummaryPro summaryPro, int month, String time, String towernum, String scale) {
        switch (month) {
            case 1:
                summaryPro.setTime1(time);
                summaryPro.setTowernum1(towernum);
                summaryPro.setScale1(scale);
                break;
            case 2:
                summaryPro.setTime2(time);
                summaryPro.setTowernum2(towernum);
                summaryPro.setScale2(scale);
                break;
            case 3:
                summaryPro.setTime3(time);
                summaryPro.setTowernum3(towernum);
                summaryPro.setScale3(scale);
                break;
            case 4:
                summaryPro.setTime4(time);
                summaryPro.setTowernum4(towernum);
                summaryPro.setScale4(scale);
                break;
            case 5:
                summaryPro.setTime5(time);
                summaryPro.setTowernum5(towernum);
                summaryPro.setScale5(scale);
                break;
            case 6:
                summaryPro.setTime6(time);
                summaryPro.setTowernum6(towernum);
                summaryPro.setScale6(scale);
                break;
            case 7:
                summaryPro.setTime7(time);
                summaryPro.setTowernum7(towernum);
                summaryPro.setScale7(scale);
                break;
            case 8:
                summaryPro.setTime8(time);
                summaryPro.setTowernum8(towernum);
                summaryPro.setScale8(scale);
                break;
            case 9:
                summaryPro.setTime9(time);
                summaryPro.setTowernum9(towernum);
                summaryPro.setScale9(scale);
                break;
            case 10:
                summaryPro.setTime10(time);
                summaryPro.setTowernum10(towernum);
                summaryPro.setScale10(scale);
                break;
            case 11:
                summaryPro.setTime11(time);
                summaryPro.setTowernum11(towernum);
                summaryPro.setScale11(scale);
                break;
            case 12:
                summaryPro.setTime12(time);
                summaryPro.setTowernum12(towernum);
                summaryPro.setScale12(scale);
                break;
        }
    }

}
