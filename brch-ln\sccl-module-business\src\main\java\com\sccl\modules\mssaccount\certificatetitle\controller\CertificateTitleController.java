package com.sccl.modules.mssaccount.certificatetitle.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.mssaccount.certificatetitle.domain.CertificateTitle;
import com.sccl.modules.mssaccount.certificatetitle.service.ICertificateTitleService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * view_certificate_title 信息操作处理
 * 
 * <AUTHOR>
 * @date 2019-09-20
 */
@RestController
@RequestMapping("/mssaccount/certificateTitle")
public class CertificateTitleController extends BaseController
{
    private String prefix = "mssaccount/certificateTitle";
	
	@Autowired
	private ICertificateTitleService certificateTitleService;
	
	@RequiresPermissions("mssaccount:mssaccountbill:view")
	@GetMapping()
	public String certificateTitle()
	{
	    return prefix + "/certificateTitle";
	}
	
	/**
	 * 查询view_certificate_title列表
	 */
	@RequiresPermissions("mssaccount:mssaccountbill:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(CertificateTitle certificateTitle)
	{
		startPage();
        List<CertificateTitle> list = certificateTitleService.selectCertificateTitleList(certificateTitle);
		return getDataTable(list);
	}
	
	/**
	 * 新增view_certificate_title
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存view_certificate_title
	 */
	@RequiresPermissions("mssaccount:mssaccountbill:add")
	//@Log(title = "view_certificate_title", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody CertificateTitle certificateTitle)
	{		
		return toAjax(certificateTitleService.insert(certificateTitle));
	}

	/**
	 * 修改view_certificate_title
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		CertificateTitle certificateTitle = certificateTitleService.get(id);

		Object object = JSONObject.toJSON(certificateTitle);

        return this.success(object);
	}
	
	/**
	 * 修改保存view_certificate_title
	 */
	@RequiresPermissions("mssaccount:mssaccountbill:edit")
	//@Log(title = "view_certificate_title", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody CertificateTitle certificateTitle)
	{		
		return toAjax(certificateTitleService.update(certificateTitle));
	}
	
	/**
	 * 删除view_certificate_title
	 */
	@RequiresPermissions("mssaccount:mssaccountbill:remove")
	//@Log(title = "view_certificate_title", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(certificateTitleService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看view_certificate_title
     */
    @RequiresPermissions("mssaccount:mssaccountbill:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		CertificateTitle certificateTitle = certificateTitleService.get(id);

        Object object = JSONObject.toJSON(certificateTitle);

        return this.success(object);
    }

}
