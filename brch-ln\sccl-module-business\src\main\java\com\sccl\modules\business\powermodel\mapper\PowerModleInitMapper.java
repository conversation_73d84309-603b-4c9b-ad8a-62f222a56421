package com.sccl.modules.business.powermodel.mapper;

import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.account.domain.Account;
import com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol;
import com.sccl.modules.business.powermodel.entity.BigWork;
import com.sccl.modules.business.powermodel.entity.PowerModleInit;
import com.sccl.modules.business.powermodel.entity.PowerModleOrgCode;
import com.sccl.modules.business.powermodel.entity.SupplyCodeMapBill;
import com.sccl.modules.mssaccount.mssinterface.domain.MeterInfo2;
import com.sccl.modules.mssaccount.mssinterface.domain.MeterInfo3;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 电量模型原始数据(PowerModleInit)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-10-20 10:44:40
 */
@Mapper
public interface PowerModleInitMapper extends BaseMapper<PowerModleInit> {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    PowerModleInit queryById(Long id);

    /**
     * 查询指定行数据
     *
     * @param powerModleInit 查询条件
     * @param pageable       分页对象
     * @return 对象列表
     */
    List<PowerModleInit> queryAllByLimit(@Param("init") PowerModleInit powerModleInit,
                                         @Param("pageable") Pageable pageable);

    /**
     * 统计总行数
     *
     * @param powerModleInit 查询条件
     * @return 总行数
     */
    /*  long count(PowerModleInit powerModleInit);*/

    /**
     * 新增数据
     *
     * @param powerModleInit 实例对象
     * @return 影响行数
     */
    int insert(PowerModleInit powerModleInit);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<PowerModleInit> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<PowerModleInit> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<PowerModleInit> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<PowerModleInit> entities);

    /**
     * 修改数据
     *
     * @param powerModleInit 实例对象
     * @return 影响行数
     */
    int update(PowerModleInit powerModleInit);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * @param list 批量插入数据到 power_modele_init_other表
     * @return
     */
    Integer batchAddToOhter(@Param("entities") List<PowerModleInit> list);

    Integer demo();

    /**
     * @param content
     * @return 批量更新数据 根据主键
     */
    int updateListById(@Param("list") List<PowerModleInit> content);

    /**
     * 插入，如果 主键冲突 则先删除在更新
     *
     * @param content
     * @return
     */
    int replaceList(List<PowerModleInit> content);

    List<PowerModleInit> selectList1(PowerModleInit msg);

    List<Ammeterorprotocol> selectAmmeterorprotocolsForContion(@Param("ammetername") String accountno);

    List<Account> selectTimeForContion(@Param("year") String year, @Param("month") String month,
                                       @Param("ids") List<Long> ids);

    PowerModleInit queryByAccountNo(@Param("accountNo") String accountNo);

    List<PowerModleInit> getPower(@Param("ammeterid") String ammeterid, @Param("startDate") String startDate,
                                  @Param("endDate") String endDate);

    List<PowerModleInit> selectByLoginId(PowerModleInit powerModleInit);


    List<PowerModleOrgCode> selectOrgCode(List<PowerModleInit> content);

    List<PowerModleInit> selectListForCalc(PowerModleInit powerModleInit);

    int selectListForCalcCount(PowerModleInit powerModleInit);

    List<Ammeterorprotocol> selectAmmeterorprotocolsForContion2(@Param("accounts") List<String> accounts);

    List<Account> selectAccountsForcontion(@Param("list") List<String> accounts, @Param("year") String year,
                                           @Param("month") String month);

    int updateForMeterInfo(MeterInfo2 meterinfo);

    int updateForMeterInfoAll(MeterInfo2 meterInfo);

    int updateForModelList(List<PowerModleInit> powerModleInits);

    List<PowerModleInit> selectExistContent(@Param("year") String year, @Param("month") String month,
                                            @Param("list") List<String> userNames);

    int updateForRepeat(List<PowerModleInit> updateContent);

    List<PowerModleInit> selectByContion(@Param("init") PowerModleInit powerModleInit1);

    List<PowerModleInit> selectEffectiveAccno(@Param("list") List<String> accnos);

    List<String> selectaAccnos(PowerModleInit powerModleInit);

    int deleteCalc(PowerModleInit powerModleInit1);

    int updateMeter(List<Ammeterorprotocol> ammeterorprotocols);

    List<Ammeterorprotocol> selectMeter(@Param("list") List<String> accno);

    List<Ammeterorprotocol> quetyMeter(Ammeterorprotocol msg);

    List<Ammeterorprotocol> quetyMeter1(Ammeterorprotocol msg);

    List<SupplyCodeMapBill> selectSupplyCodeMapBill(@Param("list") List<String> accounoTempList, @Param("budget") String budget);

    List<BigWork> selectBigWord();

    boolean calcFlag(PowerModleInit powerModleInit);

    int addMeterInfoAllJt(MeterInfo3 meterinfo);

    int updateMeterInfoAllJt(MeterInfo3 meterinfo);
}

