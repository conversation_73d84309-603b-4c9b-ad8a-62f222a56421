package com.sccl.modules.mssaccount.msscostcenter.service;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.mssaccount.msscostcenter.mapper.MssCostcenterMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sccl.modules.mssaccount.msscostcenter.domain.MssCostcenter;

import java.util.List;


/**
 * 记账成本中心 服务层实现
 * 
 * <AUTHOR>
 * @date 2019-05-01
 */
@Service
public class MssCostcenterServiceImpl extends BaseServiceImpl<MssCostcenter> implements IMssCostcenterService
{

    @Autowired
    MssCostcenterMapper mssCostcenterMapper;

    @Override
    public List<MssCostcenter> selectByLikeAuto(MssCostcenter mssCostcenter) {
        return mssCostcenterMapper.selectByLikeAuto(mssCostcenter);
    }
}
