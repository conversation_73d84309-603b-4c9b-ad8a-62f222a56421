package com.sccl.modules.business.stationreportwhitelist.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

/**
 * <AUTHOR>
 * @date 2024/4/26 10:40
 * @describe 流程实例表,这里做查询使用，不与其他的冲突
 */
@Getter
@Setter
@TableName(value = "wf_proc_inst")
public class WhitelistWfProcInst {

    /**
     * 业务主键
     */
    private Long id;

    /**
     * 业务模块编码
     */
    private String busiAlias;

    @Override
    public String toString() {
    	return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }
}
