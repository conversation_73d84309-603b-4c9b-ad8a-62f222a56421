package com.sccl.modules.business.stationreportwhitelist.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.stationreportwhitelist.dto.PowerStationInfoQuery;
import com.sccl.modules.business.stationreportwhitelist.service.PowerStationInfoService;
import com.sccl.modules.business.stationreportwhitelist.vo.PowerStationInfoVO;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/9 17:35
 * @describe 局站管理
 */
@RestController
@AllArgsConstructor
@RequestMapping("/powerStationInfo")
public class PowerStationInfoController extends BaseController {

    private final PowerStationInfoService stationInfoService;

    /**
     * 一站多表 站址列表
     */
    @GetMapping(value = "/oneStopIsMoreThanOneWatch")
    public TableDataInfo oneStopIsMoreThanOneWatch(Page<PowerStationInfoVO> page, PowerStationInfoQuery query) {
        IPage<PowerStationInfoVO> pages = stationInfoService.oneStopIsMoreThanOneWatch(page,query);
        return getDataTable(pages.getRecords(), pages.getTotal());
    }

    /**
     * 一表多站 局站列表
     */
    @GetMapping(value = "/oneTableMultiStationList")
    public AjaxResult oneTableMultiStationList(Page<PowerStationInfoVO> page, PowerStationInfoQuery query) {
        List<PowerStationInfoVO> list = stationInfoService.oneTableMultiStationList(page,query);
        return AjaxResult.success(list);
    }

}
