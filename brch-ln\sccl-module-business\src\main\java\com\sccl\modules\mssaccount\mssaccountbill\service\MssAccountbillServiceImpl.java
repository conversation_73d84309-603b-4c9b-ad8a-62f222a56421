package com.sccl.modules.mssaccount.mssaccountbill.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.sccl.common.lang.StringUtils;
import com.sccl.common.utils.DateUtils;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.common.utils.TimeUtils;
import com.sccl.framework.common.exception.base.BaseException;
import com.sccl.framework.common.exception.budget.BudgetException;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.service.IdGenerator;
import com.sccl.modules.business.account.domain.AccountAmount;
import com.sccl.modules.business.account.domain.AccountAmountStationInfo;
import com.sccl.modules.business.accountbillitempre.domain.Accountbillitempre;
import com.sccl.modules.business.accountbillpre.domain.Accountbillpre;
import com.sccl.modules.business.accountbillpre.mapper.AccountbillpreMapper;
import com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol;
import com.sccl.modules.business.ammeterorprotocol.mapper.AmmeterorprotocolMapper;
import com.sccl.modules.business.budget.service.IBudgetService;
import com.sccl.modules.business.budgetreimbursementhistory.domain.BudgetReimbursementHistory;
import com.sccl.modules.business.budgetreimbursementhistory.mapper.BudgetReimbursementHistoryMapper;
import com.sccl.modules.business.meterinfo.mapper.MeterinfoMapper;
import com.sccl.modules.business.stationaudit.controller;
import com.sccl.modules.business.stationinfo.mapper.PowerStationInfoRJtlteMapper;
import com.sccl.modules.business.stationreportwhitelist.domain.PowerCategoryType;
import com.sccl.modules.business.stationreportwhitelist.mapper.PowerCategoryTypeMapper;
import com.sccl.modules.business.syncresult.domain.Syncresult;
import com.sccl.modules.business.syncresult.mapper.SyncresultMapper;
import com.sccl.modules.mssaccount.mssabccustomerbank.domain.MssAbccustomerBank;
import com.sccl.modules.mssaccount.mssabccustomerbank.mapper.MssAbccustomerBankMapper;
import com.sccl.modules.mssaccount.mssaccountbill.controller.MssAccountbillController;
import com.sccl.modules.mssaccount.mssaccountbill.domain.AccountBillRequest;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;
import com.sccl.modules.mssaccount.mssaccountbill.domain.PrepaidAccountBillDTO;
import com.sccl.modules.mssaccount.mssaccountbill.domain.StatisticalAccountBillDTO;
import com.sccl.modules.mssaccount.mssaccountbill.frame.*;
import com.sccl.modules.mssaccount.mssaccountbill.mapper.MssAccountbillMapper;
import com.sccl.modules.mssaccount.mssaccountbillitem.domain.MssAccountbillitem;
import com.sccl.modules.mssaccount.mssaccountbillitem.mapper.MssAccountbillitemMapper;
import com.sccl.modules.mssaccount.mssaccountbillpayinfo.domain.MssAccountbillpayinfo;
import com.sccl.modules.mssaccount.mssaccountbillpayinfo.mapper.MssAccountbillpayinfoMapper;
import com.sccl.modules.mssaccount.mssaccountclearitem.domain.MssAccountclearitem;
import com.sccl.modules.mssaccount.mssaccountclearitem.mapper.MssAccountclearitemMapper;
import com.sccl.modules.mssaccount.mssaccountclearitemaccount.domain.MssAccountclearitemAccount;
import com.sccl.modules.mssaccount.mssaccountclearitemaccount.mapper.MssAccountclearitemAccountMapper;
import com.sccl.modules.mssaccount.mssinterface.service.IMssInterfaceService;
import com.sccl.modules.mssaccount.msssupplieritem2.domain.MssSupplierItem2;
import com.sccl.modules.mssaccount.msssupplieritem2.mapper.MssSupplierItem2Mapper;
import com.sccl.modules.mssaccount.rbillitemaccount.domain.RBillitemAccount;
import com.sccl.modules.mssaccount.rbillitemaccount.mapper.RBillitemAccountMapper;
import com.sccl.modules.mssaccount.rbillitemaccount.service.IRBillitemAccountService;
import com.sccl.modules.system.attachments.domain.Attachments;
import com.sccl.modules.system.attachments.mapper.AttachmentsMapper;
import com.sccl.modules.system.notice.domain.StationError;
import com.sccl.modules.system.organization.domain.Organization;
import com.sccl.modules.system.organization.mapper.OrganizationMapper;
import com.sccl.modules.system.user.domain.User;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static java.lang.Math.abs;

/**
 * 报账 服务层实现
 *
 * <AUTHOR>
 * @date 2019-04-24
 */
@Service
@Slf4j
public class MssAccountbillServiceImpl extends BaseServiceImpl<MssAccountbill> implements IMssAccountbillService {

    private static final Logger logger = LoggerFactory.getLogger(MssAccountbillServiceImpl.class);
    @Autowired
    controller auditOperation;
    @Autowired
    MssAccountbillMapper billmmapper;//报账单
    @Autowired
    MssAccountbillitemMapper itemmapper;//详情
    @Autowired
    MssAccountbillpayinfoMapper payinfoMapper;//外部收款人
    @Autowired
    MssSupplierItem2Mapper supplierItem2Mapper;//供应商外部收款人
    @Autowired
    MssAbccustomerBankMapper abccustomerBankMapper;// 客户外部收款人
    @Autowired
    MssAccountclearitemMapper clearitemMapper;// 挑对单
    @Autowired
    MssAccountclearitemAccountMapper clearitemAccountMapper;// 挑对单台账
    @Autowired
    AccountbillpreMapper accountbillpreMapper;// 归集单
    @Autowired
    RBillitemAccountMapper rBillitemAccountMapper;// 报账明细 台账 关联
    @Autowired
    IRBillitemAccountService rBillitemAccountService;// 报账明细 台账 关联
    @Autowired
    PowerStationInfoRJtlteMapper powerStationInfoRJtlteMapper;//集团4Glte
    @Autowired
    OrganizationMapper organizationMapper;
    @Autowired
    private SyncresultMapper syncresultMapper;
    @Value("${sccl.deployTo}")
    private String deployTo;
    @Autowired
    private BudgetReimbursementHistoryMapper budgetReimbursementHistoryMapper;
    @Autowired
    private AttachmentsMapper attachmentsMapper;//辽宁预提报账单需添加附件
    @Autowired
    private IBudgetService budgetService;
    @Lazy
    @Autowired
    private IMssInterfaceService mssInterfaceService;
    public static boolean excludeFlag = false;

    @Autowired
    private PowerCategoryTypeMapper powerCategoryTypeMapper;

    @Autowired
    private AmmeterorprotocolMapper ammeterorprotocolMapper;

    @Autowired
    private RBillitemAccountMapper billitemAccountMapper;

    @Autowired
    MeterinfoMapper meterinfoMapper;



    @Override //新增、保存自有 报账单
    public Map<String, Object> saveMssAccountbill(MssAccountbill mssAccountbill) {
        Map<String, Object> map = checkMssAccountbill(mssAccountbill);
        try {
            AtomicBoolean budgetFlag = new AtomicBoolean(false);
            if ((boolean) map.get("success")) {
                // 判断是否为回收电费，若是回收电费，则金额为正数
                mssAccountbill.getItem().forEach(node->{
                    AtomicBoolean flag = new AtomicBoolean(false);
                    node.getRbillitemaccount().forEach(item->{
                        if("3".equals(item.getAmmeteruse())){
                            mssAccountbill.setSum(mssAccountbill.getSum().abs());
                            mssAccountbill.setInputTaxSum(ObjectUtil.isNotNull(mssAccountbill.getInputTaxSum())?mssAccountbill.getInputTaxSum().abs():BigDecimal.ZERO);
                            mssAccountbill.setTaxAdjustSum(ObjectUtil.isNotNull(mssAccountbill.getTaxAdjustSum())?mssAccountbill.getTaxAdjustSum().abs():BigDecimal.ZERO);
                            flag.set(true);
                        }
                    });
                    if(flag.get()){
                        node.setAmount(ObjectUtil.isNotNull(node.getAmount())?node.getAmount().abs():BigDecimal.ZERO);
                        node.setSum(node.getSum().abs());
                        node.setTaxAdjustSum(ObjectUtil.isNotNull(node.getTaxAdjustSum())?node.getTaxAdjustSum().abs():BigDecimal.ZERO);
                    }
                    // 判断是否进行预算的校验
                    if (!"2".equals(node.getBudgetType())) {
                        budgetFlag.set(true);
                    }
                });
                //增加判断isNeedImage状态  qinxinmin  2024-06-19
                if (mssAccountbill.getBilltype().compareTo(new BigDecimal(10)) == 0 &&
                        "12".equals(mssAccountbill.getInvoiceType())) {
                    //报账单类型为 预提冲销，票据类型为电子发票-普票时，
                    mssAccountbill.setIsNeedImage(new BigDecimal(0));
                } else {
                    mssAccountbill.setIsNeedImage(new BigDecimal(1));
                }
                MssAccountbill accountbill = this.get(mssAccountbill.getId());
                mssAccountbill.setHappenDate(DateUtils.formatDate(DateUtils.parseDate(mssAccountbill.getHappenDate())
                        , "yyyy-MM-dd"));// 处理日期 "2019-06-05 00:00:00"
                if (accountbill == null) {// 查询为空 则插入新的报账单
                    mssAccountbill.setTimestamp(new Date());
                    mssAccountbill.setCreateDate(new Date());
                    mssAccountbill.setStatus(1);//状态1,'草稿',2,'待办',3,'生成报帐单',4,'生成凭证',7,'完成',5,'财务通过',-1,'报帐单删除',-2,
                    // '退单',-4,'等待生成,-3,'送财辅失败',8 '财辅退单' */
                    List<MssAccountbill> blist = new ArrayList<>();
                    if ("sc".equals(deployTo)) {
                        List<MssAccountbillitem> itemlist = mssAccountbill.getItem();
                        for (MssAccountbillitem item : itemlist) {
                            if (item.getUsageId().equals("2")) // 如果是代垫 增加统御科目
                            {
                                item.setReconciliationAccountCode("**********");
                                item.setReconciliationAccountName("其他应收款-代垫款-电费");
                            }
                        }
                    }
                    blist.add(mssAccountbill);
                    this.insertList(blist);// 批量插入的方式 Id根据传入的参数 不自动生成
                    insertrbillitemAccounts(mssAccountbill);//插入 明细 和 台账关联关系
                    insertPayinfo(mssAccountbill);// 外部收款人
                    insertClearitem(mssAccountbill.getClearitem());// 挑对信息
                    insertClearitemAccount(mssAccountbill);// 挑对台账信息
                    insertunionpre(mssAccountbill);//关联归集单
                    //if (mssAccountbill.getBizTypeCode().equals("1")||mssAccountbill.getBizTypeCode().equals("2"))
                    insertjtltem(mssAccountbill); //关联集团4Glte铁塔
                    //关联 预算 类型为新增
                    log.info("环境{}", deployTo);
                    if ("ln".equals(deployTo)) {
                        log.info("对billid={}的预算管控", mssAccountbill.getId());
                        if(budgetFlag.get()){
                            boolean b = budgetService.addBudget(mssAccountbill, excludeFlag);
                            if (!b) {
                                logger.warn("billid={}的报账关联 预算失败", mssAccountbill.getId());
                                throw new BudgetException("关联预算失败，请重试");
                            }
                        }
                    }

                    //调用基站一站式稽核
                    log.info("准备开始调用一站式稽核，判定环境:{},报账单id:{}", deployTo, mssAccountbill.getId());
                    /*if ("sc".equals(deployTo)) */
                    {
                        log.info("当前环境为:{}", deployTo);
                        Long billid = mssAccountbill.getId();
                        log.info("对billid={}的开始一站式稽核", billid);
                        if (billid == null) {
                            log.info("billid不存在，稽核不进行");
                        }
                        try {
                            Long billId = mssAccountbill.getId();
                            boolean auditFlag = syncresultMapper.getauditFlag();
                            if (auditFlag) {
                                auditOperation.auditBatchForSaveBill(
                                        "pro_" + billId,
                                        billId
                                );
                            }
                        } catch (Exception e) {
                            insertResult("保存报账单调用基站一站式", 1, "", "失败", "" + mssAccountbill.getId(), e.getMessage(), 1);
                            log.info("保存报账单过程中调用一站式稽核失败");
                        }

                    }

                } else {// 更新
                    this.updateForModel(mssAccountbill);//更新报账单
                    updaterbillitemAccounts(mssAccountbill);//更新 明细 和 台账关联关系
                    updatePayinfo(mssAccountbill); //更新payinfo 外部收款人信息
                    updateClearitem(mssAccountbill);//更新 挑对单
                    updateClearitemAccount(mssAccountbill);//更新 挑对单 台账
                    updateunionpre(mssAccountbill);//更新 归集单
                    updatejtlte(mssAccountbill);

                }

            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("保存报账单异常", e.getMessage());
            throw new BaseException("保存报账单异常:" + e.getMessage());
        }
        return map;
    }

    //新增、保存自有 报账单 报账单删除台账明细重新生成报账单
    public Map<String, Object> saveMssAccountbillPro(MssAccountbill mssAccountbill) {
//        Map<String, Object> map = checkMssAccountbill(mssAccountbill);
        Map<String, Object> map = new HashMap<>();
        map.put("success", true);
        try {
            if ((boolean) map.get("success")) {
                MssAccountbill accountbill = this.get(mssAccountbill.getId());
                mssAccountbill.setHappenDate(DateUtils.formatDate(DateUtils.parseDate(mssAccountbill.getHappenDate())
                        , "yyyy-MM-dd"));// 处理日期 "2019-06-05 00:00:00"// 查询为空 则插入新的报账单
                mssAccountbill.setTimestamp(new Date());
                mssAccountbill.setCreateDate(new Date());
                mssAccountbill.setStatus(1);//状态1,'草稿',2,'待办',3,'生成报帐单',4,'生成凭证',7,'完成',5,'财务通过',-1,'报帐单删除',-2,
                // '退单',-4,'等待生成,-3,'送财辅失败',8 '财辅退单' */
                List<MssAccountbill> blist = new ArrayList<>();
                if ("sc".equals(deployTo)) {
                    List<MssAccountbillitem> itemlist = mssAccountbill.getItem();
                    for (MssAccountbillitem item : itemlist) {
                        if (item.getUsageId().equals("2")) // 如果是代垫 增加统御科目
                        {
                            item.setReconciliationAccountCode("**********");
                            item.setReconciliationAccountName("其他应收款-代垫款-电费");
                        }
                    }
                }
                blist.add(mssAccountbill);
                this.insertList(blist);// 批量插入的方式 Id根据传入的参数 不自动生成
                insertrbillitemAccounts(mssAccountbill);//插入 明细 和 台账关联关系
                insertPayinfo(mssAccountbill);// 外部收款人
                insertClearitem(mssAccountbill.getClearitem());// 挑对信息
                insertClearitemAccount(mssAccountbill);// 挑对台账信息
                insertunionpre(mssAccountbill);//关联归集单
                //if (mssAccountbill.getBizTypeCode().equals("1")||mssAccountbill.getBizTypeCode().equals("2"))
                insertjtltem(mssAccountbill); //关联集团4Glte铁塔
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("保存报账单异常", e.getMessage());
            throw new BaseException("保存报账单异常:" + e.getMessage());
        }
        return map;
    }

    //插入 明细 和 台账关联关系
    private void insertrbillitemAccounts(MssAccountbill mssAccountbill) {
        List<MssAccountbillitem> list = mssAccountbill.getItem();


        for (MssAccountbillitem item : list) {
            item.setId(IdGenerator.getNextId());
            item.setWriteoffInstanceId(BigDecimal.valueOf(mssAccountbill.getId()));
            if ("6".equals(mssAccountbill.getPickingMode().toString()) && ("8".equals(mssAccountbill.getBilltype().toString())))
                insertskRbillitemaccount(item);
            else
                insertRbillitemaccount(item);

        }

        itemmapper.insertList(list);
    }

    private void insertrbillitemAccountsAndDel(MssAccountbill mssAccountbill) {
        List<MssAccountbillitem> list = mssAccountbill.getItem();


        for (MssAccountbillitem item : list) {
            item.setId(IdGenerator.getNextId());
            item.setWriteoffInstanceId(BigDecimal.valueOf(mssAccountbill.getId()));
            if ("6".equals(mssAccountbill.getPickingMode().toString()) && ("8".equals(mssAccountbill.getBilltype().toString())))
                insertskRbillitemaccount(item);
            else
                insertRbillitemaccount(item);

        }
        String billId = list.stream().map(MssAccountbillitem::getWriteoffInstanceId)
                .filter(item -> item != null)
                .map(BigDecimal::toString).findFirst().get();

        itemmapper.deleteByBillId(billId);
        itemmapper.insertList(list);
    }

    // 插入明细 台账关联关系
    private void insertRbillitemaccount(MssAccountbillitem item) {
        List<RBillitemAccount> rbillitemaccount = item.getRbillitemaccount();
        //清理都是0的数据
        rbillitemaccount = rbillitemaccount.stream().filter(e -> e.getMoney() != 0.0 || e.getAccountMoney() != 0.0 || e.getTaxmoney() != 0.0).collect(Collectors.toList());
        if (rbillitemaccount != null && rbillitemaccount.size() > 0) {
            for (RBillitemAccount rb : rbillitemaccount) {
                rb.setId(IdGenerator.getNextId());
                rb.setBillId(item.getWriteoffInstanceId().longValue());
                rb.setBillitemId(item.getId());
            }
            rBillitemAccountMapper.insertList(rbillitemaccount);
        }
    }

    // 插入明细 台账关联关系 收款
    private void insertskRbillitemaccount(MssAccountbillitem item) {
        List<RBillitemAccount> rbillitemaccount = item.getRbillitemaccount();
        //清理都是0的数据
        rbillitemaccount = rbillitemaccount.stream().filter(e -> e.getMoney() != 0.0 || e.getAccountMoney() != 0.0 || e.getTaxmoney() != 0.0).collect(Collectors.toList());
        if (rbillitemaccount != null && rbillitemaccount.size() > 0) {
            for (RBillitemAccount rb : rbillitemaccount) {
                rb.setId(IdGenerator.getNextId());
                rb.setBillId(item.getWriteoffInstanceId().longValue());
                rb.setBillitemId(item.getId());
                rb.setMoney(abs(rb.getMoney()) * -1);
                rb.setAccountMoney(abs(rb.getAccountMoney()) * -1);
                if (rb.getTaxmoney() != 0)
                    rb.setTaxmoney(abs(rb.getTaxmoney()) * -1);
            }
            rBillitemAccountMapper.insertList(rbillitemaccount);
        }
    }

    //更新 明细 和 台账关联关系
    private void updaterbillitemAccounts(MssAccountbill mssAccountbill) {
        List<MssAccountbillitem> list = mssAccountbill.getItem();
        List<MssAccountbillitem> itemlist = new ArrayList<>();
        // 删除 报账单 明细所有的关联关系
        rBillitemAccountMapper.deleteRbillitemAccountByBillId(mssAccountbill.getId());
        for (MssAccountbillitem item : list) {
            if (item.getId() == null) {
                item.setId(IdGenerator.getNextId());
                item.setWriteoffInstanceId(BigDecimal.valueOf(mssAccountbill.getId()));
                itemlist.add(item);
            } else {
                itemmapper.updateForModel(item);
            }
            if ("6".equals(mssAccountbill.getPickingMode().toString()) && ("8".equals(mssAccountbill.getBilltype().toString())))
                insertskRbillitemaccount(item);
            else
                insertRbillitemaccount(item);
            //insertRbillitemaccount(item);// 插入关联关系
        }
        if (itemlist.size() > 0) {
            itemmapper.insertList(itemlist);
        }
    }

    // 关联集团4Glte铁塔
    public void updatejtlte(MssAccountbill mssAccountbill) {
        rBillitemAccountMapper.insertpowerjtltedif(mssAccountbill.getId());
    }

    // 关联集团4Glte铁塔
    public void insertjtltem(MssAccountbill mssAccountbill) {
//找出stationid
        rBillitemAccountMapper.insertpowerjtltenew(mssAccountbill.getId());

        /*if (mssAccountbill.getAccountCode().equals("********")) {
            rBillitemAccountMapper.insertpowerjtlteTA(mssAccountbill.getId());
            List<StationbybillResult> liststation =
                    rBillitemAccountMapper.stationidListBybillId(mssAccountbill.getId());

            for (StationbybillResult item : liststation) {
                List<PowerStationInfoRJtlte> ltelist = rBillitemAccountMapper.stationTarjtlistBybillId(item.getId());
                    if (ltelist.size()>0)
                    {for (PowerStationInfoRJtlte lteitem : ltelist)
                    {   Date date = new Date();
                        PowerStationInfoRJtlte psrl = new PowerStationInfoRJtlte();
                        psrl.setJtlteCode(lteitem.getJtlteCode());
                        psrl.setJtlteName(lteitem.getJtlteName());
                        psrl.setStationid(item.getId());
                        psrl.setBillId(mssAccountbill.getId());
                        psrl.setUpdatetime(date);
                        powerStationInfoRJtlteMapper.insert(psrl);
                    }
                    }
                    else
                    {   Date date = new Date();
                    PowerStationInfoRJtlte psrl = new PowerStationInfoRJtlte();
                    psrl.setStationid(item.getId());
                    psrl.setBillId(mssAccountbill.getId());
                    psrl.setUpdatetime(date);
                    powerStationInfoRJtlteMapper.insert(psrl);
                }

            }

        } else {
            List<StationbybillResult> liststation =
                    rBillitemAccountMapper.stationidListBybillId(mssAccountbill.getId());

            for (StationbybillResult item : liststation) {
                List<PowerStationInfoRJtlte> ltelist = rBillitemAccountMapper.stationrjtlistBybillId(item.getId());
                    if (ltelist.size()>0)
                    {for (PowerStationInfoRJtlte lteitem : ltelist)
                    {   Date date = new Date();
                        PowerStationInfoRJtlte psrl = new PowerStationInfoRJtlte();
                        psrl.setStationid(item.getId());
                        psrl.setBillId(mssAccountbill.getId());
                        psrl.setJtlteCode(lteitem.getJtlteCode());
                        psrl.setJtlteName(lteitem.getJtlteName());
                        psrl.setUpdatetime(date);
                        powerStationInfoRJtlteMapper.insert(psrl);
                    }
                    }
                    else
                    {PowerStationInfoRJtlte psrl=new PowerStationInfoRJtlte();
                    Date date = new Date();
                    psrl.setStationid(item.getId());
                    psrl.setBillId(mssAccountbill.getId());
                    psrl.setUpdatetime(date);
                    powerStationInfoRJtlteMapper.insert(psrl);
                }
            }
        }*/


        //rBillitemAccountMapper.callinsertpowerjtlte(mssAccountbill.getId());

    }


    // 插入归集单
    private void insertunionpre(MssAccountbill mssAccountbill) {
        Accountbillpre accountbillpre = mssAccountbill.getAccountbillpre();
        if (accountbillpre != null) {
            if (StringUtils.isEmpty(accountbillpre.getPabid())) {
                accountbillpre.setPabid(mssAccountbill.getId().toString());
            } else {
                accountbillpre.setPabid(accountbillpre.getPabid() + "," + mssAccountbill.getId().toString());
            }
            accountbillpre.setStatus(2);//已生成报账单
            accountbillpreMapper.updateForModel(accountbillpre);
            //判断是否 合并归集单 并更新状态
            updatepreByParentPre(accountbillpre, 2);

        }
    }

    //判断是否 合并归集单 并更新状态
    private void updatepreByParentPre(Accountbillpre accountbillpre, Integer status) {
        if ("4".equals(accountbillpre.getBilltype())
                || "5".equals(accountbillpre.getBilltype())
                || "6".equals(accountbillpre.getBilltype())
        || "22".equals(accountbillpre.getBilltype())
                || "23".equals(accountbillpre.getBilltype())
                || "24".equals(accountbillpre.getBilltype())) {
            List<Accountbillpre> accountbillpres = accountbillpreMapper.selectByParentid(accountbillpre.getPabrid());
            for (Accountbillpre pre : accountbillpres) {
                pre.setStatus(status);
                accountbillpreMapper.updateForModel(pre);
            }
        }
    }

    // 更新归集单
    private void updateunionpre(MssAccountbill mssAccountbill) {
        Accountbillpre accountbillpre = mssAccountbill.getAccountbillpre();
        if (accountbillpre != null) {
            Accountbillpre auto = new Accountbillpre();
            auto.setPabid(mssAccountbill.getId().toString());//根据 关联的报账单Id 查询归集单 结果为一条
            List<Accountbillpre> list = accountbillpreMapper.selectListByAuto(auto);
            for (Accountbillpre a : list) {
                String pabid = a.getPabid();
                StringBuffer newpabid = handelsplitIds(mssAccountbill.getId(), pabid);
                a.setPabid(newpabid.toString());// 设置值
                if (StringUtils.isNotEmpty(a.getPabid())) {
                    a.setStatus(2);//已生成报账单
                } else {
                    a.setStatus(1);//未生成报账单
                }
                accountbillpreMapper.updateByPrimaryKey(a);// 更新旧的归集单
                //判断是否 合并归集单 并更新状态
                updatepreByParentPre(a, 1);// 解除关联关系
            }
            if (StringUtils.isEmpty(accountbillpre.getPabid())) {
                accountbillpre.setPabid(mssAccountbill.getId().toString());
            } else {
                if (accountbillpre.getPabid().indexOf(mssAccountbill.getId().toString()) < 0) {
                    accountbillpre.setPabid(accountbillpre.getPabid() + "," + mssAccountbill.getId().toString());
                }
            }
            accountbillpre.setStatus(2);//已生成报账单
            accountbillpreMapper.updateForModel(accountbillpre);// 更新关联的新的归集单
            //判断是否 合并归集单 并更新状态
            updatepreByParentPre(accountbillpre, 2);
        }
    }


    private StringBuffer handelsplitIds(Long id, String pabid) {
        String[] split = pabid.split(",");
        StringBuffer newpabid = new StringBuffer();
        for (String str : split) {
            if (!str.equals(id.toString())) {
                newpabid.append(str);
                newpabid.append(",");
            }
        }
        if (StringUtils.isNotEmpty(newpabid.toString()))
            newpabid.deleteCharAt(newpabid.length() - 1);
        return newpabid;
    }

    // 新增报账单 插入 外部收款人信息
    private void insertPayinfo(MssAccountbill mssAccountbill) {
        Long id = mssAccountbill.getId();
        Date date = new Date();
        List<MssAccountbillpayinfo> payinfo = new ArrayList<>();
        List<MssSupplierItem2> supplierItem2 = mssAccountbill.getSupplierItem2();
        if (supplierItem2 != null && supplierItem2.size() > 0) {
            for (MssSupplierItem2 item : supplierItem2) {
                MssAccountbillpayinfo info = new MssAccountbillpayinfo();
                info.setWriteoffInstanceId(id);
                info.setId(IdGenerator.getNextId());
                info.setEmployeename(item.getKoinh());
                info.setEmployeebankac(item.getBankn());
                info.setBank(item.getBanka());
                info.setBankcode(item.getBvtyp());
                info.setPayeecode(item.getLifnr());
                info.setPayeetype("K");//k 供应商 D 客户
                info.setRowno(item.getBankl());
                info.setProvince(item.getProvz());
                info.setCity(item.getOrt01());
                if (item.getSum() != null)
                    info.setSum(item.getSum());
                info.setStatus("1");// 状态0 删除 1正常
                info.setInputdate(date);
                info.setAccountname(item.getZpubPri());
                if (item.getSum() != null)
                    payinfo.add(info);
            }
            payinfoMapper.insertList(payinfo);
        } else {
            List<MssAbccustomerBank> customerBank = mssAccountbill.getCustomerBank();
            if (customerBank != null && customerBank.size() > 0) {
                for (MssAbccustomerBank item : customerBank) {
                    MssAccountbillpayinfo info = new MssAccountbillpayinfo();
                    info.setWriteoffInstanceId(id);
                    info.setId(IdGenerator.getNextId());
                    info.setEmployeename(item.getKoinh());
                    info.setEmployeebankac(item.getBankn());
                    info.setBank(item.getZbanka());
                    info.setBankcode(item.getBgrup());
                    info.setPayeecode(item.getKunnr());
                    info.setPayeetype("D");//k 供应商 D 客户
                    info.setRowno(item.getBankl());
                    info.setProvince(item.getProvz());
                    info.setCity(item.getCity());
                    if (item.getSum() != null)
                        info.setSum(item.getSum());
                    info.setStatus("1");// 状态0 删除 1正常
                    info.setInputdate(date);
                    info.setAccountname(item.getPublicPrivate());
                    if (item.getSum() != null)
                        payinfo.add(info);
                }
                payinfoMapper.insertList(payinfo);
            }
        }
    }

    // 更新外部收款人信息
    private void updatePayinfo(MssAccountbill mssAccountbill) {
        List<MssAccountbillpayinfo> accountbillpayinfo = mssAccountbill.getPayinfo();
        if (accountbillpayinfo != null && accountbillpayinfo.size() > 0) {
            Date date = new Date();
            List<MssAccountbillpayinfo> payinfolist = new ArrayList<>();
            for (MssAccountbillpayinfo item : accountbillpayinfo) {
                if (item.getId() == null) {
                    item.setId(IdGenerator.getNextId());
                    item.setWriteoffInstanceId(mssAccountbill.getId());
                    item.setInputdate(date);
                    if (item.getSum() != null)
                        payinfolist.add(item);
                }
            }
            if (payinfolist.size() > 0) {
                Map<String, Object> map = new HashMap<>();
                map.put("writeoffInstanceId", mssAccountbill.getId());
                map.put("findBy", true);
                List<MssAccountbillpayinfo> list2 = payinfoMapper.selectByMap(map);
                String[] ids = new String[list2.size()];
                int i = 0;
                for (MssAccountbillpayinfo item : list2) {
                    ids[i] = item.getId().toString();
                    i++;
                }
                if (ids.length > 0)
                    payinfoMapper.deleteByIdsDB(ids);//删除旧的
                payinfoMapper.insertList(payinfolist);
            } else {
                for (MssAccountbillpayinfo item : accountbillpayinfo) {
                    if (item.getSum() != null) {
                        payinfoMapper.updateForModel(item);
                    } else {
                        payinfoMapper.deleteByIdsDB(new String[]{item.getId().toString()});
                    }
                }
            }
        }
    }

    // 插入挑对单
    private void insertClearitem(List<MssAccountclearitem> clearitem) {
        if (clearitem != null && clearitem.size() > 0) {
            User user = ShiroUtils.getUser();
            String id = user.getDepartments().get(0).getId();
//            String id1 = user.getCompanies().get(0).getId();
            for (MssAccountclearitem item : clearitem) {
                item.setClearid(IdGenerator.getNextId());
                item.setInputdate(new Date());
                item.setOrgid(Long.valueOf(id));
//                item.setCompanycode(id1);
            }
            clearitemMapper.insertList(clearitem);
        }
    }

    //更新挑对单
    private void updateClearitem(MssAccountbill mssAccountbill) {
        List<MssAccountclearitem> clearitem = mssAccountbill.getClearitem();
        // 收款  othersystemdetailid 必填 为对应明细id
        if (clearitem != null && clearitem.size() > 0)
            unionClearAnDBillDetailBySK(mssAccountbill, clearitem);
        if (clearitem != null && clearitem.size() > 0) {
            List<MssAccountclearitem> clearitemnew = new ArrayList<>();
            for (MssAccountclearitem item : clearitem) {
                if (item.getClearid() != null) {
                    clearitemMapper.updateForModel(item);
                } else {
                    item.setWriteoffFirstId(mssAccountbill.getId());
                    clearitemnew.add(item);
                }
            }
            insertClearitem(clearitemnew);
        }
    }

    // 插入挑对单 台账
    private void insertClearitemAccount(MssAccountbill mssAccountbill) {
        List<MssAccountclearitemAccount> clearitemAccount = mssAccountbill.getClearitemAccount();
        if (clearitemAccount != null && clearitemAccount.size() > 0) {
            Date date = new Date();
            for (MssAccountclearitemAccount item : clearitemAccount) {
                item.setId(IdGenerator.getNextId());
                item.setBillId(mssAccountbill.getId());
                item.setStatus(1);
                item.setInputDate(date);
            }
            clearitemAccountMapper.insertList(clearitemAccount);
        }
    }

    /////////////////////////////////////////////////////////////////////////////
    // 查询报账单
    /////////////////////////////////////////////////////////////////////////////

    //更新挑对单
    private void updateClearitemAccount(MssAccountbill mssAccountbill) {
        List<MssAccountclearitemAccount> clearitemAccount = mssAccountbill.getClearitemAccount();
        if (clearitemAccount != null && clearitemAccount.size() > 0) {
            List<MssAccountclearitemAccount> clearitemnew = new ArrayList<>();
            for (MssAccountclearitemAccount item : clearitemAccount) {
                if (item.getId() != null) {
                    clearitemAccountMapper.updateForModel(item);
                } else {
                    item.setId(IdGenerator.getNextId());
                    item.setBillId(mssAccountbill.getId());
                    item.setStatus(1);
                    item.setInputDate(new Date());
                    clearitemnew.add(item);
                }
            }
            if (clearitemnew != null && clearitemnew.size() > 0)
                clearitemAccountMapper.insertList(clearitemnew);
        }
    }

    // 收款  othersystemdetailid 必填 为对应明细id
    private void unionClearAnDBillDetailBySK(MssAccountbill mssAccountbill, List<MssAccountclearitem> clearitem) {
        try {
            Map<BigDecimal, String> cm = new HashMap<>();
            List<MssAccountbillitem> list = mssAccountbill.getItem();
            for (MssAccountbillitem i : list) {
                cm.put(i.getSum(), i.getId().toString());
            }
            Set<Map.Entry<BigDecimal, String>> entries = cm.entrySet();
            for (MssAccountclearitem a : clearitem) {
                // 验证挑对 关联明细id 是否存在
                if (StringUtils.isNotEmpty(a.getOthersystemdetailid())) {
                    boolean f = false;
                    for (Map.Entry<BigDecimal, String> m : entries) {
                        if (m.getValue().equals(a.getOthersystemdetailid())) {
                            f = true;
                            break;
                        }
                    }
                    if (!f) {
                        a.setOthersystemdetailid(null);
                    }
                }
                // 方案 一 一明细对二挑对
                if (StringUtils.isEmpty(a.getOthersystemdetailid())) {
                    for (MssAccountclearitem b : clearitem) {
                        if (StringUtils.isEmpty(b.getOthersystemdetailid()) && StringUtils.isNotEmpty(a.getGuid()) && !a.getGuid().equals(b.getGuid())) {
                            for (Map.Entry<BigDecimal, String> m : entries) {
                                if (m.getKey().compareTo(a.getPickingsum().add(b.getPickingsum())) == 0) {// 明细金额 等于
                                    // 两个挑对金额 和
                                    a.setOthersystemdetailid(m.getValue());
                                    b.setOthersystemdetailid(m.getValue());
                                }
                            }
                        }
                    }
                }
                // 方案 二 一明细对一挑对
                if (StringUtils.isEmpty(a.getOthersystemdetailid())) {
                    for (Map.Entry<BigDecimal, String> m : entries) {
                        if (m.getKey().compareTo(a.getPickingsum()) == 0) {// 相等
                            a.setOthersystemdetailid(m.getValue());
                        }
                    }
                }
                // 方案 三 多对多金额不匹配 匹配一个明细金额大于挑对金额的明细id
                if (StringUtils.isEmpty(a.getOthersystemdetailid())) {
                    for (Map.Entry<BigDecimal, String> m : entries) {
                        if (m.getKey().compareTo(a.getPickingsum()) == 1) {// 明细 金额 大于挑对金额
                            a.setOthersystemdetailid(m.getValue());
                        }
                    }
                }
                // 方案 四 无法匹配数据（两个明细，一个挑对），任意取第一个
                if (StringUtils.isEmpty(a.getOthersystemdetailid()) && list != null && list.size() > 0) {
                    a.setOthersystemdetailid(list.get(0).getId().toString());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.debug("收款关联明细ID失败:【" + mssAccountbill.getId() + "】" + e.getMessage());
        }
    }

    @Override // 根据id 获取报账单 信息
    public MssAccountbill getByid(Long id) {
        MssAccountbill accountbill = this.get(id);
        if (accountbill != null) {
            // 获取报账单明细
            List<MssAccountbillitem> item = getItemsById(id);
            accountbill.setItem(item);//封装明细

            List<MssAccountbillpayinfo> payinfo = getPayInfoById(id);
            accountbill.setPayinfo(payinfo); // 封装外部收款人

            Accountbillpre accountbillpre = getAccountBillpre(id);
            accountbill.setAccountbillpre(accountbillpre);// 封装归集单

            String type = accountbill.getBilltype().toString();
//            if (type.equals("3") || type.equals("5") || type.equals("10")) {
            List<MssAccountclearitem> clearitem = getClearitemId(id);
            accountbill.setClearitem(clearitem); // 封装 挑对信息
//            }
            if ("ln".equals(deployTo)) {
                List<MssAccountclearitemAccount> clearitemAccount = clearitemAccountMapper.listBybillId(id);
                accountbill.setClearitemAccount(clearitemAccount); // 封装 挑对信息 台账
            }
            // 根据报账单查询台账
            List<Long> accountIds = billitemAccountMapper.selectAccountIdByBillId(id);
            if (StringUtils.isNotEmpty(accountIds)) {
                // 根据台账信息获取电表信息
                List<Ammeterorprotocol> ammeterorprotocolList = ammeterorprotocolMapper.selectByAccountIds(accountIds);
                accountbill.setConsumption(StringUtils.isNotEmpty(ammeterorprotocolList));
            }
        }
        return accountbill;
    }

    public MssAccountbill getByidExcludePcids(Long id, List<Long> accountIds) {
        MssAccountbill accountbill = this.get(id);
        if (accountbill != null) {
            List<MssAccountbillitem> item = getItemsByIdExcludePcids(id, accountIds);
            accountbill.setItem(item);//封装明细

            List<MssAccountbillpayinfo> payinfo = getPayInfoById(id);
            accountbill.setPayinfo(payinfo); // 封装外部收款人

            Accountbillpre accountbillpre = getAccountBillpre(id);
            accountbill.setAccountbillpre(accountbillpre);// 封装归集单

            String type = accountbill.getBilltype().toString();
//            if (type.equals("3") || type.equals("5") || type.equals("10")) {
            List<MssAccountclearitem> clearitem = getClearitemId(id);
            accountbill.setClearitem(clearitem); // 封装 挑对信息
//            }
            if ("ln".equals(deployTo)) {
                List<MssAccountclearitemAccount> clearitemAccount = clearitemAccountMapper.listBybillId(id);
                accountbill.setClearitemAccount(clearitemAccount); // 封装 挑对信息 台账
            }

        }
        return accountbill;
    }

    // 获取明细列表
    private List<MssAccountbillitem> getItemsById(Long id) {
        Map<String, Object> map = new HashMap<>();
        map.put("writeoffInstanceId", id);
        map.put("findBy", true);
        List<MssAccountbillitem> list2 = itemmapper.selectByMap(map);
        //获取 明细 关联的 台账信息
        for (MssAccountbillitem item : list2) {
            RBillitemAccount model = new RBillitemAccount();
            model.setBillitemId(item.getId());
            List<RBillitemAccount> rBillitemAccounts = rBillitemAccountMapper.selectList(model);
            item.setRbillitemaccount(rBillitemAccounts);
            // 查询 UsageName
            String name = itemmapper.selectUsageName(item.getUsageId());
            item.setUsageName(name);
        }
        return list2;
    }

    private List<MssAccountbillitem> getItemsByIdExcludePcids(Long id, List<Long> accountIds) {
        Map<String, Object> map = new HashMap<>();
        map.put("writeoffInstanceId", id);
        map.put("findBy", true);
        List<MssAccountbillitem> list2 = itemmapper.selectByMap(map);
        //获取 明细 关联的 台账信息
        for (MssAccountbillitem item : list2) {
            RBillitemAccount model = new RBillitemAccount();
            model.setBillitemId(item.getId());
            List<RBillitemAccount> rBillitemAccounts = rBillitemAccountMapper.selectList(model);
            //排除台账id
            rBillitemAccounts = rBillitemAccounts.stream().filter(r -> !accountIds.contains(r.getAccountId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(rBillitemAccounts)) {
                continue;
            }
            item.setRbillitemaccount(rBillitemAccounts);
            // 查询 UsageName
            String name = itemmapper.selectUsageName(item.getUsageId());
            item.setUsageName(name);
        }
        list2 = list2.stream().filter(item -> !CollectionUtils.isEmpty(item.getRbillitemaccount())).collect(Collectors.toList());
        return list2;
    }

    // 获取外部收款人
    private List<MssAccountbillpayinfo> getPayInfoById(Long id) {
        Map<String, Object> map = new HashMap<>();
        map.put("writeoffInstanceId", id);
        map.put("status", 1);
        map.put("findBy", true);
        List<MssAccountbillpayinfo> list2 = payinfoMapper.selectByMap(map);
        return list2;
    }

    // 获取挑对单
    private List<MssAccountclearitem> getClearitemId(Long id) {
//        Map<String, Object> map = new HashMap<>();
//        map.put("writeoffFirstId", id);
//        map.put("status", 1);
//        map.put("findBy", true);
        MssAccountclearitem auto = new MssAccountclearitem();
        auto.setWriteoffFirstId(id);
        auto.setStatus("1");
        List<MssAccountclearitem> list = clearitemMapper.selectListAuto(auto);
        return list;
    }

    @Override // 获取归集单信息
    public Accountbillpre getAccountBillpre(Long id) {
        Accountbillpre auto = new Accountbillpre();
        auto.setPabid(id.toString());
        List<Accountbillpre> list = accountbillpreMapper.selectListByAuto(auto);
        if (list != null && list.size() > 0) {
            return list.get(0);
        } else {
            return null;
        }
    }

    @Override // 根据 ids 查询多条 报账单 列表
    public List<MssAccountbill> selectListByIds(String[] ids) {
        return billmmapper.selectListByIds(ids);
    }

    @Override // 查询报账单列表
    public List<MssAccountbill> selectListByAuto(MssAccountbill mssAccountbill) {
        List<MssAccountbill> list = billmmapper.selectListByAuto(mssAccountbill);
        for (MssAccountbill b : list) {
            if (b.getStatus() != null && (b.getStatus() == 1 || b.getStatus() == -2 || b.getStatus() == -3 || b.getStatus() == 8)) {
                b.set_disabled(false);
            } else {
                b.set_disabled(true);
            }
            //所属部门如果是二级部门（部门下的部门或者区县公司下的部门），要显示上级部门；如果是分公司下的一级部门，直接显示该部门
            if (StringUtils.isNotEmpty(cascadeCountry(b.getOrgid())))
                b.setFillInDep(cascadeCountry(b.getOrgid()));
            if (b.getEnergytype() == null)
                b.setEnergytype(new BigDecimal("-1"));
        }
        return list;
    }

    private String cascadeCountry(Long country) {
        String c = "";
        Organization org = organizationMapper.selectById(country);
        if (org != null && "2".equals(org.getOrgType())) {
            c = org.getOrgName();
            if (org.getParentCompanyNo() != null) {
                String s = cascadeCountry(Long.parseLong(org.getParentCompanyNo()));
                if (!StringUtils.isEmpty(s)) {
                    c = s + "-" + c;
                }
            }
        }
        return c;
    }

    @Override // 获取挑对单 列表
    public List<MssAccountbill> selectListByCheck(MssAccountbill mssAccountbill) {
        return billmmapper.selectListByCheck(mssAccountbill);
    }

    /////////////////////////////////////////////////////////////////////////////
    // 删除报账单
    /////////////////////////////////////////////////////////////////////////////

    @Override // 获取挑对单 列表 收款
    public List<MssAccountbill> selectListByCheckSK(MssAccountbill mssAccountbill) {
        return billmmapper.selectListByCheckSK(mssAccountbill);
    }

    @Override // 删除一条报账单
    public int deleteById(Long id) {
        int count = this.deleteDB(id);
        deleteSomeBybill(id);
        //TODO : 修改预算历史
        //批量更新 报账单 对应报账历史明细
        if ("ln".equals(deployTo)) {
            List<BudgetReimbursementHistory> list = budgetReimbursementHistoryMapper.getListByMsId(id);

            list.stream().forEach(b -> System.out.println(b));

            if (list.size() != 0) {
                int n = budgetReimbursementHistoryMapper.updateListFlag(list);
                if (n != list.size()) {
                    logger.info("MsId={}的报账 预算历史 状态更新成功，但是数目可能不对，可能需到数据库核查", id);
                }
            } else {
                // 注意 对应报账单无 报账明细数据
                logger.info("MsId={}的报账单无 报账明细 数据，注意核查", id);
            }
        }
        return count;
    }

    @Override // 修改 报账单 为报账单删除 -1
    public int deleteByIdForError(Long id) {
        MssAccountbill m = new MssAccountbill();
        m.setStatus(-1);//报账单删除
        m.setId(id);
        int count = this.updateForModel(m);
        List<MssAccountbillitem> items = getItemsById(id);
        if (items != null && items.size() > 0) {
            String[] strs = new String[items.size()];
            for (int i = 0; i < items.size(); i++) {
                strs[i] = items.get(i).getId().toString();
                MssAccountbillitem mssAccountbillitem = items.get(i);
                mssAccountbillitem.setStatus("-1");
                itemmapper.updateForModel(mssAccountbillitem);
            }
            // 删除 报账单 明细所有的关联关系
            rBillitemAccountMapper.deleteRbillitemAccountByBillId(id);
            if ("ln".equals(deployTo))
                rBillitemAccountMapper.deleteAccountAmountStationInfoByBillid(id);
        }
        List<MssAccountbillpayinfo> payinfos = getPayInfoById(id);
        if (payinfos != null && payinfos.size() > 0) {
            String[] strs = new String[payinfos.size()];
            for (int i = 0; i < payinfos.size(); i++) {
                MssAccountbillpayinfo accountbillpayinfo = payinfos.get(i);
                accountbillpayinfo.setStatus("-1");
                payinfoMapper.updateForModel(accountbillpayinfo);
            }
        }
        List<MssAccountclearitem> clearitemId = getClearitemId(id);
        if (clearitemId != null && clearitemId.size() > 0) {
            String[] strs = new String[clearitemId.size()];
            for (int i = 0; i < clearitemId.size(); i++) {
                MssAccountclearitem mssAccountclearitem = clearitemId.get(i);
                mssAccountclearitem.setStatus("-1");
                clearitemMapper.updateForModel(mssAccountclearitem);
            }
        }
        Accountbillpre billpre = getAccountBillpre(id);
        if (billpre != null) {
            String pabid = billpre.getPabid();
            StringBuffer newpabid = handelsplitIds(id, pabid);
            billpre.setPabid(newpabid.toString());// 设置值
            if (StringUtils.isNotEmpty(billpre.getPabid())) {
                billpre.setStatus(2);//已生成报账单
            } else {
                billpre.setStatus(1);//未生成报账单
            }
            accountbillpreMapper.updateByPrimaryKey(billpre);
            //判断是否 合并归集单 并更新状态
            updatepreByParentPre(billpre, 1);
        }
        if ("ln".equals(deployTo)) {
            List<MssAccountclearitemAccount> clearitemAccount = clearitemAccountMapper.listBybillId(id);
            if (clearitemAccount != null && clearitemAccount.size() > 0) {
                String[] strs = new String[clearitemAccount.size()];
                for (int i = 0; i < clearitemAccount.size(); i++) {
                    MssAccountclearitemAccount mssAccountclearitemAccount = clearitemAccount.get(i);
                    mssAccountclearitemAccount.setStatus(-1);
                    clearitemAccountMapper.updateForModel(mssAccountclearitemAccount);
                }
            }
        }

        return count;
    }

    @Override // 批量删除报账单
    public int deleteByIdsAuto(String[] toStrArray) {
        int count = this.deleteByIdsDB(toStrArray);
        for (String id : toStrArray) {
            deleteSomeBybill(Long.valueOf(id));
        }
        return count;
    }

    // 删除报账单关联的数据
    private void deleteSomeBybill(Long id) {
        List<MssAccountbillitem> items = getItemsById(id);
        if (items != null && items.size() > 0) {
            String[] strs = new String[items.size()];
            for (int i = 0; i < items.size(); i++) {
                strs[i] = items.get(i).getId().toString();
            }
            itemmapper.deleteByIdsDB(strs);
            // 删除 报账单 明细所有的关联关系
            rBillitemAccountMapper.deleteRbillitemAccountByBillId(id);
            if ("ln".equals(deployTo))
                rBillitemAccountMapper.deleteAccountAmountStationInfoByBillid(id);
        }
        List<MssAccountbillpayinfo> payinfos = getPayInfoById(id);
        if (payinfos != null && payinfos.size() > 0) {
            String[] strs = new String[payinfos.size()];
            for (int i = 0; i < payinfos.size(); i++) {
                strs[i] = payinfos.get(i).getId().toString();
            }
            payinfoMapper.deleteByIdsDB(strs);
        }
        List<MssAccountclearitem> clearitemId = getClearitemId(id);
        if (clearitemId != null && clearitemId.size() > 0) {
            String[] strs = new String[clearitemId.size()];
            for (int i = 0; i < clearitemId.size(); i++) {
                strs[i] = clearitemId.get(i).getClearid().toString();
            }
            clearitemMapper.deleteByIdsDB(strs);
        }
        Accountbillpre billpre = getAccountBillpre(id);
        if (billpre != null) {
            String pabid = billpre.getPabid();
            StringBuffer newpabid = handelsplitIds(id, pabid);
            billpre.setPabid(newpabid.toString());// 设置值
            if (StringUtils.isNotEmpty(billpre.getPabid())) {
                billpre.setStatus(2);//已生成报账单
            } else {
                billpre.setStatus(1);//未生成报账单
            }
            accountbillpreMapper.updateByPrimaryKey(billpre);
            //判断是否 合并归集单 并更新状态
            updatepreByParentPre(billpre, 1);
        }
        if ("ln".equals(deployTo)) {
            List<MssAccountclearitemAccount> clearitemAccount = clearitemAccountMapper.listBybillId(id);
            if (clearitemAccount != null && clearitemAccount.size() > 0) {
                String[] strs = new String[clearitemAccount.size()];
                for (int i = 0; i < clearitemAccount.size(); i++) {
                    strs[i] = clearitemAccount.get(i).getId().toString();
                }
                clearitemAccountMapper.deleteByIdsDB(strs);
            }
        }
    }

    @Override // 根据财辅退回的单子 生成新的报账单
    public Long addNewBillByExist(Long oldid) throws Exception {
        MssAccountbill newbill = this.getByid(oldid);
        Long newId = IdGenerator.getNextId();
        Accountbillpre accountbillpre = newbill.getAccountbillpre();
        List<MssAccountbillitem> billitem = new ArrayList<>();
        if (accountbillpre != null) {
            String pabid = accountbillpre.getPabid();//修改 关联归集单
            pabid = pabid.replaceAll(oldid.toString(), newId.toString());
            accountbillpre.setPabid(pabid);
            rBillitemAccountMapper.deleteRbillitemAccountByBillId(oldid);// 删除旧的关联关系
            accountbillpreMapper.updateByPrimaryKey(accountbillpre);

            // 构造关联关系
            Accountbillitempre accountbillitempre = new Accountbillitempre();
            accountbillitempre.setParid(accountbillpre.getPabrid());
            accountbillitempre.setStatus(accountbillpre.getBilltype());
            List<MssAccountbillitem> mssAccountbillitems =
                    rBillitemAccountService.addItemByaccounts(accountbillitempre);
            if (mssAccountbillitems != null && mssAccountbillitems.size() > 0) {
                for (MssAccountbillitem old : newbill.getItem()) {
                    if ("5".equals(old.getUsageId()))
                        billitem.add(old);
                }
                for (MssAccountbillitem newi : mssAccountbillitems) {// 最多两项
                    billitem.add(newi);
                }
            } else {
                throw new BaseException("生成失败，没有查询到可关联台账，检查台账是否退回修改中？");
            }
        }

        newbill.setIresult("1");
        this.update(newbill);// 更新旧的报账单

        // 插入新的报账单
        newbill.setId(newId);
        newbill.setStatus(1);
        newbill.setWriteoffInstanceCode(null);
        newbill.setProcessinstid(null);
        newbill.setTimestamp(new Date());
        newbill.setCreateDate(new Date());
        List<MssAccountbill> blist = new ArrayList<>();
        blist.add(newbill);
        this.insertList(blist);// 批量插入的方式 Id根据传入的参数 不自动生成

        if (billitem.size() > 0)
            newbill.setItem(billitem);
        insertrbillitemAccounts(newbill);//插入 明细 和 台账关联关系

        List<MssAccountbillpayinfo> payinfo = newbill.getPayinfo();
        if (payinfo != null && payinfo.size() > 0) {
            for (MssAccountbillpayinfo info : payinfo) {
                info.setId(IdGenerator.getNextId());
                info.setWriteoffInstanceId(newId);
            }
            payinfoMapper.insertList(payinfo);// 外部收款人
        }
        List<MssAccountclearitem> clearitem = newbill.getClearitem();
        if (clearitem != null && clearitem.size() > 0)
            for (MssAccountclearitem item : clearitem) {
                item.setWriteoffFirstId(newId);
                clearitemMapper.updateForModel(item);// 挑对信息
            }
        return newbill.getId();
    }

    // 根据财辅退回的单子 生成新的报账单
    public String addNewBillByExistExcludePcids(Long oldid, List<Long> accountIds) throws Exception {
        MssAccountbill newbill = this.getByidExcludePcids(oldid, accountIds);
        Long oldBillid = newbill.getId();
        Accountbillpre accountbillpre = newbill.getAccountbillpre();
        List<MssAccountbillitem> billitem = new ArrayList<>();
        if (accountbillpre != null) {
            String pabid = accountbillpre.getPabid();//修改 关联归集单
            pabid = pabid.replaceAll(oldid.toString(), oldBillid.toString());
            accountbillpre.setPabid(pabid);
            rBillitemAccountMapper.deleteRbillitemAccountByBillId(oldid);// 删除旧的关联关系
            accountbillpreMapper.updateByPrimaryKey(accountbillpre);

            // 构造关联关系
            Accountbillitempre accountbillitempre = new Accountbillitempre();
            accountbillitempre.setParid(accountbillpre.getPabrid());
            accountbillitempre.setStatus(accountbillpre.getBilltype());
            List<MssAccountbillitem> mssAccountbillitems =
                    rBillitemAccountService.addItemByaccountsExcluedePcids(accountbillitempre, accountIds);
            if (mssAccountbillitems != null && mssAccountbillitems.size() > 0) {
                for (MssAccountbillitem old : newbill.getItem()) {
                    if ("5".equals(old.getUsageId()))
                        billitem.add(old);
                }
                for (MssAccountbillitem newi : mssAccountbillitems) {// 最多两项
                    billitem.add(newi);
                }
            } else {
                throw new BaseException("生成失败，没有查询到可关联台账，检查台账是否退回修改中？");
            }
        }

        //更新原有报账单
        newbill.setId(oldBillid);
        newbill.setTimestamp(new Date());
        newbill.setCreateDate(new Date());
        //设置报账单金额
        newbill = SetMssSum(newbill);
        this.update(newbill);

        if (billitem.size() > 0)
            newbill.setItem(billitem);
        insertrbillitemAccountsAndDel(newbill);//插入 明细 和 台账关联关系

        //删除局站报账关联信息
        billmmapper.deleJtForBillId(oldBillid, accountIds);

        //外部收款人
        List<MssAccountbillpayinfo> payinfo = newbill.getPayinfo();
        if (payinfo != null && payinfo.size() > 0) {
            for (MssAccountbillpayinfo info : payinfo) {
                info.setId(IdGenerator.getNextId());
                info.setWriteoffInstanceId(oldBillid);
            }
            payinfoMapper.insertList(payinfo);// 外部收款人
        }
        List<MssAccountclearitem> clearitem = newbill.getClearitem();
        if (clearitem != null && clearitem.size() > 0)
            for (MssAccountclearitem item : clearitem) {
                item.setWriteoffFirstId(oldBillid);
                clearitemMapper.updateForModel(item);// 挑对信息
            }
        return String.format("报账单id:%d台账明细删除成功", oldBillid);
    }

    private MssAccountbill SetMssSum(MssAccountbill mssbill) {
        List<MssAccountbillitem> mssbillItems = mssbill.getItem();

        BigDecimal sum = new BigDecimal("0");
        BigDecimal tax = new BigDecimal("0");
        for (MssAccountbillitem mssAccountbillitem : mssbillItems) {

            if (mssAccountbillitem.getWriteoffInstanceId() == null) {
                continue;
            }
            //获取关联对象，并记录总金额和总税额
            List<RBillitemAccount> rbillitemaccounts = mssAccountbillitem.getRbillitemaccount();
            for (int i = 0; i < rbillitemaccounts.size(); i++) {
                RBillitemAccount rBillitemAccount = rbillitemaccounts.get(i);
                //汇总报账金额和税额
                sum = sum.add(BigDecimal.valueOf(rBillitemAccount.getMoney()));
                tax = tax.add(BigDecimal.valueOf(rBillitemAccount.getTaxmoney()));
            }
            mssAccountbillitem.setSum(sum);
            mssAccountbillitem.setTaxAdjustSum(tax);
        }
        //更新报账单的报账金额和税额
        mssbill.setSum(sum);
        mssbill.setInputTaxSum(tax);
        return mssbill;
    }


    //判断 报账单关联的电表/协议用电类型含“生产用电-移动基站”
    @Override
    public int countAmmeterTypeBybillId(Long id) {
        return rBillitemAccountMapper.countAmmeterTypeBybillId(id);
    }

    //判断 报账单关联的电表/协议用电类型含“生产用电-移动基站” 是否关联LTE
    @Override
    public int countLteStationBybillId(Long id) {
        return rBillitemAccountMapper.countLteStationBybillId(id);
    }

    // 验证数据是否合法

    //*****新增报账单时先验证
    @Override
    public Map<String, Object> checkMssAccountbill(MssAccountbill mssAccountbill) {
        Map<String, Object> map = new HashMap<>();
        if (!mssAccountbill.getFillInAccount().equals(ShiroUtils.getUser().getLoginId())) {
            map.put("msg", "不能编辑其他人的报账单");
            map.put("success", false);
            return map;
        }
        BigDecimal sum = mssAccountbill.getSum();
        if (sum == null) {
            map.put("msg", "不含税金额不能为空");
            map.put("success", false);
            return map;
        }
        if (StringUtils.isEmpty(mssAccountbill.getCompanyCode())) {
            map.put("msg", "分公司不能为空");
            map.put("success", false);
            return map;
        }
        BigDecimal inputTaxSum = mssAccountbill.getInputTaxSum();
        if (inputTaxSum == null) {
            mssAccountbill.setInputTaxSum(BigDecimal.ZERO);
//            map.put("msg", "税额不能为空");
//            map.put("success", false);
//            return map;
        }
        List<MssAccountbillpayinfo> payinfo = mssAccountbill.getPayinfo();
        List<MssSupplierItem2> supplierItem2 = mssAccountbill.getSupplierItem2();
        List<MssAbccustomerBank> customerBank = mssAccountbill.getCustomerBank();
        if (payinfo == null && supplierItem2 == null && customerBank == null) {
            // 外部收款人 可为空
//            map.put("msg", "没有外部收款人信息");
//            map.put("success", false);
//            return map;
        }
        BigDecimal total = BigDecimal.valueOf(0);
        BigDecimal total1 = sum.add(inputTaxSum);
        if (payinfo != null && payinfo.size() > 0)
            for (MssAccountbillpayinfo pay : payinfo) {
                if (pay.getSum() != null)
                    total = total.add(pay.getSum());
            }
        if (supplierItem2 != null && supplierItem2.size() > 0)
            for (MssSupplierItem2 pay : supplierItem2) {
                total = total.add(pay.getSum());
            }
        if (customerBank != null && customerBank.size() > 0)
            for (MssAbccustomerBank pay : customerBank) {
                total = total.add(pay.getSum());
            }
        // 存在 外部收款人是 才比较
        if (total.compareTo(BigDecimal.ZERO) != 0 && total.abs().doubleValue() != total1.abs().doubleValue()) {
            map.put("msg", "外部收款人金额(和)【" + total.doubleValue() + "】不等于【不含税金额】与【税额】(和)【" + total1.doubleValue() + "】");
            map.put("success", false);
            return map;
        }
        List<MssAccountbillitem> list = mssAccountbill.getItem();
        if (list == null) {
            map.put("msg", "没有明细信息");
            map.put("success", false);
            return map;
        }
        BigDecimal totalmx = BigDecimal.valueOf(0);
        BigDecimal totalmxTax = BigDecimal.valueOf(0);
        int accountSize = 0;
        for (MssAccountbillitem item : list) {
            totalmx = totalmx.add(item.getSum());//明细不含税 和
            if (item.getTaxAdjustSum() == null)
                item.setTaxAdjustSum(BigDecimal.ZERO);
            totalmxTax = totalmxTax.add(item.getTaxAdjustSum());//明细 税和
            accountSize +=item.getRbillitemaccount().size();
        }
        //2024-12-24 新增 归集单生成报账单控制台账明细最大条数1000，超过提示“报账明细数据长度超出了最大限制, 请控制在1000以内”
        if (accountSize > 1000) {
            map.put("msg", "报账明细数据长度超出了最大限制, 请控制在1000以内");
            map.put("success", false);
            return map;
        }
        if (totalmx.abs().doubleValue() != sum.abs().doubleValue()) {
            map.put("msg", "明细不含税金额(和)【" + totalmx.doubleValue() + "】不等于【基本信息不含税金额】【" + sum.doubleValue() + "】");
            map.put("success", false);
            return map;
        }
        if (totalmxTax.abs().doubleValue() != inputTaxSum.abs().doubleValue()) {
            map.put("msg", "明细税额(和)【" + totalmxTax.doubleValue() + "】不等于【基本信息税额】【" + inputTaxSum.doubleValue() + "】");
            map.put("success", false);
            return map;
        }
        String type = mssAccountbill.getBilltype().toString();
        if (type.equals("3") || type.equals("5") || type.equals("10")) {
            List<MssAccountclearitem> clearitem = mssAccountbill.getClearitem();
            if (clearitem == null) {
                map.put("msg", "没有挑对信息");
                map.put("success", false);
                return map;
            }
            BigDecimal totalck = BigDecimal.valueOf(0);
            for (MssAccountclearitem item : clearitem) {
                totalck = totalck.add(item.getPickingsum());//明细 和
            }
            if (totalck.abs().doubleValue() != totalmx.add(totalmxTax).abs().doubleValue()) {
                map.put(
                        "msg",
                        "挑对金额(和)【" + totalck.doubleValue() + "】不等于 明细金额(和)【" + totalmx.add(totalmxTax).doubleValue() + "】"
                );
                map.put("success", false);
                return map;
            }
            if (totalck.abs().doubleValue() != sum.add(inputTaxSum).abs().doubleValue()) {
                map.put(
                        "msg",
                        "挑对金额(和)【" + totalck.doubleValue() + "】不等于 报账单金额【" + sum.add(inputTaxSum).doubleValue() + "】"
                );
                map.put("success", false);
                return map;
            }
        }
        Accountbillpre accountbillpre = mssAccountbill.getAccountbillpre();// 验证 归集单 预估类型
        if (accountbillpre != null) {
            if ("sc".equals(deployTo) && ("6".equals(mssAccountbill.getPickingMode().toString()) && ("8".equals(mssAccountbill.getBilltype().toString())))) {
                if (!((accountbillpre.getMoney().compareTo(new BigDecimal(0)) < 0) && (totalmx.compareTo(new BigDecimal(0)) > 0))) {
                    map.put("msg", "集团验证业务场景“其他收款”收款报账单金额为正归集单金额填负数，请调整!!");
                    map.put("success", false);
                    return map;
                }
            }
//            else {
//                if (((accountbillpre.getUseableMoney().compareTo(new BigDecimal(0)) >= 0) && (totalmx.compareTo(new BigDecimal(0)) < 0)) ||
//                        ((accountbillpre.getMoney().compareTo(new BigDecimal(-1)) < 0) && (totalmx.compareTo(new BigDecimal(0)) >= 0))) {
//                    map.put("msg", "报账单金额、归集单金额一正已负不允许，请调整台账重新生成报账单!!");
//                    map.put("success", false);
//                    return map;
//                }
//            }
            //            String billtype = accountbillpre.getBilltype();
//            String[] billNames = new String[]{"报销", "挂账", "挂账支付", "预付", "预付冲销", "借款冲销", "前期预付冲销", "收款", "预估", "预估冲销"};
//            //1报销 2挂账 3挂账支付 4预付 5预付冲销 6借款冲销 7前期预付冲销 8收款 9预估 10预估冲销
//            if ("9".equals(type)) {//9预估
//                if ("2".equals(billtype) || "5".equals(billtype) || "7".equals(billtype) || "8".equals(billtype)) {
//                    //预估
//                } else {
//                    map.put("msg", "报账单类型【预估】跟归集单类型不匹配!!");
//                    map.put("success", false);
//                    return map;
//                }
//            } else {// 非预估
//                if ("2".equals(billtype) || "5".equals(billtype) || "7".equals(billtype) || "8".equals(billtype)) {
//                    //预估
//                    map.put("msg", "报账单类型【" + billNames[Integer.parseInt(type) - 1] + "】跟归集单类型【预估】不匹配!!");
//                    map.put("success", false);
//                    return map;
//                }
//            }
        }
        if ("sc".equals(deployTo) && "9".equals(mssAccountbill.getBilltype().toString())) {
//            1.账单类型为“预估”的报账单的供应商固化为G951100081（其他支付对象-成本结算业务预估），
//            不允许选择其他供应商。
            // 取消限制
//            mssAccountbill.setSupplierCode("G951100081");
//            mssAccountbill.setSupplierName("其他支付对象-成本结算业务预估");
        }

        //1.报账单业务类型为“付款”或“列并付”时，
        // 需校验报账单关联的台账信息同一个电表/协议不能存在两条及以上，如果同一个电表/协议存在两条及以上台账信息，校验不通过，
        // 提示：同一个电表/协议不允许不同期号报一个报账单。
        map = saveCheckAccount(mssAccountbill);
        if (!(boolean) map.get("success"))
            return map;
//        if ("2".equals(mssAccountbill.getBizTypeCode()) || "1".equals(mssAccountbill.getBizTypeCode())) {
//            List<Ammeterorprotocol> ammlist = rBillitemAccountMapper.ifhaveTwoAmmeteridByBillId(mssAccountbill
//            .getId());
//            if (ammlist != null && ammlist.size() > 0) {
//                StringBuilder sb = new StringBuilder();
//                for (Ammeterorprotocol ml : ammlist) {
//                    if (StringUtils.isNotEmpty(ml.getAmmetername()))
//                        sb.append("《电表》【").append(ml.getAmmetername()).append("】;");
//                    if (StringUtils.isNotEmpty(ml.getProtocolname()))
//                        sb.append("《协议》【").append(ml.getProtocolname()).append("】;");
//                }
//                map.put("msg", sb.toString() + "多个账期不能同时报账！同一个电表/协议不允许不同期号报一个报账单!");
//                map.put("success", false);
//                return map;
//            }
//        }
        // 收款 报账明细跟挑对 一对一 关系
        //收款业务场景是“其他收款”的，可以不挑对
        if ("sc".equals(deployTo) && !"6".equals(mssAccountbill.getPickingMode().toString()) && ("8".equals(mssAccountbill.getBilltype().toString()) || "11".equals(mssAccountbill.getBilltype().toString()))) {
            int itemSize = mssAccountbill.getItem().size();
            List<MssAccountclearitem> clearitem = mssAccountbill.getClearitem();
            if (clearitem == null) {
                map.put("msg", "收款/调账：没有挑对信息！");
                map.put("success", false);
                return map;
            } else if (clearitem.size() != itemSize) {
                map.put("msg", "【报账明细条数和金额需与挑对条数和金额一致】收款/调账：(" + itemSize + ")条明细信息对应(" + clearitem.size() + ")" +
                        "条挑对信息有误!");
                map.put("success", false);
                return map;
            } else {
                //判断挑对金额是否一致
                try {
                    for (MssAccountclearitem citem : clearitem) {
                        BigDecimal pickingsum = citem.getPickingsum();
                        boolean flag = false;
                        for (MssAccountbillitem item : list) {
                            if (pickingsum.compareTo(item.getSum().add(item.getTaxAdjustSum())) == 0) {
                                flag = true;
                            }
                        }
                        if (!flag) {
                            map.put("msg", "【报账明细金额需与挑对金额一致】收款/调账金额：(" + pickingsum + ")没有找到对应明细!");
                            map.put("success", false);
                            return map;
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        if ("********".equals(mssAccountbill.getAccountCode()) && "1".equals(mssAccountbill.getSuppliertype())
                && StringUtils.isNotEmpty(mssAccountbill.getSupplierName()) && !mssAccountbill.getSupplierName().startsWith("中国铁塔")) {
//            报账单经济事项为“铁塔及相关资产租赁（********）”时，提交时校验报账单选择的供应商的供应商名称是否是以“中国铁塔”开头的供应商，
//            如果不是，不允许提交并提示：“铁塔代缴电费报账只能选择铁塔公司，请注意！”。当选择的是客户无需校验
            map.put("msg", "铁塔代缴电费报账只能选择铁塔公司,当前选择为[" + mssAccountbill.getSupplierName() + "]，请注意！");
            map.put("success", false);
            return map;
        }
        if ("********".equals(mssAccountbill.getAccountCode()) && "1".equals(mssAccountbill.getSuppliertype())
                && !"8".equals(mssAccountbill.getBilltype().toString()) && !"11".equals(mssAccountbill.getBilltype().toString())
                && StringUtils.isNotEmpty(mssAccountbill.getSupplierName()) && mssAccountbill.getSupplierName().startsWith("中国铁塔")) {
//            报账单类型为“报销、挂账，挂账支付、预付，预付冲销，借款冲销，前期借款冲销，预估，预估冲销”，
//            提交时校验报账单选择的供应商的供应商名称是否是以“中国铁塔”开头的供应商，如果是，不允许提交并提示：“自缴电费报账不能选择铁塔公司，请注意！”。当选择的是客户无需校验
            map.put("msg", "自缴电费报账不能选择铁塔公司,当前选择为[" + mssAccountbill.getSupplierName() + "]，请注意！");
            map.put("success", false);
            return map;
        }
        if ("********".equals(mssAccountbill.getAccountCode()) && "1".equals(mssAccountbill.getSuppliertype())
                && !"8".equals(mssAccountbill.getBilltype().toString()) && !"11".equals(mssAccountbill.getBilltype().toString())
                && StringUtils.isNotEmpty(mssAccountbill.getSupplierName()) && mssAccountbill.getSupplierName().startsWith("中国铁塔")) {
//            报账单类型为“报销、挂账，挂账支付、预付，预付冲销，借款冲销，前期借款冲销，预估，预估冲销”，
//            提交时校验报账单选择的供应商的供应商名称是否是以“中国铁塔”开头的供应商，如果是，不允许提交并提示：“自缴水费报账不能选择铁塔公司，请注意！”。当选择的是客户无需校验
            map.put("msg", "自缴水费报账不能选择铁塔公司,当前选择为[" + mssAccountbill.getSupplierName() + "]，请注意！");
            map.put("success", false);
            return map;
        }
        if ("********".equals(mssAccountbill.getAccountCode()) && "1".equals(mssAccountbill.getSuppliertype())
                && !"8".equals(mssAccountbill.getBilltype().toString()) && !"11".equals(mssAccountbill.getBilltype().toString())
                && StringUtils.isNotEmpty(mssAccountbill.getSupplierName()) && mssAccountbill.getSupplierName().startsWith("中国铁塔")) {
//            报账单类型为“报销、挂账，挂账支付、预付，预付冲销，借款冲销，前期借款冲销，预估，预估冲销”，
//            提交时校验报账单选择的供应商的供应商名称是否是以“中国铁塔”开头的供应商，如果是，不允许提交并提示：“自缴发电油费报账不能选择铁塔公司，请注意！”。当选择的是客户无需校验
            map.put("msg", "自缴发电油费报账不能选择铁塔公司,当前选择为[" + mssAccountbill.getSupplierName() + "]，请注意！");
            map.put("success", false);
            return map;
        }
        if ("********".equals(mssAccountbill.getAccountCode()) && "1".equals(mssAccountbill.getSuppliertype())
                && !"8".equals(mssAccountbill.getBilltype().toString()) && !"11".equals(mssAccountbill.getBilltype().toString())
                && StringUtils.isNotEmpty(mssAccountbill.getSupplierName()) && mssAccountbill.getSupplierName().startsWith("中国铁塔")) {
//            报账单类型为“报销、挂账，挂账支付、预付，预付冲销，借款冲销，前期借款冲销，预估，预估冲销”，
//            提交时校验报账单选择的供应商的供应商名称是否是以“中国铁塔”开头的供应商，如果是，不允许提交并提示：“自缴取暖费报账不能选择铁塔公司，请注意！”。当选择的是客户无需校验
            map.put("msg", "自缴取暖费报账不能选择铁塔公司,当前选择为[" + mssAccountbill.getSupplierName() + "]，请注意！");
            map.put("success", false);
            return map;
        }
        //1.账单类型为“预提”的报账单的 附件信息必须上传！
        if ("ln".equals(deployTo) && "9".equals(mssAccountbill.getBilltype().toString())) {
            Attachments model = new Attachments();
            model.setBusiAlias("附件(预提)");
            model.setBusiId(mssAccountbill.getId());
            Integer count = attachmentsMapper.count(model);
            if (count == null || count == 0) {
                map.put("success", false);
                map.put("msg", "预提报账单附件信息必须上传！");
                return map;
            }
        }
        map.put("success", true);
        map.put("msg", "保存成功");
        map.put("code", 0);
        return map;
    }

    /**
     * @Description: 同比环比分析统计
     * @author: lc
     * @date: 2019/5/23
     * @param:
     * @return:
     */
    @Override
    public List<Map<String, Object>> statisticalAnalysis(Map<String, Object> params) {
        return billmmapper.statisticalAnalysis(params);
    }

    @Override
    public Map<String, Object> saveChecks(MssAccountbill mssAccountbill) {
        Map<String, Object> map = checkMssAccountbill(mssAccountbill);
        if ((boolean) map.get("success")) {
            updateClearitem(mssAccountbill);//更新 挑对单
            if ("sc".equals(deployTo) || "ln".equals(deployTo)) {
                try {
                    mssInterfaceService.sendToMss(mssAccountbill.getId());// 送财辅接口
                } catch (Exception e) {
                    map.put("mes", e.getMessage());
                    e.printStackTrace();
                }
            }
        }
        return map;
    }

    @Override
    public MssAccountbill getByOldOne(MssAccountbill m) {
        return billmmapper.getByOldOne(m);
    }

    @Override
    public String getmssbasecodebyPre(String preid) {
        return billmmapper.getmssbasecodebyPre(preid);
    }

    @Override
    public Map<String, Object> saveCheckAccount(MssAccountbill mssAccountbill) {
        Map<String, Object> map = new HashMap<>();
        map.put("success", true);
        //1.报账单业务类型为“付款”或“列并付”时，
        // 需校验报账单关联的台账信息同一个电表/协议不能存在两条及以上，如果同一个电表/协议存在两条及以上台账信息，校验不通过，
        // 提示：同一个电表/协议不允许不同期号报一个报账单。
        String type = mssAccountbill.getBilltype().toString();
        if (type.equals("1") || type.equals("2") || type.equals("5") || type.equals("6") || type.equals("7") || type.equals("9") || type.equals("10"))
            /*if ("2".equals(mssAccountbill.getBizTypeCode()) || "1".equals(mssAccountbill.getBizTypeCode())) */ {
            List<Ammeterorprotocol> ammlist = rBillitemAccountMapper.ifhaveTwoAmmeteridByBillId(mssAccountbill.getId());
/*            if (ammlist != null && ammlist.size() > 0) {
                StringBuilder sb = new StringBuilder();
                for (Ammeterorprotocol ml : ammlist) {
                    sb.append("《电表》【").append(ml.getAmmetername()).append("】;");
                    if (StringUtils.isNotEmpty(ml.getProtocolname()))
                        sb.append("《协议》【").append(ml.getProtocolname()).append("】;");
                }
                map.put("msg", sb.toString() + "多个账期不能同时报账！同一个电表/协议不允许不同期号报一个报账单!");
                map.put("success", false);
                return map;
            }*/
//            提交时需校验电表/协议关联的局站是否是集团下发的IDC数据中心编码
            List<Ammeterorprotocol> ammslist = rBillitemAccountMapper.ifhaveIDCStationByBillId(mssAccountbill.getId());
            if (CollUtil.isNotEmpty(ammslist)) {
                StringBuilder sb = new StringBuilder();
                StringBuilder sb1 = new StringBuilder();
                String res = "为数据中心类，需关联集团IDC资源管理系统中数据中心名称和编码，请先到电表/协议基础信息中维护关联局站信息!";
                String res1 = "关联用电类型中有分列数据中心占比，需关联集团IDC资源管理系统中数据中心名称和编码，请先到电表/协议基础信息中维护关联用电类型中的关联局站信息！";
                for (Ammeterorprotocol ml : ammslist) {
                    if (ml.getType() == 1)
                        sb.append("《电表/协议》【").append(ml.getAmmetername()).append("】;");
                    if (ml.getType() == 2)
                        sb1.append("《电表/协议》【").append(ml.getAmmetername()).append("】;");
                }
                map.put("msg", (StringUtils.isNotEmpty(sb.toString()) ? sb.toString() + res : "") +
                        (StringUtils.isNotEmpty(sb1.toString()) ? sb1.toString() + res1 : ""));
                map.put("success", false);
                return map;
            }
            {
                ammslist = rBillitemAccountMapper.ifhaveLTEStationByBillId(mssAccountbill.getId());
                if (CollUtil.isNotEmpty(ammslist)) {
                    StringBuilder sb = new StringBuilder();
                    sb.append("报账单含基站类，共有");
                    sb.append(ammslist.size());
                    sb.append("个站址未找到关联集团5gr站址编码。");
                    sb.append("相关局站编码：【");
                    for (Ammeterorprotocol ammeterorprotocol : ammslist) {
                        sb.append(ammeterorprotocol.getStationcode()).append("，");
                    }
                    sb.append("】;");
                    sb.append("请在报账单‘局站集团网管对应’关联，如无法找到对应站址关联，请通过市无线部上报省无线部梁家辉老师或联系系统支撑");

                    map.put("msg", sb.toString());
                    map.put("success", false);
                    return map;
                }

            }
            if ("sc".equals(deployTo)) {
                ammslist = rBillitemAccountMapper.ifhaveLTEStationvalidityByBillId(mssAccountbill.getId());
                if (ammslist != null && ammslist.size() > 0) {

                    if (mssAccountbill.getAccountCode().equals("********")) {

                        int uprows = rBillitemAccountMapper.updatepowerjtl(mssAccountbill.getId());
                        System.out.println(uprows);
                    }
                    if (mssAccountbill.getAccountCode().equals("********")) {

                        rBillitemAccountMapper.cleartemp();
                        rBillitemAccountMapper.insertjt4glta(mssAccountbill.getId());
                        rBillitemAccountMapper.updatepowerjtlta(mssAccountbill.getId());
                        int uprows = rBillitemAccountMapper.updatepowerjtl(mssAccountbill.getId());
                        System.out.println(uprows);
                    }

                    StringBuilder sb = new StringBuilder();

                    String res = "报账单含基站类，共有" + ammslist.size() + "个站址未能通过2023" +
                            "年站址有效性稽核，请重新打开报账单‘局站集团网管对应’关联，如无法找到对应站址关联，请通过市无线部上报省无线部梁家辉老师或联系系统支撑";

/*                    for (Ammeterorprotocol ml : ammslist) {

                        sb.append(" 电表/协议 【").append(ml.getAmmetername()).append("】;");

                    }*/
                    //map.put("msg", res + (StringUtils.isNotEmpty(sb.toString()) ? sb.toString() : "")  );
                    map.put("msg", res);
                    map.put("success", false);
                    return map;
                }
            }

        }
        //验证idc设备耗电量
        /*if (mssAccountbill != null && ("2".equals(mssAccountbill.getBizTypeCode()) || "1".equals(mssAccountbill
        .getBizTypeCode()))) */
        if (mssAccountbill != null && (type.equals("1") || type.equals("2") || type.equals("5") || type.equals("6") || type.equals("7") || type.equals("9") || type.equals("10"))) {
            //报账单送集团
            List<AccountAmount> amounts = rBillitemAccountMapper.ifhaveNoAccountAmountByBillId(mssAccountbill.getId());
            if (amounts != null && amounts.size() > 0) {
                List<AccountAmount> amountList = new ArrayList<>();
                boolean flag = true;
                StringBuilder sb = new StringBuilder();
                for (AccountAmount accountAmount : amounts) {
                    if (accountAmount.getId() == null || accountAmount.getAmount() == null) {
                        sb.append("[" + accountAmount.getProjectname() + ":" + accountAmount.getAmmetercode() + "]");
                        sb.append("IDC机房需要录入【设备耗电量】!");
                        amountList.add(accountAmount);
                        flag = false;
                    }

                    if (accountAmount.getAmount() != null && accountAmount.getAmount() <= 0
                            && (accountAmount.getThisQuantityOfElectricity() > accountAmount.getAmount())) {
                        sb.append("[" + accountAmount.getProjectname() + ":" + accountAmount.getAmmetercode() + "]");
                        sb.append("【设备耗电量】有误!");
                        amountList.add(accountAmount);
                        flag = false;
                    }
                    if (accountAmount.getAmount() != null && accountAmount.getAmount() >= 0
                            && (accountAmount.getThisQuantityOfElectricity() < accountAmount.getAmount())) {
                        sb.append("[" + accountAmount.getProjectname() + ":" + accountAmount.getAmmetercode() + "]");
                        sb.append("【设备耗电量】有误!");
                        amountList.add(accountAmount);
                        flag = false;
                    }
                }
                map.put("list", amountList);
                map.put("msg", sb.toString());
                map.put("success", flag);
                if (!flag) return map;
            }
        }
        //辽宁收款
        if ("ln".equals(deployTo) && "8".equals(mssAccountbill.getBilltype().toString())) {
            List<Ammeterorprotocol> ammlist = rBillitemAccountMapper.ifhaveNoRecycleByBillId(mssAccountbill.getId());
            boolean flag = true;
            StringBuilder sb = new StringBuilder();
            if (ammlist != null && ammlist.size() > 0) {
                for (Ammeterorprotocol ml : ammlist) {
                    if (ml.getAmmeteruse() != 3) {
                        flag = false;
                        if (StringUtils.isNotEmpty(ml.getProtocolname()))
                            sb.append("《协议》【").append(ml.getProtocolname()).append("】;");
                        else
                            sb.append("《电表》【").append(ml.getAmmetername()).append("】;");
                    }
                }
                map.put("msg", sb.toString() + "不是【回收电费类的电表/协议】!");
                map.put("success", flag);
                if (!flag) return map;
            }
        }
        if ("ln".equals(deployTo) && ("1".equals(mssAccountbill.getBilltype().toString())
                || "3".equals(mssAccountbill.getBilltype().toString())
                || "5".equals(mssAccountbill.getBilltype().toString())
                || "6".equals(mssAccountbill.getBilltype().toString())
                || "7".equals(mssAccountbill.getBilltype().toString())
        )) {
            // 根据报账单查询台账
            List<Long> accountIds = billitemAccountMapper.selectAccountIdByBillId(mssAccountbill.getId());
            if (StringUtils.isNotEmpty(accountIds)) {
                // 根据台账信息获取电表信息
                List<Ammeterorprotocol> ammeterorprotocolList = ammeterorprotocolMapper.selectByAccountIds(accountIds);
                if (StringUtils.isNotEmpty(ammeterorprotocolList)) {
                    //验证设备耗电量局站
                    List<AccountAmountStationInfo> listStation =
                            rBillitemAccountMapper.ifhaveNoAccountAmountStationInfoByBillId(mssAccountbill.getId());
                    if (listStation != null && listStation.size() > 0) {
                        map.put("msg", "当前账期有【" + listStation.size() + "】条局站需要录入【设备耗电量】!");
                        map.put("listStation", true);
                        map.put("success", false);
                        return map;
                    }
                }
            }
        }
        return map;
    }


    @Override
    public List<Map<String, Object>> querystationError(Accountbillpre pre) {
        return accountbillpreMapper.querystationError(pre);// 归集单
    }

    @Override
    public void saveStationRemark(Long id, String remark, String userName) {
        if (id != null) {
            StationError stationError = new StationError();
            stationError.setId(id);
            stationError.setRemark(remark);
            stationError.setRemarker(userName);
            accountbillpreMapper.saveStationRemark(stationError);
        }
    }

    @Override
    public List<MssAccountbill> selectListByIdsAndType(String[] ids, BigDecimal type) {
        if (type == null || ids == null || ids.length == 0) {
            logger.error("查询失败，参数为空");
            return null;
        }
        return billmmapper.selectListByIdsAndType(ids, type);
    }

    private void insertResult(String title, int method, String oper_time, String result, String msg,
                              String failMsg, int num) {
        Syncresult syncresult = new Syncresult();
        syncresult.setTitle(title);
        syncresult.setMethod(method);
        try {
            syncresult.setOperTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(TimeUtils.getNowTime()));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        syncresult.setResult(result);
        syncresult.setMsg(msg);
        syncresult.setFailMsg(failMsg);
        syncresult.setNum(num);
        syncresultMapper.insert(syncresult);
    }

    /**
     * 查询非电费报账单
     *
     * @param mssAccountbill
     * @return
     */
    @Override
    public List<MssAccountbill> selectNonElectricListByAuto(MssAccountbill mssAccountbill) {
        List<MssAccountbill> list = billmmapper.selectNonElectricListByAuto(mssAccountbill);
        for (MssAccountbill b : list) {
            if (b.getStatus() != null && (b.getStatus() == 1 || b.getStatus() == -2 || b.getStatus() == -3 || b.getStatus() == 8)) {
                b.set_disabled(false);
            } else {
                b.set_disabled(true);
            }
            //所属部门如果是二级部门（部门下的部门或者区县公司下的部门），要显示上级部门；如果是分公司下的一级部门，直接显示该部门
            if (StringUtils.isNotEmpty(cascadeCountry(b.getOrgid())))
                b.setFillInDep(cascadeCountry(b.getOrgid()));
        }
        return list;
    }

    /**
     * 保存非电费报账单--->水、气、电
     *
     * @param mssAccountbill
     * @return
     */
    @Override
    public Map<String, Object> saveNonElectricMssAccountbill(MssAccountbill mssAccountbill) {
        Map<String, Object> map = checkNonEletricMssAccountbill(mssAccountbill);
        try {
            if ((boolean) map.get("success")) {
                MssAccountbill accountbill = this.get(mssAccountbill.getId());
                mssAccountbill.setHappenDate(DateUtils.formatDate(DateUtils.parseDate(mssAccountbill.getHappenDate())
                        , "yyyy-MM-dd"));// 处理日期 "2019-06-05 00:00:00"
                if (accountbill == null) {// 查询为空 则插入新的报账单
                    if ("********".equals(mssAccountbill.getAccountCode())) {
                        mssAccountbill.setEnergytype(new BigDecimal("2"));//水
                    } else if ("********".equals(mssAccountbill.getAccountCode())) {
                        mssAccountbill.setEnergytype(new BigDecimal("3"));//气
                    } else if ("********".equals(mssAccountbill.getAccountCode())) {
                        mssAccountbill.setEnergytype(new BigDecimal("1"));//油
                    }
                    mssAccountbill.setTimestamp(new Date());
                    mssAccountbill.setCreateDate(new Date());
                    mssAccountbill.setStatus(1);//状态1,'草稿',2,'待办',3,'生成报帐单',4,'生成凭证',7,'完成',5,'财务通过',-1,'报帐单删除',-2,
                    // '退单',-4,'等待生成,-3,'送财辅失败',8 '财辅退单' */
                    List<MssAccountbill> blist = new ArrayList<>();
                    if ("sc".equals(deployTo)) {
                        List<MssAccountbillitem> itemlist = mssAccountbill.getItem();
                        BigDecimal energytype = mssAccountbill.getEnergytype();
                        for (MssAccountbillitem item : itemlist) {
                            if (item.getUsageId().equals("21")) { // 如果是代垫 增加统御科目
                                if (energytype.doubleValue() == 1) {
                                    //item.setReconciliationAccountCode("**********");
                                    item.setReconciliationAccountName("其他应收款-代垫款-水费");

                                }/*else if(energytype.doubleValue() == 2){
                                    //item.setReconciliationAccountCode("**********");
                                    item.setReconciliationAccountName("其他应收款-代垫款-气费");

                                }else if(energytype.doubleValue() == 3){
                                    //item.setReconciliationAccountCode("**********");
                                    item.setReconciliationAccountName("其他应收款-代垫款-油费");
                                }*/
                            }

                        }
                    }
                    blist.add(mssAccountbill);
                    this.insertList(blist);// 批量插入的方式 Id根据传入的参数 不自动生成
                    insertbillitem(mssAccountbill);//插入 报账明细
                    insertPayinfo(mssAccountbill);// 外部收款人
                    insertClearitem(mssAccountbill.getClearitem());// 挑对信息
                    insertClearitemAccount(mssAccountbill);// 挑对台账信息
//                    insertunionpre(mssAccountbill);//关联归集单
                    //if (mssAccountbill.getBizTypeCode().equals("1")||mssAccountbill.getBizTypeCode().equals("2"))
                    //insertjtltem(mssAccountbill); //关联集团4Glte铁塔
                    //关联 预算 类型为新增 todo:报账对象没有主键id，需要改sql
                    log.info("环境{}", deployTo);
 /*                   if ("ln".equals(deployTo)) {
                        log.info("对billid={}的预算管控", mssAccountbill.getId());
                        boolean b = budgetService.addBudget(mssAccountbill);
                        if (!b) {
                            logger.warn("billid={}的报账关联 预算失败", mssAccountbill.getId());
                            throw new BudgetException("关联预算失败，请重试");
                        }
                    }*/

                    //调用基站一站式稽核
                    log.info("准备开始调用一站式稽核，判定环境:{},报账单id:{}", deployTo, mssAccountbill.getId());
                    if ("sc".equals(deployTo)) {
                        log.info("当前环境为:{}", deployTo);
                        Long billid = mssAccountbill.getId();
                        log.info("对billid={}的开始一站式稽核", billid);
                        if (billid == null) {
                            log.info("billid不存在，稽核不进行");
                        }
                        try {
                            Long billId = mssAccountbill.getId();
                            boolean auditFlag = syncresultMapper.getauditFlag();
                            if (auditFlag) {
                                auditOperation.auditBatchForSaveBill(
                                        "pro_" + billId,
                                        billId
                                );
                            }
                        } catch (Exception e) {
                            insertResult("保存报账单调用基站一站式", 1, "", "失败", "" + mssAccountbill.getId(), e.getMessage(), 1);
                            log.info("保存报账单过程中调用一站式稽核失败");
                        }

                    }

                } else {// 更新
                    this.updateForModel(mssAccountbill);//更新报账单
                    updatebillitem(mssAccountbill);//更新 报账明细
                    updatePayinfo(mssAccountbill); //更新payinfo 外部收款人信息
                    updateClearitem(mssAccountbill);//更新 挑对单
                    updateClearitemAccount(mssAccountbill);//更新 挑对单 台账
                    //updatejtlte(mssAccountbill);

                }

            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("保存报账单异常", e.getMessage());
            throw new BaseException("保存报账单异常" + e.getMessage());
        }
        return map;
    }

    /**
     * 保存非电费报账单-->热力、煤、用油
     *
     * @param mssAccountbill
     * @return
     */
    @Override
    public Map<String, Object> saveNonElectricMssAccountbillNew(MssAccountbill mssAccountbill) {
        // 校验非电报账单
        Map<String, Object> map = checkNonEletricMssAccountbillNew(mssAccountbill);
        try {
            if ((boolean) map.get("success")) {
                MssAccountbill accountbill = this.get(mssAccountbill.getId());
                // 处理日期 "2019-06-05 00:00:00"
                mssAccountbill.setHappenDate(DateUtils.formatDate(DateUtils.parseDate(mssAccountbill.getHappenDate())
                        , "yyyy-MM-dd"));
                // 查询为空 则插入新的报账单
                if (accountbill == null) {
                    if ("********".equals(mssAccountbill.getAccountCode())) {
                        //热力
                        mssAccountbill.setEnergytype(new BigDecimal("4"));
                    } else if ("********".equals(mssAccountbill.getAccountCode())) {
                        //气
                        mssAccountbill.setEnergytype(new BigDecimal("5"));
                    } else if ("********".equals(mssAccountbill.getAccountCode())) {
                        //油
                        mssAccountbill.setEnergytype(new BigDecimal("6"));
                    }
                    mssAccountbill.setTimestamp(new Date());
                    mssAccountbill.setCreateDate(new Date());
                    //状态1,'草稿',2,'待办',3,'生成报帐单',4,'生成凭证',7,'完成',5,'财务通过',-1,'报帐单删除',-2,
                    // '退单',-4,'等待生成,-3,'送财辅失败',8 '财辅退单' */
                    mssAccountbill.setStatus(1);
                    List<MssAccountbill> blist = new ArrayList<>();
                    blist.add(mssAccountbill);
                    // 批量插入的方式 Id根据传入的参数 不自动生成
                    this.insertList(blist);
                    //插入 报账明细
                    insertbillitem(mssAccountbill);
                    // 外部收款人
                    insertPayinfo(mssAccountbill);
                    // 挑对信息
                    insertClearitem(mssAccountbill.getClearitem());
                    // 挑对台账信息
                    insertClearitemAccount(mssAccountbill);
                    insertunionpre(mssAccountbill);//关联归集单
                    //if (mssAccountbill.getBizTypeCode().equals("1")||mssAccountbill.getBizTypeCode().equals("2"))
                    //insertjtltem(mssAccountbill); //关联集团4Glte铁塔
                    //关联 预算 类型为新增 todo:报账对象没有主键id，需要改sql
                    log.info("环境{}", deployTo);
 /*                   if ("ln".equals(deployTo)) {
                        log.info("对billid={}的预算管控", mssAccountbill.getId());
                        boolean b = budgetService.addBudget(mssAccountbill);
                        if (!b) {
                            logger.warn("billid={}的报账关联 预算失败", mssAccountbill.getId());
                            throw new BudgetException("关联预算失败，请重试");
                        }
                    }*/

                } else {// 更新
                    //更新报账单
                    this.updateForModel(mssAccountbill);
                    //更新 报账明细
                    updatebillitem(mssAccountbill);
                    //更新payinfo 外部收款人信息
                    updatePayinfo(mssAccountbill);
                    //更新 挑对单
                    updateClearitem(mssAccountbill);
                    //更新 挑对单 台账
                    updateClearitemAccount(mssAccountbill);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("保存报账单异常", e.getMessage());
            throw new BaseException("保存报账单异常" + e.getMessage());
        }
        return map;
    }


    //插入 报账明细
    private void insertbillitem(MssAccountbill mssAccountbill) {
        List<MssAccountbillitem> list = mssAccountbill.getItem();

        for (MssAccountbillitem item : list) {
            item.setId(IdGenerator.getNextId());
            item.setWriteoffInstanceId(BigDecimal.valueOf(mssAccountbill.getId()));
        }
        itemmapper.insertList(list);
    }

    //更新 报账明细
    private void updatebillitem(MssAccountbill mssAccountbill) {
        List<MssAccountbillitem> list = mssAccountbill.getItem();
        List<MssAccountbillitem> itemlist = new ArrayList<>();
        for (MssAccountbillitem item : list) {
            if (item.getId() == null) {
                item.setId(IdGenerator.getNextId());
                item.setWriteoffInstanceId(BigDecimal.valueOf(mssAccountbill.getId()));
                itemlist.add(item);
            } else {
                itemmapper.updateForModel(item);
            }
        }
        if (itemlist.size() > 0) {
            itemmapper.insertList(itemlist);
        }
    }

    //*****新增非电费报账单时先验证
    public Map<String, Object> checkNonEletricMssAccountbill(MssAccountbill mssAccountbill) {
        Map<String, Object> map = new HashMap<>();
        if (!mssAccountbill.getFillInAccount().equals(ShiroUtils.getUser().getLoginId())) {
            map.put("msg", "不能编辑其他人的报账单");
            map.put("success", false);
            return map;
        }
        BigDecimal sum = mssAccountbill.getSum();
        if (sum == null) {
            map.put("msg", "不含税金额不能为空");
            map.put("success", false);
            return map;
        }
        if (StringUtils.isEmpty(mssAccountbill.getCompanyCode())) {
            map.put("msg", "分公司不能为空");
            map.put("success", false);
            return map;
        }
        BigDecimal inputTaxSum = mssAccountbill.getInputTaxSum();
        if (inputTaxSum == null) {
            mssAccountbill.setInputTaxSum(BigDecimal.ZERO);
        }
        List<MssAccountbillpayinfo> payinfo = mssAccountbill.getPayinfo();
        List<MssSupplierItem2> supplierItem2 = mssAccountbill.getSupplierItem2();
        List<MssAbccustomerBank> customerBank = mssAccountbill.getCustomerBank();

        BigDecimal total = BigDecimal.valueOf(0);
        BigDecimal total1 = sum.add(mssAccountbill.getInputTaxSum());
        if (payinfo != null && payinfo.size() > 0)
            for (MssAccountbillpayinfo pay : payinfo) {
                if (pay.getSum() != null)
                    total = total.add(pay.getSum());
            }
        if (supplierItem2 != null && supplierItem2.size() > 0)
            for (MssSupplierItem2 pay : supplierItem2) {
                total = total.add(pay.getSum());
            }
        if (customerBank != null && customerBank.size() > 0)
            for (MssAbccustomerBank pay : customerBank) {
                total = total.add(pay.getSum());
            }
        // 存在 外部收款人是 才比较
        if (total.compareTo(BigDecimal.ZERO) != 0 && total.abs().doubleValue() != total1.abs().doubleValue()) {
            map.put("msg", "外部收款人金额(和)【" + total.doubleValue() + "】不等于【不含税金额】与【税额】(和)【" + total1.doubleValue() + "】");
            map.put("success", false);
            return map;
        }
        List<MssAccountbillitem> list = mssAccountbill.getItem();
        if (list == null) {
            map.put("msg", "没有明细信息");
            map.put("success", false);
            return map;
        }
        BigDecimal totalmx = BigDecimal.valueOf(0);
        BigDecimal totalmxTax = BigDecimal.valueOf(0);
        for (MssAccountbillitem item : list) {
            totalmx = totalmx.add(item.getSum());//明细不含税 和
            if (item.getTaxAdjustSum() == null)
                item.setTaxAdjustSum(BigDecimal.ZERO);
            totalmxTax = totalmxTax.add(item.getTaxAdjustSum());//明细 税和
        }
        if (totalmx.abs().doubleValue() != sum.abs().doubleValue()) {
            map.put("msg", "明细不含税金额(和)【" + totalmx.doubleValue() + "】不等于【基本信息不含税金额】【" + sum.doubleValue() + "】");
            map.put("success", false);
            return map;
        }
        if (totalmxTax.abs().doubleValue() != inputTaxSum.abs().doubleValue()) {
            map.put("msg", "明细税额(和)【" + totalmxTax.doubleValue() + "】不等于【基本信息税额】【" + inputTaxSum.doubleValue() + "】");
            map.put("success", false);
            return map;
        }
        String type = mssAccountbill.getBilltype().toString();
        if (type.equals("3") || type.equals("5") || type.equals("10")) {
            List<MssAccountclearitem> clearitem = mssAccountbill.getClearitem();
            if (clearitem == null) {
                map.put("msg", "没有挑对信息");
                map.put("success", false);
                return map;
            }
            BigDecimal totalck = BigDecimal.valueOf(0);
            for (MssAccountclearitem item : clearitem) {
                totalck = totalck.add(item.getPickingsum());//明细 和
            }
            if (totalck.abs().doubleValue() != totalmx.add(totalmxTax).abs().doubleValue()) {
                map.put(
                        "msg",
                        "挑对金额(和)【" + totalck.doubleValue() + "】不等于 明细金额(和)【" + totalmx.add(totalmxTax).doubleValue() + "】"
                );
                map.put("success", false);
                return map;
            }
            if (totalck.abs().doubleValue() != sum.add(inputTaxSum).abs().doubleValue()) {
                map.put(
                        "msg",
                        "挑对金额(和)【" + totalck.doubleValue() + "】不等于 报账单金额【" + sum.add(inputTaxSum).doubleValue() + "】"
                );
                map.put("success", false);
                return map;
            }
        }
/*        Accountbillpre accountbillpre = mssAccountbill.getAccountbillpre();// 验证 归集单 预估类型
        if (accountbillpre != null) {
            if ("sc".equals(deployTo) && ("6".equals(mssAccountbill.getPickingMode().toString()) && ("8".equals(mssAccountbill.getBilltype().toString())))) {
                if (!((accountbillpre.getMoney().compareTo(new BigDecimal(0)) < 0) && (totalmx.compareTo(new BigDecimal(0)) > 0))) {
                    map.put("msg", "集团验证业务场景“其他收款”收款报账单金额为正归集单金额填负数，请调整!!");
                    map.put("success", false);
                    return map;
                }
            } else {
                if (((accountbillpre.getUseableMoney().compareTo(new BigDecimal(0)) >= 0) && (totalmx.compareTo(new BigDecimal(0)) < 0)) ||
                        ((accountbillpre.getMoney().compareTo(new BigDecimal(-1)) < 0) && (totalmx.compareTo(new BigDecimal(0)) >= 0))) {
                    map.put("msg", "报账单金额、归集单金额一正已负不允许，请调整台账重新生成报账单!!");
                    map.put("success", false);
                    return map;
                }
            }
        }*/
        //       if ("sc".equals(deployTo) && "9".equals(mssAccountbill.getBilltype().toString())) {
//            1.账单类型为“预估”的报账单的供应商固化为G951100081（其他支付对象-成本结算业务预估），
//            不允许选择其他供应商。
        // 取消限制
//            mssAccountbill.setSupplierCode("G951100081");
//            mssAccountbill.setSupplierName("其他支付对象-成本结算业务预估");
//        }

        //1.报账单业务类型为“付款”或“列并付”时，
        // 需校验报账单关联的台账信息同一个电表/协议不能存在两条及以上，如果同一个电表/协议存在两条及以上台账信息，校验不通过，
        // 提示：同一个电表/协议不允许不同期号报一个报账单。
        /*map = saveCheckAccount(mssAccountbill);
        if (!(boolean) map.get("success"))
            return map;*/
//        if ("2".equals(mssAccountbill.getBizTypeCode()) || "1".equals(mssAccountbill.getBizTypeCode())) {
//            List<Ammeterorprotocol> ammlist = rBillitemAccountMapper.ifhaveTwoAmmeteridByBillId(mssAccountbill
//            .getId());
//            if (ammlist != null && ammlist.size() > 0) {
//                StringBuilder sb = new StringBuilder();
//                for (Ammeterorprotocol ml : ammlist) {
//                    if (StringUtils.isNotEmpty(ml.getAmmetername()))
//                        sb.append("《电表》【").append(ml.getAmmetername()).append("】;");
//                    if (StringUtils.isNotEmpty(ml.getProtocolname()))
//                        sb.append("《协议》【").append(ml.getProtocolname()).append("】;");
//                }
//                map.put("msg", sb.toString() + "多个账期不能同时报账！同一个电表/协议不允许不同期号报一个报账单!");
//                map.put("success", false);
//                return map;
//            }
//        }
        // 收款 报账明细跟挑对 一对一 关系
        //收款业务场景是“其他收款”的，可以不挑对
        if ("sc".equals(deployTo) && !"6".equals(mssAccountbill.getPickingMode().toString()) && ("8".equals(mssAccountbill.getBilltype().toString()) || "11".equals(mssAccountbill.getBilltype().toString()))) {
            int itemSize = mssAccountbill.getItem().size();
            List<MssAccountclearitem> clearitem = mssAccountbill.getClearitem();
            if (clearitem == null) {
                map.put("msg", "收款/调账：没有挑对信息！");
                map.put("success", false);
                return map;
            } else if (clearitem.size() != itemSize) {
                map.put("msg", "【报账明细条数和金额需与挑对条数和金额一致】收款/调账：(" + itemSize + ")条明细信息对应(" + clearitem.size() + ")" +
                        "条挑对信息有误!");
                map.put("success", false);
                return map;
            } else {
                //判断挑对金额是否一致
                try {
                    for (MssAccountclearitem citem : clearitem) {
                        BigDecimal pickingsum = citem.getPickingsum();
                        boolean flag = false;
                        for (MssAccountbillitem item : list) {
                            if (pickingsum.compareTo(item.getSum().add(item.getTaxAdjustSum())) == 0) {
                                flag = true;
                            }
                        }
                        if (!flag) {
                            map.put("msg", "【报账明细金额需与挑对金额一致】收款/调账金额：(" + pickingsum + ")没有找到对应明细!");
                            map.put("success", false);
                            return map;
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        if ("********".equals(mssAccountbill.getAccountCode()) && "1".equals(mssAccountbill.getSuppliertype())
                && !"8".equals(mssAccountbill.getBilltype().toString()) && !"11".equals(mssAccountbill.getBilltype().toString())
                && StringUtils.isNotEmpty(mssAccountbill.getSupplierName()) && mssAccountbill.getSupplierName().startsWith("中国铁塔")) {
//            报账单类型为“报销、挂账，挂账支付、预付，预付冲销，借款冲销，前期借款冲销，预估，预估冲销”，
//            提交时校验报账单选择的供应商的供应商名称是否是以“中国铁塔”开头的供应商，如果是，不允许提交并提示：“自缴水费报账不能选择铁塔公司，请注意！”。当选择的是客户无需校验
            map.put("msg", "自缴水费报账不能选择铁塔公司,当前选择为[" + mssAccountbill.getSupplierName() + "]，请注意！");
            map.put("success", false);
            return map;
        }
        if ("********".equals(mssAccountbill.getAccountCode()) && "1".equals(mssAccountbill.getSuppliertype())
                && !"8".equals(mssAccountbill.getBilltype().toString()) && !"11".equals(mssAccountbill.getBilltype().toString())
                && StringUtils.isNotEmpty(mssAccountbill.getSupplierName()) && mssAccountbill.getSupplierName().startsWith("中国铁塔")) {
//            报账单类型为“报销、挂账，挂账支付、预付，预付冲销，借款冲销，前期借款冲销，预估，预估冲销”，
//            提交时校验报账单选择的供应商的供应商名称是否是以“中国铁塔”开头的供应商，如果是，不允许提交并提示：“自缴发电油费报账不能选择铁塔公司，请注意！”。当选择的是客户无需校验
            map.put("msg", "自缴发电油费报账不能选择铁塔公司,当前选择为[" + mssAccountbill.getSupplierName() + "]，请注意！");
            map.put("success", false);
            return map;
        }
        if ("********".equals(mssAccountbill.getAccountCode()) && "1".equals(mssAccountbill.getSuppliertype())
                && !"8".equals(mssAccountbill.getBilltype().toString()) && !"11".equals(mssAccountbill.getBilltype().toString())
                && StringUtils.isNotEmpty(mssAccountbill.getSupplierName()) && mssAccountbill.getSupplierName().startsWith("中国铁塔")) {
//            报账单类型为“报销、挂账，挂账支付、预付，预付冲销，借款冲销，前期借款冲销，预估，预估冲销”，
//            提交时校验报账单选择的供应商的供应商名称是否是以“中国铁塔”开头的供应商，如果是，不允许提交并提示：“自缴取暖费报账不能选择铁塔公司，请注意！”。当选择的是客户无需校验
            map.put("msg", "自缴取暖费报账不能选择铁塔公司,当前选择为[" + mssAccountbill.getSupplierName() + "]，请注意！");
            map.put("success", false);
            return map;
        }
        //1.账单类型为“预提”的报账单的 附件信息必须上传！
        if ("ln".equals(deployTo) && "9".equals(mssAccountbill.getBilltype().toString())) {
            Attachments model = new Attachments();
            model.setBusiAlias("附件(预提)");
            model.setBusiId(mssAccountbill.getId());
            Integer count = attachmentsMapper.count(model);
            if (count == null || count == 0) {
                map.put("success", false);
                map.put("msg", "预提报账单附件信息必须上传！");
                return map;
            }
        }
        map.put("success", true);
        map.put("msg", "保存成功");
        map.put("code", 0);
        return map;
    }

    /**
     * 新增非电费报账单时先验证 -- 热力、煤、油
     */
    public Map<String, Object> checkNonEletricMssAccountbillNew(MssAccountbill mssAccountbill) {
        Map<String, Object> map = new HashMap<>();
        if (!mssAccountbill.getFillInAccount().equals(ShiroUtils.getUser().getLoginId())) {
            map.put("msg", "不能编辑其他人的报账单");
            map.put("success", false);
            return map;
        }
        // 报账单处 不含税金额
        BigDecimal sum = mssAccountbill.getSum();
        if (sum == null) {
            map.put("msg", "不含税金额不能为空");
            map.put("success", false);
            return map;
        }
        if (StringUtils.isEmpty(mssAccountbill.getCompanyCode())) {
            map.put("msg", "分公司不能为空");
            map.put("success", false);
            return map;
        }
        /**
         * （营改增）进项税金额
         */
        BigDecimal inputTaxSum = mssAccountbill.getInputTaxSum();
        if (inputTaxSum == null) {
            mssAccountbill.setInputTaxSum(BigDecimal.ZERO);
        }
        /**
         * 外部收款人信息
         */
        List<MssAccountbillpayinfo> payinfo = mssAccountbill.getPayinfo();
        /**
         * 供应商银行信息
         */
        List<MssSupplierItem2> supplierItem2 = mssAccountbill.getSupplierItem2();
        /**
         * 客户银行信息
         */
        List<MssAbccustomerBank> customerBank = mssAccountbill.getCustomerBank();

        BigDecimal total = BigDecimal.valueOf(0);
        BigDecimal total1 = sum.add(mssAccountbill.getInputTaxSum());
        if (CollectionUtil.isNotEmpty(payinfo)){
            for (MssAccountbillpayinfo pay : payinfo) {
                if (pay.getSum() != null){
                    total = total.add(pay.getSum());
                }
            }
        }

        if (CollectionUtil.isNotEmpty(supplierItem2)){
            for (MssSupplierItem2 pay : supplierItem2) {
                total = total.add(pay.getSum());
            }
        }

        if (CollectionUtil.isNotEmpty(customerBank)){
            for (MssAbccustomerBank pay : customerBank) {
                total = total.add(pay.getSum());
            }
        }

        // 存在 外部收款人时 才比较
        if (total.compareTo(BigDecimal.ZERO) != 0 && total.abs().doubleValue() != total1.abs().doubleValue()) {
            map.put("msg", "外部收款人金额(和)【" + total.doubleValue() + "】不等于【不含税金额】与【税额】(和)【" + total1.doubleValue() + "】");
            map.put("success", false);
            return map;
        }
        List<MssAccountbillitem> list = mssAccountbill.getItem();
        if (list == null) {
            map.put("msg", "没有明细信息");
            map.put("success", false);
            return map;
        }
        // 报账明细处 不含税金额和
        BigDecimal totalmx = BigDecimal.valueOf(0);
        // 报账明细处 明细 税和
        BigDecimal totalmxTax = BigDecimal.valueOf(0);
        for (MssAccountbillitem item : list) {
            //明细不含税 和
            totalmx = totalmx.add(item.getSum());
            if (item.getTaxAdjustSum() == null) {
                item.setTaxAdjustSum(BigDecimal.ZERO);
            }
            //明细 税和
            totalmxTax = totalmxTax.add(item.getTaxAdjustSum());
        }
        if (totalmx.abs().doubleValue() != sum.abs().doubleValue()) {
            map.put("msg", "明细不含税金额(和)【" + totalmx.doubleValue() + "】不等于【基本信息不含税金额】【" + sum.doubleValue() + "】");
            map.put("success", false);
            return map;
        }
        if (totalmxTax.abs().doubleValue() != inputTaxSum.abs().doubleValue()) {
            map.put("msg", "明细税额(和)【" + totalmxTax.doubleValue() + "】不等于【基本信息税额】【" + inputTaxSum.doubleValue() + "】");
            map.put("success", false);
            return map;
        }
        String type = mssAccountbill.getBilltype().toString();
        // 3挂账支付 5预付冲销 10预估冲销 会生成 挑对报账单
        if (type.equals("3") || type.equals("5") || type.equals("10")) {
            List<MssAccountclearitem> clearitem = mssAccountbill.getClearitem();
            if (clearitem == null) {
                map.put("msg", "没有挑对信息");
                map.put("success", false);
                return map;
            }
            BigDecimal totalck = BigDecimal.valueOf(0);
            for (MssAccountclearitem item : clearitem) {
                //明细 和
                totalck = totalck.add(item.getPickingsum());
            }
            if (totalck.abs().doubleValue() != totalmx.add(totalmxTax).abs().doubleValue()) {
                map.put(
                        "msg",
                        "挑对金额(和)【" + totalck.doubleValue() + "】不等于 明细金额(和)【" + totalmx.add(totalmxTax).doubleValue() + "】"
                );
                map.put("success", false);
                return map;
            }
            if (totalck.abs().doubleValue() != sum.add(inputTaxSum).abs().doubleValue()) {
                map.put(
                        "msg",
                        "挑对金额(和)【" + totalck.doubleValue() + "】不等于 报账单金额【" + sum.add(inputTaxSum).doubleValue() + "】"
                );
                map.put("success", false);
                return map;
            }
        }

        // 收款 报账明细跟挑对 一对一 关系
        //收款业务场景是“其他收款”的，可以不挑对
        // mssAccountbill.getAccountCode() 这里取的是前端选择的经济事项，就是 煤、油、热力三种类型
        if ("********".equals(mssAccountbill.getAccountCode()) && "1".equals(mssAccountbill.getSuppliertype())
                && !"8".equals(mssAccountbill.getBilltype().toString()) && !"11".equals(mssAccountbill.getBilltype().toString())
                && StringUtils.isNotEmpty(mssAccountbill.getSupplierName()) && mssAccountbill.getSupplierName().startsWith("中国铁塔")) {
//            报账单类型为“报销、挂账，挂账支付、预付，预付冲销，借款冲销，前期借款冲销，预估，预估冲销”，
//            提交时校验报账单选择的供应商的供应商名称是否是以“中国铁塔”开头的供应商，如果是，不允许提交并提示：“自缴水费报账不能选择铁塔公司，请注意！”。当选择的是客户无需校验
            map.put("msg", "自缴热力费报账不能选择铁塔公司,当前选择为[" + mssAccountbill.getSupplierName() + "]，请注意！");
            map.put("success", false);
            return map;
        }
        if ("********".equals(mssAccountbill.getAccountCode()) && "1".equals(mssAccountbill.getSuppliertype())
                && !"8".equals(mssAccountbill.getBilltype().toString()) && !"11".equals(mssAccountbill.getBilltype().toString())
                && StringUtils.isNotEmpty(mssAccountbill.getSupplierName()) && mssAccountbill.getSupplierName().startsWith("中国铁塔")) {
//            报账单类型为“报销、挂账，挂账支付、预付，预付冲销，借款冲销，前期借款冲销，预估，预估冲销”，
//            提交时校验报账单选择的供应商的供应商名称是否是以“中国铁塔”开头的供应商，如果是，不允许提交并提示：“自缴发电油费报账不能选择铁塔公司，请注意！”。当选择的是客户无需校验
            map.put("msg", "自缴煤费报账不能选择铁塔公司,当前选择为[" + mssAccountbill.getSupplierName() + "]，请注意！");
            map.put("success", false);
            return map;
        }
        if ("********".equals(mssAccountbill.getAccountCode()) && "1".equals(mssAccountbill.getSuppliertype())
                && !"8".equals(mssAccountbill.getBilltype().toString()) && !"11".equals(mssAccountbill.getBilltype().toString())
                && StringUtils.isNotEmpty(mssAccountbill.getSupplierName()) && mssAccountbill.getSupplierName().startsWith("中国铁塔")) {
//            报账单类型为“报销、挂账，挂账支付、预付，预付冲销，借款冲销，前期借款冲销，预估，预估冲销”，
//            提交时校验报账单选择的供应商的供应商名称是否是以“中国铁塔”开头的供应商，如果是，不允许提交并提示：“自缴取暖费报账不能选择铁塔公司，请注意！”。当选择的是客户无需校验
            map.put("msg", "自缴油费报账不能选择铁塔公司,当前选择为[" + mssAccountbill.getSupplierName() + "]，请注意！");
            map.put("success", false);
            return map;
        }
        //1.账单类型为“预提”的报账单的 附件信息必须上传！
        if ("9".equals(mssAccountbill.getBilltype().toString())) {
            Attachments model = new Attachments();
            model.setBusiAlias("附件(预提)");
            model.setBusiId(mssAccountbill.getId());
            Integer count = attachmentsMapper.count(model);
            if (count == null || count == 0) {
                map.put("success", false);
                map.put("msg", "预提报账单附件信息必须上传！");
                return map;
            }
        }
        map.put("success", true);
        map.put("msg", "保存成功");
        map.put("code", 0);
        return map;
    }

    @Override
    public MssAccountbill selectForSameCity(MssAccountbill mForSameCity) {
        return billmmapper.selectForSameCity(mForSameCity);
    }

    @Autowired
    private MssAccountbillController mssAccountbillController;

    public static void main(String[] args) {

    }

    @Override
    public String removeForAccountIds(Long billId, String accountids) {
        log.info("校验台账明细是否允许删除");
        List<Long> accountIds = Arrays.stream(accountids.split("-")).map(Long::parseLong).collect(Collectors.toList());
        RemoveVerify removeVerify = new RemoveVerify();
        removeVerify
                .addRule(new BillExistVerify())
                .addRule(new AccountOnlyVerify())
                .addRule(new AccountAllVerify())
                .addRule(new AmmeterTypeVerify())
                .addRule(new AccountStatusVerify())
                .addRule(new PickVerify())
                .addRule(new AccountMultipleVerify());

        String verifyResult = removeVerify.Verify(billId, accountIds);

        log.info("判定是否可以执行删除操作");
        if (verifyResult.length() > 21) {
            return String.format("{\n" +
                            "  \"code\": 500,\n" +
                            "  \"msg\": \"%s\"\n" +
                            "}",
                    verifyResult
            );
        }

        log.info("开始执行删除操作");
        String result = mssAccountbillController.addNewBillByExistExcludePcids(billId, accountids);
        return String.format("{\n" +
                        "  \"code\": 0,\n" +
                        "  \"msg\": \"%s\"\n" +
                        "}",
                result
        );

//               log.info("获取原始报账单");
//        MssAccountbill oldMss = (MssAccountbill) ((Map) mssAccountbillController.edit(billId).get("data")).get("data");
//        log.info("删除原有报账信息");
//        AjaxResult delResult = mssAccountbillController.removeById(billId);
//        Integer code = (Integer) delResult.get("code");
//        if (code == 1) {
//            return String.format("报账单:%d已经不允许修改", billId);
//        }
//
//        log.info("开始生成新的报账单");
//        mssAccountbillController.addSave(oldMss);
    }

    @Override
    public int deletePowerAuditByBillIds(String[] billIds) {
        int n = billmmapper.deletePowerAuditByBillIds(billIds);
        return n;
    }

    @Override
    public List<StatisticalAccountBillDTO> listStatisticalAccountBill(AccountBillRequest request) {
        List<StatisticalAccountBillDTO> list = billmmapper.listStatisticalAccountBill(request);
        if (StringUtils.isNotEmpty(list)) {
            // 获取枚举值
            List<PowerCategoryType> billTypes = billmmapper.selectByType("billtype");
            Map<String, String> billTypeMap = billTypes.stream().collect(Collectors.toMap(
                    PowerCategoryType::getTypeCode, PowerCategoryType::getTypeName, (key1, key2) -> key2));
            list.forEach(account -> {
                if (StringUtils.isNotEmpty(billTypeMap.get(account.getAccountBillType()))) {
                    account.setAccountBillType(billTypeMap.get(account.getAccountBillType()));
                }
                account.setStatus("完成");
            });
        }
        return list;
    }


    @Override
    public List<PrepaidAccountBillDTO> listPreAccountBill(AccountBillRequest request) {
        List<PrepaidAccountBillDTO> list = Lists.newArrayList();
        if (request.getExportType() == 1) {
            list = billmmapper.listPreAccountBill(request);
        }
        if (request.getExportType() == 2) {
            list = billmmapper.listSupplierPreAccountBill(request);
        }
        return list;
    }
}
