package com.sccl.modules.business.oilaccount.domain;

import com.sccl.common.constant.ExcelColumn;
import com.sccl.common.constant.enums.OilCategoryEnum;
import com.sccl.common.constant.enums.OilTypeEnum;
import com.sccl.common.constant.enums.TicketTaxRateTypeEnum;
import com.sccl.common.lang.StringUtils;
import com.sccl.common.utils.BigDecimlUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;

@Data
public class OilAccountVo {
    private static final long serialVersionUID = 1L;

    private Long id;

    private Byte oilAccountType;

    private String accountNo;

    private String oilUseBody;

    private String oilImportType;

    private Byte oilType;

    private String oilImportCategory;

    private Byte oilCategory;

    private BigDecimal oilAmount;

    private BigDecimal unitPrice;

    private BigDecimal taxTicketMoney;

    private BigDecimal ticketMoney;

    private BigDecimal otherFee;

    private BigDecimal totalMoney;

    private BigDecimal prepayMoney;

    private BigDecimal paidMoney;

    private String inputerId;

    private Date inputDate;

    private String lastediterId;

    private Date lasteditDate;

    private String feeStartDate;

    private Long orgId;

    private Byte status = 1;

    private Long company;

    private Long country;

    private String delFlag;

    private BigDecimal taxAmount;

    private BigDecimal taxImportRate;

    private Byte taxRate;

    private BigDecimal inputTaxTicketMoney;

    private BigDecimal inputTicketMoney;

    private String remark;
}
