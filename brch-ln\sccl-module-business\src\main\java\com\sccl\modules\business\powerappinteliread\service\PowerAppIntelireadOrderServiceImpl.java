package com.sccl.modules.business.powerappinteliread.service;

import com.sccl.common.lang.StringUtils;
import com.sccl.framework.service.BaseServiceImpl;

import com.sccl.modules.business.order.domain.Order;
import com.sccl.modules.business.order.service.IOrderService;
import com.sccl.modules.business.powerappinteliread.domain.PowerAppInteliread;
import com.sccl.modules.business.powerappinteliread.mapper.PowerAppIntelireadMapper;
import com.sccl.modules.monitor.operlog.domain.OperLog;
import com.sccl.modules.monitor.operlog.mapper.OperLogMapper;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;
import com.sccl.modules.uniflow.common.WFModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
@Service
public class PowerAppIntelireadOrderServiceImpl extends BaseServiceImpl<Order> implements IOrderService {
    private static final Logger logger = LoggerFactory.getLogger(PowerAppIntelireadOrderServiceImpl.class);
    @Autowired
    private PowerAppIntelireadMapper powerAppIntelireadMapper;
    @Autowired
    private OperLogMapper operLogMapper;
    @Override
    public void uniflowCallBack(WFModel wfModel) throws Exception {

        {
            System.out.println("-----------------lt:" + wfModel.toString());
            if (StringUtils.isNotEmpty(wfModel.getCallbackType()) && "PROCESS_STARTED".equals(wfModel.getCallbackType()) && wfModel.getVariables().containsKey("firstNode") && wfModel.getVariables().get("firstNode").equals(true)) {
                if ("ADD_SITEREADING".equals(wfModel.getBusiAlias())) {
                    this.doStartFlow(wfModel.getBusiId(),wfModel.getProcInstId());
                    this.updateStatus (wfModel.getBusiId(), 1);//修改单据状态为流程中
                } else if (StringUtils.isNotEmpty(wfModel.getCallbackType()) && "PROCESS_COMPLETED".equals(wfModel.getCallbackType())) {

                    try {
                        //修改单据状态为已完成并更新数据
                        this.updateStatus (wfModel.getBusiId(), 2);//修改单据状态为流程中
                    } catch (Exception e) {
                        e.printStackTrace();
                        OperLog model = new OperLog();
                        model.setOperName("ammeter");
                        model.setTitle("电表协议流程回调修改单据状态为已完成并更新数据异常");
                        model.setMethod("updateDateByChange");
                        model.setErrorMsg("电表id:" + wfModel.getBusiId() + " 流程：" + wfModel.getBusiAlias() + " 用户：" + wfModel.getApplyUserId());
                        model.setOperTime(new Date());
                        operLogMapper.insert(model);
                        throw e;
                    }
                } else if (StringUtils.isNotEmpty(wfModel.getCallbackType()) && "PROCESS_CANCELLED".equals(wfModel.getCallbackType())) {
                    //只有驳回才更改状态，不同意时还是在流程中
                    Map<String, Object> params = new HashMap<String, Object>();
                    params.put("id", wfModel.getBusiId());
/*                    Ammeterorprotocol ammeterorprotocol = ammeterorprotocolMapper.selectByPrimaryKey(params);
                    if (null != ammeterorprotocol.getBillStatus() && ammeterorprotocol.getBillStatus() == 1) {
                        this.updateStatus(wfModel.getBusiId(), 0);//修改单据状态为草稿
                    } else if (null != ammeterorprotocol.getBillStatus() && ammeterorprotocol.getBillStatus() == 4) {
                        this.updateStatus(wfModel.getBusiId(), 3);//修改单据状态为修改中
                    }*/
                }
            }

        }
    }
    private void updateStatus(String id, int billStatus) {
        Map<String, Object> parms = new HashMap<>();
        parms.put("id", id);
        PowerAppInteliread powerAppInteliread = powerAppIntelireadMapper.selectByPrimaryKey(parms);
        if (null != powerAppInteliread) {
            powerAppInteliread.setBillStatus(billStatus);
            powerAppIntelireadMapper.updateForModel(powerAppInteliread);
        } else {
            logger.error("没有找到对应的基础数据，或已被删除");
        }
    }
    // 提交流程 执行方法
    public void doStartFlow(String busiId, Long processinstid) {
        Map<String, Object> parms = new HashMap<>();
        parms.put("id", busiId);
        PowerAppInteliread powerAppInteliread = powerAppIntelireadMapper.selectByPrimaryKey(parms);
        if (null != powerAppInteliread) {
            powerAppInteliread.setProcessinstid(processinstid);
            powerAppIntelireadMapper.updateForModel(powerAppInteliread);
        } else {
            logger.error("没有找到对应的基础数据，或已被删除");
        }
    }
}
