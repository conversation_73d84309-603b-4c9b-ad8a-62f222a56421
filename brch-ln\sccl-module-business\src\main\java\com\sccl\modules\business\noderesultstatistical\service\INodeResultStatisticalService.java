package com.sccl.modules.business.noderesultstatistical.service;

import com.enrising.dcarbon.audit.RefereeDatasource;
import com.sccl.modules.business.noderesultstatistical.domain.AuditFlag;
import com.sccl.modules.business.noderesultstatistical.domain.NodeResultStatistical;
import com.sccl.framework.service.IBaseService;

import java.util.ArrayList;
import java.util.List;

/**
 * 统计指标 服务层
 * 
 * <AUTHOR>
 * @date 2022-11-24
 */
public interface INodeResultStatisticalService extends IBaseService<NodeResultStatistical>
{


    List<? extends RefereeDatasource> contentList(String nodeKey);
}
