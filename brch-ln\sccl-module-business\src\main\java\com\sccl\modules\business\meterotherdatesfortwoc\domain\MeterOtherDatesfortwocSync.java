package com.sccl.modules.business.meterotherdatesfortwoc.domain;

import com.sccl.modules.business.twoc.domain.TwoCFlag;
import lombok.Data;

@Data
public class MeterOtherDatesfortwocSync implements TwoCFlag {
    /**
     * 用能月份 YYYYMM
     */
    private String statisPeriod;
    /**
     * 省份编码
     */
    private String provinceCode;
    /**
     * 市局组织编码
     */
    private String cityCode;
    /**
     * 市局组织名称
     */
    private String cityName;
    /**
     * 区县组织名称
     */
    private String countyCode;
    /**
     * 区县组织名称
     */
    private String countyName;
    /**
     * 归属类型 1:集团存续 2:股份上市
     */
    private String groupType;
    /**
     * 局站类型
     */
    private String stationType;
    /**
     * 能耗类型
     */
    private String energyType;
    /**
     * 财辅系统报账单号
     */
    private String billCode;
    /**
     * 消耗量
     */
    private String energyConsumption;
}
