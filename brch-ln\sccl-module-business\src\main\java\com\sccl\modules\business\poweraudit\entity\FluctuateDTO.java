package com.sccl.modules.business.poweraudit.entity;

import com.sccl.framework.aspectj.lang.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: 芮永恒
 * @CreateTime: 2024-03-01  16:22
 * @Description: 台账日均电量波动
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FluctuateDTO {

    /**
     * 地市
     */
    @Excel(name = "所属部门")
    private String city;

    /**
     * 区县
     */
//    @Excel(name = "区县公司")
    private String countyCompanies;

    /**
     * 运营分局
     */
    @Excel(name = "运营分局")
    private String operationsBranch;

    /**
     * 台账期号
     */
    @Excel(name = "台账期号")
    private String accountNo;

    /**
     * 电表协议编号
     */
    @Excel(name = "电表户名/协议号码")
    private String ammeterid;


    /**
     * 局站编码
     */
    @Excel(name = "集团站址编码")
    private String stationcode;

    /**
     * 铁塔站址编码
     */
    @Excel(name = "铁塔站址编码")
    private String towerSiteCode;

    /**
     * 日均电量的波动合理性(集团5gr)
     */
    @Excel(name = "类型")
    private String errorType = "日均电量的波动合理性(集团5gr)";

    /**
     * 报账开始时间
     */
    @Excel(name = "报账开始时间")
    private String startTime;

    /**
     * 报账结束时间
     */
    @Excel(name = "报账结束时间")
    private String stopTime;

    /**
     * 本期总用电量
     */
    @Excel(name = "本期总用电量")
    private String degrees;

    /**
     * 台账日均电量
     */
    @Excel(name = "台账日均电量")
    private String useDay;


    /***  集团5gr日均电量（标准电量） */
    @Excel(name = "集团5gr日均电量（标准电量）")
    private String degreesDay;

    /**
     * 标准日均电量
     */
    @Excel(name = "波动幅度")
    private String degreesFluctuate;

}
