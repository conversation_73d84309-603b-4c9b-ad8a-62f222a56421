package com.sccl.modules.mssaccount.mssaccountbillpayinfo.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.mssaccount.mssaccountbillpayinfo.domain.MssAccountbillpayinfo;
import com.sccl.modules.mssaccount.mssaccountbillpayinfo.service.IMssAccountbillpayinfoService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 收款 信息操作处理
 * 
 * <AUTHOR>
 * @date 2019-05-01
 */
@RestController
@RequestMapping("/mssaccount/mssAccountbillpayinfo")
public class MssAccountbillpayinfoController extends BaseController
{
    private String prefix = "mssaccount/mssAccountbillpayinfo";
	
	@Autowired
	private IMssAccountbillpayinfoService mssAccountbillpayinfoService;
	
	@RequiresPermissions("mssaccount:mssAccountbillpayinfo:view")
	@GetMapping()
	public String mssAccountbillpayinfo()
	{
	    return prefix + "/mssAccountbillpayinfo";
	}
	
	/**
	 * 查询收款列表
	 */
	@RequiresPermissions("mssaccount:mssAccountbillpayinfo:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(MssAccountbillpayinfo mssAccountbillpayinfo)
	{
		startPage();
        List<MssAccountbillpayinfo> list = mssAccountbillpayinfoService.selectList(mssAccountbillpayinfo);
		return getDataTable(list);
	}
	
	/**
	 * 新增收款
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存收款
	 */
	@RequiresPermissions("mssaccount:mssAccountbillpayinfo:add")
	//@Log(title = "收款", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody MssAccountbillpayinfo mssAccountbillpayinfo)
	{		
		return toAjax(mssAccountbillpayinfoService.insert(mssAccountbillpayinfo));
	}

	/**
	 * 修改收款
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		MssAccountbillpayinfo mssAccountbillpayinfo = mssAccountbillpayinfoService.get(id);

		Object object = JSONObject.toJSON(mssAccountbillpayinfo);

        return this.success(object);
	}
	
	/**
	 * 修改保存收款
	 */
	@RequiresPermissions("mssaccount:mssAccountbillpayinfo:edit")
	//@Log(title = "收款", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody MssAccountbillpayinfo mssAccountbillpayinfo)
	{		
		return toAjax(mssAccountbillpayinfoService.update(mssAccountbillpayinfo));
	}
	
	/**
	 * 删除收款
	 */
	@RequiresPermissions("mssaccount:mssAccountbillpayinfo:remove")
	//@Log(title = "收款", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(mssAccountbillpayinfoService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看收款
     */
    @RequiresPermissions("mssaccount:mssAccountbillpayinfo:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		MssAccountbillpayinfo mssAccountbillpayinfo = mssAccountbillpayinfoService.get(id);

        Object object = JSONObject.toJSON(mssAccountbillpayinfo);

        return this.success(object);
    }

}
