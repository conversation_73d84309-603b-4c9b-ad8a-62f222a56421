package com.sccl.modules.business.oilcard.mapper;

import com.sccl.modules.business.oilcard.domain.OilCard;
import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.oilcard.domain.OilCardVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 油卡 数据层
 *
 * <AUTHOR>
 * @date 2021-12-16
 */
public interface OilCardMapper extends BaseMapper<OilCard> {
    OilCard selectByOilCardId(String oilCardId);

    /** //根据油卡编号查询(除开当前项) */
    OilCard selectByOilCardIdNoMe(@Param("id") Long id,@Param("oilCardId") String oilCardId);

    OilCard selectById(String busiId);

    List<OilCard> selectListViewCard(OilCard oilCard);

    void updateByOilCardMapper(OilCard oilCard);

    List<OilCardVo> selectListAll(OilCard oilCard);

    void updateForRecharge(OilCard oilCard);
}
