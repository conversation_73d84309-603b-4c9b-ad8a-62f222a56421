package com.sccl.modules.business.ecceptiondetail.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;


/**
 * 公共异常明细表 common_ecception_detail
 * 
 * <AUTHOR>
 * @date 2023-03-23
 */
public class EcceptionDetail extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
    /** 异常主表id */
    private Long sourceid;
    /** 异常来源 */
    private String exceptionSource;
    /** 异常业务表id */
    private Long busitabid;

	/**
	 * 异常回复表id
	 */
	private String replyId;

	/**
	 * 异常回复标识
	 */
	private String replyFlag;

	/**
	 * 流程id
	 */
	private String processinstid;
	/**
	 * 流程类型
	 */
	private String busialias;
	/**
	 * 流程状态 0：草稿 1：流程中 2：申请流程归档完成
	 */
	private String billstatus;

	public String getReplyId() {
		return replyId;
	}

	public void setReplyId(String replyId) {
		this.replyId = replyId;
	}

	public String getReplyFlag() {
		return replyFlag;
	}

	public void setReplyFlag(String replyFlag) {
		this.replyFlag = replyFlag;
	}

	public void setSourceid(Long sourceid)
	{
		this.sourceid = sourceid;
	}

	public Long getSourceid() 
	{
		return sourceid;
	}

	public void setExceptionSource(String exceptionSource)
	{
		this.exceptionSource = exceptionSource;
	}

	public String getExceptionSource() 
	{
		return exceptionSource;
	}

	public void setBusitabid(Long busitabid)
	{
		this.busitabid = busitabid;
	}

	public Long getBusitabid() 
	{
		return busitabid;
	}

	public String getProcessinstid() {
		return processinstid;
	}

	public EcceptionDetail setProcessinstid(String processinstid) {
		this.processinstid = processinstid;
		return this;
	}

	public String getBusialias() {
		return busialias;
	}

	public EcceptionDetail setBusialias(String busialias) {
		this.busialias = busialias;
		return this;
	}

	public String getBillstatus() {
		return billstatus;
	}

	public EcceptionDetail setBillstatus(String billstatus) {
		this.billstatus = billstatus;
		return this;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("sourceid", getSourceid())
            .append("exceptionSource", getExceptionSource())
            .append("busitabid", getBusitabid())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
