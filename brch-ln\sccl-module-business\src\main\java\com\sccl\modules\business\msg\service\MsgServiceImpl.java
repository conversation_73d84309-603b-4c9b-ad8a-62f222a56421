package com.sccl.modules.business.msg.service;

import com.sccl.common.lang.StringUtils;
import com.sccl.common.utils.ObjectStoreUtils;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.utils.ExcelUtil;
import com.sccl.framework.utils.FileUploadUtils;
import com.sccl.modules.autojob.util.convert.DateUtils;
import com.sccl.modules.business.msg.domain.Message;
import com.sccl.modules.business.msg.mapper.MsgMapper;
import com.sccl.modules.system.attachments.domain.Attachments;
import com.sccl.modules.system.attachments.mapper.AttachmentsMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.InputStream;
import java.util.Collections;
import java.util.List;
import java.util.Map;


/**
 * 铁塔稽核结果 服务层实现
 *
 * <AUTHOR>
 * @date 2021-08-16
 */
@Service
public class MsgServiceImpl extends BaseServiceImpl<Message> implements IMsgService {
    @Autowired(required = false)
    private MsgMapper mapper;

    @Autowired(required = false)
    private AttachmentsMapper attachmentsMapper;


    @Override
    public List<Message> getMessageByCity(String city, String from, String to) {
        if (StringUtils.isEmpty(city)) {
            return Collections.emptyList();
        }
        if (!StringUtils.isEmpty(from) && StringUtils.isEmpty(to)) {
            to = DateUtils.getTime();
        }
        return mapper.selectListByCityBetween(city, from, to);
    }

    @Override
    public List<Message> getMessageByCountryAndCompany(Long country, Long company, String from, String to) {
        if (company == null || country == null || StringUtils.isEmpty(from)) {
            return Collections.emptyList();
        }
        to = StringUtils.isEmpty(to) ? DateUtils.getTime() : to;
        return mapper.selectListByCompanyAndCountryBetween(company, country, from, to);
    }


    @Override
    public List<Message> getLatestMessageByTowerId(List<String> towerIdList) {
        if (CollectionUtils.isEmpty(towerIdList)) {
            return Collections.emptyList();
        }
        return mapper.selectLatestMessageByTowerId(towerIdList);
    }

    @Override
    public List<Message> getLatestMessageByTowerKey(List<Long> towerKeyList) {
        if (CollectionUtils.isEmpty(towerKeyList)) {
            return Collections.emptyList();
        }
        return mapper.getLatestMessageByTowerKey(towerKeyList);
    }

    @Override
    public Attachments exportExcel(List<Message> messages, String name, Map<String, String> columnMap, Map<String, String> promptMap) {
        ExcelUtil<Message> excelUtil = new ExcelUtil<>(Message.class);
        String fileName = "站址设备信息表" + DateUtils
                .getTime()
                .replace(" ", "_");
        try {
            InputStream inputStream = excelUtil.exportExcel(messages, columnMap, promptMap, name);
            Attachments attachments = new Attachments();
            String filedIdName = FileUploadUtils.encodingFilename(fileName, ".xls");
            String filedId = filedIdName.substring(0, filedIdName.lastIndexOf("."));
            attachments.setFileName(fileName);
            attachments.setMongodbFileId(filedId);
            attachments.setBusiAlias("附件(稽核记录导出)");
            attachments.setCategoryCode("file");
            attachments.setYear(Integer.valueOf(com.sccl.common.utils.DateUtils.getYear()));
            attachments.setDelFlag("0");
            attachments.setCollection("附件(稽核记录导出)");
            attachments.setBusiId(ShiroUtils.getUserId());
            if (ObjectStoreUtils.upload2OSS(filedId + ".xls", inputStream) && attachmentsMapper.insert(attachments) == 1) {
                String url = ObjectStoreUtils.shareFileByKey(filedId + ".xls");
                attachments.setUploadUrl(url);
                return attachments;
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


}
