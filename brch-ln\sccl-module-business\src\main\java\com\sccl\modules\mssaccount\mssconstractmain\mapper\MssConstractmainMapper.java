package com.sccl.modules.mssaccount.mssconstractmain.mapper;

import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.powermodel.entity.HistoryContractPricePro;
import com.sccl.modules.mssaccount.mssconstractmain.domain.HistoryContractPrice;
import com.sccl.modules.mssaccount.mssconstractmain.domain.MssConstractmain;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 合同 数据层
 *
 * <AUTHOR>
 * @date 2019-05-01
 */
public interface MssConstractmainMapper extends BaseMapper<MssConstractmain> {


    String selectContractPrice(@Param("contractcode") String contractcode);

    String selectContractPricebyammeterid(@Param("ammeterid") String ammeterid);

    List<HistoryContractPrice> selectHistoryContractPrice(HistoryContractPrice historyContractPrice);

    List<HistoryContractPricePro> selectHistoryContractPricePro(HistoryContractPricePro historyContractPrice);
}