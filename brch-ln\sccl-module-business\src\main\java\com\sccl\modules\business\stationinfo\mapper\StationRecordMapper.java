package com.sccl.modules.business.stationinfo.mapper;

import com.sccl.modules.business.stationinfo.domain.StationRecord;
import com.sccl.framework.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 局站历史 数据层
 * 
 * <AUTHOR>
 * @date 2019-05-31
 */
public interface StationRecordMapper extends BaseMapper<StationRecord>
{

    public StationRecord getByStationId (StationRecord stationRecord);

    public void deleteStstionRecord(@Param("stationId") Long stationId);

    public int deleteByStationId(@Param("stationId") String[] stationId);

    public List<StationRecord> selectObjectBy(Map<String,Object> params);

    /**
     * 根据局站id获取最新状态
     * @param stationId 局站id
     * @return
     */
    String getStatusByStationId(@Param("stationId") Long stationId);
}