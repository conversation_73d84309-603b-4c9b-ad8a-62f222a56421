package com.sccl.modules.rental.rentalcarmodel.service;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.rental.rentalcarmodel.mapper.RentalcarmodelMapper;
import com.sccl.modules.rental.rentalordercarmodel.mapper.RentalorderCarmodelMapper;
import org.hibernate.type.BigDecimalType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sccl.modules.rental.rentalcarmodel.domain.Rentalcarmodel;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 车辆 （model） 服务层实现
 *
 * <AUTHOR>
 * @date 2019-08-23
 */
@Service
public class RentalcarmodelServiceImpl extends BaseServiceImpl<Rentalcarmodel> implements IRentalcarmodelService {
    @Autowired
    RentalcarmodelMapper rentalcarmodelMapper;

    @Override
    public int removeByid(Long modelid) {
        Map<String, Object> paramMap = new HashMap<>(2);
        paramMap.put("modelid", modelid);
        return rentalcarmodelMapper.deleteByPrimaryKeyDB(paramMap);
    }

    @Override
    public List<Rentalcarmodel> selectListByIds(String[] toStrArray) {
        return rentalcarmodelMapper.selectListByIds(toStrArray);
    }
    @Override
    public List<Map<String,Object>> statistical(Rentalcarmodel rentalcarmodel){
        if(null == rentalcarmodel.getCompany()|| "-1".equals(rentalcarmodel.getCompany())){
            rentalcarmodel.setCompany(null);
        }
        rentalcarmodel.setStatus("5");
        List<Rentalcarmodel> list = rentalcarmodelMapper.selectList(rentalcarmodel);
        Map<String,Object> params = new HashMap<>();
        params.put("year",rentalcarmodel.getYear());
        params.put("company",rentalcarmodel.getCompany());
        List<Map<String,Object>> lists = rentalcarmodelMapper.statistical(params);
        List<Map<String,Object>> result = new ArrayList<>();
        Map<String,Object> lastMap = new HashMap<>();
        Map<String,Object> map1 = new HashMap<>();
        int count=0;
        int num=0;
        BigDecimal rowSum = BigDecimal.ZERO;
        Map<String,Object> sum  = new HashMap<>();
        BigDecimal allSum = BigDecimal.ZERO;
        for (Map<String,Object> map:lists) {
            if(count == 0){
                lastMap = map;
            }else{
                lastMap = lists.get(count-1);
            }
            String title = rvZeroAndDot(map.get("title")+"");
            if(null != map.get("company") && lastMap.get("company").toString().equals(map.get("company").toString())){
                map1.put("company",lastMap.get("company"));
                map1.put("companyName",lastMap.get("companyName"));
                int i = 0;
                for (Rentalcarmodel value:list) {
                    if(value.getModelid().toString().equals(title)){
                        if(sum.containsKey(title)){
                            sum.put(title,new BigDecimal(sum.get(title).toString()).add(new BigDecimal(map.get("sum")+"")));
                            break;
                        }
                    }
                    i++;
                }
                if(i== list.size()){
                    sum.put(title,BigDecimal.ZERO.add(new BigDecimal(map.get("sum")+"")));
                }

                map1.put(title,map.get("sum"));
                rowSum = rowSum.add(new BigDecimal(map.get("sum").toString()));
                num++;
            }else{
                map1.put("rowSum",rowSum);
                allSum = allSum.add(rowSum);
                result.add(map1);
                map1 = new HashMap<>();
                rowSum = BigDecimal.ZERO;
                num =0;
                int i = 0;
                map1.put("company",lastMap.get("company"));
                map1.put("companyName",lastMap.get("companyName"));
                for (Rentalcarmodel value:list) {
                    if(value.getModelid().toString().equals(title)){
                        if(sum.containsKey(title)){
                            sum.put(title,new BigDecimal(sum.get(title).toString()).add(new BigDecimal(map.get("sum")+"")));
                            break;
                        }
                    }
                    i++;
                }
                if(i== list.size()){
                    sum.put(title,BigDecimal.ZERO.add(new BigDecimal(map.get("sum")+"")));
                }
                map1.put(title,map.get("sum"));
                rowSum = rowSum.add(new BigDecimal(map.get("sum").toString()));
                num++;
            }
            count++;
        }
        if(num != 0 ){
            map1.put("rowSum",rowSum);
            allSum = allSum.add(rowSum);
            result.add(map1);
            sum.put("rowSum",allSum);
            sum.put("companyName","合计（辆）");
            result.add(sum);
        }

        return result;
    }
    public String rvZeroAndDot(String s){
        if (s.isEmpty()) {
            return null;
        }
        if(s.indexOf(".") > 0){
            s = s.replaceAll("0+?$", "");//去掉多余的0
            s = s.replaceAll("[.]$", "");//如最后一位是.则去掉
        }
        return s;
    }

}
