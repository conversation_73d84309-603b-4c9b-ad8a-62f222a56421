package com.sccl.modules.business.lnidc.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.sccl.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode
@TableName(value = "idc_monitor_monthly")
public class IdcMonitorMonthly extends BaseEntity {

    private static final long serialVersionUID = 1L;

    private Long id = IdWorker.getId();

    /**
     * 统计id
     */
    private Long statisticsId;

    /**
     * 年份
     */
    private String year;

    /**
     * 一月
     */
    private String january;

    /**
     * 二月
     */
    private String february;

    /**
     * 三月
     */
    private String march;

    /**
     * 四月
     */
    private String april;

    /**
     * 五月
     */
    private String may;

    /**
     * 六月
     */
    private String june;

    /**
     * 七月
     */
    private String july;

    /**
     * 八月
     */
    private String august;

    /**
     * 九月
     */
    private String september;

    /**
     * 十月
     */
    private String october;

    /**
     * 十一月
     */
    private String november;

    /**
     * 十二月
     */
    private String december;

}
