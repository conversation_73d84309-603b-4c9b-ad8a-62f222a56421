package com.sccl.modules.business.powerauditstaiongrade.entity;

import com.sccl.modules.business.powerauditstaiongrade.util.ExcelExport;
import lombok.Data;


public class StaionMapBill implements ExcelExport {
    /**
     * 局站id
     */
    private String stationid;
    /**
     * 集团LTE站址编码
     */
    private String jtlte_code;
    /**
     * 集团LTE站址名称
     */
    private String jtlte_name;
    /**
     * 集团LTE铁塔站址
     */
    private String jtlte_tacode;
    /**
     * 集团LTE铁塔站址名称
     */
    private String jtlte_taname;
    /**
     * 电表编码
     */
    private String ammetername;
    /**
     * 账期
     */
    private String budget;
    /**
     * 关联时间
     */
    private String updatetime;
    /**
     * 报账单主键
     */
    private String billid;
    /**
     * 财辅报帐单号
     */
    private String writeoff_instance_code;
    /**
     * 预算期间名称
     */
    private String budgetsetname;
    /**
     * 摘要
     */
    private String mark;

    public String getStationid() {
        return stationid;
    }

    public void setStationid(String stationid) {
        this.stationid = stationid;
    }

    public String getJtlte_code() {
        return jtlte_code;
    }

    public void setJtlte_code(String jtlte_code) {
        this.jtlte_code = jtlte_code;
    }

    public String getJtlte_name() {
        return jtlte_name;
    }

    public void setJtlte_name(String jtlte_name) {
        this.jtlte_name = jtlte_name;
    }

    public String getJtlte_tacode() {
        return jtlte_tacode;
    }

    public void setJtlte_tacode(String jtlte_tacode) {
        this.jtlte_tacode = jtlte_tacode;
    }

    public String getJtlte_taname() {
        return jtlte_taname;
    }

    public void setJtlte_taname(String jtlte_taname) {
        this.jtlte_taname = jtlte_taname;
    }

    public String getAmmetername() {
        return ammetername;
    }

    public void setAmmetername(String ammetername) {
        this.ammetername = ammetername;
    }

    public String getBudget() {
        return budget;
    }

    public void setBudget(String budget) {
        this.budget = budget;
    }

    public String getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(String updatetime) {
        this.updatetime = updatetime;
    }

    public String getBillid() {
        return billid;
    }

    public void setBillid(String billid) {
        this.billid = billid;
    }

    public String getWriteoff_instance_code() {
        return writeoff_instance_code;
    }

    public void setWriteoff_instance_code(String writeoff_instance_code) {
        this.writeoff_instance_code = writeoff_instance_code;
    }

    public String getBudgetsetname() {
        return budgetsetname;
    }

    public void setBudgetsetname(String budgetsetname) {
        this.budgetsetname = budgetsetname;
    }

    public String getMark() {
        return mark;
    }

    public void setMark(String mark) {
        this.mark = mark;
    }

    public StaionMapBill() {
    }

    public StaionMapBill(String stationid, String jtlte_code, String jtlte_name, String jtlte_tacode, String jtlte_taname, String ammetername, String budget, String updatetime, String billid, String writeoff_instance_code, String budgetsetname, String mark) {
        this.stationid = stationid;
        this.jtlte_code = jtlte_code;
        this.jtlte_name = jtlte_name;
        this.jtlte_tacode = jtlte_tacode;
        this.jtlte_taname = jtlte_taname;
        this.ammetername = ammetername;
        this.budget = budget;
        this.updatetime = updatetime;
        this.billid = billid;
        this.writeoff_instance_code = writeoff_instance_code;
        this.budgetsetname = budgetsetname;
        this.mark = mark;
    }
}
