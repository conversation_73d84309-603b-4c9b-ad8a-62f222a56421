package com.sccl.modules.business.stationinfo.controller;

import java.util.List;

import com.sccl.modules.business.stationinfo.domain.StationJt5g;
import com.sccl.modules.business.stationinfo.service.IStationJt5gService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;

import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 集团lte网管 信息操作处理
 * 
 * <AUTHOR>
 * @date 2021-05-13
 */
@RestController
@RequestMapping("/ammeterorprotocol/stationJt5g")
public class StationJt5gController extends BaseController
{
    private String prefix = "ammeterorprotocol/stationJt5g";
	
	@Autowired
	private IStationJt5gService stationJt5gService;
	
	@RequiresPermissions("ammeterorprotocol:stationJt5g:view")
	@GetMapping()
	public String stationJt5g()
	{
	    return prefix + "/stationJt5g";
	}
	
	/**
	 * 查询集团lte网管列表
	 */
	@RequiresPermissions("ammeterorprotocol:stationJt5g:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(StationJt5g stationJt5g)
	{
		startPage();
        List<StationJt5g> list = stationJt5gService.selectList(stationJt5g);
		return getDataTable(list);
	}
	
	/**
	 * 新增集团lte网管
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存集团lte网管
	 */
	@RequiresPermissions("ammeterorprotocol:stationJt5g:add")
	@Log(title = "集团lte网管", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody StationJt5g stationJt5g)
	{		
		return toAjax(stationJt5gService.insert(stationJt5g));
	}

	/**
	 * 修改集团lte网管
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		StationJt5g stationJt5g = stationJt5gService.get(id);

		Object object = JSONObject.toJSON(stationJt5g);

        return this.success(object);
	}
	
	/**
	 * 修改保存集团lte网管
	 */
	@RequiresPermissions("ammeterorprotocol:stationJt5g:edit")
	@Log(title = "集团lte网管", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody StationJt5g stationJt5g)
	{		
		return toAjax(stationJt5gService.update(stationJt5g));
	}
	
	/**
	 * 删除集团lte网管
	 */
	@RequiresPermissions("ammeterorprotocol:stationJt5g:remove")
	@Log(title = "集团lte网管", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(stationJt5gService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看集团lte网管
     */
    @RequiresPermissions("ammeterorprotocol:stationJt5g:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		StationJt5g stationJt5g = stationJt5gService.get(id);

        Object object = JSONObject.toJSON(stationJt5g);

        return this.success(object);
    }

}
