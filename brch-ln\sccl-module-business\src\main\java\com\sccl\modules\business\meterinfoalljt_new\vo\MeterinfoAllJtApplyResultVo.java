package com.sccl.modules.business.meterinfoalljt_new.vo;

import com.sccl.framework.aspectj.lang.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

/**
 * 在网表计数据清单-新增申请 一览 结果列表
 */
@Data
public class MeterinfoAllJtApplyResultVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 在网表计数据清单新增申请表.主键id
     */
    private String id;

    /**
     * 所属分公司
     */
    @Excel(name = "所属分公司")
    private String companyName;

    /**
     * 所属部门
     */
    @Excel(name = "所属部门")
    private String countryName;

    /**
     * 电表编号
     */
    @Excel(name = "电表编号")
    private String ammeterCode;

    /**
     * 项目名称
     */
    @Excel(name = "项目名称")
    private String projectname;

    /**
     * 资源局站id
     */
    @Excel(name = "资源局站id")
    private String resstationcode;

    /**
     * 5gr站址编码
     */
    @Excel(name = "5gr站址编码")
    private String stationcode5gr;

    /**
     * 站址名称
     */
    @Excel(name = "站址名称")
    private String resstationname;

    /**
     * 用电类型
     */
    @Excel(name = "用电类型")
    private String electrotype;

    /**
     * 创建时间【yyyy-MM-dd HH:mm:ss】
     */
    @Excel(name = "创建时间")
    private String createTime;

    /**
     * 状态
     */
    private String status;

    /**
     * 状态名称
     */
    @Excel(name = "状态")
    private String statusName;

    /**
     * 是否可选择
     */
    private Boolean _disabled;
}
