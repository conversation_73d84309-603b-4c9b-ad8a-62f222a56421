package com.sccl.modules.business.poweraudit.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.poweraudit.entity.PowerAuditDTO;
import com.sccl.modules.business.poweraudit.entity.PowerAuditEntity;
import com.sccl.modules.business.poweraudit.entity.PowerAuditVO;
import com.sccl.modules.business.poweraudit.service.PowerAuditService;
import com.sccl.modules.business.statinAudit.domain.AuditResults;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-22
 */
@RestController
@RequestMapping("/business/poweraudit")
public class PowerAuditController extends BaseController {

    @Autowired
    private PowerAuditService powerAuditService;



    /**
     * 通过区县查询
     */
    @RequestMapping("/getPowerAuditCompanies")
    public TableDataInfo getPowerAuditCompanies(@RequestBody PowerAuditVO powerAuditVO){
        startPage();
        List<PowerAuditDTO> list = powerAuditService.getPowerAuditCompanies(powerAuditVO);
        return getDataTable(list);
    }

    /**
     * 通过运营分局
     */
    @RequestMapping("/getAuditOperationsBranch")
    public TableDataInfo getAuditOperationsBranch(@RequestBody PowerAuditVO powerAuditVO){
        startPage();
        List<PowerAuditDTO> list = powerAuditService.getAuditOperationsBranch(powerAuditVO);
        return getDataTable(list);
    }


    /**
     * 导出稽核
     */
    @RequestMapping("/exportPowerAudit")
    public void exportPowerAudit(HttpServletResponse response, @RequestBody PowerAuditVO powerAuditVO){
        powerAuditService.exportPowerAudit(response,powerAuditVO,powerAuditVO.getFileName());
    }

    /**
     * 稽核合计
     */
    @RequestMapping("/auditTotal")
    public AjaxResult auditTotal(@RequestBody PowerAuditVO powerAuditVO){
        PowerAuditDTO powerAuditDTO = powerAuditService.auditTotal(powerAuditVO);
        return this.success(powerAuditDTO);
    }

    /**
     * 查询稽核报表
     */
    @PostMapping("/getAuditResult")
    public TableDataInfo getAuditResult(@RequestBody PowerAuditVO vo ){
        startPage();
        List<PowerAuditEntity> powerAuditEntities = powerAuditService.getAuditResult(vo.getAmmeterids());
        return getDataTable(powerAuditEntities);
    }

    /**
     * 查询台账、报账单处的稽核结果pcid
     */
//	@RequiresPermissions("business:account:list")
    @PostMapping("/getJHResultList")
    @ResponseBody
    public AjaxResult getAuditResultList(@RequestBody Map<String,List<String>> map) {
        List<String> keyList = map.get("pcids");
        String type = ObjectUtil.isEmpty(map.get("mssAccountId")) ? "0" : map.get("mssAccountId").toString();
//        List<String> keyList = (List<String>)map.get("pcids");
//        String type = ObjectUtil.isEmpty(map.get("type")) ? "0" : map.get("type").toString();
        List<AuditResults> list = powerAuditService.getAuditResultList(type, keyList);
        return AjaxResult.success(list);
    }

    /**
     * 根据报账单ID查询稽核（最新）
     */
    @RequestMapping("/getAuditByMssAccountId")
    public AjaxResult getAuditByMssAccountId( Long mssAccountId){
        if(ObjectUtil.isEmpty(mssAccountId)){
            return this.success();
        }
        PowerAuditEntity powerAuditEntity = powerAuditService.getAuditByMssAccountId(mssAccountId);
        return this.success(powerAuditEntity);
    }

    /**
     * 根据报账单ID查询稽核（最新）
     */
    @RequestMapping("/getAuditByPcid")
    public AjaxResult getAuditByPcid(String pcid, String mssAccountId){
        if(StrUtil.isBlank(pcid)){
            return this.success();
        }
        PowerAuditEntity powerAuditEntity = powerAuditService.getAuditByPcid(pcid, mssAccountId);
        return this.success(powerAuditEntity);
    }

    /**
     * 通过地市查询
     */
    @RequestMapping("/getPowerAuditByCity")
    public AjaxResult getPowerAuditByCity(@RequestBody PowerAuditVO powerAuditVO){
//        startPage();
        List<PowerAuditDTO> list = powerAuditService.getPowerAuditByCity(powerAuditVO);
        return this.success(list);
    }

    /**
     *  获取台账稽核详情（数值超链接）
     */
    @RequestMapping("/getAuditDetails")
    public AjaxResult getAuditDetails(@RequestBody PowerAuditVO powerAuditVO){
//        startPage();
        PageInfo<T> powerAuditEntities = powerAuditService.getAuditDetails(powerAuditVO);
        return this.success(powerAuditEntities);
    }

    /**
     *  台账稽核详情（数值超链接） 导出
     */
    @RequestMapping("/exportAuditDetails")
    public void exportAuditDetails(HttpServletResponse response,@RequestBody PowerAuditVO powerAuditVO){
        powerAuditService.exportAuditDetails(response, powerAuditVO);
    }

    /**
     * @description: 稽核台账power_account
     * @author: qinxinmin
     * @date: 2024-07-27
     **/
    @PostMapping("/auditAccount")
    @ResponseBody
    public AjaxResult auditAccount(@RequestBody List<Long> pcidList)
    {
        return toAjax(powerAuditService.auditAccount(pcidList));
    }

    /**
     * @description: 稽核台账power_account_es
     * @author: qinxinmin
     * @date: 2024-07-27
     **/
    @PostMapping("/auditAccountEs")
    @ResponseBody
    public AjaxResult auditAccountEs(@RequestBody List<Long> pcidList)
    {
        return toAjax(powerAuditService.auditAccountEs(pcidList));
    }

    /**
     * @description: 稽核所有台账
     * @author: qinxinmin
     * @date: 2024-08-02
     **/
    @PostMapping("/auditAllAccount")
    @ResponseBody
    public AjaxResult auditAllAccount()
    {
        return toAjax(powerAuditService.auditAllAccount());
    }
}
