package com.sccl.modules.protocolexpiration.noaccount.service;

import com.sccl.framework.service.IBaseService;
import com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol;
import com.sccl.modules.system.organization.domain.Organization;

import java.util.List;

/**
 * 台账 服务层
 * 
 * <AUTHOR>
 * @date 2019-05-20
 */
public interface INoAccountAlertService extends IBaseService<Ammeterorprotocol>
{
    public List<Ammeterorprotocol> selectNoAccountList(Ammeterorprotocol ammeterorprotocol);

    public List<Organization> organizationList (String parentcompanyno);
	
}
