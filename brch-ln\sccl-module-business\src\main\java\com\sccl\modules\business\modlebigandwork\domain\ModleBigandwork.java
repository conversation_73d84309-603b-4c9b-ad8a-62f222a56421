package com.sccl.modules.business.modlebigandwork.domain;

import com.sccl.framework.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.StringJoiner;


/**
 * 单价-大工业-办公 表 power_modle_bigandwork
 *
 * <AUTHOR>
 * @date 2023-03-13
 */
public class ModleBigandwork extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 组织编码 power_city_organization org_code字段
     */
    private String orgcode;
    /**
     * 录入人id
     */
    private String loginid;
    /**
     * 月份
     */
    private String month;
    /**
     * 户号
     */
    private String accountno;
    /**
     * 变压器容量
     */
    private BigDecimal capacitydemandbig1;
    /**
     * 当月变压器最大需量
     */
    private BigDecimal capacitydemandbig2;
    /**
     * 变压器计算方式  1容量 /2 需量 power_category_type type_category=bigFlag
     */
    private Integer bigflag;
    /**
     * 创建时间
     */
    private Date createtime;
    /**
     * 更新时间
     */
    private Date updatetime;

    public static boolean checkBig(ModleBigandwork modleBigandwork) {
        return
          modleBigandwork.getCapacitydemandbig1() != null
            &&
            modleBigandwork.getCapacitydemandbig2() != null;
    }

    public static String getBigWorkEx(List<String> accnosForBigWork) {
        StringJoiner joiner = new StringJoiner("--", "当前计算结算数据用电类型为大工业户号的", "全未找到大工业相关信息，请维护");
        accnosForBigWork.forEach(
          item -> {
              joiner.add(item);
          }
        );
        return joiner.toString();

    }

    public String getOrgcode() {
        return orgcode;
    }

    public void setOrgcode(String orgcode) {
        this.orgcode = orgcode;
    }

    public String getLoginid() {
        return loginid;
    }

    public void setLoginid(String loginid) {
        this.loginid = loginid;
    }

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }

    public String getAccountno() {
        return accountno;
    }

    public void setAccountno(String accountno) {
        this.accountno = accountno;
    }

    public BigDecimal getCapacitydemandbig1() {
        return capacitydemandbig1;
    }

    public void setCapacitydemandbig1(BigDecimal capacitydemandbig1) {
        this.capacitydemandbig1 = capacitydemandbig1;
    }

    public BigDecimal getCapacitydemandbig2() {
        return capacitydemandbig2;
    }

    public void setCapacitydemandbig2(BigDecimal capacitydemandbig2) {
        this.capacitydemandbig2 = capacitydemandbig2;
    }

    public Integer getBigflag() {
        return bigflag;
    }

    public void setBigflag(Integer bigflag) {
        this.bigflag = bigflag;
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public Date getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(Date updatetime) {
        this.updatetime = updatetime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
          .append("id", getId())
          .append("orgcode", getOrgcode())
          .append("loginid", getLoginid())
          .append("month", getMonth())
          .append("accountno", getAccountno())
          .append("capacitydemandbig1", getCapacitydemandbig1())
          .append("capacitydemandbig2", getCapacitydemandbig2())
          .append("bigflag", getBigflag())
          .append("createtime", getCreatetime())
          .append("updatetime", getUpdatetime())
          .append("delFlag", getDelFlag())
          .toString();
    }
}
