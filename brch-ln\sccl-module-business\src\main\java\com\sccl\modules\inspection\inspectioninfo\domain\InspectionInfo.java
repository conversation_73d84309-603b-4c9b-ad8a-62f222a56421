package com.sccl.modules.inspection.inspectioninfo.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;
import java.math.BigDecimal;


/**
 * 巡检记录表 power_inspection_info
 * 
 * <AUTHOR>
 * @date 2019-05-27
 */
public class InspectionInfo extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
    /** 电表ID */
    private BigDecimal ammeterid;
    /** 电表名称 */
    private String ammetername;
    /** 电表类型 */
    private BigDecimal ammetercategory;
    /** 所属分公司 */
    private BigDecimal company;
    /** 责任中心 */
    private BigDecimal country;
    /** 台帐ID */
    private BigDecimal pcid;
    /** 台帐账期 */
    private String accountno;
    /** 总电量 */
    private BigDecimal quotereadings;
    /** 定额 */
    private BigDecimal totalusedreading;
    /** 异常类型 1：单价和浮动比都超了 2：浮动比超了 3：单价超了 */
    private BigDecimal exceptiontype;
    /** 责任中心异常处理描述 */
    private String countrydesc;
    /** 责任中心异常处理人 */
    private String countryuser;
    /** 责任中心异常处理时间 */
    private Date countrydate;
    /** 责任中心异常处理状态 1：已处理 0：未处理 */
    private BigDecimal countryinspectflag;
    /** 分公司巡检处理描述 */
    private String companydesc;
    /** 分公司巡检处理人 */
    private String companyuser;
    /** 分公司巡检处理时间 */
    private Date companydate;
    /** 分公司巡检处理状态 1：已处理 0：未处理 */
    private BigDecimal companyinspectflag;
    /** 省公司巡检处理描述 */
    private String provincedesc;
    /** 省公司巡检处理人 */
    private String provinceuser;
    /** 省公司巡检处理时间 */
    private Date provincedate;
    /** 省公司巡检处理状态 1：已处理 0：未处理 */
    private BigDecimal provinceinspectflag;
    /** 插入时间 */
    private Date inputdate;
    /** 录入人 */
    private BigDecimal inputuser;
    /** 最后一次操作人 */
    private BigDecimal modifyuser;
    /** 最后一次操作时间 */
    private Date modifydate;
    /** 备注 */
    private String memo;
    //added
    private String categoryname;//电表协议类型名称
	private String projectname;//项目名称
	private String substation;//分支局
	private String exceptionname;//异常名称
	private String isprovinceinspect;//省公司是否巡检
	private String iscityinspect;//市公司是否巡检
	private String iscountryinspect;//责任中心是否处理
	private String unitpirce;//单价
	private String avgprice;//标准单价
	private String quotereadingsratio;//浮动比
	private String alertquotereading;//标准浮动比
	private String category;//电表协议类型
	private String companyname;//分公司显示值
	private String countryname;//责任中心显示值
	private String strcountrydate;//
	private String strcompanydate;
	private String strprovincedate;

	public void setAmmeterid(BigDecimal ammeterid)
	{
		this.ammeterid = ammeterid;
	}

	public BigDecimal getAmmeterid() 
	{
		return ammeterid;
	}

	public void setAmmetername(String ammetername)
	{
		this.ammetername = ammetername;
	}

	public String getAmmetername() 
	{
		return ammetername;
	}

	public void setAmmetercategory(BigDecimal ammetercategory)
	{
		this.ammetercategory = ammetercategory;
	}

	public BigDecimal getAmmetercategory() 
	{
		return ammetercategory;
	}

	public void setCompany(BigDecimal company)
	{
		this.company = company;
	}

	public BigDecimal getCompany() 
	{
		return company;
	}

	public void setCountry(BigDecimal country)
	{
		this.country = country;
	}

	public BigDecimal getCountry() 
	{
		return country;
	}

	public void setPcid(BigDecimal pcid)
	{
		this.pcid = pcid;
	}

	public BigDecimal getPcid() 
	{
		return pcid;
	}

	public void setAccountno(String accountno)
	{
		this.accountno = accountno;
	}

	public String getAccountno() 
	{
		return accountno;
	}

	public void setQuotereadings(BigDecimal quotereadings)
	{
		this.quotereadings = quotereadings;
	}

	public BigDecimal getQuotereadings() 
	{
		return quotereadings;
	}

	public void setTotalusedreading(BigDecimal totalusedreading)
	{
		this.totalusedreading = totalusedreading;
	}

	public BigDecimal getTotalusedreading() 
	{
		return totalusedreading;
	}

	public void setExceptiontype(BigDecimal exceptiontype)
	{
		this.exceptiontype = exceptiontype;
	}

	public BigDecimal getExceptiontype() 
	{
		return exceptiontype;
	}

	public void setCountrydesc(String countrydesc)
	{
		this.countrydesc = countrydesc;
	}

	public String getCountrydesc() 
	{
		return countrydesc;
	}

	public void setCountryuser(String countryuser)
	{
		this.countryuser = countryuser;
	}

	public String getCountryuser() 
	{
		return countryuser;
	}

	public void setCountrydate(Date countrydate)
	{
		this.countrydate = countrydate;
	}

	public Date getCountrydate() 
	{
		return countrydate;
	}

	public void setCountryinspectflag(BigDecimal countryinspectflag)
	{
		this.countryinspectflag = countryinspectflag;
	}

	public BigDecimal getCountryinspectflag() 
	{
		return countryinspectflag;
	}

	public void setCompanydesc(String companydesc)
	{
		this.companydesc = companydesc;
	}

	public String getCompanydesc() 
	{
		return companydesc;
	}

	public void setCompanyuser(String companyuser)
	{
		this.companyuser = companyuser;
	}

	public String getCompanyuser() 
	{
		return companyuser;
	}

	public void setCompanydate(Date companydate)
	{
		this.companydate = companydate;
	}

	public Date getCompanydate() 
	{
		return companydate;
	}

	public void setCompanyinspectflag(BigDecimal companyinspectflag)
	{
		this.companyinspectflag = companyinspectflag;
	}

	public BigDecimal getCompanyinspectflag() 
	{
		return companyinspectflag;
	}

	public void setProvincedesc(String provincedesc)
	{
		this.provincedesc = provincedesc;
	}

	public String getProvincedesc() 
	{
		return provincedesc;
	}

	public void setProvinceuser(String provinceuser)
	{
		this.provinceuser = provinceuser;
	}

	public String getProvinceuser() 
	{
		return provinceuser;
	}

	public void setProvincedate(Date provincedate)
	{
		this.provincedate = provincedate;
	}

	public Date getProvincedate() 
	{
		return provincedate;
	}

	public void setProvinceinspectflag(BigDecimal provinceinspectflag)
	{
		this.provinceinspectflag = provinceinspectflag;
	}

	public BigDecimal getProvinceinspectflag() 
	{
		return provinceinspectflag;
	}

	public void setInputdate(Date inputdate)
	{
		this.inputdate = inputdate;
	}

	public Date getInputdate() 
	{
		return inputdate;
	}

	public void setInputuser(BigDecimal inputuser)
	{
		this.inputuser = inputuser;
	}

	public BigDecimal getInputuser() 
	{
		return inputuser;
	}

	public void setModifyuser(BigDecimal modifyuser)
	{
		this.modifyuser = modifyuser;
	}

	public BigDecimal getModifyuser() 
	{
		return modifyuser;
	}

	public void setModifydate(Date modifydate)
	{
		this.modifydate = modifydate;
	}

	public Date getModifydate() 
	{
		return modifydate;
	}

	public void setMemo(String memo)
	{
		this.memo = memo;
	}

	public String getMemo() 
	{
		return memo;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("ammeterid", getAmmeterid())
            .append("ammetername", getAmmetername())
            .append("ammetercategory", getAmmetercategory())
            .append("company", getCompany())
            .append("country", getCountry())
            .append("pcid", getPcid())
            .append("accountno", getAccountno())
            .append("quotereadings", getQuotereadings())
            .append("totalusedreading", getTotalusedreading())
            .append("exceptiontype", getExceptiontype())
            .append("countrydesc", getCountrydesc())
            .append("countryuser", getCountryuser())
            .append("countrydate", getCountrydate())
            .append("countryinspectflag", getCountryinspectflag())
            .append("companydesc", getCompanydesc())
            .append("companyuser", getCompanyuser())
            .append("companydate", getCompanydate())
            .append("companyinspectflag", getCompanyinspectflag())
            .append("provincedesc", getProvincedesc())
            .append("provinceuser", getProvinceuser())
            .append("provincedate", getProvincedate())
            .append("provinceinspectflag", getProvinceinspectflag())
            .append("inputdate", getInputdate())
            .append("inputuser", getInputuser())
            .append("modifyuser", getModifyuser())
            .append("modifydate", getModifydate())
            .append("memo", getMemo())
            .toString();
    }

	public String getCategoryname() {
		return categoryname;
	}

	public void setCategoryname(String categoryname) {
		this.categoryname = categoryname;
	}

	public String getProjectname() {
		return projectname;
	}

	public void setProjectname(String projectname) {
		this.projectname = projectname;
	}

	public String getSubstation() {
		return substation;
	}

	public void setSubstation(String substation) {
		this.substation = substation;
	}

	public String getExceptionname() {
		return exceptionname;
	}

	public void setExceptionname(String exceptionname) {
		this.exceptionname = exceptionname;
	}

	public String getIsprovinceinspect() {
		return isprovinceinspect;
	}

	public void setIsprovinceinspect(String isprovinceinspect) {
		this.isprovinceinspect = isprovinceinspect;
	}

	public String getIscityinspect() {
		return iscityinspect;
	}

	public void setIscityinspect(String iscityinspect) {
		this.iscityinspect = iscityinspect;
	}

	public String getIscountryinspect() {
		return iscountryinspect;
	}

	public void setIscountryinspect(String iscountryinspect) {
		this.iscountryinspect = iscountryinspect;
	}

	public String getUnitpirce() {
		return unitpirce;
	}

	public void setUnitpirce(String unitpirce) {
		this.unitpirce = unitpirce;
	}

	public String getAvgprice() {
		return avgprice;
	}

	public void setAvgprice(String avgprice) {
		this.avgprice = avgprice;
	}

	public String getQuotereadingsratio() {
		return quotereadingsratio;
	}

	public void setQuotereadingsratio(String quotereadingsratio) {
		this.quotereadingsratio = quotereadingsratio;
	}

	public String getAlertquotereading() {
		return alertquotereading;
	}

	public void setAlertquotereading(String alertquotereading) {
		this.alertquotereading = alertquotereading;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public String getCompanyname() {
		return companyname;
	}

	public void setCompanyname(String companyname) {
		this.companyname = companyname;
	}

	public String getCountryname() {
		return countryname;
	}

	public void setCountryname(String countryname) {
		this.countryname = countryname;
	}

	public String getStrcountrydate() {
		return strcountrydate;
	}

	public void setStrcountrydate(String strcountrydate) {
		this.strcountrydate = strcountrydate;
	}

	public String getStrcompanydate() {
		return strcompanydate;
	}

	public void setStrcompanydate(String strcompanydate) {
		this.strcompanydate = strcompanydate;
	}

	public String getStrprovincedate() {
		return strprovincedate;
	}

	public void setStrprovincedate(String strprovincedate) {
		this.strprovincedate = strprovincedate;
	}
}
