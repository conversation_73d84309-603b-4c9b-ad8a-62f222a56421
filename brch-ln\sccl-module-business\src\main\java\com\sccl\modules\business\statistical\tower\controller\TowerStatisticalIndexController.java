package com.sccl.modules.business.statistical.tower.controller;

import com.sccl.common.lang.StringUtils;
import com.sccl.common.utils.DateUtils;
import com.sccl.common.utils.RegexUtil;
import com.sccl.common.utils.SystemClock;
import com.sccl.modules.autojob.util.convert.JsonUtil;
import com.sccl.modules.autojob.util.convert.MessageMaster;
import com.sccl.modules.business.cache.utils.RedisUtil;
import com.sccl.modules.business.statistical.framework.StatisticalIndex;
import com.sccl.modules.business.statistical.tower.TowerAuditResultStatisticalIndexGroupHandler;
import com.sccl.modules.business.statistical.tower.TowerProgressStatisticalIndexGroupHandler;
import com.sccl.modules.business.statistical.tower.domain.TowerBillAuditResult;
import com.sccl.modules.business.statistical.tower.domain.TowerBillSyncProgress;
import com.sccl.modules.business.statistical.tower.query.TowerAuditResultSummeryQuery;
import com.sccl.modules.business.statistical.tower.service.ITowerStatisticalIndexService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.FutureTask;

/**
 * <AUTHOR> Yongxiang
 * @Date 2022/10/31 12:19
 * @Email <EMAIL>
 */
@RestController
@RequestMapping("/tower_bill_sync")
@Slf4j
public class TowerStatisticalIndexController {
    @Autowired
    private ITowerStatisticalIndexService service;

    @PostMapping(value = "/progress", produces = "application/json;charset=UTF-8")
    private String progressSync(@RequestBody(required = false) TowerBillSyncProgress progress) {
        if (progress == null) {
            return MessageMaster.DefaultMessage.EMPTY_PARAMS.toString();
        }
        progress.setUpdateTime(DateUtils.getTime());
        TowerProgressStatisticalIndexGroupHandler handler = new TowerProgressStatisticalIndexGroupHandler(progress.getName(), progress.getPushDate(), progress.getPushCount(), progress.getHandleType());
        StatisticalIndex index = progress.getIndexObject();
        index.setOwnerAs(handler.getOwnerAs());
        index.setGroupAlias(handler.getGroupName());
        if (handler.loadInRedis(Collections.singletonList(index))) {
            return MessageMaster.getMessage(MessageMaster.Code.OK, "同步成功");
        }
        return MessageMaster.getMessage(MessageMaster.Code.ERROR, "同步失败");
    }

    @GetMapping(value = "/real_time_progress", produces = "application/json;charset=UTF-8")
    public String getProgressByCity(@RequestParam(value = "CITY", required = false) String city) {
        if (StringUtils.isEmpty(city)) {
            return MessageMaster.DefaultMessage.EMPTY_PARAMS.toString();
        }
        TowerProgressStatisticalIndexGroupHandler handler = new TowerProgressStatisticalIndexGroupHandler(city);
        List<StatisticalIndex> indices = handler.taskOutAllStatisticalIndices();
        return MessageMaster.getMessage(MessageMaster.Code.OK, "获取成功", indices, true, false);
    }

    @PostMapping(value = "/audit_summery", produces = "application/json;charset=UTF-8")
    public String doTowerAuditResultSummery(@RequestBody(required = false) String body) {
        if (StringUtils.isEmpty(body)) {
            return MessageMaster.DefaultMessage.EMPTY_PARAMS.toString();
        }
        TowerAuditResultSummeryQuery query = JsonUtil.jsonStringToPojo(body, TowerAuditResultSummeryQuery.class);
        if (query == null || query.getFrom() == null) {
            return MessageMaster.DefaultMessage.EMPTY_PARAMS.toString();
        }
        TowerBillAuditResult result = null;
        if (query.getCountry() != null || query.getCompany() != null) {
            result = service.doStatistical(query.getCompany(), query.getCountry(), query.getFrom(), query.getTo());
            if (result == null) {
                return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "统计失败，可能无相关数据");
            }
            TowerAuditResultStatisticalIndexGroupHandler handler = new TowerAuditResultStatisticalIndexGroupHandler(query.getCompany(), query.getCountry());
            handler.createNewStatisticalIndex(result);
            if (query.getIsLoadInRedis() != null && query.getIsLoadInRedis()) {
                StatisticalIndex index = result.getIndexObject();
                index.setGroupAlias(handler.getGroupName());
                index.setOwnerAs(handler.getOwnerAs());
                handler.loadInRedis(Collections.singletonList(index));
            }
        } else {
            //全省异步统计
            result = service.doStatistical(query.getFrom(), query.getTo(), query.getIsLoadInRedis() == null || query.getIsLoadInRedis());
        }
        return MessageMaster.getMessage(MessageMaster.Code.OK, "统计成功", result, false, false);
    }

    @GetMapping(value = "/get_summery", produces = "application/json;charset=UTF-8")
    public String getAuditResultSummery(@RequestParam(value = "COMPANY", required = false) Long company, @RequestParam(value = "COUNTRY", required = false) Long country, @RequestParam(value = "STATISTICAL_DATE", required = false) String statisticalDateString) {
        if (StringUtils.isEmpty(statisticalDateString)) {
            return MessageMaster.DefaultMessage.EMPTY_PARAMS.toString();
        }
        if (!RegexUtil.isMatch(statisticalDateString, RegexUtil.Type.DATE_YYYY_MM_DD)) {
            return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "日期格式应该为：yyyy-MM-dd");
        }
        Date statisticalDate = DateUtils.parseDate(statisticalDateString);
        TowerAuditResultStatisticalIndexGroupHandler handler = new TowerAuditResultStatisticalIndexGroupHandler(company, country);
        List<StatisticalIndex> indices = handler.takeOutStatisticalIndicesByDateAndTitle(statisticalDate, null);
        if (CollectionUtils.isEmpty(indices)) {
            String key = "STATISTICAL_LOCK:" + handler.getOwnerAs();
            FutureTask<Object> futureTask = null;
            try {
                boolean flag = RedisUtil.lock(key, SystemClock.now(), 30000, 5000, 5000);
                if (!flag) {
                    indices = handler.takeOutStatisticalIndicesByDateAndTitle(statisticalDate, null);
                    if (!CollectionUtils.isEmpty(indices)) {
                        return MessageMaster.getMessage(MessageMaster.Code.OK, "获取成功", indices, true, false);
                    }
                    return MessageMaster.getMessage(MessageMaster.Code.FORBIDDEN, "系统繁忙，请稍后重试");
                } else {
                    futureTask = RedisUtil.listenLock(key, 0.3, 3 * 60 * 1000);
                    log.info("加锁成功，将从数据库获取");
                    indices = handler.getStatisticalIndicesByDateAndTitle(statisticalDate, null);
                    if (CollectionUtils.isEmpty(indices)) {
                        return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "没有相关的统计记录");
                    } else {
                        handler.loadInRedis(indices);
                        return MessageMaster.getMessage(MessageMaster.Code.OK, "获取成功", indices, true, false);
                    }
                }
            } finally {
                RedisUtil.release(key);
                if (futureTask != null) {
                    futureTask.cancel(true);
                }
            }
        }
        return MessageMaster.getMessage(MessageMaster.Code.OK, "获取成功", indices, true, false);
    }


}
