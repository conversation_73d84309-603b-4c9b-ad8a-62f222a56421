package com.sccl.modules.business.examine.mapper;

import com.sccl.modules.business.examine.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 转供电考核
 *
 * <AUTHOR>
 * @date 2025-02-12
 */
@Mapper
public interface TransferExamineMapper
{
    /**
     * 定时生成本月 转供电考核明细表数据
     * @param year  统计年份【yyyy】
     * @param month 统计月份【MM】
     */
    List<TransferExamineDataVo> statisticsAuto(@Param("year") String year,
                                               @Param("month") String month);

    /**
     * 一览查询
     * @param searchVo
     * @return
     */
    List<TransferExamineResultVo> list(TransferExamineSearchVo searchVo);

    /**
     * 详情 一览查询
     * @param stationSearchVo
     * @return
     */
    List<TransferExamineXqResultVo> xqList(TransferExamineXqSearchVo stationSearchVo);
}