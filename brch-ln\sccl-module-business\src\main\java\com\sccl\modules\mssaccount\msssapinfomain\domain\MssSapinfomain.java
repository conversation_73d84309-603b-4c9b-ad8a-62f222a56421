package com.sccl.modules.mssaccount.msssapinfomain.domain;

import com.sccl.modules.mssaccount.msssapinfodetail.domain.MssSapinfodetail;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.List;


/**
 * 报账回传表 MSS_SAPINFOMAIN
 *
 * <AUTHOR>
 * @date 2019-05-01
 */
public class MssSapinfomain extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private List<MssSapinfodetail> details;

    public List<MssSapinfodetail> getDetails() {
        return details;
    }

    public void setDetails(List<MssSapinfodetail> details) {
        this.details = details;
    }

    private Long msmid;

    public Long getMsmid() {
        return msmid;
    }

    public void setMsmid(Long msmid) {
        this.msmid = msmid;
    }

    /**
     * 报账单号
     */
    private String instancecode;
    /**
     * Sap凭证号
     */
    private String sapcertificatecode;
    /**
     * 会计年度
     */
    private String accountyear;
    /**
     * 公司代码
     */
    private String sapcompanycode;
    /**
     * 制证会计
     */
    private String sapcreator;
    /**
     * 凭证记账日期的月份
     */
    private String accountmonth;
    /**
     *
     */
    private String status;


    public void setInstancecode(String instancecode) {
        this.instancecode = instancecode;
    }

    public String getInstancecode() {
        return instancecode;
    }

    public void setSapcertificatecode(String sapcertificatecode) {
        this.sapcertificatecode = sapcertificatecode;
    }

    public String getSapcertificatecode() {
        return sapcertificatecode;
    }

    public void setAccountyear(String accountyear) {
        this.accountyear = accountyear;
    }

    public String getAccountyear() {
        return accountyear;
    }

    public void setSapcompanycode(String sapcompanycode) {
        this.sapcompanycode = sapcompanycode;
    }

    public String getSapcompanycode() {
        return sapcompanycode;
    }

    public void setSapcreator(String sapcreator) {
        this.sapcreator = sapcreator;
    }

    public String getSapcreator() {
        return sapcreator;
    }

    public void setAccountmonth(String accountmonth) {
        this.accountmonth = accountmonth;
    }

    public String getAccountmonth() {
        return accountmonth;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("msmid", getMsmid())
                .append("instancecode", getInstancecode())
                .append("sapcertificatecode", getSapcertificatecode())
                .append("accountyear", getAccountyear())
                .append("sapcompanycode", getSapcompanycode())
                .append("sapcreator", getSapcreator())
                .append("accountmonth", getAccountmonth())
                .append("status", getStatus())
                .toString();
    }
}
