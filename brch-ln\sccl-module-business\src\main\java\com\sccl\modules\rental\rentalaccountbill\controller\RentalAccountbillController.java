package com.sccl.modules.rental.rentalaccountbill.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.rental.rentalaccountbill.domain.RentalAccountbill;
import com.sccl.modules.rental.rentalaccountbill.service.IRentalAccountbillService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 车辆（报账） 信息操作处理
 * 
 * <AUTHOR>
 * @date 2019-08-23
 */
@RestController
@RequestMapping("/rental/rentalAccountbill")
public class RentalAccountbillController extends BaseController
{
    private String prefix = "rental/rentalAccountbill";
	
	@Autowired
	private IRentalAccountbillService rentalAccountbillService;
	
	@RequiresPermissions("rental:rentalAccountbill:view")
	@GetMapping()
	public String rentalAccountbill()
	{
	    return prefix + "/rentalAccountbill";
	}
	
	/**
	 * 查询车辆（报账）列表
	 */
	@RequiresPermissions("rental:rentalAccountbill:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(RentalAccountbill rentalAccountbill)
	{
		startPage();
        List<RentalAccountbill> list = rentalAccountbillService.selectList(rentalAccountbill);
		return getDataTable(list);
	}
	
	/**
	 * 新增车辆（报账）
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存车辆（报账）
	 */
	@RequiresPermissions("rental:rentalAccountbill:add")
	//@Log(title = "车辆（报账）", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody RentalAccountbill rentalAccountbill)
	{		
		return toAjax(rentalAccountbillService.insert(rentalAccountbill));
	}

	/**
	 * 修改车辆（报账）
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id")Long id)
	{
		RentalAccountbill rentalAccountbill = rentalAccountbillService.get(id);

		Object object = JSONObject.toJSON(rentalAccountbill);

        return this.success(object);
	}
	
	/**
	 * 修改保存车辆（报账）
	 */
	@RequiresPermissions("rental:rentalAccountbill:edit")
	//@Log(title = "车辆（报账）", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody RentalAccountbill rentalAccountbill)
	{		
		return toAjax(rentalAccountbillService.update(rentalAccountbill));
	}
	
	/**
	 * 删除车辆（报账）
	 */
	@RequiresPermissions("rental:rentalAccountbill:remove")
	//@Log(title = "车辆（报账）", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(rentalAccountbillService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看车辆（报账）
     */
    @RequiresPermissions("rental:rentalAccountbill:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id")Long id)
    {
		RentalAccountbill rentalAccountbill = rentalAccountbillService.get(id);

        Object object = JSONObject.toJSON(rentalAccountbill);

        return this.success(object);
    }

}
