package com.sccl.modules.business.stationaudit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.enrising.dcarbon.audit.*;
import com.enrising.dcarbon.codec.JsonUtil;
import com.sccl.common.lang.StringUtils;
import com.sccl.common.utils.SystemClock;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.utils.RedisUtil;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.business.account.domain.Account;
import com.sccl.modules.business.account.mapper.AccountMapper;
import com.sccl.modules.business.auditresult.mapper.AuditResultMapper;
import com.sccl.modules.business.auditresultvo.domain.Auditresultvo;
import com.sccl.modules.business.auditresultvo.mapper.AuditresultvoMapper;
import com.sccl.modules.business.noderesult.domain.NodeResult;
import com.sccl.modules.business.noderesult.service.INodeResultService;
import com.sccl.modules.business.stationaudit.demo.StuauditProgressListener;
import com.sccl.modules.business.stationaudit.msshistory.MssHistroyCreator;
import com.sccl.modules.business.stationaudit.pavgpowertoolow.AvgPowerToolLowCreator;
import com.sccl.modules.business.stationaudit.pcomparequtoa.quotaCompareCreator;
import com.sccl.modules.business.stationaudit.powerhistory.PowerHistoryCreator;
import com.sccl.modules.business.stationaudit.pstationaccountchange.StationAccountChangeCreator;
import com.sccl.modules.business.stationaudit.pstationchangesamemeter.StationChangeSameMeterCreator;
import com.sccl.modules.business.stationaudit.pstationempty.StationEmptyCreator;
import com.sccl.modules.business.stationaudit.pstationgrade.StaionGradeExCreator;
import com.sccl.modules.business.stationaudit.pstationmeterchangesamecode.StationMeterChangeSameCodeCreator;
import com.sccl.modules.business.stationaudit.pstationpowerchange.StationPowerChangeCreator;
import com.sccl.modules.business.stationaudit.pstationprotocolexpired.StationProtocolExpiredCreator;
import com.sccl.modules.business.stationaudit.pstationstop.StationStopCreator;
import com.sccl.modules.business.stationauditnoderesult.service.IStationauditNodeResultService;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.concurrent.FutureTask;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@RestController
@Slf4j
@RequestMapping("/stationaudit")
public class controller extends BaseController {
    /**
     * 评判链
     */
    private RefereeChain chain = RefereeChain
      .builder()
      .addRefereeNode(new RefereeNode(new StationEmptyCreator()))
      .addRefereeNode(new RefereeNode(new StaionGradeExCreator()))
      .addRefereeNode(new RefereeNode(new StationPowerChangeCreator()))
      .addRefereeNode(new RefereeNode(new StationAccountChangeCreator()))
      .addRefereeNode(new RefereeNode(new StationProtocolExpiredCreator()))
      .addRefereeNode(new RefereeNode(new StationChangeSameMeterCreator()))
      .addRefereeNode(new RefereeNode(new StationMeterChangeSameCodeCreator()))
      .addRefereeNode(new RefereeNode(new StationStopCreator()))
      .addRefereeNode(new RefereeNode(new quotaCompareCreator()))
      .addRefereeNode(new RefereeNode(new AvgPowerToolLowCreator()))
      //.addRefereeNode(new RefereeNode(new ContractExCreator()))
      .build();

    private RefereeChainPool chainPool = RefereeChainPool
      .builder(chain)
      .setPoolSize(10)
      .setMinIdle(3)
      .setGetChainTimeout(3, TimeUnit.HOURS)
      .setKeepAliveTime(60, TimeUnit.MINUTES)
      .build();
    /**
     * 稽核上下文
     */
    private AuditContext context = new AuditContext(chainPool,new ThreadPoolExecutor(chainPool.getPoolSize(), chainPool.getPoolSize(), 60, TimeUnit.SECONDS, new LinkedBlockingQueue<>(Integer.MAX_VALUE)), Integer.MAX_VALUE);
    @Autowired
    private IStationauditNodeResultService stationauditNodeResultService;
    @Autowired
    private INodeResultService nodeResultService;
    @Autowired
    private AuditresultvoMapper auditresultvoMapper;
    @Autowired
    private AuditResultMapper auditResultMapper;
    @Autowired
    private AccountMapper accountMapper;

    /**
     * 对当前报账单稽核 的方法
     *
     * @param mssAccountbill
     * @return
     */
    @PostMapping("/audit")
    public AjaxResult audit(@RequestBody MssAccountbill mssAccountbill) {
        long start = System.currentTimeMillis();
        RefereeChain chain = RefereeChain
          .builder()
          .addRefereeNode(new RefereeNode(new MssHistroyCreator()))
          .addRefereeNode(new RefereeNode(new PowerHistoryCreator()))
          .build();

        //创建上下文
        AuditContext context = new AuditContext(chain);

//
//        Map<Long, List<RefereeResult>> resultMap = new HashMap<>();
//        Map<Long, List<RefereeResult>> finalResultMap = resultMap;
//        finalResultMap.put(mssAccountbill.getId(), chain.collectResults());
        context.submit(mssAccountbill);




        /*=================异步稽核=================>*/
//        //直接提交到上下文
//        waitAuditBills.forEach(context::submit);
//
//        //...可以处理其他事情
//
//        过段时间后可以获取稽核结果
//        Map<Long, List<RefereeResult>> resultMap = context.getResultsMapSync();
//        List<RefereeResult> refereeResults = resultMap.get(mssAccountbill.getId());
//        int n = stationauditNodeResultService.insertListResults(refereeResults);
        // 将评判结果插入到数据库
        //创建监听器
//        context.addProgressListener(progress -> {
//            if (progress.getFinishedPercent()==1) {
//                List<RefereeResult> refereeResults = chain.collectResults();
//                int n = stationauditNodeResultService.insertListResults(refereeResults);
//            }
//        });


        long end = System.currentTimeMillis();
        log.info("稽核运行时间{}", (end - start));
        return AjaxResult.success("基站一站式稽核任务开启成功");
    }


    @GetMapping("/demo2")
    public String demo2() {
        return "demo2";
    }


    @PostMapping("/auditBatch")
    public AjaxResult auditBatch(@RequestBody List<Account> accounts,
                                 @RequestParam("progressId") String ProgressId,
                                 @RequestParam("billId") Long billId
    ) {
        long start = System.currentTimeMillis();
        RefereeChain chain = RefereeChain
          .builder()
//                .addRefereeNode(new RefereeNode(new GradeCreatorPowerAccount()))
//                .addRefereeNode(new RefereeNode(new PowerHistoryCreator()))
//                .addRefereeNode(new RefereeNode(new StaionequiCreator()))
//                .addRefereeNode(new RefereeNode(new DiffSACreator()))
//                .addRefereeNode(new RefereeNode(new TietaDiffCreator()))
//                .addRefereeNode(new RefereeNode(new StationRecordCreator()))
//                .addRefereeNode(new RefereeNode(new PowerTransferContractCreator()))

          .build();

        //创建上下文
        AuditContext context = new AuditContext(chain);

        accounts.forEach(
          account -> {
              context.submit(account);
          });

        //监听 进度链结果 放入redis中

        context.addProgressListener(
          progress -> {
//                    ProgressHelper.ThresholdUnit.PERCENT
//                    ProgressHelper helper = new ProgressHelper();
//                    helper.setChangeThresholdPercent(1.0, ProgressHelper.ThresholdUnit.COUNT.name());
//                    helper.setTotalCount(progress.getTotalCount());
              RedisUtil.setJson(ProgressId, progress);
              RedisUtil.setValidTimeForString(ProgressId, 10 * 60 * 1000);
              System.out.println(progress);
              ;
          }
        );

        //监听 任务执行情况，放入数据库中 并且 调用一站式稽核汇总
        context.addProgressListener(progress -> {
            if (progress.getFinishedPercent() == 100) {
//                放入数据库
//                Map<String, List<RefereeResult>> resultsMap = context.getResultsMap();
//                List<RefereeResult> refereeResults = new ArrayList<>();
//                resultsMap.forEach(
//                        (Key, value) -> {
//                            refereeResults.addAll(value);
//                        }
//                );
//                int n = nodeResultService.insertListResults(refereeResults);

                //调用一站式稽核汇总
                MssAccountbill mssAccountbill = new MssAccountbill();
                mssAccountbill.setId(billId);
                AjaxResult ajaxResult = nodeResultService.collectNodeResult(mssAccountbill);
                Object data = ajaxResult.get("data");
                //结果放入redis中
                RedisUtil.setJson(billId + "", data);
                RedisUtil.setValidTimeForString(billId + "", 10 * 60 * 60 * 1000);
            }
        });

        return AjaxResult.success("基站一站式稽核任务开启成功");
    }

    @GetMapping("/testredis")
    public AjaxResult testRedis() {
//        RedisUtil.setObj("lm", "owcao");
//        RedisUtil.setValidTimeForString("wjs", 20 * 1000L);
        Object wjs = RedisUtil.getObj("lm");
//        RedisUtil.getRedisTemplate().expire("lm", 50L, TimeUnit.SECONDS);
        return AjaxResult.success(wjs);
    }
    @GetMapping("/auditbitch")
    public AjaxResult auditbitch(String budget) {
        List<String> billids = accountMapper.setBillidForBudget(budget);
        billids.forEach(
                billid -> auditBatchForSaveBill(billid, Long.valueOf(billid))
        );

        return AjaxResult.success(
                String.format("%s账期一站式稽核数据异步调用结束，正在生成", budget)
        );
    }
    /**
     * 批量新节点
     *
     * @param accounts
     * @param ProgressId
     * @param billId
     * @return
     */
    @PostMapping("/auditBatch2")
    public AjaxResult auditBatch2(@RequestBody(required = false) List<Account> accounts,
                                  @RequestParam("progressId") String ProgressId,
                                  @RequestParam("billId") Long billId
    ) {

        //
        long start = System.currentTimeMillis();
        accounts = accountMapper.selectListByBillId(billId);
        log.info("过滤掉billid={}用电类型非基站的台账", billId);
        accounts = accounts.stream().filter(
          item -> "14".equals(item.getElectrotype())
        ).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(accounts)) {
            log.info("当前billid={}所有台账均为非基站，稽核不启用");
            return AjaxResult.success("稽核结束");
        }
        if (CollectionUtils.isEmpty(accounts)) {
            return AjaxResult.success("当前报账单未在系统中找的台账明细，请联系管理员核实");
        }

        accounts.forEach(
          account -> {
              account.setBillId(billId);
              this.context.submit(account);
          });

        //监听 进度链结果 放入redis中
        //this.context.addProgressListener(
        //  new StuauditProgressListener() {
        //      @Override
        //      public void onProgressChange(Progress progress) {
        //          try {
        //              RedisUtil.setJson(ProgressId, progress);
        //              RedisUtil.setValidTimeForString(ProgressId, 10 * 60 * 1000);
        //          } catch (Exception e) {
        //              log.info("redis异常，缓存中将无billid:{}的进度信息", billId);
        //          }
        //      }
        //  }
        //);
        //监听 任务执行情况，放入数据库中 并且 调用一站式稽核汇总
        List<Account> finalAccounts = accounts;
        this.context.addProgressListener(
          new StuauditProgressListener() {
              @Override
              public void onProgressChange(Progress progress) {
                  this.setName(billId + "");
                  if (progress.getFinishedPercent() == 100) {
                      log.info("billid={}", billId);
//                放入数据库
                      Map<String, List<RefereeResult>> resultsMap = context.getResultsMapSync();
                      List<RefereeResult> refereeResults = new ArrayList<>();
                      resultsMap.forEach(
                        (Key, value) -> {
                            refereeResults.addAll(value);
                        }
                      );
                      int n = nodeResultService.insertListResults(refereeResults);

                      //调用一站式稽核汇总
                      MssAccountbill mssAccountbill = new MssAccountbill();
                      mssAccountbill.setId(billId);
                      AjaxResult ajaxResult = nodeResultService.collectNodeResultNew(mssAccountbill);
                      HashMap<String, Object> data = ((HashMap<String, Object>) ajaxResult.get("data"));

                      //结果放入数据库
                      //逻辑删除 同一报账单数据
                      int n0 = auditresultvoMapper.deleteByBillId(billId);
                      log.info("逻辑删除 汇总数量表{}条", n0);
                      //data转换List<AuditVO>
                      List<AuditVo> auditVos = new ArrayList<>();
                      data.forEach(
                        (k, v) -> {
                            AuditVo auditVo = new AuditVo();
                            auditVo.setNodeTitle(k);
                            ArrayList<AuditVoTwo> twos = new ArrayList<>();
                            try {
                                HashMap<String, Object> map = (HashMap<String, Object>) v;
                                ArrayList<AuditVoTwo> finalTwos = twos;
                                map.forEach(
                                  (k1, v1) -> {
                                      AuditVoTwo auditVoTwo = new AuditVoTwo();
                                      auditVoTwo.setTitle(k1);
                                      Integer num = null;
                                      try {
                                          num = (Integer) v1;
                                      } catch (Exception e) {
                                          auditVoTwo.setKey(((String) v1));
                                      }
                                      auditVoTwo.setNum(num);
                                      finalTwos.add(auditVoTwo);
                                  }
                                );
                            } catch (Exception e) {
                                twos = null;
                            }

                            auditVo.setAuditMsg(twos);
                            auditVos.add(auditVo);

                        }
                      );
                      auditVos.stream().forEach(
                        item -> {
                            ArrayList<AuditVoTwo> auditVoTwos = mergeByTitle(item.getAuditMsg());
                            item.setAuditMsg(auditVoTwos);
                        }
                      );
                      List<AuditVoTwo> mergedAuditMsg = auditVos.stream()
                        .filter(item -> item.getAuditMsg() != null)
                        .flatMap(auditVo -> {
                            List<AuditVoTwo> auditMsg = auditVo.getAuditMsg();
                            auditMsg.forEach(
                              item -> item.setNodeTitlePlus(auditVo
                                                              .getNodeTitle())
                            );
                            return auditMsg.stream();
                        })
                        .collect(Collectors.toList());
                      //结果放入redis中
                      try {
                          RedisUtil.setObj(billId + "", mergedAuditMsg);
                          RedisUtil.getRedisTemplate().expire("" + billId, 60, TimeUnit.MINUTES);
                      } catch (Exception e) {
                          log.info("redis异常，缓存中将无 billdi:{}相关数据", billId);
                      }
                      String content = JSON.toJSONString(mergedAuditMsg);
                      Auditresultvo auditresultvo = new Auditresultvo();
                      auditresultvo.setBillid(billId);
                      auditresultvo.setContent(content);
                      auditresultvo.setCreatetime(new Date());
                      int num_auditvo = auditresultvoMapper.insert(auditresultvo);
                      log.info("插入 汇总数量表{}条", num_auditvo);

                      //调用完毕之后，移除监听器
                      context.removeProgressListenerByName(billId + "");
                      //调用完毕之后，清空context数据
                      finalAccounts.forEach(
                        item -> {
                            context.removeResult(item.getPcid() + "");
                        }
                      );
                  }

              }
          }
        );

        return AjaxResult.success("基站一站式稽核任务开启成功");
    }

    public ArrayList<AuditVoTwo> mergeByTitle(List<AuditVoTwo> originalList) {
        ArrayList<AuditVoTwo> mergedList = new ArrayList<>();

        if (CollectionUtils.isEmpty(originalList)) {
            return null;
        }
        for (AuditVoTwo current : originalList) {
            boolean merged = false;

            for (AuditVoTwo mergedItem : mergedList) {
                if (mergedItem.getTitle().contains(current.getTitle()) || current.getTitle().contains(mergedItem.getTitle())) {
                    mergedItem.setTitle(getShortTitle(mergedItem.getTitle(), current.getTitle()));
                    mergedItem.setNum(getNotNullNum(mergedItem.getNum(), current.getNum()));
                    mergedItem.setKey(getNotNUllKey(mergedItem.getKey(), current.getKey()));
                    merged = true;
                    break;
                }
            }

            if (!merged) {
                mergedList.add(current);
            }
        }

        return mergedList;
    }

    private String getNotNUllKey(String title, String title1) {
        return title == null ? title1 : title;
    }

    private Integer getNotNullNum(Integer num, Integer num1) {
        return num == null ? num1 : num;
    }

    private String getShortTitle(String title1, String title2) {
        return title1.length() > title2.length() ? title1 : title2;
    }


    private void convertAuditVo(List<AuditVo> auditVos) {
        if (CollectionUtils.isEmpty(auditVos)) {
            return;
        }

    }

    /**
     * 报账单保存时候调用
     *
     * @param accounts
     * @param ProgressId
     * @param billId
     * @return
     */
    @GetMapping("/testaudit")
    public AjaxResult auditBatchForSaveBill(String ProgressId,
                                            Long billId
    ) {

        //构建测试数据
        log.info("开始稽核");
        long start = System.currentTimeMillis();
        List<Account> accounts = accountMapper.selectListByBillId(billId);

        log.info("过滤掉billid={}用电类型非基站的台账", billId);
        accounts = accounts.stream().filter(
          item -> "14".equals(item.getElectrotype())
        ).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(accounts)) {
            log.info("当前billid={}所有台账均为非基站，稽核不启用", billId);
            return AjaxResult.success("稽核结束");
        }

        log.info("找到系统中存在billid={}相关的有效台账{}条", billId, accounts.size());
        if (CollectionUtils.isEmpty(accounts)) {
            return AjaxResult.success("当前报账单未在系统中找的台账明细，请联系管理员核实");
        }

        String progressKey = billId + "";
        accounts.forEach(
          account -> {
              account.setBillId(billId);
              context.submit(account, progressKey);
          });

        //监听 任务执行情况，放入数据库中 并且 调用一站式稽核汇总
        List<Account> finalAccounts = accounts;
        context.addProgressListener(
          new StuauditProgressListener() {
              @Override
              public void onProgressChange(Progress progress) {
                  this.setName(progressKey);
                      if (progress.getFinishedPercent()==100&&progress.getProgressKey().equals(progressKey)) {
                          log.info("progress finsh:{} key:{}", progress.getFinishedPercent(),progress.getProgressKey());
                          List<RefereeResult> refereeResults = new ArrayList<>();
                          finalAccounts.forEach(
                            item -> {
                                List<RefereeResult> temp = context.getResult(item.getAuditKey());
                                refereeResults.addAll(temp);
                            }
                          );
                          int n = nodeResultService.insertListResults(refereeResults);

                          //调用一站式稽核汇总
                          MssAccountbill mssAccountbill = new MssAccountbill();
                          mssAccountbill.setId(billId);
                          log.info("开始对billid={}的稽核结果汇总", billId);
                          AjaxResult ajaxResult = nodeResultService.collectNodeResultNew(mssAccountbill);
                          HashMap<String, Object> data = ((HashMap<String, Object>) ajaxResult.get("data"));

                          //结果放入数据库
                          //逻辑删除 同一报账单数据
                          log.info("逻辑删除 billid={}的 auditresultvo重复数据", billId);
                          int n0 = auditresultvoMapper.deleteByBillId(billId);
                          log.info("逻辑删除 汇总数量表{}条", n0);
                          //data转换List<AuditVO>
                          log.info("开始对统计结果map转换为list");
                          List<AuditVo> auditVos = new ArrayList<>();
                          data.forEach(
                            (k, v) -> {
                                AuditVo auditVo = new AuditVo();
                                auditVo.setNodeTitle(k);
                                ArrayList<AuditVoTwo> twos = new ArrayList<>();
                                try {
                                    HashMap<String, Object> map = (HashMap<String, Object>) v;
                                    ArrayList<AuditVoTwo> finalTwos = twos;
                                    map.forEach(
                                      (k1, v1) -> {
                                          AuditVoTwo auditVoTwo = new AuditVoTwo();
                                          auditVoTwo.setTitle(k1);
                                          Integer num = null;
                                          try {
                                              num = (Integer) v1;
                                          } catch (Exception e) {
                                              auditVoTwo.setKey(((String) v1));
                                          }
                                          auditVoTwo.setNum(num);
                                          finalTwos.add(auditVoTwo);
                                      }
                                    );
                                } catch (Exception e) {
                                    twos = null;
                                }

                                auditVo.setAuditMsg(twos);
                                auditVos.add(auditVo);

                            }
                          );
                          auditVos.stream().forEach(
                            item -> {
                                ArrayList<AuditVoTwo> auditVoTwos = mergeByTitle(item.getAuditMsg());
                                item.setAuditMsg(auditVoTwos);
                            }
                          );
                          log.info("billid={}统计结果map转换list成功", billId);
                          log.info("auditVos对象转换");
                          List<AuditVoTwo> mergedAuditMsg = auditVos.stream()
                            .filter(item -> item.getAuditMsg() != null)
                            .flatMap(auditVo -> {
                                List<AuditVoTwo> auditMsg = auditVo.getAuditMsg();
                                auditMsg.forEach(
                                  item -> item.setNodeTitlePlus(auditVo
                                                                  .getNodeTitle())
                                );
                                return auditMsg.stream();
                            })
                            .collect(Collectors.toList());
                          //结果放入redis中
                          try {
                              log.info("billid={}统计结果放入redis", billId);
                              RedisUtil.setObj(billId + "", mergedAuditMsg);
                              RedisUtil.getRedisTemplate().expire("" + billId, 60, TimeUnit.MINUTES);
                          } catch (Exception e) {
                              log.info("redis异常，缓存中将无 billdi:{}相关数据", billId);
                          }
                          log.info("billid={}统计结果放入数据库", billId);
                          String content = JSON.toJSONString(mergedAuditMsg);
                          Auditresultvo auditresultvo = new Auditresultvo();
                          auditresultvo.setBillid(billId);
                          auditresultvo.setContent(content);
                          auditresultvo.setCreatetime(new Date());
                          int num_auditvo = auditresultvoMapper.insert(auditresultvo);
                          log.info("billid={}统计结果放入数据库完毕", billId);
                          log.info("插入 汇总数量表{}条", num_auditvo);
                          context.removeProgressListenerByName(progressKey);
                          log.info("移除billid={}的监听器", billId);

                          finalAccounts.forEach(
                            item -> {
                                context.removeResult(item.getPcid() + "");
                            }
                          );
                          log.info("清空context billid={}的数据", billId);
                      }

              }
          }
        );

        return AjaxResult.success("基站一站式稽核任务开启成功");

    }


    /**
     * 根据进度唯一id 获取进度 情况
     *
     * @param ProgressId
     * @return
     */
    @GetMapping("/proGress")
    public AjaxResult getProGress(@RequestParam("progressId") String ProgressId) {
        return AjaxResult.success(RedisUtil.getJson(ProgressId, Progress.class));
    }

    @GetMapping("/collectNodeResult1")
    public AjaxResult getCollectNodeResult(@RequestParam("billId") String billId) {
        return AjaxResult.success(RedisUtil.getJson(billId, Object.class));
    }


    /**
     * 汇总 台账 评级
     *
     * @param nodeResult
     * @return
     */
    @GetMapping("/getNodeResult")
    public AjaxResult getNodeResult(NodeResult nodeResult) {
        return nodeResultService.getNodeResult(nodeResult);
    }

    /**
     * 汇总 本次台账 上次台账 波动
     *
     * @param nodeResult
     * @return
     */
    @GetMapping("/getNodeResultByStaioncode")
    public AjaxResult collectPowerChange(NodeResult nodeResult) {
        return nodeResultService.collectPowerChange(nodeResult);
    }

    /**
     * 汇总 node_result 结果
     *
     * @param nodeResult
     * @return
     */
    @GetMapping("/collectNodeResult")
    public AjaxResult collectNodeResult(MssAccountbill mssAccountbill) {
        return nodeResultService.collectNodeResultNew2(mssAccountbill);
    }


    /**
     * 参考 xiang 从redis中获取，redis中没有，则从数据库中拿
     *
     * @param company
     * @param country
     * @param statisticalDateString
     * @return
     */
    @GetMapping(value = "/get_summery", produces = "application/json;charset=UTF-8")
    public AjaxResult getAuditResultSummery(
      @RequestParam(value = "redisKey", required = false) String redisKey) {
        if (StringUtils.isEmpty(redisKey)) {
            return AjaxResult.success("错误,redisKey 不能为空");
        }

        Object data = RedisUtil.getObj(redisKey);
        if (data == null) {
            FutureTask<Object> futureTask = null;
            try {
                boolean flag = com.sccl.modules.business.cache.utils.RedisUtil.lock(redisKey, SystemClock.now(),
                                                                                    30000, 5000, 500
                );
                if (!flag) {
                    if (data != null) {
                        return AjaxResult.success(data);
                    }
                } else {
                    futureTask = com.sccl.modules.business.cache.utils.RedisUtil.listenLock(redisKey, 0.3,
                                                                                            3 * 60 * 1000
                    );
                    log.info("加锁成功，将从数据库获取");
                    String content = auditresultvoMapper.selectByBillId(Long.parseLong(redisKey));

                    log.info("对数据库数据判空");
                    if (content == null) {
                        return AjaxResult.success("无数据");
                    }

                    log.info("数据转化");
                    if (content.contains("auditMsg")) {
                        log.info("数据库统计数据格式为修改前，需要转化");
                        List<AuditVo> auditVos = JsonUtil.jsonString2List(content, AuditVo.class);
                        List<AuditVoTwo> mergedAuditMsg = auditVos.stream()
                          .filter(item -> item.getAuditMsg() != null)
                          .flatMap(auditVo -> {
                              List<AuditVoTwo> auditMsg = auditVo.getAuditMsg();
                              auditMsg.forEach(
                                item -> item.setNodeTitlePlus(auditVo
                                                                .getNodeTitle())
                              );
                              return auditMsg.stream();
                          })
                          .collect(Collectors.toList());
                        data = mergedAuditMsg;
                        log.info("当前统计数据为before数据");
                        log.info("过滤掉：电表协议异常->协议正常期限");
                        data = mergedAuditMsg.stream().filter(
                          item -> !item.getTitle().contains("协议正常期限")
                        ).collect(Collectors.toList());
                        return AjaxResult.success(data);
                    } else {
                        log.info("数据库统计数据格式为修改后，不需要转化");
                        data = JsonUtil.jsonString2List(content, AuditVoTwo.class);
                        log.info("过滤掉：电表协议异常->协议正常期限");
                        List<AuditVoTwo> voTwos = (List<AuditVoTwo>) data;
                        data = voTwos.stream().filter(
                          item -> !item.getTitle().contains("协议正常期限")
                        ).collect(Collectors.toList());

                    }


                }
            } finally {
                com.sccl.modules.business.cache.utils.RedisUtil.release(redisKey);
                if (futureTask != null) {
                    futureTask.cancel(true);
                }
            }
        }
        return AjaxResult.success(data);
    }

    /**
     * 根据 稽核明细id 获取对象
     *
     * @param ids
     * @return
     */
    @PostMapping("/getListByIds")
    @ResponseBody
    public AjaxResult ListByIds(@RequestBody List<Long> ids) {
        startPage();

        List<AuditResult> nodeResults = auditResultMapper.getIds(ids);

        Object object = JSONObject.toJSON(nodeResults);

        return AjaxResult.success(object);
    }
}

