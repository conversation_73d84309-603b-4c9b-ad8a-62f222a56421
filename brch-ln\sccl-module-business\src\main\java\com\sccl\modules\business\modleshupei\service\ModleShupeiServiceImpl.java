package com.sccl.modules.business.modleshupei.service;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.business.modleshupei.mapper.ModleShupeiMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sccl.modules.business.modleshupei.domain.ModleShupei;


/**
 * 单价输配电价 服务层实现
 * 
 * <AUTHOR>
 * @date 2023-03-03
 */
@Service
public class ModleShupeiServiceImpl extends BaseServiceImpl<ModleShupei> implements IModleShupeiService
{
    @Autowired
    private ModleShupeiMapper mapper;

    @Override
    public ModleShupei selectByLatest(ModleShupei modleShupei) {
        return mapper.selectByLatest(modleShupei);
    }
}
