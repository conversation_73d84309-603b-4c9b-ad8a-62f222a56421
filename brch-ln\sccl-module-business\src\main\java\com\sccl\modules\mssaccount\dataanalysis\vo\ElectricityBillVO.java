package com.sccl.modules.mssaccount.dataanalysis.vo;


import lombok.Data;

import java.math.BigDecimal;

/**
 * 报账单统计分析
 */
@Data
public class ElectricityBillVO {
    /**
     * 序号
     */
    private String index;

    /**
     * 公司ID
     */
    private Long orgId;

    /**
     * 所属公司
     */
    private String orgName;

    /**
     * 年份
     */
    private String year;

    /**
     * 月份
     */
    private String month;

    /**
     * 总用电量(千瓦时)
     */
    private BigDecimal kwh;

    /**
     * 平均电单价
     */
    private BigDecimal unitPrice;

    /**
     * 总电费(元)
     */
    private BigDecimal electricityCost;

}