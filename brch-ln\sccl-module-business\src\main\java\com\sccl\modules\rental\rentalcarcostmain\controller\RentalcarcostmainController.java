package com.sccl.modules.rental.rentalcarcostmain.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.modules.rental.rentalcar.service.IRentalcarService;
import com.sccl.modules.rental.rentalcarmain.domain.Rentalcarmain;
import com.sccl.modules.system.role.domain.Role;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.service.IUserService;
import com.sccl.modules.uniflow.wfprocinst.service.IWfProcInstService;
import com.sccl.modules.uniflow.wftask.domain.WfTask;
import com.sccl.modules.uniflow.wftask.service.IWfTaskService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.rental.rentalcarcostmain.domain.Rentalcarcostmain;
import com.sccl.modules.rental.rentalcarcostmain.service.IRentalcarcostmainService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 租赁费用主 信息操作处理
 * 
 * <AUTHOR>
 * @date 2019-08-29
 */
@RestController
@RequestMapping("/rental/rentalcarcostmain")
public class RentalcarcostmainController extends BaseController
{
    private String prefix = "rental/rentalcarcostmain";
	
	@Autowired
	private IRentalcarcostmainService rentalcarcostmainService;
	@Autowired
	private IWfProcInstService wfProcInstService;// 流程
	@Autowired
	private IWfTaskService wfTaskService;// 流程
	@Autowired
	private IRentalcarService rentalcarService;
	@Autowired
	private IUserService userService;
	
	@RequiresPermissions("rental:rentalcarcostmain:view")
	@GetMapping()
	public String rentalcarcostmain()
	{
	    return prefix + "/rentalcarcostmain";
	}
	
	/**
	 * 查询租赁费用主列表
	 */
	@RequiresPermissions("rental:rentalcarcostmain:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(Rentalcarcostmain rentalcarcostmain)
	{
		User user = ShiroUtils.getUser();
		List<Role> roles = userService.selectUserRole(user.getId());
		boolean isProAdmin = false;
		boolean isCityAdmin = false;
		boolean isSubAdmin = false;
		for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
			if (role.getCode().startsWith("PROVI_")) {//省能耗费管理员
				isProAdmin = true;
			}
			if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
				isCityAdmin = true;
			}
			if (role.getCode().startsWith("SUB_")) {//县能耗费管理员
				isSubAdmin = true;
			}
		}
		if (isProAdmin) {//  查询权限设置 分公司
		} else if (isCityAdmin) {
			List<IdNameVO> companies = user.getCompanies();
			if (companies != null && companies.size() > 0)
				rentalcarcostmain.setCompany(Long.parseLong(companies.get(0).getId()));
		} else if (isSubAdmin) {
			List<IdNameVO> departments = user.getDepartments();
			if (departments != null && departments.size() > 0)
				rentalcarcostmain.setCountry(Long.parseLong(departments.get(0).getId()));
		} else {
			rentalcarcostmain.setInputuserid(user.getId());
		}
		startPage();
        List<Rentalcarcostmain> list = rentalcarcostmainService.selectList(rentalcarcostmain);
		return getDataTable(list);
	}
	
	/**
	 * 新增租赁费用主
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存租赁费用主
	 */
	@RequiresPermissions("rental:rentalcarcostmain:add")
	//@Log(title = "租赁费用主", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody Rentalcarcostmain rentalcarcostmain)
	{		
		return rentalcarcostmainService.savecostmain(rentalcarcostmain);
	}

	/**
	 * 修改租赁费用主
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		Rentalcarcostmain rentalcarcostmain = rentalcarcostmainService.get(id);

		Object object = JSONObject.toJSON(rentalcarcostmain);

        return this.success(object);
	}
	
	/**
	 * 修改保存租赁费用主
	 */
	@RequiresPermissions("rental:rentalcarcostmain:edit")
	//@Log(title = "租赁费用主", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody Rentalcarcostmain rentalcarcostmain)
	{		
		return toAjax(rentalcarcostmainService.update(rentalcarcostmain));
	}
	
	/**
	 * 删除租赁费用主
	 */
	@RequiresPermissions("rental:rentalcarcostmain:remove")
	//@Log(title = "租赁费用主", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{
		int i = 0;
		try {
			List<Rentalcarcostmain> rentalcarcostmains = rentalcarcostmainService.selectByIds(Convert.toStrArray(ids));
			for (Rentalcarcostmain b : rentalcarcostmains) {
				if (!"1".equals(b.getStatus())) {
					return this.error(1, b.getSetitle() + "(" + b.getId() + ")" + "草稿状态才能删除");
				}
				// 终止流程
				killFlow(b);
			}
			i = rentalcarcostmainService.deleteByIds(Convert.toStrArray(ids));
			return this.success("删除(" + i + ")条");
		} catch (Exception e) {
			e.printStackTrace();
			return this.error(1, "删除失败:" + e.getMessage());
		}
	}

	// 终止流程
	private void killFlow(Rentalcarcostmain b) throws Exception {
		if (b.getIprocessinstid() != null) {
			WfTask wfTask = new WfTask();
			wfTask.setProcInstId(b.getIprocessinstid().toString());// 流程实例id
			List<WfTask> wfTasks = wfTaskService.selectList(wfTask);
			if (wfTasks != null && wfTasks.size() > 0) {
				Map<String, Object> param = new HashMap<>();
				param.put("procTaskId", wfTasks.get(0).getId());
				param.put("procInstId", b.getIprocessinstid());
				param.put("shardKey", "0000");
				param.put("stop", "sys");
				JSONObject jsonObject = wfProcInstService.stopTask(this.getCurrentUser(), param);
				System.out.println(jsonObject.toJSONString());
			}
		}
	}

    /**
     * 查看租赁费用主
     */
    @RequiresPermissions("rental:rentalcarcostmain:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		Rentalcarcostmain rentalcarcostmain = rentalcarcostmainService.selectById(id);

        Object object = JSONObject.toJSON(rentalcarcostmain);

        return this.success(object);
    }

}
