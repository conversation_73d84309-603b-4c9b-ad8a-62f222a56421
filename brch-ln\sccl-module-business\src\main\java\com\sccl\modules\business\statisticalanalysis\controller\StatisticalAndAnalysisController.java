package com.sccl.modules.business.statisticalanalysis.controller;

import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.statisticalanalysis.domain.AmmeterProtocolNumber;
import com.sccl.modules.business.statisticalanalysis.service.IAmmeterProtocolNumberService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/business/statisticalAnalysis")
public class StatisticalAndAnalysisController extends BaseController {
    @Autowired
    private IAmmeterProtocolNumberService numberService;

    @RequiresPermissions("business:statisticalAnalysis:list")
    @GetMapping("/listGroupByCompany")
    public TableDataInfo listGroupByCompany(AmmeterProtocolNumber ammeterProtocolNumber) {
        startPage();
        List<AmmeterProtocolNumber> list = numberService.findGroupByCompany(ammeterProtocolNumber);
        return getDataTable(list);
    }

    @GetMapping("/listGroupByCountry")
    public TableDataInfo listGroupByCountry(AmmeterProtocolNumber ammeterProtocolNumber) {
        startPage();
        List<AmmeterProtocolNumber> list = numberService.findGroupByCountry(ammeterProtocolNumber);
        return getDataTable(list);
    }

}
