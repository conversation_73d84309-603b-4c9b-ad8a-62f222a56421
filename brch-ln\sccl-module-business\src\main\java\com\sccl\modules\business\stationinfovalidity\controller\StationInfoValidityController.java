package com.sccl.modules.business.stationinfovalidity.controller;

import com.alibaba.fastjson.JSONObject;
import com.sccl.common.support.Convert;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.autojob.util.convert.StringUtils;
import com.sccl.modules.business.stationinfovalidity.domain.StationInfoValidity;
import com.sccl.modules.business.stationinfovalidity.service.IStationInfoValidityService;
import com.sccl.modules.system.role.domain.Role;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.service.IUserService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 电站址有效 信息操作处理
 *
 * <AUTHOR>
 * @date 2023-03-31
 */
@RestController
@RequestMapping("/business/stationInfoValidity")
public class StationInfoValidityController extends BaseController {
    private String prefix = "business/stationInfoValidity";
    @Autowired
    private IUserService userService;
    @Autowired
    private IStationInfoValidityService stationInfoValidityService;

    @RequiresPermissions("business:stationInfoValidity:view")
    @GetMapping()
    public String stationInfoValidity() {
        return prefix + "/stationInfoValidity";
    }

    /**
     * 查询电站址有效列表
     */
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(StationInfoValidity stationInfoValidity) {

        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("PROVI_")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
            if (role.getCode().startsWith("SUB_")) {//县能耗费管理员
                isSubAdmin = true;
            }
        }
        //  权限设置
        if (isProAdmin) {

        } else if (isCityAdmin) {
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0)
                stationInfoValidity.setCompany(companies.get(0).getId());
        } else if (isSubAdmin) {
            List<IdNameVO> departments = user.getDepartments();
            if (departments != null && departments.size() > 0)
                stationInfoValidity.setCountry(departments.get(0).getId());
        }
        startPage();
        List<StationInfoValidity> list = stationInfoValidityService.selectList(stationInfoValidity);
        return getDataTable(list);
    }

    /**
     * 新增电站址有效
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存电站址有效
     */
    @RequiresPermissions("business:stationInfoValidity:add")
    @Log(title = "电站址有效", action = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody StationInfoValidity stationInfoValidity) {
        return toAjax(stationInfoValidityService.insert(stationInfoValidity));
    }

    /**
     * 修改电站址有效
     */
    @GetMapping("/edit/{id}")
    public AjaxResult edit(@PathVariable("id") Long id) {
        StationInfoValidity stationInfoValidity = stationInfoValidityService.get(id);

        Object object = JSONObject.toJSON(stationInfoValidity);

        return this.success(object);
    }

    /**
     * 修改保存电站址有效
     */
    @RequiresPermissions("business:stationInfoValidity:edit")
    @Log(title = "电站址有效", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody StationInfoValidity stationInfoValidity) {
        return toAjax(stationInfoValidityService.update(stationInfoValidity));
    }

    /**
     * 删除电站址有效
     */
    @RequiresPermissions("business:stationInfoValidity:remove")
    @Log(title = "电站址有效", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(stationInfoValidityService.deleteByIds(Convert.toStrArray(ids)));
    }


    /**
     * 查看电站址有效
     */
    @RequiresPermissions("business:stationInfoValidity:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id) {
        StationInfoValidity stationInfoValidity = stationInfoValidityService.get(id);

        Object object = JSONObject.toJSON(stationInfoValidity);

        return this.success(object);
    }

    /**
     * 查询有效性站址
     */
    @GetMapping("getStationInfoValidity")
    @ResponseBody
    public AjaxResult getStationInfoValidity(
            @RequestParam("company") String company,
            @RequestParam("electrotype") String electrotype,
            @RequestParam(value = "stationcodeintid", required = false) String stationcodeintid,
            @RequestParam(value = "stationname", required = false) String stationname,
            @RequestParam(value = "stationcodetowercode", required = false) String stationcodetowercode,
            @RequestParam(value = "stationcodetowername", required = false) String stationcodetowername
    ) {
        if (StringUtils.isBlank(electrotype)) {
            return AjaxResult.error(
                    "错误的用电类型,必须属于移动基站分类"
            );
        }
        if (!"14".equals(electrotype.substring(0, 2))) {
            return AjaxResult.error(
                    "错误的用电类型,必须属于移动基站分类"
            );
        }
        if ("142".equals(electrotype.substring(0, 3))) {
            return AjaxResult.error(
                    "错误的用电类型,不支持第三方租赁基站查询"
            );
        }
        if (
                (StringUtils.isNotBlank(stationcodetowercode) || StringUtils.isNotBlank(stationcodetowername))
                        &&
                        (!"141".equals(electrotype.substring(0, 3)))
        ) {
            return AjaxResult.error(
                    "错误，查询铁塔站址用电类型为非铁塔"
            );
        }

        StationInfoValidity parm = new StationInfoValidity();
        parm.setCompany(company);
        parm.setStationcodeintid(stationcodeintid);
        parm.setStationname(stationname);
        parm.setStationcodetowercode(stationcodetowercode);
        parm.setStationcodetowername(stationcodetowername);
        startPage();
        List<StationInfoValidity> stationInfoValidities = stationInfoValidityService.getStationInfoValidity(parm);
        stationInfoValidities.forEach(
                stationinfo -> {
                    if ("141".equals(electrotype.substring(0, 3))) {
                        stationinfo.setStationcode5gr(stationinfo.getStationcodetowercode());
                        stationinfo.setStationname5gr(stationinfo.getStationcodetowername());
                    } else {
                        stationinfo.setStationcode5gr(stationinfo.getStationcodeintid());
                        stationinfo.setStationname5gr(stationinfo.getStationname());
                    }
                });
        return AjaxResult.success(stationInfoValidities);
    }


}
