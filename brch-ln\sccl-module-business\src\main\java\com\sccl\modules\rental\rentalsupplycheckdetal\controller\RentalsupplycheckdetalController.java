package com.sccl.modules.rental.rentalsupplycheckdetal.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.rental.rentalsupplycheckdetal.domain.Rentalsupplycheckdetal;
import com.sccl.modules.rental.rentalsupplycheckdetal.service.IRentalsupplycheckdetalService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 供应商 检验 信息操作处理
 * 
 * <AUTHOR>
 * @date 2019-09-06
 */
@RestController
@RequestMapping("/rental/rentalsupplycheckdetal")
public class RentalsupplycheckdetalController extends BaseController
{
    private String prefix = "rental/rentalsupplycheckdetal";
	
	@Autowired
	private IRentalsupplycheckdetalService rentalsupplycheckdetalService;
	
	@RequiresPermissions("rental:rentalsupplycheckdetal:view")
	@GetMapping()
	public String rentalsupplycheckdetal()
	{
	    return prefix + "/rentalsupplycheckdetal";
	}
	
	/**
	 * 查询供应商 检验列表
	 */
	@RequiresPermissions("rental:rentalsupplycheckdetal:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(Rentalsupplycheckdetal rentalsupplycheckdetal)
	{
		startPage();
        List<Rentalsupplycheckdetal> list = rentalsupplycheckdetalService.selectList(rentalsupplycheckdetal);
		return getDataTable(list);
	}
	
	/**
	 * 新增供应商 检验
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存供应商 检验
	 */
	@RequiresPermissions("rental:rentalsupplycheckdetal:add")
	//@Log(title = "供应商 检验", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody Rentalsupplycheckdetal rentalsupplycheckdetal)
	{		
		return toAjax(rentalsupplycheckdetalService.insert(rentalsupplycheckdetal));
	}

	/**
	 * 修改供应商 检验
	 */
	@GetMapping("/edit/{rsdid}")
	public AjaxResult edit(@PathVariable("rsdid") Long rsdid)
	{
		Rentalsupplycheckdetal rentalsupplycheckdetal = rentalsupplycheckdetalService.get(rsdid);

		Object object = JSONObject.toJSON(rentalsupplycheckdetal);

        return this.success(object);
	}
	
	/**
	 * 修改保存供应商 检验
	 */
	@RequiresPermissions("rental:rentalsupplycheckdetal:edit")
	//@Log(title = "供应商 检验", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody Rentalsupplycheckdetal rentalsupplycheckdetal)
	{		
		return toAjax(rentalsupplycheckdetalService.update(rentalsupplycheckdetal));
	}
	
	/**
	 * 删除供应商 检验
	 */
//	@RequiresPermissions("rental:rentalsupplycheckdetal:remove")
	//@Log(title = "供应商 检验", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(rentalsupplycheckdetalService.deleteByIdsDB(Convert.toStrArray(ids)));
	}


    /**
     * 查看供应商 检验
     */
    @RequiresPermissions("rental:rentalsupplycheckdetal:view")
    @GetMapping("/view/{rsdid}")
    @ResponseBody
    public AjaxResult view(@PathVariable("rsdid") Long rsdid)
    {
		Rentalsupplycheckdetal rentalsupplycheckdetal = rentalsupplycheckdetalService.get(rsdid);

        Object object = JSONObject.toJSON(rentalsupplycheckdetal);

        return this.success(object);
    }

	@PostMapping("/selectByParams")
	@ResponseBody
	public AjaxResult selectByParams(@RequestBody Rentalsupplycheckdetal rentalsupplycheckdetal)
	{
		AjaxResult r = new AjaxResult();
		r.put("data",rentalsupplycheckdetalService.selectByParams(rentalsupplycheckdetal));
		return r;
	}

}
