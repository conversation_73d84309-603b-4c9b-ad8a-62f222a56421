package com.sccl.modules.business.stationaudit.pavgpowertoolow;

import com.enrising.dcarbon.audit.AbstractRefereeContent;
import com.enrising.dcarbon.audit.RefereeDatasource;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AvgPowerTooLowContent  extends AbstractRefereeContent implements RefereeDatasource {
    private Long billId;
    private Long pcid;

    /**
     * 电表编号
     */
    private String ammetername;

    private String startdate;
    private String enddate;
    /**
     * 日均电量
     */
    private BigDecimal avgpower;

    /**
     * 评判信息
     */
    private String exmsg;
}
