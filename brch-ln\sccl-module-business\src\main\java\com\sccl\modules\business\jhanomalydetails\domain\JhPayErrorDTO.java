package com.sccl.modules.business.jhanomalydetails.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import com.sccl.framework.aspectj.lang.annotation.Excel;
import lombok.Data;

@Data
public class JhPayErrorDTO {

    /**
     * 报账单期号
     */
    @TableField("bzdqh")
    @Excel(name = "报账单期号_bz")
    private String bzdqh;

    /**
     * 报账单id
     */
    @TableField("bzdid")
    @Excel(name = "报账单id_bz")
    private String bzdid;


    /**
     * 电表户名/协议编码
     */
    @Excel(name = "电表户名/协议编码_bz")
    private String dbhm;


    /**
     * 台账关联的支付对象
     */
    @TableField("tzglzfdx")
    @Excel(name = "台账关联的支付对象_bz")
    private String tzglzfdx;

    /**
     * 台站关联收款账号
     */
    @TableField("tzglskzh")
    @Excel(name = "台站关联收款账号_bz")
    private String tzglskzh;

    /**
     * 电表基础表关联的支付对象
     */
    @TableField("dbjcbzfdx")
    @Excel(name = "电表基础表关联的支付对象_bz")
    private String dbjcbzfdx;

    /**
     * 电表基础表关联的收款账号
     */
    @TableField("dbjcbskzh")
    @Excel(name = "电表基础表关联的收款账号_bz")
    private String dbjcbskzh;


}
