package com.sccl.modules.business.powerintelligentinf2.service;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.business.powerintelligentinf2.mapper.PowerIntelligentRelateMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sccl.modules.business.powerintelligentinf2.domain.PowerIntelligentRelate;

import java.util.List;
import java.util.Map;


/**
 * PUE管控详情 服务层实现
 * 
 * <AUTHOR>
 * @date 2019-07-18
 */
@Service
public class PowerIntelligentRelateServiceImpl extends BaseServiceImpl<PowerIntelligentRelate> implements IPowerIntelligentRelateService
{
    @Autowired
    private PowerIntelligentRelateMapper powerIntelligentRelateMapper;

    @Override
    public List<Map<String,Object>> selectByList(PowerIntelligentRelate powerIntelligentRelate){
        return powerIntelligentRelateMapper.selectByList(powerIntelligentRelate);
    }

}
