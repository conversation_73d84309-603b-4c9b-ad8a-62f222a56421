package com.sccl.modules.business.powerintelligentinf2.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.modules.business.ammeterorprotocol.service.IAmmeterorprotocolService;
import com.sccl.modules.business.powerintelligentinf2.dto.Powerintelligentinf2Dto;
import com.sccl.modules.business.powerintelligentinf2.service.IPowerIntelligentRelateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.business.powerintelligentinf2.domain.PowerIntelligentInf2;
import com.sccl.modules.business.powerintelligentinf2.service.IPowerIntelligentInf2Service;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.alibaba.fastjson.JSONObject;

/**
 * PUE管控 信息操作处理
 * 
 * <AUTHOR>
 * @date 2019-07-18
 */
@RestController
@RequestMapping("/business/PUECountrols")
public class PowerIntelligentInf2Controller extends BaseController
{
    private String prefix = "business/PUECountrols";
	
	@Autowired
	private IPowerIntelligentInf2Service powerIntelligentInf2Service;
	@Autowired
	private IAmmeterorprotocolService ammeterorprotocolService;


	@Autowired
	private IPowerIntelligentRelateService powerIntelligentRelateService;

//	public void  sendTotalPower() {
//		try{
//			List<Map<String,Object>> lists = powerIntelligentInf2Service.selectTotalPower();
//			if(lists.size() != 0){
//				SyPowerService.sendTotalPower("成都",lists);
//			}
//		}catch (Exception e){
//			e.printStackTrace();
//		}
//	}

//	@RequiresPermissions("business:PUECountrols:view")
	@GetMapping()
	public String powerIntelligentInf2()
	{
	    return prefix + "/powerIntelligentInf2";
	}
	
	/**
	 * 查询PUE管控列表
	 */
//	@RequiresPermissions("business:PUECountrols:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(PowerIntelligentInf2 powerIntelligentInf2,String ammeterCode)
	{
		List<IdNameVO> companies = new ArrayList<>();
		AjaxResult object = ammeterorprotocolService.getUserByUserRole();
		Map<String, Object> map = (Map<String, Object>)object.get("data");
		boolean isProAdmin = (boolean)map.get("isProAdmin");
		if(!isProAdmin ){
			companies = (List<IdNameVO>)map.get("companies");
		}
		startPage();
        List<Map<String,Object>> list = powerIntelligentInf2Service.selectByList(powerIntelligentInf2,ammeterCode,companies);
		return getDataTable(list);
	}
	
	/**
	 * 新增PUE管控
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存PUE管控
	 */
//	@RequiresPermissions("business:PUECountrols:add")
	//@Log(title = "PUE管控", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody PowerIntelligentInf2 powerIntelligentInf2)
	{		
		return toAjax(powerIntelligentInf2Service.insert(powerIntelligentInf2));
	}

	/**
	 * 修改PUE管控
	 */
	@GetMapping("/edit")
	public AjaxResult edit(Long pinId)
	{
		PowerIntelligentInf2 powerIntelligentInf2 = powerIntelligentInf2Service.get(pinId);

		Object object = JSONObject.toJSON(powerIntelligentInf2);

        return this.success(object);
	}
	
	/**
	 * 修改保存PUE管控
	 */
//	@RequiresPermissions("business:PUECountrols:edit")
	//@Log(title = "PUE管控", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody Powerintelligentinf2Dto powerintelligentinf2Dto)
	{		
		return toAjax(powerIntelligentInf2Service.updateData(powerintelligentinf2Dto));
	}
	/**
	 * 通过电表id和时间获取数据
	 */
	@GetMapping("/selectAcountByDateAmmeter")
	public AjaxResult selectAcountByDateAmmeter(Long ammeterid,Integer type)
	{
		Map<String,Object> result = powerIntelligentInf2Service.selectAcountByDateAmmeter(ammeterid,type);
		Object object = JSONObject.toJSON(result);
		return this.success(object);
	}
	
	/**
	 * 删除PUE管控
	 */
//	@RequiresPermissions("business:PUECountrols:remove")
	//@Log(title = "PUE管控", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(powerIntelligentInf2Service.deletePUECountrols(ids));
	}


    /**
     * 查看PUE管控
     */
//    @RequiresPermissions("business:PUECountrols:view")
    @GetMapping("/view/{pinId}")
    @ResponseBody
    public AjaxResult view(@PathVariable("pinId") Long pinId)
    {
		PowerIntelligentInf2 powerIntelligentInf2 = powerIntelligentInf2Service.get(pinId);

        Object object = JSONObject.toJSON(powerIntelligentInf2);

        return this.success(object);
    }

}
