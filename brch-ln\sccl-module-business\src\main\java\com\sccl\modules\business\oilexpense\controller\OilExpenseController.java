package com.sccl.modules.business.oilexpense.controller;

import com.alibaba.fastjson.JSONObject;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.autojob.util.bean.ObjectUtil;
import com.sccl.modules.business.energyaccount.domain.EnergyAccountVo;
import com.sccl.modules.business.energyaccount.service.IEnergyAccountService;
import com.sccl.modules.business.oilexpense.domain.OilExpense;
import com.sccl.modules.business.oilexpense.domain.OilExpenseRecord;
import com.sccl.modules.business.oilexpense.domain.OilExpenseVo;
import com.sccl.modules.business.oilexpense.mapper.OilExpenseMapper;
import com.sccl.modules.business.oilexpense.service.IOilExpenseService;
import com.sccl.modules.mssaccount.mssinterface.domain.BillExecuteState;
import com.sccl.modules.system.user.domain.User;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 油机基础 信息操作处理
 *
 * <AUTHOR>
 * @date 2021-12-14
 */
@RestController
@RequestMapping("/business/oilExpense")
public class OilExpenseController extends BaseController {

    private final String prefix = "business/oilExpense";

    @Autowired
    private IOilExpenseService oilExpenseService;

    @Autowired
    private IEnergyAccountService energyAccountService;

    @Resource
    OilExpenseMapper oilExpenseMapper;

    @RequiresPermissions("business:oilExpense:view")
    @GetMapping()
    public String oilExpense() {
        return prefix + "/oilExpense";
    }

    /**
     * 查询油机基础列表
     */
//    @RequiresPermissions("business:oilExpense:list")
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(OilExpense oilExpense) {
        if (oilExpense.getId() == null) {
            Boolean a = false;
            if (ObjectUtil.isNull(oilExpense.getCompany())) {
                oilExpense.setCompany(-1L);
            }
            if (ObjectUtil.isNull(oilExpense.getCountry())) {
                oilExpense.setCountry(-1L);
            }
            String company = oilExpense.getCompany().toString();
            String country = oilExpense.getCountry().toString();
            User currentUser = getCurrentUser();
            if (currentUser.getId() != -1) { //不为管理员
            List<IdNameVO> departments = currentUser.getDepartments();
            for (IdNameVO department : departments) {
                if (department.getId() .equals(country) ) {
                    a = true;
                }
            }
            List<IdNameVO> companies = currentUser.getCompanies();
            for (IdNameVO idNameVO : companies) {
                if (idNameVO.getId().equals(company)) {
                    a = true;
                }
            }
            }
            if (!a) {
                return null;
            }
            if (oilExpense.getCompany() == null) {
                oilExpense.setCompany(Long.valueOf(company));
            }
            if (oilExpense.getCountry() == null || oilExpense.getCountry()==0L) {
                oilExpense.setCountry(Long.valueOf(country));
            }
            if (oilExpense.getCompany() == -1) {
                oilExpense.setCompany(null);
            }
            if (oilExpense.getCountry() == -1) {
                oilExpense.setCountry(null);
            }
        }
        startPage();
        List<OilExpense> list = oilExpenseService.selectList(oilExpense);
        return getDataTable(list);
    }


    @RequestMapping("/listAll")
    @ResponseBody
    public TableDataInfo listAll(OilExpense oilExpense) {
        if (oilExpense.getId() == null) {
        User currentUser = getCurrentUser();
        String country = currentUser.getDepartments().get(0).getId();
        String company = currentUser.getCompanies().get(0).getId();
        if (oilExpense.getCompany() == null) {
            oilExpense.setCompany(Long.valueOf(company));
        }
        if (oilExpense.getCountry() == null || oilExpense.getCountry()==0L) {
            oilExpense.setCountry(Long.valueOf(country));
        }
        if (oilExpense.getCompany() == -1) {
            oilExpense.setCompany(null);
        }
        if (oilExpense.getCountry() == -1) {
            oilExpense.setCountry(null);
        }
        }
        startPage();
        List<OilExpenseVo> list = oilExpenseService.selectListAll(oilExpense);
        return getDataTable(list);
    }


    /**
     * 季度稽核油机统计 - con
     */
//    @RequiresPermissions("business:oilExpense:list")
    @RequestMapping("/listAudit")
    @ResponseBody
    public TableDataInfo listAudit(OilExpenseVo oilExpense) {
        User currentUser = getCurrentUser();
        if (oilExpense.getId() == null) {
        String country = currentUser.getDepartments().get(0).getId();
        String company = currentUser.getCompanies().get(0).getId();
        if (oilExpense.getCompany() == null) {
            oilExpense.setCompany(Long.valueOf(company));
        }
        if (oilExpense.getCountry() == null || oilExpense.getCountry()==0L) {
            oilExpense.setCountry(Long.valueOf(country));
        }
        if (oilExpense.getCompany() == -1) {
            oilExpense.setCompany(null);
        }
        if (oilExpense.getCountry() == -1) {
            oilExpense.setCountry(null);
        }
        }
        startPage();
       // List<OilExpenseVo> list = oilExpenseService.selectListVo(oilExpense);
        List<OilExpenseVo> list1 = oilExpenseService.selectListAuditVo(oilExpense);
        List<OilExpenseVo> list2 = oilExpenseService.selectListAuditVo4(oilExpense); //应急中心
        boolean b = list1.addAll(list2);
        //通过流将当前操作人插入
        list1 = list1.stream().peek(oil -> oil.setSummaruser(currentUser.getUserName())).collect(Collectors.toList());
        return getDataTable(list1);
    }

    /**
     * 汇总单查询油机统计
     * <AUTHOR>
     * @date 2022/4/7
     */
    @RequestMapping("/listHavedAudit")
    @ResponseBody
    public TableDataInfo listHavedAudit(OilExpenseVo oilExpense) {
        startPage();
        List<OilExpenseVo> list1 = oilExpenseService.selectListHavedAuditVo(oilExpense);
        //通过流将当前操作人插入
        return getDataTable(list1);
    }

    /**
     * 汇总单查询台账 - con
     * <AUTHOR>
     * @date 2022/4/7
     */
    @RequestMapping("/listAccountDetail")
    @ResponseBody
    public TableDataInfo listAccountDetail(OilExpenseVo oilExpense) {
        startPage();
        List<EnergyAccountVo> list1 = oilExpenseService.listAccountDetail(oilExpense);
        //通过流将当前操作人插入
        return getDataTable(list1);
    }

    @RequestMapping("/viewList")
    @ResponseBody
    public TableDataInfo viewList(Long id) {
        startPage();
        OilExpense oilExpense = new OilExpense();
        oilExpense.setId(id);
        List<OilExpense> list = oilExpenseService.selectListView(oilExpense);
        return getDataTable(list);
    }
    @RequestMapping("/viewListOld")
    @ResponseBody
    public TableDataInfo viewListOld(Long id) {
        startPage();
        OilExpenseRecord oilExpense = new OilExpenseRecord();
        oilExpense.setId(id);
        List<OilExpenseRecord> list = oilExpenseService.selectListViewOld(oilExpense);
        return getDataTable(list);
    }

    /**
     * 新增油机基础
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存油机基础/应急中心基础
     */
//    @RequiresPermissions("business:oilExpense:add")
    @Log(title = "油机基础", action = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult addSave(OilExpense oilExpense) {
        try {
            if (oilExpense.getId() == null&&oilExpense.getBillStatus()!=3) {
                if (oilExpense.getQuality() == 2) {
                OilExpense oilExpense1 = oilExpenseMapper.selectByCarPlate(oilExpense.getCarplate());
                if (oilExpense1 != null) {
                    return AjaxResult.error("车牌号重复，请重新输入");
                }
            }
            oilExpense.setStatus(1);// 在用
             //*****新增油机
                OilExpense oilExpense1 = oilExpenseMapper.selectByOilEngineId(oilExpense.getOilEngineId());
                if (oilExpense1 != null) {
                    return AjaxResult.error("油机编号重复，请重新输入");
                }
            oilExpense.setCreatetime(new Date());
           oilExpenseMapper.insert(oilExpense);
            //oilExpenseService.insert(oilExpense);// 先写入 获得id  前端选择局站则关联，没选择则不关联
            if (oilExpense.getStationCode() != null && !oilExpense.getStationCode().equals("")) {
                String[] split = oilExpense.getStationCode().split(",");
                for (String stationId : split) {
                    oilExpenseMapper.saveMachineAndStation(Long.valueOf(stationId), oilExpense.getId());
                }
            }
            }
            else{ //*****修改油机
                /** 1.将修改的油机新数据插入新表
                 *  2.修改单据状态为3修改中
                 *
                 * */
                OilExpenseRecord oilExpenseRecord = new OilExpenseRecord();
                BeanUtils.copyProperties(oilExpense, oilExpenseRecord);
                oilExpenseRecord.setId(null);
                oilExpenseRecord.setOilid(oilExpense.getId());
                oilExpenseRecord.setUpdatetime(new Date());
                oilExpenseRecord.setDelFlag("0");
                oilExpenseMapper.delNewOilExpense(oilExpense.getId());
                oilExpenseMapper.insertRecord(oilExpenseRecord);
                OilExpense oilExpense1 = new OilExpense();
                oilExpense1.setId(oilExpense.getId());
                oilExpense1.setBillStatus(oilExpense.getBillStatus());
                oilExpenseMapper.updateForModel(oilExpense1);
            }

        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("操作失败");
        }
        return AjaxResult.success("操作成功");
    }

    /**
     * 修改油机基础
     */
    @GetMapping("/edit/{id}")
    public AjaxResult edit(@PathVariable("id") Long id) {
        OilExpense oilExpense = oilExpenseService.get(id);
        Object object = JSONObject.toJSON(oilExpense);
        return this.success(object);
    }

    /**
     * 修改保存油机基础
     */
//    @RequiresPermissions("business:oilExpense:edit")
    @Log(title = "油机基础", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult editSave(@RequestBody OilExpense oilExpense) {
        if (oilExpense != null) {
            oilExpense.setBillStatus(3);
        }
         return oilExpenseService.editSave(oilExpense);
    }

    /**
     * 删除油机基础
     */
//    @RequiresPermissions("business:oilExpense:remove")
    @Log(title = "油机基础", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return oilExpenseService.removeOilExpense(ids);
    }


    /**
     * 查看油机基础
     */
//    @RequiresPermissions("business:oilExpense:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id) {
        OilExpense oilExpense = oilExpenseService.get(id);
        Object object = JSONObject.toJSON(oilExpense);
        return this.success(object);
    }

    // 查询油机、水表、气表 关联局站 参数 油机ID
    @PostMapping("/queryMachineAndStation")
    public TableDataInfo queryMachineAndStation(Long machineId, Integer type) {
        List<String> list = oilExpenseMapper.queryMachineAndStation(machineId, type);
        return getDataTable(list);
    }

    /**
     * 获取当前用户所在公司
     */
    //@RequiresPermissions("business:ammeterorprotocol:view")
    @GetMapping("/getUserByUserRole")
    @ResponseBody
    public AjaxResult getUserByUserRole() {
        return oilExpenseService.getUserByUserRole();
    }

    /**
     * 根据节点CODE和用户权限查询下属机构
     * @param orgCode 当前节点的code
     * @return 当前节点下的所有组织（只含一级）
     */
    @RequestMapping("/selectSubordinateOrgByRole")
    @ResponseBody
    public List<Map<String, Object>> selectSubordinateOrgByRole(@RequestParam String orgCode, String type){
        return oilExpenseService.selectSubordinateOrgByRole(orgCode,type);
    }
}
