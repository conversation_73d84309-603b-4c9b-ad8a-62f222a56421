package com.sccl.modules.business.cost.vo;

import com.sccl.framework.aspectj.lang.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

/**
 * 局站业务电量查询 一览 结果列表
 */
@Data
public class StationElectricResultVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 市局编码
     */
    @Excel(name = "市局编码")
    private String cityCode;

    /**
     * 市局名称
     */
    @Excel(name = "市局名称")
    private String cityName;

    /**
     * 区县编码
     */
    @Excel(name = "区县编码")
    private String countyCode;

    /**
     * 区县名称
     */
    @Excel(name = "区县名称")
    private String countryName;

    /**
     * 局站编码
     */
    @Excel(name = "局站编码")
    private String stationCode;

    /**
     * 资源编码
     */
    private String pueCode;

    /**
     * 局站名称
     */
    @Excel(name = "局站名称")
    private String station;

    /**
     * 5gr站址编码
     */
    private String stationcode5gr;

    /**
     * 数据来源
     */
    private String source;
    private String sourceName;

    /**
     * 月份【yyyy年MM月】
     */
    @Excel(name = "月份")
    private String yf;

    /**
     * 月份【yyyyMM】
     */
    private String tjyf;

    /**
     * 月总电量
     */
    @Excel(name = "月总电量")
    private String ywdl;

    /**
     * 日均电量
     */
    @Excel(name = "日均电量")
    private String rjdl;
}
