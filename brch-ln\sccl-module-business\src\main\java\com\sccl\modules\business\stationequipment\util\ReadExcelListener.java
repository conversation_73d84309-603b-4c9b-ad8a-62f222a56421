package com.sccl.modules.business.stationequipment.util;


import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.CellData;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

public class ReadExcelListener extends AnalysisEventListener<Map<Integer, Object>> {
    private ArrayList<Map<Integer, Object>> list = new ArrayList<>();
    private ArrayList<Map<String, Object>> data = new ArrayList<>();
    private String json;
    private Map<Integer, CellData> head;

    //统计读取时间和数量
    private int count = 0;
    private int add = 0;
    private long start;
    private long past;


    public ReadExcelListener() {

    }

    public ArrayList<Map<String, Object>> getData() {
        return data;
    }

    public void setData(ArrayList<Map<String, Object>> data) {
        this.data = data;
    }

    public String getJson() {
        return json;
    }

    public void setJson(String json) {
        this.json = json;
    }

    public long getStart() {
        return start;
    }

    public void setStart(long start) {
        this.start = start;
    }

    public Map<Integer, CellData> getHead() {
        return head;
    }

    public void setHead(Map<Integer, CellData> head) {
        this.head = head;
    }

    public ArrayList<Map<Integer, Object>> getList() {
        return list;
    }

    public void setList(ArrayList<Map<Integer, Object>> list) {
        this.list = list;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    @Override
    public void invoke(Map<Integer, Object> integerObjectMap, AnalysisContext analysisContext) {
        list.add(integerObjectMap);
        add++;
        count++;
        if (add == 10000) {
            long time = System.currentTimeMillis();
            long t = (time - start) / 1000;
            add = 0;
            System.out.println("读取" + count + "条数据用时：" + t + "S,近10000条读取时间增量：" + (t - past));
            //limit=limit>min? (int)(t-past)^2+limit:min;
            past = t;
        }
        /*if (list.size() == limit) {
            getValueOfJSON(list);
            list.clear();
        }*/
    }

    @Override
    public void invokeHead(Map<Integer, CellData> headMap, AnalysisContext context) {
        super.invokeHead(headMap, context);
        System.out.println(headMap);
        this.head = headMap;
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        getValue(list);
    }


    public void getValue(ArrayList<Map<Integer, Object>> list) {
        if (head != null && list != null) {
            long st = System.currentTimeMillis();
            Map<String, Object> map2 = null;
            for (int i = 0; i < list.size(); i++) {
                map2 = new HashMap<>();
                for (int j = 0; j < this.head.size(); j++) {

                    map2.put(head.get(j).toString(), list.get(i).get(j));
                }
                this.data.add(map2);
            }

            long e = System.currentTimeMillis();
            System.out.println("此次录入信息：" + list.size() + "条，总共用时：" + ((e - st) / 1000) + "S");
        }
    }

    public String getRowsJson(int start, int limit, ArrayList<Map<String, Object>> data, String only) {
//        ArrayList<Map<String,Object>> list1=new ArrayList<>();
//        Map<String,Object> map=new HashMap<>();
//        long st=0;
//
//        if(data!=null&&data.size()>0){
//            this.data=data;
//        }
//
//        if(start>this.data.size()){
//            map.put("max",this.data.size());
//            map.put("len",0);
//            map.put("data",list1);
//            return jsonUtil.getMapJson(map);
//        }
//        if(start==0&&limit==0){
//            limit=this.data.size();
//        }
//
//        int max=start+limit>this.data.size()? this.data.size():start+limit;
//        for(int i=start;i<max;i++){
//            list1.add(this.data.get(i));
//        }
//
//        st=System.currentTimeMillis();
//        System.out.println("要转化的JSON信息："+list1.size()+"条");
//        if(!only.equalsIgnoreCase("true")) {
//            map.put("max", this.data.size());
//            map.put("len", list1.size());
//            map.put("data", list1);
//            this.json=jsonUtil.LargeMapToJson(map);
//        }
//        else{
//            this.json=jsonUtil.LargeListToJson(list1);
//        }
//
//        long e2=System.currentTimeMillis();
//        System.out.println("此次转换到JSON信息："+list1.size()+"条，总共用时："+((e2-st)/1000)+"S");
//        return this.json;
        return null;
    }
}

