package com.sccl.modules.business.stationinfo.controller;

import java.util.List;

import com.sccl.modules.business.stationinfo.domain.StationJt5gjz;
import com.sccl.modules.business.stationinfo.service.IStationJt5gjzService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 集团LTE网管基站 信息操作处理
 * 
 * <AUTHOR>
 * @date 2021-05-16
 */
@RestController
@RequestMapping("/ammeterorprotocol/stationJt5gjz")
public class StationJt5gjzController extends BaseController
{
    private String prefix = "ammeterorprotocol/stationJt5gjz";
	
	@Autowired
	private IStationJt5gjzService stationJt5gjzService;
	
	@RequiresPermissions("ammeterorprotocol:stationJt5gjz:view")
	@GetMapping()
	public String stationJt5gjz()
	{
	    return prefix + "/stationJt5gjz";
	}
	
	/**
	 * 查询集团LTE网管基站列表
	 */
	@RequiresPermissions("ammeterorprotocol:stationJt5gjz:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(StationJt5gjz stationJt5gjz)
	{
		startPage();
        List<StationJt5gjz> list = stationJt5gjzService.selectList(stationJt5gjz);
		return getDataTable(list);
	}

	@RequestMapping("/getList")
	@ResponseBody
	public TableDataInfo getList(StationJt5gjz stationJt5gjz)
	{
		startPage();
		List<StationJt5gjz> list = stationJt5gjzService.selectListLike(stationJt5gjz);
		return getDataTable(list);
	}

	/**
	 * 新增集团LTE网管基站
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存集团LTE网管基站
	 */
	@RequiresPermissions("ammeterorprotocol:stationJt5gjz:add")
	@Log(title = "集团LTE网管基站", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody StationJt5gjz stationJt5gjz)
	{		
		return toAjax(stationJt5gjzService.insert(stationJt5gjz));
	}

	/**
	 * 修改集团LTE网管基站
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		StationJt5gjz stationJt5gjz = stationJt5gjzService.get(id);

		Object object = JSONObject.toJSON(stationJt5gjz);

        return this.success(object);
	}
	
	/**
	 * 修改保存集团LTE网管基站
	 */
	@RequiresPermissions("ammeterorprotocol:stationJt5gjz:edit")
	@Log(title = "集团LTE网管基站", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody StationJt5gjz stationJt5gjz)
	{		
		return toAjax(stationJt5gjzService.update(stationJt5gjz));
	}
	
	/**
	 * 删除集团LTE网管基站
	 */
	@RequiresPermissions("ammeterorprotocol:stationJt5gjz:remove")
	@Log(title = "集团LTE网管基站", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(stationJt5gjzService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看集团LTE网管基站
     */
    @RequiresPermissions("ammeterorprotocol:stationJt5gjz:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		StationJt5gjz stationJt5gjz = stationJt5gjzService.get(id);

        Object object = JSONObject.toJSON(stationJt5gjz);

        return this.success(object);
    }

}
