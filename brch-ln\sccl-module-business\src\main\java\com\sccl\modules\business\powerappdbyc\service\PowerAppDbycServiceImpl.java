package com.sccl.modules.business.powerappdbyc.service;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.business.account.mapper.AccountMapper;
import com.sccl.modules.business.powerappdbyc.mapper.PowerAppDbycMapper;
import com.sccl.modules.business.toweraccount.domain.TowerData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sccl.modules.business.powerappdbyc.domain.PowerAppDbyc;

import java.util.List;


/**
 * 大数据基站异常 服务层实现
 * 
 * <AUTHOR>
 * @date 2022-03-20
 */
@Service
public class PowerAppDbycServiceImpl extends BaseServiceImpl<PowerAppDbyc> implements IPowerAppDbycService
{
    @Autowired
    public PowerAppDbycMapper powerAppDbycMapper;
    @Override
    public List<PowerAppDbyc> selectByList(PowerAppDbyc powerAppDbyc) {
        return powerAppDbycMapper.selectByList(powerAppDbyc);
    }
}
