package com.sccl.modules.business.quota.controller;

import com.alibaba.fastjson.JSONObject;
import com.sccl.common.support.Convert;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol;
import com.sccl.modules.business.ammeterorprotocol.service.IAmmeterorprotocolService;
import com.sccl.modules.business.quota.domain.Quota;
import com.sccl.modules.business.quota.domain.QuotaBaseResult;
import com.sccl.modules.business.quota.domain.QuotaCondition;
import com.sccl.modules.business.quota.domain.QuotaRecord;
import com.sccl.modules.business.quota.service.IQuotaRecordService;
import com.sccl.modules.business.quota.service.IQuotaService;
import com.sccl.modules.system.user.domain.User;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 定额管理
 * 
 * <AUTHOR>
 * @date 2019-05-13
 */
@RestController
@RequestMapping("/business/quotaRecord")
public class QuotaRecordController extends BaseController
{
    private String prefix = "business/quota";

	@Autowired
	private IQuotaRecordService quotaRecordService;
	
//	@RequiresPermissions("business:quota:view")
	@GetMapping()
	public String quota()
	{
	    return prefix + "/quota";
	}

//	/**
//	 * 修改定额
//	 */
//	@GetMapping("/edit")
//	public AjaxResult edit(QuotaRecord quotaRecord)
//	{
//		User user = ShiroUtils.getUser();
//		QuotaRecord quota = quotaRecordService.getByQuotaId(quotaRecord);
//		if(null != quota && !"2".equals(quota.getBillStatus().toString())){
//			if(null == quotaRecord.getCreatorId()) {
//				if (null != user) {
//					quotaRecord.setCreatorId(user.getId());
//				}
//			}
//			quota = quotaRecordService.getByQuotaId(quotaRecord);
//		}
//
//		Object object = JSONObject.toJSON(quota);
//
//        return this.success(object);
//	}
	/**
	 * 修改定额
	 */
	@GetMapping("/edit")
	public AjaxResult edit(@RequestParam(required = false) Long id,Long creatorId)
	{
		List<QuotaRecord> quota = new ArrayList<>();
		QuotaRecord searchValue = new QuotaRecord();
		searchValue.setQuotaId(id);
		if(null != creatorId){//审核查询 根据审核人查询最新一条数据
			searchValue.setCreatorId(creatorId);
			quota = quotaRecordService.getByQuotaId(searchValue);
		}else {//编辑 查询最新一条数据
			quota = quotaRecordService.getByQuotaId(searchValue);
			User user = ShiroUtils.getUser();
			if( null != quota && quota.size()!=0 && !"2".equals(quota.get(0).getBillStatus().toString()) && !quota.get(0).getCreatorId().toString().equals(user.getId().toString())){
				//最新数据不是有效数据并且不是自己的数据，根据用户查询上一有效数据时间后面的
				searchValue.setBillStatus(2);
				if (null != user) {
					searchValue.setCreatorId(user.getId());
				}
				quota = quotaRecordService.getByQuotaDateId(searchValue);
				if(null == quota){//如果为null 查询有效数据
					searchValue.setCreatorId(null);
					quota = quotaRecordService.getByQuotaId(searchValue);
				}
			}
		}
		Object object =null;
		if(null != quota && quota.size()!=0){
			object = JSONObject.toJSON(quota.get(0));
		}
		return this.success(object);
	}
}
