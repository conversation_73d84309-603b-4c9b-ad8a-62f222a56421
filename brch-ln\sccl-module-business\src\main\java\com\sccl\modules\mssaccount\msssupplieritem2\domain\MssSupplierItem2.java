package com.sccl.modules.mssaccount.msssupplieritem2.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;
import java.math.BigDecimal;


/**
 * 供应商item表 MSS_SUPPLIER_ITEM2
 * 
 * <AUTHOR>
 * @date 2019-05-01
 */
public class MssSupplierItem2 extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
	private Long iId;
    public Long getIId() {
		return iId;
	}

	public void setIId(Long iId) {
		this.iId = iId;
	}

	/** 主表id */
    private BigDecimal pId;
    /** 银行类别 */
    private String bvtyp;
    /** 银行名称 */
    private String banka;
    /** 银行国家代码 */
    private String banks;
    /** 先根据名称及联行号检查系统中是否已经有创建的银行，如果有就获取银行代码 */
    private String bankl;
    /** 银行帐户号码 */
    private String bankn;
    /** 帐户持有人姓名 */
    private String koinh;
    /** 联行号 联行号就是一个地区银行的唯一识别标志，由12位组成：3位银行代码+4位城市代码+4位银行编号+1位校验位。 */
    private String brnch;
    /** 开户省 根据接口传输的银行代码来获取表BNKA里已经维护的数据，如果接口与表中的值不一致，不更新BNKA表，以BNKA表里的值为准。 */
    private String provz;
    /** 开户市  根据接口传输的银行代码来获取表BNKA里已经维护的数据，如果接口与表中的值不一致，不更新BNKA表，以BNKA表里的值为准 */
    private String ort01;
    /** 对公对私标示 */
    private String zpubPri;
    /** 创建省份 */
    private String zprovz1;
    /** 序号 */
    private String zNo;
    /** 供应商编码 */
    private String lifnr;
    /**  */
    private Date infDate;
    /**  */
    private String infStatus;

	public void setPId(BigDecimal pId)
	{
		this.pId = pId;
	}

	public BigDecimal getPId() 
	{
		return pId;
	}


	public void setBvtyp(String bvtyp)
	{
		this.bvtyp = bvtyp;
	}

	public String getBvtyp() 
	{
		return bvtyp;
	}

	public void setBanka(String banka)
	{
		this.banka = banka;
	}

	public String getBanka() 
	{
		return banka;
	}

	public void setBanks(String banks)
	{
		this.banks = banks;
	}

	public String getBanks() 
	{
		return banks;
	}

	public void setBankl(String bankl)
	{
		this.bankl = bankl;
	}

	public String getBankl() 
	{
		return bankl;
	}

	public void setBankn(String bankn)
	{
		this.bankn = bankn;
	}

	public String getBankn() 
	{
		return bankn;
	}

	public void setKoinh(String koinh)
	{
		this.koinh = koinh;
	}

	public String getKoinh() 
	{
		return koinh;
	}

	public void setBrnch(String brnch)
	{
		this.brnch = brnch;
	}

	public String getBrnch() 
	{
		return brnch;
	}

	public void setProvz(String provz)
	{
		this.provz = provz;
	}

	public String getProvz() 
	{
		return provz;
	}

	public void setOrt01(String ort01)
	{
		this.ort01 = ort01;
	}

	public String getOrt01() 
	{
		return ort01;
	}

	public void setZpubPri(String zpubPri)
	{
		this.zpubPri = zpubPri;
	}

	public String getZpubPri() 
	{
		return zpubPri;
	}

	public void setZprovz1(String zprovz1)
	{
		this.zprovz1 = zprovz1;
	}

	public String getZprovz1() 
	{
		return zprovz1;
	}

	public void setZNo(String zNo)
	{
		this.zNo = zNo;
	}

	public String getZNo() 
	{
		return zNo;
	}

	public void setLifnr(String lifnr)
	{
		this.lifnr = lifnr;
	}

	public String getLifnr() 
	{
		return lifnr;
	}

	public void setInfDate(Date infDate)
	{
		this.infDate = infDate;
	}

	public Date getInfDate() 
	{
		return infDate;
	}

	public void setInfStatus(String infStatus)
	{
		this.infStatus = infStatus;
	}

	public String getInfStatus() 
	{
		return infStatus;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("pId", getPId())
            .append("iId", getIId())
            .append("bvtyp", getBvtyp())
            .append("banka", getBanka())
            .append("banks", getBanks())
            .append("bankl", getBankl())
            .append("bankn", getBankn())
            .append("koinh", getKoinh())
            .append("brnch", getBrnch())
            .append("provz", getProvz())
            .append("ort01", getOrt01())
            .append("zpubPri", getZpubPri())
            .append("zprovz1", getZprovz1())
            .append("zNo", getZNo())
            .append("lifnr", getLifnr())
            .append("infDate", getInfDate())
            .append("infStatus", getInfStatus())
            .toString();
    }
	private BigDecimal sum;//获取前台数据
	public BigDecimal getSum() {
		return sum;
	}

	public void setSum(BigDecimal sum) {
		this.sum = sum;
	}
	
}
