package com.sccl.modules.business.examine.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 转供电考核 查询条件对象
 */
@Data
public class TransferExamineSearchVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 所属分公司
     */
    private String company;

    /**
     * 年份
     */
    private String year;

    /**
     * 开始账期【yyyy-MM】
     */
    private String startDate;

    /**
     * 结束账期【yyyy-MM】
     */
    private String endDate;
}
