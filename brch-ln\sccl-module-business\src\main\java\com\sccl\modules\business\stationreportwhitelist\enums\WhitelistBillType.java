package com.sccl.modules.business.stationreportwhitelist.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/4/22 16:32
 * @describe 流程类型
 */
@Getter
@AllArgsConstructor
public enum WhitelistBillType {
    // 新增，修改
    ADD(1, "ADD_WHITELIST"),
    MODIFY(3, "MODIFY_WHITELIST"),
    MODIFY_PROCESSING(4, "MODIFY_WHITELIST"),
    ;

    /**
     * 状态码
     */
    private final Integer code;

     /**
     * 状态名称
     */
    private final String name;

    /**
     * 根据code获取name
     */
    public static String getNameByCode(Integer code) {
        for (WhitelistBillType billStatus : WhitelistBillType.values()) {
            if (billStatus.getCode().equals(code)) {
                return billStatus.getName();
            }
        }
        return null;
    }
}
