package com.sccl.modules.oss.collect;

import com.enrising.dcarbon.bean.SpringUtil;
import com.enrising.dcarbon.collector.AbstractCollectorTemplate;
import com.enrising.dcarbon.observer.EventListener;
import com.sccl.modules.oss.entity.OssMsgEntity;
import com.sccl.modules.oss.mapper.OssMsgMapper;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-06-29 15:45
 * @email <EMAIL>
 */
@Slf4j
public class OssEntityRouteEventListener implements EventListener<OssEntityRouteEvent> {
    @Override
    public void onEvent(OssEntityRouteEvent event) {
        List<OssMsgEntity> entityList = AbstractCollectorTemplate.convert(event.getSource(), OssMsgEntity.class);
        Optional<OssMsgMapper> mapper = SpringUtil.getBeanOptional(OssMsgMapper.class);
        mapper.ifPresent(ossMsgMapper -> {
            log.info("成功保存OSS信息{}条", ossMsgMapper.insertList(entityList));
        });
    }
}
