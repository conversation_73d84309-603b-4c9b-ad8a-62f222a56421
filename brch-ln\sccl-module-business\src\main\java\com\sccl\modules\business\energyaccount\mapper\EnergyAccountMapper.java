package com.sccl.modules.business.energyaccount.mapper;

import com.sccl.modules.business.eneregyaccountpoolpre.domain.EneregyAccountpoolpreDto;
import com.sccl.modules.business.energyaccount.domain.EnergyAccount;
import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.energyaccount.domain.EnergyAccountVo;
import com.sccl.modules.business.energyaccount.domain.RefuelDetail;
import com.sccl.modules.business.oilexpense.domain.Audit;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 油水气出入台账 数据层
 *
 * <AUTHOR>
 * @date 2021-12-15
 */
public interface EnergyAccountMapper extends BaseMapper<EnergyAccount> {

    /** 查询该油机是否被使用 */
    int selectUsedByOilEngineId(String id);

    void saveOilAccount(EnergyAccount energyAccount);

    EnergyAccount querySumAccount(EnergyAccount energyAccount);

    EnergyAccount queryCurflatreadings(@Param("pcid") Long pcid);

    EnergyAccount selectAllByPcid(EnergyAccount energyAccount);

    // 查询最新一次台账
    EnergyAccount queryLastAccount(EnergyAccount energyAccount);

    //根据购油台账id查询入库台账
    List<EnergyAccount> selectAccountByOilAccount(Long id);

    //插入加油台账细节
    void insertRefuekDetail(RefuelDetail refuelDteail);

    int deleteRefuelDetailbyIds(String[] array);

    //根据id查询加油台账细节
    List<RefuelDetail> selectDetailbyid(String id);

    //根据油机id查询该油机所有加油台账明细
    List<RefuelDetail> selectDetailbyOilId(Long cardid);

    //根据台账id查询当前加油台账是否被使用
    List<RefuelDetail> selectDetailUsed(String id);

    //根据cardid购油台账id查询台账明细
    List<RefuelDetail> selectDetailbyCardId(String id);

    BigDecimal selectListSum(Audit audit);

    /** 查询汇总单明细 */
    List<EnergyAccount> selectDetailAudit(EneregyAccountpoolpreDto dto);

    /** 查询油卡是否被应急中心使用 */
    int selectAccountByCardid(String id);

    List<EnergyAccount> listAccount(EnergyAccount account);

    int getPcid(EnergyAccount energyAccount);
}
