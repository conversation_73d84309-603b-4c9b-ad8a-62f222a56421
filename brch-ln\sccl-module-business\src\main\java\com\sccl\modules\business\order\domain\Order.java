package com.sccl.modules.business.order.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.sccl.framework.web.domain.BaseEntity;


/**
 * 订单表 t_order
 * 
 * <AUTHOR>
 * @date 2019-03-05
 */
public class Order extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
    /** 订单名称 */
    private String orderName;
    /** 订单编码 */
    private String orderCode;
    /** 供应商 */
    private String providerName;


	public void setOrderName(String orderName)
	{
		this.orderName = orderName;
	}

	public String getOrderName() 
	{
		return orderName;
	}

	public void setOrderCode(String orderCode)
	{
		this.orderCode = orderCode;
	}

	public String getOrderCode() 
	{
		return orderCode;
	}

	public void setProviderName(String providerName)
	{
		this.providerName = providerName;
	}

	public String getProviderName() 
	{
		return providerName;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderName", getOrderName())
            .append("orderCode", getOrderCode())
            .append("remark", getRemark())
            .append("creatorId", getCreatorId())
            .append("creatorName", getCreatorName())
            .append("updateTime", getUpdateTime())
            .append("createTime", getCreateTime())
            .append("providerName", getProviderName())
            .toString();
    }
}
