package com.sccl.modules.oss.collect;

import com.enrising.dcarbon.collector.CollectedDatasource;
import com.enrising.dcarbon.collector.Collector;
import com.enrising.dcarbon.date.DateUtils;
import com.enrising.dcarbon.id.IdGenerator;
import com.enrising.dcarbon.redis.RedisUtil;
import com.enrising.dcarbon.string.StringUtils;
import com.sccl.common.utils.ObjectStoreUtils;
import com.sccl.modules.oss.entity.OssMsgEntity;
import com.unnet.oss.entity.Bucket;
import com.unnet.oss.entity.OssObject;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-06-29 13:56
 * @email <EMAIL>
 */
@Slf4j
public class OssMsgCollector implements Collector {
    public static final String COLLECTED_PAGE_KEY_PREFIX = "oss_collected:";
    public static final int SIZE = 100;
    private final Map<String, Integer> currentPages = new HashMap<>();

    @Override
    public List<CollectedDatasource> collect() {
        log.info("oss初始页码0");
        int pageNum = 0;
        log.info("遍历获取oss存储桶");
        List<Bucket> buckets = ObjectStoreUtils.listBuckets();

        for (Bucket bucket : buckets) {
            if (currentPages.size() == 0 || !currentPages.containsKey(bucket.getBucket())) {
                String page = RedisUtil.getStr(COLLECTED_PAGE_KEY_PREFIX + bucket.getBucket());
                log.info("redis 桶页码{}", page);
                if (!StringUtils.isEmpty(page)) {
                    pageNum = Integer.parseInt(page) + 1;
                }
            } else {
                pageNum = currentPages.get(bucket.getBucket()) + 1;
            }
            log.info("当前桶pageNum{}", pageNum);
            long used = bucket.getUsed_objects();
            int realTotalPageCount = (int) Math.ceil((used * 1.0) / SIZE);
            log.info("实际桶realTotalPageCount {}", realTotalPageCount);
            if (pageNum > realTotalPageCount) {
                log.info("当前桶redis页码 超过 实际桶页码，不继续发送请求");
                continue;
            }
            log.info("当前请求OSS参数：页号：{}，页大小：{}，总计页数：{}", pageNum, SIZE, realTotalPageCount);
            List<OssObject> ossObjects = ObjectStoreUtils.listObjects(bucket.getBucket(), null, (pageNum - 1) * SIZE, SIZE);
            if (ossObjects.size() == 0) {
                continue;
            }
            log.info("请求到OSS信息{}条", ossObjects.size());
            currentPages.put(bucket.getBucket(), pageNum);
            return ossObjects
                    .stream()
                    .map(item -> {
                        OssMsgEntity entity = new OssMsgEntity();
                        entity.setDelFlag(0);
                        entity.setId(IdGenerator.getNextIdAsLong());
                        entity.setCreateTime(new Date());
                        entity.setUpdateTime(new Date());
                        entity.setOssFileName(item.getPath());
                        entity.setOssId(item.getId());
                        entity.setBucketName(item.getBucket());
                        entity.setOssFileName(getOssFileName(item.getPath()));
                        entity.setOssPath(item.getPath());
                        entity.setOssCreateTime(DateUtils.formatDateTime(new Date(Long.parseLong(item.getCreate_time()) * 1000)));
                        entity.setOssSize(BigDecimal.valueOf(Long.parseLong(item.getSize()) / 1024.0));
                        return entity;
                    })
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    private static String getOssFileName(String path) {
        int index = path.lastIndexOf(File.separator);
        index = index < 0 ? path.lastIndexOf("/") : index;
        if (index > 0) {
            return path.substring(index + 1);
        }
        return path;
    }

    public Map<String, Integer> getCurrentPages() {
        return currentPages;
    }
}
