package com.sccl.modules.mssaccount.rbillitemaccount.service;

import com.sccl.framework.service.IBaseService;
import com.sccl.modules.business.account.domain.*;
import com.sccl.modules.business.accountEs.domain.AccountEsResult;
import com.sccl.modules.business.accountbillitempre.domain.Accountbillitempre;
import com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;
import com.sccl.modules.mssaccount.mssaccountbillitem.domain.MssAccountbillitem;
import com.sccl.modules.mssaccount.rbillitemaccount.domain.RBillitemAccount;

import java.util.List;
import java.util.Map;

/**
 * 报账明细 台账 关联 服务层
 *
 * <AUTHOR>
 * @date 2019-06-01
 */
public interface IRBillitemAccountService extends IBaseService<RBillitemAccount> {


    List<MssAccountbillitem> addItemByaccounts(Accountbillitempre accountbillitempre) throws Exception;
    List<MssAccountbillitem> addItemByaccountsExcluedePcids(Accountbillitempre accountbillitempre,List<Long> accountIds) throws Exception;
    List<MssAccountbillitem> addItemByaccountsgz(Accountbillitempre accountbillitempre) throws Exception;
    Map<String, Double> listNoPage(String[] toStrArray);

    Map<String, Double> countUseMoneyedit(Map<String, Object> reqMap);

    List<AccountBaseResult> accountlistBybillId(RBillitemAccount rBillitemAccount);
    List<StationbybillResult> stationlistBybillId(RBillitemAccount rBillitemAccount);
    List<StationbybillResult> stationlistBycompany(Ammeterorprotocol ammeterorprotocol);

    List<AccountEsResult> accountEslistBybillId(RBillitemAccount rBillitemAccount);

    String getBillType(RBillitemAccount rBillitemAccount);

    //根据 报账单 更新 归集单 台账状态 -- 电费
    void updateAccountsByBill(MssAccountbill bill);

    //根据 报账单 更新 归集单 台账状态 -- 热力、煤炭、用油
    void updateNewAccountsByBill(MssAccountbill bill);

    // 如果 所有报账都完成 更新 归集单 为报账完成
    void updatePreByBill(MssAccountbill bill);

    List<AccountEsResult> accountEslistBybillIds(String[] ids);

    AccountAmount getAccountAmount(AccountAmount accountAmount);

    int saveAccountAmount(AccountAmount accountAmount);

    int saveAccountAmounts(List<AccountAmount> list);

	int saveAccountAmountStationInfo(List<AccountAmountStationInfo> list);

	List<AccountAmountStationInfo> getAccountAmountStationInfo(AccountAmountStationInfo accountAmount);
	int countqutoaBybillId(Long id);

    StationListTop stationlistTop(String ammeterorprotocol);

    String stationlistTopGenerate(String company);

    List<StationbybillResult> stationlistTwo(String company, String country, boolean relevanceFlag);

    List<NhSite> getStationFor5G(NhSite nhSite);

    List<MssAccountbillitem> addNewItemAccounts(Accountbillitempre accountbillitempre) throws Exception;
}
