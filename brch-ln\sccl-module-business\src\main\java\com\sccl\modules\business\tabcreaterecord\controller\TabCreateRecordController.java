package com.sccl.modules.business.tabcreaterecord.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.business.tabcreaterecord.domain.TabCreateRecord;
import com.sccl.modules.business.tabcreaterecord.service.ITabCreateRecordService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 操作记录 信息操作处理
 * 
 * <AUTHOR>
 * @date 2022-12-08
 */
@RestController
@RequestMapping("/business/tabCreateRecord")
public class TabCreateRecordController extends BaseController
{
    private String prefix = "business/tabCreateRecord";
	
	@Autowired
	private ITabCreateRecordService tabCreateRecordService;
	
	@RequiresPermissions("business:tabCreateRecord:view")
	@GetMapping()
	public String tabCreateRecord()
	{
	    return prefix + "/tabCreateRecord";
	}
	
	/**
	 * 查询操作记录列表
	 */
	@RequiresPermissions("business:tabCreateRecord:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(TabCreateRecord tabCreateRecord)
	{
		startPage();
        List<TabCreateRecord> list = tabCreateRecordService.selectList(tabCreateRecord);
		return getDataTable(list);
	}
	
	/**
	 * 新增操作记录
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存操作记录
	 */
	@RequiresPermissions("business:tabCreateRecord:add")
	@Log(title = "操作记录", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody TabCreateRecord tabCreateRecord)
	{		
		return toAjax(tabCreateRecordService.insert(tabCreateRecord));
	}

	/**
	 * 修改操作记录
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		TabCreateRecord tabCreateRecord = tabCreateRecordService.get(id);

		Object object = JSONObject.toJSON(tabCreateRecord);

        return this.success(object);
	}
	
	/**
	 * 修改保存操作记录
	 */
	@RequiresPermissions("business:tabCreateRecord:edit")
	@Log(title = "操作记录", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody TabCreateRecord tabCreateRecord)
	{		
		return toAjax(tabCreateRecordService.update(tabCreateRecord));
	}
	
	/**
	 * 删除操作记录
	 */
	@RequiresPermissions("business:tabCreateRecord:remove")
	@Log(title = "操作记录", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(tabCreateRecordService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看操作记录
     */
    @RequiresPermissions("business:tabCreateRecord:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		TabCreateRecord tabCreateRecord = tabCreateRecordService.get(id);

        Object object = JSONObject.toJSON(tabCreateRecord);

        return this.success(object);
    }

}
