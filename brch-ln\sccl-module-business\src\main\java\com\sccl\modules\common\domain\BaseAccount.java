package com.sccl.modules.common.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 台账基本信息
 * @date 2024/9/26  16:50
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseAccount {

    private Long id;

    /**
     * 所属公司
     */
    private String companyName;

    /**
     * 所属部门
     */
    private String orgName;

    /**
     * 总金额（不含税）
     */
    private BigDecimal totalMoney;

    /**
     * 台账状态
     */
    private String accountStatus;

    /**
     * 录入人
     */
    private String inputName;

    /**
     * 归集单事项名称
     */
    private String accountBillPreName;

    private String taxRateShow;

    private Byte taxRate;

    private BigDecimal inputTaxTicketMoney;

    private BigDecimal inputTicketMoney;

    private Long company;

    private Long country;

    private String delFlag;

    private Long orgId;

    private Byte status = 1;

    private String inputerId;

    private Date inputDate;

    private String lastediterId;

    private Date lasteditDate;
}
