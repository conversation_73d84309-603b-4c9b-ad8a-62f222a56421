package com.sccl.modules.mssaccount.mssaccountbill.service;

import com.sccl.framework.service.IBaseService;
import com.sccl.modules.business.accountbillpre.domain.Accountbillpre;
import com.sccl.modules.mssaccount.mssaccountbill.domain.AccountBillRequest;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;
import com.sccl.modules.mssaccount.mssaccountbill.domain.PrepaidAccountBillDTO;
import com.sccl.modules.mssaccount.mssaccountbill.domain.StatisticalAccountBillDTO;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 报账 服务层
 *
 * <AUTHOR>
 * @date 2019-04-24
 */
public interface IMssAccountbillService extends IBaseService<MssAccountbill> {

    Map<String, Object> saveMssAccountbill(MssAccountbill mssAccountbill);

    MssAccountbill getByid(Long id);

    int deleteById(Long id);

    List<MssAccountbill> selectListByAuto(MssAccountbill mssAccountbill);

    List<MssAccountbill> selectListByCheck(MssAccountbill mssAccountbill);

    List<MssAccountbill> selectListByCheckSK(MssAccountbill mssAccountbill);

    Accountbillpre getAccountBillpre(Long id);

    int deleteByIdsAuto(String[] toStrArray);

    /**
     * @Description: 通过id数组查询对象集合
     * @author: dongk
     * @date: 2019/5/23
     * @param:
     * @return:
     */
    List<MssAccountbill> selectListByIds(String[] ids);

    /**
     * 通过id数组和类型查询对象
     *
     * @param ids
     * @param type
     * @return java.util.List<com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill>
     * <AUTHOR> Yongxiang
     * @date 2021/12/23 15:26
     */
    List<MssAccountbill> selectListByIdsAndType(String[] ids, BigDecimal type);

    //根据财辅退回的单子 生成新的报账单
    Long addNewBillByExist(Long id) throws Exception;
    String addNewBillByExistExcludePcids(Long id, List<Long> accountIds) throws Exception;

    // 判断 报账单关联的电表/协议用电类型含“生产用电-移动基站”下所有的细类，
    // 即“1411,1412,1421,1422,1431,1432”时
    int countAmmeterTypeBybillId(Long id);

    // 判断 报账单关联的电表/协议用电类型含“生产用电-移动基站”下所有LTE，
    // 即“1411,1412,1421,1422,1431,1432”时
    int countLteStationBybillId(Long id);

    /**
     * @Description: 同比环比分析统计
     * @author: lc
     * @date: 2019/5/23
     * @param:
     * @return:
     */
    List<Map<String, Object>> statisticalAnalysis(Map<String, Object> params);

    int deleteByIdForError(Long id);

    Map<String, Object> saveChecks(MssAccountbill mssAccountbill);

    // 查询 上一次 新增的报账单
    MssAccountbill getByOldOne(MssAccountbill m);
    String getmssbasecodebyPre(String id);

    Map<String, Object> saveCheckAccount(MssAccountbill mssAccountbill);

    Map<String, Object> checkMssAccountbill(MssAccountbill mssAccountbill);

    List<Map<String, Object>> querystationError(Accountbillpre pre);

    void saveStationRemark(Long id, String remark, String userName);

    void insertjtltem(MssAccountbill mssAccountbill);

    List<MssAccountbill> selectNonElectricListByAuto(MssAccountbill mssAccountbill);

    Map<String, Object> saveNonElectricMssAccountbill(MssAccountbill mssAccountbill);

    Map<String, Object> saveNonElectricMssAccountbillNew(MssAccountbill mssAccountbill);

    Map<String, Object> checkNonEletricMssAccountbill(MssAccountbill mssAccountbill);

    MssAccountbill selectForSameCity(MssAccountbill mForSameCity);

    String removeForAccountIds(Long billId, String accountIds);

    int deletePowerAuditByBillIds(String[] strArray);

    /**
     * 报账统计查询
     * @param request
     * @return
     */
    List<StatisticalAccountBillDTO> listStatisticalAccountBill(AccountBillRequest request);


    /**
     * 预付管理统计查询
     * @param request
     * @return
     */
    List<PrepaidAccountBillDTO> listPreAccountBill(AccountBillRequest request);
}
