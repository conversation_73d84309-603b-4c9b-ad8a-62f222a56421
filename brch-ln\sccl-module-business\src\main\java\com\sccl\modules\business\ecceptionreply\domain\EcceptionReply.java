package com.sccl.modules.business.ecceptionreply.domain;

import com.sccl.framework.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * 公共异常回复表 common_ecception_reply
 *
 * <AUTHOR>
 * @date 2023-03-27
 */
public class EcceptionReply extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 异常主表id
     */
    private String exceptionCommonId;
    /**
     * 异常明细id
     */
    private String exceptionid;
    /**
     * 异常来源 1一站式稽核/2集团下发异常/3基础稽核异常/4大数据 5/事后报账规范 power_category_type type_category=commonEcceptionSourc
     */
    private String exceptionsource;
    /**
     * 流程实例id
     */
    private String processid;
    /**
     * 任务实例id
     */
    private String taskid;
    /**
     * 填报人id
     */
    private String userid;
    /**
     * 所属公司id
     */
    private String company;
    /**
     * 所属部门id
     */
    private String country;
    /**
     * 回复内容
     */
    private String replyMsg;
    /**
     * 回复选项 power_category_type type_category=commonEcceptionEeply
     */
    private String replyOption;

    public String getExceptionCommonId() {
        return exceptionCommonId;
    }

    public void setExceptionCommonId(String exceptionCommonId) {
        this.exceptionCommonId = exceptionCommonId;
    }

    public String getExceptionid() {
        return exceptionid;
    }

    public void setExceptionid(String exceptionid) {
        this.exceptionid = exceptionid;
    }

    public String getExceptionsource() {
        return exceptionsource;
    }

    public void setExceptionsource(String exceptionsource) {
        this.exceptionsource = exceptionsource;
    }

    public String getProcessid() {
        return processid;
    }

    public void setProcessid(String processid) {
        this.processid = processid;
    }

    public String getTaskid() {
        return taskid;
    }

    public void setTaskid(String taskid) {
        this.taskid = taskid;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getReplyMsg() {
        return replyMsg;
    }

    public void setReplyMsg(String replyMsg) {
        this.replyMsg = replyMsg;
    }

    public String getReplyOption() {
        return replyOption;
    }

    public void setReplyOption(String replyOption) {
        this.replyOption = replyOption;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("exceptionCommonId", getExceptionCommonId())
                .append("exceptionid", getExceptionid())
                .append("exceptionsource", getExceptionsource())
                .append("processid", getProcessid())
                .append("taskid", getTaskid())
                .append("userid", getUserid())
                .append("company", getCompany())
                .append("country", getCountry())
                .append("replyMsg", getReplyMsg())
                .append("replyOption", getReplyOption())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("delFlag", getDelFlag())
                .toString();
    }
}
