package com.sccl.modules.business.msg.domain;

import com.sccl.framework.web.domain.BaseEntity;
import com.sccl.modules.business.powerauditstaiongrade.entity.StaionAnalysisDetail;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 铁塔稽核结果表 msg
 *
 * <AUTHOR>
 * @date 2021-08-16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Message extends BaseEntity implements StaionAnalysisDetail {
    private static final long serialVersionUID = 1L;

    /**
     * 地市名
     */
    private String city;

    /**
     * 状态，0表示存在异常，1表示正常
     */
    private Integer status;
    /**
     * 与数据库比较的字段的id
     */
    private String compareId;
    /**
     * 铁塔台账唯一id
     */
    private String towerId;

    /**
     * 异常位置：1：新增站址；2：基础数据异常站址；3：连续性异常站址（不存在上一期条目）4：时序异常站址；5：连续性异常站址（电表读数跳跃）；-1：正常站址
     */
    private Integer rongStep;
    /**
     * 记录时间
     */
    private String time;
    /**
     * 信息
     */
    private String message;
    /**
     * 与数据库比较字段所存在的表名
     */
    private String compareTable;
    /**
     * 标记
     */
    private String mark;
    /**
     * 定额异常类型
     */
    private String type;
    /**
     * toweraccount表唯一ID
     */
    private Long towerKey;
    private String companyName;
    private Long company;
    private Long country;
    /**
     * 运行时长：ms
     */
    private Integer runTime;
    /**
     * 铁塔站址编码 : 511402908000000012
     */
    private String towerSiteCode;
    /**
     * 铁塔站址名称
     */
    private String towerSiteName;
    /**
     * 铁塔区县
     */
    private String district;
    /**
     *
     */
    private String isrelate;
}
