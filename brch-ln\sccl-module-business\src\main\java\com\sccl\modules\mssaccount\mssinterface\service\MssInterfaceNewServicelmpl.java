package com.sccl.modules.mssaccount.mssinterface.service;

import com.sccl.framework.common.exception.base.BaseException;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.business.accountbillpre.mapper.AccountbillpreMapper;
import com.sccl.modules.business.budgetapproval.service.BudgetApproveOrderServiceImpl;
import com.sccl.modules.business.order.domain.Order;
import com.sccl.modules.business.order.service.IOrderService;
import com.sccl.modules.business.stationinfo.mapper.PowerStationInfoRJtlteMapper;
import com.sccl.modules.monitor.operlog.domain.OperLog;
import com.sccl.modules.monitor.operlog.mapper.OperLogMapper;
import com.sccl.modules.mssaccount.mssabccustomerbank.mapper.MssAbccustomerBankMapper;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;
import com.sccl.modules.mssaccount.mssaccountbill.mapper.MssAccountbillMapper;
import com.sccl.modules.mssaccount.mssaccountbillitem.mapper.MssAccountbillitemMapper;
import com.sccl.modules.mssaccount.mssaccountbillpayinfo.mapper.MssAccountbillpayinfoMapper;
import com.sccl.modules.mssaccount.mssaccountclearitem.mapper.MssAccountclearitemMapper;
import com.sccl.modules.mssaccount.mssaccountclearitemaccount.mapper.MssAccountclearitemAccountMapper;
import com.sccl.modules.mssaccount.msssupplieritem2.mapper.MssSupplierItem2Mapper;
import com.sccl.modules.mssaccount.rbillitemaccount.mapper.RBillitemAccountMapper;
import com.sccl.modules.mssaccount.rbillitemaccount.service.IRBillitemAccountService;
import com.sccl.modules.uniflow.common.WFModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 *
 * 油、取暖、煤炭报账流程回调类
 *
* */

@Service
public class MssInterfaceNewServicelmpl extends BaseServiceImpl<Order> implements IOrderService {

    private static final Logger logger = LoggerFactory.getLogger(MssInterfaceNewServicelmpl.class);

    /**
     * 操作日志
     */
    @Autowired
    private OperLogMapper operLogMapper;
    /**
     * 报账单
     */
    @Autowired
    MssAccountbillMapper billmmapper;
    /**
     * 报账单详情
     */
    @Autowired
    MssAccountbillitemMapper itemmapper;
    /**
     * 外部收款人
     */
    @Autowired
    MssAccountbillpayinfoMapper payinfoMapper;
    /**
     * 供应商外部收款人
     */
    @Autowired
    MssSupplierItem2Mapper supplierItem2Mapper;
    /**
     * 客户外部收款人
     */
    @Autowired
    MssAbccustomerBankMapper abccustomerBankMapper;
    /**
     * 挑对单
     */
    @Autowired
    MssAccountclearitemMapper clearitemMapper;
    /**
     * 挑对单台账
     */
    @Autowired
    MssAccountclearitemAccountMapper clearitemAccountMapper;
    /**
     * 归集单
     */
    @Autowired
    AccountbillpreMapper accountbillpreMapper;
    /**
     * 报账明细 台账 关联
     */
    @Autowired
    RBillitemAccountMapper rBillitemAccountMapper;
    /**
     * 报账明细 台账 关联
     */
    @Autowired
    IRBillitemAccountService rBillitemAccountService;
    /**
     * 集团4Glte
     */
    @Autowired
    PowerStationInfoRJtlteMapper powerStationInfoRJtlteMapper;

    @Autowired
    private MssAccountbillMapper billMapper;

    @Autowired
    private MssInterfaceServiceImpl mssInterfaceService;

    @Override
    public void uniflowCallBack(WFModel wfModel) throws Exception {
        logger.debug("-----------------lt:" + wfModel.toString());
        if ("PROCESS_STARTED".equals(wfModel.getCallbackType())) {
            /**
             * 更新流程Id  流程 提交
             */
            try {
                doStartFlow(wfModel.getBusiId(), wfModel.getProcInstId());
                logger.debug("油、取暖、煤炭报账单新增" + wfModel.getBusiId());
            } catch (NumberFormatException e) {
                e.printStackTrace();
                logger.error("油、取暖、煤炭报账提交流程失败:" + e.getMessage());
                throw new BaseException("油、取暖、煤炭报账提交流程失败:" + e.getMessage());//
            }
            // 结束流程默认设置的是 SYS_ADMIN 系统自动（流程接口使用）
        } else if ("sys".equals(wfModel.getVariables().get("appointUserId"))) {
            try {// 送财辅接口
                mssInterfaceService.sendToMssNonelectric(Long.valueOf(wfModel.getBusiId()));
                logger.debug("油、取暖、煤炭报账送财辅接口" + wfModel.getBusiId());
            } catch (Exception e) {
                e.printStackTrace();
                doErrorFlow(wfModel.getBusiId());
                insertLog("油、取暖、煤炭报账提交财辅系统接口异常", "uniflowCallBack", e.getMessage());
                logger.error("油、取暖、煤炭报账提交财辅系统接口异常，提交失败" + e.getMessage());
                throw e;
            }
        } else if ("PROCESS_COMPLETED".equals(wfModel.getCallbackType())) {
//            流程完成后执行的回调  不在这里处理
//            改为系统自动轮询结果 getStausBybills
//            try {
//                doEndFlow(wfModel.getBusiId());
//            } catch (Exception e) {
//                e.printStackTrace();
//                throw new BaseException("提交流程失败:" + e.getMessage());//
//            }
            mssInterfaceService.sendToMssNonelectric(Long.valueOf(wfModel.getBusiId()));
            logger.debug("油、取暖、煤炭报账送财辅接口" + wfModel.getBusiId());

        } else if ("TURNBACK_TO_START".equals(wfModel.getCallbackType())) {

            // 流程 退回
            doExistFlow(wfModel.getBusiId());
        } else if ("PROCESS_CANCELLED".equals(wfModel.getCallbackType())) {

            // 流程 终止
            doKillFlow(wfModel.getBusiId());
        }
    }


    /**
     * 记录 错误日志
     */
    private void insertLog(String title, String method, String errorMes) {
        OperLog model = new OperLog();
        model.setOperName("mss");
        model.setTitle(title);
        model.setMethod(method);
        model.setErrorMsg(errorMes);
        model.setOperTime(new Date());

        operLogMapper.insert(model);
    }

    /**
     * 提交流程 执行方法
     */
    public void doStartFlow(String busiId, Long processinstid) {
        MssAccountbill bill = new MssAccountbill();
        // 代办
        bill.setStatus(2);
        bill.setId(Long.valueOf(busiId));
        bill.setProcessinstid(processinstid);
        billMapper.updateForModel(bill);
        //根据 报账单 台账状态
        rBillitemAccountService.updateNewAccountsByBill(bill);
    }

    /**
     * 流程结束 执行方法
     */
    public void doEndFlow(MssAccountbill bill) {
        //更新状态
        billMapper.updateForModel(bill);
        //根据 报账单  修改台账状态
        rBillitemAccountService.updateAccountsByBill(bill);
        // 如果 所有报账都完成 更新 归集单 为报账完成
        rBillitemAccountService.updatePreByBill(bill);
        if (bill.getStatus() == 8 && "-1".equals(bill.getIresult())) {
            // 退单 标识 退单 删除 台账 明细关联关系
            // 删除旧的关联关系
            rBillitemAccountMapper.deleteRbillitemAccountByBillId(bill.getId());
            // 推送数据接口 终止报账
            try {//支付类报账单送集团
                // 终止报账
                mssInterfaceService.selectWriteoffDetailInfo(bill.getId(), "3");
            } catch (Exception e) {
                e.printStackTrace();
                insertLog("油、取暖、煤炭报账推送数据接口 终止报账，报账单:【" + bill.getId() + "】", "meterInfo", e.getMessage());
                logger.error("油、取暖、煤炭报账报账单" + bill.getId() + " 油、取暖、煤炭报账同步集团电费化小失败：" + e.getMessage());
            }
        }
    }

    /**
     * 从财辅失败 执行方法
     */
    public void doErrorFlow(String busiId) {
        MssAccountbill bill = new MssAccountbill();
        //完成
        bill.setStatus(-3);
        bill.setId(Long.valueOf(busiId));
        billMapper.updateForModel(bill);
    }

    /**
     * 流程 退回
     */
    private void doExistFlow(String busiId) {
        // 修改状态
        MssAccountbill bill = new MssAccountbill();
        //退回
        bill.setStatus(-2);
        bill.setId(Long.valueOf(busiId));
        billMapper.updateForModel(bill);
    }

    /**
     * 流程 终止
     */
    private void doKillFlow(String busiId) {
        // 修改状态
        MssAccountbill bill = new MssAccountbill();
        //草稿
        bill.setStatus(1);
        bill.setId(Long.valueOf(busiId));
        billMapper.updateForModel(bill);
    }
}
