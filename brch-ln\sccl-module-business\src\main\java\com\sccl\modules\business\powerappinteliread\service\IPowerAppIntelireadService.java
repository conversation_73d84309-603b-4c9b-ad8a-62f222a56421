package com.sccl.modules.business.powerappinteliread.service;

import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.business.accountEs.domain.PowerAccountEs;
import com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol;
import com.sccl.modules.business.powerappinteliread.domain.PowerAppInteliread;
import com.sccl.framework.service.IBaseService;

import java.util.List;

/**
 * 远程抄记录 服务层
 * 
 * <AUTHOR>
 * @date 2022-03-08
 */
public interface IPowerAppIntelireadService extends IBaseService<PowerAppInteliread>
{
    public int insertAccount(List<PowerAccountEs> accountEsList);
    public List<PowerAppInteliread> selectByList(PowerAppInteliread powerAppInteliread);
    public List<PowerAppInteliread> selectByNeed(PowerAppInteliread powerAppInteliread);

    public List<PowerAppInteliread> listDetail(PowerAppInteliread powerAppInteliread);
    public PowerAppInteliread viewDetail(Long id);
    public int insertireaddata(PowerAppInteliread powerAppInteliread);
}
