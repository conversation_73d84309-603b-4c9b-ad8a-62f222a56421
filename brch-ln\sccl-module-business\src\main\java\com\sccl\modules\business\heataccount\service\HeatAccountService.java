package com.sccl.modules.business.heataccount.service;

import com.sccl.framework.service.IBaseService;
import com.sccl.modules.business.heataccount.domain.HeatAccount;
import com.sccl.modules.business.heataccount.domain.HeatAccountRequest;
import com.sccl.modules.system.user.domain.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 热力服务层
 * @date 2024/8/27  11:44
 */
public interface HeatAccountService extends IBaseService<HeatAccount> {

    List<HeatAccount> listHeatAccount(HeatAccountRequest request);

    Map<String, Object> importHeatAccount(HttpServletRequest request, HttpServletResponse response) throws Exception;

    int batchAddOrUpdateHeatAccount(List<HeatAccount> accounts, User user);
}
