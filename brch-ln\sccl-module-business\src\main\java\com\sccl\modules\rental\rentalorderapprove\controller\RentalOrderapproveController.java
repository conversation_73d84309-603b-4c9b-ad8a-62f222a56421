package com.sccl.modules.rental.rentalorderapprove.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.rental.rentalorderapprove.domain.RentalOrderapprove;
import com.sccl.modules.rental.rentalorderapprove.service.IRentalOrderapproveService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 审批记录 信息操作处理
 * 
 * <AUTHOR>
 * @date 2019-08-23
 */
@RestController
@RequestMapping("/rental/rentalOrderapprove")
public class RentalOrderapproveController extends BaseController
{
    private String prefix = "rental/rentalOrderapprove";
	
	@Autowired
	private IRentalOrderapproveService rentalOrderapproveService;
	
	@RequiresPermissions("rental:rentalOrderapprove:view")
	@GetMapping()
	public String rentalOrderapprove()
	{
	    return prefix + "/rentalOrderapprove";
	}
	
	/**
	 * 查询审批记录列表
	 */
	@RequiresPermissions("rental:rentalOrderapprove:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(RentalOrderapprove rentalOrderapprove)
	{
		startPage();
        List<RentalOrderapprove> list = rentalOrderapproveService.selectList(rentalOrderapprove);
		return getDataTable(list);
	}
	
	/**
	 * 新增审批记录
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存审批记录
	 */
	@RequiresPermissions("rental:rentalOrderapprove:add")
	//@Log(title = "审批记录", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody RentalOrderapprove rentalOrderapprove)
	{		
		return toAjax(rentalOrderapproveService.insert(rentalOrderapprove));
	}

	/**
	 * 修改审批记录
	 */
	@GetMapping("/edit/{pabaid}")
	public AjaxResult edit(@PathVariable("pabaid") Long pabaid)
	{
		RentalOrderapprove rentalOrderapprove = rentalOrderapproveService.get(pabaid);

		Object object = JSONObject.toJSON(rentalOrderapprove);

        return this.success(object);
	}
	
	/**
	 * 修改保存审批记录
	 */
	@RequiresPermissions("rental:rentalOrderapprove:edit")
	//@Log(title = "审批记录", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody RentalOrderapprove rentalOrderapprove)
	{		
		return toAjax(rentalOrderapproveService.update(rentalOrderapprove));
	}
	
	/**
	 * 删除审批记录
	 */
	@RequiresPermissions("rental:rentalOrderapprove:remove")
	//@Log(title = "审批记录", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(rentalOrderapproveService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看审批记录
     */
    @RequiresPermissions("rental:rentalOrderapprove:view")
    @GetMapping("/view/{pabaid}")
    @ResponseBody
    public AjaxResult view(@PathVariable("pabaid") Long pabaid)
    {
		RentalOrderapprove rentalOrderapprove = rentalOrderapproveService.get(pabaid);

        Object object = JSONObject.toJSON(rentalOrderapprove);

        return this.success(object);
    }

}
