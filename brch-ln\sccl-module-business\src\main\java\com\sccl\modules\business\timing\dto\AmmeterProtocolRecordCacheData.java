package com.sccl.modules.business.timing.dto;

import com.sccl.timing.finder.framework.CacheData;
import com.sccl.timing.finder.util.DateUtils;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-06-14 9:53
 * @email <EMAIL>
 */
@Getter
@Setter
public class AmmeterProtocolRecordCacheData implements CacheData {
    /**
     * 用电类型
     */
    private String electrotype;

    /**
     * 对外结算类型
     */
    private String directsupplyflag;

    private String createTime;

    public AmmeterProtocolRecordCacheData(Long electrotype, Integer directsupplyflag, Date createTime) {
        this.electrotype = electrotype + "";
        this.directsupplyflag = directsupplyflag + "";
        if (createTime != null) {
            this.createTime = DateUtils.formatDateTime(createTime);
        }
    }
}
