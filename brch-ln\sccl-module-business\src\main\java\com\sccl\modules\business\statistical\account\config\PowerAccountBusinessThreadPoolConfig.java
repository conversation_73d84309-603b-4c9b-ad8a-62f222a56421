package com.sccl.modules.business.statistical.account.config;

import com.sccl.common.thread.TimerThreadPoolExecutor;
import com.sccl.modules.autojob.util.id.IdGenerator;
import com.sccl.modules.monitor.operlog.domain.OperLog;
import com.sccl.modules.monitor.operlog.mapper.OperLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 业务线程池配置
 *
 * <AUTHOR>
 * @date 2022-12-06 14:00
 * @email <EMAIL>
 */
@Configuration
@Slf4j
public class PowerAccountBusinessThreadPoolConfig {
    @Autowired(required = false)
    private OperLogMapper mapper;

    @Bean("unitPriceStat")
    TimerThreadPoolExecutor timerThreadPoolExecutor() {
        TimerThreadPoolExecutor.TimerEntry night = new TimerThreadPoolExecutor.TimerEntry("0 0 22 * * ?", 10, 20, -1, TimeUnit.SECONDS);
        TimerThreadPoolExecutor.TimerEntry day = new TimerThreadPoolExecutor.TimerEntry("0 0 5 * * ?", 0, 1, -1, TimeUnit.SECONDS);
        night.setTriggerListener((cronExpression, threadPoolExecutor) -> {
            log.info("夜间线程池调整->C:{} M:{}", threadPoolExecutor.getCorePoolSize(), threadPoolExecutor.getMaximumPoolSize());
            if (mapper != null) {
                OperLog operLog = new OperLog()
                        .setOperTime(new Date())
                        .setMethod("timerThreadPoolExecutor")
                        .setErrorMsg(String.format("夜间线程池调整->C:%d M:%d", threadPoolExecutor.getCorePoolSize(), threadPoolExecutor.getMaximumPoolSize()));
                operLog.setId(IdGenerator.getNextIdAsLong());
                operLog.setStatus("1");
                mapper.insert(operLog);
            }

        });
        day.setTriggerListener(((cronExpression, threadPoolExecutor) -> {
            log.info("日间线程池调整->C:{} M:{}", threadPoolExecutor.getCorePoolSize(), threadPoolExecutor.getMaximumPoolSize());
            if (mapper != null) {
                OperLog operLog = new OperLog()
                        .setOperTime(new Date())
                        .setMethod("timerThreadPoolExecutor")
                        .setErrorMsg(String.format("日间线程池调整->C:%d M:%d", threadPoolExecutor.getCorePoolSize(), threadPoolExecutor.getMaximumPoolSize()));
                operLog.setId(IdGenerator.getNextIdAsLong());
                operLog.setStatus("1");
                mapper.insert(operLog);
            }
        }));
        return TimerThreadPoolExecutor
                .builder()
                .addTimerEntry(day)
                .addTimerEntry(night)
                .setInitialCoreTreadCount(3)
                .setTaskQueueCapacity(100)
                .setInitialMaximizeTreadCount(5)
                .build();
    }
}
