package com.sccl.modules.business.stationinfo.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.business.account.domain.NhSite;
import com.sccl.modules.business.stationinfo.domain.StationJt5gjz;
import com.sccl.modules.business.stationinfo.mapper.StationJt5gjzMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 集团LTE网管基站 服务层实现
 *
 * <AUTHOR>
 * @date 2021-05-16
 */
@Service
public class StationJt5gjzServiceImpl extends BaseServiceImpl<StationJt5gjz> implements IStationJt5gjzService {
    @Autowired
    private StationJt5gjzMapper stationJt5gjzMapper;

    @Override
    public NhSite getNhsite(Long nhSiteId) {
        return stationJt5gjzMapper.getgetNhsite(nhSiteId);
    }

    @Override
    public List<StationJt5gjz> selectListLike(StationJt5gjz stationJt5gjz) {
        if (ObjectUtil.isNotEmpty(stationJt5gjz) && StrUtil.isNotBlank(stationJt5gjz.getCitycode())) {
            String cityCode = stationJt5gjz.getCitycode().substring(3, 5);
            stationJt5gjz.setCitycode(cityCode);
        }
        return stationJt5gjzMapper.selectListLike(stationJt5gjz);
    }
}
