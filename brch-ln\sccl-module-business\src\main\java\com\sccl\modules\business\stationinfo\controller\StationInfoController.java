package com.sccl.modules.business.stationinfo.controller;

import com.alibaba.fastjson.JSONObject;
import com.sccl.common.lang.StringUtils;
import com.sccl.common.support.Convert;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.account.domain.Ltestation;
import com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol;
import com.sccl.modules.business.stationinfo.domain.CheckStationInfoDto;
import com.sccl.modules.business.stationinfo.domain.StationInfo;
import com.sccl.modules.business.stationinfo.domain.StationRecord;
import com.sccl.modules.business.stationinfo.dto.ResStationQueryDto;
import com.sccl.modules.business.stationinfo.service.IStationInfoService;
import com.sccl.modules.business.stationinfo.service.IStationRecordService;
import com.sccl.modules.business.stationinfo.vo.StationBatchStopVo;
import com.sccl.modules.protocolexpiration.noaccount.service.INoAccountAlertService;
import com.sccl.modules.system.user.domain.User;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 局站 信息操作处理
 *
 * <AUTHOR>
 * @date 2019-05-29
 */
@RestController
@RequestMapping("/business/stationInfo")
public class StationInfoController extends BaseController
{
    private String prefix = "business/stationInfo";
    @Autowired
    private INoAccountAlertService noAccountAlertService;

	@Autowired
	private IStationInfoService stationInfoService;
	@Autowired
	IStationRecordService stationRecordService;

	@RequiresPermissions("business:stationInfo:view")
	@GetMapping()
	public String stationInfo()
	{
	    return prefix + "/stationInfo";
	}

	/**
	 * 查询局站列表
	 */
	//@RequiresPermissions("business:stationInfo:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(StationInfo stationInfo)
	{
	    boolean isCityMAnager=false;
        User user = ShiroUtils.getUser();
		stationInfo.setCreateuser(user.getId());
		List<String> countryList=new ArrayList<String>();
		/*if(new Long(-1).equals(stationInfo.getCountry())){
			if(!stationInfo.getIsAdmin()){//非管理员
				List<IdNameVO> listCountry=user.getDepartments();
				for(IdNameVO idName:listCountry){
					countryList.add(idName.getId());
				}
			}else if(stationInfo.getIsProAdmin()||stationInfo.getIsCityAdmin()){//有权限查看分公司下所有子部门的用户
				countryList=new ArrayList<String>();
			}else if (stationInfo.getIsSubAdmin()){//县能耗管理员时取其所在部门上级下所有部门
				//登陆人上级部门下所有部门list
				countryList=stationInfoService.getAllBelongCountry();
				//没取到上级部门下所有同级部门时，用当前用户自己所在部门做调价
				if(countryList.size()==0){
					List<IdNameVO> listCountry=user.getDepartments();
					for(IdNameVO idName:listCountry){
						countryList.add(idName.getId());
					}
				}
			}
			stationInfo.setListIds(countryList);
		}普通用户也能查询整个分公司下所有局站*/
		stationInfo.setListIds(countryList);
		startPage();
        List<StationInfo> list = stationInfoService.selectListWithArea(stationInfo);
        for(StationInfo tempstationInfo:list){
			//辽宁新增5gr字段
			// stationInfoService.set5grCodeForLn(tempstationInfo);
			if(new Long(1).equals(tempstationInfo.getWfStatus())||new Long(4).equals(tempstationInfo.getWfStatus())){
				tempstationInfo.set_disabled(true);
			}else{
				tempstationInfo.set_disabled(false);
			}
		}

		return getDataTable(list);
	}
	/**
	 * 查询局站列表
	 */
	//@RequiresPermissions("business:stationInfo:list")
	@RequestMapping("/baselist")
	@ResponseBody
	public TableDataInfo baselist(StationInfo stationInfo)
	{
		boolean isCityMAnager=false;
		User user = ShiroUtils.getUser();
		stationInfo.setCreateuser(user.getId());
		List<String> countryList=new ArrayList<String>();
		/*if(new Long(-1).equals(stationInfo.getCountry())){
			if(!stationInfo.getIsAdmin()){//非管理员
				List<IdNameVO> listCountry=user.getDepartments();
				for(IdNameVO idName:listCountry){
					countryList.add(idName.getId());
				}
			}else if(stationInfo.getIsProAdmin()||stationInfo.getIsCityAdmin()){//有权限查看分公司下所有子部门的用户
				countryList=new ArrayList<String>();
			}else if (stationInfo.getIsSubAdmin()){//县能耗管理员时取其所在部门上级下所有部门
				//登陆人上级部门下所有部门list
				countryList=stationInfoService.getAllBelongCountry();
				//没取到上级部门下所有同级部门时，用当前用户自己所在部门做调价
				if(countryList.size()==0){
					List<IdNameVO> listCountry=user.getDepartments();
					for(IdNameVO idName:listCountry){
						countryList.add(idName.getId());
					}
				}
			}
			stationInfo.setListIds(countryList);
		}普通用户也能查询整个分公司下所有局站*/
		stationInfo.setListIds(countryList);
		startPage();
		List<StationInfo> list = stationInfoService.selectbaseList(stationInfo);
		for(StationInfo tempstationInfo:list){
			if(new Long(1).equals(tempstationInfo.getWfStatus())||new Long(4).equals(tempstationInfo.getWfStatus())){
				tempstationInfo.set_disabled(true);
			}else{
				tempstationInfo.set_disabled(false);
			}
		}

		return getDataTable(list);
	}

	@RequestMapping("/oldlist")
	@ResponseBody
	public StationInfo oldlist(@RequestParam("id") Long id) {
		StationInfo stationInfo = stationInfoService.get(id);
		return stationInfo;
	}
	@RequestMapping("/getstationbyname")
	@ResponseBody
	public StationInfo getStationByName(@RequestParam("name") String name) {
		StationInfo stationInfo = stationInfoService.getStationByName(name);
		return stationInfo;
	}

	@RequestMapping("/newlist")
	@ResponseBody
	public TableDataInfo newlist(StationInfo stationInfo) {
		startPage();
		List<StationInfo> list = stationInfoService.selectListWithArea(stationInfo);
		return getDataTable(list);
	}

	/**
	 * 新增局站
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}

	/**
	 * 新增保存局站
	 */
	//@RequiresPermissions("business:stationInfo:add")
	//@Log(title = "局站", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody StationInfo stationInfo)
	{
		AjaxResult ar=new AjaxResult();
		try{
			//验证四川资源局站是否一一对应
			ar = this.checkResourceStationExist(stationInfo.getStationtype().toString(),stationInfo.getId(),stationInfo.getResstationcode(),stationInfo.getResstationname());
			if(!"0".equals(ar.get("code").toString())){
				return ar;
			}
			toAjax(stationInfoService.addStation(stationInfo));
			ar.put("flag","1");
			ar.put("msg","保存成功");
		}catch(Exception e){
			e.printStackTrace();
			return AjaxResult.error("保存失败"+e.getMessage());
		}
		return ar;
	}

	/**
	 * 电表或协议为有效性站址时，保存到power_station_info
	 */
	//@RequiresPermissions("business:stationInfo:add")
	//@Log(title = "局站", action = BusinessType.INSERT)
	@PostMapping("/addstationinfoforvalidity")
	@ResponseBody
	public AjaxResult addstationinfoforvalidity(@RequestBody StationInfo stationInfo)
	{
		return AjaxResult.success();
	}


	/**
	 * *****验证局站信息 - con
	 * <AUTHOR>
	 * @date 2022/4/21
	 */
	@PostMapping("/checkStationInfo")
	@ResponseBody
	public Map<String,String> checkStationInfo(@RequestBody CheckStationInfoDto stationInfo){
		Map<String, String> map = new HashMap<>();
		if (!StringUtils.isEmpty(stationInfo.getType())) {
		if (stationInfo.getType().equals("account")) { //验证归集单加入报账单
			map = stationInfoService.checkSheet(stationInfo);
		}
		}else {
			map = stationInfoService.checkStationInfo(stationInfo);
		}
		return map;
	}

	/**
	 * 验证项目名称是否存在
	 * @param id
	 * @param name
	 * @return
	 */
	public AjaxResult checkResourceStationExist(String type,Long id,@RequestParam(required = true) String code,String name) {
		//验证四川资源局站是否一一对应
		String str = "对应资源系统局站已经存在";
		if("10002".equals(type) && "3".equals(type)){//铁塔
			str = "对应站址编码已经存在";
		}else if("20001".equals(type) || "20002".equals(type) || "-2".equals(type)){//房屋
			str = "房屋名称和编码已经存在";
		}
		StationInfo station = new StationInfo();
		station.setResstationcode(name.trim());
		List<StationInfo> ammeter = stationInfoService.selectObjectBy(station);
		//如果正式表的数据为空则查询记录表，预防其他数据在审核过程中重名
		if (null == ammeter || ammeter.size() == 0){
			Map<String,Object> params = new HashMap<>();
			params.put("resstationcode",code);
			params.put("resstationname",name);

			User user = ShiroUtils.getUser();
			List<IdNameVO> company=user.getCompanies();
			String companyid = company.get(0).getId();
			params.put("company",companyid);

			if (StringUtils.isBlank(code)&&StringUtils.isBlank(name)){
				return AjaxResult.error("错误的查询，局站名称与编码全部为空");
			}

			List<StationRecord> records= stationRecordService.selectObjectBy(params);
			if(records.size() == 0){
				return AjaxResult.success();
			}else {
				for (StationRecord record:records) {
					if(record != null && null != id && record.getStationid().toString().equals(id.toString())){
						return AjaxResult.success();
					}
				}
			}
		}else{
			for (StationInfo value:ammeter) {
				if(value != null && null != id && value.getId().toString().equals(id.toString())){
					return AjaxResult.success();
				}
			}
		}
		return AjaxResult.error(str);
	}
	/**
	 * 修改局站
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		StationInfo stationInfo = stationInfoService.get(id);

		Object object = JSONObject.toJSON(stationInfo);

        return this.success(object);
	}

	/**
	 * 修改保存局站
	 */
	//@RequiresPermissions("business:stationInfo:edit")
	//@Log(title = "局站", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody StationInfo stationInfo)
	{
		AjaxResult ar=new AjaxResult();
		try{
			//验证四川资源局站是否一一对应
			ar = this.checkResourceStationExist(stationInfo.getStationtype().toString(),stationInfo.getId(),stationInfo.getResstationcode(),stationInfo.getResstationname());
			if(!"0".equals(ar.get("code").toString())){
				return ar;
			}
			toAjax(stationInfoService.updateStation(stationInfo));
			ar.put("flag","1");
			ar.put("msg","修改成功");
		}catch(Exception e){
			e.printStackTrace();
			return AjaxResult.error("修改失败"+e.getMessage());
		}
		return ar;
	}

	/**
	 * 删除局站
	 */
	//@RequiresPermissions("business:stationInfo:remove")
	//@Log(title = "局站", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{
		AjaxResult ar=new AjaxResult();
	    //物理刪除草稿狀態的数据
		ar= toAjax(stationInfoService.deleteByIdsDB(Convert.toStrArray(ids)));
		//删除记录表数据
		ar=toAjax(stationRecordService.deleteByStationId(Convert.toStrArray(ids)));
		return ar;
	}


    /**
     * 查看局站
     */
    @RequiresPermissions("business:stationInfo:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		StationInfo stationInfo = stationInfoService.get(id);

        Object object = JSONObject.toJSON(stationInfo);

        return this.success(object);
    }

	@RequestMapping("/logininfo")
	@ResponseBody
	public User compnyList()
	{
		User user = ShiroUtils.getUser();

		return user;
	}

	/**
	 * 查询局站母站房列表
	 */
	@RequestMapping("/motherlist")
	@ResponseBody
	public TableDataInfo motherList(StationInfo stationInfo)
	{
		startPage();
		/*User user = ShiroUtils.getUser();
		List<IdNameVO> company=user.getCompanies();
		stationInfo.setCompany(new Long(company.get(0).getId()));*/
		List<StationInfo> list = stationInfoService.selectmotherList(stationInfo);
		return getDataTable(list);
	}


	/**
	 * 电表协议获取局站列表
	 */
	@RequestMapping("/getListByAmmeter")
	@ResponseBody
	public TableDataInfo getListByAmmeter(StationInfo stationInfo,String type) {
		startPage();
		List<StationInfo> list = stationInfoService.selectListByAmmeter(stationInfo,type);
		return getDataTable(list);
	}

	/**
	 * *****验证局站
	 * <AUTHOR>
	 * @date 2022/4/26
	 */
	@RequestMapping("/getCheckListByAmmeter")
	@ResponseBody
	public TableDataInfo getCheckListByAmmeter(StationInfo stationInfo,String type) {
		startPage();
		List<StationInfo> list = stationInfoService.getCheckListByAmmeter(stationInfo,type);
		return getDataTable(list);
	}

	// 新增油机 查询局站
	@RequestMapping("/getStationListOil")
	@ResponseBody
	public TableDataInfo getStationListOil(StationInfo stationInfo) {
		if (stationInfo.getCompany() == -1) {
			stationInfo.setCompany(null);
		}
		if (stationInfo.getCountry() == -1) {
			stationInfo.setCountry(null);
		}
		startPage();
		List<StationInfo> list = stationInfoService.selectListWithArea(stationInfo);
		return getDataTable(list);
	}

	/**
	 * 获取集团LTE局站列表
	 */
	@RequestMapping("/getListLtestation")
	@ResponseBody
	public TableDataInfo getListLtestation(Ltestation ltestation) {
		startPage();
		List<Ltestation> list = stationInfoService.getListLtestation(ltestation);
		return getDataTable(list);
	}
	/**
	 * 资源局站信息
	 */
	@RequestMapping("/getResStation")
	@ResponseBody
	public TableDataInfo getresstation(StationInfo stationInfo) {
		startPage();
		/*User user = ShiroUtils.getUser();
		List<IdNameVO> company=user.getCompanies();

		String companyid = company.get(0).getId();
		//TODO test
		//companyid="12";
		stationInfo.setCompany(new Long(companyid));*/
		List<StationInfo> list = stationInfoService.getResStation(stationInfo);
		return getDataTable(list);
	}

	/**
	 * 集团5GR站址选择列表
	 */
	@RequestMapping("/get5GRList")
	@ResponseBody
	public TableDataInfo get5GRList(StationInfo stationInfo) {
		startPage();
		/*User user = ShiroUtils.getUser();
		List<IdNameVO> company=user.getCompanies();

		String companyid = company.get(0).getId();
		//TODO test
		//companyid="12";
		stationInfo.setCompany(new Long(companyid));*/
		List<StationInfo> list = stationInfoService.getResStation(stationInfo);
		return getDataTable(list);
	}
	/**
	 * 根据登陆用户id获取角色权限
	 */
	@RequestMapping("/getUserRoleAuth")
	@ResponseBody
	public List<String> getUserAuth(@RequestParam("id") String id) {
		Long idL=new Long(id);
		return stationInfoService.getUserRoleAuth(idL);
	}

	/**
	 * 查询资源系统局站和机房列表
	 */
	@RequestMapping("/getResStationAndRoom")
	@ResponseBody
	public TableDataInfo getResStationAndRoom(ResStationQueryDto queryDto) {
		startPage();
		List<StationInfo> list = stationInfoService.getResStationAndRoom(queryDto);
		return getDataTable(list);
	}

	/**
	 * 判断登陆用户是否有权限修改局站信息
	 * @return
	 */
	@RequestMapping("/isAuthEditStation")
	@ResponseBody
	public boolean isAuthEditStation() {
		User user = ShiroUtils.getUser();

		return stationInfoService.isAuthEditStation(user.getId());
	}
	/**
	 * 新增时判断局站名是否已存在
	 */
	@RequestMapping("/isstationnameexist")
	@ResponseBody
	public boolean checkIsStationnameExist(StationInfo stationInfo){
		//局站表中该名称个数
		int counts=stationInfoService.IsStationnameExist(stationInfo);
		//记录表最新记录中该名称个数
		int counts1=stationInfoService.IsStationnameExistInRecord(stationInfo);
		if(counts>0||counts1>0){
			return true;
		}
	return false;
	}
	/**
	 * 编辑时判断局站名是否已存在
	 */
	@RequestMapping("/isstationnameexisteidt")
	@ResponseBody
	public boolean checkIsStationnameExistEdit(StationInfo stationInfo){
		//局站表中除自己外该名称个数
		int counts=stationInfoService.IsStationnameExistEdit(stationInfo);
		//记录表最新记录中除自己外该名称个数
		int counts1=stationInfoService.IsStationnameExistInRecordEdit(stationInfo);
		if(counts>0||counts1>0){
			return true;
		}
		return false;
	}

	/**
	 * 新增时局站编码自动生成
	 */
	@RequestMapping("/autoCreateStationCode")
	@ResponseBody
	public Map autoCreateStationCode(){
		Map retmap=new HashMap();
		retmap.put("stationcode",stationInfoService.getStationCode());
		return retmap;
	}

	/**
	 * 电表协议获取局站列表
	 */
	@RequestMapping("/getAmmeterListByStation")
	@ResponseBody
	public TableDataInfo getAmmeterListByStation(@RequestParam("id") String id) {
		Ammeterorprotocol ammeterorprotocol=new Ammeterorprotocol();
		ammeterorprotocol.setStationcode(id);
		ammeterorprotocol.setId(null);
		startPage();
		List<Ammeterorprotocol> list = stationInfoService.getAmmeterListByStation(ammeterorprotocol);
		return getDataTable(list);
	}
	/**
	 * 查询数据是否处于代办里表中，确定能否提交流程
	 */
	@RequestMapping("/isnitodolist")
	@ResponseBody
	public List isInTodoList(@RequestParam("id") String id,Integer type) {
		startPage();
		Map<String,Object> params = new HashMap<>();
		params.put("id",id);
		params.put("type",type);
		List<StationInfo> list = stationInfoService.isInTodoList(params);
		return list;
	}
	/**
	 * 判断当前用户在该数据最后一次审批通过生效后后是否修改过，取修改的记录
	 */
	@RequestMapping("/ismodifybynowuser")
	@ResponseBody
	public int isModifiedByNowUser(@RequestParam("id") Long id) {
		User user = ShiroUtils.getUser();
		return stationInfoService.isModifiedByNowUser(id,user.getId());
	}

	/**
	 * 资站址信息
	 */
	@RequestMapping("/getStationAddr")
	@ResponseBody
	public TableDataInfo getStationAddr(StationInfo stationInfo) {
		startPage();
		List<StationInfo> list = stationInfoService.getStationAddr(stationInfo);
		return getDataTable(list);
	}
	/**
	 * 房屋信息
	 */
	@RequestMapping("/getStationHousing")
	@ResponseBody
	public TableDataInfo getStationHousing(StationInfo stationInfo) {
		startPage();
		List<StationInfo> list = stationInfoService.getStationHousing(stationInfo);
		return getDataTable(list);
	}

	/**
	 * 批量查询数据是否处于代办里表中，确定能否提交流程
	 * @param ids 业务ids（多个逗号隔开）
	 * @param type 1-电表协议 2-局站 3-定额 4-能耗应用 5-油气水 6-辽宁预算调整流程 6-异常处理流程
	 */
	@RequestMapping("/batch/isnitodolist")
	@ResponseBody
	public List bdatchIsInTodoList(@RequestParam("ids") String ids,Integer type) {
		Map<String, Object> params = new HashMap<>();
		if (StringUtils.isBlank(ids)) {
			return null;
		}
		params.put("ids", StringUtils.split(ids, ","));
		params.put("type", type);
		List<StationInfo> list = stationInfoService.isInTodoList(params);
		return list;
	}

	/**
	 * 批量停用-局站
	 */
	@PostMapping("/batch/stop")
	@ResponseBody
	public AjaxResult batchStopSave(@RequestBody StationBatchStopVo vo) {
		stationInfoService.batchStop(vo);
		return AjaxResult.success("停用成功");
	}
}
