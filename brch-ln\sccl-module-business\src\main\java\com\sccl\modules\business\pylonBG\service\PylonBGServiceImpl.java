package com.sccl.modules.business.pylonBG.service;

import com.sccl.common.lang.StringUtils;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.modules.business.account.domain.Account;
import com.sccl.modules.business.account.domain.AccountBaseResult;
import com.sccl.modules.business.account.domain.AccountCondition;
import com.sccl.modules.business.account.mapper.AccountMapper;
import com.sccl.modules.business.account.service.IAccountService;
import com.sccl.modules.business.accountbillitempre.domain.Accountbillitempre;
import com.sccl.modules.business.ammeterorprotocol.service.IAmmeterorprotocolService;
import com.sccl.modules.business.powerlumpprice.domain.PowerLumpprice;
import com.sccl.modules.business.powerlumpprice.mapper.PowerLumppriceMapper;
import com.sccl.modules.system.user.domain.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.sccl.modules.business.pylonBG.mapper.PylonBGMapper;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.sccl.modules.business.account.service.AccountServiceImpl.subMonth;

/**
 * __铁塔包干__<br/>
 * 2019/7/18
 *
 * <AUTHOR>
 */
@Service
public class PylonBGServiceImpl extends BaseServiceImpl<Account> implements IPylonBGService {

    @Autowired
    PylonBGMapper pylonBGMapper;
    @Autowired
    public AccountMapper accountMapper;
    @Autowired
    PowerLumppriceMapper lumppriceMapper;
    @Autowired
    IAccountService accountService;
    @Autowired
    IAmmeterorprotocolService ammeterorprotocolService;
    @Value("${sccl.deployTo}")
    private String deployTo;

    @Override
    public List<AccountBaseResult> selectByParams(AccountCondition condition) {

//录入台账初始数据
        Long companyId = condition.getCompany();
        Long countryId = condition.getCountry();
        User user = ShiroUtils.getUser();
        if(user != null){
            //部门组
            List<IdNameVO> departments = user.getDepartments();
            //分公司组
            List<IdNameVO> companies = user.getCompanies();
            //如果没有传入分公司和责任中心，则默认第一个
            if(companyId == null){
                companyId = companies != null && companies.size() > 0 ?Long.parseLong(companies.get(0).getId()):null;
            }
            if(countryId == null){
                countryId = departments != null && departments.size() > 0 ?Long.parseLong(departments.get(0).getId()):null;
            }
        }
        if(!"-1".equals(condition.getAccountno())){
            initPoweerAccount(condition.getAccountno(),companyId,countryId,user.getId());
        }

        condition.setCompany(companyId);
        condition.setCountry(countryId);
        List<AccountBaseResult> resultList = pylonBGMapper.selectByParams(condition);
        if(resultList != null && resultList.size() > 0){
            for (AccountBaseResult result : resultList){
                AccountCondition ifNextCondition = new AccountCondition();
                ifNextCondition.setAmmeterid(result.getAmmeterid());
                try{
                    ifNextCondition.setAccountno(result.getAccountno());
                }catch(Exception e){

                }

                Integer ifNext = pylonBGMapper.selectifNext(ifNextCondition);
                if(ifNext != null && ifNext > 0){
                    result.setIfNext(true);
                }else {
                    result.setIfNext(false);
                }
            }
        }
        return resultList;
    }

    @Override
    public Map<String,Object> updateData(List<Account> accountList) {
        int num = 0;
        String str = "";
        BigDecimal z = new BigDecimal(0);
        for(Account account : accountList){
            User user = ShiroUtils.getUser();
            if(user != null){
                //设置最后修改人
                account.setLastediterid(user.getId());
                account.setLasteditdate(new Date());
                if(account.getAccountmoney() != null && account.getAccountmoney().compareTo(new BigDecimal(0)) != 0){
                    account.setEffective(new BigDecimal(1));
                }
            }
            account.setIsnew(new BigDecimal(0));
            boolean b = true;
            BigDecimal money = accountService.selectCompletedMoney(account.getPcid());
            if(money != null && money.compareTo(z) > 0){
                if(account.getAccountmoney().compareTo(money) < 0){
                    if("sc".equals(deployTo)){
                        str+="电表/协议编号为【"+account.getAmmetercode()+"】的台账已经报账完成："+money+",台账实缴费用不能低于已经报账金额";
                    }else if("ln".equals(deployTo)){
                        str+="项目名称为【"+account.getProjectname()+"】的台账已经报账完成："+money+",台账实缴费用不能低于已经报账金额";
                    }
                    b = false;
                }
            }

            if(null != account.getRruIndex() && account.getRruIndex() == 1){
                ammeterorprotocolService.savaAndUpdateammeter(account.getAmmeterid(),account.getRru());
            }

            if(b){
                num += pylonBGMapper.update(account);
                accountMapper.deleteByAmmeterid(account.getAmmeterid());
            }

        }
        Map<String,Object> map = new HashMap<>();
        map.put("num",num);
        map.put("str",str);
        return map;
    }

    @Override
    public int deleteAccountByIds(String[] pcids) {
        return pylonBGMapper.deleteByIds(pcids);
    }

    @Override
    public void selectlumpprice(Long orgid, Long company,AccountBaseResult baseResult) {
        try{
            String startdate = baseResult.getStartdate();
            String enddate = baseResult.getEnddate();
            SimpleDateFormat formatter  = new SimpleDateFormat("yyyyMMdd");
            Date start = formatter.parse(startdate);
            Date end = formatter.parse(enddate);
            PowerLumpprice powerLumpprice = new PowerLumpprice();
            powerLumpprice.setCompany(company);
            powerLumpprice.setOrgid(orgid);
            powerLumpprice.setStartdate(start);
            powerLumpprice.setEnddate(end);
            BigDecimal lumpprice = lumppriceMapper.selectlumpprice(powerLumpprice);
            baseResult.setUnitpirce(lumpprice);
        }catch (Exception e){
            baseResult.setUnitpirce(BigDecimal.valueOf(0));
        }
    }

    @Override
    public AccountBaseResult accountTotal(AccountCondition condition) {
        Long countryId = condition.getCountry();
        Long companyId = condition.getCompany();
        User user = ShiroUtils.getUser();
        if(user != null){
            //分公司组
            List<IdNameVO> companies = user.getCompanies();
            //部门组
            List<IdNameVO> departments = user.getDepartments();
            //如果没有传入分公司和责任中心，则默认第一个
            if(companyId == null){
                companyId = companies != null && companies.size() > 0 ?Long.parseLong(companies.get(0).getId()):null;
            }
            if(countryId == null){
                countryId = departments != null && departments.size() > 0 ?Long.parseLong(departments.get(0).getId()):null;
            }
        }
        //搜索条件中加上当前登录人所属分公司
        //查询
        condition.setCompany(companyId);
        condition.setCountry(countryId);
        return pylonBGMapper.accountTotal(condition);
    }

    @Override
    public List<AccountBaseResult> selectListByPre(Accountbillitempre accountbillitempre) {
        Map<String,Object> map = new HashMap<>();
        map.put("parid",accountbillitempre.getParid());
        map.put("projectname",accountbillitempre.getProjectname());
        map.put("ammetercode",accountbillitempre.getAmmetercode());
        map.put("supplybureauammetercode",accountbillitempre.getSupplybureauammetercode());
        return pylonBGMapper.selectListByPre(map);
    }

    @Override
    public List<AccountBaseResult> selectQuery(AccountCondition condition) {
        return pylonBGMapper.selectQuery(condition);
    }

    @Override
    public AccountBaseResult queryTotal(AccountCondition condition) {
        return pylonBGMapper.queryTotal(condition);
    }

    /**
     * @Description: 初始化台账
     * @author: dongk
     * @date: 2019/7/9
     * @param:
     * @return:
     */
    private void initPoweerAccount(String accountno,Long companyId,Long countryId,Long userid){
        Account account = new Account();
        account.setAccountno(accountno);
        account.setCompany(companyId);
        account.setCountry(countryId);
        account.setInputerid(userid);
        pylonBGMapper.initBGAccount(account);
    }
}
