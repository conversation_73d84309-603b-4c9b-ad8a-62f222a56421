package com.sccl.modules.business.oilaccount.service;

import com.sccl.common.constant.enums.AccountStatusEnum;
import com.sccl.common.constant.enums.OilCategoryEnum;
import com.sccl.common.constant.enums.OilTypeEnum;
import com.sccl.common.constant.enums.TicketTaxRateTypeEnum;
import com.sccl.common.lang.StringUtils;
import com.sccl.common.utils.DateUtils;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.service.IdGenerator;
import com.sccl.framework.utils.ExcelUtil;
import com.sccl.exception.BusinessException;
import com.sccl.exception.DataException;
import com.sccl.modules.business.oilaccount.domain.OilAccount;
import com.sccl.modules.business.oilaccount.domain.OilAccountRequest;
import com.sccl.modules.business.oilaccount.mapper.OilAccountMapper;
import com.sccl.modules.common.domain.ConstantCheck;
import com.sccl.modules.system.user.domain.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 热力服务层
 * @date 2024/8/27  11:46
 */
@Service
@Slf4j
public class OilAccountServiceImpl extends BaseServiceImpl<OilAccount> implements OilAccountService {

    @Autowired
    private OilAccountMapper accountMapper;

    @Override
    public List<OilAccount> listOilAccount(OilAccountRequest request) {
        List<OilAccount> accounts;
        // 台账查询
        if (null != request.getQuery() && request.getQuery()) {
            if (request.getStartAccountNo() != null && request.getEndAccountNo() == null) {
                request.setCurrentAccountNo(DateUtils.getDate("yyyyMM"));
            }
            if (StringUtils.isEmpty(request.getStartAccountNo())) {
                request.setStartAccountNo(null);
            }
            if (StringUtils.isEmpty(request.getEndAccountNo())) {
                request.setEndAccountNo(null);
            }
            accounts = accountMapper.listOilAccountByCondition(request);
        } else {
            accounts = accountMapper.listOilAccount(request);
        }
        accounts.forEach(account -> {
            if (account.getOilAccountType() == 1) {
                account.setOilImportCategory(OilCategoryEnum.getInfoByCode(account.getOilCategory().intValue()));
                account.setOilImportType(OilTypeEnum.getInfoByCode(account.getOilType().intValue()));
                if (null !=account.getTaxRate() && TicketTaxRateTypeEnum.containTaxType(Integer.valueOf(account.getTaxRate()))) {
                    account.setTaxRateShow(TicketTaxRateTypeEnum.getInfoByCode(account.getTaxRate().intValue()).replace("%", ""));
                }
            }
            account.setTaxAmount(null != account.getTaxAmount() ? account.getTaxAmount() : BigDecimal.ZERO);
            account.setTotalMoney(account.getTicketMoney().add(account.getTaxTicketMoney()).subtract(account.getTaxAmount())
            .setScale(2, RoundingMode.HALF_UP));
            account.setAccountStatus(AccountStatusEnum.getInfoByCode(Integer.valueOf(account.getStatus())));
        });
        return accounts;
    }

    @Override
    public Map<String, Object> importOilAccount(HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        // 导入结果
        StringBuffer result = new StringBuffer();
        Map<String, Object> map = new HashMap<>();
        // 获取用户信息
        User user = ShiroUtils.getUser();
        if (null == user) {
            map.put("导入失败", "登录用户异常，请检查！！！");
            return map;
        }
        // 获取数据
        List<List<OilAccount>> lists = getOilAccountList(request, response);
        if (lists.get(0).size() == 0) {
            map.put("导入失败", "模板为空");
            return map;
        }
        // 校验数据并入库
        int i = 0;
        for (List<OilAccount> accounts: lists) {
            List<Integer> errorList = new ArrayList<>();
            for(OilAccount account : accounts) {
                i++;
                String checkResult = account.checkEmpty(true);
                if (!StringUtils.isEmpty(checkResult)) {
                    errorList.add(i);
                }
            }
            if (errorList.size() > 0) {
                result.append("第");
                errorList.forEach(t ->
                        result.append(t).append("、"));
                result.deleteCharAt(result.length() - 1).append("行数据有问题");
                throw new DataException(result.toString());
            }
            // 数据转换
            OilAccount.convertOilAccount(accounts, user);
            // 批量插入数据库
            accountMapper.insertList(accounts);
        }
        map.put("导入成功", lists.get(0).size());
        return map;
    }

    /**
     * 获取导入数据
     * @Param request  请求参数
     * @Param response 响应设置
     *
     * @return List<HeatAccount>
     */
    private List<List<OilAccount>> getOilAccountList(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 设置响应头
        response.setContentType("text/html;charset=utf-8");
        // 获取上传文件
        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        Iterator<String> iterator = multiRequest.getFileNames();
        // 返回数据
        List<List<OilAccount>> resultList = new ArrayList<>();
        while (iterator.hasNext()) {
            String name = iterator.next();
            List<MultipartFile> files;
            // 获取导入文件
            files = multiRequest.getFiles(name);
            // 一次遍历所有文件
            for (MultipartFile file : files) {
                if (file == null) {
                    continue;
                }
                //如果文件大小为0则不上传
                long fileSize = file.getSize();
                if (fileSize <= 0L) {
                    throw new BusinessException("文件名称：【" + file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("\\") + 1, file.getOriginalFilename().length()) + "】的文件大小为0,请核对!");
                }
                ExcelUtil<OilAccount> excelUtil = new ExcelUtil<>(OilAccount.class);
                Workbook workbook = WorkbookFactory.create(file.getInputStream());
                List<OilAccount> list = excelUtil.readExcelFile(OilAccount.class, workbook, 0);
                Iterator<OilAccount> it = list.iterator();
                while (it.hasNext()) {
                    OilAccount account = it.next();
                    if (account.getAccountNo().length() > 8) {
                        it.remove();
                    }
                }
                resultList.add(list);
            }
        }
        return resultList;
    }

    @Override
    public int batchAddOrUpdateOilAccount(List<OilAccount> accounts, User user) {
        int num = 0;
        if (StringUtils.isEmpty(accounts)) {
            return num;
        }
        int oilAccountType = accounts.get(0).getOilAccountType();
        // 用油台账
        if (oilAccountType == 1) {
            // 校验
            for (OilAccount account: accounts) {
                String checkResult = account.checkEmpty(false);
                if (StringUtils.isNotEmpty(checkResult)) {
                    throw new DataException(checkResult.substring(0, checkResult.length() - 1));
                }
            }
        }
        // 预付油台账
        if (oilAccountType == 2) {
            checkOilPreAccount(accounts);
        }
        // Id集合
        List<Long> ids = accounts.stream().map(OilAccount::getId).filter(id ->id != null).
                collect(Collectors.toList());
        boolean addOrUpdate = StringUtils.isEmpty(ids);
        if (addOrUpdate) {
            // 新增
            if (oilAccountType == 1) {
                accounts.forEach(account ->{
                    account.assignmentOilAccount(account, user);
                    if (StringUtils.isNotEmpty(account.getTaxRateShow())) {
                        account.setTaxRate(Integer.valueOf(account.getTaxRateShow()).byteValue());
                    }
                });
            }
            // 预付油
            if (oilAccountType == 2) {
                accounts.forEach(account ->{
                    if (StringUtils.isNotEmpty(user.getCompanies())) {
                        account.setCompany(Long.parseLong(user.getCompanies().get(0).getId()));
                    }
                    if (StringUtils.isNotEmpty(user.getDepartments())) {
                        account.setCountry(Long.parseLong(user.getDepartments().get(0).getId()));
                        account.setOrgId(Long.parseLong(user.getDepartments().get(0).getId()));
                    }
                    account.setId(IdGenerator.getNextId());
                    account.setInputerId(String.valueOf(user.getId()));
                });
            }
            num = accountMapper.insertList(accounts);
        }
        if (!addOrUpdate) {
            // 修改
            if (oilAccountType == 1) {
                accounts.forEach(account ->{
                    account.setOilType(OilTypeEnum.getCodeByType(account.getOilImportType()).byteValue());
                    account.setOilCategory(OilCategoryEnum.getCodeByType(account.getOilImportCategory()).byteValue());
                    if (StringUtils.isNotEmpty(account.getTaxRateShow())) {
                        account.setTaxRate(Integer.valueOf(account.getTaxRateShow()).byteValue());
                    }
                });
            }
            // 需要修改的数据
            List<OilAccount> updateList = new ArrayList<>(accounts.size());
            // 获取需要修改的数据
            List<OilAccount> oldList = accountMapper.selectByIds(ids);
            Map<Long, OilAccount> accountMap = oldList.stream().collect(Collectors.toMap(
                    OilAccount::getId, oilAccount -> oilAccount, (key1, key2) -> key2));
            // 前端传入对象比较判断是否有修改
            for (OilAccount oilAccount : accounts) {
                if (!OilAccount.isEqual(oilAccount, accountMap.get(oilAccount.getId()))) {
                    updateList.add(oilAccount);
                }
            }
            if (updateList.size() > 0) {
                num = accountMapper.batchUpdateOilAccount(updateList);
            }
        }
        return num;
    }

    /**
     * 预付油校验
     * @param accounts
     */
    private void checkOilPreAccount(List<OilAccount> accounts) {
        for (OilAccount account: accounts) {
            // 用能主体校验
            if (StringUtils.isEmpty(account.getOilUseBody()) || account.getOilUseBody().length() > 100) {
                throw new DataException("用能主体为空或过长，");
            }
            // 费用发生日校验
            StringBuilder result = new StringBuilder();
            ConstantCheck.feeStartDateCheck(account.getAccountNo(), account.getFeeStartDate(), result);
            if (StringUtils.isNotEmpty(result)) {
                throw new DataException(result.substring(0, result.length() - 1));
            }
            // 用量校验
            if (null == account.getOilAmount() || account.getOilAmount().compareTo(BigDecimal.ZERO) < 0) {
                throw new DataException("用油量为空或为负数，");
            }
            // 金额校验
            if (null == account.getPaidMoney() || account.getPaidMoney().compareTo(BigDecimal.ZERO) < 0) {
                throw new DataException("金额为空或为负数，");
            }
        }
    }
}
