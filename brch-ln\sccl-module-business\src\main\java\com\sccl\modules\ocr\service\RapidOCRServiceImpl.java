package com.sccl.modules.ocr.service;

import com.benjaminwan.ocrlibrary.OcrResult;
import com.benjaminwan.ocrlibrary.TextBlock;
import com.sccl.common.io.FileUtils;
import com.sccl.common.lang.StringUtils;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.ocr.utils.FileConvertUtils;
import io.github.mymonstercat.Model;
import io.github.mymonstercat.ocr.InferenceEngine;
import io.github.mymonstercat.ocr.config.ParamConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * 加载电子发票图像
 */
@Service
@Slf4j
public class RapidOCRServiceImpl implements IRapidOCRService {
    /**
     * 识别发票并获取供应商信息
     * @param file
     * @return
     */
    public String getSupplier(File file) {
        String supplier = "";
        String path = file.getPath();
        ParamConfig paramConfig = null;
        InferenceEngine engine = null;
        try {
            paramConfig = ParamConfig.getDefaultConfig();
            paramConfig.setDoAngle(true);
            paramConfig.setMostAngle(true);
            paramConfig.setMaxSideLen(1280);
            engine = InferenceEngine.getInstance(Model.ONNX_PPOCR_V4);
            OcrResult ocrResult = engine.runOcr(file.getPath(), paramConfig);
            engine = null;
            paramConfig = null;
            FileUtils.deleteFile(path);//删除已识别的临时文件
            if (ocrResult == null) {
                return "";
            }

            if (ocrResult.getTextBlocks() == null) {
                return "";
            }

            //获取销售方名称
            String[] sub = {"称：", "称:", "收款单位", "收款单位（章）：", "名称：", "名称:"};
            String regex = String.join("|", sub);
            regex = ".*(" + regex + ").*";//构建正则表达式
            List<String> suppliers = new ArrayList<>();
            for (TextBlock textBlock : ocrResult.getTextBlocks()) {
                String text = textBlock.getText();//文本块文字
                if (StringUtils.isNotBlank(text)) {
                    String replaceText = text.replaceAll("[\\n\\r\\u2028\\u2029\\u0085]+","").trim();
                    //log.info("识别文字块：{}", replaceText);
                    if (replaceText.matches(regex)) {
                        suppliers.add(replaceText);
                    }
                }
            }
            if (suppliers.size() == 1) {
                supplier = suppliers.get(0);
            } else if (suppliers.size() >= 2) {
                supplier = suppliers.get(1);//一般第一个为购买方名称，第二个才是销售方名称
            }
            //提取出销售方名称
            if (StringUtils.isNotBlank(supplier)) {
                if (supplier.contains("：")) {
                    String[] strings = StringUtils.split(supplier, "：");
                    if (strings.length >= 2) {
                        supplier = strings[1];
                    }
                } else if (supplier.contains(":")) {
                    String[] strings = StringUtils.split(supplier, ":");
                    if (strings.length >= 2) {
                        supplier = strings[1];
                    }
                } else {
                    supplier = supplier.replace("收款单位", "");
                }
            }
            supplier = supplier.trim();

        } catch (Exception e) {
            e.printStackTrace();
            log.error("发票上传异常：" + e.getMessage());
        } finally {
            paramConfig = null;
            engine = null;
        }
        return supplier;
    }

    /**
     * 识别发票并获取供应商信息
     * @param mfiles
     * @return
     */
    @Override
    public AjaxResult getSupplier(List<MultipartFile> mfiles) {
        String supplier = "";
        String supplierPre = "";//用于判断各发票的供应商名称是否一致
        int cnt = 0;
        try {
            for (MultipartFile mfile : mfiles) {
                File file = FileConvertUtils.convertMultipartFileToFile(mfile);
                supplier = this.getSupplier(file);
                if (StringUtils.isBlank(supplier)) {
                    return AjaxResult.error("发票【" + mfile.getOriginalFilename() + "】，未获取到供应商信息，请上传清晰的发票！");
                }
                if (cnt == 0) {
                    supplierPre = supplier;
                }
                if (!supplierPre.equals(supplier)) {
                    return AjaxResult.error("所有上传的发票的供应商名称不一致，请确认！");
                }
                cnt += 1;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("发票上传异常：" + e.getMessage());
            return AjaxResult.error("发票上传异常，" + e.getMessage() + "！");
        }
        if (StringUtils.isBlank(supplier)) {
            return AjaxResult.error("未获取到供应商名称，请上传清晰的发票！");
        }
        return AjaxResult.success("", supplier);
    }
}
