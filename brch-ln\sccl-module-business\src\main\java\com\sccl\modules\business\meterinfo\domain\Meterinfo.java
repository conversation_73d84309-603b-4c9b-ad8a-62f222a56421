package com.sccl.modules.business.meterinfo.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import org.apache.ibatis.type.Alias;


/**
 * 计量设备表 meterinfo
 *
 * <AUTHOR>
 * @date 2023-03-15
 */
@Alias("meterinfo")
public class Meterinfo extends BaseEntity
{
	private static final long serialVersionUID = 1L;

	/**  */
	private String provincecode;
	/**  */
	private String citycode;
	/**  */
	private String cityname;
	/**  */
	private String countycode;
	/**  */
	private String countyname;
	/**  */
	private String energymetercode;
	/**  */
	private String energymetername;
	/**  */
	private String status;
	/**  */
	private String usagecopy;
	/**  */
	private String type;
	/**  */
	private String stationcode;
	/**  */
	private String stationname;
	/**  */
	private String stationlocation;
	/**  */
	private String stationstatus;
	/**  */
	private String stationtype;
	/**  */
	private String largeindustrialelectricityflag;
	/**  */
	private String energysupplyway;
	/**  */
	private String sitecode;
	/**  */
	private String powergridenergymetercode;
	/**  */
	private String energytype;
	/**  */
	private String typestationcode;
	/**  */
	private String contractprice;
	/** 同步标志 0未同步，1同步成功 2同步失败  */
	private Integer syncflag;
	/**  */
	private String failmag;


	public void setProvincecode(String provincecode)
	{
		this.provincecode = provincecode;
	}

	public String getProvincecode()
	{
		return provincecode;
	}

	public void setCitycode(String citycode)
	{
		this.citycode = citycode;
	}

	public String getCitycode()
	{
		return citycode;
	}

	public void setCityname(String cityname)
	{
		this.cityname = cityname;
	}

	public String getCityname()
	{
		return cityname;
	}

	public void setCountycode(String countycode)
	{
		this.countycode = countycode;
	}

	public String getCountycode()
	{
		return countycode;
	}

	public void setCountyname(String countyname)
	{
		this.countyname = countyname;
	}

	public String getCountyname()
	{
		return countyname;
	}

	public void setEnergymetercode(String energymetercode)
	{
		this.energymetercode = energymetercode;
	}

	public String getEnergymetercode()
	{
		return energymetercode;
	}

	public void setEnergymetername(String energymetername)
	{
		this.energymetername = energymetername;
	}

	public String getEnergymetername()
	{
		return energymetername;
	}

	public void setStatus(String status)
	{
		this.status = status;
	}

	public String getStatus()
	{
		return status;
	}

	public void setUsagecopy(String usagecopy)
	{
		this.usagecopy = usagecopy;
	}

	public String getUsagecopy()
	{
		return usagecopy;
	}

	public void setType(String type)
	{
		this.type = type;
	}

	public String getType()
	{
		return type;
	}

	public void setStationcode(String stationcode)
	{
		this.stationcode = stationcode;
	}

	public String getStationcode()
	{
		return stationcode;
	}

	public void setStationname(String stationname)
	{
		this.stationname = stationname;
	}

	public String getStationname()
	{
		return stationname;
	}

	public void setStationlocation(String stationlocation)
	{
		this.stationlocation = stationlocation;
	}

	public String getStationlocation()
	{
		return stationlocation;
	}

	public void setStationstatus(String stationstatus)
	{
		this.stationstatus = stationstatus;
	}

	public String getStationstatus()
	{
		return stationstatus;
	}

	public void setStationtype(String stationtype)
	{
		this.stationtype = stationtype;
	}

	public String getStationtype()
	{
		return stationtype;
	}

	public void setLargeindustrialelectricityflag(String largeindustrialelectricityflag)
	{
		this.largeindustrialelectricityflag = largeindustrialelectricityflag;
	}

	public String getLargeindustrialelectricityflag()
	{
		return largeindustrialelectricityflag;
	}

	public void setEnergysupplyway(String energysupplyway)
	{
		this.energysupplyway = energysupplyway;
	}

	public String getEnergysupplyway()
	{
		return energysupplyway;
	}

	public void setSitecode(String sitecode)
	{
		this.sitecode = sitecode;
	}

	public String getSitecode()
	{
		return sitecode;
	}

	public void setPowergridenergymetercode(String powergridenergymetercode)
	{
		this.powergridenergymetercode = powergridenergymetercode;
	}

	public String getPowergridenergymetercode()
	{
		return powergridenergymetercode;
	}


	public void setEnergytype(String energytype)
	{
		this.energytype = energytype;
	}

	public String getEnergytype()
	{
		return energytype;
	}

	public void setTypestationcode(String typestationcode)
	{
		this.typestationcode = typestationcode;
	}

	public String getTypestationcode()
	{
		return typestationcode;
	}

	public void setContractprice(String contractprice)
	{
		this.contractprice = contractprice;
	}

	public String getContractprice()
	{
		return contractprice;
	}

	public void setSyncflag(Integer syncflag)
	{
		this.syncflag = syncflag;
	}

	public Integer getSyncflag()
	{
		return syncflag;
	}

	public void setFailmag(String failmag)
	{
		this.failmag = failmag;
	}

	public String getFailmag()
	{
		return failmag;
	}

	@Override
	public String toString() {
		return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
				.append("id", getId())
				.append("provincecode", getProvincecode())
				.append("citycode", getCitycode())
				.append("cityname", getCityname())
				.append("countycode", getCountycode())
				.append("countyname", getCountyname())
				.append("energymetercode", getEnergymetercode())
				.append("energymetername", getEnergymetername())
				.append("status", getStatus())
				.append("usagecopy", getUsagecopy())
				.append("type", getType())
				.append("stationcode", getStationcode())
				.append("stationname", getStationname())
				.append("stationlocation", getStationlocation())
				.append("stationstatus", getStationstatus())
				.append("stationtype", getStationtype())
				.append("largeindustrialelectricityflag", getLargeindustrialelectricityflag())
				.append("energysupplyway", getEnergysupplyway())
				.append("sitecode", getSitecode())
				.append("powergridenergymetercode", getPowergridenergymetercode())
				.append("delFlag", getDelFlag())
				.append("energytype", getEnergytype())
				.append("typestationcode", getTypestationcode())
				.append("contractprice", getContractprice())
				.append("syncflag", getSyncflag())
				.append("failmag", getFailmag())
				.toString();
	}
}
