package com.sccl.modules.business.powerappinteliread.domain;

import com.sccl.modules.system.organization.domain.Organization;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * 远程抄记录表 power_app_inteliread
 * 
 * <AUTHOR>
 * @date 2022-03-08
 */
public class PowerAppInteliread extends BaseEntity
{
	private static final long serialVersionUID = 1L;

	public Long getAmmeterid() {
		return ammeterid;
	}

	public void setAmmeterid(Long ammeterid) {
		this.ammeterid = ammeterid;
	}

	/** 关联电表id */
	private Long ammeterid;
    /** 关联台账id */
    private Long pcid;
    /** 处理人 */
    private String handlename;
    /** 处理人id */
    private Long handleid;
    /** 创建人 */
    private Long createUser;
    /** 单位 */
    private Long company;

	public Long getIntelireadid() {
		return intelireadid;
	}

	public void setIntelireadid(Long intelireadid) {
		this.intelireadid = intelireadid;
	}
	/** 抄表记录表id */
	private Long intelireadid;

	public String getCreatetime() {
		return createtime;
	}

	public void setCreatetime(String createtime) {
		this.createtime = createtime;
	}

	private String createtime;

	public String getPayperiod() {
		return payperiod;
	}

	public void setPayperiod(String payperiod) {
		this.payperiod = payperiod;
	}

	private String payperiod;

    /** 部门 */
    private Long country;

	public List<Organization> getCountrys() {
		return countrys;
	}

	public void setCountrys(List<Organization> countrys) {
		this.countrys = countrys;
	}

	private List<Organization> countrys;//责任中心 1001,1002,1003

	public String getPrevtotalreadings() {
		return prevtotalreadings;
	}

	public void setPrevtotalreadings(String prevtotalreadings) {
		this.prevtotalreadings = prevtotalreadings;
	}

	@Override
	public String getRemark() {
		return remark;
	}

	@Override
	public void setRemark(String remark) {
		this.remark = remark;
	}

	private String prevtotalreadings;
    private String remark;

	public String getStationName() {
		return stationName;
	}

	public void setStationName(String stationName) {
		this.stationName = stationName;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	private String stationName;

	public String getStationCode() {
		return stationCode;
	}

	public void setStationCode(String stationCode) {
		this.stationCode = stationCode;
	}

	private String stationCode;
    private String address;

	public Integer getBillStatus() {
		return billStatus;
	}

	public void setBillStatus(Integer billStatus) {
		this.billStatus = billStatus;
	}

	/**
	 * 单据状态  0：草稿 1：流程中 2：申请流程归档完成
	 */
	private Integer billStatus;
	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public String getCountryName() {
		return countryName;
	}

	public void setCountryName(String countryName) {
		this.countryName = countryName;
	}

	public String getAmmeterName() {
		return ammeterName;
	}

	public void setAmmeterName(String ammeterName) {
		this.ammeterName = ammeterName;
	}

	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	public String getStartdate() {
		return startdate;
	}

	public void setStartdate(String startdate) {
		this.startdate = startdate;
	}

	public String getEnddate() {
		return enddate;
	}

	public void setEnddate(String enddate) {
		this.enddate = enddate;
	}

	private String companyName;
	private String countryName;
	private String ammeterName;
	private String projectName;
	private String startdate;
	private String enddate;
	/**  */
	private String pcountryname;
	private String orgname;
	private Integer done;
	/**  */
	private Integer notdone;

	public Integer getDoned() {
		return doned;
	}

	public void setDoned(Integer doned) {
		this.doned = doned;
	}

	private Integer doned;

	public String getElectrotype() {
		return electrotype;
	}

	public void setElectrotype(String electrotype) {
		this.electrotype = electrotype;
	}

	public String getAccountmoney1() {
		return accountmoney1;
	}

	public void setAccountmoney1(String accountmoney1) {
		this.accountmoney1 = accountmoney1;
	}

	public String getEnddate1() {
		return enddate1;
	}

	public void setEnddate1(String enddate1) {
		this.enddate1 = enddate1;
	}

	public String getAccountmoney2() {
		return accountmoney2;
	}

	public void setAccountmoney2(String accountmoney2) {
		this.accountmoney2 = accountmoney2;
	}

	public String getEnddate2() {
		return enddate2;
	}

	public void setEnddate2(String enddate2) {
		this.enddate2 = enddate2;
	}

	private String electrotype;
	private String accountmoney1;
	private String enddate1;
	private String accountmoney2;
	private String enddate2;

	public String  getPcountryname() {
		return pcountryname;
	}

	public void setPcountryname(String pcountryname) {
		this.pcountryname = pcountryname;
	}

	public String getOrgname() {
		return orgname;
	}

	public void setOrgname(String orgname) {
		this.orgname = orgname;
	}



	public Integer getDone() {
		return done;
	}

	public void setDone(Integer done) {
		this.done = done;
	}

	public Integer getNotdone() {
		return notdone;
	}

	public void setNotdone(Integer notdone) {
		this.notdone = notdone;
	}





	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	private Integer status;

	public Long getProcessinstid() {
		return processinstid;
	}

	public void setProcessinstid(Long processinstid) {
		this.processinstid = processinstid;
	}

	/**
	 * 流程id
	 */
	private Long processinstid;

	public String getAccountno() {
		return accountno;
	}

	public void setAccountno(String accountno) {
		this.accountno = accountno;
	}

	private String accountno;

	public void setPcid(Long pcid)
	{
		this.pcid = pcid;
	}

	public Long getPcid() 
	{
		return pcid;
	}

	public void setHandlename(String handlename)
	{
		this.handlename = handlename;
	}

	public String getHandlename() 
	{
		return handlename;
	}

	public void setHandleid(Long handleid)
	{
		this.handleid = handleid;
	}

	public Long getHandleid() 
	{
		return handleid;
	}


	public void setCreateUser(Long createUser)
	{
		this.createUser = createUser;
	}

	public Long getCreateUser() 
	{
		return createUser;
	}

	public void setCompany(Long company)
	{
		this.company = company;
	}

	public Long getCompany() 
	{
		return company;
	}

	public void setCountry(Long country)
	{
		this.country = country;
	}

	public Long getCountry() 
	{
		return country;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("pcid", getPcid())
            .append("handlename", getHandlename())
            .append("handleid", getHandleid())
            .append("createTime", getCreateTime())
            .append("createUser", getCreateUser())
            .append("company", getCompany())
            .append("country", getCountry())
            .toString();
    }
}
