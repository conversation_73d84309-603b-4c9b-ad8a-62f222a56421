package com.sccl.modules.business.statistical.framework;

import com.sccl.common.lang.StringUtils;
import com.sccl.common.utils.DateUtils;
import com.sccl.modules.autojob.util.convert.JsonUtil;
import com.sccl.modules.business.statistical.domain.StatisticalEntity;
import com.sccl.modules.business.statistical.domain.StatisticalRedisValue;
import com.sccl.modules.business.statistical.framework.codec.StatisticalDBSerializerAndDeserializer;
import com.sccl.modules.business.statistical.framework.codec.StatisticalRedisSerializerAndDeserializer;
import com.sccl.modules.business.statistical.framework.repository.StatisticalDBRepository;
import com.sccl.modules.business.statistical.framework.repository.StatisticalRedisRepository;
import org.springframework.lang.Nullable;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 抽象统计指标处理器
 *
 * <AUTHOR>
 * @Date 2022/10/25 16:33
 */
public abstract class AbstractStatisticalIndexGroupHandler {
    /**
     * Redis存储库
     */
    public static final StatisticalRedisRepository REDIS_REPOSITORY = new StatisticalRedisRepository();
    /**
     * DB存储库
     */
    public static final StatisticalDBRepository DB_REPOSITORY = new StatisticalDBRepository();
    /**
     * DB序列化器
     */
    public static final StatisticalDBSerializerAndDeserializer DB_SERIALIZER_AND_DESERIALIZER = new StatisticalDBSerializerAndDeserializer();
    /**
     * Redis序列化器
     */
    public static final StatisticalRedisSerializerAndDeserializer REDIS_SERIALIZER_AND_DESERIALIZER = new StatisticalRedisSerializerAndDeserializer();

    /**
     * 该指标组的组名
     *
     * @return java.lang.String
     * <AUTHOR> Yongxiang
     * @date 2022/10/26 15:52
     */
    public abstract String getGroupName();

    /**
     * 该指标组的所属域
     *
     * @return java.lang.String
     * <AUTHOR> Yongxiang
     * @date 2022/10/26 16:04
     */
    public abstract String getOwnerAs();

    /**
     * 创建一个新的统计指标
     *
     * @param title           标题
     * @param content         内容
     * @param statisticalDate 统计的日期
     * @return boolean
     * <AUTHOR> Yongxiang
     * @date 2022/10/26 16:06
     */
    public boolean createNewStatisticalIndex(String title, Object content, Date statisticalDate) {
        if (StringUtils.isEmpty(title) || content == null) {
            return false;
        }
        if (!updateStatisticalIndices(title, content, statisticalDate)) {
            StatisticalIndex index = wrapperIndex(title, content, statisticalDate);
            return DB_REPOSITORY.save(Collections.singletonList(index), DB_SERIALIZER_AND_DESERIALIZER);
        }
        return true;
    }

    public StatisticalIndex wrapperIndex(String title, Object content, Date statisticalDate) {
        if (StringUtils.isEmpty(title) || content == null) {
            return null;
        }
        StatisticalIndex index = new StatisticalIndex();
        index.setTitle(title);
        index.setContentType(content.getClass());
        index.setContent(content);
        index.setGroupAlias(getGroupName());
        index.setStatisticalTime(statisticalDate == null ? new Date() : statisticalDate);
        index.setOwnerAs(getOwnerAs());
        return index;
    }

    public boolean createNewStatisticalIndex(StatisticalIndexObject indexObject) {
        return createNewStatisticalIndex(indexObject.getTitle(), indexObject.getContent(), indexObject.getStatisticalDate());
    }

    /**
     * 按照统计日期和标题查询指标
     *
     * @param queryDate  可选，日期 yyyy-MM-dd
     * @param queryTitle 可选，要查询的标题
     * @return java.util.List<com.sccl.modules.business.statistical.framework.StatisticalIndex>
     * <AUTHOR> Yongxiang
     * @date 2022/10/26 16:09
     */
    public List<StatisticalIndex> getStatisticalIndicesByDateAndTitle(Date queryDate, String queryTitle) {
        if (StringUtils.isEmpty(queryTitle) && queryDate == null) {
            return Collections.emptyList();
        }
        StatisticalEntity query = new StatisticalEntity();
        query.setStatisticalTime(queryDate);
        query.setGroupAlias(getGroupName());
        query.setTitle(queryTitle);
        query.setOwnerAs(getOwnerAs());
        return DB_REPOSITORY.query(DB_SERIALIZER_AND_DESERIALIZER, query);
    }


    /**
     * 获取该组该域下所有指标
     *
     * @return java.util.List<com.sccl.modules.business.statistical.framework.StatisticalIndex>
     * <AUTHOR> Yongxiang
     * @date 2022/10/26 16:24
     */
    public List<StatisticalIndex> getAllStatisticalIndices() {
        StatisticalEntity query = new StatisticalEntity();
        query.setGroupAlias(getGroupName());
        query.setOwnerAs(getOwnerAs());
        return DB_REPOSITORY.query(DB_SERIALIZER_AND_DESERIALIZER, query);
    }

    /**
     * 更新指标
     *
     * @param queryTitle             要更新的指标标题
     * @param updatedValue           可选，更新的值
     * @param updatedStatisticalDate 可选，统计日期
     * @return boolean
     * <AUTHOR> Yongxiang
     * @date 2022/10/26 16:19
     */
    public boolean updateStatisticalIndices(String queryTitle, @Nullable Object updatedValue, @Nullable Date updatedStatisticalDate) {
        if (StringUtils.isEmpty(queryTitle)) {
            return false;
        }
        StatisticalEntity query = new StatisticalEntity();
        query.setOwnerAs(getOwnerAs());
        query.setGroupAlias(getGroupName());
        query.setTitle(queryTitle);
        StatisticalEntity params = new StatisticalEntity();
        if (updatedValue != null) {
            params.setContent(JsonUtil.pojoToJsonString(updatedValue));
            params.setContentType(updatedValue
                    .getClass()
                    .getName());
        }
        if (updatedStatisticalDate != null) {
            params.setStatisticalTime(updatedStatisticalDate);
        }
        return DB_REPOSITORY.update(params, query);
    }

    /**
     * 删除指定标题或指定日期的指标
     *
     * @param deletedTitle    要删除的标题
     * @param statisticalDate 要删除的日期 yyyy-MM-dd，和标题同时存在时为AND
     * @return boolean
     * <AUTHOR> Yongxiang
     * @date 2022/10/26 16:21
     */
    public boolean deleteStatisticalIndices(String deletedTitle, Date statisticalDate) {
        if (StringUtils.isEmpty(deletedTitle) && statisticalDate == null) {
            return false;
        }
        StatisticalEntity query = new StatisticalEntity();
        query.setTitle(deletedTitle);
        query.setStatisticalTime(statisticalDate);
        return DB_REPOSITORY.delete(query);
    }

    /**
     * 将给定指标加载进Redis中，默认过期时间：24小时
     *
     * @param statisticalIndices 要加载进Redis的指标
     * @return boolean
     * <AUTHOR> Yongxiang
     * @date 2022/10/26 16:27
     */
    public boolean loadInRedis(List<StatisticalIndex> statisticalIndices) {
        if (CollectionUtils.isEmpty(statisticalIndices)) {
            return false;
        }
        return REDIS_REPOSITORY.save(statisticalIndices, REDIS_SERIALIZER_AND_DESERIALIZER);
    }

    public boolean loadInRedis(List<StatisticalIndex> statisticalIndices, long expiringTime, TimeUnit unit) {
        if (CollectionUtils.isEmpty(statisticalIndices)) {
            return false;
        }
        return REDIS_REPOSITORY.save(statisticalIndices, REDIS_SERIALIZER_AND_DESERIALIZER, expiringTime, unit);
    }

    /**
     * 获取该组该域在Redis中所有的指标信息
     *
     * @return java.util.List<com.sccl.modules.business.statistical.framework.StatisticalIndex>
     * <AUTHOR> Yongxiang
     * @date 2022/10/26 16:30
     */
    public List<StatisticalIndex> taskOutAllStatisticalIndices() {
        StatisticalRedisValue query = new StatisticalRedisValue();
        query.setOwnerAs(getOwnerAs());
        query.setGroupAlias(getGroupName());
        return REDIS_REPOSITORY.query(REDIS_SERIALIZER_AND_DESERIALIZER, query);
    }

    /**
     * 获取该组该域在Redis中指定统计日期、指定标题的指标信息
     *
     * @param queryDate  可选，要查询的日期
     * @param queryTitle 可选，要查询的标题
     * @return java.util.List<com.sccl.modules.business.statistical.framework.StatisticalIndex>
     * <AUTHOR> Yongxiang
     * @date 2022/10/26 16:33
     */
    public List<StatisticalIndex> takeOutStatisticalIndicesByDateAndTitle(Date queryDate, String queryTitle) {
        if (StringUtils.isEmpty(queryTitle) && queryDate == null) {
            return Collections.emptyList();
        }
        StatisticalRedisValue query = new StatisticalRedisValue();
        query.setOwnerAs(getOwnerAs());
        query.setGroupAlias(getGroupName());
        if (queryDate != null) {
            query.setStatisticalTime(DateUtils.formatDate(queryDate));
        }
        query.setTitle(queryTitle);
        return REDIS_REPOSITORY.query(REDIS_SERIALIZER_AND_DESERIALIZER, query);
    }

    /**
     * 移除Redis中该组该域的所有指标信息
     *
     * @return boolean
     * <AUTHOR> Yongxiang
     * @date 2022/10/26 16:36
     */
    public boolean removeAllStatisticalIndices() {
        StatisticalRedisValue query = new StatisticalRedisValue();
        query.setOwnerAs(getOwnerAs());
        query.setGroupAlias(getGroupName());
        return REDIS_REPOSITORY.delete(query);
    }
}
