package com.sccl.modules.business.stationaudit.pstationchangesamemeter;

import com.enrising.dcarbon.audit.*;
import com.sccl.common.utils.SpringUtil;
import com.sccl.modules.business.account.domain.Account;

import com.sccl.modules.business.stationaudit.pstationaccountchange.StationAccountChangeReferee;
import com.sccl.modules.business.stationaudit.pstationaccountchange.StationAccountChangeRefereeContent;
import com.sccl.modules.mssaccount.mssaccountbill.mapper.MssAccountbillMapper;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

public class StationChangeSameMeterCreator extends AbstractRefereeDatasourceCreator {
    @Override
    public Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> collect(Auditable auditable) {
        //数据源
        Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> datasource = new HashMap<>();

        //从Spring上下文取得mapper
        MssAccountbillMapper mapper = SpringUtil.getBean(MssAccountbillMapper.class);
        //可能上下文没有这个mapper
        if (mapper == null) {
            return null;
        }
        Account account = (Account) auditable;
        //根据 台账主键 获取对应的台账 信息
        Long pcid = account.getPcid();
        List<StationChangeSameMeterRefereeContent> nodeResults = mapper.getStationChangeSameMeter(pcid);

        //去除null数据
        nodeResults = nodeResults.stream().filter(Objects::nonNull).collect(Collectors.toList());


        //获取本次台账 对应 同一电表的 上一次 站址信息
        nodeResults.stream().forEach(
                nodeResult -> {
                    Long ammeterid = nodeResult.getAmmeterid();
                    //站址变化
                    String stationcodelast_samemeter = mapper.getStationCodeLastSamemeter(ammeterid);
                    nodeResult.setStationcodelast_samemeter(stationcodelast_samemeter);
                }
        );

        //转为auditResult 稽核对象
        List<AuditResult> auditResults = nodeResults.stream().map(
                n -> {
                    //创建 评审内容对象
                    RefereeResult refereeResult = new RefereeResult("站址变动", true, "成功");
                    n.setRefereeResult(refereeResult);
                    AuditResult auditResult = n.getAuditResult();
                    auditResult.setAuditKey(String.valueOf(n.getPcid()));
                    auditResult.setStep(6);
                    auditResult.setNodeTopic("站址变动");
                    auditResult.setRefereeMessage("站址变动");
                    return auditResult;
                }
        ).collect(Collectors.toList());
        //可以添加多种不同类型的评判数据
        //2添加到数据源
        datasource.put(StationChangeSameMeterRefereeContent.class, new ArrayList<>(auditResults));
        return datasource;
    }

    @Override
    public AbstractReferee getReferee() {
        return new StationChangeSameMeterReferee("站址变动");
    }

}
