package com.sccl.modules.mssaccount.mssinterface.domain.dateprocessing;

import com.sccl.modules.mssaccount.mssinterface.domain.CollectMeter;
import java.util.List;
import java.util.Collections;

/**
 * 日期处理结果
 * 包含最终生成的CollectMeter数据和统计信息
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
public class DateProcessingResult {
    
    /**
     * 最终生成的CollectMeter数据列表
     */
    private final List<CollectMeter> collectMeterList;
    
    /**
     * 有效天数统计
     */
    private final int validDaysCount;
    
    /**
     * 排除天数统计
     */
    private final int excludedDaysCount;
    
    /**
     * 构造函数
     * 
     * @param collectMeterList CollectMeter数据列表
     * @param validDaysCount 有效天数
     * @param excludedDaysCount 排除天数
     */
    public DateProcessingResult(List<CollectMeter> collectMeterList, int validDaysCount, int excludedDaysCount) {
        if (collectMeterList == null) {
            throw new IllegalArgumentException("CollectMeter数据列表不能为空");
        }
        if (validDaysCount < 0 || excludedDaysCount < 0) {
            throw new IllegalArgumentException("天数统计不能为负数");
        }
        
        this.collectMeterList = Collections.unmodifiableList(collectMeterList);
        this.validDaysCount = validDaysCount;
        this.excludedDaysCount = excludedDaysCount;
    }
    
    /**
     * 获取CollectMeter数据列表
     * 
     * @return CollectMeter数据列表（不可修改）
     */
    public List<CollectMeter> getCollectMeterList() {
        return collectMeterList;
    }
    
    /**
     * 获取有效天数统计
     * 
     * @return 有效天数
     */
    public int getValidDaysCount() {
        return validDaysCount;
    }
    
    /**
     * 获取排除天数统计
     * 
     * @return 排除天数
     */
    public int getExcludedDaysCount() {
        return excludedDaysCount;
    }
    
    /**
     * 获取总天数
     * 
     * @return 总天数
     */
    public int getTotalDaysCount() {
        return validDaysCount + excludedDaysCount;
    }
    
    /**
     * 获取生成的数据条数
     * 
     * @return 数据条数
     */
    public int getDataCount() {
        return collectMeterList.size();
    }
    
    /**
     * 判断是否有数据
     * 
     * @return true如果有数据，false否则
     */
    public boolean hasData() {
        return !collectMeterList.isEmpty();
    }
    
    /**
     * 判断是否有排除的天数
     * 
     * @return true如果有排除天数，false否则
     */
    public boolean hasExcludedDays() {
        return excludedDaysCount > 0;
    }
    
    /**
     * 计算有效天数占比
     * 
     * @return 有效天数占比（0.0-1.0）
     */
    public double getValidDaysRatio() {
        int totalDays = getTotalDaysCount();
        return totalDays > 0 ? (double) validDaysCount / totalDays : 0.0;
    }
    
    /**
     * 计算排除天数占比
     * 
     * @return 排除天数占比（0.0-1.0）
     */
    public double getExcludedDaysRatio() {
        int totalDays = getTotalDaysCount();
        return totalDays > 0 ? (double) excludedDaysCount / totalDays : 0.0;
    }
    
    /**
     * 获取处理摘要信息
     * 
     * @return 处理摘要
     */
    public String getSummary() {
        return String.format("处理结果：总天数=%d，有效天数=%d（%.1f%%），排除天数=%d（%.1f%%），生成数据=%d条",
            getTotalDaysCount(),
            validDaysCount, getValidDaysRatio() * 100,
            excludedDaysCount, getExcludedDaysRatio() * 100,
            getDataCount());
    }
    
    @Override
    public String toString() {
        return String.format("DateProcessingResult{dataCount=%d, validDays=%d, excludedDays=%d, totalDays=%d}",
            getDataCount(), validDaysCount, excludedDaysCount, getTotalDaysCount());
    }
}
