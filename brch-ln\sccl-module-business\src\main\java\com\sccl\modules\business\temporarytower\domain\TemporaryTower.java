package com.sccl.modules.business.temporarytower.domain;

import com.sccl.framework.web.domain.BaseEntity;

import java.util.Date;

/**
 * __铁塔临时表__<br/>
 * 2019/10/23
 *
 * <AUTHOR>
 */
public class TemporaryTower extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 省份 */
    private String provice;
    /** 地市编码 */
    private String citycode;
    /** 地市 */
    private String city;
    /** 区县 */
    private String district;
    /** 运营商 */
    private String carrier;
    /** 需求类型 */
    private String needstype;
    /** 站址编码 */
    private String stationaddrcode;
    /** 站址名称 */
    private String stationaddrname;
    /** 详细地址 */
    private String address;

    /** 服务起始日期 */
    private String servestartdate;

    /** 服务结束日期 */
    private String serveenddate;

    public String getOrderno() {
        return orderno;
    }

    public void setOrderno(String orderno) {
        this.orderno = orderno;
    }

    /** 需求单编号 */
    private String orderno;



    public String getServestartdate() {
        return servestartdate;
    }

    public void setServestartdate(String servestartdate) {
        this.servestartdate = servestartdate;
    }

    public String getServeenddate() {
        return serveenddate;
    }

    public void setServeenddate(String serveenddate) {
        this.serveenddate = serveenddate;
    }



    public String getProvice() {
        return provice;
    }

    public void setProvice(String provice) {
        this.provice = provice;
    }

    public String getCitycode() {
        return citycode;
    }

    public void setCitycode(String citycode) {
        this.citycode = citycode;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getCarrier() {
        return carrier;
    }

    public void setCarrier(String carrier) {
        this.carrier = carrier;
    }

    public String getNeedstype() {
        return needstype;
    }

    public void setNeedstype(String needstype) {
        this.needstype = needstype;
    }

    public String getStationaddrcode() {
        return stationaddrcode;
    }

    public void setStationaddrcode(String stationaddrcode) {
        this.stationaddrcode = stationaddrcode;
    }

    public String getStationaddrname() {
        return stationaddrname;
    }

    public void setStationaddrname(String stationaddrname) {
        this.stationaddrname = stationaddrname;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
}
