package com.sccl.modules.business.examine.service;

import com.sccl.modules.autojob.util.convert.StringUtils;
import com.sccl.modules.business.examine.domain.TransferExamineData;
import com.sccl.modules.business.examine.mapper.TransferExamineBaseMapper;
import com.sccl.modules.business.examine.mapper.TransferExamineDataMapper;
import com.sccl.modules.business.examine.mapper.TransferExamineMapper;
import com.sccl.modules.business.examine.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 转供电考核
 *
 * <AUTHOR>
 * @date 2025-02-12
 */
@Service
@Slf4j
public class TransferExamineServiceImpl implements ITransferExamineService {

    @Autowired
    private TransferExamineMapper transferExamineMapper;
    @Autowired
    private TransferExamineDataMapper transferExamineDataMapper;
    @Autowired
    private TransferExamineBaseMapper transferExamineBaseMapper;

    /**
     * 定时统计当月 转供电考核明细数据 定时任务调用
     * @param tjsj 统计时间【yyyy-MM】
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean statisticsAuto(String tjsj) {
        String year = StringUtils.left(tjsj, 4);
        String month = StringUtils.right(tjsj, 2);
        //先根据统计时间删除
        transferExamineDataMapper.delByTjsj(tjsj);
        //查询本月报账数据
        List<TransferExamineDataVo> list = transferExamineMapper.statisticsAuto(year, month);
        List<TransferExamineDataVo> baseData = null;
        if ("01".equals(month)) {
            //查询基础数据
            baseData = transferExamineBaseMapper.baseList();
        } else {
            //查询上一账期数据
            int intMonth = Integer.parseInt(month) - 1;
            String preTjsj = intMonth < 10 ? "-0" + intMonth : "-" + intMonth;
            preTjsj = year + preTjsj;
            baseData = transferExamineDataMapper.selectByTjsj(preTjsj);
        }
        if (baseData != null && baseData.size() > 0) {
            List<TransferExamineData> saveData = new ArrayList<>();
            for (TransferExamineDataVo data : baseData) {
                TransferExamineData entity = new TransferExamineData();
                List<TransferExamineDataVo> items = null;
                if (list != null && list.size() > 0) {
                    items = list.stream().filter(q -> data.getAmmeterid().equals(q.getAmmeterid())).collect(Collectors.toList());
                }
                if (items != null && items.size() > 0) {
                    BeanUtils.copyProperties(items.get(0), entity);
                } else {
                    BeanUtils.copyProperties(data, entity);
                    entity.setAccountPeriod(tjsj);
                }
                entity.initInsert("1");
                saveData.add(entity);
                if (saveData.size() == 1000) {
                    transferExamineDataMapper.insertList(saveData);
                    saveData.clear();
                }
            }
            if (saveData.size() > 0) {
                transferExamineDataMapper.insertList(saveData);
            }
            saveData = null;
        }
        list = null;
        baseData = null;
        return true;
    }

    /**
     * 一览
     * @param searchVo
     * @return
     */
    @Override
    public List<TransferExamineResultVo> list(TransferExamineSearchVo searchVo) {
        return transferExamineMapper.list(searchVo);
    }

    /**
     * 详情一览
     * @param searchVo
     * @return
     */
    @Override
    public List<TransferExamineXqResultVo> xqList(TransferExamineXqSearchVo searchVo) {
        return transferExamineMapper.xqList(searchVo);
    }
}
