package com.sccl.modules.business.powerintelligentinf2.mapper;

import com.sccl.modules.business.powerintelligentinf2.domain.PowerIntelligentRelate;
import com.sccl.framework.mapper.BaseMapper;

import java.util.List;
import java.util.Map;

/**
 * PUE管控详情 数据层
 * 
 * <AUTHOR>
 * @date 2019-07-18
 */
public interface PowerIntelligentRelateMapper extends BaseMapper<PowerIntelligentRelate>
{
    int deleteDetailsByMain(String[] ids);
    List<Map<String,Object>> selectByList(PowerIntelligentRelate powerIntelligentRelate);//通过主表id查询详情
}