package com.sccl.modules.business.oilcardaccount.service;

import com.sccl.common.support.Convert;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.business.energyaccount.domain.RefuelDetail;
import com.sccl.modules.business.energyaccount.mapper.EnergyAccountMapper;
import com.sccl.modules.business.oilcard.domain.OilCard;
import com.sccl.modules.business.oilcard.mapper.OilCardMapper;
import com.sccl.modules.business.oilcardaccount.domain.OilCardAccountVo;
import com.sccl.modules.business.oilcardaccount.mapper.OilCardAccountMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sccl.modules.business.oilcardaccount.domain.OilCardAccount;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;


/**
 * 油卡台账 服务层实现
 *
 * <AUTHOR>
 * @date 2021-12-20
 */
@Service
@Transactional
public class OilCardAccountServiceImpl extends BaseServiceImpl<OilCardAccount> implements IOilCardAccountService {


    @Autowired
    private OilCardAccountMapper oilCardAccountMapper;
    @Autowired
    private EnergyAccountMapper energyAccountMapper;
    @Autowired
    private OilCardMapper oilCardMapper;

    /**
     * 删除购油台账
     * 1.判断当前购油台账是否被使用
     * 2.判断是否加入归集单
     * 3.返还油卡余额
     * 4.删除台账
     *
     * <AUTHOR>
     * @date 2022/3/17
     */
    @Override
    public AjaxResult removeOilCardAccount(String ids) {
        String[] split = ids.split(",");
        for (String id : split) {
            /** 1.判断购油台账是否被使用 */
            List<RefuelDetail> details = energyAccountMapper.selectDetailbyCardId(id);
            if (details.size() != 0) {
                return AjaxResult.error(id + "该购油台账已被使用，不能删除");
            }

            /** 2.判断是否加入归集单 */
            OilCardAccount oilCardAccount = oilCardAccountMapper.queryById(id);
            if (oilCardAccount.getStatus().intValue() != 1) {
                return AjaxResult.error(oilCardAccount.getId() + "台账已加入归集单或在其他状态，不能删除");
            }
            /** 3.根据购油台账id查询购油费用
             * 返回油卡余额
             * */
            Long cardId = oilCardAccount.getCardId();//油卡id
            BigDecimal cost = oilCardAccount.getCost(); //当前台账花费
            BigDecimal balance = oilCardAccount.getBalance(); //当前油卡购油后余额
            balance = balance.add(cost);
            OilCard oilCard = new OilCard();
            oilCard.setId(cardId);
            oilCard.setResidue(balance);
            oilCardMapper.updateForModel(oilCard); //返回油卡余额
        }

        /** 删除台账 */
        int i = oilCardAccountMapper.deleteByIds(Convert.toStrArray(ids));
        if (i != 0) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.success("删除失败");
    }



/*    @Override
    public String addsave(List<OilCardAccount> oilCardAccounts) throws ParseException {
        for (OilCardAccount o : oilCardAccounts) {
            o.setCreateTime(new Date());
            if (o.getBuyTime() != null) {
                String format = new SimpleDateFormat("yyyy-MM-dd").format(o.getBuyTime());
                Date parse = new SimpleDateFormat("yyyy-MM-dd").parse(format);
                o.setBuyDate(parse);
            }
//            Optional.ofNullable(o)
//                    .filter(oilCardAccount1 -> oilCardAccount1.getBuyTime()!=null)
//                    .map(oilCardAccount1 -> {
//                        String format = new SimpleDateFormat("yyyy-MM-dd").format(o.getBuyTime());
//                        Date parse = new SimpleDateFormat("yyyy-MM-dd").parse(format);
//                        oilCardAccount1.setBuyDate(parse);
//                        return oilCardAccount1;
//
            if (o.getAccountType().equals("1")) { // 充值 更新油卡余额
                o.setBalance(o.getBalance().add(o.getCost()));
            } else if (o.getAccountType().equals("2")) { // 购油 更新油卡余额
                if (o.getBalance().compareTo(o.getCost()) == -1) {//小于
//                        return AjaxResult.error("油卡余额不足");
                    return MessageMaster.getMessage(MessageMaster.Code.OK, "油卡余额不足");
                }
                o.setBalance(o.getBalance().subtract(o.getCost()));
            }
            o.setSurplus(o.getBuyQuantity());
            //计算购油单价(保留两位小数并向上取整)
            o.setUnitprice(o.getCost().divide(o.getBuyQuantity(), 2, RoundingMode.UP));
            int n1 = insert(o);
            OilCard oilCard = new OilCard();
            oilCard.setResidue(o.getBalance());// 更新油卡余额
            oilCard.setId(o.getCardId());
            int n2 = oilCardMapper.updateForModel(oilCard);
            if (n1 <= 0 || n2 <= 0) {
//                return MessageMaster.getMessage(MessageMaster.Code.ERROR, "操作台账失败");
               // throw new OilException("操作台账失败");
            }
        }
        return MessageMaster.getMessage(MessageMaster.Code.OK, "操作台账成功");

    }

    @Override
    public boolean check(OilCardAccount oilCardAccount) {
        return false;
    }

    @Override
    public OilCardAccountVo getById(Long id) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("id", id);
        return oilCardAccountMapper.getById(map);
    }

    @Override
    public boolean updateTwo(OilCardAccount oilCardAccount) {
        int n1 = updateForModel(oilCardAccount);
        //找到原来的oilCardAccount
        Long oilCardId = Long.valueOf(oilCardAccount.getCardId());
        Long id = oilCardAccount.getId();
        OilCardAccount oilCardAccount0 = oilCardAccountMapper.selectByIdToPoJo(id);
        OilCard oilCard = oilCardMapper.selectByOilCardId(String.valueOf(oilCardId));
        //oilCard-oilCardAccount1+OilCardAccount
        if (oilCardAccount.getAccountType().equals("1")) {
            //充值
            oilCard.setResidue(
                    oilCard.getResidue().subtract(oilCardAccount0.getCost())
                            .add(oilCardAccount.getCost())
            );
        } else {
            //购油
            BigDecimal decimal = oilCard.getResidue().add(oilCardAccount0.getCost())
                    .subtract(oilCardAccount.getCost());
            if (decimal.compareTo(BigDecimal.ZERO) == -1) {
               // throw new OilException("修改金额有误，请核对后再修改");
            }
            oilCard.setResidue(decimal);
        }
        int n2 = oilCardMapper.updateForModel(oilCard);

        return (n1 > 0 && n2 > 0) ? true : false;
    }*/
}
