package com.sccl.modules.mssaccount.mssinterface.domain.dateprocessing;

import java.time.LocalDate;
import java.util.List;
import java.util.Collections;

/**
 * 日期范围分析结果
 * 包含有效日期、排除日期和统计信息
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
public class DateRangeAnalysis {
    
    /**
     * 有效日期列表（在推送范围内的日期）
     */
    private final List<LocalDate> validDates;
    
    /**
     * 排除日期列表（超出推送范围的日期）
     */
    private final List<LocalDate> excludedDates;
    
    /**
     * 总天数
     */
    private final int totalDays;
    
    /**
     * 构造函数
     * 
     * @param validDates 有效日期列表
     * @param excludedDates 排除日期列表
     * @param totalDays 总天数
     */
    public DateRangeAnalysis(List<LocalDate> validDates, List<LocalDate> excludedDates, int totalDays) {
        if (validDates == null || excludedDates == null) {
            throw new IllegalArgumentException("有效日期列表和排除日期列表不能为空");
        }
        if (totalDays < 0) {
            throw new IllegalArgumentException("总天数不能为负数");
        }
        
        this.validDates = Collections.unmodifiableList(validDates);
        this.excludedDates = Collections.unmodifiableList(excludedDates);
        this.totalDays = totalDays;
    }
    
    /**
     * 获取有效日期列表
     * 
     * @return 有效日期列表（不可修改）
     */
    public List<LocalDate> getValidDates() {
        return validDates;
    }
    
    /**
     * 获取排除日期列表
     * 
     * @return 排除日期列表（不可修改）
     */
    public List<LocalDate> getExcludedDates() {
        return excludedDates;
    }
    
    /**
     * 获取总天数
     * 
     * @return 总天数
     */
    public int getTotalDays() {
        return totalDays;
    }
    
    /**
     * 获取有效天数
     * 
     * @return 有效天数
     */
    public int getValidDaysCount() {
        return validDates.size();
    }
    
    /**
     * 获取排除天数
     * 
     * @return 排除天数
     */
    public int getExcludedDaysCount() {
        return excludedDates.size();
    }
    
    /**
     * 判断是否有有效日期
     * 
     * @return true如果有有效日期，false否则
     */
    public boolean hasValidDates() {
        return !validDates.isEmpty();
    }
    
    /**
     * 判断是否有排除日期
     * 
     * @return true如果有排除日期，false否则
     */
    public boolean hasExcludedDates() {
        return !excludedDates.isEmpty();
    }
    
    /**
     * 计算有效日期占比
     * 
     * @return 有效日期占比（0.0-1.0）
     */
    public double getValidDateRatio() {
        return totalDays > 0 ? (double) getValidDaysCount() / totalDays : 0.0;
    }
    
    /**
     * 计算排除日期占比
     * 
     * @return 排除日期占比（0.0-1.0）
     */
    public double getExcludedDateRatio() {
        return totalDays > 0 ? (double) getExcludedDaysCount() / totalDays : 0.0;
    }
    
    @Override
    public String toString() {
        return String.format("DateRangeAnalysis{totalDays=%d, validDays=%d, excludedDays=%d, validRatio=%.2f%%}", 
            totalDays, getValidDaysCount(), getExcludedDaysCount(), getValidDateRatio() * 100);
    }
}
