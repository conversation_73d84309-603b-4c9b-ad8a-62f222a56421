package com.sccl.modules.rental.rentalmodelapprove.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.rental.rentalmodelapprove.domain.RentalModelapprove;
import com.sccl.modules.rental.rentalmodelapprove.service.IRentalModelapproveService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 审批记录 信息操作处理
 * 
 * <AUTHOR>
 * @date 2019-08-23
 */
@RestController
@RequestMapping("/rental/rentalModelapprove")
public class RentalModelapproveController extends BaseController
{
    private String prefix = "rental/rentalModelapprove";
	
	@Autowired
	private IRentalModelapproveService rentalModelapproveService;
	
	@RequiresPermissions("rental:rentalModelapprove:view")
	@GetMapping()
	public String rentalModelapprove()
	{
	    return prefix + "/rentalModelapprove";
	}
	
	/**
	 * 查询审批记录列表
	 */
	@RequiresPermissions("rental:rentalModelapprove:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(RentalModelapprove rentalModelapprove)
	{
		startPage();
        List<RentalModelapprove> list = rentalModelapproveService.selectList(rentalModelapprove);
		return getDataTable(list);
	}
	
	/**
	 * 新增审批记录
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存审批记录
	 */
	@RequiresPermissions("rental:rentalModelapprove:add")
	//@Log(title = "审批记录", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody RentalModelapprove rentalModelapprove)
	{		
		return toAjax(rentalModelapproveService.insert(rentalModelapprove));
	}

	/**
	 * 修改审批记录
	 */
	@GetMapping("/edit/{pabaid}")
	public AjaxResult edit(@PathVariable("pabaid") Long pabaid)
	{
		RentalModelapprove rentalModelapprove = rentalModelapproveService.get(pabaid);

		Object object = JSONObject.toJSON(rentalModelapprove);

        return this.success(object);
	}
	
	/**
	 * 修改保存审批记录
	 */
	@RequiresPermissions("rental:rentalModelapprove:edit")
	//@Log(title = "审批记录", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody RentalModelapprove rentalModelapprove)
	{		
		return toAjax(rentalModelapproveService.update(rentalModelapprove));
	}
	
	/**
	 * 删除审批记录
	 */
	@RequiresPermissions("rental:rentalModelapprove:remove")
	//@Log(title = "审批记录", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(rentalModelapproveService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看审批记录
     */
    @RequiresPermissions("rental:rentalModelapprove:view")
    @GetMapping("/view/{pabaid}")
    @ResponseBody
    public AjaxResult view(@PathVariable("pabaid") Long pabaid)
    {
		RentalModelapprove rentalModelapprove = rentalModelapproveService.get(pabaid);

        Object object = JSONObject.toJSON(rentalModelapprove);

        return this.success(object);
    }

}
