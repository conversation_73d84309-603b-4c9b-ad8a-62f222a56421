package com.sccl.modules.business.stationaudit.pstationgrade;

import com.enrising.dcarbon.audit.*;
import com.sccl.common.utils.SpringUtil;
import com.sccl.modules.business.account.domain.Account;
import com.sccl.modules.business.basestation.service.IStationGradeService;

import com.sccl.modules.business.stationaudit.pstationempty.StationEmptyRefereeContent;
import com.sccl.modules.business.stationequipment.domain.StationEquipment;
import com.sccl.modules.business.stationequipment.mapper.StationEquipmentMapper;
import com.sccl.modules.business.stationequipment.service.IStationEquipmentService;
import com.sccl.modules.mssaccount.mssaccountbill.mapper.MssAccountbillMapper;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

public class StaionGradeExCreator extends AbstractRefereeDatasourceCreator {
    @Override
    public Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> collect(Auditable auditable) {
        //数据源
        Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> datasource = new HashMap<>();

        //从Spring上下文取得mapper
        MssAccountbillMapper mapper = SpringUtil.getBean(MssAccountbillMapper.class);
        //可能上下文没有这个mapper
        if (mapper == null) {
            return null;
        }
        Account account = (Account) auditable;
        Long billId = account.getBillId();
        //根据 台账主键 获取对应的台账 信息
        Long pcid = account.getPcid();
        List<StationGradeExRefereeContent> nodeResults = mapper.getPowerHistoryOneNew(pcid);

        //去除null数据
        nodeResults = nodeResults.stream().filter(Objects::nonNull).collect(Collectors.toList());


        //对每条台账数据进行评判
        //1.1 事前站址设备mpper
        StationEquipmentMapper stationEquipmentMapper = SpringUtil.getBean(StationEquipmentMapper.class);

        nodeResults.stream().forEach(
                nodeResult -> {
                    //1.2 拿到对应站址的的站址设备模型
                    String stationCode = nodeResult.getStationCode();

                    List<StationEquipment> stationEquipments =
                            stationEquipmentMapper.selectListForStationId(stationCode,billId);
                    //1.3 去重 （站址，设备厂家，设备型号，设备类型）
                    TreeSet<StationEquipment> set =
                            new TreeSet<>((Comparator.comparing(o -> o.getFactory() + o.getModel() + o.getType())));
                    set.addAll(stationEquipments);
                    //1.4开始评级
                    nodeResult.setGid(1L);
                    nodeResult.setBillId(account.getBillId());
                    //1.4.1 set标准功率
                    Integer diff = nodeResult.getDiff();
                    IStationEquipmentService stationEquipmentService =
                            SpringUtil.getBean(IStationEquipmentService.class);
                    nodeResult.setStandardpower(stationEquipmentService.caclStandardBefore(set, diff));
                    //1.4.2 进行评级
                    IStationGradeService gradeService = SpringUtil.getBean(IStationGradeService.class);
                    String gradeStr = gradeService.processGrade(nodeResult);

                    nodeResult.setEvaluate(gradeStr);
                    //1.4.3 计算实际偏离标准 百分比
                    if (nodeResult.getStandardpower().compareTo(BigDecimal.ZERO) != 0) {
                        BigDecimal wide = nodeResult.getPower().subtract(nodeResult.getStandardpower()).divide(
                                nodeResult.getStandardpower(), 2
                        ).multiply(BigDecimal.valueOf(100));

                        nodeResult.setWide(wide);
                    } else {
                        nodeResult.setWide(null);
                    }
                }
        );

        //转为auditResult 稽核对象
        List<AuditResult> auditResults = nodeResults.stream().map(
                n -> {
                    //创建 评审内容对象
                    RefereeResult refereeResult = new RefereeResult("站址分级", true, "成功");
                    n.setRefereeResult(refereeResult);
                    AuditResult auditResult = n.getAuditResult();
                    auditResult.setAuditKey(String.valueOf(n.getPcid()));
                    auditResult.setStep(2);
                    auditResult.setNodeTopic("站址评级");
                    auditResult.setRefereeMessage("站址评级已出");
                    return auditResult;
                }
        ).collect(Collectors.toList());
        //可以添加多种不同类型的评判数据
        //2添加到数据源
        datasource.put(StationGradeExRefereeContent.class, new ArrayList<>(auditResults));
        return datasource;
    }

    @Override
    public AbstractReferee getReferee() {
        return new StationGradeExReferee("站址评级");
    }

}
