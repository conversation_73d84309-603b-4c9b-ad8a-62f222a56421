package com.sccl.modules.business.powermodel.entity;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.io.Serializable;

/**
 * 电量模型信息(PowerModleInfo)实体类
 *
 * <AUTHOR>
 * @since 2022-10-20 11:04:35
 */
@Slf4j
@Data
public class PowerModleInfo implements Serializable {
    private static final long serialVersionUID = -63488215090115396L;
    /**
     * 用户名称
     */
    private String username;
    /**
     * 户号
     */
    private String accountno;
    /**
     * 售电公司
     */
    private String elesellcompany;
    /**
     * 地市级公司
     */
    private String citycompany;
    /**
     * 县级公司
     */
    private String countylevelcompany;
    /**
     * 电压等级
     */
    private Integer voltagelevel;
    /**
     * 结算类型
     */
    private String sttype;
    /**
     * 用户类别
     */
    private String ueertype;
    /**
     * 年
     */
    private Integer year;
    /**
     * 月
     */
    private Integer month;
    /**
     * 用电量
     */
    private Long powercSize;
    /**
     * 工业电量
     */
    private Long powercIndustrySize;
    /**
     * 非工业电量
     */
    private Long powercIndustrySizeNot;
    /**
     * 富余基数
     */
    private Long surplusBase;
    /**
     * 总结算电量(常规直购)
     */
    private Long powerTotalSize;
    /**
     * 总结算均价(常规直购)
     */
    private BigDecimal averagepriceTotal;
    /**
     * 总结算电量(精准长协)
     */
    private Long powerTotalSize2;
    /**
     * 总结算均价(精准长协)
     */
    private Long averagepriceTotal2;
    /**
     * 总结算电量(富余电量)
     */
    private Long powerTotalSize3;
    /**
     * 总结算均价(富余电量)
     */
    private Long averagepriceTotal3;
    /**
     * 合同电量
     */
    private Long powerContract;

    /**
     * 国网代购价
     */
    private BigDecimal indirectCountry;


    /**
     * 火电价格
     */
    private BigDecimal priceThermalpower;
    /**
     * 输配电价
     */
    private BigDecimal priceSp;
    /**
     * 损益
     */
    private BigDecimal profitLoss;
    /**
     * 基金附加费
     */
    private BigDecimal appendFund;
    /**
     * 大工业容量/需量
     */
    private BigDecimal capacityDemandBig;
    /**
     * 大工业基本电费折算单价
     */
    private BigDecimal priceDiscountBig;
    /**
     * 峰电量1.6
     */
    private Long powerHigh;
    /**
     * 平电量1
     */
    private Long powerMiddle;
    /**
     * 谷电量0.4
     */
    private Long powerLow;
    /**
     * 水电合同价
     */
    private BigDecimal contractHydropower;
    /**
     * 水电合同电量（度）
     */
    private Long powerContractHydropower;
    /**
     * 火电合同电量（度）
     */
    private Long powerContractThermalpower;
    /**
     * 水电浮动价
     */
    private BigDecimal priceContractHydropower;
    /**
     * 实际水电电量
     */
    private Long powerHydropowerCalc;
    /**
     * 实际火电电量
     */
    private Long powerThermalpowerCalc;
    /**
     * 浮动水电电量
     */
    private Long powerHydropowerChange;
    /**
     * 理论与实际的差值（分合同内外，不分峰平谷）
     */
    private BigDecimal diffTheoryActual;
    /**
     * 理论与实际的差值（不分合同内外、不分峰平谷）
     */
    private BigDecimal diffTheoryActual2;
    /**
     * 直购电与国网（不分合同内外、分峰平谷电价差）
     */
    private BigDecimal directpureleStategrid;
    /**
     * 直购电交易电价与国网代购价差（不分峰平谷、不浮动）
     */
    private BigDecimal diffDirectpureleStategrid;
    /**
     * 直购电实际（含富余、并分峰平谷）与国网代购分峰平谷的差值
     */
    private BigDecimal diffDirectpureleStategrid2;
    /**
     * 直购电理论电价测算（不分合同内外、不含富余、分峰平谷）
     */
    private BigDecimal priceDirectpureleEstimate;
    /**
     * 直购电理论电价测算（不分合同内外、不含富余、分峰平谷）与直购电实际（含富余、并分峰平谷）的差值
     */
    private BigDecimal diffPriceDirectpureleEstimate;
    /**
     * 国网代购测算值单价
     */
    private BigDecimal priceCountryProxy;
    /**
     * 国网代购分峰平谷单价
     */
    private BigDecimal priceCountryProxyTimeshar;
    /**
     * 电价代购峰值
     */
    private BigDecimal priceCountryProxyHigh;
    /**
     * 电价代购平值
     */
    private BigDecimal priceCountryProxyMid;
    /**
     * 电价代购谷值
     */
    private BigDecimal priceCountryProxyLow;
    /**
     * 直购电(实际)
     */
    private BigDecimal priceDirectpurele;
    /**
     * 直购电实际（含富余、并分峰平谷）
     */
    private BigDecimal priceDirectpurele2;
    /**
     * 直购电理论值电价测算（分合同、浮动）
     */
    private BigDecimal priceDirectpureleTheoryEstimate;
    /**
     * 直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余）
     */
    private BigDecimal priceDirectpureleTheoryEstimate2;
    /**
     * 直购电理论电价测算（不分合同内外、不含富余、分峰平谷）
     */
    private BigDecimal priceDirectpureleTheoryEstimate3;




}

