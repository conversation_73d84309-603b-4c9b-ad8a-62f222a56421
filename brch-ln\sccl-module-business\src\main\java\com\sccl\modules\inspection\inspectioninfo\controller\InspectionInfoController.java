package com.sccl.modules.inspection.inspectioninfo.controller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.modules.system.user.domain.User;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.inspection.inspectioninfo.domain.InspectionInfo;
import com.sccl.modules.inspection.inspectioninfo.service.IInspectionInfoService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 巡检记录 信息操作处理
 * 
 * <AUTHOR>
 * @date 2019-05-27
 */
@RestController
@RequestMapping("/inspection/inspectionInfo")
public class InspectionInfoController extends BaseController
{
    private String prefix = "inspection/inspectionInfo";
	
	@Autowired
	private IInspectionInfoService inspectionInfoService;
	
	@RequiresPermissions("inspection:inspectionInfo:view")
	@GetMapping()
	public String inspectionInfo()
	{
	    return prefix + "/inspectionInfo";
	}
	
	/**
	 * 查询巡检记录列表
	 */
	@RequiresPermissions("inspection:inspectionInfo:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(InspectionInfo inspectionInfo)
	{
		//如果分公司为空，初始化以登陆用户分公司查询
		if(inspectionInfo.getCompany()==null||"".equals(inspectionInfo.getCompany())){
			User user = ShiroUtils.getUser();
			inspectionInfo.setCompany(new BigDecimal(user.getCompanies().get(0).getId()));
		}
		startPage();
        List<InspectionInfo> list = inspectionInfoService.selectListByCondition(inspectionInfo);
		return getDataTable(list);
	}
	
	/**
	 * 新增巡检记录
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存巡检记录
	 */
	@RequiresPermissions("inspection:inspectionInfo:add")
	//@Log(title = "巡检记录", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody InspectionInfo inspectionInfo)
	{		
		return toAjax(inspectionInfoService.insert(inspectionInfo));
	}

	/**
	 * 修改巡检记录
	 */
	/*@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") ${primaryKey.attrType} id)
	{
		InspectionInfo inspectionInfo = inspectionInfoService.get(id);

		Object object = JSONObject.toJSON(inspectionInfo);

        return this.success(object);
	}*/
	
	/**
	 * 修改保存巡检记录
	 */
	//@RequiresPermissions("inspection:inspectionInfo:edit")
	//@Log(title = "巡检记录", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody List<InspectionInfo> inspectionInfoobj)
	{		
		AjaxResult ar=new AjaxResult();
		try{
			ar.put("data",inspectionInfoService.update(inspectionInfoobj));
			//alertControlService.updateMatchs(alertObj);
			ar.put("flag","1");
			ar.put("msg","保存成功");
		}catch(Exception e){
			e.printStackTrace();
			ar.put("flag","0");
			ar.put("msg","保存失败"+e.getMessage());

		}
		return ar;
	}
	
	/**
	 * 删除巡检记录
	 */
	@RequiresPermissions("inspection:inspectionInfo:remove")
	//@Log(title = "巡检记录", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(inspectionInfoService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看巡检记录
     */
   /* @RequiresPermissions("inspection:inspectionInfo:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") ${primaryKey.attrType} id)
    {
		InspectionInfo inspectionInfo = inspectionInfoService.get(id);

        Object object = JSONObject.toJSON(inspectionInfo);

        return this.success(object);
    }*/
   //获取当前登陆用户信息
	@RequestMapping("/user")
	@ResponseBody
	public User compnyList() {
		User user = ShiroUtils.getUser();
		return user;
	}

	@RequestMapping("/allaccountno")
	@ResponseBody
	public List getAllAccountno(){
		List retList=new ArrayList();
		User user = ShiroUtils.getUser();
		List tempList=inspectionInfoService.getAllAccountno(user.getCompanies().get(0).getId());
		for(int i=0;i<tempList.size();i++){
			Map tempmap=new HashMap();
			tempmap.put("accountno",tempList.get(i));
			retList.add(tempmap);
		}
		return retList;

	}
}
