package com.sccl.modules.business.stationaudit.pstationmeterchangesamecode;

import com.enrising.dcarbon.audit.*;
import com.sccl.common.utils.SpringUtil;
import com.sccl.modules.business.account.domain.Account;

import com.sccl.modules.mssaccount.mssaccountbill.mapper.MssAccountbillMapper;

import java.util.*;
import java.util.stream.Collectors;

public class StationMeterChangeSameCodeCreator extends AbstractRefereeDatasourceCreator {
    @Override
    public Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> collect(Auditable auditable) {
        //数据源
        Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> datasource = new HashMap<>();

        //从Spring上下文取得mapper
        MssAccountbillMapper mapper = SpringUtil.getBean(MssAccountbillMapper.class);
        //可能上下文没有这个mapper
        if (mapper == null) {
            return null;
        }
        Account account = (Account) auditable;
        //根据 台账主键 获取对应的台账 信息
        Long pcid = account.getPcid();
        List<StationMeterChangeSameCodeRefereeContent> nodeResults = mapper.getMeterChangeSameCode(pcid);

        //去除null数据
        nodeResults = nodeResults.stream().filter(Objects::nonNull).collect(Collectors.toList());


        //获取本次台账 对应 同一电表的 上一次 站址信息
        nodeResults.stream().forEach(
                nodeResult -> {
                    String stationcode = nodeResult.getStationCode();
                    //电表变化
                    String meterlast_samestationcode = mapper.getMeterLastSameStationCode(stationcode, pcid );
                    nodeResult.setMeterlast_samestationcode(meterlast_samestationcode);

                }
        );

        //转为auditResult 稽核对象
        List<AuditResult> auditResults = nodeResults.stream().map(
                n -> {
                    //创建 评审内容对象
                    RefereeResult refereeResult = new RefereeResult("电表变动", true, "成功");
                    n.setRefereeResult(refereeResult);
                    AuditResult auditResult = n.getAuditResult();
                    auditResult.setAuditKey(String.valueOf(n.getPcid()));
                    auditResult.setStep(7);
                    auditResult.setNodeTopic("电表变动");
                    auditResult.setRefereeMessage("电表变动结果");
                    return auditResult;
                }
        ).collect(Collectors.toList());
        //可以添加多种不同类型的评判数据
        //2添加到数据源
        datasource.put(StationMeterChangeSameCodeRefereeContent.class, new ArrayList<>(auditResults));
        return datasource;
    }

    @Override
    public AbstractReferee getReferee() {
        return new StationMeterChangeSameCodeReferee("电表变动");
    }


}
