package com.sccl.modules.business.stationreportwhitelist.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.io.Serializable;
import java.util.Date;


/**
 * <AUTHOR>
 * @date 2024/4/9 16:23
 * @describe 一站多表白名单
 */
@Getter
@Setter
@TableName(value = "station_report_whitelist")
public class StationReportWhitelist extends Model<StationReportWhitelist> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;
    /**
     * 地市
     */
    private String city;
    /**
     * 区县
     */
    private String district;
    /**
     * 电表表号
     */
    private String meterCode;
    /**
     * 户号
     */
    private String payAccountNum;
    /**
     * 对外结算类型
     */
    private String dwjslx;
    /**
     * 转供电协议单价
     */
    private Double zgdxydj;
    /**
     * 协议启动日期
     */
    private Date xyCreateTime;
    /**
     * 协议终止时间
     */
    private Date xyEndTime;
    /**
     * 关联站址数目
     */
    private String glzz;
    /**
     * 站址类型
     */
    private String zzlx;
    /**
     * 主报账站址编码
     */
    private String zbzzzbm;
    /**
     * 主报账站址名称
     */
    private String zbzzzmc;
    /**
     * 电信是否起租
     */
    private String dxsfqz;
    /**
     * 打包报账站址编码
     */
    private String dbbzzzbm;
    /**
     * 打包报账站址与主报账站址重复
     */
    private String dbbzzzyzbzzzcf;
    /**
     * 共享运营商名称
     */
    private String gxyysmc;
    /**
     * 打包报账站址名称
     */
    private String dbbzzzmc;
    /**
     * 站址地址
     */
    private String zzdz;
    /**
     * 附件
     */
    private String fj;
    /**
     * 是否推送分公司
     */
    private String sftsfgs;
    /**
     * 分公司审核人
     */
    private String fgsshr;
    /**
     * 是否推送省公司
     */
    private String sftssgs;
    /**
     * 省公司审核人
     */
    private String sgsshr;
    /**
     * 是否推送电信
     */
    private String sftsdx;
    /**
     * 推送电信人
     */
    private String tsdxr;
    /**
     * 推送电信时间
     */
    private String tsdxsj;
    /**
     * 电信稽核
     */
    private String dxjh;
    /**
     * 稽核时间
     */
    private String jhsj;
    /**
     * 是否电信请求修改
     */
    private String sfdxqqxg;
    /**
     * 是否同意电信修改
     */
    private String sftydxxg;
    /**
     * 系统校验
     */
    private String xtjy;
    /**
     * 白名单类型 1:一表多站 2:一站多表 3:单价
     */
    private String type;
    /**
     * 录入时间
     */
    private Date createTime;
    /**
     * 删除标识 0-未删除 1-已删除
     */
    private Integer delFlag;

    /**
     * 流程单据id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long billId;

    /**
     * 电表id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long meterId;

    /**
     * 流程单据状态
     */
    private Integer billStatus;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
