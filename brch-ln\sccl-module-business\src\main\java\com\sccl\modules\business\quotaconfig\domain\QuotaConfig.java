package com.sccl.modules.business.quotaconfig.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.math.BigDecimal;
import java.util.List;


/**
 * 基站定额分公司设置表 station_quota_config
 * 
 * <AUTHOR>
 * @date 2023-06-07
 */
public class QuotaConfig extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
    /** 单位 */
    private Long company;
    /** 部门 */
    private Long country;
    /** 定额类型：1：公司自定义定额；2：无限大数据指标；3：分摊比例；4：历史波动 */
    private Integer type;
    /** 站址编码 */
    private String resstationcode;
    /** 站址名称 */
    private String resstationname;
    /** 定额值 */
    private BigDecimal quotaValue;
    /** 上下限 */
    private BigDecimal percent;

	public List<String> getListIds() {
		return listIds;
	}

	public void setListIds(List<String> listIds) {
		this.listIds = listIds;
	}

	public List<String> getCountryIds() {
		return countryIds;
	}

	public void setCountryIds(List<String> countryIds) {
		this.countryIds = countryIds;
	}

	private List<String> listIds;
	private List<String> countryIds;

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public String getCountryName() {
		return countryName;
	}

	public void setCountryName(String countryName) {
		this.countryName = countryName;
	}

	/** 所属分公司名称*/
	private String companyName;
	/** 责任中心名称*/
	private String countryName;
	public void setCompany(Long company)
	{
		this.company = company;
	}

	public Long getCompany() 
	{
		return company;
	}

	public void setCountry(Long country)
	{
		this.country = country;
	}

	public Long getCountry() 
	{
		return country;
	}

	public void setType(Integer type)
	{
		this.type = type;
	}

	public Integer getType() 
	{
		return type;
	}

	public void setResstationcode(String resstationcode)
	{
		this.resstationcode = resstationcode;
	}

	public String getResstationcode() 
	{
		return resstationcode;
	}

	public void setResstationname(String resstationname)
	{
		this.resstationname = resstationname;
	}

	public String getResstationname() 
	{
		return resstationname;
	}

	public void setQuotaValue(BigDecimal quotaValue)
	{
		this.quotaValue = quotaValue;
	}

	public BigDecimal getQuotaValue() 
	{
		return quotaValue;
	}

	public void setPercent(BigDecimal percent)
	{
		this.percent = percent;
	}

	public BigDecimal getPercent() 
	{
		return percent;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("company", getCompany())
            .append("country", getCountry())
            .append("type", getType())
            .append("resstationcode", getResstationcode())
            .append("resstationname", getResstationname())
            .append("quotaValue", getQuotaValue())
            .append("percent", getPercent())
            .toString();
    }
}
