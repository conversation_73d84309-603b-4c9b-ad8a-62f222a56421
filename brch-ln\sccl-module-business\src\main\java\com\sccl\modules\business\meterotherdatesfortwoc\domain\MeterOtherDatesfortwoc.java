package com.sccl.modules.business.meterotherdatesfortwoc.domain;

import com.sccl.modules.business.twoc.domain.TwoCFlag;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;


/**
 * 双碳接口其它能耗数据表 meter_other_datesfortwoc
 * 
 * <AUTHOR>
 * @date 2023-04-17
 */
public class MeterOtherDatesfortwoc extends BaseEntity implements TwoCFlag
{
	private static final long serialVersionUID = 1L;
	
    /** 地市编码 */
    private String subjectcode;
    /** 用能月份 YYYYMM */
    private String statisperiod;
    /** 省份编码 */
    private String provincecode;
    /** 市局组织编码 */
    private String citycode;
    /** 市局组织名称 */
    private String cityname;
    /** 区县组织名称 */
    private String countycode;
    /** 区县组织名称 */
    private String countyname;
    /** 归属类型 1:集团存续 2:股份上市 */
    private String grouptype;
    /** 局站类型 */
    private String stationtype;
    /** 能耗类型 */
    private String energytype;
    /** 财辅系统报账单号 */
    private String billcode;
    /** 消耗量 */
    private String energyconsumption;
    /** 创建时间 */
    private Date createtime;
    /** 同步标志 0未同步，1同步成功 2同步失败  */
    private Integer syncflag;
    /**  */
    private String failmag;


	public void setSubjectcode(String subjectcode)
	{
		this.subjectcode = subjectcode;
	}

	public String getSubjectcode() 
	{
		return subjectcode;
	}

	public void setStatisperiod(String statisperiod)
	{
		this.statisperiod = statisperiod;
	}

	public String getStatisperiod() 
	{
		return statisperiod;
	}

	public void setProvincecode(String provincecode)
	{
		this.provincecode = provincecode;
	}

	public String getProvincecode() 
	{
		return provincecode;
	}

	public void setCitycode(String citycode)
	{
		this.citycode = citycode;
	}

	public String getCitycode() 
	{
		return citycode;
	}

	public void setCityname(String cityname)
	{
		this.cityname = cityname;
	}

	public String getCityname() 
	{
		return cityname;
	}

	public void setCountycode(String countycode)
	{
		this.countycode = countycode;
	}

	public String getCountycode() 
	{
		return countycode;
	}

	public void setCountyname(String countyname)
	{
		this.countyname = countyname;
	}

	public String getCountyname() 
	{
		return countyname;
	}

	public void setGrouptype(String grouptype)
	{
		this.grouptype = grouptype;
	}

	public String getGrouptype() 
	{
		return grouptype;
	}

	public void setStationtype(String stationtype)
	{
		this.stationtype = stationtype;
	}

	public String getStationtype() 
	{
		return stationtype;
	}

	public void setEnergytype(String energytype)
	{
		this.energytype = energytype;
	}

	public String getEnergytype() 
	{
		return energytype;
	}

	public void setBillcode(String billcode)
	{
		this.billcode = billcode;
	}

	public String getBillcode() 
	{
		return billcode;
	}

	public void setEnergyconsumption(String energyconsumption)
	{
		this.energyconsumption = energyconsumption;
	}

	public String getEnergyconsumption() 
	{
		return energyconsumption;
	}


	public void setCreatetime(Date createtime)
	{
		this.createtime = createtime;
	}

	public Date getCreatetime() 
	{
		return createtime;
	}

	public void setSyncflag(Integer syncflag)
	{
		this.syncflag = syncflag;
	}

	public Integer getSyncflag() 
	{
		return syncflag;
	}

	public void setFailmag(String failmag)
	{
		this.failmag = failmag;
	}

	public String getFailmag() 
	{
		return failmag;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("subjectcode", getSubjectcode())
            .append("statisperiod", getStatisperiod())
            .append("provincecode", getProvincecode())
            .append("citycode", getCitycode())
            .append("cityname", getCityname())
            .append("countycode", getCountycode())
            .append("countyname", getCountyname())
            .append("grouptype", getGrouptype())
            .append("stationtype", getStationtype())
            .append("energytype", getEnergytype())
            .append("billcode", getBillcode())
            .append("energyconsumption", getEnergyconsumption())
            .append("delFlag", getDelFlag())
            .append("createtime", getCreatetime())
            .append("syncflag", getSyncflag())
            .append("failmag", getFailmag())
            .toString();
    }
}
