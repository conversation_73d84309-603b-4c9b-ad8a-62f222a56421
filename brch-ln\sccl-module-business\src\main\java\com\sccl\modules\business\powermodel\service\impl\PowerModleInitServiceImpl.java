package com.sccl.modules.business.powermodel.service.impl;

import com.sccl.common.utils.ShiroUtils;
import com.sccl.common.utils.TimeUtils;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.modules.autojob.util.bean.ObjectUtil;
import com.sccl.modules.autojob.util.convert.StringUtils;
import com.sccl.modules.business.account.domain.Account;
import com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol;
import com.sccl.modules.business.modelegetprice.domain.ModeleGetprice;
import com.sccl.modules.business.modelegetprice.service.IModeleGetpriceService;
import com.sccl.modules.business.modlebigandwork.domain.ModleBigandwork;
import com.sccl.modules.business.modlebigandwork.mapper.ModleBigandworkMapper;
import com.sccl.modules.business.modlepricesp.domain.ModlePriceSp2;
import com.sccl.modules.business.modlepricesp.mapper.ModlePricespMapper;
import com.sccl.modules.business.powermodel.entity.BigWork;
import com.sccl.modules.business.powermodel.entity.PowerModleInit;
import com.sccl.modules.business.powermodel.entity.SupplyCodeMapBill;
import com.sccl.modules.business.powermodel.mapper.PowerModleInitMapper;
import com.sccl.modules.business.powermodel.service.PowerModleInitService;
import com.sccl.modules.business.powermodel.util.OptPro;
import com.sccl.modules.system.role.domain.Role;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.service.IUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static java.util.stream.Collectors.*;

/**
 * 电量模型原始数据(PowerModleInit)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-10-20 10:44:40
 */
@Service
@Slf4j
public class PowerModleInitServiceImpl extends BaseServiceImpl<PowerModleInit> implements PowerModleInitService {

    @Autowired
    private IUserService userService;
    @Autowired(required = false)
    private PowerModleInitMapper powerModleInitMapper;
    @Autowired
    private IModeleGetpriceService modeleGetpriceService;
    @Autowired
    private ModlePricespMapper pricespMapper;
    @Autowired
    private ModleBigandworkMapper bigandworkMapper;

    /**
     * 快速测试
     *
     * @param args
     */
    public static void main(String[] args) throws ParseException {
        BigDecimal one = new BigDecimal(1);
        BigDecimal two = new BigDecimal(1);
        BigDecimal threee = new BigDecimal(2);
        System.out.println(one.add(two).multiply(threee));
        System.out.println((one.add(two)).multiply(threee));

    }

    private static Integer calc(Integer a1, Integer a2, int divider) {
        try {
            return a1 + a2 / divider;
        } catch (Exception e) {
            System.out.println("出异常了");
        }
        return 0;
    }

    /**
     * 新增数据
     *
     * @param powerModleInit 实例对象
     * @return 实例对象
     */
//    @Override
//    public PowerModleInit insert(PowerModleInit powerModleInit) {
//        this.powerModleInitMapper.insert(powerModleInit);
//        return powerModleInit;
//    }
//
//    /**
//     * 修改数据
//     *
//     * @param powerModleInit 实例对象
//     * @return 实例对象
//     */
//    @Override
//    public PowerModleInit update(PowerModleInit powerModleInit) {
//        this.powerModleInitMapper.update(powerModleInit);
//        return this.queryById(powerModleInit.getId());
//    }
    private static boolean TimeMatch(Account content, Integer year, Integer month) {
        String startdate = content.getStartdate();
        String enddate = content.getEnddate();

        String endday = TimeUtils.getLastDayOfMonth(year, month);
        String firstday = "" + year + (month.intValue() <= 9 ? "0" + month : month) + "01";

        Date ts = null;
        Date td = null;
        Date tf = null;
        Date tend = null;
        try {
            ts = new SimpleDateFormat("yyyyMMdd").parse(startdate);
            td = new SimpleDateFormat("yyyyMMdd").parse(enddate);
            tf = new SimpleDateFormat("yyyyMMdd").parse(firstday);
            tend = new SimpleDateFormat("yyyyMMdd").parse(endday);
        } catch (ParseException e) {
            e.printStackTrace();
        }
//ts  tf   tend  td
        if (ts.compareTo(tf) != 1 && td.compareTo(tend) != -1) {
            return true;
        }
        return false;
    }

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public PowerModleInit queryById(Long id) {
        return this.powerModleInitMapper.queryById(id);
    }

    /**
     * 分页查询
     *
     * @param powerModleInit 筛选条件
     * @param pageRequest    分页对象
     * @return 查询结果
     */
    @Override
    public Page<PowerModleInit> queryByPage(PowerModleInit powerModleInit, PageRequest pageRequest) {
        long total = this.powerModleInitMapper.count(powerModleInit);
        return new PageImpl<>(this.powerModleInitMapper.queryAllByLimit(powerModleInit, pageRequest), pageRequest,
                total
        );
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(Long id) {
        return this.powerModleInitMapper.deleteById(id) > 0;
    }

    @Override
    public List<PowerModleInit> checkImportExcel(List<PowerModleInit> content) {
        return null;
    }

    @Override
    public Integer batchAdd(List<PowerModleInit> content) {

        int batchSize = 1000; // 批处理大小
        int sum = 0;
        for (int i = 0; i < content.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, content.size());
            List<PowerModleInit> batchContent = content.subList(i, endIndex);
            int rowsInserted = powerModleInitMapper.insertBatch(batchContent);
            sum += rowsInserted;
        }
        return sum;
    }

    @Override
    public BigDecimal calcPriceContryProxy(PowerModleInit init) {
        //检查国网代购测算值单价 所需原始数据
        if (checkinit1(init)) {
            return BigDecimal.ZERO;
        }

        //计算
        //国网代购价+基金附加费+大工业基本电费折算单价（市州填写）+ 输配电价
        //大工业基本电费折算单价（市州填写）=大工业容量/需量（需填） * 22 /  用电量
        int bigFlag =
                init.getCapacityDemandBigFlag() == null ? 0 :
                        init.getCapacityDemandBigFlag() == -1 ? 0 :
                                init.getCapacityDemandBigFlag() == 1 ? 22 :
                                        init.getCapacityDemandBigFlag() == 2 ? 33 :
                                                0;

        BigDecimal arg1 = init.getCapacityDemandBigFlag() == -1 ? BigDecimal.ZERO
                :
                init.getPowercSize().equals("0") ? BigDecimal.ZERO
                        :
                        init.getCapacityDemandBig().
                                multiply(new BigDecimal(bigFlag)).
                                divide(new BigDecimal(init.getPowercSize()), 2);

        return init.getIndirectCountry()
                .add(init.getAppendFund())
                .add(arg1)
                .add(init.getPriceSp());
    }

    public BigDecimal calcPriceContryProxyPro(PowerModleInit init) {
        //检查国网代购测算值单价 所需原始数据
        if (checkForcalcPriceContryProxy(init)) {
            return BigDecimal.ZERO;
        }

        //计算
        //国网代购价+基金附加费+大工业基本电费折算单价（市州填写）+ 输配电价
        //大工业基本电费折算单价（市州填写）=大工业容量/需量（需填） * 22 /  用电量
        int bigFlag =
                init.getCapacityDemandBigFlag() == null ? 0 :
                        init.getCapacityDemandBigFlag() == -1 ? 0 :
                                init.getCapacityDemandBigFlag() == 1 ? 22 :
                                        init.getCapacityDemandBigFlag() == 2 ? 33 :
                                                0;

        BigDecimal arg1 = init.getCapacityDemandBigFlag() == -1 ? BigDecimal.ZERO
                :
                init.getPowercSize().equals("0") ? BigDecimal.ZERO
                        :
                        init.getCapacityDemandBig().
                                multiply(new BigDecimal(bigFlag)).
                                divide(new BigDecimal(init.getPowercSize()), 2);

        return init.getIndirectCountry()
                .add(init.getAppendFund())
                .add(arg1)
                .add(init.getPriceSp())
                .add(init.getLineLoss());
    }

    @Override
    public BigDecimal calcPriceCountryProxyTimeshar(PowerModleInit init) {
        if (init.getPowerHigh() == null || init.getPowerMiddle() == null || init.getPowerLow() == null) {
            String power = new BigDecimal(init.getPowercSize()).
                    divide(
                            new BigDecimal("3"), 2, RoundingMode.HALF_UP).toString();
            init.setPowerHigh(power);
            init.setPowerMiddle(power);
            init.setPowerLow(power);
        }
        //检查 国网代购分峰平谷单价 初始数据
        if (checkinit2(init)) {
            return BigDecimal.ZERO;
        }
        if (init.getPowercSize().equals("0")) {
            return BigDecimal.ZERO;
        }

        //计算
        //国网代购分峰平谷单价
        //(((国网代购价+输配电价)*1.6+基金附加费+大工业基本电费折算单价（市州填写）)*峰电量1.6)+((大工业基本电费折算单价（市州填写）+基金附加费+输配电价+国网代购价)*平电量1)+((
        // (国网代购价+输配电价)\*0.4+基金附加费+大工业基本电费折算单价（市州填写）)*谷电量0.4))/用电量
        //大工业基本电费折算单价（市州填写）=大工业容量/需量（需填） * 22 /  用电量
//        ((((AR4+AT4)*1.6+AV4+AX4)*BA4)+((AX4+AV4+AT4+AR4)*BB4)+(((AR4+AT4)*0.4+AV4+AX4)*BC4))/M4

        //根据用电月份判断是否采用尖峰电价
        String high = highMacth(init.getMonth()) ? "1.92" : "1.6";
        BigDecimal arg1 =
                init.getIndirectCountry().subtract(init.getProfitLoss()).add(init.getPriceSp()).multiply(new BigDecimal(high));
        int bigFlag =
                init.getCapacityDemandBigFlag() == null ? 0 :
                        init.getCapacityDemandBigFlag() == -1 ? 0 :
                                init.getCapacityDemandBigFlag() == 1 ? 22 :
                                        init.getCapacityDemandBigFlag() == 2 ? 33 :
                                                0;
        BigDecimal arg2 = init.getCapacityDemandBigFlag() == -1 ? BigDecimal.ZERO
                :
                init.getPowercSize().equals("0") ? BigDecimal.ZERO
                        :
                        init.getCapacityDemandBig().
                                multiply(new BigDecimal(bigFlag)).
                                divide(new BigDecimal(init.getPowercSize()), 2);
        BigDecimal one =
                (arg1.add(init.getAppendFund()).add(arg2).add(init.getProfitLoss())).multiply(new BigDecimal(init.getPowerHigh()));

        BigDecimal two =
                (arg2.add(init.getAppendFund()).add(init.getPriceSp()).add(init.getIndirectCountry()).subtract(init.getProfitLoss()).add(init.getProfitLoss())).multiply(new BigDecimal(init.getPowerMiddle()));

        BigDecimal arg3 =
                init.getIndirectCountry().subtract(init.getProfitLoss()).add(init.getPriceSp()).multiply(new BigDecimal("0" +
                        ".4"));
        BigDecimal arg4 = arg3.add(init.getAppendFund()).add(arg2).add(init.getProfitLoss());
        BigDecimal three = arg4.multiply(new BigDecimal(init.getPowerLow()));

        return (one.add(two).add(three)).divide(new BigDecimal(init.getPowercSize()), 2);


    }

    public BigDecimal calcPriceCountryProxyTimesharPro(PowerModleInit init) {
        if (checkForCalcPriceCountryProxyTimeshar(init)) {
            return BigDecimal.ZERO;
        }
        if (init.getPowercSize().equals("0")) {
            return BigDecimal.ZERO;
        }

        if (init.getPowerHigh() == null || init.getPowerMiddle() == null || init.getPowerLow() == null) {
            String power = new BigDecimal(init.getPowercSize()).
                    divide(
                            new BigDecimal("3"), 2, RoundingMode.HALF_UP).toString();
            init.setPowerHigh(power);
            init.setPowerMiddle(power);
            init.setPowerLow(power);
        }

        String high = highMacth(init.getMonth()) ? "1.92" : "1.6";
        BigDecimal arg1 =
                init.getIndirectCountry().add(init.getPriceSp()).add(init.getLineLoss()).multiply(new BigDecimal(high));
        int bigFlag =
                init.getCapacityDemandBigFlag() == null ? 0 :
                        init.getCapacityDemandBigFlag() == -1 ? 0 :
                                init.getCapacityDemandBigFlag() == 1 ? 22 :
                                        init.getCapacityDemandBigFlag() == 2 ? 33 :
                                                0;
        BigDecimal arg2 = init.getCapacityDemandBigFlag() == -1 ? BigDecimal.ZERO
                :
                init.getPowercSize().equals("0") ? BigDecimal.ZERO
                        :
                        init.getCapacityDemandBig().
                                multiply(new BigDecimal(bigFlag)).
                                divide(new BigDecimal(init.getPowercSize()), 2);
        BigDecimal one =
                arg1.add(init.getAppendFund()).add(arg2).multiply(new BigDecimal(init.getPowerHigh()));

        BigDecimal two =
                (arg2.add(init.getAppendFund()).add(init.getLineLoss()).add(init.getPriceSp()).add(init.getIndirectCountry()).add(init.getProfitLoss())).multiply(new BigDecimal(init.getPowerMiddle()));

        BigDecimal arg3 =
                init.getIndirectCountry().add(init.getLineLoss()).add(init.getPriceSp()).multiply(new BigDecimal("0.4"));
        BigDecimal arg4 = arg3.add(init.getAppendFund()).add(arg2);
        BigDecimal three = arg4.multiply(new BigDecimal(init.getPowerLow()));

        return (one.add(two).add(three)).divide(new BigDecimal(init.getPowercSize()), 2);


    }

    private boolean highMacth(String month) {
        if ("7".equals(month) || "8".equals(month) || "1".equals(month) || "12".equals(month)) {
            return true;
        }
        return false;
    }

    @Override
    public BigDecimal calcPriceCountryProxyHigh(PowerModleInit init) {
        //检查 代购电价 分时 峰电价 初始数据
        if (checkinit3(init)) {
            return BigDecimal.ZERO;
        }
//        (arg1+基金附加费+大工业基本电费折算单价（市州填写）)\*峰电量1.6;
//        ((AR3+AT3)*1.6+AV3+AX3)*BA3
        BigDecimal arg1 = init.getIndirectCountry().add(init.getPriceSp()).multiply(new BigDecimal("1.6"));
        BigDecimal arg2 = init.getCapacityDemandBig().
                multiply(new BigDecimal(22)).
                divide(new BigDecimal(init.getPowercSize()), 2);
        return (arg1.add(init.getAppendFund()).add(arg2)).multiply(new BigDecimal(init.getPowerHigh()));
    }

    /**
     * 检查 代购电价 分时 峰电价 初始数据
     *
     * @param init
     */
    private boolean checkinit3(PowerModleInit init) {
        //((国网代购价+输配电价)*1.6+基金附加费+大工业基本电费折算单价（市州填写）)\*峰电量1.6
        //大工业基本电费折算单价（市州填写）=大工业容量/需量（需填） * 22 /  用电量
//        Optional.ofNullable(init.getIndirectCountry()).orElseThrow(() -> new BaseException("代购电价 分时 峰电价 初始数据 " +
//                                                                                                   "国网代购价为null"));
//        Optional.ofNullable(init.getPriceSp()).orElseThrow(() -> new BaseException("代购电价 分时 峰电价 初始数据 输配电价为null"));
//        Optional.ofNullable(init.getAppendFund()).orElseThrow(() -> new BaseException("代购电价 分时 峰电价 初始数据 基金附加费为null"));
//        Optional.ofNullable(init.getCapacityDemandBig()).orElseThrow(() -> new BaseException("代购电价 分时 峰电价 初始数据 " +
//                                                                                                     "大工业容量/需量为null"));
//        Optional.ofNullable(init.getPowercSize()).orElseThrow(() -> new BaseException("代购电价 分时 峰电价 初始数据 用电量为null"));
//        Optional.ofNullable(init.getPowerHigh()).orElseThrow(() -> new BaseException("代购电价 分时 峰电价 初始数据 峰电量1.6为null"));

        OptPro.ofNullable(init.getIndirectCountry()).ifNotPresent(bigDecimal -> {
            init.appendExMsg("代购电价分时峰电价 " +
                    "国网代购价为null");
        });
        OptPro.ofNullable(init.getPriceSp()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("代购电价分时峰电价：输配电价为null");
                }
        );
        OptPro.ofNullable(init.getAppendFund()).ifNotPresent(bigDecimal -> {
            init.appendExMsg("代购电价分时峰电价：基金附加费为null");
        });
        OptPro.ofNullable(init.getCapacityDemandBig()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("代购电价分时峰电价：大工业容量/需量为null");
                }
        );
        OptPro.ofNullable(init.getPowercSize()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("代购电价分时峰电价：用电量为null");
                }
        );

        OptPro.ofNullable(init.getPowerHigh()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("代购电价分时峰电价：峰电量为null");
                }
        );


        return ObjectUtil.isExistsNull2(init.getIndirectCountry(), init.getPriceSp(), init.getAppendFund(),
                init.getCapacityDemandBig(), init.getPowercSize(),
                init.getPowerHigh()
        );
    }

    /**
     * @param init 计算代购电价 分时 平电价
     * @return
     */
    @Override
    public BigDecimal calcPriceCountryProxyMid(PowerModleInit init) {
        //检查 代购电价 分时 平电价 初始数据
        if (check4(init)) {
            return BigDecimal.ZERO;
        }

        //计算
        //(大工业基本电费折算单价（市州填写）+基金附加费+输配电价+国网代购价)*平电量1
        BigDecimal arg1 = init.getCapacityDemandBig().
                multiply(new BigDecimal(22)).
                divide(new BigDecimal(init.getPowercSize()), 2);
        BigDecimal arg2 = arg1.add(init.getAppendFund()).add(init.getPriceSp()).add(init.getIndirectCountry());
        return arg2.multiply(new BigDecimal(init.getPowerMiddle()));
    }

    /**
     * 检查 代购电价 分时 平电价 初始数据
     *
     * @param init
     */
    private boolean check4(PowerModleInit init) {
        //(大工业基本电费折算单价（市州填写）+基金附加费+输配电价+国网代购价)*平电量1
        //大工业基本电费折算单价（市州填写）=大工业容量/需量（需填） * 22 /  用电量
//        Optional.ofNullable(init.getCapacityDemandBig()).orElseThrow(() -> new BaseException("代购电价 分时 平电价 初始数据 " +
//                                                                                                     "大工业容量/需量为null"));
//        Optional.ofNullable(init.getPowercSize()).orElseThrow(() -> new BaseException("代购电价 分时 平电价 初始数据 用电量为null"));
//        Optional.ofNullable(init.getAppendFund()).orElseThrow(() -> new BaseException("代购电价 分时 平电价 初始数据 基金附加费为null"));
//        Optional.ofNullable(init.getPriceSp()).orElseThrow(() -> new BaseException("代购电价 分时 平电价 初始数据 输配电价为null"));
//        Optional.ofNullable(init.getIndirectCountry()).orElseThrow(() -> new BaseException("代购电价 分时 平电价 初始数据 " +
//                                                                                                   "国网代购价为null"));
//        Optional.ofNullable(init.getPowerMiddle()).orElseThrow(() -> new BaseException("代购电价 分时 平电价 初始数据 平电量为null"));
        OptPro.ofNullable(init.getCapacityDemandBig()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("计算代购电价分时平电价：大工业容量/需量为null");
                }
        );
        OptPro.ofNullable(init.getPowercSize()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("计算代购电价分时平电价：用电量为null");
                }
        );
        OptPro.ofNullable(init.getPriceSp()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("计算代购电价分时平电价：输配电价为null");
                }
        );
        OptPro.ofNullable(init.getAppendFund()).ifNotPresent(bigDecimal -> {
            init.appendExMsg("计算代购电价分时平电价：基金附加费为null");
        });
        OptPro.ofNullable(init.getIndirectCountry()).ifNotPresent(bigDecimal -> {
            init.appendExMsg("计算代购电价分时平电价 " +
                    "国网代购价为null");
        });

        OptPro.ofNullable(init.getPowerMiddle()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("计算代购电价分时平电价：平电量为null");
                }
        );


        return ObjectUtil.isExistsNull2(init.getCapacityDemandBig(), init.getPowercSize(),
                init.getAppendFund(), init.getPriceSp(), init.getIndirectCountry(),
                init.getPowerMiddle()
        );

    }

    /**
     * @param init 计算代购电价 分时 谷电价
     * @return
     */
    @Override
    public BigDecimal calcPriceCountryProxyLow(PowerModleInit init) {
        //检查 代购电价 分时 谷电价 初始数据

        if (checkinit5(init)) {
            return BigDecimal.ZERO;
        }

        //计算
//        arg3\*谷电量0.4
//        =((AR3+AT3)*0.4+AV3+AX3)*BC3
        BigDecimal arg1 = init.getIndirectCountry().add(init.getPriceSp()).multiply(new BigDecimal("0.4"));
        BigDecimal arg2 = init.getCapacityDemandBig().
                multiply(new BigDecimal(22)).
                divide(new BigDecimal(init.getPowercSize()), 2);
        BigDecimal arg3 = arg1.add(init.getAppendFund()).add(arg2);
        return arg3.multiply(new BigDecimal(init.getPowerLow()));
    }

    @Override
    public BigDecimal calcPriceDirectpurele(PowerModleInit init) {
//        输配电价+损益+基金附加费+大工业基本电费折算单价（市州填写）+常规直购)总结算均价
//        =AT3+AU3+AV3+AX3+(AD3*AC3+AL3*AK3)/(AC3+AK3)
        if (checkinit6(init)) {
            return BigDecimal.ZERO;
        }
        int bigFlag =
                init.getCapacityDemandBigFlag() == null ? 0 :
                        init.getCapacityDemandBigFlag() == -1 ? 0 :
                                init.getCapacityDemandBigFlag() == 1 ? 22 :
                                        init.getCapacityDemandBigFlag() == 2 ? 33 :
                                                0;
        BigDecimal arg1 = init.getCapacityDemandBigFlag() == -1 ? BigDecimal.ZERO
                :
                init.getPowercSize().equals("0") ? BigDecimal.ZERO
                        :
                        init.getCapacityDemandBig().
                                multiply(new BigDecimal(bigFlag)).
                                divide(new BigDecimal(init.getPowercSize()), 2);
        //判断结算品种
        //结算品种为常规直购
        if ("1".equals(init.getSttype())) {
            return init.getPriceSp().add(init.getProfitLoss()).add(init.getAppendFund()).add(arg1).add(init.getAveragepriceTotal());
        }
        //结算品种为精准长协
        else if ("2".equals(init.getSttype())) {
            return init.getPriceSp().add(init.getProfitLoss()).add(init.getAppendFund()).add(arg1).add(init.getAveragepriceTota2());
        }
        //未知的结算类型
        return BigDecimal.ZERO;
    }

    public BigDecimal calcPriceDirectpurelePro(PowerModleInit init) {
//        输配电价+损益+基金附加费+大工业基本电费折算单价（市州填写）+(常规直购)总结算均价
//        =AT3+AU3+AV3+AX3+(AD3*AC3+AL3*AK3)/(AC3+AK3)
        if (checkCalcPriceDirectpurele(init)) {
            return BigDecimal.ZERO;
        }
        int bigFlag =
                init.getCapacityDemandBigFlag() == null ? 0 :
                        init.getCapacityDemandBigFlag() == -1 ? 0 :
                                init.getCapacityDemandBigFlag() == 1 ? 22 :
                                        init.getCapacityDemandBigFlag() == 2 ? 33 :
                                                0;
        BigDecimal arg1 = init.getCapacityDemandBigFlag() == -1 ? BigDecimal.ZERO
                :
                init.getPowercSize().equals("0") ? BigDecimal.ZERO
                        :
                        init.getCapacityDemandBig().
                                multiply(new BigDecimal(bigFlag)).
                                divide(new BigDecimal(init.getPowercSize()), 2);

        return init.getPriceSp().add(init.getProfitLoss()).add(init.getAppendFund())
                .add(init.getLineLoss())
                .add(arg1).add(init.getAveragepriceTotal());
    }

    /**
     * 检查 直购电实际
     *
     * @param init
     * @return
     */
    private boolean checkinit6(PowerModleInit init) {
//        输配电价+损益+基金附加费+大工业基本电费折算单价（市州填写）+常规直购)总结算均价

        StringJoiner joiner = new StringJoiner("||");
        OptPro.ofNullable(init.getPriceSp()).ifNotPresent(
                bigDecimal -> {
                    joiner.add("输配电价为null");
                }
        );
        OptPro.ofNullable(init.getProfitLoss()).ifNotPresent(
                bigDecimal -> {
                    joiner.add("损益为null");
                }
        );
        OptPro.ofNullable(init.getAppendFund()).ifNotPresent(bigDecimal -> {
            joiner.add("基金附加费为null");
        });

        //结算品种
        if ("1".equals(init.getSttype())) {
            OptPro.ofNullable(init.getAveragepriceTotal()).ifNotPresent(
                    bigDecimal -> {
                        joiner.add("(常规直购)总结算均价为null");
                    }
            );
        } else if ("2".equals(init.getSttype())) {
            OptPro.ofNullable(init.getAveragepriceTota2()).ifNotPresent(
                    bigDecimal -> {
                        joiner.add("(精准长协)总结算均价为null");
                    }
            );
        }

        OptPro.ofNullable(joiner.toString()).ifPresent(
                bigDecimal -> {
                    init.appendExMsg(
                            new StringJoiner("", "[计算直购电(无办公)", "]")
                                    .merge(joiner).toString()
                    );
                }
        );


        return "1".equals(init.getSttype()) ?
                ObjectUtil.isExistsNull2(init.getPriceSp(), init.getProfitLoss(), init.getAppendFund(),
                        init.getAveragepriceTotal()

                )
                : "2".equals(init.getSttype()) ?
                ObjectUtil.isExistsNull2(init.getPriceSp(), init.getProfitLoss(), init.getAppendFund(),
                        init.getAveragepriceTota2()
                )
                : true;
    }

    private boolean checkCalcPriceDirectpurele(PowerModleInit init) {
//        输配电价+损益+基金附加费+大工业基本电费折算单价（市州填写）+常规直购)总结算均价

        StringJoiner joiner = new StringJoiner("||");
        OptPro.ofNullable(init.getPriceSp()).ifNotPresent(
                bigDecimal -> {
                    joiner.add("输配电价为null");
                }
        );
        OptPro.ofNullable(init.getProfitLoss()).ifNotPresent(
                bigDecimal -> {
                    joiner.add("损益为null");
                }
        );
        OptPro.ofNullable(init.getAppendFund()).ifNotPresent(bigDecimal -> {
            joiner.add("基金附加费为null");
        });
        OptPro.ofNullable(init.getLineLoss()).ifNotPresent(bigDecimal -> {
            joiner.add("线损为null");
        });
        OptPro.ofNullable(init.getAveragepriceTotal()).ifNotPresent(
                bigDecimal -> {
                    joiner.add("(常规直购)总结算均价为null");
                }
        );


        OptPro.ofNullable(joiner.toString()).ifPresent(
                bigDecimal -> {
                    init.appendExMsg(
                            new StringJoiner("", "[计算直购电(无办公)", "]")
                                    .merge(joiner).toString()
                    );
                }
        );
        return ObjectUtil.isExistsNull2(init.getPriceSp(), init.getProfitLoss(), init.getAppendFund(),
                init.getLineLoss(),
                init.getAveragepriceTotal()

        );
    }

    /**
     * 检查 代购电价 分时 谷电价 初始数据
     *
     * @param init
     */
    private boolean checkinit5(PowerModleInit init) {
//        Optional.ofNullable(init.getIndirectCountry()).orElseThrow(() -> new BaseException("代购电价 分时 谷电价 初始数据 " +
//                                                                                                   "国网代购价为null"));
//        Optional.ofNullable(init.getPriceSp()).orElseThrow(() -> new BaseException("代购电价 分时 谷电价 初始数据 输配电价为null"));
//
//        Optional.ofNullable(init.getAppendFund()).orElseThrow(() -> new BaseException("代购电价 分时 谷电价 初始数据 基金附加费为null"));
//        Optional.ofNullable(init.getCapacityDemandBig()).orElseThrow(() -> new BaseException("代购电价 分时 谷电价 初始数据 " +
//                                                                                                     "大工业需量容量为null"));
//        Optional.ofNullable(init.getPowercSize()).orElseThrow(() -> new BaseException("代购电价 分时 谷电价 初始数据 用电量为null"));
        //((国网代购价+输配电价)*0.4+基金附加费+大工业基本电费折算单价（市州填写）)\*谷电量0.4
        //大工业基本电费折算单价（市州填写）=大工业容量/需量（需填） * 22 /  用电量


        OptPro.ofNullable(init.getCapacityDemandBig()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("计算代购电价分时平电价：大工业容量/需量为null");
                }
        );
        OptPro.ofNullable(init.getPowercSize()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("计算代购电价分时平电价：用电量为null");
                }
        );
        OptPro.ofNullable(init.getPriceSp()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("计算代购电价分时平电价：输配电价为null");
                }
        );
        OptPro.ofNullable(init.getAppendFund()).ifNotPresent(bigDecimal -> {
            init.appendExMsg("计算代购电价分时平电价：基金附加费为null");
        });
        OptPro.ofNullable(init.getIndirectCountry()).ifNotPresent(bigDecimal -> {
            init.appendExMsg("计算代购电价分时平电价 " +
                    "国网代购价为null");
        });

        OptPro.ofNullable(init.getPowerLow()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("计算代购电价分时平电价：谷电量为null");
                }
        );
        return ObjectUtil.isExistsNull2(init.getIndirectCountry(), init.getPriceSp(), init.getAppendFund(),
                init.getCapacityDemandBig(), init.getPowercSize(), init.getPowerLow()
        );
    }

    //检查 国网代购分峰平谷单价 初始数据
    //存在一个 一个所需数据为Null，即返回true
    private boolean checkinit2(PowerModleInit init) {
        //(((国网代购价+输配电价)*1.6+基金附加费+大工业基本电费折算单价（市州填写）)*峰电量1.6)+((大工业基本电费折算单价（市州填写）+基金附加费+输配电价+国网代购价)*平电量1)+((
        // (国网代购价+输配电价)\*0.4+基金附加费+大工业基本电费折算单价（市州填写）)*谷电量0.4))/用电量
        //大工业基本电费折算单价（市州填写）=大工业容量/需量（需填） * 22 /  用电量
        StringJoiner joiner = new StringJoiner("||");
        OptPro.ofNullable(init.getIndirectCountry()).ifNotPresent(bigDecimal -> {
            joiner.add("国网代购价为null");
        });
        OptPro.ofNullable(init.getPriceSp()).ifNotPresent(
                bigDecimal -> {
                    joiner.add("输配电价为null");
                }
        );
        OptPro.ofNullable(init.getAppendFund()).ifNotPresent(bigDecimal -> {
            joiner.add("基金附加费为null");
        });
        OptPro.ofNullable(init.getPowercSize()).ifNotPresent(
                bigDecimal -> {
                    joiner.add("用电量为null");
                    if (bigDecimal.equals("0")) {
                        joiner.add("用电量为0");
                    }
                }
        );


        OptPro.ofNullable(init.getPowerHigh()).ifNotPresent(
                bigDecimal -> {
                    joiner.add("峰电量为null");
                }
        );
        OptPro.ofNullable(init.getPowerMiddle()).ifNotPresent(
                bigDecimal -> {
                    joiner.add("平电量为null");
                }
        );
        OptPro.ofNullable(init.getPowerLow()).ifNotPresent(
                bigDecimal -> {
                    joiner.add("谷电量为null");
                }
        );
        OptPro.ofNullable(joiner.toString()).ifPresent(
                bigDecimal -> {
                    init.appendExMsg(
                            new StringJoiner("", "\t[计算国网代购分峰平谷单价:", "]")
                                    .merge(joiner).toString()
                    );
                }
        );

        boolean result = (ObjectUtil.isExistsNull2(init.getIndirectCountry(), init.getPriceSp(), init.getAppendFund(),
                init.getPowercSize(),
                init.getPowerHigh(), init.getPowerMiddle(),
                init.getPowerLow())
                &&
                (!init.getPowercSize().equals("0"))
        );

        return result;

    }

    private boolean checkForCalcPriceCountryProxyTimeshar(PowerModleInit init) {
        //(((国网代购价+输配电价)*1.6+基金附加费+大工业基本电费折算单价（市州填写）)*峰电量1.6)+((大工业基本电费折算单价（市州填写）+基金附加费+输配电价+国网代购价)*平电量1)+((
        // (国网代购价+输配电价)\*0.4+基金附加费+大工业基本电费折算单价（市州填写）)*谷电量0.4))/用电量
        //大工业基本电费折算单价（市州填写）=大工业容量/需量（需填） * 22 /  用电量
        StringJoiner joiner = new StringJoiner("||");
        OptPro.ofNullable(init.getIndirectCountry()).ifNotPresent(bigDecimal -> {
            joiner.add("国网代购价为null");
        });
        OptPro.ofNullable(init.getPriceSp()).ifNotPresent(
                bigDecimal -> {
                    joiner.add("输配电价为null");
                }
        );
        OptPro.ofNullable(init.getLineLoss()).ifNotPresent(
                bigDecimal -> {
                    joiner.add("线损为null");
                }
        );

        OptPro.ofNullable(init.getAppendFund()).ifNotPresent(bigDecimal -> {
            joiner.add("基金附加费为null");
        });
        OptPro.ofNullable(init.getPowercSize()).ifNotPresent(
                bigDecimal -> {
                    joiner.add("用电量为null");
                    if (bigDecimal.equals("0")) {
                        joiner.add("用电量为0");
                    }
                }
        );


//        OptPro.ofNullable(init.getPowerHigh()).ifNotPresent(
//                bigDecimal -> {
//                    joiner.add("峰电量为null");
//                }
//        );
//        OptPro.ofNullable(init.getPowerMiddle()).ifNotPresent(
//                bigDecimal -> {
//                    joiner.add("平电量为null");
//                }
//        );
//        OptPro.ofNullable(init.getPowerLow()).ifNotPresent(
//                bigDecimal -> {
//                    joiner.add("谷电量为null");
//                }
//        );
        OptPro.ofNullable(joiner.toString()).ifPresent(
                bigDecimal -> {
                    init.appendExMsg(
                            new StringJoiner("", "\t[计算国网代购分峰平谷单价:", "]")
                                    .merge(joiner).toString()
                    );
                }
        );

        boolean result = (ObjectUtil.isExistsNull2(init.getIndirectCountry(), init.getPriceSp(), init.getAppendFund(),
                init.getPowercSize(),
                init.getPowerHigh(), init.getPowerMiddle(),
                init.getPowerLow())
                &&
                (!init.getPowercSize().equals("0"))
        );

        return result;

    }

    /**
     * 检查国网代购测算值单价 所需原始数据
     *
     * @param init
     */
    private boolean checkinit1(PowerModleInit init) {
        StringJoiner joiner = new StringJoiner("||");
        OptPro.ofNullable(init.getIndirectCountry()).ifNotPresent(bigDecimal -> {
            joiner.add("国网代购价为null");
        });
        OptPro.ofNullable(init.getAppendFund()).ifNotPresent(bigDecimal -> {
            joiner.add("基金附加费为null");
        });
        OptPro.ofNullable(init.getPowerTotalSize()).ifNotPresent(
                bigDecimal -> {
                    joiner.add("用电量为null");
                }
        );
        OptPro.ofNullable(init.getPriceSp()).ifNotPresent(
                bigDecimal -> {
                    joiner.add("输配电价为null");
                }
        );
        OptPro.ofNullable(joiner.toString()).ifPresent(
                bigDecimal -> {
                    init.appendExMsg(
                            new StringJoiner("", "\t计算国网代购无办公", "]").merge(joiner).toString()
                    );
                }
        );
        return ObjectUtil.isExistsNull2(init.getIndirectCountry(), init.getAppendFund(),
                init.getPowerTotalSize(), init.getPriceSp()
        );


    }

    private boolean checkForcalcPriceContryProxy(PowerModleInit init) {
        StringJoiner joiner = new StringJoiner("||");
        OptPro.ofNullable(init.getIndirectCountry()).ifNotPresent(bigDecimal -> {
            joiner.add("国网代购价为null");
        });
        OptPro.ofNullable(init.getAppendFund()).ifNotPresent(bigDecimal -> {
            joiner.add("基金附加费为null");
        });
        OptPro.ofNullable(init.getPowerTotalSize()).ifNotPresent(
                bigDecimal -> {
                    joiner.add("用电量为null");
                }
        );
        OptPro.ofNullable(init.getPriceSp()).ifNotPresent(
                bigDecimal -> {
                    joiner.add("输配电价为null");
                }
        );
        OptPro.ofNullable(init.getLineLoss()).ifNotPresent(
                bigDecimal -> {
                    joiner.add("线损为null");
                }
        );
        OptPro.ofNullable(joiner.toString()).ifPresent(
                bigDecimal -> {
                    init.appendExMsg(
                            new StringJoiner("", "\t计算国网代购无办公", "]").merge(joiner).toString()
                    );
                }
        );
        return ObjectUtil.isExistsNull2(init.getIndirectCountry(), init.getAppendFund(),
                init.getPowerTotalSize(), init.getPriceSp(), init.getLineLoss()
        );


    }

    /**
     * 直购电理论值电价测算（分合同、浮动）
     *
     * @param init
     * @return
     */
    @Override
    public BigDecimal calcPriceDirectpureleTheoryEstimate(PowerModleInit init) {
        //(水电合同电量（度）\*水电合同价+浮动水电电量*水电浮动价+实际火电电量\*火电价格)/(实际水电电量+实际火电电量)
        //水电合同电量（度）=合同电量*0.8
        //实际火电电量 = 用电量\*0.2
        //实际水电电量 = 用电量*0.8
        if (checkinit7(init)) {
            return BigDecimal.ZERO;
        }
        //todo ：四舍五入方式待确认
        Long powerContractHydropower = Math.round(Double.valueOf(init.getPowerContract()) * 0.8);
        BigDecimal arg1 =
                new BigDecimal(powerContractHydropower).multiply(init.getContractHydropower());

        BigDecimal arg2 =
                new BigDecimal(init.getPowerHydropowerChange()).multiply(init.getPriceContractHydropower());

        Long powerThermalpowerCalc = Math.round(Double.valueOf(init.getPowercSize()) * 0.2);
        BigDecimal arg3 = new BigDecimal(powerThermalpowerCalc).multiply(init.getPriceThermalpower());

        Long powerThermalpowerCalc1 = powerThermalpowerCalc;
        Long powerHydropowerCalc = Math.round(Double.valueOf(init.getPowercSize()) * 0.8);
        long arg4 = powerHydropowerCalc + powerThermalpowerCalc1;

        return (arg1.add(arg2).add(arg3)).divide(new BigDecimal(arg4), 2);
    }

    private boolean checkinit7(PowerModleInit init) {
        //(水电合同电量（度）\*水电合同价+浮动水电电量*水电浮动价+实际火电电量\*火电价格)/(实际水电电量+实际火电电量)
        //水电合同电量（度）=合同电量*0.8
        //实际火电电量 = 用电量\*0.2
        //实际水电电量 = 用电量*0.8
        OptPro.ofNullable(init.getPriceThermalpower()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("直购电理论值电价测算（分合同、浮动）：火电价格为null");
                }
        );
        OptPro.ofNullable(init.getPowercSize()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("直购电理论值电价测算（分合同、浮动）：用电量为null");
                }
        );
        OptPro.ofNullable(init.getPriceContractHydropower()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("直购电理论值电价测算（分合同、浮动）：水电浮动价为null");
                }
        );

        OptPro.ofNullable(init.getPowerHydropowerChange()).ifNotPresent(bigDecimal -> {
            init.appendExMsg("直购电理论值电价测算（分合同、浮动） ：浮动水电电量为null");
        });

        OptPro.ofNullable(init.getContractHydropower()).ifNotPresent(bigDecimal -> {
            init.appendExMsg("直购电理论值电价测算（分合同、浮动） ：水电合同价为null");
        });

        OptPro.ofNullable(init.getPowerContract()).ifNotPresent(bigDecimal -> {
            init.appendExMsg("直购电理论值电价测算（分合同、浮动） ：合同电量为null");
        });
        return ObjectUtil.isExistsNull2(init.getPowerContract(), init.getContractHydropower(),
                init.getPowerHydropowerChange(), init.getPriceContractHydropower(),
                init.getPowercSize(), init.getPriceThermalpower()
        );
    }

    /**
     * 直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余）
     *
     * @param init
     * @return
     */
    @Override
    public BigDecimal calcPriceDirectpureleTheoryEstimate2(PowerModleInit init) {
        if (checkinit8(init)) {
            return BigDecimal.ZERO;
        }
        //(实际水电电量\*水电合同价+实际火电电量\*火电价格)/(实际水电电量+实际火电电量)
        //实际水电电量 = 用电量*0.8
        //实际火电电量 = 用电量\*0.2
        Long powerHydropowerCalc = Math.round(Double.valueOf(init.getPowercSize()) * 0.8);
        Long powerThermalpowerCalc = Math.round(Double.valueOf(init.getPowercSize()) * 0.2);

        BigDecimal arg1 = new BigDecimal(powerHydropowerCalc).multiply(init.getContractHydropower());
        BigDecimal arg2 = new BigDecimal(powerThermalpowerCalc).multiply(init.getPriceThermalpower());
        long arg3 = powerHydropowerCalc + powerThermalpowerCalc;

        return (arg1.add(arg2)).divide(new BigDecimal(arg3), 2);

    }

    private boolean checkinit8(PowerModleInit init) {
        //(实际水电电量\*水电合同价+实际火电电量\*火电价格)/(实际水电电量+实际火电电量)
        //实际水电电量 = 用电量*0.8
        //实际火电电量 = 用电量\*0.2
        OptPro.ofNullable(init.getPowercSize()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("计算直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余）：用电量为null");
                }
        );

        OptPro.ofNullable(init.getPriceThermalpower()).ifNotPresent(bigDecimal -> {
            init.appendExMsg("计算直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余） ：火电价格为null");
        });

        OptPro.ofNullable(init.getContractHydropower()).ifNotPresent(bigDecimal -> {
            init.appendExMsg("计算直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余） ：水电合同价为null");
        });
        return ObjectUtil.isExistsNull2(init.getPowercSize(), init.getContractHydropower(),
                init.getPriceThermalpower()
        );
    }

    /**
     * @param init 直购电理论电价测算（不分合同内外、不含富余、分峰平谷）
     * @return
     */
    @Override
    public BigDecimal calcPriceDirectpureleEstimate(PowerModleInit init) {
        if (init.getPowerHigh() == null || init.getPowerMiddle() == null || init.getPowerLow() == null) {
            String power = new BigDecimal(init.getPowercSize()).
                    divide(
                            new BigDecimal("3"), 2, RoundingMode.HALF_UP).toString();
            init.setPowerHigh(power);
            init.setPowerMiddle(power);
            init.setPowerLow(power);
        }
        if (checkinit9(init)) {
            return BigDecimal.ZERO;
        }
        if (init.getPowercSize().equals("0")) {
            return BigDecimal.ZERO;
        }

        //(((((输配电价+直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余）+损益)\*1.6)+大工业基本电费折算单价（市州填写）+基金附加费)\*峰电量1.6)+
        // (
        // (输配电价+损益+基金附加费+大工业基本电费折算单价（市州填写）+直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余）)\*平电量1)+(
        // (输配电价+直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余）+损益)\*0.4+大工业基本电费折算单价（市州填写）+基金附加费)\*谷电量0.4)/(峰电量1.6
        // +平电量1+谷电量0.4)
        BigDecimal averagepriceTotal =
                "1".equals(init.getSttype()) ? init.getAveragepriceTotal() :
                        "2".equals(init.getSttype()) ? init.getAveragepriceTota2() :
                                BigDecimal.ZERO;
        BigDecimal arg1 =
                init.getPriceSp().add(averagepriceTotal).multiply(new BigDecimal(
                        "1.6"));

        int bigFlag =
                init.getCapacityDemandBigFlag() == null ? 0 :
                        init.getCapacityDemandBigFlag() == -1 ? 0 :
                                init.getCapacityDemandBigFlag() == 1 ? 22 :
                                        init.getCapacityDemandBigFlag() == 2 ? 33 :
                                                0;
        BigDecimal arg2 = init.getCapacityDemandBigFlag() == -1 ? BigDecimal.ZERO
                :
                init.getPowercSize().equals("0") ? BigDecimal.ZERO
                        :
                        init.getCapacityDemandBig().
                                multiply(new BigDecimal(bigFlag)).
                                divide(new BigDecimal(init.getPowercSize()), 2);
        BigDecimal one =
                (arg1.add(arg2).add(init.getAppendFund()).add(init.getProfitLoss())).multiply(new BigDecimal(init.getPowerHigh()));


        BigDecimal two =
                (init.getPriceSp().add(init.getProfitLoss()).add(init.getAppendFund()).add(arg2).add(averagepriceTotal)).multiply(new BigDecimal(init.getPowerMiddle()));

        BigDecimal three =
                ((init.getPriceSp().add(averagepriceTotal)).multiply(new BigDecimal("0.4"))
                        .add(arg2)
                        .add(init.getAppendFund())
                        .add(init.getProfitLoss()))
                        .multiply(new BigDecimal(init.getPowerLow()));

        BigDecimal four = new BigDecimal(init.getPowerHigh()).add(
                new BigDecimal(init.getPowerMiddle())
        ).add(
                new BigDecimal(init.getPowerLow())
        );

        return one.add(two).add(three).divide(four, 2);

    }

    public BigDecimal calcPriceDirectpureleEstimatePro(PowerModleInit init) {
        if (init.getPowerHigh() == null || init.getPowerMiddle() == null || init.getPowerLow() == null) {
            String power = new BigDecimal(init.getPowercSize()).
                    divide(
                            new BigDecimal("3"), 2, RoundingMode.HALF_UP).toString();
            init.setPowerHigh(power);
            init.setPowerMiddle(power);
            init.setPowerLow(power);
        }
        if (checkinit9(init)) {
            return BigDecimal.ZERO;
        }
        if (init.getPowercSize().equals("0")) {
            return BigDecimal.ZERO;
        }

        //(((((输配电价+直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余）+损益)\*1.6)+大工业基本电费折算单价（市州填写）+基金附加费)\*峰电量1.6)+
        // (
        // (输配电价+损益+基金附加费+大工业基本电费折算单价（市州填写）+直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余）)\*平电量1)+(
        // (输配电价+直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余）+损益)\*0.4+大工业基本电费折算单价（市州填写）+基金附加费)\*谷电量0.4)/(峰电量1.6
        // +平电量1+谷电量0.4)
        BigDecimal averagepriceTotal =
                "1".equals(init.getSttype()) ? init.getAveragepriceTotal() :
                        "2".equals(init.getSttype()) ? init.getAveragepriceTota2() :
                                BigDecimal.ZERO;
        BigDecimal arg1 =
                init.getPriceSp().add(averagepriceTotal).multiply(new BigDecimal(
                        "1.6"));

        int bigFlag =
                init.getCapacityDemandBigFlag() == null ? 0 :
                        init.getCapacityDemandBigFlag() == -1 ? 0 :
                                init.getCapacityDemandBigFlag() == 1 ? 22 :
                                        init.getCapacityDemandBigFlag() == 2 ? 33 :
                                                0;
        BigDecimal arg2 = init.getCapacityDemandBigFlag() == -1 ? BigDecimal.ZERO
                :
                init.getPowercSize().equals("0") ? BigDecimal.ZERO
                        :
                        init.getCapacityDemandBig().
                                multiply(new BigDecimal(bigFlag)).
                                divide(new BigDecimal(init.getPowercSize()), 2);
        BigDecimal one =
                (arg1.add(arg2).add(init.getAppendFund()).add(init.getProfitLoss())).multiply(new BigDecimal(init.getPowerHigh()));


        BigDecimal two =
                (init.getPriceSp().add(init.getProfitLoss()).add(init.getAppendFund()).add(arg2).add(averagepriceTotal)).multiply(new BigDecimal(init.getPowerMiddle()));

        BigDecimal three =
                ((init.getPriceSp().add(averagepriceTotal)).multiply(new BigDecimal("0.4"))
                        .add(arg2)
                        .add(init.getAppendFund())
                        .add(init.getProfitLoss()))
                        .multiply(new BigDecimal(init.getPowerLow()));

        BigDecimal four = new BigDecimal(init.getPowerHigh()).add(
                new BigDecimal(init.getPowerMiddle())
        ).add(
                new BigDecimal(init.getPowerLow())
        );

        return one.add(two).add(three).divide(four, 2);

    }

    private boolean checkinit9(PowerModleInit init) {
        //(((((输配电价+直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余）+损益)\*1.6)+大工业基本电费折算单价（市州填写）+基金附加费)\*峰电量1.6)+(
        // (输配电价+损益+基金附加费+大工业基本电费折算单价（市州填写）+直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余）)\*平电量1)+(
        // (输配电价+直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余）+损益)\*0.4+大工业基本电费折算单价（市州填写）+基金附加费)\*谷电量0.4)/(峰电量1
        // .6+平电量1+谷电量0.4)
        StringJoiner joiner = new StringJoiner("||");
        OptPro.ofNullable(init.getPriceSp()).ifNotPresent(
                bigDecimal -> {
                    joiner.add("输配电价为null");
                }
        );
        //结算品种
        if ("1".equals(init.getSttype())) {
            OptPro.ofNullable(init.getAveragepriceTotal()).ifNotPresent(
                    bigDecimal -> {
                        joiner.add("(常规直购)总结算均价为null");
                    }
            );
        } else if ("2".equals(init.getSttype())) {
            OptPro.ofNullable(init.getAveragepriceTota2()).ifNotPresent(
                    bigDecimal -> {
                        joiner.add("(精准长协)总结算均价为null");
                    }
            );
        }
        OptPro.ofNullable(init.getPowercSize()).ifNotPresent(
                bigDecimal -> {
                    joiner.add("用电量为null");
                }
        );

        OptPro.ofNullable(init.getAppendFund()).ifNotPresent(bigDecimal -> {
            joiner.add("基金附加费为null");
        });

        OptPro.ofNullable(init.getPowerHigh()).ifNotPresent(bigDecimal -> {
            joiner.add("峰电量为null");
        });
        OptPro.ofNullable(init.getPowerMiddle()).ifNotPresent(bigDecimal -> {
            joiner.add("平电量为null");
        });
        OptPro.ofNullable(init.getPowerLow()).ifNotPresent(bigDecimal -> {
            joiner.add("谷电量为null");
        });
        OptPro.ofNullable(joiner.toString()).ifPresent(bigDecimal -> {
            init.appendExMsg(
                    new StringJoiner("", "\t[计算直购电分峰平谷（办公）:", "]")
                            .merge(joiner).toString()
            );
        });
        return "1".equals(init.getSttype()) ?
                ObjectUtil.isExistsNull2(init.getPriceSp(), init.getAveragepriceTotal(),
                        init.getPowercSize(),
                        init.getAppendFund(), init.getPowerHigh(),
                        init.getProfitLoss(), init.getPowerMiddle(),
                        init.getPowerLow()
                )
                :
                "2".equals(init.getSttype()) ?
                        ObjectUtil.isExistsNull2(init.getPriceSp(), init.getAveragepriceTota2(),
                                init.getPowercSize(),
                                init.getAppendFund(), init.getPowerHigh(),
                                init.getProfitLoss(), init.getPowerMiddle(),
                                init.getPowerLow()
                        )
                        : true;
    }

    @Override
    public BigDecimal calcPriceDirectpurele2(PowerModleInit init) {
//        (one2+two3+three2)/four
        if (checkinit10(init)) {
            return BigDecimal.ZERO;
        }

//        ((常规直购)总结算均价\*(常规直购)总结算电量+(富余电量)总结算均价\*(富余电量)总结算电量)
        BigDecimal arg1 = init.getAveragepriceTotal().multiply(new BigDecimal(init.getPowerTotalSize()))
                .add((
                        init.getAveragepriceTota2().multiply(new BigDecimal(init.getPowerTotalSize2()))
                ));
//        ((富余电量)总结算电量+(常规直购)总结算电量)
        BigDecimal arg2 = new BigDecimal(init.getPowerTotalSize()).add(
                new BigDecimal(init.getPowerTotalSize2())
        );

        BigDecimal arg3 = init.getCapacityDemandBig().
                multiply(new BigDecimal(22)).
                divide(new BigDecimal(init.getPowercSize()));

        BigDecimal one = arg1.divide(arg2).add(init.getProfitLoss()).add(init.getPriceSp());


        BigDecimal one2 =
                (one.multiply(new BigDecimal("1.6")).add(init.getAppendFund()).add(arg3)).multiply(new BigDecimal(init.getPowerHigh()));

        BigDecimal two = arg1.divide(arg2);

        BigDecimal two2 = two.add(init.getProfitLoss()).add(init.getPriceSp()).add(init.getAppendFund()).add(arg3);

        BigDecimal two3 = two2.multiply(new BigDecimal(init.getPowerMiddle()));

        BigDecimal three = two.add(init.getProfitLoss()).add(init.getPriceSp()).multiply(new BigDecimal("0.4"));

        BigDecimal three1 = three.add(init.getAppendFund()).add(arg3);

        BigDecimal three2 = three1.multiply(new BigDecimal(init.getPowerLow()));
        BigDecimal four = new BigDecimal(init.getPowerHigh())
                .add(new BigDecimal(init.getPowerMiddle()))
                .add(new BigDecimal(init.getPowerLow()));


        //(one2+two3+three2)/four
        return (one2.add(two3).add(three2)).divide(four);


    }

    /**
     * @param init 计算直购电收益
     * @return
     */
    @Override
    public BigDecimal caclProfitDire(PowerModleInit init) {
        // 判断是否 直购电
        if (init.getGetDire() == null || init.getGetDire() != 1) {
            return BigDecimal.ZERO;
        }
        //有办公收益
        //是否参加办公
        if (init.getWork() != null && init.getWork() == 1) {
            //参加
            //(国网代购分峰平谷单价-直购电理论电价测算（不分合同内外、不含富余、分峰平谷）)*用电量
            if (checkinit11(init)) {
                return BigDecimal.ZERO;
            }
            //calcPriceDirectpureleEstimate calcPriceCountryProxyTimeshar
            return init.getPriceDirectpureleEstimate()
                    .subtract(init.getPriceCountryProxyTimeshar())
                    .subtract(init.getProfitLoss())
                    .multiply(new BigDecimal(init.getPowercSize()));

        }
        //无办公收益
        if (init.getWork() != null && init.getWork() == 0) {
            if (checkinit12(init)) {
                return BigDecimal.ZERO;
            }

            //(国网代购测算值单价-直购电（实际）无办公)*(常规直购)总结算电量
            //setPriceCountryProxy  setPriceDirectpurele
            return init.getPriceDirectpurele()
                    .subtract(init.getPriceCountryProxy())
                    .subtract(init.getProfitLoss())
                    .multiply(new BigDecimal(init.getPowerTotalSize()));
        }
        return BigDecimal.ZERO;
    }

    public BigDecimal caclProfitDirePro(PowerModleInit init) {
        if (checkForCaclProfitDire(init)) {
            return BigDecimal.ZERO;
        }

        BigDecimal indirectCountry = init.getIndirectCountry();
        BigDecimal profitLoss = init.getProfitLoss();
        BigDecimal averagepriceTotal = init.getAveragepriceTotal();
        String powercSize = init.getPowercSize();

        return indirectCountry.subtract(profitLoss).subtract(averagepriceTotal)
                .multiply(new BigDecimal(powercSize));
    }

    private boolean checkForCaclProfitDire(PowerModleInit init) {

        StringJoiner joiner = new StringJoiner("||");
        OptPro.ofNullable(init.getIndirectCountry()).ifNotPresent(
                bigDecimal -> {
                    joiner.add("国网代购价不存在");
                }
        );
        OptPro.ofNullable(init.getProfitLoss()).ifNotPresent(
                bigDecimal -> {
                    joiner.add("损益不存在");
                }
        );

        OptPro.ofNullable(init.getAveragepriceTotal()).ifNotPresent(
                bigDecimal -> {
                    joiner.add("(常规直购)总结算均价不存在");
                }
        );
        OptPro.ofNullable(init.getPowercSize()).ifNotPresent(
                bigDecimal -> {
                    joiner.add("用电量不存在");
                }
        );
        OptPro.ofNullable(joiner.toString()).ifPresent(
                bigDecimal -> {
                    init.appendExMsg(
                            new StringJoiner("", "\t计算收益", "]").merge(joiner).toString()
                    );
                }
        );

        return ObjectUtil.isExistsNull2(init.getIndirectCountry(), init.getProfitLoss(),
                init.getAveragepriceTotal(), init.getPowercSize()
        );

    }

    @Override
    public Integer batchAddToOhter(List<PowerModleInit> list) {
        return powerModleInitMapper.batchAddToOhter(list);
    }

    @Override
    public int updateListById(List<PowerModleInit> content) {
        return powerModleInitMapper.updateListById(content);
    }

    @Override
    public int replaceList(List<PowerModleInit> content) {
        return powerModleInitMapper.replaceList(content);
    }

    @Override
    public List<PowerModleInit> selectList(PowerModleInit msg) {
        return powerModleInitMapper.selectList(msg);
    }

    @Override
    public List<PowerModleInit> selectList1(PowerModleInit msg) {
        return powerModleInitMapper.selectList1(msg);
    }

    @Override
    public void checkAndInput(PowerModleInit init, String budget, Map<String, List<Ammeterorprotocol>> ammeterorprotocolMap,
                              Map<String, List<SupplyCodeMapBill>> supplyCodeBillMap) {
        String accountno = init.getAccountno();

        List<Ammeterorprotocol> ammeterorprotocols = ammeterorprotocolMap.get(accountno);
        List<SupplyCodeMapBill> supplyCodeMapBills = supplyCodeBillMap.get(accountno);
        if (CollectionUtils.isEmpty(ammeterorprotocols)) {
            init.appendExMsg("有效户号未在能耗系统中找到");
            return;
        }
        if (CollectionUtils.isEmpty(supplyCodeMapBills)) {
            init.appendExMsg(String.format("当前户号%s账期不存在报账记录", budget));
            return;
        }
    }

    @Override
    public AjaxResult getPower(String ammeterid, String startDate, String endDate) {
        List<PowerModleInit> list = powerModleInitMapper.getPower(ammeterid, startDate, endDate);
        //无对应结算数据
        if (CollectionUtils.isEmpty(list)) {
            return AjaxResult.success("无数据");
        }
        //时间天数
        long days = Duration.between(
                LocalDate.parse(startDate, DateTimeFormatter.BASIC_ISO_DATE).atStartOfDay(),
                LocalDate.parse(endDate, DateTimeFormatter.BASIC_ISO_DATE).atStartOfDay()
        ).toDays();
        //结算电量平均值
        list = list.stream().filter(init -> StringUtils.isNotBlank(init.getPowercSize())).collect(toList());
        Double powerForEveryDay = list.stream().collect(averagingDouble(
                init -> Double.parseDouble(init.getPowercSize())));
        //电量
        return AjaxResult.success(days * powerForEveryDay);

    }

    @Override
    public int updateForModelList(List<PowerModleInit> powerModleInits) {
        return powerModleInitMapper.updateForModelList(powerModleInits);
    }

    @Override
    public AjaxResult reloadAuditinit(PowerModleInit powerModleInit, String auditType) {
        //0 拿到要稽核的户号
        List<String> accnos = new ArrayList<>();
        if ("all".equals(auditType)) {
            //权限判定 组装
            User user = ShiroUtils.getUser();
            List<Role> roles = userService.selectUserRole(user.getId());
            boolean isProAdmin = false;
            boolean isCityAdmin = false;
            boolean isSubAdmin = false;
            for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
                if (role.getCode().startsWith("admin")) {//省能耗费管理员
                    isProAdmin = true;
                }
                if (role.getCode().startsWith("PROVI_")) {//省能耗费管理员
                    isProAdmin = true;
                }
                if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                    isCityAdmin = true;
                }
            }
            if (isProAdmin) {
                powerModleInit.setOrgCode(null);
                //  查询权限设置 分公司
            } else if (isCityAdmin) {
                List<IdNameVO> companies = user.getCompanies();
                if (companies != null && companies.size() > 0)
                    powerModleInit.setOrgCode(companies.get(0).getId());
            }
            accnos = powerModleInitMapper.selectaAccnos(powerModleInit);
        }
        if ("accnos".equals(auditType)) {
            accnos = powerModleInit.getAccnos().stream().distinct().collect(toList());
        }
        //1 获取对应的原始数据
        List<PowerModleInit> content = powerModleInitMapper.selectEffectiveAccno(accnos);
        //1.1 将原来的稽核结果清空
        content.forEach(
                init -> init.setExMsg(null)
        );
        //2 开始稽核
        //2.1 户号有效性稽核
        List<Ammeterorprotocol> ammeterorprotocols =
                powerModleInitMapper.selectAmmeterorprotocolsForContion2(accnos);
        if (CollectionUtils.isEmpty(ammeterorprotocols)) {
            content.forEach(
                    init ->
                            init.setExMsg("有效户号未在能耗系统中找到")
            );
        }
        //2.2 账期有效性稽核
        String year = content.get(0).getYear();
        String month = content.get(0).getMonth();
        int month1 = Integer.parseInt(month);
        month = month1 < 10 ? "0" + month : month;
        String budget = year + "-" + month;

        accnos = ammeterorprotocols.stream().map(Ammeterorprotocol::getSupplybureauammetercode).collect(toList());
        List<Account> accountList = powerModleInitMapper.selectAccountsForcontion(accnos, year, month);
        Map<String, List<Ammeterorprotocol>> ammeterorprotocolMap =
                ammeterorprotocols.stream().distinct().collect(groupingBy(Ammeterorprotocol::getSupplybureauammetercode));
        Map<String, List<Account>> accountMap =
                accountList.stream().distinct().collect(groupingBy(Account::getAmmetercode));
        content.stream().forEach(
                init ->
                {
                    //checkAndInput(init, budget, ammeterorprotocolMap, accountMap)
                }
        );
        //3
        // 3.1更新原有稽核记录
        int n = powerModleInitMapper.updateListById(content);
        //3.2 插入新的稽核记录
        content.forEach(
                init -> init.setCreatetime(LocalDateTime.now())
        );
        int n2 = powerModleInitMapper.insertBatch(content);
        log.info("更新原有稽核记录{}条，插入新的稽核记录{}条", n, n2);
        return AjaxResult.success("重新稽核成功，请刷新查看结果");
    }

    @Override
    public void deleteCalcAndInsert(PowerModleInit powerModleInit1, List<PowerModleInit> content) {

    }

    @Override
    public AjaxResult calcByinit(PowerModleInit powerModleInitTemp) {
        log.info("获取company={}要效益分析的结算数据", powerModleInitTemp.getOrgCode());
        PowerModleInit powerModleInit1 = new PowerModleInit();
        powerModleInit1.setDelflag(0);
        powerModleInit1.setOrgCode(powerModleInitTemp.getOrgCode());
        powerModleInit1.setYear(powerModleInitTemp.getYear());
        powerModleInit1.setMonth(powerModleInitTemp.getMonth());
        //todo: wjs_指定地市为德阳
        powerModleInit1.setOrgCode("1000275");
        List<PowerModleInit> content =
                powerModleInitMapper.selectByContion(powerModleInit1);
        log.info("逻辑删除company={}的结算数据", powerModleInitTemp.getOrgCode());
        int n1 = powerModleInitMapper.deleteCalc(powerModleInit1);
        log.info("逻辑删除了部门{} {}年{}月单价效益分析数据{}条", powerModleInitTemp.getOrgCode(), powerModleInitTemp.getYear(), powerModleInitTemp.getMonth(), n1);

        log.info("结算数据空校验");
        if (CollectionUtils.isEmpty(content)) {
            return AjaxResult.success("此地市当前账期无可用于计算的单价基础数据，请导入或维护基础数据后再计算");
        }

        log.info("国网代购 损益 线损 基金附加费 校验");
        PowerModleInit modleInit =
                content.stream().filter(powerModleInit -> powerModleInit.check1()).findFirst().get();
        String province = modleInit.getElesellcompany().substring(0, 2);
        String year = modleInit.getYear();
        String month = modleInit.getMonth();

        log.info("获取能耗系统省:{},年:{},月：{}--国网代购 损益 基金附加费 线损", province, year, month);
        ModeleGetprice modeleGetprice1 = getModeleGetprice(province, year, month);
        if (modeleGetprice1 == null) {
            return AjaxResult.success(String.format("%s省%s年%s月的相关-国网代购||损益||基金附加费||线损-数据不存在，不允许业务人员添加，请联系管理员维护", province, year,
                    month
            ));
        }
        BigDecimal indirectcountry = modeleGetprice1.getIndirectcountry();
        BigDecimal profitloss = modeleGetprice1.getProfitloss();
        BigDecimal appendFund = modeleGetprice1.getAppendFund();
        BigDecimal lineLoss = modeleGetprice1.getLineLoss();
        log.info("输配电信息校验");
        List<String> accountNos = content.stream().map(PowerModleInit::getAccountno).collect(toList());
        List<ModlePriceSp2> priceSp2s = pricespMapper.selctPrisp(accountNos);
        if (CollectionUtils.isEmpty(priceSp2s)) {
            return AjaxResult.success(String.format("当前计算的所有户号无对应的输配信息，请检查电表基础信息的电压等级、是否供电局直供、是否办公、是否直供电" +
                    "\n或者输配信息的电压等级"));
        }
        Map<String, ModlePriceSp2> priceSp2Map = priceSp2s.stream().distinct().collect(
                toMap(
                        ModlePriceSp2::getAccountNo,
                        modlePriceSp2 -> modlePriceSp2,
                        (item1, item2) -> item1
                )
        );

        log.info("结算类型为大工业校验");
        List<String> accnosForBigWork = content.stream().
                filter(item -> item.getCapacityDemandBigFlag() != -1)
                .map(item -> item.getAccountno())
                .collect(toList());

        List<ModleBigandwork> bigandworks = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(accnosForBigWork)) {
            bigandworks = bigandworkMapper.selectListByAccountNo(accnosForBigWork);
            bigandworks = bigandworks.stream()
                    .filter(ModleBigandwork::checkBig).collect(toList());

            List<PowerModleInit> bigwork2 = content.stream().
                    filter(item -> item.getCapacityDemandBigFlag() != -1)
                    .filter(item -> item.checkBigWork())
                    .collect(toList());

            if (CollectionUtils.isEmpty(bigandworks) || CollectionUtils.isEmpty(bigwork2)) {
                String bigworkEx = ModleBigandwork.getBigWorkEx(accnosForBigWork);
                return AjaxResult.success(bigworkEx);
            }

        }

        Map<String, ModleBigandwork> bigandworkMap = bigandworks.stream().collect(toMap(
                ModleBigandwork::getAccountno,
                modleBigandwork -> modleBigandwork
        ));

        //峰平谷
        //2. 先计算
        //2.1开线程执行
        //2.1.1可使用cpu核数-1
        ExecutorService pool = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors() - 1);

        List<CompletableFuture<Void>> futures = content.stream().map(
                init -> {
                    CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
                        calcinitPro(indirectcountry, profitloss, lineLoss, appendFund, priceSp2Map, bigandworkMap, init);
                    }, pool);
                    return completableFuture;

                }
        ).collect(toList());
        futures.forEach(CompletableFuture::join);
        //新增新的计算数据
        //设置基础表主键id
        content.forEach(
                init -> init.setInit_id(init.getId())
        );

        int n2 = powerModleInitMapper.replaceList(content);

        log.info("新增单价计算表{}条", n2);

        return AjaxResult.success("计算了" + content.size() + "条单价");
    }

    private ModeleGetprice getModeleGetprice(String province, String year, String month) {
        ModeleGetprice modeleGetprice = new ModeleGetprice();
        modeleGetprice.setProvince(province);
        modeleGetprice.setYear(year);
        modeleGetprice.setMonth(month);
        ModeleGetprice modeleGetprice1 = this.modeleGetpriceService.selectByLatest(modeleGetprice);
        return modeleGetprice1;
    }

    @Override
    public AjaxResult updateMeter(List<Ammeterorprotocol> ammeterorprotocols) {
        //判空
        if (CollectionUtils.isEmpty(ammeterorprotocols)) {
            return AjaxResult.success("无电表要更新");
        }
        //判断对应户号是否已经绑定了电表
        List<String> accno = ammeterorprotocols.stream().filter(
                init -> init.getSupplybureauammetercode() != null && init.getSupplybureauammetercode() != ""
        ).map(Ammeterorprotocol::getSupplybureauammetercode).collect(toList());

        List<Ammeterorprotocol> meters = powerModleInitMapper.selectMeter(accno);
        //如果存在，返回原表存在记录，同时不做更新
        if (CollectionUtils.isNotEmpty(meters)) {
            Map<String, List<Ammeterorprotocol>> resultList = meters.stream().collect(
                    groupingBy(Ammeterorprotocol::getSupplybureauammetercode)
            );

            return AjaxResult.success(resultList);
        }
        //不存在才做更新操作
        int n = powerModleInitMapper.updateMeter(ammeterorprotocols);
        return AjaxResult.success("电表更新成功");
    }

    @Override
    public AjaxResult updateMeterend(List<Ammeterorprotocol> ammeterorprotocols) {
        //判空
        if (CollectionUtils.isEmpty(ammeterorprotocols)) {
            return AjaxResult.success("无电表要更新");
        }
        int n = powerModleInitMapper.updateMeter(ammeterorprotocols);
        return AjaxResult.success("电表更新成功");
    }

    @Override
    public AjaxResult queryMeter(Ammeterorprotocol msg) {
        List<Ammeterorprotocol> ammeterorprotocols = powerModleInitMapper.quetyMeter1(msg);
        return AjaxResult.success(ammeterorprotocols);
    }

    @Override
    public void SetBigWord(List<PowerModleInit> content) {
        List<BigWork> bigWorks = powerModleInitMapper.selectBigWord();
        Map<String, List<BigWork>> bigworkMap = bigWorks.stream().collect(
                groupingBy(BigWork::getSupplybureauammetercode)
        );
        content.stream().forEach(
                item -> {
                    if (bigworkMap.get(item.getAccountno()) == null) {
                        item.setCapacityDemandBigFlag(-1);
                    } else {
                        item.setCapacityDemandBigFlag(1);
                    }
                }
        );
    }

    @Override
    public AjaxResult quetyMeter(Ammeterorprotocol msg) {
        List<Ammeterorprotocol> ammeterorprotocols = powerModleInitMapper.quetyMeter(msg);
        Map<String, List<Ammeterorprotocol>> resultMap = ammeterorprotocols.stream().collect(
                groupingBy(Ammeterorprotocol::getSupplybureauammetercode)
        );
        resultMap = resultMap.entrySet().stream().filter(
                e -> e.getValue().size() > 1
        ).collect(toMap(
                e -> e.getKey(),
                e -> e.getValue()
        ));
        return AjaxResult.success(resultMap);
    }

    private void calcinit(BigDecimal indirectcountry, BigDecimal profitloss, BigDecimal appendFund, Map<String,
            ModlePriceSp2> priceSp2Map, Map<String, ModleBigandwork> bigandworkMap, PowerModleInit init) {
        long s = System.currentTimeMillis();
        //设置默认值
        //结算品种
        String b1 = init.getPowerTotalSize();
        String b2 = init.getPowerAccurateLong();
        if ((!"0".equals(b1)) && "0".equals(b2)) {
            init.setSttype("1");
        } else if ("0".equals(b1) && (!"0".equals(b2))) {
            init.setSttype("2");
        } else {
            init.setSttype("-1");
        }
        //国网代购价 损益 基金附加费
        init.setAppendFund(appendFund);
        init.setProfitLoss(profitloss);
        init.setIndirectCountry(indirectcountry);
        //输配 是否直购电 是否办公
        ModlePriceSp2 sp2 = priceSp2Map.get(init.getAccountno());
        if (sp2 != null) {
            init.setPriceSp(sp2.getPriceSp());
        } else {
            //todo:输配电价没用，默认为一般工商 1-10KV 输配
            init.setPriceSp(new BigDecimal("0.2560"));
        }
        //缺省 办公/直购电
        init.setWork(sp2 != null && sp2.getWork() != null ? sp2.getWork() : 1);
        init.setGetDire(sp2 != null && sp2.getDire() != null ? sp2.getDire() : 1);
        String accountno = init.getAccountno();

        log.info("户号{}大工业配置", accountno);
        ModleBigandwork bigandwork = bigandworkMap.get(init.getAccountno());

        int bigFlag = -1;
        bigFlag = init.getCapacityDemandBigFlag() == null ? -1 :
                init.getCapacityDemandBigFlag();
        log.info("户号{}大工业标识{}", accountno, bigFlag);

        BigDecimal capacityDemandBig = BigDecimal.ZERO;
        capacityDemandBig = init.getCapacityDemandBig() != null ? init.getCapacityDemandBig() :
                bigandwork != null ? bigandwork.getCapacitydemandbig1() != null ? bigandwork.getCapacitydemandbig1() :
                        BigDecimal.ZERO : BigDecimal.ZERO;
        log.info("户号{}大工业容量{}", accountno, capacityDemandBig.toString());

        BigDecimal Capacitydemandbig2 = BigDecimal.ZERO;
        if (init.getCapacityDemandBig2() != null) {
            Capacitydemandbig2 = init.getCapacityDemandBig2();
        } else {
            Capacitydemandbig2 = bigandwork == null ? BigDecimal.ZERO :
                    bigandwork.getCapacitydemandbig2() == null ? BigDecimal.ZERO :
                            bigandwork.getCapacitydemandbig2();
        }
        log.info("户号{}大工业需量{}", accountno, Capacitydemandbig2.toString());
        init.setCapacityDemandBigFlag(bigFlag);
        init.setCapacityDemandBig(capacityDemandBig);
        init.setCapacityDemandBig2(Capacitydemandbig2);
        log.info("户号{}大工业标识配置结束", accountno);
        //国网代购（无办公）
        //国网代购价+基金附加费+大工业基本电费折算单价（市州填写）+ 输配电价+损益
        //大工业基本电费折算单价（市州填写）=大工业容量/需量（需填） * 22 /  用电量
        init.setPriceCountryProxy(calcPriceContryProxy(init));

        //直购电实际（无办公）
        //输配电价+损益+基金附加费+大工业基本电费折算单价（市州填写）+常规直购)总结算均价
        init.setPriceDirectpurele(calcPriceDirectpurele(init));

        //国网代购分峰平谷单价（有办公）
        //(((国网代购价+输配电价)*1.6+基金附加费+大工业基本电费折算单价（市州填写）)*峰电量1.6)+(
        // (大工业基本电费折算单价（市州填写）+基金附加费+输配电价+国网代购价)
        // *平电量1)+(((国网代购价+输配电价)\*0.4+基金附加费+大工业基本电费折算单价（市州填写）)*谷电量0.4))/用电量
        //大工业基本电费折算单价（市州填写）=大工业容量/需量（需填） * 22 /  用电量
        init.setPriceCountryProxyTimeshar(calcPriceCountryProxyTimeshar(init));

        //直购电理论电价测算（不分合同内外、不含富余、分峰平谷）
        //(((((输配电价+直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余）+损益)\*1.6)+大工业基本电费折算单价（市州填写）+基金附加费)\*峰电量1.6)+(
        // (输配电价+损益+基金附加费+大工业基本电费折算单价（市州填写）+直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余）)\*平电量1)+(
        // (输配电价+直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余）+损益)\*0.4+大工业基本电费折算单价（市州填写）+基金附加费)\*谷电量0.4)/(峰电量1
        // .6+平电量1+谷电量0.4)
        init.setPriceDirectpureleEstimate(calcPriceDirectpureleEstimate(init));

        //直购电收益
        //是否 参加直购电
        // 参加 办公
        // (国网代购分峰平谷单价-直购电理论电价测算（不分合同内外、不含富余、分峰平谷）)*用电量
        //无 办公
        //   (国网代购测算值单价-直购电（实际）无办公)*(常规直购)总结算电量
        init.setProfitDire(caclProfitDire(init));
        long e = System.currentTimeMillis();
        log.info("耗时：{}", (e - s));
    }

    private void calcinitPro(BigDecimal indirectcountry, BigDecimal profitloss, BigDecimal lineLoss, BigDecimal appendFund, Map<String,
            ModlePriceSp2> priceSp2Map, Map<String, ModleBigandwork> bigandworkMap, PowerModleInit init) {
        long s = System.currentTimeMillis();

        log.info("户号{}输配电价填入", init.getAccountno());
        ModlePriceSp2 sp2 = priceSp2Map.get(init.getAccountno());
        if (sp2 != null) {
            init.setPriceSp(sp2.getPriceSp());
        }
        log.info("户号{} 输配电价为{}", init.getAccountno(), init.getPriceSp());

        init.setWork(sp2 != null && sp2.getWork() != null ? sp2.getWork() : 1);
        init.setGetDire(sp2 != null && sp2.getDire() != null ? sp2.getDire() : 1);
        log.info("办公类型判定,购电类型判定");
        Integer work = init.getWork();
        Integer getDire = init.getGetDire();
        init.setWork(work == null ? 0 : work);
        log.info("户号:{}办公类型为{},购电类型为{}",
                init.getAccountno(),
                work == null ? "缺省，有办公" : work == 0 ? "无办公" : "有办公",
                getDire == null ? "缺省，直购电" : getDire == 0 ? "代购电" : "直购电"
        );

        log.info("户号{}国网代购价{} 损益{} 线损{} 基金附加费填入{}", init.getAccountno(), indirectcountry, profitloss, lineLoss, appendFund);
        init.setAppendFund(appendFund);
        init.setProfitLoss(profitloss);
        init.setIndirectCountry(indirectcountry);
        init.setLineLoss(lineLoss);


        String accountno = init.getAccountno();

        log.info("户号{}大工业配置", accountno);
        ModleBigandwork bigandwork = bigandworkMap.get(init.getAccountno());

        int bigFlag = -1;
        bigFlag = init.getCapacityDemandBigFlag() == null ? -1 :
                init.getCapacityDemandBigFlag();
        log.info("户号{}大工业标识{}", accountno,
                bigFlag == -1 ? "非工业" : bigFlag == 1 ? "容量法" : "需量法");

        BigDecimal capacityDemandBig = BigDecimal.ZERO;
        capacityDemandBig = init.getCapacityDemandBig() != null ? init.getCapacityDemandBig() :
                bigandwork != null ? bigandwork.getCapacitydemandbig1() != null ? bigandwork.getCapacitydemandbig1() :
                        BigDecimal.ZERO : BigDecimal.ZERO;
        log.info("户号{}大工业容量{}", accountno, capacityDemandBig.toString());

        BigDecimal Capacitydemandbig2 = BigDecimal.ZERO;
        if (init.getCapacityDemandBig2() != null) {
            Capacitydemandbig2 = init.getCapacityDemandBig2();
        } else {
            Capacitydemandbig2 = bigandwork == null ? BigDecimal.ZERO :
                    bigandwork.getCapacitydemandbig2() == null ? BigDecimal.ZERO :
                            bigandwork.getCapacitydemandbig2();
        }
        log.info("户号{}大工业需量{}", accountno, Capacitydemandbig2.toString());
        init.setCapacityDemandBigFlag(bigFlag);
        init.setCapacityDemandBig(capacityDemandBig);
        init.setCapacityDemandBig2(Capacitydemandbig2);
        log.info("户号{}大工业标识配置结束", accountno);

        log.info("户号{}开始效益分析", accountno);
        init.setPriceCountryProxy(calcPriceContryProxyPro(init));
        init.setPriceDirectpurele(calcPriceDirectpurelePro(init));
        init.setPriceCountryProxyTimeshar(calcPriceCountryProxyTimesharPro(init));
        init.setPriceDirectpureleEstimate(calcPriceDirectpureleEstimate(init));
        init.setProfitDire(caclProfitDirePro(init));

        long e = System.currentTimeMillis();
        log.info("耗时：{}", (e - s));
    }


    private boolean checkinit12(PowerModleInit init) {
        //(国网代购测算值单价-直购电（实际）无办公)*(常规直购)总结算电量
        //输配电价+损益+基金附加费+大工业基本电费折算单价（非工业不填市州填写）+(常规直购)总结算均价
        OptPro.ofNullable(init.getPriceCountryProxy()).ifNotPresent(bigDecimal -> {
            init.appendExMsg("计算直购电收益 " +
                    "国网代无办公为null");
        });
        OptPro.ofNullable(init.getPriceSp()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("计算直购电收益：输配电价为null");
                }
        );
        OptPro.ofNullable(init.getProfitLoss()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("计算直购电收益：损益为null");
                }
        );
        OptPro.ofNullable(init.getPowercSize()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("计算直购电收益：用电量为null");
                }
        );
        OptPro.ofNullable(init.getAveragepriceTotal()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("计算直购电收益：（常规直购）总结算均价为null");
                }
        );
        OptPro.ofNullable(init.getPowerTotalSize()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("计算直购电收益：（常规直购）总结算电量为null");
                }
        );
        return ObjectUtil.isExistsNull2(
                init.getPriceCountryProxy(),
                init.getPriceSp(),
                init.getProfitLoss(),
                init.getPowercSize(),
                init.getAveragepriceTotal(),
                init.getPowerTotalSize()
        );
    }

    private boolean checkinit11(PowerModleInit init) {
        //参加
        //(国网代购分峰平谷单价-直购电理论电价测算（不分合同内外、不含富余、分峰平谷）)*用电量
        OptPro.ofNullable(init.getPriceCountryProxyTimeshar()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("计算直购电收益：国网代购有办公 为null");
                }
        );
        OptPro.ofNullable(init.getPriceDirectpureleEstimate()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("计算直购电收益：直购电有办公 为null");
                }
        );
        OptPro.ofNullable(init.getPowercSize()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("计算直购电收益：用电量 为null");
                }
        );

        return ObjectUtil.isExistsNull2(
                init.getPriceCountryProxyTimeshar(),
                init.getPriceDirectpureleEstimate(),
                init.getPowercSize()
        );
    }

    private boolean checkinit10(PowerModleInit init) {
        //((((((常规直购)总结算均价\*(常规直购)总结算电量+(富余电量)总结算均价\*(富余电量)总结算电量)/((富余电量)总结算电量+(常规直购)总结算电量)+损益+输配电价)
        // \*1.6+基金附加费+大工业基本电费折算单价（市州填写）)\*峰电量1.6)+((((常规直购)总结算电量\*(常规直购)总结算均价+(富余电量)总结算电量\*(富余电量)
        // 总结算均价)/((常规直购)总结算电量+(富余电量)总结算电量)+损益+输配电价+基金附加费+大工业基本电费折算单价（市州填写）)\*平电量1)+((((常规直购)总结算均价\*
        // (常规直购)总结算电量+(富余电量)总结算均价\*(富余电量)总结算电量)/((常规直购)总结算电量+(富余电量)总结算电量)+损益+输配电价)\*0
        // .4+基金附加费+大工业基本电费折算单价（市州填写）)\*谷电量0.4)/(峰电量1.6+平电量1+谷电量0.4)

        OptPro.ofNullable(init.getAveragepriceTotal()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("直购电实际(含富余、并分峰平谷)：(常规直购)总结算均价为null");
                }
        );
        OptPro.ofNullable(init.getPowerTotalSize()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("直购电实际(含富余、并分峰平谷)：(常规直购)总结算电量为null");
                }
        );
        OptPro.ofNullable(init.getAveragepriceTota2()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("直购电实际(含富余、并分峰平谷)：(富余电量)总结算均价为null");
                }

        );
        OptPro.ofNullable(init.getPowerTotalSize2()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("直购电实际(含富余、并分峰平谷)：(富余电量)总结算电量为null");
                }
        );
        OptPro.ofNullable(init.getProfitLoss()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("直购电实际(含富余、并分峰平谷)：损益为null");
                }
        );
        OptPro.ofNullable(init.getPriceSp()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("直购电实际(含富余、并分峰平谷)：输配电价为null");
                }
        );
        OptPro.ofNullable(init.getAppendFund()).ifNotPresent(bigDecimal -> {
            init.appendExMsg("直购电实际(含富余、并分峰平谷)：基金附加费为null");
        });

        OptPro.ofNullable(init.getCapacityDemandBig()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("直购电实际(含富余、并分峰平谷)：大工业容量/需量为null");
                }
        );
        OptPro.ofNullable(init.getPowercSize()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("直购电实际(含富余、并分峰平谷)：用电量为null");
                }
        );
        OptPro.ofNullable(init.getPowerHigh()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("直购电实际(含富余、并分峰平谷)：峰电量为null");
                }
        );
        OptPro.ofNullable(init.getPowerMiddle()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("直购电实际(含富余、并分峰平谷)：平电量1为null");
                }
        );
        OptPro.ofNullable(init.getPowerLow()).ifNotPresent(
                bigDecimal -> {
                    init.appendExMsg("直购电实际(含富余、并分峰平谷)：谷电量0.4为null");
                }
        );

        return ObjectUtil.isExistsNull2(init.getAveragepriceTotal(), init.getPowerTotalSize(),
                init.getAveragepriceTota2(), init.getPowerTotalSize2(),
                init.getProfitLoss(), init.getPriceSp(), init.getAppendFund(),
                init.getCapacityDemandBig(), init.getPowercSize(),
                init.getPowerHigh(), init.getPowerMiddle(), init.getPowerLow()
        );
    }


}
