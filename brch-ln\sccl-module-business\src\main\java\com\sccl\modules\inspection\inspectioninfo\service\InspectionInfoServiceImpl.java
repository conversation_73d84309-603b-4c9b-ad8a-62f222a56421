package com.sccl.modules.inspection.inspectioninfo.service;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.inspection.inspectioninfo.mapper.InspectionInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sccl.modules.inspection.inspectioninfo.domain.InspectionInfo;

import java.util.List;


/**
 * 巡检记录 服务层实现
 * 
 * <AUTHOR>
 * @date 2019-05-27
 */
@Service
public class InspectionInfoServiceImpl extends BaseServiceImpl<InspectionInfo> implements IInspectionInfoService
{
    @Autowired
    InspectionInfoMapper inspectionInfoMapper;

    public List<InspectionInfo> selectListByCondition(InspectionInfo inspectionInfo){
        return inspectionInfoMapper.selectListByCondition(inspectionInfo);
    }

    public Object update(List<InspectionInfo> inspectionInfoList){
        for(InspectionInfo inspectionInfo:inspectionInfoList){
            inspectionInfoMapper.updateForInspectByModel(inspectionInfo);
        }
        return null;
    }

    public List getAllAccountno(String company){
        return inspectionInfoMapper.getAllAccountno(company);
    }
}
