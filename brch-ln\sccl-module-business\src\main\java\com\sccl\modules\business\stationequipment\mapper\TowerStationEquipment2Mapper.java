package com.sccl.modules.business.stationequipment.mapper;

import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.stationequipment.domain.TowerStationEquipment2;

import java.util.List;

/**
 * 铁塔站址设备 数据层
 * 
 * <AUTHOR>
 * @date 2022-10-31
 */
public interface TowerStationEquipment2Mapper extends BaseMapper<TowerStationEquipment2>
{


    List<TowerStationEquipment2> selectByIds(List<String> ids);
}