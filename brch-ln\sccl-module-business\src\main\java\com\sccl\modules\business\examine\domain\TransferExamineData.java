package com.sccl.modules.business.examine.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 转供电考核明细表
 */
@Data
@EqualsAndHashCode
@TableName(value = "transfer_examine_data")
public class TransferExamineData implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	private Long id;

	/**
	 * 报账单id
	 */
	private Long billId;

	/**
	 * 财辅报账编码
	 */
	private String writeoffInstanceCode;

	/**
	 * 台账id
	 */
	private Long pcid;

	/**
	 * 所属分公司
	 */
	private String company;

	/**
	 * 电表协议ID
	 */
	private Long ammeterid;

	/**
	 * 结算类型 直供电、转供电 在power_category_type表中type_category="directSupplyFlag"
	 */
	private int directsupplyflag;

	/**
	 * 账期 yyyy-MM
	 */
	private String accountPeriod;

	/**
	 * 台账期号 yyyyMM
	 */
	private String accountno;

	/**
	 * 起始日期-格式为 yyyyMMdd
	 */
	private String startdate;

	/**
	 * 截止日期-格式为 yyyyMMdd
	 */
	private String enddate;

	/**
	 * 总电量
	 */
	private BigDecimal totalusedreadings;

	/**
	 * 总金额
	 */
	private BigDecimal accountmoney;

	/**
	 * 单价
	 */
	private BigDecimal unitpirce;

	/**
	 * 状态
	 */
	private String status;

	/**
	 * 备注1
	 */
	private String remark1;

	/**
	 * 备注2
	 */
	private String remark2;

	/**
	 * 备注3
	 */
	private String remark3;

	/**
	 * 备注4
	 */
	private String remark4;

	/**
	 * 创建人
	 */
	private String createdBy;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;

	/**
	 * 删除标志 0/1 正常/删除
	 */
	private String delFlag;

	/**
	 * 新增
	 *
	 * @param userName 当前登录用户
	 */
	public void initInsert(String userName) {
		delFlag = "0";
		createdBy = userName;
		createTime = new Date();
		updateTime = new Date();
	}

	/**
	 * 修改
	 */
	public void initUpdate() {
		updateTime = new Date();
	}
}
