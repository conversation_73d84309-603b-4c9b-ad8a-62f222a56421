package com.sccl.modules.business.stationaudit.pavgpowertoolow;

import com.enrising.dcarbon.audit.*;
import com.sccl.common.utils.BigDecimlUtil;
import com.sccl.common.utils.SpringUtil;
import com.sccl.modules.autojob.util.convert.StringUtils;
import com.sccl.modules.business.account.domain.Account;
import com.sccl.modules.business.stationaudit.pcomparequtoa.quotaCompareContent;
import com.sccl.modules.business.stationaudit.pcomparequtoa.quotaCompareReferen;
import com.sccl.modules.mssaccount.mssaccountbill.mapper.MssAccountbillMapper;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
public class AvgPowerToolLowCreator extends AbstractRefereeDatasourceCreator {
    @Override
    public Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> collect(Auditable auditable) {
        //数据源
        Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> datasource = new HashMap<>();

        //从Spring上下文取得mapper
        MssAccountbillMapper mapper = SpringUtil.getBean(MssAccountbillMapper.class);
        //可能上下文没有这个mapper
        if (mapper == null) {
            return datasource;
        }
        Account account = (Account) auditable;
        //根据 台账主键 获取对应的平均电量比对信息
        Long pcid = account.getPcid();
        AvgPowerTooLowContent content = mapper.getAvgPowerToolLow(pcid);

        log.info("判空");

        if (content == null) {
            return datasource;
        }

        log.info("开始评判");
        boolean b = BigDecimlUtil.checkValue(content.getAvgpower(), BigDecimal.ZERO, new BigDecimal("0.5"));
        if (b) {
            content.setExmsg("日均耗电量低于0.5度");
        }
        //构造auditResult
        RefereeResult refereeResult = new RefereeResult("日均耗电量过低", true, "成功");
        content.setAuditKey(pcid + "");
        content.setRefereeResult(refereeResult);
        content.setStep(10);

        AuditResult auditResult = content.getAuditResult();


        //可以添加多种不同类型的评判数据
        //2添加到数据源
        datasource.put(quotaCompareContent.class, new ArrayList<>(Arrays.asList(auditResult)));
        return datasource;
    }

    @Override
    public AbstractReferee getReferee() {
        return new AvgPowerToolLowReferen("日均耗电量过低");
    }

}
