package com.sccl.modules.business.examine.mapper;

import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.examine.domain.TransferExamineBase;
import com.sccl.modules.business.examine.vo.TransferExamineDataVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 转供电考核-基础数据表 数据层
 * 
 * <AUTHOR>
 * @date 2025-02-24
 */
@Mapper
public interface TransferExamineBaseMapper extends BaseMapper<TransferExamineBase>
{
    /**
     * 根据主键id数组批量更新
     * @param entity
     * @return
     */
    int updateForModelBatch(TransferExamineBase entity);

    /**
     * 查询基础数据
     * @return
     */
    List<TransferExamineDataVo> baseList();
}