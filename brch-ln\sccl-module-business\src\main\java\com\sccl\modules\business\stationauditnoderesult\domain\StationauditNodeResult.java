package com.sccl.modules.business.stationauditnoderesult.domain;

import com.enrising.dcarbon.audit.RefereeDatasource;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 基站一站式稽核结果表 stationaudit_node_result
 * 
 * <AUTHOR>
 * @date 2022-11-16
 */
@Data
public class StationauditNodeResult extends BaseEntity implements RefereeDatasource
{

	private static final long serialVersionUID = 1L;
	
    /** 评判的报账单 */
    private Long billId;
    /** 评判的时间 */
    private LocalDateTime auditTime;
    /** 节点类型 1-报账历史/2-电量历史/3-转供电合同是否失效/4-站址连续报账记录存在（转6直2）/5-电表不同站址/6-站址不同电表7-站址设备变动/ */
    private Integer nodetype;
    /** 报账单历史id */
    private Long mssId;
    /** 站址编号 */
    private String stationCode;
    /** 电量 */
    private BigDecimal power;
    /** 录入时间 */
    private String inputdate;
    /** 转供电站址 */
    private String stationcodez;
    /** 合同开始时间 */
    private LocalDateTime startTime;
    /** 合同结束时间 */
    private LocalDateTime endTime;
    /** 本次报账的时间 */
    private LocalDate nowdate;
    /** 上次报账时间 */
    private LocalDate lastdate;
    /** 电表类型 */
    private Integer directsupplyflag;
    /** 附加信息 */
    private String message;
    /** 电表id */
    private String ammeterid;
    /** 本期站址编号 */
    private String stationcodenow;
    /** 上期站址编号 */
    private String laststationcode;
    /** 本次电表编号 */
    private String ammeter;
    /** 上次电表编号 */
    private String lastAmmeter;
    /** 录入日期 */
    private LocalDateTime  inputdate2;
    /** 设备版本 */
    private Integer version;
    /** 版本时间 */
    private LocalDateTime versionTime;
    /** 设备类型 */
    private String type;

    /** 评级标准主键 */
    private Long gid;
    /** 实际功耗 */
    private BigDecimal calcpower;

    /** 标准功耗 */
    private BigDecimal standardpower;

    /** 节点评判信息 */
    private String auditmsg;






    public static void main(String[] args) {
        ArrayList<Object> objects = new ArrayList<>();
        objects.add(null);
        System.out.println(1);

        List<Object> collect = objects.stream().filter(s -> s != null).collect(Collectors.toList());
        System.out.println(2);
    }
}
