package com.sccl.modules.business.statistical.account.mapper;

import com.sccl.modules.business.statistical.account.domain.UnitPriceRank;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-12-05 12:09
 * @email <EMAIL>
 */
@Mapper
public interface PowerAccountStatisticalMapper {
    List<UnitPriceRank> selectUnitPriceRankOnCompany(@Param("companies") List<Long> companies, @Param("isPrivate") Boolean isPrivate, @Param("isDirectly") Boolean isDirectly);

    BigDecimal selectProvinceAverageUnitPrice(@Param("isPrivate") Boolean isPrivate, @Param("isDirectly") Boolean isDirectly);
}
