package com.sccl.modules.business.meterinfoalljt_new.vo;

import lombok.Data;

import java.util.Date;

/**
 * 表计清单查询 返回VO
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class MeterinfoAllJtVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 省级编码
     */
    private String provinceCode;

    /**
     * 市局组织编码
     */
    private String cityCode;

    /**
     * 市局组织名称
     */
    private String cityName;

    /**
     * 区县组织编码
     */
    private String countyCode;

    /**
     * 区县组织名称
     */
    private String countyName;

    /**
     * 电表编码
     */
    private String energyMeterCode;

    /**
     * 电表名称
     */
    private String energyMeterName;

    /**
     * 电表状态
     */
    private String status;

    /**
     * 电表用途
     */
    private String usageCopy;

    /**
     * 电表类型
     */
    private String type;

    /**
     * 局站编码
     */
    private String stationCode;

    /**
     * 局站名称
     */
    private String stationName;

    /**
     * 局站位置
     */
    private String stationLocation;

    /**
     * 局站状态
     */
    private String stationStatus;

    /**
     * 局站类型
     */
    private String stationType;

    /**
     * 大工业用电标识
     */
    private String largeIndustrialElectricityFlag;

    /**
     * 供电方式
     */
    private String energySupplyWay;

    /**
     * 站点编码
     */
    private String siteCode;

    /**
     * 电网电表编码
     */
    private String powerGridEnergyMeterCode;

    /**
     * 删除标识
     */
    private Integer delFlag;

    /**
     * 能源类型
     */
    private String energyType;

    /**
     * 类型局站编码
     */
    private String typeStationCode;

    /**
     * 合同价格
     */
    private String contractPrice;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 同步标识
     */
    private Integer syncFlag;

    /**
     * 失败信息
     */
    private String failMag;

    /**
     * 消息ID
     */
    private String msgId;
}
