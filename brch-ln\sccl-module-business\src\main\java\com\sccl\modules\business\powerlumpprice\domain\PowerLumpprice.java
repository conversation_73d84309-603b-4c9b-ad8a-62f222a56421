package com.sccl.modules.business.powerlumpprice.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


/**
 * 铁塔包干单价维护表 power_lumpprice
 * 
 * <AUTHOR>
 * @date 2019-06-19
 */
public class PowerLumpprice
{
	private static final long serialVersionUID = 1L;

	private Long plpId;
	
    /** 所属部门 */
    private Long orgid;
    /** 分公司 */
    private Long company;
    /** 包干单价 */
    private BigDecimal lumpprice;
    /** 开始时间 */
    private Date startdate;
    /** 截止时间 */
    private Date enddate;
    /** 录入时间 */
    private Date inputdate;
    /** 状态 参考power_category_type中type_category中的status */
    private String status;
    /** 录入人id */
    private Long inputuserid;
    /** 备注 */
    private String memo;
    /** 录入人名称 */
    private String inputusername;
    /**多个组织机构 */
    private List<Map<String,Object>> countrys;

	public Long getPlpId() { return plpId; }

	public void setPlpId(Long plpId) { this.plpId = plpId; }

	public void setOrgid(Long orgid)
	{
		this.orgid = orgid;
	}

	public Long getOrgid() 
	{
		return orgid;
	}

	public void setCompany(Long company)
	{
		this.company = company;
	}

	public Long getCompany() 
	{
		return company;
	}

	public void setLumpprice(BigDecimal lumpprice)
	{
		this.lumpprice = lumpprice;
	}

	public BigDecimal getLumpprice() 
	{
		return lumpprice;
	}

	public void setStartdate(Date startdate)
	{
		this.startdate = startdate;
	}

	public Date getStartdate() 
	{
		return startdate;
	}

	public void setEnddate(Date enddate)
	{
		this.enddate = enddate;
	}

	public Date getEnddate() 
	{
		return enddate;
	}

	public void setInputdate(Date inputdate)
	{
		this.inputdate = inputdate;
	}

	public Date getInputdate() 
	{
		return inputdate;
	}

	public void setStatus(String status)
	{
		this.status = status;
	}

	public String getStatus() 
	{
		return status;
	}

	public void setInputuserid(Long inputuserid)
	{
		this.inputuserid = inputuserid;
	}

	public Long getInputuserid() 
	{
		return inputuserid;
	}

	public void setMemo(String memo)
	{
		this.memo = memo;
	}

	public String getMemo() 
	{
		return memo;
	}

	public void setInputusername(String inputusername)
	{
		this.inputusername = inputusername;
	}

	public String getInputusername() 
	{
		return inputusername;
	}

	public List<Map<String, Object>> getCountrys() {
		return countrys;
	}

	public void setCountrys(List<Map<String, Object>> countrys) {
		this.countrys = countrys;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("plpId", getPlpId())
            .append("orgid", getOrgid())
            .append("company", getCompany())
            .append("lumpprice", getLumpprice())
            .append("startdate", getStartdate())
            .append("enddate", getEnddate())
            .append("inputdate", getInputdate())
            .append("status", getStatus())
            .append("inputuserid", getInputuserid())
            .append("memo", getMemo())
            .append("inputusername", getInputusername())
            .toString();
    }
}
