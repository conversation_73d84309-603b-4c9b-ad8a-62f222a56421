package com.sccl.modules.business.stationreportwhitelist.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.google.common.collect.Lists;
import com.sccl.common.lang.StringUtils;
import com.sccl.common.utils.BeanUtils;
import com.sccl.exception.BusinessException;
import com.sccl.modules.business.statinAudit.domain.SysOrganizations;
import com.sccl.modules.business.statinAudit.mapper.PowerStationInfoMapper;
import com.sccl.modules.business.stationreportwhitelist.domain.*;
import com.sccl.modules.business.stationreportwhitelist.dto.StationReportWhitelistBillDTO;
import com.sccl.modules.business.stationreportwhitelist.dto.StationReportWhitelistBillQuery;
import com.sccl.modules.business.stationreportwhitelist.enums.BillStatus;
import com.sccl.modules.business.stationreportwhitelist.enums.MeterCategory;
import com.sccl.modules.business.stationreportwhitelist.enums.MyDict;
import com.sccl.modules.business.stationreportwhitelist.enums.WhitelistType;
import com.sccl.modules.business.stationreportwhitelist.mapper.MpPowerStationInfoMapper;
import com.sccl.modules.business.stationreportwhitelist.mapper.PowerAmmeterorprotocolMapper;
import com.sccl.modules.business.stationreportwhitelist.mapper.StationReportWhitelistBillMapper;
import com.sccl.modules.business.stationreportwhitelist.vo.OneStopIsMoreThanOneWatchExport;
import com.sccl.modules.business.stationreportwhitelist.vo.PowerAmmeterorprotocolVO;
import com.sccl.modules.business.stationreportwhitelist.vo.StationReportWhitelistBillVO;
import com.sccl.modules.monitor.operlog.domain.OperLog;
import com.sccl.modules.monitor.operlog.mapper.OperLogMapper;
import com.sccl.modules.uniflow.common.WFModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class StationReportWhitelistBillServiceImpl extends ServiceImpl<StationReportWhitelistBillMapper, StationReportWhitelistBill> implements StationReportWhitelistBillService {

    private final OperLogMapper operLogMapper;
    private final PowerStationInfoMapper stationInfoMapper;
    private final MpPowerStationInfoMapper mpStationInfoMapper;
    private final PowerAmmeterorprotocolService ammeterorprotocolService;
    private final PowerAmmeterorprotocolMapper powerAmmeterorprotocolMapper;

    /**
     * 添加白名单单据
     *
     * @param dto 白名单单据
     */
    @Override
    @Transactional(rollbackOn = Exception.class)
    public String add(StationReportWhitelistBillDTO dto) {
        if (dto.getId() != null) {
            throw new BusinessException("保存时id不能赋值");
        }
        if (CollUtil.isEmpty(dto.getMeterIdList())) {
            throw new BusinessException("电表id列表不能为空");
        }
        // 流程单据
        StationReportWhitelistBill bill = new StationReportWhitelistBill();
        BeanUtils.copyBeanProp(bill, dto);
        // 默认草稿
        bill.setBillStatus(BillStatus.DRAFT.getCode());
        bill.setCreateTime(new Date());

        // 如果是一站多表 局站编码和类型不能重复
        if (WhitelistType.ONE_STOP_IS_MORE_THAN_ONE_WATCH.getCode().equals(dto.getType())) {
            bill = baseMapper.selectOne(Wrappers.<StationReportWhitelistBill>lambdaQuery()
                    .eq(StationReportWhitelistBill::getStationcode, dto.getStationcode())
                    .eq(StationReportWhitelistBill::getWhitelistType, dto.getType())
                    .last("LIMIT 1")
            );
            if (bill != null) {
                log.info("该局站编码和类型已存在");
            } else {
                log.info("添加一站多表白名单单据");
                bill.setWhitelistType(dto.getType());
                // 添加白名单，并赋值单据id，方便后面查询，最后返回单据id，方便提流程
                baseMapper.insert(bill);
            }
        } else {
            // 添加单据
            bill.setWhitelistType(dto.getType());
            // 添加白名单，并赋值单据id，方便后面查询，最后返回单据id，方便提流程
            baseMapper.insert(bill);
        }

        // 先校验参数并添加白名单
        this.verifyTheMeterAndAdd(dto, bill.getId());
        return String.valueOf(bill.getId());
    }


    /**
     * 修改白名单单据
     *
     * @param dto 白名单单据
     */
    @Override
    @Transactional(rollbackOn = Exception.class)
    public String edit(StationReportWhitelistBillDTO dto) {
        if (dto.getId() == null) {
            throw new BusinessException("id不能为空");
        }
        if (CollUtil.isEmpty(dto.getMeterIdList())) {
            throw new BusinessException("电表id列表不能为空");
        }
        // 流程中状态不能修改
        StationReportWhitelistBill bill = baseMapper.selectById(dto.getId());
        if (bill == null) {
            throw new BusinessException("该条数据已被删除");
        }
        if (BillStatus.DRAFT.getCode().equals(bill.getBillStatus()) || BillStatus.REMOVE.getCode().equals(bill.getBillStatus())) {
            BeanUtils.copyBeanProp(bill, dto);
            bill.setBillStatus(BillStatus.DRAFT.getCode());
            // 流程id清空,准备提交新的流程 energy
            bill.setProcInstId(null);
            baseMapper.updateById(bill);
            // 清空之前的白名单重新添加
            new StationReportWhitelist().delete(Wrappers.<StationReportWhitelist>lambdaQuery()
                    .eq(StationReportWhitelist::getBillId, bill.getId())
            );
            // 检验重复
            this.verifyTheMeterAndAdd(dto, bill.getId());

            return String.valueOf(dto.getId());
        } else {
            throw new BusinessException("只允许草稿、移除的状态可删除");
        }


    }

    /**
     * 详情
     *
     * @param id 白名单单据id
     */
    @Override
    public StationReportWhitelistBillVO findById(Long id) {
        StationReportWhitelistBill bill = baseMapper.selectById(id);
        if (bill == null) {
            throw new BusinessException("未找到白名单单据");
        }
        StationReportWhitelistBillQuery query = new StationReportWhitelistBillQuery();
        query.setId(id);
        MPJLambdaWrapper<StationReportWhitelistBill> wrapper = getWrapper(query);
        StationReportWhitelistBillVO vo = baseMapper.selectJoinOne(StationReportWhitelistBillVO.class, wrapper);
        BeanUtils.copyBeanProp(vo, bill);
        // 查询相关电表
        List<Long> meterIdList = new StationReportWhitelist().selectList(Wrappers.<StationReportWhitelist>lambdaQuery()
                .select(StationReportWhitelist::getMeterId)
                .eq(StationReportWhitelist::getBillId, bill.getId())
        ).stream().map(StationReportWhitelist::getMeterId).collect(Collectors.toList());

        for (Long meterId : meterIdList) {
            PowerAmmeterorprotocolVO meter = ammeterorprotocolService.findById(meterId);
            if (MeterCategory.ELECTRIC_METER.getCode().equals(meter.getCategory())) {
                List<Long> stationList = new PowerAmmeterorprotocol().selectList(Wrappers.<PowerAmmeterorprotocol>lambdaQuery()
                        .select(PowerAmmeterorprotocol::getStationcode)
                        .eq(PowerAmmeterorprotocol::getCategory, MeterCategory.ELECTRIC_METER.getCode())
                        .eq(PowerAmmeterorprotocol::getAmmetername, meter.getAmmetername())
                        .groupBy(PowerAmmeterorprotocol::getStationcode)
                ).stream().map(PowerAmmeterorprotocol::getStationcode).distinct().collect(Collectors.toList());
                meter.setStationTotal(stationList.size());
                meter.setStationCodes(CollUtil.join(stationList, ","));
            } else {
                List<Long> stationList = new PowerAmmeterorprotocol().selectList(Wrappers.<PowerAmmeterorprotocol>lambdaQuery()
                        .select(PowerAmmeterorprotocol::getStationcode)
                        .ne(PowerAmmeterorprotocol::getCategory, MeterCategory.ELECTRIC_METER.getCode())
                        .eq(PowerAmmeterorprotocol::getProtocolname, meter.getProtocolname())
                        .groupBy(PowerAmmeterorprotocol::getProtocolname)
                ).stream().map(PowerAmmeterorprotocol::getStationcode).distinct().collect(Collectors.toList());
                meter.setStationTotal(stationList.size());
                meter.setStationCodes(CollUtil.join(stationList, ","));
            }
            // 如果是一表多站，统计站点数量
/*            if (WhitelistType.ONE_WATCH_HAS_MANY_STATIONS.getCode().equals(vo.getWhitelistType())) {
//                 电表与协议分别查询
                OneTableMultiStationListCountVO oneTableMultiStationListCount = powerAmmeterorprotocolMapper.oneTableMultiStationListCount(meter.getAmmetername());
                meter.setStationTotal(oneTableMultiStationListCount.getStationTotal());
                meter.setStationCodes(oneTableMultiStationListCount.getStationCodes());
            }*/
            vo.getMeterList().add(meter);
        }
        this.setAdditionalParameters(vo);
        // 获取附件
        List<MpAttachments> fileList = new MpAttachments().selectList(Wrappers.<MpAttachments>lambdaQuery()
                .eq(MpAttachments::getBusiId, vo.getId())
        );
        vo.setFileList(fileList);

        return vo;
    }

    public static void main(String[] args) {
        System.out.println(IdWorker.getId());
    }

    /**
     * 校验电表并添加
     *
     * @param dto    白名单单据
     * @param billId 白名单单据id
     */
    private void verifyTheMeterAndAdd(StationReportWhitelistBillDTO dto, long billId) {
        for (Long meterId : dto.getMeterIdList()) {
            // 查询电表信息,后面赋值电表编号
            PowerAmmeterorprotocol dbWhitelist = new PowerAmmeterorprotocol().selectOne(Wrappers.<PowerAmmeterorprotocol>lambdaQuery()
                    .select(
                            PowerAmmeterorprotocol::getAmmetername,
                            PowerAmmeterorprotocol::getProtocolname,
                            PowerAmmeterorprotocol::getCategory,
                            PowerAmmeterorprotocol::getProjectname
                    )
                    .eq(PowerAmmeterorprotocol::getId, meterId)
                    .last("LIMIT 1")
            );

            // 白名单去重判断
            boolean alreadyExist = new StationReportWhitelist().selectCount(Wrappers.<StationReportWhitelist>lambdaQuery()
                    .eq(StationReportWhitelist::getMeterId, meterId)
                    .eq(StationReportWhitelist::getType, dto.getType())
            ) > 0;
            if (alreadyExist) {
                throw new BusinessException(StrUtil.format("电表[{}]已存在", dbWhitelist.getProjectname()));
            }

            StationReportWhitelist whitelist = new StationReportWhitelist();
            whitelist.setMeterId(meterId);
            if (MeterCategory.ELECTRIC_METER.getCode().equals(dbWhitelist.getCategory())) {
                whitelist.setMeterCode(dbWhitelist.getAmmetername());
            } else {
                whitelist.setMeterCode(dbWhitelist.getProtocolname());
            }
            whitelist.setType(dto.getType());
            whitelist.setBillId(billId);
            whitelist.setCreateTime(new Date());
            whitelist.setBillStatus(BillStatus.DRAFT.getCode());
            whitelist.insert();
        }
    }

    /**
     * 删除白名单单据
     *
     * @param id 白名单单据id
     */
    @Override
    public void del(Long id) {
        // 只能移除状态、草稿状态可以删除
        StationReportWhitelistBill bill = getById(id);
        if (bill != null) {
            if (BillStatus.DRAFT.getCode().equals(bill.getBillStatus()) || BillStatus.REMOVE.getCode().equals(bill.getBillStatus())) {
                // 删除单据
                baseMapper.deleteById(id);

                // 删除白名单电表
                new StationReportWhitelist().delete(Wrappers.<StationReportWhitelist>lambdaQuery()
                        .eq(StationReportWhitelist::getBillId, id)
                );
            } else {
                throw new BusinessException("只允许草稿、移除的状态可删除");
            }
        }
    }

    /**
     * 流程回调
     *
     * @param wfModel 流程实例
     */
    @Override
    public void uniflowCallBack(WFModel wfModel) {
        log.info("白名单流程回调:{}-{}-{}", wfModel.getBusiId(), wfModel.getBusiAlias(), wfModel.getProcInstId());
        StationReportWhitelistBill whitelist = new StationReportWhitelistBill();
        whitelist.setId(Long.valueOf(wfModel.getBusiId()));
        whitelist.setProcInstId(wfModel.getProcInstId());
        // 流程开始
        if (StringUtils.isNotEmpty(wfModel.getCallbackType())
                && "PROCESS_STARTED".equals(wfModel.getCallbackType())
                && wfModel.getVariables().containsKey("firstNode")
                && wfModel.getVariables().get("firstNode").equals(true)) {
            if ("ADD_WHITELIST".equals(wfModel.getBusiAlias())) {
                // 修改单据状态为流程中
                whitelist.setBillStatus(BillStatus.PROCESSING.getCode());
            } else {
                // 修改单据状态为修改流程中
                whitelist.setBillStatus(BillStatus.MODIFY_PROCESSING.getCode());
            }
            this.updateById(whitelist);
        } else if (StringUtils.isNotEmpty(wfModel.getCallbackType()) && "PROCESS_COMPLETED".equals(wfModel.getCallbackType())) {
            // 修改单据状态为流程结束
            try {
                whitelist.setBillStatus(BillStatus.COMPLETED.getCode());
                this.updateById(whitelist);
            } catch (Exception e) {
                log.info("白名单流程回调修改单据状态为已完成并更新数据异常");
                OperLog model = new OperLog();
                model.setOperName("WHITELIST");
                model.setTitle("白名单流程回调修改单据状态为已完成并更新数据异常");
                model.setMethod("updateDateByChange");
                model.setErrorMsg("白名单id:" + wfModel.getBusiId() + " 流程：" + wfModel.getBusiAlias() + " 用户：" + wfModel.getApplyUserId());
                model.setOperTime(new Date());
                operLogMapper.insert(model);
                throw e;
            }
        }
    }

    @Override
    public IPage<StationReportWhitelistBillVO> selectList(Page<StationReportWhitelistBillVO> page, StationReportWhitelistBillQuery query) {
        MPJLambdaWrapper<StationReportWhitelistBill> wrapper = this.getWrapper(query);

        // 流程表
        wrapper.leftJoin(WhitelistWfProcInst.class, WhitelistWfProcInst::getId, StationReportWhitelistBill::getProcInstId);
        wrapper.select(WhitelistWfProcInst::getBusiAlias);

        wrapper.orderByDesc(StationReportWhitelist::getId);
        Page<StationReportWhitelistBillVO> voPage = baseMapper.selectJoinPage(page, StationReportWhitelistBillVO.class, wrapper);


        for (StationReportWhitelistBillVO vo : voPage.getRecords()) {
            this.setAdditionalParameters(vo);
        }
        return voPage;
    }

    @Override
    public List<OneStopIsMoreThanOneWatchExport> export(Page<StationReportWhitelistBillVO> page, StationReportWhitelistBillQuery query) {
        MPJLambdaWrapper<StationReportWhitelistBill> wrapper = this.getWrapper(query);
        // 流程表
        wrapper.leftJoin(WhitelistWfProcInst.class, WhitelistWfProcInst::getId, StationReportWhitelistBill::getProcInstId);
        wrapper.select(WhitelistWfProcInst::getBusiAlias);
        wrapper.orderByDesc(StationReportWhitelist::getId);

        List<StationReportWhitelistBillVO> list;
        List<OneStopIsMoreThanOneWatchExport> exportList = Lists.newArrayList();
        if (query.isExportAll()) {
            list = baseMapper.selectJoinList(StationReportWhitelistBillVO.class, wrapper);
        } else {
            Page<StationReportWhitelistBillVO> voPage = baseMapper.selectJoinPage(page, StationReportWhitelistBillVO.class, wrapper);
            list = voPage.getRecords();
        }

        for (StationReportWhitelistBillVO vo : list) {
            this.setAdditionalParameters(vo);
            exportList.add(OneStopIsMoreThanOneWatchExport.builder()
                    .stationtypeName(vo.getStationtypeName())
                    .companyName(vo.getCompanyName())
                    .countryName(vo.getCountryName())
                    .stationMetersCount(vo.getStationMetersCount())
                    .billStatusName(vo.getBillStatusName())
                    .stationName(vo.getStationname())
                    .stationcode(vo.getStationcode())
                    .build());
        }
        return exportList;
    }

    @Override
    public void notAvailable(Long id) {
        if (id != null) {
            StationReportWhitelistBill bill = new StationReportWhitelistBill();
            bill.setId(id);
            bill.setBillStatus(BillStatus.REMOVE.getCode());

            this.updateById(bill);
        }
    }

    public MPJLambdaWrapper<StationReportWhitelistBill> getWrapper(StationReportWhitelistBillQuery query) {
        MPJLambdaWrapper<StationReportWhitelistBill> wrapper = new MPJLambdaWrapper<>();
        wrapper.eq(query.getId() != null, StationReportWhitelistBill::getId, query.getId());
        wrapper.selectAll(StationReportWhitelistBill.class);
        // 局站信息
        wrapper.selectAll(PowerStationInfo.class);
        wrapper.leftJoin(PowerStationInfo.class, PowerStationInfo::getStationcode, StationReportWhitelistBill::getStationcode);
        // 局站名称
        wrapper.like(StrUtil.isNotBlank(query.getStationname()), PowerStationInfo::getStationname, query.getStationname());
        // 局站id
        wrapper.selectAs(PowerStationInfo::getId, StationReportWhitelistBillVO::getStationId);
        wrapper.eq(query.getCompany() != null, PowerStationInfo::getCompany, query.getCompany());
        wrapper.eq(query.getCountry() != null, PowerStationInfo::getCountry, query.getCountry());
        wrapper.eq(query.getBillStatus() != null, StationReportWhitelistBill::getBillStatus, query.getBillStatus());
        wrapper.eq(StrUtil.isNotBlank(query.getType()), StationReportWhitelistBill::getWhitelistType, query.getType());
        return wrapper;
    }

    /**
     * 设置额外属性
     */
    private void setAdditionalParameters(StationReportWhitelistBillVO vo) {
        // 局站类型
        vo.setStationtypeName(MyDict.findDictLabel(MyDict.TYPE.BUR_STAND_TYPE, String.valueOf(vo.getStationtype())));
        this.getOrgName(vo);
        // 当前局站关联电表数
        vo.setStationMetersCount(new StationReportWhitelist().selectCount(Wrappers.<StationReportWhitelist>lambdaQuery()
                .eq(StationReportWhitelist::getBillId, vo.getId())
        ));
        // 获取流程状态名称
        vo.setBillStatusName(BillStatus.getNameByCode(vo.getBillStatus()));
    }

    /**
     * 获取公司名称
     *
     * @param vo 白名单
     */
    private void getOrgName(StationReportWhitelistBillVO vo) {
        // 分公司
        if (vo.getCompany() != null) {
            SysOrganizations orgCompany = stationInfoMapper.selectsysOrgNameById(vo.getCompany());
            if (orgCompany != null) {
                vo.setCompanyName(orgCompany.getOrgName());
            }
        }

        // 部门
        if (vo.getCountry() != null) {
            SysOrganizations orgCountry = stationInfoMapper.selectsysOrgNameById(vo.getCountry());
            if (orgCountry != null) {
                String countryName = "";
                SysOrganizations superOrg = stationInfoMapper.selectsysOrgNameById(Long.valueOf(orgCountry.getParentCompanyNo()));
                if (superOrg != null) {
                    countryName = StrUtil.format("{}-{}", superOrg.getOrgName(), orgCountry.getOrgName());
                } else {
                    countryName = orgCountry.getOrgName();
                }
                vo.setCountryName(countryName);
            }
        }
    }
}
