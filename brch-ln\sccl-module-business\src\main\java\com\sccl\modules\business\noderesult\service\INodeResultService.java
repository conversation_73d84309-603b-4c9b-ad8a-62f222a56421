package com.sccl.modules.business.noderesult.service;


import com.enrising.dcarbon.audit.RefereeResult;
import com.sccl.framework.service.IBaseService;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.business.noderesult.domain.NodeResult;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;

import java.util.Collection;
import java.util.List;

/**
 * 基站一站式稽核结果 服务层
 * 
 * <AUTHOR>
 * @date 2022-11-22
 */
public interface INodeResultService extends IBaseService<NodeResult>
{


    /**
     *获取 分组 nodeResult
     * @param nodeResult
     * @return
     */
    AjaxResult getNodeResult(NodeResult nodeResult);

    /**
     * 获取 billid 最新时间的list
     * @param nodeResult
     * @return
     */
    List<NodeResult> selectListLatestTime(NodeResult nodeResult);

    /**
     * 将责任链的 结果插入到数据库中
     * @param refereeResults
     * @return
     */
    int insertListResults(List<RefereeResult> refereeResults);

    /**
     * 汇总 本次台账 上次台账 波动
     * @param nodeResult
     * @return
     */
    AjaxResult collectPowerChange(NodeResult nodeResult);

    /**
     * 去找 本次台账 同一站址的 上次台账
     * @param nodeResult
     * @return
     */
    NodeResult selectupToDateNode(NodeResult nodeResult);

    /**
     * 去找 本次台账 同一站址的 上次台账
     * @param powerNode
     * @return
     */
    NodeResult selectNodeLast(NodeResult powerNode);

    /**
     * 汇总 node_result 结果
     * @param mssAccountbill
     * @return
     */
    AjaxResult collectNodeResult(MssAccountbill mssAccountbill);

    /**
     * 根据ids 批量获取数据
     * @param ids
     * @return
     */
    List<NodeResult> get(List<Long> ids);

    /**
     * 新节点
     * @param mssAccountbill
     * @return
     */
    AjaxResult collectNodeResultNew(MssAccountbill mssAccountbill);
    AjaxResult collectNodeResultNew2(MssAccountbill mssAccountbill);

}
