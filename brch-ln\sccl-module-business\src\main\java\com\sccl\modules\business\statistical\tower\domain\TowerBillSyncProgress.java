package com.sccl.modules.business.statistical.tower.domain;

import com.sccl.modules.business.statistical.framework.StatisticalIndex;
import com.sccl.modules.business.statistical.framework.StatisticalIndexObject;
import lombok.Data;

import java.util.Date;

/**
 * 铁塔同步进度
 *
 * <AUTHOR>
 * @Date 2022/10/31 11:32
 * @Email <EMAIL>
 */
@Data
public class TowerBillSyncProgress implements StatisticalIndexObject {
    /**
     * 地市名
     */
    private String name;
    /**
     * 推送日期
     */
    private Date pushDate;
    /**
     * 推送数目
     */
    private Integer pushCount;
    /**
     * 处理类型
     */
    private String handleType;
    /**
     * 当前步骤
     */
    private String step;
    /**
     * 下一步骤
     */
    private String nextStep;
    /**
     * 回调时间
     */
    private Long callbackTime;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 总数目
     */
    private Integer totalCount;
    /**
     * 已完成数目
     */
    private Integer finishedCount;
    /**
     * 错误信息
     */
    private String errMsg;

    @Override
    public String getTitle() {
        return name + "进度";
    }

    @Override
    public Date getStatisticalDate() {
        return pushDate;
    }

    @Override
    public StatisticalIndex getIndexObject() {
        StatisticalIndex index = new StatisticalIndex();
        index.setTitle(getTitle());
        index.setContent(getContent());
        index.setContentType(this.getClass());
        index.setStatisticalTime(getStatisticalDate());
        return index;
    }
}
