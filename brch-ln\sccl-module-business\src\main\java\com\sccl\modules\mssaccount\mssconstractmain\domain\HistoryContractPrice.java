package com.sccl.modules.mssaccount.mssconstractmain.domain;

import com.sccl.modules.autojob.util.convert.StringUtils;
import lombok.Data;

@Data
public class HistoryContractPrice {
    private String company;
    private String companyname;
    private String country;
    private String countryname;
    private String ammename;
    private String price;
    private String transferpowersupplycontractprice;
    private String transferpowersupplycontractcode;
    private String accountno;
    private String startdate;
    private String enddate;
    private String inputdate;

    public static void ProcessPrice(HistoryContractPrice item) {
        String contractCode = item.getTransferpowersupplycontractcode();
        String contractPrice = item.getTransferpowersupplycontractprice();
        item.setTransferpowersupplycontractprice(MssConstractmain.extractTextWithRegex(contractPrice, "\\d+\\.\\d+元/度"));
        String price = item.getPrice();
        if (StringUtils.isBlank(contractCode)) {
            item.setTransferpowersupplycontractcode("对应合同为空");
        }
        if (StringUtils.isBlank(price)) {
            item.setPrice("对应单价为空");
        }
        if (StringUtils.isBlank(contractPrice)) {
            item.setTransferpowersupplycontractprice("对应签约合同单价为空");
        }
    }
}
