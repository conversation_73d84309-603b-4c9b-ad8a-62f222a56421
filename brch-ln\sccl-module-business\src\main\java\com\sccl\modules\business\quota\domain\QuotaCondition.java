package com.sccl.modules.business.quota.domain;

import com.sccl.framework.web.domain.IdNameVO;
import jdk.nashorn.internal.ir.IdentNode;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 定额 查询条件
 *
 * <AUTHOR>
 * @date 2019-05-14
 */
public class QuotaCondition {
    /** id */
    private Long id;
    /** 关联电表或者协议 */
    private String ammeterCode;
    /** 项目名称 */
    private String projectName;
    /** 责任中心 */
    private Long country;
    /** 所属分公司 */
    private Long company;
    /** 备注信息 */
    private String remark;
    /** 状态 1：正常  0：无效*/
    private Integer status;
    /** 审批人*/
    private String approverName;

    /** 单据状态  0：草稿 1：流程中 2：申请流程归档完成*/
    private Integer billStatus;

    /** 用户id*/
    private Long userId;

    /** 责任中心*/
    private List<Map<String,Object>> countrys;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public List<Map<String,Object>> getCountrys() {
        return countrys;
    }

    public void setCountrys(List<Map<String,Object>> countrys) {
        this.countrys = countrys;
    }

    public Integer getBillStatus() {
        return billStatus;
    }

    public void setBillStatus(Integer billStatus) {
        this.billStatus = billStatus;
    }

    public String getAmmeterCode() {
        return ammeterCode;
    }

    public void setAmmeterCode(String ammeterCode) {
        this.ammeterCode = ammeterCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public Long getCountry() {
        return country;
    }

    public void setCountry(Long country) {
        this.country = country;
    }

    public Long getCompany() {
        return company;
    }

    public void setCompany(Long company) {
        this.company = company;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getApproverName() {
        return approverName;
    }

    public void setApproverName(String approverName) {
        this.approverName = approverName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}
