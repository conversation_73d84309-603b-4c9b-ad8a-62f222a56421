package com.sccl.modules.business.jhanomalydetails.domain;

import com.sccl.common.utils.DateUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;


/**
 * 稽核结果详情表 jh_anomaly_details
 * 
 * <AUTHOR>
 * @date 2024-02-27
 */
public class JhAnomalyDetails extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
    /** 异常类型 */
    private String yclx;
	/** 异常类型名称 */
	private String yclxmc;
    /** 报账单期号 */
    private String bzdqh;
    /** 报账单id */
    private String bzdid;
    /** 台账期号 */
    private String tzqh;
	/** 台账id */
	private String tzid;
    /** 类型 */
    private String lx;
    /** 电表户名/协议编码 */
    private String dbhm;
    /** 集团站址编码 */
    private String jtzzbm;
    /** 铁塔站址编码 */
    private String ttzzbm;
    /** 本次单价（含税，元） */
    private String dj;
    /** 协议单价/电表单价 */
    private String xydj;
    /** 台账关联集团站址编码 */
    private String tzgljjtzzbm;
    /** 台账关联铁塔站址编码 */
    private String tzglttzzbm;
    /** 电表基础信息关联的集团站址编码 */
    private String dbjcxxgljtzzbm;
    /** 电表基础信息关联铁塔站址编码 */
    private String dbjcxxglttzzbm;
    /** 台账关联的支付对象 */
    private String tzglzfdx;
    /** 台站关联收款账号 */
    private String tzglskzh;
    /** 电表基础表关联的支付对象 */
    private String dbjcbzfdx;
    /** 电表基础表关联的收款账号 */
    private String dbjcbskzh;
    /** 起始时间 */
    private String qssj;
    /** 截至时间 */
    private String jzsj;
    /** 本期启度 */
    private String bqqd;
    /** 本期止度 */
    private String bqzd;
    /** 报账电量 */
    private String bzdl;
    /** 标准日均电量 */
    private String bzrjdl;
    /** 波动幅度 */
    private String bdfd;
    /** 起租单维护数 */
    private String qzdwhs;
    /** 运营商1 */
    private String yys1;
    /** 比例1 改为铁塔推送电信比例*/
    private String bl1;
    /** 运营商2 */
    private String yys2;
    /** 比例2 改为协议管理电信比例*/
    private String bl2;
    /** 电信比例 */
    private String dxbl;
    /** 最近一次报账时间 */
    private String zjycbzsj;
    /** 最近报账期号 */
    private String zjbzqh;
    /** 间隔天数 */
    private String jg;
    /** 稽核时间 */
    private Date jhsj;
    /** 台账日均电量 */
    private String tzrjdl;
    /** 异常原因 */
    private String abnormalCause;
    /** 异常原因描述 */
    private String abnormalCauseDescribe;
	/** 最近一次报账比例 */
	private String zjycbzbl;

	/** 稽核结果 0-正常 1-异常  */
	private String jhjg;

	public String getJhjg() {
		return jhjg;
	}

	public void setJhjg(String jhjg) {
		this.jhjg = jhjg;
	}

	public String getZjycbzbl() {
		return zjycbzbl;
	}

	public void setZjycbzbl(String zjycbzbl) {
		this.zjycbzbl = zjycbzbl;
	}

	public void setYclx(String yclx)
	{
		this.yclx = yclx;
	}

	public String getYclx()
	{
		return yclx;
	}

	public void setYclxmc(String yclxmc)
	{
		this.yclxmc = yclxmc;
	}

	public String getYclxmc()
	{
		return yclxmc;
	}

	public void setBzdqh(String bzdqh)
	{
		this.bzdqh = bzdqh;
	}

	public String getBzdqh() 
	{
		return bzdqh;
	}

	public void setBzdid(String bzdid)
	{
		this.bzdid = bzdid;
	}

	public String getBzdid() 
	{
		return bzdid;
	}

	public void setTzqh(String tzqh)
	{
		this.tzqh = tzqh;
	}

	public String getTzqh() 
	{
		return tzqh;
	}

	public void setTzid(String tzid)
	{
		this.tzid = tzid;
	}

	public String getTzid() { return tzid; }

	public void setLx(String lx)
	{
		this.lx = lx;
	}

	public String getLx() 
	{
		return lx;
	}

	public void setDbhm(String dbhm)
	{
		this.dbhm = dbhm;
	}

	public String getDbhm() 
	{
		return dbhm;
	}

	public void setJtzzbm(String jtzzbm)
	{
		this.jtzzbm = jtzzbm;
	}

	public String getJtzzbm() 
	{
		return jtzzbm;
	}

	public void setTtzzbm(String ttzzbm)
	{
		this.ttzzbm = ttzzbm;
	}

	public String getTtzzbm() 
	{
		return ttzzbm;
	}

	public void setDj(String dj)
	{
		this.dj = dj;
	}

	public String getDj() 
	{
		return dj;
	}

	public void setXydj(String xydj)
	{
		this.xydj = xydj;
	}

	public String getXydj() 
	{
		return xydj;
	}

	public void setTzgljjtzzbm(String tzgljjtzzbm)
	{
		this.tzgljjtzzbm = tzgljjtzzbm;
	}

	public String getTzgljjtzzbm() 
	{
		return tzgljjtzzbm;
	}

	public void setTzglttzzbm(String tzglttzzbm)
	{
		this.tzglttzzbm = tzglttzzbm;
	}

	public String getTzglttzzbm() 
	{
		return tzglttzzbm;
	}

	public void setDbjcxxgljtzzbm(String dbjcxxgljtzzbm)
	{
		this.dbjcxxgljtzzbm = dbjcxxgljtzzbm;
	}

	public String getDbjcxxgljtzzbm() 
	{
		return dbjcxxgljtzzbm;
	}

	public void setDbjcxxglttzzbm(String dbjcxxglttzzbm)
	{
		this.dbjcxxglttzzbm = dbjcxxglttzzbm;
	}

	public String getDbjcxxglttzzbm() 
	{
		return dbjcxxglttzzbm;
	}

	public void setTzglzfdx(String tzglzfdx)
	{
		this.tzglzfdx = tzglzfdx;
	}

	public String getTzglzfdx() 
	{
		return tzglzfdx;
	}

	public void setTzglskzh(String tzglskzh)
	{
		this.tzglskzh = tzglskzh;
	}

	public String getTzglskzh() 
	{
		return tzglskzh;
	}

	public void setDbjcbzfdx(String dbjcbzfdx)
	{
		this.dbjcbzfdx = dbjcbzfdx;
	}

	public String getDbjcbzfdx() 
	{
		return dbjcbzfdx;
	}

	public void setDbjcbskzh(String dbjcbskzh)
	{
		this.dbjcbskzh = dbjcbskzh;
	}

	public String getDbjcbskzh() 
	{
		return dbjcbskzh;
	}

	public void setQssj(String qssj)
	{
		this.qssj = qssj;
	}

	public String getQssj() 
	{
		return qssj;
	}

	public void setJzsj(String jzsj)
	{
		this.jzsj = jzsj;
	}

	public String getJzsj() 
	{
		return jzsj;
	}

	public void setBqqd(String bqqd)
	{
		this.bqqd = bqqd;
	}

	public String getBqqd() 
	{
		return bqqd;
	}

	public void setBqzd(String bqzd)
	{
		this.bqzd = bqzd;
	}

	public String getBqzd() 
	{
		return bqzd;
	}

	public void setBzdl(String bzdl)
	{
		this.bzdl = bzdl;
	}

	public String getBzdl() 
	{
		return bzdl;
	}

	public void setBzrjdl(String bzrjdl)
	{
		this.bzrjdl = bzrjdl;
	}

	public String getBzrjdl() 
	{
		return bzrjdl;
	}

	public void setBdfd(String bdfd)
	{
		this.bdfd = bdfd;
	}

	public String getBdfd() 
	{
		return bdfd;
	}

	public void setQzdwhs(String qzdwhs)
	{
		this.qzdwhs = qzdwhs;
	}

	public String getQzdwhs() 
	{
		return qzdwhs;
	}

	public void setYys1(String yys1)
	{
		this.yys1 = yys1;
	}

	public String getYys1() 
	{
		return yys1;
	}

	public void setBl1(String bl1)
	{
		this.bl1 = bl1;
	}

	public String getBl1() 
	{
		return bl1;
	}

	public void setYys2(String yys2)
	{
		this.yys2 = yys2;
	}

	public String getYys2() 
	{
		return yys2;
	}

	public void setBl2(String bl2)
	{
		this.bl2 = bl2;
	}

	public String getBl2() 
	{
		return bl2;
	}

	public void setDxbl(String dxbl)
	{
		this.dxbl = dxbl;
	}

	public String getDxbl() 
	{
		return dxbl;
	}

	public void setZjycbzsj(String zjycbzsj)
	{
		this.zjycbzsj = zjycbzsj;
	}

	public String getZjycbzsj() 
	{
		return zjycbzsj;
	}

	public void setZjbzqh(String zjbzqh)
	{
		this.zjbzqh = zjbzqh;
	}

	public String getZjbzqh() 
	{
		return zjbzqh;
	}

	public void setJg(String jg)
	{
		this.jg = jg;
	}

	public String getJg() 
	{
		return jg;
	}

	public void setJhsj(Date jhsj)
	{
		this.jhsj = jhsj;
	}

	public Date getJhsj() 
	{
		return jhsj;
	}

	public void setTzrjdl(String tzrjdl)
	{
		this.tzrjdl = tzrjdl;
	}

	public String getTzrjdl() 
	{
		return tzrjdl;
	}

	public void setAbnormalCause(String abnormalCause)
	{
		this.abnormalCause = abnormalCause;
	}

	public String getAbnormalCause() 
	{
		return abnormalCause;
	}

	public void setAbnormalCauseDescribe(String abnormalCauseDescribe)
	{
		this.abnormalCauseDescribe = abnormalCauseDescribe;
	}

	public String getAbnormalCauseDescribe() 
	{
		return abnormalCauseDescribe;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("yclx", getYclx())
			.append("yclxmc", getYclxmc())
            .append("bzdqh", getBzdqh())
            .append("bzdid", getBzdid())
            .append("tzqh", getTzqh())
			.append("tzid", getTzid())
			.append("lx", getLx())
            .append("dbhm", getDbhm())
            .append("jtzzbm", getJtzzbm())
            .append("ttzzbm", getTtzzbm())
            .append("dj", getDj())
            .append("xydj", getXydj())
            .append("tzgljjtzzbm", getTzgljjtzzbm())
            .append("tzglttzzbm", getTzglttzzbm())
            .append("dbjcxxgljtzzbm", getDbjcxxgljtzzbm())
            .append("dbjcxxglttzzbm", getDbjcxxglttzzbm())
            .append("tzglzfdx", getTzglzfdx())
            .append("tzglskzh", getTzglskzh())
            .append("dbjcbzfdx", getDbjcbzfdx())
            .append("dbjcbskzh", getDbjcbskzh())
            .append("qssj", getQssj())
            .append("jzsj", getJzsj())
            .append("bqqd", getBqqd())
            .append("bqzd", getBqzd())
            .append("bzdl", getBzdl())
            .append("bzrjdl", getBzrjdl())
            .append("bdfd", getBdfd())
            .append("qzdwhs", getQzdwhs())
            .append("yys1", getYys1())
            .append("bl1", getBl1())
            .append("yys2", getYys2())
            .append("bl2", getBl2())
            .append("dxbl", getDxbl())
            .append("zjycbzsj", getZjycbzsj())
            .append("zjbzqh", getZjbzqh())
            .append("jg", getJg())
            .append("jhsj", getJhsj())
            .append("tzrjdl", getTzrjdl())
            .append("abnormalCause", getAbnormalCause())
            .append("abnormalCauseDescribe", getAbnormalCauseDescribe())
				.append("zjycbzbl", getZjycbzbl())
            .toString();
    }


	public static JhAnomalyDetails compositeAudit(String yclx,String tzId,String BZid,String jhsj){
		JhAnomalyDetails jhAnomalyDetails = new JhAnomalyDetails();
		jhAnomalyDetails.setYclx(yclx);
		jhAnomalyDetails.setTzid(tzId);
		jhAnomalyDetails.setBzdid(BZid);
		jhAnomalyDetails.setJhsj(DateUtils.dateTime("yyyy-MM-dd HH:mm:ss",jhsj));
		return jhAnomalyDetails;

	}
}
