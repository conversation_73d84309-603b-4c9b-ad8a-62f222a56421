package com.sccl.modules.business.poweraudit.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: 芮永恒
 * @CreateTime: 2024-02-22  11:19
 * @Description: TODO
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PowerAuditVO extends PowerAuditEntity{

    // 地级市
    private String city;

    // 地级市编码
    private String cityCode;

    // 区县
    private String countyCompanies;

    // 区县编码
    private String countyCompaniesCode;

    // 运营分局
    private String operationsBranch;

    // 站址类型 1:基站类，0:非基站
    private Integer siteType;

    // 月份  202402
    private String month;

    // 导出文件名(不带后缀.xlsx)
    private String fileName;

    // 导出用，（地市/区县）
    private String type;

    // 导出什么数据
    private String exportName;

    private long pageSize;

    private long pageNum;

    private Integer isnew;

    private Integer property;

    private Long electrotype;

    private Long toweraccountid;

    private List<String> ammeterids;
}
