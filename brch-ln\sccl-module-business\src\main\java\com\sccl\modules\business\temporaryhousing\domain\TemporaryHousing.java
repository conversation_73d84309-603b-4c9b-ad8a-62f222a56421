package com.sccl.modules.business.temporaryhousing.domain;

import com.sccl.framework.web.domain.BaseEntity;

/**
 * __房屋临时表__<br/>
 * 2019/10/29
 *
 * <AUTHOR>
 */
public class TemporaryHousing extends BaseEntity {

    private static final long serialVersionUID = 1L;
    /** 类型 */
    private Integer type;

    /** 房屋编号 */
    private String housingcode;

    /** 房屋名称 */
    private String housingname;

    /** 所有权单位编号（承租单位编码） */
    private String ownershipunitcode;

    /** 所有权单位名称（承租单位名称） */
    private String ownershipunitname;

    /** 房屋座落详细地址 */
    private String houselocateddetailaddress;

    /** 所有权单位属性（所属帐套） */
    private String ownershipunitattribute;

    /** 所在行政区划编码（行政区划编码） */
    private String administrativedivisioncode;

    /** 所在行政区划（行政区划名称） */
    private String administrativedivision;

    /** 结构（建筑结构） */
    private String structure;

    /** 总建筑面积 */
    private String grossfloorarea;

    /** 办公面积 */
    private String officespace;

    /** 机房面积 */
    private String roomarea;

    /** 出租面积 */
    private String rentarea;

    /** 营业厅面积 */
    private String businesshallarea;

    /** 闲置面积 */
    private String sparearea;


    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getHousingcode() {
        return housingcode;
    }

    public void setHousingcode(String housingcode) {
        this.housingcode = housingcode;
    }

    public String getHousingname() {
        return housingname;
    }

    public void setHousingname(String housingname) {
        this.housingname = housingname;
    }

    public String getOwnershipunitcode() {
        return ownershipunitcode;
    }

    public void setOwnershipunitcode(String ownershipunitcode) {
        this.ownershipunitcode = ownershipunitcode;
    }

    public String getOwnershipunitname() {
        return ownershipunitname;
    }

    public void setOwnershipunitname(String ownershipunitname) {
        this.ownershipunitname = ownershipunitname;
    }

    public String getHouselocateddetailaddress() {
        return houselocateddetailaddress;
    }

    public void setHouselocateddetailaddress(String houselocateddetailaddress) {
        this.houselocateddetailaddress = houselocateddetailaddress;
    }

    public String getOwnershipunitattribute() {
        return ownershipunitattribute;
    }

    public void setOwnershipunitattribute(String ownershipunitattribute) {
        this.ownershipunitattribute = ownershipunitattribute;
    }

    public String getAdministrativedivisioncode() {
        return administrativedivisioncode;
    }

    public void setAdministrativedivisioncode(String administrativedivisioncode) {
        this.administrativedivisioncode = administrativedivisioncode;
    }

    public String getAdministrativedivision() {
        return administrativedivision;
    }

    public void setAdministrativedivision(String administrativedivision) {
        this.administrativedivision = administrativedivision;
    }

    public String getStructure() {
        return structure;
    }

    public void setStructure(String structure) {
        this.structure = structure;
    }

    public String getGrossfloorarea() {
        return grossfloorarea;
    }

    public void setGrossfloorarea(String grossfloorarea) {
        this.grossfloorarea = grossfloorarea;
    }

    public String getOfficespace() {
        return officespace;
    }

    public void setOfficespace(String officespace) {
        this.officespace = officespace;
    }

    public String getRoomarea() {
        return roomarea;
    }

    public void setRoomarea(String roomarea) {
        this.roomarea = roomarea;
    }

    public String getRentarea() {
        return rentarea;
    }

    public void setRentarea(String rentarea) {
        this.rentarea = rentarea;
    }

    public String getBusinesshallarea() {
        return businesshallarea;
    }

    public void setBusinesshallarea(String businesshallarea) {
        this.businesshallarea = businesshallarea;
    }

    public String getSparearea() {
        return sparearea;
    }

    public void setSparearea(String sparearea) {
        this.sparearea = sparearea;
    }
}
