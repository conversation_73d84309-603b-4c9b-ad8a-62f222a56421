package com.sccl.modules.dataperfect;

import cn.hutool.core.util.StrUtil;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.autojob.util.convert.MessageMaster;
import com.sccl.modules.dataperfect.domain.CollectMeterVo;
import com.sccl.modules.mssaccount.mssinterface.domain.CollectMeterFail;
import com.sccl.modules.system.attachments.domain.Attachments;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据治理-》采集治理
 */
@RestController
@Slf4j
@RequestMapping("/dataperfect")
public class ColleterPerfectController {
    private static Map<String, String> EXPORT_COLUMN_MAPFORSYNCCOLLECT = new HashMap<String, String>();
    private static Map<String, String> PROMPT_COLUMN_MAP = new HashMap<String, String>();

    static {
        EXPORT_COLUMN_MAPFORSYNCCOLLECT.put("budget", "采集账期");
        EXPORT_COLUMN_MAPFORSYNCCOLLECT.put("cityCode", "市编码");
        EXPORT_COLUMN_MAPFORSYNCCOLLECT.put("countyCode", "区县编码");
        EXPORT_COLUMN_MAPFORSYNCCOLLECT.put("stationCode", "局站编码");
        EXPORT_COLUMN_MAPFORSYNCCOLLECT.put("energyData", "账期平均能耗");

    }

//    @Autowired
//    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private CollecterPerfectService collecterPerfectService;


    public static void main(String[] args) {

    }

    /**
     * 采集数据漏传list
     *
     * @param stationcode
     * @return
     * @throws Exception
     */
    @GetMapping("/selectFailCollectMeter")
    @ResponseBody
    public AjaxResult selectFailCollectMeter(@RequestParam(value = "pageNum", defaultValue = "1") int pageNum, @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) throws Exception {
        List<CollectMeterVo> list = collecterPerfectService.selectFailCollectMeter(pageNum, pageSize);
        return AjaxResult.success(list);
    }

    /**
     * 重新推送采集失败数据
     *
     * @param vo
     * @return
     */
    @GetMapping("/retryFailCollectMeter")
    @ResponseBody
    public AjaxResult retryFailCollectMeter(CollectMeterVo vo) {
        String result = collecterPerfectService.retryFailCollectMeter(vo);
        return AjaxResult.success(result);
    }

    /**
     * 同步增量电表到计量设备表中
     *
     * @param id
     * @return
     * @throws Exception
     */
    @GetMapping("/syncIncrementalMeter/{stationcode}")
    @ResponseBody
    public AjaxResult syncIncrementalMeter(@PathVariable("stationcode") String stationcode) throws Exception {
        String result = collecterPerfectService.syncIncrementalMeter(stationcode);
        return AjaxResult.success(result);
    }

    /**
     * 分批处理sta待统计异常
     *
     * @param id
     * @return
     * @throws Exception
     */
    @GetMapping("/syncIncrementalMeter/staException")
    @ResponseBody
    public AjaxResult staException(@RequestParam("bitch") String bitch) throws Exception {
        String result = collecterPerfectService.staException(bitch);
        return AjaxResult.success(result);
    }

    /**
     * 分批插入sta汇总结果表
     *
     * @param id
     * @return
     * @throws Exception
     */
    @GetMapping("/syncIncrementalMeter/staResult")
    @ResponseBody
    public AjaxResult staResult(@RequestParam("bitch") String bitch) throws Exception {
        String result = collecterPerfectService.staResult(bitch);
        return AjaxResult.success(result);
    }

    /**
     * 分批插入stagateway汇总表
     *
     * @param id
     * @return
     * @throws Exception
     */
    @GetMapping("/syncIncrementalMeter/stagatewayResult")
    @ResponseBody
    public AjaxResult stagatewayResult(@RequestParam("bitch") String bitch) throws Exception {
        String result = collecterPerfectService.stagatewayResult(bitch);
        return AjaxResult.success(result);
    }

    /**
     * 分批汇总 台账日均电量波动异常
     *
     * @param id
     * @return
     * @throws Exception
     */
    @GetMapping("/syncIncrementalMeter/AccountAvgException")
    @ResponseBody
    public AjaxResult AccountAvgException(@RequestParam("time") String time, @RequestParam("bitch") String bitch) throws Exception {
        String result = collecterPerfectService.AccountAvgException(time,bitch);
        return AjaxResult.success(result);
    }

    /**
     * 采集数据完善，传入对应的账期，向集团发送对应账期全量起止时间的采集数据
     *
     * @param bitch
     * @return
     * @throws Exception
     */
    @GetMapping("/syncIncrementalMeter/collectAll")
    @ResponseBody
    public AjaxResult collectAll(@RequestParam(required = false, value = "budget") String budget) throws Exception {
        if (budget == null) {
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM");
            LocalDate lastMonth = LocalDate.now().minusMonths(1);
            String lastBudget = df.format(lastMonth);
            budget = lastBudget;
        }
        collecterPerfectService.collectAll(budget);
        return AjaxResult.success(budget + "账期全量采集数据传送完毕");
    }

    /**
     * 测试推送集团日电量
     */
    @GetMapping(value = "/autoCollectForFixedLine")
    @ResponseBody
    public void autoCollectForFixedLine() {
        long startTime = System.currentTimeMillis();
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM");
        LocalDate last = LocalDate.now().minusMonths(1L);
//        String lastMonth = dtf.format(last);
        String lastMonth = "2024-04";
        try {
            for (int timeFlag = 1; timeFlag <= 2; timeFlag++) {
                log.info("lastMonth:{};timeFlag:{}", lastMonth, timeFlag);
                AjaxResult result = collectAllPro(lastMonth, timeFlag);

                log.info("{}", result.get("msg"));
                log.info("{}调用固网采集({}): 耗时{}s",
                        lastMonth,
                        timeFlag == 1 ? "电表历史通过电表id分组"
                                : timeFlag == 2 ? "电表历史通过局站id分组"
                                : timeFlag == 3 ? "电表历史全量有效电表" :
                                "未知的分支",
                        (System.currentTimeMillis() - startTime) / 1000);
            }

        } catch (Exception e) {
            log.error("推送失败", e);
            log.info("{}调用固网采集结束: 耗时  {} s", lastMonth, ((System.currentTimeMillis() - startTime) / 1000));
        }
    }

    @GetMapping("/syncIncrementalMeter/collectAllProFixnew")
    @ResponseBody
    public AjaxResult collectAllProFixnew(
            @RequestParam(value = "budget") String budget,
            @RequestParam(value = "timeFlag") Integer timeFlag
    ) throws Exception {
        log.info("账期：{},业财一致率---上传集团账期全量采集new数据传送开始执行---->", budget);
        if (budget == null) {
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM");
            LocalDate lastMonth = LocalDate.now().minusMonths(1);
            String lastBudget = df.format(lastMonth);
            budget = lastBudget;
        }
        collecterPerfectService.collectAllProFixnewSync(budget,timeFlag);
        return AjaxResult.success(budget + "账期：{},业财一致率---上传集团账期全量采集new数据传送完毕");
    }


    @GetMapping("/syncIncrementalMeter/collectAllProQxm")
    @ResponseBody
    public AjaxResult collectAllProQxm(
            @RequestParam(value = "budget") String budget
    ) throws Exception {
        log.info("账期：{},业财一致率---上传集团账期全量采集new数据传送开始执行---->", budget);
        if (budget == null) {
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM");
            LocalDate lastMonth = LocalDate.now().minusMonths(1);
            String lastBudget = df.format(lastMonth);
            budget = lastBudget;
        }
        collecterPerfectService.collectAllProSyncQxm(budget);
        return AjaxResult.success(budget + "账期：{},业财一致率---上传集团账期全量采集new数据传送完毕");
    }

    @GetMapping("/syncIncrementalMeter/collectAllProCheckAllnew")
    @ResponseBody
    public AjaxResult collectAllProCheckAllnew(
            @RequestParam(value = "budget") String budget
    ) throws Exception {
        log.info("账期：{},业财一致率---全量采集数据new异步开始执行---->", budget);
        if (StrUtil.isBlank(budget)) {
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM");
            LocalDate lastMonth = LocalDate.now().minusMonths(1);
            String lastBudget = df.format(lastMonth);
            budget = lastBudget;
        }
        collecterPerfectService.collectAllProCheckAllnewSync(budget);
        return AjaxResult.success(budget + "业财-->>全量采集数据new传送完毕");
    }


    @GetMapping("/syncIncrementalMeter/collectAllPro")
    @ResponseBody
    public AjaxResult collectAllPro(
            @RequestParam(value = "budget") String budget,
            @RequestParam(value = "timeFlag") Integer timeFlag
    ) throws Exception {
        if (budget == null) {
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM");
            LocalDate lastMonth = LocalDate.now().minusMonths(1);
            String lastBudget = df.format(lastMonth);
            budget = lastBudget;
        }
        log.info("开始固网采集数据传输");
        collecterPerfectService.collectAllPro(budget, timeFlag);
        return AjaxResult.success(budget + "账期全量采集数据传送完毕");
    }

    @GetMapping("/syncIncrementalMeter/collectAllProPro")
    @ResponseBody
    public AjaxResult collectAllProPro(
            @RequestParam(value = "budget") String budget,
            @RequestParam(value = "timeFlag") Integer timeFlag,
            @RequestParam(value = "interval") Integer interval
    ) throws Exception {
        if (budget == null) {
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM");
            LocalDate lastMonth = LocalDate.now().minusMonths(1);
            String lastBudget = df.format(lastMonth);
            budget = lastBudget;
        }
        collecterPerfectService.collectAllProPro(budget, timeFlag, interval);
        return AjaxResult.success(budget + "账期全量采集数据传送完毕");
    }

    /**
     * 优化数据传输，使用budget生成日期范围，从ycyz_null表查询局站编码
     *
     * @param budget       预算账期，格式为：yyyy-MM
     * @param timeFlag     时间标志，用于确定数据处理方式
     * @param interval     数据推送间隔时间（毫秒）
     * @return 处理结果
     */
    @GetMapping("/syncIncrementalMeter/collectAllProProWithStationcodes")
    @ResponseBody
    public AjaxResult collectAllProProWithStationcodes(
            @RequestParam(value = "budget") String budget,
            @RequestParam(value = "timeFlag") Integer timeFlag,
            @RequestParam(value = "interval") Integer interval
    ) {
        if (budget == null) {
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM");
            LocalDate lastMonth = LocalDate.now().minusMonths(1);
            budget = df.format(lastMonth);
        }

        log.info("使用 budget {} 从 ycyz_null 表查询局站编码进行优化数据传输", budget);
        collecterPerfectService.collectAllProProWithStationcodes(budget, timeFlag, interval);
        return AjaxResult.success(budget + "账期优化数据传输完毕");
    }

    @GetMapping("/syncIncrementalMeter/generateSyncCollect")
    @ResponseBody
    public String generateSyncCollect(@RequestParam(required = false, value = "budget") String budget) throws Exception {
        if (budget == null) {
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM");
            LocalDate lastMonth = LocalDate.now().minusMonths(1);
            String lastBudget = df.format(lastMonth);
            budget = lastBudget;
        }
        log.info("{}账期采集数据开始生成", budget);

        List<CollectMeterFail> fails = collecterPerfectService.generateSyncCollect(budget);
        log.info("{}账期采集准备数据完成，共{}条局站信息", budget, fails.size());

        if (CollectionUtils.isEmpty(fails)) {
            return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "指定条件下无匹配数据");
        }


        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Attachments attachments = collecterPerfectService.exportExcelForSyncCollect(fails, EXPORT_COLUMN_MAPFORSYNCCOLLECT,
                PROMPT_COLUMN_MAP);
        if (attachments == null) {
            return MessageMaster.DefaultMessage.SYSTEM_ERROR.toString();
        }
        return MessageMaster.getMessage(MessageMaster.Code.OK, "上传成功", attachments, true, false);
    }

    /**
     * 采集数据推送，直接从collectmeter表查询数据进行推送
     *
     * @param budget 账期，格式为：yyyy-MM，如：2025-06
     * @return 处理结果
     */
    @GetMapping("/syncIncrementalMeter/collectFromTable")
    @ResponseBody
    public AjaxResult collectFromTable(@RequestParam(value = "budget") String budget) {
        if (StrUtil.isBlank(budget)) {
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM");
            LocalDate lastMonth = LocalDate.now().minusMonths(1);
            budget = df.format(lastMonth);
        }

        log.info("开始从collectmeter表推送{}账期采集数据", budget);
        collecterPerfectService.collectFromTable(budget);
        return AjaxResult.success(budget + "账期采集数据推送完毕");
    }

//    /**
//     * 改变铁塔电子化稽核 根据站址+户号 获取电表id 分支
//     *
//     * @param bitch
//     * @return
//     * @throws Exception
//     */
//    @GetMapping("/syncIncrementalMeter/updateCacheFlagForAmmeterid")
//    @ResponseBody
//    public AjaxResult updateCacheFlagForAmmeterid(@RequestParam("flag") Boolean flag) throws Exception {
//        boolean useCacehFlag = TowerAuditServiceTemplate.useCacehFlag;
//        TowerAuditServiceTemplate.useCacehFlag = flag;
//        log.info("发布事件");
//        applicationEventPublisher.publishEvent(new MyEvent("更新电表标识事件发布"));
//        return AjaxResult.success(
//                String.format("更新标识成功,已由%s 更改为 %s",
//                        useCacehFlag ? "|使用缓存查询电表id|" : "使用数据库查询电表id",
//                        flag ? "|使用缓存查询电表id|" : "使用数据库查询电表id")
//        );
//    }


}
