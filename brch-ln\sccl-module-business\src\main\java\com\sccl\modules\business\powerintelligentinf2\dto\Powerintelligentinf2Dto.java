package com.sccl.modules.business.powerintelligentinf2.dto;

import com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol;
import com.sccl.modules.business.ammeterorprotocol.domain.ElectricType;
import com.sccl.modules.business.powerintelligentinf2.domain.PowerIntelligentInf2;
import com.sccl.modules.business.powerintelligentinf2.domain.PowerIntelligentRelate;

import java.util.List;

/** *
 * <AUTHOR>
 * @date 2019-04-28
 */
public class Powerintelligentinf2Dto extends PowerIntelligentInf2 {
	List<PowerIntelligentRelate> details;

	public List<PowerIntelligentRelate> getDetails() {
		return details;
	}

	public void setDetails(List<PowerIntelligentRelate> details) {
		this.details = details;
	}
}
