package com.sccl.modules.business.msg.service;

import com.sccl.framework.service.IBaseService;
import com.sccl.modules.business.msg.domain.Message;
import com.sccl.modules.system.attachments.domain.Attachments;

import java.util.List;
import java.util.Map;

/**
 * 铁塔稽核结果 服务层
 *
 * <AUTHOR>
 * @date 2021-08-16
 */
public interface IMsgService extends IBaseService<Message> {
    List<Message> getMessageByCity(String city, String from, String to);

    List<Message> getMessageByCountryAndCompany(Long country,Long company,String from,String to);

    List<Message> getLatestMessageByTowerId(List<String> towerIdList);

    List<Message> getLatestMessageByTowerKey(List<Long> towerKeyList);

    Attachments exportExcel(List<Message> messages, String name,Map<String, String> columnMap,
                            Map<String, String> promptMap);
}
