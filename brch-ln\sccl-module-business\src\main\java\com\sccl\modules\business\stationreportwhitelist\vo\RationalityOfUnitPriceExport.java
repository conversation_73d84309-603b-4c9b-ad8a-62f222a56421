package com.sccl.modules.business.stationreportwhitelist.vo;

import com.sccl.framework.aspectj.lang.annotation.Excel;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @date 2024/4/9 16:23
 * @describe 一站多表白名单
 */
@Getter
@Setter
public class RationalityOfUnitPriceExport {

    /**
     * 电表表号
     */
    @Excel(name = "电表/协议编号")
    private String meterCode;

    /**
     * 项目名称
     */
    @Excel(name = "项目名称")
    private String projectname;

    /**
     * 关联局站
     */
    @Excel(name = "关联局站")
    private String stationName;

    /**
     * 分公司名称
     */
    @Excel(name = "所属分公司")
    private String companyName;

    /**
     * 部门名称
     */
    @Excel(name = "所属部门")
    private String countryName;

    /**
     * 对外结算类型名称
     */
    @Excel(name = "对外结算类型")
    private String directsupplyflagName;

    /**
     * 单价
     */
    @Excel(name = "单价")
    private BigDecimal price;

    /**
     * 流程单据状态名称
     */
    @Excel(name = "单据状态")
    private String billStatusName;

    @Override
    public String toString() {
        return super.toString();
    }

}
