package com.sccl.modules.business.modelegetprice.controller;

import com.alibaba.fastjson.JSONObject;
import com.sccl.common.support.Convert;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.common.exception.base.BaseException;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.modelegetprice.domain.ModeleGetprice;
import com.sccl.modules.business.modelegetprice.service.IModeleGetpriceService;
import com.sccl.modules.system.role.domain.Role;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.service.IUserService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 来自官网的单价数据 信息操作处理
 *
 * <AUTHOR>
 * @date 2023-03-03
 */
@RestController
@RequestMapping("/business/modeleGetprice")
public class ModeleGetpriceController extends BaseController {
    @Autowired
    private IUserService userService;
    private String prefix = "business/modeleGetprice";

    @Autowired
    private IModeleGetpriceService modeleGetpriceService;

    @RequiresPermissions("business:modeleGetprice:view")
    @GetMapping()
    public String modeleGetprice() {
        return prefix + "/modeleGetprice";
    }

    /**
     * 查询来自官网的单价数据列表
     */
    @RequiresPermissions("business:modeleGetprice:list")
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(ModeleGetprice modeleGetprice) {
        //权限判定 组装
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("PROVI_")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
        }
        if (isProAdmin == false && isCityAdmin == false) {
            throw new BaseException("只有省管理员或市管理员有权限操作");
        }

        startPage();
        List<ModeleGetprice> list = modeleGetpriceService.selectList(modeleGetprice);
        return getDataTable(list);
    }

    /**
     * 新增来自官网的单价数据
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存来自官网的单价数据
     */
    @Log(title = "来自官网的单价数据", action = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody List<ModeleGetprice> modeleGetprices) {
        //权限判定 组装
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("PROVI_")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
        }
        if (isProAdmin == false && isCityAdmin == false) {
            return AjaxResult.success("只有省管理员或市管理员有权限操作");
        }

        modeleGetprices.forEach(
                init -> {
                    init.setDelFlag("0");
                    init.setCreateTime(new Date());
                    init.setUpdateTime(new Date());
                }
        );

        return toAjax(
                modeleGetpriceService.insertList(modeleGetprices)
        );
    }

    /**
     * 批量更新来自官网的国网代购、损益、基金附加费
     */
    @PostMapping("/updatebitch")
    @ResponseBody
    public AjaxResult updatebitch(@RequestBody List<ModeleGetprice> modeleGetprices) {
        //权限判定 组装
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("PROVI_")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
        }
        if (isProAdmin == false && isCityAdmin == false) {
            return AjaxResult.success("只有省管理员或市管理员有权限操作");
        }

        modeleGetprices.forEach(
                init -> {
                    init.setDelFlag("0");
                    init.setUpdateTime(new Date());
                }
        );

        return toAjax(
                modeleGetpriceService.updatebitch(modeleGetprices)
        );
    }


    /**
     * 修改来自官网的单价数据
     */
    @GetMapping("/edit/{id}")
    public AjaxResult edit(@PathVariable("id") Long id) {
        ModeleGetprice modeleGetprice = modeleGetpriceService.get(id);

        Object object = JSONObject.toJSON(modeleGetprice);

        return this.success(object);
    }

    /**
     * 修改保存来自官网的单价数据
     */
    @RequiresPermissions("business:modeleGetprice:edit")
    @Log(title = "来自官网的单价数据", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody ModeleGetprice modeleGetprice) {
        return toAjax(modeleGetpriceService.update(modeleGetprice));
    }

    /**
     * 删除来自官网的单价数据
     */
    @RequiresPermissions("business:modeleGetprice:remove")
    @Log(title = "来自官网的单价数据", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(modeleGetpriceService.deleteByIds(Convert.toStrArray(ids)));
    }


    /**
     * 查看来自官网的单价数据
     */
    @RequiresPermissions("business:modeleGetprice:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id) {
        ModeleGetprice modeleGetprice = modeleGetpriceService.get(id);

        Object object = JSONObject.toJSON(modeleGetprice);

        return this.success(object);
    }

}
