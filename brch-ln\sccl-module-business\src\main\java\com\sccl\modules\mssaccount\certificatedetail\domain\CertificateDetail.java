package com.sccl.modules.mssaccount.certificatedetail.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;


/**
 * view_certificate_detail表 view_certificate_detail
 *
 * <AUTHOR>
 * @date 2019-09-20
 */
public class CertificateDetail {
    private static final long serialVersionUID = 1L;
    private String id;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    /**
     *
     */
    private String certificateId;
    /**
     *
     */
    private BigDecimal certificateAmount;
    /**
     *
     */
    private String distribution;
    /**
     *
     */
    private String detailTxt;
    /**
     *
     */
    private String dueDate;
    /**
     *
     */
    private String tradePartner;
    /**
     *
     */
    private String tradeType;
    /**
     *
     */
    private String fixFee;
    /**
     *
     */
    private String zyjFee;
    /**
     *
     */
    private String equipmentsFee;
    /**
     *
     */
    private String chargeFee;
    /**
     *
     */
    private String wbs;
    /**
     *
     */
    private String ledgerCategory;
    /**
     *
     */
    private String ledgerNo;
    /**
     *
     */
    private String specialLedger;
    /**
     *
     */
    private String costCenter;
    /**
     *
     */
    private String profitsCenter;
    /**
     *
     */
    private String connectionTradeType;
    /**
     *
     */
    private String ledgerCategoryName;
    /**
     *
     */
    private String ledgerCategoryDetail;
    /**
     *
     */
    private String productCode;
    /**
     *
     */
    private String clientCode;
    /**
     *
     */
    private String networkElementCode;
    /**
     *
     */
    private String clientGroupCode;
    /**
     *
     */
    private String productGroupCode;
    /**
     *
     */
    private String orderNo;
    /**
     *
     */
    private String wwa20;
    /**
     *
     */
    private String bezek;
    /**
     *
     */
    private String type;
    /**
     *
     */
    private String productName;
    /**
     *
     */
    private String clientName;
    /**
     *
     */
    private String networkElementName;
    /**
     *
     */
    private String productGroupName;
    /**
     *
     */
    private String clientGroupName;
    /**
     *
     */
    private String currenttime;
    /**
     *
     */
    private String writeoffdetailids;
    /**
     *
     */
    private Double itemNo;
    /**
     *
     */
    private BigDecimal status;
    /**
     *
     */
    private String writeoffinstanceId;
    /**
     *
     */
    private String ledgercategoryCompanycode;
    /**
     *
     */
    private String onceSupplierName;
    /**
     *
     */
    private String onceSupplierCity;
    /**
     *
     */
    private String isoncesupplier;
    /**
     *
     */
    private String netMark;
    /**
     *
     */
    private String ebeln;
    /**
     *
     */
    private String gdProduct;
    /**
     *
     */
    private String cashflowItem;
    /**
     *
     */
    private String relationType;
    /**
     *
     */
    private String contratNo;
    /**
     *
     */
    private String cashflowItemName;
    /**
     *
     */
    private String taxAdjustType;
    /**
     *
     */
    private String fillInAccount;
    /**
     *
     */
    private String fillInDept;
    /**
     *
     */
    private String setlunchtype;
    /**
     *
     */
    private String trademarktype;
    /**
     *
     */
    private String businesstype;
    /**
     *
     */
    private String clearCertificateCode;
    /**
     *
     */
    private String isDebitCredit;
    /**
     *
     */
    private String cooperateTypeCode;
    /**
     *
     */
    private String cooperateTypeName;
    /**
     *
     */
    private String isOppositeLedger;
    /**
     *
     */
    private String isSubstituteBank;
    /**
     *
     */
    private String isAmortize;
    /**
     *
     */
    private String purchaseNo;
    /**
     *
     */
    private String payType;
    /**
     *
     */
    private String writeoffinstanceCode;
    /**
     *
     */
    private String economicitemCode;
    /**
     *
     */
    private String reserveInfo;
    /**
     *
     */
    private String partnerProfitsCenter;
    /**
     *
     */
    private String contactDetailTxt;
    /**
     *
     */
    private String ictCode;
    /**
     *
     */
    private String accountCode;
    /**
     *
     */
    private String accountName;
    /**
     *
     */
    private String supplierCode;
    /**
     *
     */
    private String customerCode;
    /**
     *
     */
    private String wbsName;
    /**
     *
     */
    private String provinceCode;
    /**
     *
     */
    private String ledgerCategoryType;
    /**
     *
     */
    private String fillInName;
    /**
     *
     */
    private String transactionCode;
    /**
     *
     */
    private String responseCenterCode;
    /**
     *
     */
    private String performancePlanCode;
    /**
     *
     */
    private String performancePlanName;
    /**
     *
     */
    private String salaryCode;
    /**
     *
     */
    private String preAccountCode;
    /**
     *
     */
    private String preAccountName;
    /**
     *
     */
    private String settlementNo;
    /**
     *
     */
    private String isCounteract;
    /**
     *
     */
    private String usageId;
    /**
     *
     */
    private String isExistContactdetail;
    /**
     *
     */
    private String isExistSpecialIndicator;
    /**
     *
     */
    private BigDecimal currencySum;
    /**
     *
     */
    private String internationalCurrency;
    /**
     *
     */
    private String isExistSpecialPreaccount;
    /**
     *
     */
    private String billingCode;


    public void setCertificateId(String certificateId) {
        this.certificateId = certificateId;
    }

    public String getCertificateId() {
        return certificateId;
    }

    public void setCertificateAmount(BigDecimal certificateAmount) {
        this.certificateAmount = certificateAmount;
    }

    public BigDecimal getCertificateAmount() {
        return certificateAmount;
    }

    public void setDistribution(String distribution) {
        this.distribution = distribution;
    }

    public String getDistribution() {
        return distribution;
    }

    public void setDetailTxt(String detailTxt) {
        this.detailTxt = detailTxt;
    }

    public String getDetailTxt() {
        return detailTxt;
    }

    public void setDueDate(String dueDate) {
        this.dueDate = dueDate;
    }

    public String getDueDate() {
        return dueDate;
    }

    public void setTradePartner(String tradePartner) {
        this.tradePartner = tradePartner;
    }

    public String getTradePartner() {
        return tradePartner;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setFixFee(String fixFee) {
        this.fixFee = fixFee;
    }

    public String getFixFee() {
        return fixFee;
    }

    public void setZyjFee(String zyjFee) {
        this.zyjFee = zyjFee;
    }

    public String getZyjFee() {
        return zyjFee;
    }

    public void setEquipmentsFee(String equipmentsFee) {
        this.equipmentsFee = equipmentsFee;
    }

    public String getEquipmentsFee() {
        return equipmentsFee;
    }

    public void setChargeFee(String chargeFee) {
        this.chargeFee = chargeFee;
    }

    public String getChargeFee() {
        return chargeFee;
    }

    public void setWbs(String wbs) {
        this.wbs = wbs;
    }

    public String getWbs() {
        return wbs;
    }

    public void setLedgerCategory(String ledgerCategory) {
        this.ledgerCategory = ledgerCategory;
    }

    public String getLedgerCategory() {
        return ledgerCategory;
    }

    public void setLedgerNo(String ledgerNo) {
        this.ledgerNo = ledgerNo;
    }

    public String getLedgerNo() {
        return ledgerNo;
    }

    public void setSpecialLedger(String specialLedger) {
        this.specialLedger = specialLedger;
    }

    public String getSpecialLedger() {
        return specialLedger;
    }

    public void setCostCenter(String costCenter) {
        this.costCenter = costCenter;
    }

    public String getCostCenter() {
        return costCenter;
    }

    public void setProfitsCenter(String profitsCenter) {
        this.profitsCenter = profitsCenter;
    }

    public String getProfitsCenter() {
        return profitsCenter;
    }

    public void setConnectionTradeType(String connectionTradeType) {
        this.connectionTradeType = connectionTradeType;
    }

    public String getConnectionTradeType() {
        return connectionTradeType;
    }

    public void setLedgerCategoryName(String ledgerCategoryName) {
        this.ledgerCategoryName = ledgerCategoryName;
    }

    public String getLedgerCategoryName() {
        return ledgerCategoryName;
    }

    public void setLedgerCategoryDetail(String ledgerCategoryDetail) {
        this.ledgerCategoryDetail = ledgerCategoryDetail;
    }

    public String getLedgerCategoryDetail() {
        return ledgerCategoryDetail;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setClientCode(String clientCode) {
        this.clientCode = clientCode;
    }

    public String getClientCode() {
        return clientCode;
    }

    public void setNetworkElementCode(String networkElementCode) {
        this.networkElementCode = networkElementCode;
    }

    public String getNetworkElementCode() {
        return networkElementCode;
    }

    public void setClientGroupCode(String clientGroupCode) {
        this.clientGroupCode = clientGroupCode;
    }

    public String getClientGroupCode() {
        return clientGroupCode;
    }

    public void setProductGroupCode(String productGroupCode) {
        this.productGroupCode = productGroupCode;
    }

    public String getProductGroupCode() {
        return productGroupCode;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setWwa20(String wwa20) {
        this.wwa20 = wwa20;
    }

    public String getWwa20() {
        return wwa20;
    }

    public void setBezek(String bezek) {
        this.bezek = bezek;
    }

    public String getBezek() {
        return bezek;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductName() {
        return productName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getClientName() {
        return clientName;
    }

    public void setNetworkElementName(String networkElementName) {
        this.networkElementName = networkElementName;
    }

    public String getNetworkElementName() {
        return networkElementName;
    }

    public void setProductGroupName(String productGroupName) {
        this.productGroupName = productGroupName;
    }

    public String getProductGroupName() {
        return productGroupName;
    }

    public void setClientGroupName(String clientGroupName) {
        this.clientGroupName = clientGroupName;
    }

    public String getClientGroupName() {
        return clientGroupName;
    }

    public void setCurrenttime(String currenttime) {
        this.currenttime = currenttime;
    }

    public String getCurrenttime() {
        return currenttime;
    }

    public void setWriteoffdetailids(String writeoffdetailids) {
        this.writeoffdetailids = writeoffdetailids;
    }

    public String getWriteoffdetailids() {
        return writeoffdetailids;
    }

    public void setItemNo(Double itemNo) {
        this.itemNo = itemNo;
    }

    public Double getItemNo() {
        return itemNo;
    }

    public void setStatus(BigDecimal status) {
        this.status = status;
    }

    public BigDecimal getStatus() {
        return status;
    }

    public void setWriteoffinstanceId(String writeoffinstanceId) {
        this.writeoffinstanceId = writeoffinstanceId;
    }

    public String getWriteoffinstanceId() {
        return writeoffinstanceId;
    }

    public void setLedgercategoryCompanycode(String ledgercategoryCompanycode) {
        this.ledgercategoryCompanycode = ledgercategoryCompanycode;
    }

    public String getLedgercategoryCompanycode() {
        return ledgercategoryCompanycode;
    }

    public void setOnceSupplierName(String onceSupplierName) {
        this.onceSupplierName = onceSupplierName;
    }

    public String getOnceSupplierName() {
        return onceSupplierName;
    }

    public void setOnceSupplierCity(String onceSupplierCity) {
        this.onceSupplierCity = onceSupplierCity;
    }

    public String getOnceSupplierCity() {
        return onceSupplierCity;
    }

    public void setIsoncesupplier(String isoncesupplier) {
        this.isoncesupplier = isoncesupplier;
    }

    public String getIsoncesupplier() {
        return isoncesupplier;
    }

    public void setNetMark(String netMark) {
        this.netMark = netMark;
    }

    public String getNetMark() {
        return netMark;
    }

    public void setEbeln(String ebeln) {
        this.ebeln = ebeln;
    }

    public String getEbeln() {
        return ebeln;
    }

    public void setGdProduct(String gdProduct) {
        this.gdProduct = gdProduct;
    }

    public String getGdProduct() {
        return gdProduct;
    }

    public void setCashflowItem(String cashflowItem) {
        this.cashflowItem = cashflowItem;
    }

    public String getCashflowItem() {
        return cashflowItem;
    }

    public void setRelationType(String relationType) {
        this.relationType = relationType;
    }

    public String getRelationType() {
        return relationType;
    }

    public void setContratNo(String contratNo) {
        this.contratNo = contratNo;
    }

    public String getContratNo() {
        return contratNo;
    }

    public void setCashflowItemName(String cashflowItemName) {
        this.cashflowItemName = cashflowItemName;
    }

    public String getCashflowItemName() {
        return cashflowItemName;
    }

    public void setTaxAdjustType(String taxAdjustType) {
        this.taxAdjustType = taxAdjustType;
    }

    public String getTaxAdjustType() {
        return taxAdjustType;
    }

    public void setFillInAccount(String fillInAccount) {
        this.fillInAccount = fillInAccount;
    }

    public String getFillInAccount() {
        return fillInAccount;
    }

    public void setFillInDept(String fillInDept) {
        this.fillInDept = fillInDept;
    }

    public String getFillInDept() {
        return fillInDept;
    }

    public void setSetlunchtype(String setlunchtype) {
        this.setlunchtype = setlunchtype;
    }

    public String getSetlunchtype() {
        return setlunchtype;
    }

    public void setTrademarktype(String trademarktype) {
        this.trademarktype = trademarktype;
    }

    public String getTrademarktype() {
        return trademarktype;
    }

    public void setBusinesstype(String businesstype) {
        this.businesstype = businesstype;
    }

    public String getBusinesstype() {
        return businesstype;
    }

    public void setClearCertificateCode(String clearCertificateCode) {
        this.clearCertificateCode = clearCertificateCode;
    }

    public String getClearCertificateCode() {
        return clearCertificateCode;
    }

    public void setIsDebitCredit(String isDebitCredit) {
        this.isDebitCredit = isDebitCredit;
    }

    public String getIsDebitCredit() {
        return isDebitCredit;
    }

    public void setCooperateTypeCode(String cooperateTypeCode) {
        this.cooperateTypeCode = cooperateTypeCode;
    }

    public String getCooperateTypeCode() {
        return cooperateTypeCode;
    }

    public void setCooperateTypeName(String cooperateTypeName) {
        this.cooperateTypeName = cooperateTypeName;
    }

    public String getCooperateTypeName() {
        return cooperateTypeName;
    }

    public void setIsOppositeLedger(String isOppositeLedger) {
        this.isOppositeLedger = isOppositeLedger;
    }

    public String getIsOppositeLedger() {
        return isOppositeLedger;
    }

    public void setIsSubstituteBank(String isSubstituteBank) {
        this.isSubstituteBank = isSubstituteBank;
    }

    public String getIsSubstituteBank() {
        return isSubstituteBank;
    }

    public void setIsAmortize(String isAmortize) {
        this.isAmortize = isAmortize;
    }

    public String getIsAmortize() {
        return isAmortize;
    }

    public void setPurchaseNo(String purchaseNo) {
        this.purchaseNo = purchaseNo;
    }

    public String getPurchaseNo() {
        return purchaseNo;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getPayType() {
        return payType;
    }

    public void setWriteoffinstanceCode(String writeoffinstanceCode) {
        this.writeoffinstanceCode = writeoffinstanceCode;
    }

    public String getWriteoffinstanceCode() {
        return writeoffinstanceCode;
    }

    public void setEconomicitemCode(String economicitemCode) {
        this.economicitemCode = economicitemCode;
    }

    public String getEconomicitemCode() {
        return economicitemCode;
    }

    public void setReserveInfo(String reserveInfo) {
        this.reserveInfo = reserveInfo;
    }

    public String getReserveInfo() {
        return reserveInfo;
    }

    public void setPartnerProfitsCenter(String partnerProfitsCenter) {
        this.partnerProfitsCenter = partnerProfitsCenter;
    }

    public String getPartnerProfitsCenter() {
        return partnerProfitsCenter;
    }

    public void setContactDetailTxt(String contactDetailTxt) {
        this.contactDetailTxt = contactDetailTxt;
    }

    public String getContactDetailTxt() {
        return contactDetailTxt;
    }

    public void setIctCode(String ictCode) {
        this.ictCode = ictCode;
    }

    public String getIctCode() {
        return ictCode;
    }

    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }

    public String getAccountCode() {
        return accountCode;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setWbsName(String wbsName) {
        this.wbsName = wbsName;
    }

    public String getWbsName() {
        return wbsName;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setLedgerCategoryType(String ledgerCategoryType) {
        this.ledgerCategoryType = ledgerCategoryType;
    }

    public String getLedgerCategoryType() {
        return ledgerCategoryType;
    }

    public void setFillInName(String fillInName) {
        this.fillInName = fillInName;
    }

    public String getFillInName() {
        return fillInName;
    }

    public void setTransactionCode(String transactionCode) {
        this.transactionCode = transactionCode;
    }

    public String getTransactionCode() {
        return transactionCode;
    }

    public void setResponseCenterCode(String responseCenterCode) {
        this.responseCenterCode = responseCenterCode;
    }

    public String getResponseCenterCode() {
        return responseCenterCode;
    }

    public void setPerformancePlanCode(String performancePlanCode) {
        this.performancePlanCode = performancePlanCode;
    }

    public String getPerformancePlanCode() {
        return performancePlanCode;
    }

    public void setPerformancePlanName(String performancePlanName) {
        this.performancePlanName = performancePlanName;
    }

    public String getPerformancePlanName() {
        return performancePlanName;
    }

    public void setSalaryCode(String salaryCode) {
        this.salaryCode = salaryCode;
    }

    public String getSalaryCode() {
        return salaryCode;
    }

    public void setPreAccountCode(String preAccountCode) {
        this.preAccountCode = preAccountCode;
    }

    public String getPreAccountCode() {
        return preAccountCode;
    }

    public void setPreAccountName(String preAccountName) {
        this.preAccountName = preAccountName;
    }

    public String getPreAccountName() {
        return preAccountName;
    }

    public void setSettlementNo(String settlementNo) {
        this.settlementNo = settlementNo;
    }

    public String getSettlementNo() {
        return settlementNo;
    }

    public void setIsCounteract(String isCounteract) {
        this.isCounteract = isCounteract;
    }

    public String getIsCounteract() {
        return isCounteract;
    }

    public void setUsageId(String usageId) {
        this.usageId = usageId;
    }

    public String getUsageId() {
        return usageId;
    }

    public void setIsExistContactdetail(String isExistContactdetail) {
        this.isExistContactdetail = isExistContactdetail;
    }

    public String getIsExistContactdetail() {
        return isExistContactdetail;
    }

    public void setIsExistSpecialIndicator(String isExistSpecialIndicator) {
        this.isExistSpecialIndicator = isExistSpecialIndicator;
    }

    public String getIsExistSpecialIndicator() {
        return isExistSpecialIndicator;
    }

    public void setCurrencySum(BigDecimal currencySum) {
        this.currencySum = currencySum;
    }

    public BigDecimal getCurrencySum() {
        return currencySum;
    }

    public void setInternationalCurrency(String internationalCurrency) {
        this.internationalCurrency = internationalCurrency;
    }

    public String getInternationalCurrency() {
        return internationalCurrency;
    }

    public void setIsExistSpecialPreaccount(String isExistSpecialPreaccount) {
        this.isExistSpecialPreaccount = isExistSpecialPreaccount;
    }

    public String getIsExistSpecialPreaccount() {
        return isExistSpecialPreaccount;
    }

    public void setBillingCode(String billingCode) {
        this.billingCode = billingCode;
    }

    public String getBillingCode() {
        return billingCode;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("certificateId", getCertificateId())
                .append("certificateAmount", getCertificateAmount())
                .append("distribution", getDistribution())
                .append("detailTxt", getDetailTxt())
                .append("dueDate", getDueDate())
                .append("tradePartner", getTradePartner())
                .append("tradeType", getTradeType())
                .append("fixFee", getFixFee())
                .append("zyjFee", getZyjFee())
                .append("equipmentsFee", getEquipmentsFee())
                .append("chargeFee", getChargeFee())
                .append("wbs", getWbs())
                .append("ledgerCategory", getLedgerCategory())
                .append("ledgerNo", getLedgerNo())
                .append("specialLedger", getSpecialLedger())
                .append("costCenter", getCostCenter())
                .append("profitsCenter", getProfitsCenter())
                .append("connectionTradeType", getConnectionTradeType())
                .append("ledgerCategoryName", getLedgerCategoryName())
                .append("ledgerCategoryDetail", getLedgerCategoryDetail())
                .append("productCode", getProductCode())
                .append("clientCode", getClientCode())
                .append("networkElementCode", getNetworkElementCode())
                .append("clientGroupCode", getClientGroupCode())
                .append("productGroupCode", getProductGroupCode())
                .append("orderNo", getOrderNo())
                .append("wwa20", getWwa20())
                .append("bezek", getBezek())
                .append("type", getType())
                .append("productName", getProductName())
                .append("clientName", getClientName())
                .append("networkElementName", getNetworkElementName())
                .append("productGroupName", getProductGroupName())
                .append("clientGroupName", getClientGroupName())
                .append("currenttime", getCurrenttime())
                .append("writeoffdetailids", getWriteoffdetailids())
                .append("itemNo", getItemNo())
                .append("status", getStatus())
                .append("writeoffinstanceId", getWriteoffinstanceId())
                .append("ledgercategoryCompanycode", getLedgercategoryCompanycode())
                .append("onceSupplierName", getOnceSupplierName())
                .append("onceSupplierCity", getOnceSupplierCity())
                .append("isoncesupplier", getIsoncesupplier())
                .append("netMark", getNetMark())
                .append("ebeln", getEbeln())
                .append("gdProduct", getGdProduct())
                .append("cashflowItem", getCashflowItem())
                .append("relationType", getRelationType())
                .append("contratNo", getContratNo())
                .append("cashflowItemName", getCashflowItemName())
                .append("taxAdjustType", getTaxAdjustType())
                .append("fillInAccount", getFillInAccount())
                .append("fillInDept", getFillInDept())
                .append("setlunchtype", getSetlunchtype())
                .append("trademarktype", getTrademarktype())
                .append("businesstype", getBusinesstype())
                .append("clearCertificateCode", getClearCertificateCode())
                .append("isDebitCredit", getIsDebitCredit())
                .append("cooperateTypeCode", getCooperateTypeCode())
                .append("cooperateTypeName", getCooperateTypeName())
                .append("isOppositeLedger", getIsOppositeLedger())
                .append("isSubstituteBank", getIsSubstituteBank())
                .append("isAmortize", getIsAmortize())
                .append("purchaseNo", getPurchaseNo())
                .append("payType", getPayType())
                .append("writeoffinstanceCode", getWriteoffinstanceCode())
                .append("economicitemCode", getEconomicitemCode())
                .append("reserveInfo", getReserveInfo())
                .append("partnerProfitsCenter", getPartnerProfitsCenter())
                .append("contactDetailTxt", getContactDetailTxt())
                .append("ictCode", getIctCode())
                .append("accountCode", getAccountCode())
                .append("accountName", getAccountName())
                .append("supplierCode", getSupplierCode())
                .append("customerCode", getCustomerCode())
                .append("wbsName", getWbsName())
                .append("provinceCode", getProvinceCode())
                .append("ledgerCategoryType", getLedgerCategoryType())
                .append("fillInName", getFillInName())
                .append("transactionCode", getTransactionCode())
                .append("responseCenterCode", getResponseCenterCode())
                .append("performancePlanCode", getPerformancePlanCode())
                .append("performancePlanName", getPerformancePlanName())
                .append("salaryCode", getSalaryCode())
                .append("preAccountCode", getPreAccountCode())
                .append("preAccountName", getPreAccountName())
                .append("settlementNo", getSettlementNo())
                .append("isCounteract", getIsCounteract())
                .append("usageId", getUsageId())
                .append("isExistContactdetail", getIsExistContactdetail())
                .append("isExistSpecialIndicator", getIsExistSpecialIndicator())
                .append("currencySum", getCurrencySum())
                .append("internationalCurrency", getInternationalCurrency())
                .append("isExistSpecialPreaccount", getIsExistSpecialPreaccount())
                .append("billingCode", getBillingCode())
                .toString();
    }
}
