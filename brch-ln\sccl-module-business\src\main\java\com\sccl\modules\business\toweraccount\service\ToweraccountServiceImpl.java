package com.sccl.modules.business.toweraccount.service;

import com.sccl.common.support.Convert;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.autojob.util.bean.ObjectUtil;
import com.sccl.modules.autojob.util.id.IdGenerator;
import com.sccl.modules.business.account.domain.Account;
import com.sccl.modules.business.account.mapper.AccountMapper;
import com.sccl.modules.business.ammeterorprotocol.mapper.AmmeterorprotocolMapper;
import com.sccl.modules.business.msg.domain.Message;
import com.sccl.modules.business.statinAudit.util.StationAuditUtil;
import com.sccl.modules.business.toweraccount.domain.TowerData;
import com.sccl.modules.business.toweraccount.domain.mssbusi;
import com.sccl.modules.business.toweraccount.mapper.ToweraccountMapper;
import com.sccl.modules.monitor.operlog.domain.OperLog;
import com.sccl.modules.monitor.operlog.mapper.OperLogMapper;
import com.sccl.modules.system.attachments.domain.Attachments;
import com.sccl.modules.system.attachments.mapper.AttachmentsMapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DateTime.now;
import static com.sccl.modules.business.datafilter.util.Convert.convertDate;


/**
 * 铁塔接口 服务层实现
 * 
 * <AUTHOR>
 * @date 2021-08-12
 */
@Service
public class ToweraccountServiceImpl extends BaseServiceImpl<TowerData> implements IToweraccountService
{
    @Autowired
    private ToweraccountMapper toweraccountMapper;
    @Autowired
    public AccountMapper accountMapper;
    @Autowired
    public AttachmentsMapper attachmentsMapper;
    @Autowired
    private OperLogMapper operLogMapper;
    @Override
    public List<TowerData> selectByList(TowerData towerdata) {
        return toweraccountMapper.selectByList(towerdata);
    }

    @Override
    public List<TowerData> selectListByIds(String[] ids) {
        return toweraccountMapper.selectListByIds(ids);
    }
    @Override
    public List<TowerData> selectListByOrgid(String orgid) {
        return toweraccountMapper.selectListByOrgid(orgid);
    }
    @Override
    public List<String> selectListtowidsByIds(String[] ids) {
        return toweraccountMapper.selectListtowidsByIds(ids);
    }

    @Override
    public Map<String, Object> selectlst(Account account) {
        return    accountMapper.selectLastacc(account);
    }

    @Override
    public void deloldaccount(Account account) {
        accountMapper.deleteAccount(account);
    }

    @Override
    public int inserttaaccount(Account account) {
        return    accountMapper.insert(account);
    }

    @Override
    public void upannix(Attachments attachments,Long pcid) {
        List<Attachments> list=attachmentsMapper.selectList(attachments);
/*        for (int i = 0; i <list.size(); i++) {
            System.out.println("attachments old:size= "+list.size()+",list.filename= "+list.get(i).getFileName()+",list.id= "+list.get(i).getId()+",list.BusiAlias= "+list.get(i).getBusiAlias()+",list.busi= "+list.get(i).getBusiId());
            System.out.println("old:"+StringUtils.join(list, "-"));
        }*/
        if (list.size()>0) {
            list.stream().peek(item -> {
                item.setBusiId(pcid);
                item.setId(IdGenerator.getNextIdAsLong());
                item.setBusiAlias("附件(铁塔对账台账)");
            }).collect(Collectors.toList());
/*        for (int i = 0; i <list.size(); i++) {
            System.out.println("new:"+StringUtils.join(list, "-"));
        }*/
            attachmentsMapper.insertList(list);
        }

    }

    @Transactional(rollbackOn = Exception.class)
    @Override
    public int addaccount(String ids)  {
        List<TowerData> tdlist = selectListByIds(Convert.toStrArray(ids));
        System.out.println("tdlist size "+tdlist.size());
        int i = 0;
        List<Long> pcidList = new ArrayList<>();
        for (TowerData towerData : tdlist) {
            if (towerData != null && (towerData.getCompareid()!=null ))
            {
                Account account = new Account();
                account.setAmmeterid(towerData.getCompareid());
                Map<String, Object> map =selectlst(account);
                if (map != null)
                {	SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                    SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
                    if (!StringUtils.isBlank(map.get("enddate").toString())){
                        try {
                            Date lstenddate = sdf.parse(map.get("enddate").toString());
                            Calendar cal = Calendar.getInstance();
                            cal.setTime(lstenddate);//设置起时间
                            cal.add(Calendar.DATE, 1);//增加一天
                            lstenddate=cal.getTime();
                            Date tastartdate = sdf1.parse(towerData.getUseStartTime());
                            String rtl="";
                            if (lstenddate.compareTo(tastartdate)==0)//上期截至时间+1比较铁塔开始时间
                            {   BigDecimal startdegree=new BigDecimal(towerData.getUseStartDegree());
                                BigDecimal curtotalreadings=new BigDecimal(map.get("curtotalreadings").toString());
                                if (startdegree.compareTo(curtotalreadings)==0)//上期截至度数=铁塔开始度数
                                {   Account taaccount=new Account();
                                    taaccount.setAmmeterid(towerData.getCompareid());
                                    taaccount.setToweraccountid(towerData.getId());
                                    taaccount.setOrgid(Long.valueOf(map.get("country").toString()));
                                    taaccount.setStatus(1);
                                    taaccount.setEffective(new BigDecimal("1"));
                                    taaccount.setCompany(Long.valueOf(map.get("company").toString()));
                                    taaccount.setCountry(Long.valueOf(map.get("country").toString()));
				/*					Date format2 = null;
									format2 = new SimpleDateFormat("yyyy-MM-dd").parse(towerData.getUseStartTime());
									String startshortDate = new SimpleDateFormat("yyyyMMdd").format(format2);
									Date format3 = null;
									format3 = new SimpleDateFormat("yyyy-MM-dd").parse(towerData.getUseEndTime());
									String endshortDate = new SimpleDateFormat("yyyyMMdd").format(format3);*/
                                    DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");

                                    String startshortDate = dateFormat.format(convertDate(towerData.getUseStartTime(), "yyyy/MM/dd", "yyyy-MM-dd", "yyyy.MM.dd"));
                                    String endshortDate=dateFormat.format(convertDate(towerData.getUseEndTime(), "yyyy/MM/dd", "yyyy-MM-dd", "yyyy.MM.dd"));
                                    taaccount.setStartdate(startshortDate);
                                    taaccount.setEnddate(endshortDate);
                                    taaccount.setCurtotalreadings(new BigDecimal(towerData.getUseEndDegree()));
                                    taaccount.setPrevtotalreadings(new BigDecimal(towerData.getUseStartDegree()));
                                    taaccount.setCurusedreadings(new BigDecimal(towerData.getEnergyUsed()));
                                    taaccount.setMulttimes(new BigDecimal(towerData.getMagnification()));
                                    taaccount.setTotalusedreadings(new BigDecimal(towerData.getEnergyUsed()).multiply(new BigDecimal(towerData.getApportionmentRatio())));
                                    taaccount.setPercent(new BigDecimal(towerData.getApportionmentRatio()).multiply(new BigDecimal(100)));
                                    taaccount.setAccountmoney(new BigDecimal(towerData.getTelecomBillingAmount()).setScale(2,BigDecimal.ROUND_HALF_UP));

                                    taaccount.setTaxticketmoney(new BigDecimal(towerData.getTelecomBillingAmount()).setScale(2,BigDecimal.ROUND_HALF_UP));
                                    System.out.println("ActualPay:"+new BigDecimal(towerData.getActualPay()));
                                    //taaccount.setInputtaxticketmoney(new BigDecimal(towerData.getActualPay()));
                                    taaccount.setInputtaxticketmoney(new BigDecimal(towerData.getTelecomBillingAmount()).divide(new BigDecimal(towerData.getApportionmentRatio()),2, BigDecimal.ROUND_HALF_UP));
                                    System.out.println(new BigDecimal(towerData.getTelecomBillingAmount()).divide(new BigDecimal(1.13),2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(0.13)).setScale(2,BigDecimal.ROUND_HALF_UP));
                                    BigDecimal tax=new BigDecimal(towerData.getTelecomBillingAmount()).divide(new BigDecimal(1.13),2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(0.13)).setScale(2,BigDecimal.ROUND_HALF_UP);
                                    System.out.println("tax:"+tax);
                                    taaccount.setTaxamount(tax);
                                    taaccount.setTaxrate(new BigDecimal(13).setScale(2,BigDecimal.ROUND_HALF_UP));
                                    System.out.println("ApportionmentRatio: "+towerData.getApportionmentRatio().toString());
                                    System.out.println("ApportionmentRatio: "+towerData.getApportionmentRatio().toString());
                                    BigDecimal uprice=new BigDecimal(towerData.getTelecomBillingAmount()).divide(new BigDecimal(towerData.getEnergyUsed()).multiply(new BigDecimal(towerData.getApportionmentRatio())),2,BigDecimal.ROUND_HALF_UP);
                                    taaccount.setUnitpirce(uprice);
                                    taaccount.setInputdate(now());
                                    taaccount.setLasteditdate(now());
                                    taaccount.setRru(new BigDecimal(0));
                                    taaccount.setIsnew(new BigDecimal(0));
                                    taaccount.setOpflag(new BigDecimal(0));
                                    System.out.println(towerData.getAnnex());

                                    System.out.println(towerData.getPowerLoss());
                                    if (towerData.getPowerLoss()==null)
                                        taaccount.setTransformerullage(new BigDecimal(0));
                                    else
                                        taaccount.setTransformerullage(new BigDecimal(towerData.getPowerLoss()).multiply(new BigDecimal(towerData.getApportionmentRatio())));
                                    String accountnostr=map.get("accountno").toString()+"01";
                                    Date formatacc = null;
                                    formatacc = new SimpleDateFormat("yyyyMMdd").parse(accountnostr);
                                    Calendar calacc = Calendar.getInstance();
                                    calacc.setTime(formatacc);//设置起时间
                                    calacc.add(Calendar.MONTH, 1);//增加一月
                                    Date accountnodate=calacc.getTime();
                                    String yyyyMM = FastDateFormat.getInstance("yyyyMM").format(accountnodate);
                                    String yyyy = FastDateFormat.getInstance("yyyy").format(accountnodate);
                                    taaccount.setAccountno(yyyyMM);
                                    taaccount.setYear(yyyy);
                                    taaccount.setTaRemark(towerData.getRemark());
                                    Message msg=selectLstMsg(towerData.getId());
                                    if (msg!=null&&msg.getRongStep()!=null)
                                    {if (msg.getRongStep()==9)
                                        taaccount.setRemark(msg.getMessage());
                                    }
                                    deloldaccount(taaccount);
                                    inserttaaccount(taaccount);
                                    pcidList.add(taaccount.getPcid());
                                    if (towerData.getAnnex()!=null)
                                    {
                                        Attachments at =new Attachments();
                                        at.setBusiId(towerData.getId());
                                        upannix(at,taaccount.getPcid());
                                    }

					/*				{rtl=uploadaccount(towerData.getAnnex(),taaccount.getPcid());
										System.out.println("rtl:"+rtl);}*/
                                }
                            }
                        } catch (Exception e) {
                            System.out.println(e.getMessage());
                            insertLog("铁塔转入" + ids, "taaccount", e.getMessage());
                        }
                    }
                }

                //System.out.println("nh percent "+map.get("percent").toString()+"ApportionmentRatio "+towerData.getApportionmentRatio());
                //System.out.println("TowerSiteCode:"+towerData.getTowerSiteCode()+" ,UseEndTime:"+towerData.getUseEndTime()+"nh lst end date"+map.get("startdate").toString() +" ,getUseStartTime:"+towerData.getUseStartTime()+"nh lst enddate"+map.get("enddate").toString() + " ,UseEndTime:"+towerData.getUseEndTime()+" ,UseStartDegree:"+towerData.getUseStartDegree() +"nh lst prevtotalreadings"+map.get("prevtotalreadings").toString()+towerData.getUseEndDegree() +"nh lst prevtotalreadings"+map.get("curtotalreadings").toString());
            }
            i++;
        }
        //稽核台账
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                StationAuditUtil.doAuditAccountThreadByPcids(pcidList);
            }
        });
        return i;
    }

    @Transactional
    @Override
    public int batchaccount(String orgid) {
        List<TowerData> tdlist = selectListByOrgid(orgid);
        System.out.println("tdlist size "+tdlist.size());
        int i = 0;
        List<Long> pcidList = new ArrayList<>();
        for (TowerData towerData : tdlist) {
            if (towerData != null && (towerData.getCompareid()!=null ))
            {
                Account account = new Account();
                account.setAmmeterid(towerData.getCompareid());
                Map<String, Object> map =selectlst(account);
                if (map != null)
                {	SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                    SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
                    if (!StringUtils.isBlank(map.get("enddate").toString())){
                        try {
                            Date lstenddate = sdf.parse(map.get("enddate").toString());
                            Calendar cal = Calendar.getInstance();
                            cal.setTime(lstenddate);//设置起时间
                            cal.add(Calendar.DATE, 1);//增加一天
                            lstenddate=cal.getTime();
                            Date tastartdate = sdf1.parse(towerData.getUseStartTime());
                            String rtl="";
                            if (lstenddate.compareTo(tastartdate)==0)//上期截至时间+1比较铁塔开始时间
                            {   BigDecimal startdegree=new BigDecimal(towerData.getUseStartDegree());
                                BigDecimal curtotalreadings=new BigDecimal(map.get("curtotalreadings").toString());
                                if (startdegree.compareTo(curtotalreadings)==0)//上期截至度数=铁塔开始度数
                                {   Account taaccount=new Account();
                                    taaccount.setAmmeterid(towerData.getCompareid());
                                    taaccount.setToweraccountid(towerData.getId());
                                    taaccount.setOrgid(Long.valueOf(map.get("country").toString()));
                                    taaccount.setStatus(1);
                                    taaccount.setEffective(new BigDecimal("1"));
                                    taaccount.setCompany(Long.valueOf(map.get("company").toString()));
                                    taaccount.setCountry(Long.valueOf(map.get("country").toString()));
				/*					Date format2 = null;
									format2 = new SimpleDateFormat("yyyy-MM-dd").parse(towerData.getUseStartTime());
									String startshortDate = new SimpleDateFormat("yyyyMMdd").format(format2);
									Date format3 = null;
									format3 = new SimpleDateFormat("yyyy-MM-dd").parse(towerData.getUseEndTime());
									String endshortDate = new SimpleDateFormat("yyyyMMdd").format(format3);*/
                                    DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");

                                    String startshortDate = dateFormat.format(convertDate(towerData.getUseStartTime(), "yyyy/MM/dd", "yyyy-MM-dd", "yyyy.MM.dd"));
                                    String endshortDate=dateFormat.format(convertDate(towerData.getUseEndTime(), "yyyy/MM/dd", "yyyy-MM-dd", "yyyy.MM.dd"));
                                    taaccount.setStartdate(startshortDate);
                                    taaccount.setEnddate(endshortDate);
                                    taaccount.setCurtotalreadings(new BigDecimal(towerData.getUseEndDegree()));
                                    taaccount.setPrevtotalreadings(new BigDecimal(towerData.getUseStartDegree()));
                                    taaccount.setCurusedreadings(new BigDecimal(towerData.getEnergyUsed()));
                                    taaccount.setMulttimes(new BigDecimal(towerData.getMagnification()));
                                    taaccount.setTotalusedreadings(new BigDecimal(towerData.getEnergyUsed()).multiply(new BigDecimal(towerData.getApportionmentRatio())));
                                    taaccount.setPercent(new BigDecimal(towerData.getApportionmentRatio()).multiply(new BigDecimal(100)));
                                    taaccount.setAccountmoney(new BigDecimal(towerData.getTelecomBillingAmount()).setScale(2,BigDecimal.ROUND_HALF_UP));

                                    taaccount.setTaxticketmoney(new BigDecimal(towerData.getTelecomBillingAmount()).setScale(2,BigDecimal.ROUND_HALF_UP));
                                    System.out.println("ActualPay:"+new BigDecimal(towerData.getActualPay()));
                                    //taaccount.setInputtaxticketmoney(new BigDecimal(towerData.getActualPay()));
                                    taaccount.setInputtaxticketmoney(new BigDecimal(towerData.getTelecomBillingAmount()).divide(new BigDecimal(towerData.getApportionmentRatio()),2, BigDecimal.ROUND_HALF_UP));
                                    System.out.println(new BigDecimal(towerData.getTelecomBillingAmount()).divide(new BigDecimal(1.13),2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(0.13)).setScale(2,BigDecimal.ROUND_HALF_UP));
                                    BigDecimal tax=new BigDecimal(towerData.getTelecomBillingAmount()).divide(new BigDecimal(1.13),2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(0.13)).setScale(2,BigDecimal.ROUND_HALF_UP);
                                    System.out.println("tax:"+tax);
                                    taaccount.setTaxamount(tax);
                                    taaccount.setTaxrate(new BigDecimal(13).setScale(2,BigDecimal.ROUND_HALF_UP));
                                    System.out.println("ApportionmentRatio: "+towerData.getApportionmentRatio().toString());
                                    System.out.println("ApportionmentRatio: "+towerData.getApportionmentRatio().toString());
                                    BigDecimal uprice=new BigDecimal(towerData.getTelecomBillingAmount()).divide(new BigDecimal(towerData.getEnergyUsed()).multiply(new BigDecimal(towerData.getApportionmentRatio())),2,BigDecimal.ROUND_HALF_UP);
                                    taaccount.setUnitpirce(uprice);
                                    taaccount.setInputdate(now());
                                    taaccount.setLasteditdate(now());
                                    taaccount.setRru(new BigDecimal(0));
                                    taaccount.setIsnew(new BigDecimal(0));
                                    taaccount.setOpflag(new BigDecimal(0));
                                    System.out.println(towerData.getAnnex());

                                    System.out.println(towerData.getPowerLoss());
                                    if (towerData.getPowerLoss()==null)
                                        taaccount.setTransformerullage(new BigDecimal(0));
                                    else
                                        taaccount.setTransformerullage(new BigDecimal(towerData.getPowerLoss()).multiply(new BigDecimal(towerData.getApportionmentRatio())));
                                    String accountnostr=map.get("accountno").toString()+"01";
                                    Date formatacc = null;
                                    formatacc = new SimpleDateFormat("yyyyMMdd").parse(accountnostr);
                                    Calendar calacc = Calendar.getInstance();
                                    calacc.setTime(formatacc);//设置起时间
                                    calacc.add(Calendar.MONTH, 1);//增加一月
                                    Date accountnodate=calacc.getTime();
                                    String yyyyMM = FastDateFormat.getInstance("yyyyMM").format(accountnodate);
                                    String yyyy = FastDateFormat.getInstance("yyyy").format(accountnodate);
                                    taaccount.setAccountno(yyyyMM);
                                    taaccount.setYear(yyyy);
                                    taaccount.setTaRemark(towerData.getRemark());
                                    Message msg=selectLstMsg(towerData.getId());
                                    if (msg!=null&&msg.getRongStep()!=null)
                                    {if (msg.getRongStep()==9)
                                        taaccount.setRemark(msg.getMessage());
                                    }
                                    deloldaccount(taaccount);
                                    inserttaaccount(taaccount);
                                    pcidList.add(taaccount.getPcid());
                                    if (towerData.getAnnex()!=null)
                                    {
                                        Attachments at =new Attachments();
                                        at.setBusiId(towerData.getId());
                                        upannix(at,taaccount.getPcid());
                                    }

					/*				{rtl=uploadaccount(towerData.getAnnex(),taaccount.getPcid());
										System.out.println("rtl:"+rtl);}*/
                                }
                            }
                        } catch (Exception e) {
                            System.out.println(e.getMessage());
                            insertLog("铁塔转入" + orgid, "tabatchaccount", e.getMessage());
                        }
                    }
                }

                //System.out.println("nh percent "+map.get("percent").toString()+"ApportionmentRatio "+towerData.getApportionmentRatio());
                //System.out.println("TowerSiteCode:"+towerData.getTowerSiteCode()+" ,UseEndTime:"+towerData.getUseEndTime()+"nh lst end date"+map.get("startdate").toString() +" ,getUseStartTime:"+towerData.getUseStartTime()+"nh lst enddate"+map.get("enddate").toString() + " ,UseEndTime:"+towerData.getUseEndTime()+" ,UseStartDegree:"+towerData.getUseStartDegree() +"nh lst prevtotalreadings"+map.get("prevtotalreadings").toString()+towerData.getUseEndDegree() +"nh lst prevtotalreadings"+map.get("curtotalreadings").toString());
            }
            i++;
        }
        //稽核台账
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                StationAuditUtil.doAuditAccountThreadByPcids(pcidList);
            }
        });
        return i;
    }

    @Override
    public Message selectLstMsg(Long id) {
        return   toweraccountMapper.selectLstMsg(id);
    }

    // 记录 错误日志
    private void insertLog(String title, String method, String errorMes) {
        OperLog model = new OperLog();
        model.setOperName("mss");
        model.setTitle(title);
        model.setMethod(method);
        model.setErrorMsg(errorMes);
        model.setOperTime(new Date());

        operLogMapper.insert(model);
    }

    @Override
    public List<mssbusi> seletmssbusi(TowerData towerdata) {
        return toweraccountMapper.selectmssbusi(towerdata);
    }
}
