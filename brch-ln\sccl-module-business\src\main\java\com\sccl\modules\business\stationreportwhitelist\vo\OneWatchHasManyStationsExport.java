package com.sccl.modules.business.stationreportwhitelist.vo;

import com.sccl.framework.aspectj.lang.annotation.Excel;
import lombok.Getter;
import lombok.Setter;


/**
 * <AUTHOR>
 * @date 2024/4/9 16:23
 * @describe 一站多表白名单
 */
@Getter
@Setter
public class OneWatchHasManyStationsExport {

    /**
     * 电表表号
     */
    @Excel(name = "电表/协议编号")
    private String meterCode;

    /**
     * 项目名称
     */
    @Excel(name = "项目名称")
    private String projectname;

    /**
     * 分公司名称
     */
    @Excel(name = "所属分公司")
    private String companyName;

    /**
     * 部门名称
     */
    @Excel(name = "所属部门")
    private String countryName;

    /**
     * 用电类型名称
     */
    @Excel(name = "用电类型")
    private String electrotypeName;

    /**
     * 关联局站数量
     */
    @Excel(name = "关联局站数量")
    private Integer stationTotal;

    /**
     * 流程单据状态名称
     */
    @Excel(name = "单据状态")
    private String billStatusName;

    @Override
    public String toString() {
        return super.toString();
    }

}
