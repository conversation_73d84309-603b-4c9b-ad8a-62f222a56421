package com.sccl.modules.business.stationequipment.util;


import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;

import java.io.File;
import java.util.ArrayList;
import java.util.Map;


public class LargeExcelUtil {
    private ArrayList<Map<String, Object>> data;
    private ReadExcelListener listener;

    public ArrayList<Map<String, Object>> getData() {
        return data;
    }

    public void setData(ArrayList<Map<String, Object>> data) {
        this.data = data;
    }

    public ReadExcelListener getListener() {
        return listener;
    }

    public void setListener(ReadExcelListener listener) {
        this.listener = listener;
    }

    public LargeExcelUtil() {
        this.listener = new ReadExcelListener();
        this.data = new ArrayList<>();
    }

    public String readLargeExcel(String path, int start, int limit,String only) {
        ExcelReaderBuilder builder = new ExcelReaderBuilder();
        long startTime = System.currentTimeMillis(); //程序开始记录时间
        String json = null;
        builder.file(new File(path));
        builder.registerReadListener(listener);

        ExcelReader reader = builder.build();
        listener.setStart(startTime);
        if (this.data == null||this.data.size()==0) {
            reader.readAll();
            json = listener.getRowsJson(start, limit, this.data,only);
        } else {
            json = listener.getRowsJson(start, limit, this.data,only);
        }

        long end = System.currentTimeMillis();
        long min = (end - startTime) / 1000;
        System.out.println("共读取到" + listener.getCount() + "条数据，总共用时：" + min);

        this.data = listener.getData();
        return json;
    }


}

