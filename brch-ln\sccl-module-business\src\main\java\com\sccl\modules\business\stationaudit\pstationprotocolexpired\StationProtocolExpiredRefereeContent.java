package com.sccl.modules.business.stationaudit.pstationprotocolexpired;


import com.enrising.dcarbon.audit.AbstractRefereeContent;
import com.enrising.dcarbon.audit.RefereeDatasource;
import com.enrising.dcarbon.audit.RefereeResult;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class StationProtocolExpiredRefereeContent extends AbstractRefereeContent implements RefereeDatasource {
    private Long billId;
    private Long pcid;
    /**
     * 台账对应的电表id
     */
    private Long ammeterid;
    private String ammetername;
    private String stationCode;
    /**
     * 项目名称
     */
    private String projectname;
    /**
     * 局站名称
     */
    private String stationname;
    /**
     * 局站地址
     */
    private String stationaddress;
    /**
     * 协议签订时间
     */
    private LocalDateTime protocolsigneddate;
    /**
     * 协议终止时间
     */
    private LocalDateTime protocolterminatedate;

    public StationProtocolExpiredRefereeContent(RefereeResult refereeResult, int step, String auditKey) {
        super(refereeResult, step, auditKey);
    }

    public StationProtocolExpiredRefereeContent() {

    }
}
