package com.sccl.modules.business.stationinfo.mapper;

import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.stationinfo.domain.PowerStationInfoRJtlte;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 集团LTE和局站对应 数据层
 * 
 * <AUTHOR>
 * @date 2021-05-13
 */
public interface PowerStationInfoRJtlteMapper extends BaseMapper<PowerStationInfoRJtlte>
{

    public List<PowerStationInfoRJtlte> getListjtltermap(Map<String, Object> paramMap);

    int disassociate(@Param("stationid") Long stationid);

    int newAssociation(PowerStationInfoRJtlte psij);
}