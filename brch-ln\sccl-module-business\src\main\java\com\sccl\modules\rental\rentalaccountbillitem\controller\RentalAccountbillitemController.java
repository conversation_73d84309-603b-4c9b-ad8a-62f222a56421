package com.sccl.modules.rental.rentalaccountbillitem.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.rental.rentalaccountbillitem.domain.RentalAccountbillitem;
import com.sccl.modules.rental.rentalaccountbillitem.service.IRentalAccountbillitemService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 车辆 （报账明细） 信息操作处理
 * 
 * <AUTHOR>
 * @date 2019-08-23
 */
@RestController
@RequestMapping("/rental/rentalAccountbillitem")
public class RentalAccountbillitemController extends BaseController
{
    private String prefix = "rental/rentalAccountbillitem";
	
	@Autowired
	private IRentalAccountbillitemService rentalAccountbillitemService;
	
	@RequiresPermissions("rental:rentalAccountbillitem:view")
	@GetMapping()
	public String rentalAccountbillitem()
	{
	    return prefix + "/rentalAccountbillitem";
	}
	
	/**
	 * 查询车辆 （报账明细）列表
	 */
	@RequiresPermissions("rental:rentalAccountbillitem:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(RentalAccountbillitem rentalAccountbillitem)
	{
		startPage();
        List<RentalAccountbillitem> list = rentalAccountbillitemService.selectList(rentalAccountbillitem);
		return getDataTable(list);
	}
	
	/**
	 * 新增车辆 （报账明细）
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存车辆 （报账明细）
	 */
	@RequiresPermissions("rental:rentalAccountbillitem:add")
	//@Log(title = "车辆 （报账明细）", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody RentalAccountbillitem rentalAccountbillitem)
	{		
		return toAjax(rentalAccountbillitemService.insert(rentalAccountbillitem));
	}

	/**
	 * 修改车辆 （报账明细）
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id")Long id)
	{
		RentalAccountbillitem rentalAccountbillitem = rentalAccountbillitemService.get(id);

		Object object = JSONObject.toJSON(rentalAccountbillitem);

        return this.success(object);
	}
	
	/**
	 * 修改保存车辆 （报账明细）
	 */
	@RequiresPermissions("rental:rentalAccountbillitem:edit")
	//@Log(title = "车辆 （报账明细）", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody RentalAccountbillitem rentalAccountbillitem)
	{		
		return toAjax(rentalAccountbillitemService.update(rentalAccountbillitem));
	}
	
	/**
	 * 删除车辆 （报账明细）
	 */
	@RequiresPermissions("rental:rentalAccountbillitem:remove")
	//@Log(title = "车辆 （报账明细）", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(rentalAccountbillitemService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看车辆 （报账明细）
     */
    @RequiresPermissions("rental:rentalAccountbillitem:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id")Long id)
    {
		RentalAccountbillitem rentalAccountbillitem = rentalAccountbillitemService.get(id);

        Object object = JSONObject.toJSON(rentalAccountbillitem);

        return this.success(object);
    }

}
