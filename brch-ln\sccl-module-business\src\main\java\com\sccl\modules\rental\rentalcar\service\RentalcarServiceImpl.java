package com.sccl.modules.rental.rentalcar.service;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.rental.rentalcar.mapper.RentalcarMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sccl.modules.rental.rentalcar.domain.Rentalcar;

import java.util.List;


/**
 * 车辆  服务层实现
 * 
 * <AUTHOR>
 * @date 2019-08-23
 */
@Service
public class RentalcarServiceImpl extends BaseServiceImpl<Rentalcar> implements IRentalcarService
{
    @Autowired
    RentalcarMapper rentalcarMapper;

    @Override
    public List<Rentalcar> selectAndNameByRcmid(Long rcmid) {
        return rentalcarMapper.selectAndNameByRcmid(rcmid);
    }

    @Override
    public Rentalcar selectByVin(Rentalcar vin) {
        return rentalcarMapper.selectByVin(vin);
    }
}
