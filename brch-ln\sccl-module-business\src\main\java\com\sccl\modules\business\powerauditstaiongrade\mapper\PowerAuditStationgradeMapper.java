package com.sccl.modules.business.powerauditstaiongrade.mapper;

import com.sccl.modules.business.msg.domain.Message;
import com.sccl.modules.business.powerauditstaiongrade.entity.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.sccl.modules.business.stationinfo.domain.PowerStationInfoRJtlte;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Mapper
public interface PowerAuditStationgradeMapper extends BaseMapper<PowerAuditStationgradeEntity> {

    List<PowerAuditStationgradeEntity> selectListByMybatis(PowerAuditStationgradeEntity stationgradeEntity);

    List<TowerStationGradeaudit> selectListForTowerStaionGrade(TowerStationGradeaudit towerStationGrade);

    List<StaionMapBill> selectListForStationMapBill(PowerStationInfoRJtlte rJtlte);

    List<TowerResultSummary> selecTowerResultSummaryList(Message message);

    List<TowerAudit> selectAuditList(Message message);
    List<TowerAudit> selectAuditListForStaionAnaly(StaionAnalysis message);

    List<TowerAudit> selectAuditOneList(Message message);
    List<TowerAudit> selectAuditOneListForStaionAnaly(StaionAnalysis message);

    List<StaionAnalysis> selectStaionAnalysisList(StaionAnalysis message);

    List<StaionAnalysisDetail> getStaionAnalysisDetailForAudit(StaionAnalysis keyOb);

    List<StaionAnalysisDetail> getStaionAnalysisDetailForGrade(StaionAnalysis keyOb);

    Integer generatPrice(@Param("time") String month);

    Integer truncatePrice(@Param("time") String month);

    List<TowerResultSummary> selecTowerResultSummary(Message message);

    List<ExceptionAnalysis> selectExceptionAnalysis(StaionAnalysis message);
}
