package com.sccl.modules.mssaccount.dataanalysis.dto;

import com.sccl.framework.web.domain.BasePage;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 局站统计列表
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StatisticsListDTO extends BasePage {

    /**
     * 是否大工业
     * 0：否，1：是 null：未知
     */
    private String isbigfactories;


    /**
     * 状态(0闲置、1停用、2在用)
     */
    private String status;

    /**
     * 机构/部门ID
     */
    private Long company;

    /**
     * 局站类型
     */
    private String stationtype;


    /**
     * 局站名称
     */
    private String stationname;

    /**
     * 局站编码
     */
    private String stationcode;
}
