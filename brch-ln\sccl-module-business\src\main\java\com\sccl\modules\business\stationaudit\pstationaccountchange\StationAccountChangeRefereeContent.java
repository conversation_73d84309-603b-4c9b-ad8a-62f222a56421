package com.sccl.modules.business.stationaudit.pstationaccountchange;


import com.enrising.dcarbon.audit.AbstractRefereeContent;
import com.enrising.dcarbon.audit.RefereeDatasource;
import com.enrising.dcarbon.audit.RefereeResult;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 电量波动
 */
@Data
public class StationAccountChangeRefereeContent extends AbstractRefereeContent implements RefereeDatasource {
    private Long billId;

    /**
     * 本次台账id
     */
    private Long pcid;

    /**
     * 台账起始日期
     */
    private String startdate;

    /**
     * 台账截止日期
     */
    private String enddate;
    /**
     * 本地电表id
     */
    private Long ammeterid;
    /**
     * 电表名称
     */
    private String ammetername;
    /**
     * 本次站址id
     */
    private String stationCode;
    /**
     * 项目名称
     */
    private String projectname;
    /**
     * 局站名称
     */
    private String stationname;
    /**
     * 局站地址
     */
    private String stationaddress;

    /**
     * 报账->台账 对应的金额
     */
    private BigDecimal accountmoney;


    /**
     * 比对的台账id
     */
    private Long pcid_compare;
    /**
     * 比对的电表id
     */
    private Long ammeterid_compare;
    /**
     * 比对的电表名称
     */
    private String ammetername_compare;
    /**
     * 比对的站址id
     */
    private String stationcode_compare;

    /**
     * 比对的台账金额
     */
    private BigDecimal accountmoney_compare;
    /**
     * 本次台账金额 偏离上一次的比例
     */
    private BigDecimal wideaccount;

    /**
     * 比对的集团站址id
     */
    private String station_source_compare;

    /**
     * 项目名称
     */
    private String projectname_compare;
    /**
     * 局站名称
     */
    private String stationname_compare;
    /**
     * 局站地址
     */
    private String stationaddress_compare;


    public StationAccountChangeRefereeContent(RefereeResult refereeResult, int step, String auditKey) {
        super(refereeResult, step, auditKey);
    }

    public StationAccountChangeRefereeContent() {

    }

    public Long getPcid() {
        return pcid;
    }

    public void setPcid(Long pcid) {
        this.pcid = pcid;
    }

    public String getStationCode() {
        return stationCode;
    }

    public void setStationCode(String stationCode) {
        this.stationCode = stationCode;
    }

    public String getProjectname() {
        return projectname;
    }

    public void setProjectname(String projectname) {
        this.projectname = projectname;
    }

    public String getStationname() {
        return stationname;
    }

    public void setStationname(String stationname) {
        this.stationname = stationname;
    }

    public String getStationaddress() {
        return stationaddress;
    }

    public void setStationaddress(String stationaddress) {
        this.stationaddress = stationaddress;
    }
}
