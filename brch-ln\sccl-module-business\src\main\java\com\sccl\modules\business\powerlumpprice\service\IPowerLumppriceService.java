package com.sccl.modules.business.powerlumpprice.service;

import com.sccl.modules.business.powerlumpprice.domain.PowerLumpprice;
import com.sccl.framework.service.IBaseService;

import java.util.List;
import java.util.Map;

/**
 * 铁塔包干单价维护 服务层
 * 
 * <AUTHOR>
 * @date 2019-06-19
 */
public interface IPowerLumppriceService extends IBaseService<PowerLumpprice>
{

    List<Map<String,Object>> selectByList(PowerLumpprice powerLumpprice);
    Map<String,Object> selectById(Long id);
    List<Map<String,Object>> checkDate(PowerLumpprice powerLumpprice);
}
