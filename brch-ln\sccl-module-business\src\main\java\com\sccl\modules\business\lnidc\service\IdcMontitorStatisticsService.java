package com.sccl.modules.business.lnidc.service;

import com.sccl.framework.service.IBaseService;
import com.sccl.modules.business.lnidc.domain.IdcMonitorStatistics;
import com.sccl.modules.business.lnidc.domain.IdcMonitorStatisticsBo;
import com.sccl.modules.business.lnidc.domain.IdcMonitorStatisticsVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * service__<br/>
 * 2024/4/7
 *
 * <AUTHOR>
 */
public interface IdcMontitorStatisticsService extends IBaseService<IdcMonitorStatistics> {


    List<IdcMonitorStatisticsVo> selfIdcEnergyList(IdcMonitorStatisticsBo bo);

    void editIdcEnergy(IdcMonitorStatisticsBo bo);

    void exportIdcEnergy(HttpServletResponse response,IdcMonitorStatisticsBo bo);
}
