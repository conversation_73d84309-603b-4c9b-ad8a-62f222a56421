package com.sccl.modules.business.twoc.domain;

import lombok.Data;

@Data
public class MeterInofTwoC implements TwoCFlag {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private String provinceCode;
    /**
     *
     */
    private String cityCode;
    /**
     *
     */
    private String cityName;
    /**
     *
     */
    private String countyCode;
    /**
     *
     */
    private String countyName;
    /**
     *
     */
    private String energyMeterCode;
    /**
     *
     */
    private String energyMeterName;
    /**
     *
     */
    private String status;
    /**
     *
     */
    private String usage;
    /**
     *
     */
    private String type;
    /**
     *
     */
    private String stationCode;
    /**
     *
     */
    private String stationName;
    /**
     *
     */
    private String stationLocation;
    /**
     *
     */
    private String stationStatus;
    /**
     *
     */
    private String stationType;
    /**
     *
     */
    private String energySupplyWay;
    /**
     *
     */
    private String powerGridEnergyMeterCode;
    /**
     *
     */
    private String siteCode;
}
