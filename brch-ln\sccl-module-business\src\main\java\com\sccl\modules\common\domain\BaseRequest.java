package com.sccl.modules.common.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 请求参数公共类
 * @date 2024/8/28  16:39
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseRequest {

    /**
     * 期号
     */
    private String accountno;


    /**
     * 所属分公司
     */
    private String company;

    /**
     * 所属部门
     */
    private String country;

    /**
     * 填报人帐号
     */
    private String fillInAccount;

    /**
     * 查询本级及下级
     */
    private List<Map<String, Object>> countrys;

    /**
     * 开始期号
     */
    private String startAccountNo;

    /**
     * 结束期号
     */
    private String endAccountNo;

    /**
     * 当前期号期号
     */
    private String currentAccountNo;

    /**
     * 是否列表查询
     */
    private Boolean query;

    /**
     * 台账状态
     */
    private String accountStatus;
}
