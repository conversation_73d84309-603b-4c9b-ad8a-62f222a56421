package com.sccl.modules.business.statistical.tower.service;

import com.sccl.modules.business.statistical.tower.domain.TowerBillAuditResult;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/10/31 16:46
 * @Email <EMAIL>
 */
public interface ITowerStatisticalIndexService {
    TowerBillAuditResult doStatistical(Long company, Long country, Date from, Date to);

    TowerBillAuditResult doStatistical(Date from, Date to,boolean isLoadInRedis);
}
