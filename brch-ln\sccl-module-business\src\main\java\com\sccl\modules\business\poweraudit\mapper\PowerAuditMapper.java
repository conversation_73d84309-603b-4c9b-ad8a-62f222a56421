package com.sccl.modules.business.poweraudit.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sccl.modules.business.account.domain.AccountQua;
import com.sccl.modules.business.poweraudit.entity.DetailsDTO;
import com.sccl.modules.business.poweraudit.entity.PowerAuditDTO;
import com.sccl.modules.business.poweraudit.entity.PowerAuditEntity;
import com.sccl.modules.business.poweraudit.entity.PowerAuditVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-22
 */
@Mapper
public interface PowerAuditMapper extends BaseMapper<PowerAuditEntity> {

    int insertList(@Param("powerAuditEntities") List<PowerAuditEntity> powerAuditEntities);

    int insertPowerAudit(PowerAuditEntity powerAuditEntity);

    int updateForModel(PowerAuditEntity powerAuditEntity);

    List<PowerAuditEntity> getPowerAudityByPcid(@Param("type") String type, @Param("pcids") List<String> pcids);
    List<PowerAuditDTO> getPowerAuditByCity(@Param("powerAuditVO") PowerAuditVO powerAuditVO);

    List<PowerAuditDTO> getPowerAuditCompanies(@Param("powerAuditVO") PowerAuditVO powerAuditVO);

    List<PowerAuditDTO> getAuditOperationsBranch(@Param("powerAuditVO") PowerAuditVO powerAuditVO);

    List<PowerAuditEntity> getAuditResult(@Param("ammeterids") List<String> ammeterids);

    PowerAuditEntity getAuditByMssAccountId(@Param("mssAccountId") Long mssAccountId);

    PowerAuditEntity getAuditByPcid(@Param("pcid") String pcid, @Param("type") String type);

    List<PowerAuditEntity> getAuditDetails(@Param("powerAuditVO") PowerAuditVO powerAuditVO);
    List<DetailsDTO> getAuditDetailsNew(@Param("powerAuditVO") PowerAuditVO powerAuditVO);

    List<DetailsDTO> getAuditDetailsMore(@Param("pcids") List<String> pcids,@Param("month") String month);
    PowerAuditVO getAuditYmmc(@Param("pcid") Long pcid);
    PowerAuditVO getAuditEsYmmc(@Param("pcid") Long pcid);

    List<DetailsDTO> getPeriodAccount(@Param("pcids") List<String> pcids);

    List<AccountQua> getQuaJt5grList(@Param("ids") List<String> stationcodeintidList);

    PowerAuditEntity getAuditByInfo(@Param("pcid") String pcid, @Param("mssAccountId") String mssAccountId);
}
