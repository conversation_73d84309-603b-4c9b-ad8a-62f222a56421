package com.sccl.modules.business.powerintelligentinf2.mapper;

import com.sccl.modules.business.powerintelligentinf2.domain.PowerIntelligentInf2;
import com.sccl.framework.mapper.BaseMapper;

import java.util.List;
import java.util.Map;

/**
 * PUE管控主 数据层
 * 
 * <AUTHOR>
 * @date 2019-07-18
 */
public interface PowerIntelligentInf2Mapper extends BaseMapper<PowerIntelligentInf2>
{

    List<Map<String,Object>> selectByList(Map<String,Object> params);//列表
    List<Map<String,Object>> selectTotalPower(Map<String,Object> params);//列表

}