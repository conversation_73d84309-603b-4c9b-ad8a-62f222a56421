package com.sccl.modules.mssaccount.mssconstractmain.service;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.autojob.util.convert.StringUtils;
import com.sccl.modules.business.powermodel.entity.HistoryContractPricePro;
import com.sccl.modules.mssaccount.mssconstractmain.domain.HistoryContractPrice;
import com.sccl.modules.mssaccount.mssconstractmain.domain.MssConstractmain;
import com.sccl.modules.mssaccount.mssconstractmain.mapper.MssConstractmainMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 合同 服务层实现
 *
 * <AUTHOR>
 * @date 2019-05-01
 */
@Service
public class MssConstractmainServiceImpl extends BaseServiceImpl<MssConstractmain> implements IMssConstractmainService {
    @Autowired
    private MssConstractmainMapper mssConstractmainMapper;

    @Override
    public String selectContractPrice(String contractcode) {
        String price = mssConstractmainMapper.selectContractPrice(contractcode);
        if (price != null) {
            price = MssConstractmain.extractTextWithRegex(price, "\\d+\\.\\d+元/度");
        }
        if (StringUtils.isBlank(price)) {
            price = String.format("%s合同对应电费单价未找到，请手工填写协议确定单价", contractcode);
        }

        return price;

    }

    @Override
    public String selectContractPricebyammeterid(String ammeterid) {
        String price = mssConstractmainMapper.selectContractPricebyammeterid(ammeterid);
        if (price != null) {
            price = MssConstractmain.extractTextWithRegex(price, "\\d+\\.\\d+元/度");
        }
        if (StringUtils.isBlank(price)) {
            price = String.format("合同对应电费单价未找到");
        }
        return price;
    }

    @Override
    public List<HistoryContractPrice> selectHistoryContractPrice(HistoryContractPrice historyContractPrice) {
        List<HistoryContractPrice> list = mssConstractmainMapper.selectHistoryContractPrice(historyContractPrice);

        list.forEach(
          item -> {
              item.ProcessPrice(item);
          }
        );
        return list;
    }
   @Override
    public List<HistoryContractPricePro> selectHistoryContractPricePro(HistoryContractPricePro historyContractPrice) {
        List<HistoryContractPricePro> list = mssConstractmainMapper.selectHistoryContractPricePro(historyContractPrice);
        list.forEach(
          item->item.ProcessPrice(item)
        );
        return list;
    }
}
