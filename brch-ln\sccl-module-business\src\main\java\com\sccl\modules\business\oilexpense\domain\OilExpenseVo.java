package com.sccl.modules.business.oilexpense.domain;

import com.sccl.framework.web.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2021/12/16 19:49
 **/
@Getter
@Setter
public class OilExpenseVo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 油机编号
     */
    private String oilEngineId;
    /**
     * 油机户名
     */
    private String oilEngineName;
    /**
     * 所属部门
     */
    private Long country;
    /**
     * 分公司
     */
    private Long company;
    /**
     * 分局支局
     */
    private String substation;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 型号
     */
    private String type;
    /**
     * 用油类别
     */
    private String oilType;
    /**
     * 总容量
     */
    private BigDecimal capacity;
    /**
     * 库存量
     */
    private BigDecimal stock;
    /**
     * 系统库存量
     */
    private BigDecimal sysstock;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 油机性质 0 固定 1 移动
     */
    private Integer quality;
    /**
     * 发电功率
     */
    private BigDecimal power;
    /**
     * 单位功率油耗
     */
    private BigDecimal unitOilCost;
    /**
     * 管理负责人
     */
    private String master;
    /**
     * 单据状态
     */
    private Integer billStatus;
    /**
     * 局站编码
     */
    private String stationCode;

    private String companyName;

    private String countryName;
    private String carplate;

    private Integer status;
    private Long procInstId;
    private Long busiKey;
    private String busiAlias;
    private String summaruser;
    private Integer pageSize;
    private Integer pageNum;

    private BigDecimal refueInTotal; //油机加油量合计
    private BigDecimal refueInMoney; //油机加油费用合计
    private BigDecimal refueOutMoney;//油机耗油费用合计
    private BigDecimal refueOutTotal; //油机发电耗油量合计
    private BigDecimal differenceMoney; //油机发电耗油量合计
    private BigDecimal unitprice; //油卡单价
    private BigDecimal eaunitprice; //手动输入加油费用计算单价
    private String  startdate;
    private String enddate;
    private Long pabrid;
    private String accountnum;

}
