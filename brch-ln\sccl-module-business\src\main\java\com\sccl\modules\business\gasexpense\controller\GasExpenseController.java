package com.sccl.modules.business.gasexpense.controller;

import com.alibaba.fastjson.JSONObject;
import com.sccl.common.support.Convert;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.gasexpense.domain.GasExpense;
import com.sccl.modules.business.gasexpense.service.IGasExpenseService;
import com.sccl.modules.business.oilexpense.mapper.OilExpenseMapper;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 气基础 信息操作处理
 *
 * <AUTHOR>
 * @date 2022-01-07
 */
@RestController
@RequestMapping("/business/gasExpense")
public class GasExpenseController extends BaseController {
    private final String prefix = "business/gasExpense";

    @Autowired
    private IGasExpenseService gasExpenseService;

    @Resource
    OilExpenseMapper oilExpenseMapper;

    @RequiresPermissions("business:gasExpense:view")
    @GetMapping()
    public String gasExpense() {
        return prefix + "/gasExpense";
    }

    /**
     * 查询气基础列表
     */
//    @RequiresPermissions("business:gasExpense:list")
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(GasExpense gasExpense) {
        if (gasExpense.getCompany() == -1) {
            gasExpense.setCompany(null);
        }
        if (gasExpense.getCountry() == -1) {
            gasExpense.setCountry(null);
        }
        startPage();
        List<GasExpense> list = gasExpenseService.selectList(gasExpense);
        return getDataTable(list);
    }

    /**
     * 新增气基础
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存气基础
     */
//    @RequiresPermissions("business:gasExpense:add")
    @Log(title = "气基础", action = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody GasExpense gasExpense) {
        try {
            gasExpenseService.insert(gasExpense);
            if (gasExpense.getStationCode() != null && !gasExpense.getStationCode().equals("")) {
                String[] split = gasExpense.getStationCode().split(",");
                for (String stationId : split) {
                    oilExpenseMapper.saveMachineAndStation(Long.valueOf(stationId), gasExpense.getId());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("添加失败");
        }
        return AjaxResult.success("操作成功");
    }

    /**
     * 修改气基础
     */
    @GetMapping("/edit/{id}")
    public AjaxResult edit(@PathVariable("id") Long id) {
        GasExpense gasExpense = gasExpenseService.get(id);
        Object object = JSONObject.toJSON(gasExpense);
        return this.success(object);
    }

    /**
     * 修改保存气基础
     */
//    @RequiresPermissions("business:gasExpense:edit")
    @Log(title = "气基础", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody GasExpense gasExpense) {
        if (gasExpense.getStationCode() != null && !gasExpense.getStationCode().equals("")) {
            oilExpenseMapper.updateMachineAndStation(gasExpense.getId()); // 删除
            String[] split = gasExpense.getStationCode().split(",");
            for (String stationId : split) {
                oilExpenseMapper.saveMachineAndStation(Long.valueOf(stationId), gasExpense.getId());
            }
        }
        return toAjax(gasExpenseService.updateForModel(gasExpense));
    }

    /**
     * 删除气基础
     */
//    @RequiresPermissions("business:gasExpense:remove")
    @Log(title = "气基础", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(gasExpenseService.deleteByIds(Convert.toStrArray(ids)));
    }


    /**
     * 查看气基础
     */
//    @RequiresPermissions("business:gasExpense:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id) {
        GasExpense gasExpense = gasExpenseService.get(id);
        Object object = JSONObject.toJSON(gasExpense);
        return this.success(object);
    }

}
