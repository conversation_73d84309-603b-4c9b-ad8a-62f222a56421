package com.sccl.modules.business.mssaccountprepaid.controller;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.modules.business.accountbillpre.domain.Accountbillpre;
import com.sccl.modules.mssaccount.mssabccustomer.service.IMssAbccustomerService;
import com.sccl.modules.mssaccount.mssaccountbillitem.domain.MssAccountbillitem;
import com.sccl.modules.mssaccount.mssaccountbillitem.service.IMssAccountbillitemService;
import com.sccl.modules.system.organization.service.IOrganizationService;
import com.sccl.modules.system.role.domain.Role;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.service.IUserService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.business.mssaccountprepaid.domain.MssAccountPrepaid;
import com.sccl.modules.business.mssaccountprepaid.service.IMssAccountPrepaidService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 关联交易客户代垫及收款 信息操作处理
 * 
 * <AUTHOR>
 * @date 2021-10-24
 */
@RestController
@RequestMapping("/business/mssAccountPrepaid")
public class MssAccountPrepaidController extends BaseController
{
    private String prefix = "business/mssAccountPrepaid";
	
	@Autowired
	private IMssAccountPrepaidService mssAccountPrepaidService;
    @Autowired
    private IUserService userService;
    @Autowired
    private IOrganizationService organizationService;
	@Autowired
	private IMssAbccustomerService mssAbccustomerService;
	@Autowired
	private IMssAccountbillitemService mssAccountbillitemService;
	@RequiresPermissions("business:mssAccountPrepaid:view")
	@GetMapping()
	public String mssAccountPrepaid()
	{
	    return prefix + "/mssAccountPrepaid";
	}
	
	/**
	 * 查询关联交易客户代垫及收款列表
	 */
	@RequiresPermissions("business:mssAccountPrepaid:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(MssAccountPrepaid mssAccountPrepaid)
	{
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("PROVI_")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
            if (role.getCode().startsWith("SUB_")) {//县能耗费管理员
                isSubAdmin = true;
            }
        }
        if (isProAdmin) {//  查询权限设置 分公司
        } else if (isCityAdmin) {
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0)
                mssAccountPrepaid.setCompanyCode(new Long(companies.get(0).getId()));
        } else if (isSubAdmin) {
            List<IdNameVO> departments = user.getDepartments();
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0) {
                List<Map<String, Object>> countrys = organizationService.selectSubordinateOrgByRole(companies.get(0).getId(), "1");
/*                if (countrys != null && countrys.size() > 0) {
                    mssAccountPrepaid.setCountrys(countrys);
                } */
            }
        }
		startPage();
        List<MssAccountPrepaid> list = mssAccountPrepaidService.selectList(mssAccountPrepaid);
		return getDataTable(list);
	}
	
	/**
	 * 新增关联交易客户代垫及收款
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存关联交易客户代垫及收款
	 */
	@RequiresPermissions("business:mssAccountPrepaid:add")
	@Log(title = "关联交易客户代垫及收款", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody MssAccountPrepaid mssAccountPrepaid)
	{		
		return toAjax(mssAccountPrepaidService.insert(mssAccountPrepaid));
	}

	/**
	 * 修改关联交易客户代垫及收款
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		MssAccountPrepaid mssAccountPrepaid = mssAccountPrepaidService.get(id);

		Object object = JSONObject.toJSON(mssAccountPrepaid);

        return this.success(object);
	}
	
	/**
	 * 修改保存关联交易客户代垫及收款
	 */
	@RequiresPermissions("business:mssAccountPrepaid:edit")
	@Log(title = "关联交易客户代垫及收款", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody MssAccountPrepaid mssAccountPrepaid)
	{		
		return toAjax(mssAccountPrepaidService.update(mssAccountPrepaid));
	}
	
	/**
	 * 删除关联交易客户代垫及收款
	 */
	@RequiresPermissions("business:mssAccountPrepaid:remove")
	@Log(title = "关联交易客户代垫及收款", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(mssAccountPrepaidService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看关联交易客户代垫及收款
     */
    @RequiresPermissions("business:mssAccountPrepaid:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		MssAccountPrepaid mssAccountPrepaid = mssAccountPrepaidService.get(id);

        Object object = JSONObject.toJSON(mssAccountPrepaid);

        return this.success(object);
    }
	/**
	 * @Description: 余额
	 * @author: xuyi
	 * @date: ********
	 * @param:
	 * @return:
	 */
	@RequestMapping("/isprepaid")
	@ResponseBody
	public AjaxResult isprepaid(@RequestBody List<MssAccountbillitem>  itemlist)
	{
		AjaxResult json = new AjaxResult();
		Map<String, Object> map = new HashMap<>();
		String flag="0";
		String orgid = null;
		User user = ShiroUtils.getUser();
		List<IdNameVO> companies = user.getCompanies();
		List<IdNameVO> departments = user.getDepartments();

		//List<MssAccountbillitem> list = mssAccountbillitemService.selectBy("writeoffInstanceId", mssAccountbillitem.getId());
		for (MssAccountbillitem m: itemlist ) {
			if (("2".equals(m.getBudgetType())) && ("2".equals(m.getUsageId()))) {
				if (mssAbccustomerService.isRelateabccustomer(m.getDebitAccountCode())) {
					MssAccountPrepaid mssAccountPrepaid = new MssAccountPrepaid();
					mssAccountPrepaid.setDebitAccountCode(m.getDebitAccountCode());
					if (companies != null && companies.size() > 0)
						mssAccountPrepaid.setCompanyCode(new Long(companies.get(0).getId()));
					if (departments != null && departments.size() > 0) {
						mssAccountPrepaid.setCountry(new Long(departments.get(0).getId()));
					} else {
						return this.error(1, "没有查询到部门信息");
					}

					BigDecimal result = mssAccountPrepaidService.computeBalance(mssAccountPrepaid);
					System.out.println("sum: " + m.getSum().toString());
					System.out.println("result: " + result);
					if (m.getSum().compareTo(result) == 1)
						flag = "1";
				}
			}
		}

		map.put("msg", "关联交易供应商 先收款后代垫");
		map.put("code", flag);
		json.put("data", map);
		return json;
	}
}
