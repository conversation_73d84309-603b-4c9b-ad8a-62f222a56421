package com.sccl.modules.business.stationequipment.controller;

import com.alibaba.fastjson.JSONObject;
import com.sccl.common.support.Convert;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.utils.ExcelUtil;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.autojob.util.bean.ObjectUtil;
import com.sccl.modules.autojob.util.convert.MessageMaster;
import com.sccl.modules.autojob.util.convert.StringUtils;
import com.sccl.modules.autojob.util.id.IdGenerator;
import com.sccl.modules.business.basestation.service.IStationGradeService;
import com.sccl.modules.business.budget.domain.PageBean;
import com.sccl.modules.business.stationequipment.domain.StationEquipment;
import com.sccl.modules.business.stationequipment.domain.TowerStationEquipment2;
import com.sccl.modules.business.stationequipment.service.IStationEquipmentService;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;
import com.sccl.modules.system.attachments.domain.Attachments;
import com.sccl.modules.system.attachments.domain.UpLoadFile;
import com.sccl.modules.system.role.domain.Role;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.service.IUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 铁塔站址设备 信息操作处理
 *
 * <AUTHOR> Yongxiang
 * @date 2022-08-09
 */

@RestController
@RequestMapping("/business/station_equipment")
@Slf4j
public class StationEquipmentController extends BaseController {
    private static final Map<String, String> IMPORT_COLUMN_MAP = new HashMap<>();
    private static final Map<String, String> IMPORT_COLUMN_MAP_2 = new HashMap<>();
    private static final Map<String, String> EXPORT_COLUMN_MAP = new HashMap<>();
    private static final Map<String, String> EXPORT_COLUMN_MAP_2 = new HashMap<>();
    private static final Map<String, String> PROMPT_COLUMN_MAP = new HashMap<>();

    static {
        IMPORT_COLUMN_MAP.put("设备组Id（请勿修改）", "groupId");
        IMPORT_COLUMN_MAP.put("省", "province");
        IMPORT_COLUMN_MAP.put("市", "city");
        IMPORT_COLUMN_MAP.put("站址编码", "stationCode");
        IMPORT_COLUMN_MAP.put("站址编码老", "oldStationCode");
        IMPORT_COLUMN_MAP.put("铁塔站址编码", "towerStationCode");
        IMPORT_COLUMN_MAP.put("设备类型", "type");
        IMPORT_COLUMN_MAP.put("设备厂家", "factory");
        IMPORT_COLUMN_MAP.put("设备型号", "model");
        IMPORT_COLUMN_MAP.put("设备数目", "count");
        IMPORT_COLUMN_MAP.put("版本号（请勿修改）", "version");
    }

    static {
        IMPORT_COLUMN_MAP_2.put("省份标识", "province");
        IMPORT_COLUMN_MAP_2.put("月账期", "monthaccount");
        IMPORT_COLUMN_MAP_2.put("省份名称", "provincename");
        IMPORT_COLUMN_MAP_2.put("MSS省份编码", "mssProvince");
        IMPORT_COLUMN_MAP_2.put("本地网标识", "networkLocal");
        IMPORT_COLUMN_MAP_2.put("本地网名称", "networkLocalName");
        IMPORT_COLUMN_MAP_2.put("市局组织编码", "city");
        IMPORT_COLUMN_MAP_2.put("站址编码-报账", "mssStationCode");
        IMPORT_COLUMN_MAP_2.put("站址编码-旧", "oldStationCode");
        IMPORT_COLUMN_MAP_2.put("铁塔站址编码", "towerStationCode");
        IMPORT_COLUMN_MAP_2.put("局站类型", "typeStation");
        IMPORT_COLUMN_MAP_2.put("机房类型", "typeComputer");
        IMPORT_COLUMN_MAP_2.put("报账单号", "mssCode");
        IMPORT_COLUMN_MAP_2.put("电表编码", "codeMeter");
        IMPORT_COLUMN_MAP_2.put("填报人名称", "nameFull");
        IMPORT_COLUMN_MAP_2.put("开始日期", "timeStart");
        IMPORT_COLUMN_MAP_2.put("结束日期", "timeEnd");
        IMPORT_COLUMN_MAP_2.put("报账金额", "mssaccount");
        IMPORT_COLUMN_MAP_2.put("报账电量", "powerMss");
        IMPORT_COLUMN_MAP_2.put("价款", "pricePre");
        IMPORT_COLUMN_MAP_2.put("税款", "priceAfter");
        IMPORT_COLUMN_MAP_2.put("供电方式", "typePowersupply");
        IMPORT_COLUMN_MAP_2.put("站址稽核电量-全部", "powerStationAudit1");
        IMPORT_COLUMN_MAP_2.put("报账与能耗差异-全部", "diffMssFornh1");
        IMPORT_COLUMN_MAP_2.put("站址稽核电量-不含CDMA", "powerStationAudit2");
        IMPORT_COLUMN_MAP_2.put("报账与能耗差异-不含CDMA%", "diffMssFornh2");
        IMPORT_COLUMN_MAP_2.put("差异区间", "diffRange");
        IMPORT_COLUMN_MAP_2.put("AAU/RRU数量（5G)", "quantityAauRru1");
        IMPORT_COLUMN_MAP_2.put("BBU数量(5G)", "quantityBbu1");
        IMPORT_COLUMN_MAP_2.put("AAU/RRU数量（4G)", "quantityAauRru2");
        IMPORT_COLUMN_MAP_2.put("BBU数量(4G)", "quantityBbu2");
        IMPORT_COLUMN_MAP_2.put("PRRU数量(5G)", "quantityPrru1");
        IMPORT_COLUMN_MAP_2.put("PRRU数量(4G)", "quantityPrru2");
        IMPORT_COLUMN_MAP_2.put("C网设备数量", "quantityC");
        IMPORT_COLUMN_MAP_2.put("远端直放站数量", "quantityRemote");
        IMPORT_COLUMN_MAP_2.put("近端直放站数量", "quantityNear");
        IMPORT_COLUMN_MAP_2.put("微波数量", "quantityWei");
        IMPORT_COLUMN_MAP_2.put("IPRAN数量", "quantityIppan");
        IMPORT_COLUMN_MAP_2.put("皮站数量", "quantityP");
        IMPORT_COLUMN_MAP_2.put("光接入数量", "quantityG");
        IMPORT_COLUMN_MAP_2.put("传输设备数量", "quantityTrans");
        IMPORT_COLUMN_MAP_2.put("交换机数量", "quantitySwitch");
        IMPORT_COLUMN_MAP_2.put("城域网数量", "quantityDomain");
        IMPORT_COLUMN_MAP_2.put("其它设备数量", "quantityOther");
        IMPORT_COLUMN_MAP_2.put("AAU/RRU能耗（)", "powerAauRru1");
        IMPORT_COLUMN_MAP_2.put("BBU能耗(5G)", "powerBbu1");
        IMPORT_COLUMN_MAP_2.put("AAU/RRU能耗（)", "powerAauRru2");
        IMPORT_COLUMN_MAP_2.put("BBU能耗(4G)", "powerBbu2");
        IMPORT_COLUMN_MAP_2.put("PRRU能耗(5G)", "powerPrru1");
        IMPORT_COLUMN_MAP_2.put("PRRU能耗(4G)", "powerPrru2");
        IMPORT_COLUMN_MAP_2.put("C网设备能耗", "powerC");
        IMPORT_COLUMN_MAP_2.put("远端直放站能耗", "powerRemote");
        IMPORT_COLUMN_MAP_2.put("近端直放站能耗", "powerNear");
        IMPORT_COLUMN_MAP_2.put("微波能耗", "powerWei");
        IMPORT_COLUMN_MAP_2.put("IPRAN能耗", "powerIpran");
        IMPORT_COLUMN_MAP_2.put("光接入能耗", "powerG");
        IMPORT_COLUMN_MAP_2.put("传输设备能耗", "powerTrans");
        IMPORT_COLUMN_MAP_2.put("交换机能耗", "powerSwitch");
        IMPORT_COLUMN_MAP_2.put("城域网能耗", "powerDomain");
        IMPORT_COLUMN_MAP_2.put("其它设备能耗", "powerOther");
        IMPORT_COLUMN_MAP_2.put("PUE系数(主要)pueMain", "");
        IMPORT_COLUMN_MAP_2.put("PUE系数(机房)pueComputer", "");
        IMPORT_COLUMN_MAP_2.put("PUE系数(季节)pueSeason", "");
        IMPORT_COLUMN_MAP_2.put("PUE系数(默认)pueDefault", "");
        IMPORT_COLUMN_MAP_2.put("是否打包报账站址", "packFlag");
        IMPORT_COLUMN_MAP_2.put("是否包干站址", "baoganFlag");
        IMPORT_COLUMN_MAP_2.put("是否补缴预缴站址", "bujiaoFlag");
        IMPORT_COLUMN_MAP_2.put("是否有换充电业务", "chargingFlag");
        IMPORT_COLUMN_MAP_2.put("是否有智联业务", "zlFlag");
        IMPORT_COLUMN_MAP_2.put("是否报账周期异常", "exceptionMsFlag");
        IMPORT_COLUMN_MAP_2.put("是否需要核查", "validateFlag");
    }

    static {
        EXPORT_COLUMN_MAP.put("groupId", "设备组Id（请勿修改）");
        EXPORT_COLUMN_MAP.put("province", "省");
        EXPORT_COLUMN_MAP.put("city", "市");
        EXPORT_COLUMN_MAP.put("stationCode", "站址编码");
        EXPORT_COLUMN_MAP.put("oldStationCode", "站址编码老");
        EXPORT_COLUMN_MAP.put("towerStationCode", "铁塔站址编码");
        EXPORT_COLUMN_MAP.put("type", "设备类型");
        EXPORT_COLUMN_MAP.put("factory", "设备厂家");
        EXPORT_COLUMN_MAP.put("model", "设备型号");
        EXPORT_COLUMN_MAP.put("count", "设备数目");
        EXPORT_COLUMN_MAP.put("version", "版本号（请勿修改）");
    }

    static {
        EXPORT_COLUMN_MAP_2.put("province", "省份标识");
        EXPORT_COLUMN_MAP_2.put("monthaccount", "月账期");
        EXPORT_COLUMN_MAP_2.put("provincename", "省份名称");
        EXPORT_COLUMN_MAP_2.put("mssProvince", "MSS省份编码");
        EXPORT_COLUMN_MAP_2.put("networkLocal", "本地网标识");
        EXPORT_COLUMN_MAP_2.put("networkLocalName", "本地网名称");
        EXPORT_COLUMN_MAP_2.put("city", "市局组织编码");
        EXPORT_COLUMN_MAP_2.put("mssStationCode", "站址编码-报账");
        EXPORT_COLUMN_MAP_2.put("oldStationCode", "站址编码-旧");
        EXPORT_COLUMN_MAP_2.put("towerStationCode", "铁塔站址编码");
        EXPORT_COLUMN_MAP_2.put("typeStation", "局站类型");
        EXPORT_COLUMN_MAP_2.put("typeComputer", "机房类型");
        EXPORT_COLUMN_MAP_2.put("mssCode", "报账单号");
        EXPORT_COLUMN_MAP_2.put("codeMeter", "电表编码");
        EXPORT_COLUMN_MAP_2.put("nameFull", "填报人名称");
        EXPORT_COLUMN_MAP_2.put("timeStart", "开始日期");
        EXPORT_COLUMN_MAP_2.put("timeEnd", "结束日期");
        EXPORT_COLUMN_MAP_2.put("mssaccount", "报账金额");
        EXPORT_COLUMN_MAP_2.put("powerMss", "报账电量");
        EXPORT_COLUMN_MAP_2.put("pricePre", "价款");
        EXPORT_COLUMN_MAP_2.put("priceAfter", "税款");
        EXPORT_COLUMN_MAP_2.put("typePowersupply", "供电方式");
        EXPORT_COLUMN_MAP_2.put("powerStationAudit1", "站址稽核电量-全部");
        EXPORT_COLUMN_MAP_2.put("diffMssFornh1", "报账与能耗差异-全部");
        EXPORT_COLUMN_MAP_2.put("powerStationAudit2", "站址稽核电量-不含CDMA");
        EXPORT_COLUMN_MAP_2.put("diffMssFornh2", "报账与能耗差异-不含CDMA");
        EXPORT_COLUMN_MAP_2.put("diffRange", "差异区间");
        EXPORT_COLUMN_MAP_2.put("quantityAauRru1", "AAU/RRU数量");
        EXPORT_COLUMN_MAP_2.put("quantityBbu1", "BBU数量(5G)");
        EXPORT_COLUMN_MAP_2.put("quantityAauRru2", "AAU/RRU数量");
        EXPORT_COLUMN_MAP_2.put("quantityBbu2", "BBU数量(4G)");
        EXPORT_COLUMN_MAP_2.put("quantityPrru1", "PRRU数量(5G)");
        EXPORT_COLUMN_MAP_2.put("quantityPrru2", "PRRU数量(4G)");
        EXPORT_COLUMN_MAP_2.put("quantityC", "C网设备数量");
        EXPORT_COLUMN_MAP_2.put("quantityRemote", "远端直放站数量");
        EXPORT_COLUMN_MAP_2.put("quantityNear", "近端直放站数量");
        EXPORT_COLUMN_MAP_2.put("quantityWei", "微波数量");
        EXPORT_COLUMN_MAP_2.put("quantityIppan", "IPRAN数量");
        EXPORT_COLUMN_MAP_2.put("quantityP", "皮站数量");
        EXPORT_COLUMN_MAP_2.put("quantityG", "光接入数量");
        EXPORT_COLUMN_MAP_2.put("quantityTrans", "传输设备数量");
        EXPORT_COLUMN_MAP_2.put("quantitySwitch", "交换机数量");
        EXPORT_COLUMN_MAP_2.put("quantityDomain", "城域网数量");
        EXPORT_COLUMN_MAP_2.put("quantityOther", "其它设备数量");
        EXPORT_COLUMN_MAP_2.put("powerAauRru1", "AAU/RRU能耗)");
        EXPORT_COLUMN_MAP_2.put("powerBbu1", "BBU能耗(5G)");
        EXPORT_COLUMN_MAP_2.put("powerAauRru2", "AAU/RRU能耗)");
        EXPORT_COLUMN_MAP_2.put("powerBbu2", "BBU能耗(4G)");
        EXPORT_COLUMN_MAP_2.put("powerPrru1", "PRRU能耗(5G)");
        EXPORT_COLUMN_MAP_2.put("powerPrru2", "PRRU能耗(4G)");
        EXPORT_COLUMN_MAP_2.put("powerC", "C网设备能耗");
        EXPORT_COLUMN_MAP_2.put("powerRemote", "远端直放站能耗");
        EXPORT_COLUMN_MAP_2.put("powerNear", "近端直放站能耗");
        EXPORT_COLUMN_MAP_2.put("powerWei", "微波能耗");
        EXPORT_COLUMN_MAP_2.put("powerIpran", "IPRAN能耗");
        EXPORT_COLUMN_MAP_2.put("powerG", "光接入能耗");
        EXPORT_COLUMN_MAP_2.put("powerTrans", "传输设备能耗");
        EXPORT_COLUMN_MAP_2.put("powerSwitch", "交换机能耗");
        EXPORT_COLUMN_MAP_2.put("powerDomain", "城域网能耗");
        EXPORT_COLUMN_MAP_2.put("powerOther", "其它设备能耗");
        EXPORT_COLUMN_MAP_2.put("pueMain", "PUE系数(主要");
        EXPORT_COLUMN_MAP_2.put("pueComputer", "PUE系数(机房");
        EXPORT_COLUMN_MAP_2.put("pueSeason", "PUE系数(季节");
        EXPORT_COLUMN_MAP_2.put("pueDefault", "PUE系数(默认");
        EXPORT_COLUMN_MAP_2.put("packFlag", "是否打包报账站址");
        EXPORT_COLUMN_MAP_2.put("baoganFlag", "是否包干站址");
        EXPORT_COLUMN_MAP_2.put("bujiaoFlag", "是否补缴预缴站址");
        EXPORT_COLUMN_MAP_2.put("chargingFlag", "是否有换充电业务");
        EXPORT_COLUMN_MAP_2.put("zlFlag", "是否有智联业务");
        EXPORT_COLUMN_MAP_2.put("exceptionMsFlag", "是否报账周期异常");
        EXPORT_COLUMN_MAP_2.put("validateFlag", "是否需要核查");

    }

    static {
        PROMPT_COLUMN_MAP.put("groupId", "系统导出的表格拥有该字段，用户可以对导出的表格进行任意修改（除标注请勿修改的字段除外），修改后可以直接导入到系统，系统可以通过设备组ID" + "找到匹配的设备进行更新");
        PROMPT_COLUMN_MAP.put("province", "必填");
        PROMPT_COLUMN_MAP.put("city", "必填");
        PROMPT_COLUMN_MAP.put("stationCode", "必填\n1、与4G综合网管/5GR系统站址编码保持一致；\n\n2、每个站址可填多种设备，但设备信息需分行填写");
        PROMPT_COLUMN_MAP.put("oldStationCode", "省内自己添加（非必填项）");
        PROMPT_COLUMN_MAP.put("towerStationCode", "省内自己添加（非必填项）");
        PROMPT_COLUMN_MAP.put("type", "必填\n字典值：\n直放站\n皮站\n微波\nIPRAN\n光接入\n传输\n交换机\n城域网\n其它\n（只能按照上面9类字典值填写，不属于前8" + "类定义的设备一律填“其它”）");
        PROMPT_COLUMN_MAP.put("model", "非必填");
        PROMPT_COLUMN_MAP.put("factory", "必填\n1、当设备类型=直放站时，设备型号只可填近端或远端；\n2、2/3/4/5G无线主设备、空调、门禁、摄像头均无需填写");
        PROMPT_COLUMN_MAP.put("count", "必填");
        PROMPT_COLUMN_MAP.put("version", "版本号为系统生成字段，数值代表此版本前还有n个版本，最大的版本号代表最新的记录");
    }

    private final String prefix = "business/stationEquipment";

    @Autowired
    private IStationGradeService gradeService;
    @Autowired
    private IStationEquipmentService stationEquipmentService;
    @Autowired
    private IUserService userService;

    public static void main(String[] args) {

        LocalDate parse = LocalDate.parse("2022-08");
    }

    /*=================新增接口=================>*/
    @PostMapping(value = "/insert", produces = "application/json;charset=UTF-8")
    public String insert(@RequestBody(required = false) StationEquipment stationEquipment) {
        if (stationEquipment == null || StringUtils.isEmpty(stationEquipment.getProvince()) || StringUtils.isEmpty(stationEquipment.getCity()) || stationEquipment.getCount() == null || StringUtils.isEmpty(stationEquipment.getStationCode()) || StringUtils.isEmpty(stationEquipment.getModel()) || StringUtils.isEmpty(stationEquipment.getType())) {
            return MessageMaster.DefaultMessage.EMPTY_PARAMS.toString();
        }
        stationEquipment.setVersion(0L);
        stationEquipment.setId(IdGenerator.getNextIdAsLong());
        stationEquipment.setGroupId(IdGenerator.getNextIdAsLong());
        stationEquipment.setCreateTime(new Date());
        stationEquipment.setUpdateTime(new Date());
        int count = stationEquipmentService.insert(stationEquipment);
        return MessageMaster.getMessage(count == 1 ? MessageMaster.Code.OK : MessageMaster.Code.ERROR, count == 1 ? "新增成功" : "新增失败", stationEquipment, false, false);
    }

    @PostMapping(value = "/update", produces = "application/json;charset=UTF-8")
    public String update(@RequestBody(required = false) StationEquipment stationEquipment) {
        if (stationEquipment == null || stationEquipment.getGroupId() == null) {
            return MessageMaster.DefaultMessage.EMPTY_PARAMS.toString();
        }
        StationEquipment history = stationEquipmentService.getLatestByGroupId(stationEquipment.getGroupId());
        if (history == null) {
            return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "没有Id：" + stationEquipment.getId() + "的设备");
        }
        ObjectUtil.mergeObjectFields(stationEquipment, history, "id", "version");
        history.setId(IdGenerator.getNextIdAsLong());
        //更新版本号
        history.setVersion(history.getVersion() + 1);
        stationEquipment.setGroupId(history.getGroupId());
        history.setCreateTime(new Date());
        history.setUpdateTime(new Date());
        int count = stationEquipmentService.insert(history);
        return MessageMaster.getMessage(count == 1 ? MessageMaster.Code.OK : MessageMaster.Code.ERROR, count == 1 ? "更新成功" : "更新失败", history, false, false);
    }

    @GetMapping(value = "/get/{groupId}", produces = "application/json;charset=UTF-8")
    public String getByGroupId(@PathVariable("groupId") Long groupId) {
        if (groupId == null) {
            return MessageMaster.DefaultMessage.EMPTY_PARAMS.toString();
        }
        StationEquipment history = stationEquipmentService.getLatestByGroupId(groupId);
        if (history == null) {
            return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "没有指定设备");
        }
        return MessageMaster.getMessage(MessageMaster.Code.OK, "查找成功", history, false, false);
    }

    @GetMapping("/demo")
    public String demo() {
        return "dmeo";
    }

    @GetMapping(value = "/get_all", produces = "application/json;charset=UTF-8")
    public TableDataInfo getAllEquipment() {
        startPage();
        List<StationEquipment> stationEquipments = stationEquipmentService.listLatest();
        return getDataTable(stationEquipments);
    }

    /**
     * 查询所有
     *
     * @return
     */
    @GetMapping(value = "/getall", produces = "application/json;charset=UTF-8")
    public TableDataInfo getAll(PageBean pageBean) {
        startPage();
        List<TowerStationEquipment2> stationEquipments = stationEquipmentService.getAll(pageBean);
        return getDataTable(stationEquipments);
    }

    /**
     * 传统poi 3.14 导入
     *
     * @param groupId
     * @return
     */
    @GetMapping(value = "/list_history/{groupId}", produces = "application/json;charset=UTF-8")
    public TableDataInfo listHistory(@PathVariable("groupId") Long groupId) {
        startPage();
        if (groupId == null) {
            TableDataInfo tableDataInfo = new TableDataInfo();
            tableDataInfo.setCode(0);
            tableDataInfo.setRows(Collections.emptyList());
            tableDataInfo.setTotal(0);
            return tableDataInfo;
        }
        List<StationEquipment> stationEquipments = stationEquipmentService.listHistoryByGroupId(groupId);
        return getDataTable(stationEquipments);
    }

    /*    */

    /**
     * easypoi 方式 导入
     *
     * @param file
     * @return
     *//*
    @RequestMapping(value = "/importe", method = RequestMethod.POST)
    @ResponseBody
    public String importMemberList(@RequestPart("file") MultipartFile file) {
        ImportParams params = new ImportParams();
        params.setTitleRows(1);
        params.setHeadRows(1);
        try {
            List<TowerStationEquipment2> list = ExcelImportUtil.importExcel(
                    file.getInputStream(),
                    TowerStationEquipment2.class, params
            );
            return list.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return "导入失败";
        }
    }*/
    @GetMapping(value = "/delete_by_group_id/{groupId}", produces = "application/json;charset=UTF-8")
    public String delete(@PathVariable("groupId") Long groupId) {
        if (groupId == null) {
            return MessageMaster.DefaultMessage.EMPTY_PARAMS.toString();
        }
        int count = stationEquipmentService.deleteByGroupId(groupId);
        if (count == 0) {
            return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "无匹配记录");
        }
        return MessageMaster.getMessage(MessageMaster.Code.OK, "删除成功", count);
    }


    @GetMapping(value = "/import_excel", produces = "application/json;charset=UTF-8")
    public String importExcel(HttpServletRequest request, @RequestParam(required = false, value = "sheet_name") String sheetName) {
        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        MultipartFile multipartFile = multiRequest.getFile("file");
        if (multipartFile == null || multipartFile.isEmpty()) {
            return MessageMaster.DefaultMessage.EMPTY_PARAMS.toString();
        }
        ExcelUtil<TowerStationEquipment2> excelUtil = new ExcelUtil<>(TowerStationEquipment2.class);
        try {
            List<TowerStationEquipment2> content = excelUtil.importExcel(sheetName, multipartFile, IMPORT_COLUMN_MAP_2);
            if (CollectionUtils.isEmpty(content)) {
                return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "文件内容为空，请检查格式或内容");
            }
            List<TowerStationEquipment2> existData = content
                    .stream()
                    .filter(item -> item.getGroupId() != null)
                    .collect(Collectors.toList());
            Map<Long, Long> groupVersionMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(existData)) {
                List<TowerStationEquipment2> historyList = stationEquipmentService.getAllLatestByGroupId(existData
                        .stream()
                        .map(TowerStationEquipment2::getGroupId)
                        .collect(Collectors.toList()));
                historyList.forEach(item -> {
                    groupVersionMap.put(item.getGroupId(), item.getVersion() + 1);
                });
            }
            content.forEach(item -> {
                item.setId(IdGenerator.getNextIdAsLong());
                if (item.getGroupId() != null && groupVersionMap.containsKey(item.getGroupId())) {
                    item.setVersion(groupVersionMap.get(item.getGroupId()));
                } else {
                    item.setGroupId(IdGenerator.getNextIdAsLong());
                    item.setVersion(0L);
                }
                item.setCreateTime(new Date());
                item.setUpdateTime(new Date());
            });
            int count = stationEquipmentService.insertList2(content);
            if (count != content.size()) {
                return MessageMaster.getMessage(MessageMaster.Code.OK, "导入成功，但导条目数和内容条目数不匹配");
            }
            return MessageMaster.getMessage(MessageMaster.Code.OK, "导入成功");
        } catch (Exception e) {
            e.printStackTrace();
            return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "导入错误：" + e.getMessage());
        }
    }

    /**
     * 参考原有excel导入
     *
     * @param request
     * @param response
     * @param uploadFile
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/uploadExcel")
    @ResponseBody
    public Map<String, Object> uploadExcel(HttpServletRequest request, HttpServletResponse response, UpLoadFile uploadFile) throws Exception {
        long start = System.currentTimeMillis();

        response.setContentType("text/html;charset=utf-8");
        Map<String, Object> map = new HashMap<>();
        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        Iterator<String> iterator = multiRequest.getFileNames();
        String str = "";

        List<TowerStationEquipment2> list = new ArrayList<>();
        while (iterator.hasNext()) {
            String name = iterator.next();
            List<MultipartFile> files = new LinkedList<>();
            files = multiRequest.getFiles(name);
            // 一次遍历所有文件
            for (MultipartFile file : files) {
                if (file == null) {
                    continue;
                }

                //如果文件大小为0则不上传
                long fileSize = file.getSize();
                if (fileSize <= 0L) {
                    throw new Exception("文件名称：【" + file
                            .getOriginalFilename()
                            .substring(file
                                    .getOriginalFilename()
                                    .lastIndexOf("\\") + 1, file
                                    .getOriginalFilename()
                                    .length()) + "】的文件大小为0,请核对!");
                }
                list = stationEquipmentService.importExcel("sheet1", file.getInputStream());

            }

        }


        if (list != null && list.size() > 0) {
            str += "成功解析【" + list.size() + "】条数据；";
            int listSize = list.size();
            int toIndex = 1000;
            for (int i = 0; i < list.size(); i += 1000) {
                //作用为toIndex最后没有1000条数据则剩余几条newList中就装几条
                if (i + 1000 > listSize) {
                    toIndex = listSize - i;
                }
                List newList = list.subList(i, i + toIndex);

                //保存到临时表
                stationEquipmentService.insertList2_Temporary(newList);
            }

            //删除重复数据
//            stationEquipmentService.deleteRepeat();
            //加入到正式表
            int num = 0;
            num = stationEquipmentService.insertTowerInfo();
            str += "成功加入【" + num + "】条数据到站址事后异常表；";

            //删除临时表
            stationEquipmentService.deleteAll();
            map.put("str", str);
        }

        if (com.sccl.common.lang.StringUtils.isEmpty(str)) {
            map.put("str", "导入失败");
        } else {
            map.put("str", str);
        }
        long end = System.currentTimeMillis();
        log.info("导入excel花费时间:{}", (end - start) / 1000);
        return map;
    }


    @PostMapping(value = "export_excel", produces = "application/json;charset=UTF-8")
    public String exportExcel(@RequestBody(required = false) TowerStationEquipment2 TowerStationEquipment2s, @RequestParam(value = "latest", required = false) Boolean latest) {
        if (TowerStationEquipment2s == null) {
            return MessageMaster.DefaultMessage.EMPTY_PARAMS.toString();
        }
        List<TowerStationEquipment2> equipmentDictList = null;
        if (latest != null && !latest) {
            equipmentDictList = stationEquipmentService.selectList1(TowerStationEquipment2s);
        } else {
            equipmentDictList = stationEquipmentService.listLatestConditional(TowerStationEquipment2s);
        }
        if (CollectionUtils.isEmpty(equipmentDictList)) {
            return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "指定条件下无匹配数据");
        }
        Attachments attachments = stationEquipmentService.exportExcel(equipmentDictList, EXPORT_COLUMN_MAP_2, PROMPT_COLUMN_MAP);
        if (attachments == null) {
            return MessageMaster.DefaultMessage.SYSTEM_ERROR.toString();
        }
        return MessageMaster.getMessage(MessageMaster.Code.OK, "上传成功", attachments, true, false);
    }

    @PostMapping(value = "/export_excel_content", produces = "application/json;charset=UTF-8")
//    public String exportExcel(@RequestBody(required = false) List<StationEquipment> stationEquipments) {
//        if (CollectionUtils.isEmpty(stationEquipments)) {
//            return MessageMaster.DefaultMessage.EMPTY_PARAMS.toString();
//        }
//        Attachments attachments = stationEquipmentService.exportExcel(stationEquipments, EXPORT_COLUMN_MAP,
//                                                                      PROMPT_COLUMN_MAP
//        );
//        if (attachments == null) {
//            return MessageMaster.DefaultMessage.SYSTEM_ERROR.toString();
//        }
//        return MessageMaster.getMessage(MessageMaster.Code.OK, "上传成功", attachments, true, false);
//    }


    /*=======================Finished======================<*/

    @RequiresPermissions("business:stationEquipment:view")
    @GetMapping()
    public String stationEquipment() {
        return prefix + "/stationEquipment";
    }

    /**
     * 查询铁塔站址设备列表
     */
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(StationEquipment stationEquipment) {
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        boolean isEditAdmin = false;
        for (Role role : roles) {
            if (null != role && com.sccl.common.lang.StringUtils.isNotEmpty(role.getCode()) && ("admin".equalsIgnoreCase(role.getCode()))) {
                isProAdmin = true;
                break;
            } else if (null != role && com.sccl.common.lang.StringUtils.isNotEmpty(role.getCode()) && role
                    .getCode()
                    .contains("_")) {
                String code = role
                        .getCode()
                        .substring(0, role
                                .getCode()
                                .indexOf("_", role
                                        .getCode()
                                        .indexOf("_")));
                if ("PROVI".equalsIgnoreCase(code)) {//省能耗费管理员
                    isProAdmin = true;
                    break;
                } else if ("CITY".equalsIgnoreCase(code) || "LOCALNET".equalsIgnoreCase(code)) {//省能耗费管理员
                    isCityAdmin = true;
                } else if ("SUB".equalsIgnoreCase(code)) {//县能耗费管理员
                    isSubAdmin = true;
                }
            }
        }
        List<StationEquipment> list = new ArrayList<>();
        if (isProAdmin) {
            startPage();
            list = stationEquipmentService.selectList(stationEquipment);
        } else if (isCityAdmin) {
            String company = user
                    .getCompanies()
                    .get(0)
                    .getId();
            String comy = stationEquipmentService.selCity2(company);
            stationEquipment.setCity(comy);
            startPage();
            list = stationEquipmentService.selectList(stationEquipment);
        } else {
            String company = user
                    .getCompanies()
                    .get(0)
                    .getId();
            String comy = stationEquipmentService.selCity2(company);
            stationEquipment.setCity(comy);
            startPage();
            list = stationEquipmentService.selectList(stationEquipment);
        }
        return getDataTable(list);
    }

    /**
     * 查询铁塔站址设备列表
     */
    @RequestMapping("/list2")
    @ResponseBody
    public TableDataInfo list2(StationEquipment stationEquipment) {
        startPage();
        List<StationEquipment> list = stationEquipmentService.selectList(stationEquipment);
        return getDataTable(list);
    }

    /**
     * 查询铁塔站址设备列表
     */

    @RequestMapping("/list3")
    @ResponseBody
    public TableDataInfo list3(TowerStationEquipment2 stationEquipment) {
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        boolean isEditAdmin = false;
        for (Role role : roles) {
            if (null != role && com.sccl.common.lang.StringUtils.isNotEmpty(role.getCode()) && ("admin".equalsIgnoreCase(role.getCode()))) {
                isProAdmin = true;
                break;
            } else if (null != role && com.sccl.common.lang.StringUtils.isNotEmpty(role.getCode()) && role
                    .getCode()
                    .contains("_")) {
                String code = role
                        .getCode()
                        .substring(0, role
                                .getCode()
                                .indexOf("_", role
                                        .getCode()
                                        .indexOf("_")));
                if ("PROVI".equalsIgnoreCase(code)) {//省能耗费管理员
                    isProAdmin = true;
                    break;
                } else if ("CITY".equalsIgnoreCase(code) || "LOCALNET".equalsIgnoreCase(code)) {//省能耗费管理员
                    isCityAdmin = true;
                } else if ("SUB".equalsIgnoreCase(code)) {//县能耗费管理员
                    isSubAdmin = true;
                }
            }
        }
        List<IdNameVO> companies = new ArrayList<>();
        List<TowerStationEquipment2> list = new ArrayList<>();

        if (isProAdmin) {
            startPage();
            list = stationEquipmentService.selectList3(stationEquipment);
        } else if (isCityAdmin) {
            String company = user
                    .getCompanies()
                    .get(0)
                    .getId();
            String comy = stationEquipmentService.selCity(company);
            stationEquipment.setCity(comy);
            startPage();
            list = stationEquipmentService.selectList3(stationEquipment);
        } else {
            String company = user
                    .getCompanies()
                    .get(0)
                    .getId();
            String comy = stationEquipmentService.selCity(company);
            stationEquipment.setCity(comy);

            startPage();
            list = stationEquipmentService.selectList3(stationEquipment);
        }

        return getDataTable(list);
    }

    @RequestMapping("/list3num")
    @ResponseBody
    public Map<String, Object> list3num(TowerStationEquipment2 stationEquipment) {
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        boolean isEditAdmin = false;
        for (Role role : roles) {
            if (null != role && com.sccl.common.lang.StringUtils.isNotEmpty(role.getCode()) && ("admin".equalsIgnoreCase(role.getCode()))) {
                isProAdmin = true;
                break;
            } else if (null != role && com.sccl.common.lang.StringUtils.isNotEmpty(role.getCode()) && role
                    .getCode()
                    .contains("_")) {
                String code = role
                        .getCode()
                        .substring(0, role
                                .getCode()
                                .indexOf("_", role
                                        .getCode()
                                        .indexOf("_")));
                if ("PROVI".equalsIgnoreCase(code)) {//省能耗费管理员
                    isProAdmin = true;
                    break;
                } else if ("CITY".equalsIgnoreCase(code) || "LOCALNET".equalsIgnoreCase(code)) {//省能耗费管理员
                    isCityAdmin = true;
                } else if ("SUB".equalsIgnoreCase(code)) {//县能耗费管理员
                    isSubAdmin = true;
                }
            }
        }
        List<IdNameVO> companies = new ArrayList<>();
        Long num;
        Map<String, Object> map = new HashMap<String, Object>();
        if (isProAdmin) {

            num = stationEquipmentService.selectList3num(stationEquipment);
            map.put("groupnum", num.longValue());
        } else if (isCityAdmin) {
            String company = user
                    .getCompanies()
                    .get(0)
                    .getId();
            String comy = stationEquipmentService.selCity(company);
            stationEquipment.setCity(comy);

            num = stationEquipmentService.selectList3num(stationEquipment);
            map.put("groupnum", num.longValue());
        } else {
            String company = user
                    .getCompanies()
                    .get(0)
                    .getId();
            String comy = stationEquipmentService.selCity(company);
            stationEquipment.setCity(comy);
            num = stationEquipmentService.selectList3num(stationEquipment);
            map.put("groupnum", num.longValue());
        }

        return map;
    }

    /**
     * 以 站址 为纬度 进行查询，依据 过往稽核数据
     *
     * @param stationEquipment
     * @return
     */
    @RequestMapping("/list1")
    @ResponseBody
    public TableDataInfo list1(TowerStationEquipment2 stationEquipment) {
        startPage();
        List<TowerStationEquipment2> list = stationEquipmentService.selectList1(stationEquipment);
        list = list
                .stream()
                .map(towerStationEquipment2 -> {
                    //set 实际功率
                    towerStationEquipment2.setCalc(stationEquipmentService.calcActual(towerStationEquipment2));
                    //set 标准功率
                    towerStationEquipment2.setStandard(stationEquipmentService.caclStandard(towerStationEquipment2));

                    BigDecimal standard = towerStationEquipment2.getStandard();
                    double v = 0;
                    if (standard != null && (standard.intValue() != 0)) {
                        v = (towerStationEquipment2
                                .getCalc()
                                .subtract(standard)
                                .divide(standard, 4)).doubleValue();
                    }
                    towerStationEquipment2.setDeviate(v * 100);

                    return towerStationEquipment2;
                })
                .collect(Collectors.toList());
        return getDataTable(list);
    }

    /**
     * 按月份 依据集团下发 站址数据，生成对应 站址评价信息
     *
     * @param id
     * @return
     */
    @GetMapping("/evaluateForMonth")
    public AjaxResult evaluateForMonth(TowerStationEquipment2 stationEquipment) {

        List<TowerStationEquipment2> list = stationEquipmentService.selectList1(stationEquipment);
        if (CollectionUtils.isEmpty(list)) {
            return AjaxResult.success("当前月份无数据");
        }

        stationEquipmentService.batchSql(list, 1000);


        return AjaxResult.success(String.format("%d 月份站址评价 数据 已生成，请注意查看", stationEquipment
                .getMonthaccount()
                .getMonth()
                .getValue()));
    }


    /**
     * 新增铁塔站址设备
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存铁塔站址设备
     */
    @RequiresPermissions("business:stationEquipment:add")
    @Log(title = "铁塔站址设备", action = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody StationEquipment stationEquipment) {
        return toAjax(stationEquipmentService.insert(stationEquipment));
    }

    /**
     * 修改铁塔站址设备
     */
    @GetMapping("/edit/{id}")
    public AjaxResult edit(@PathVariable("id") Long id) {
        StationEquipment stationEquipment = stationEquipmentService.get(id);

        Object object = JSONObject.toJSON(stationEquipment);

        return this.success(object);
    }

    /**
     * 修改保存铁塔站址设备
     */
    @RequiresPermissions("business:stationEquipment:edit")
    @Log(title = "铁塔站址设备", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody StationEquipment stationEquipment) {
        return toAjax(stationEquipmentService.update(stationEquipment));
    }

    /**
     * 删除铁塔站址设备
     */
    @RequiresPermissions("business:stationEquipment:remove")
    @Log(title = "铁塔站址设备", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(stationEquipmentService.deleteByIds(Convert.toStrArray(ids)));
    }


    /**
     * 查看铁塔站址设备
     */
    @RequiresPermissions("business:stationEquipment:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id) {
        StationEquipment stationEquipment = stationEquipmentService.get(id);

        Object object = JSONObject.toJSON(stationEquipment);

        return this.success(object);


    }

    /**
     * @param id 根据报账单号 查询 最近的一条 站址相关信息
     * @return
     */
    @GetMapping("newest/byMsId")
    public AjaxResult newestStation(@RequestParam("id") Long id) {
        TowerStationEquipment2 stationEquipment = stationEquipmentService.getNewestStation(id);
        return this.success(stationEquipment);


    }

    /**
     * @param id 报账时，根据报账信息 生成 站址评级 信息，并插入
     * @return
     */
    @PostMapping("/generateGrade")
    public AjaxResult generateGrade(@RequestBody MssAccountbill mssAccountbill) {
        String result = stationEquipmentService.generateGrade(mssAccountbill);
        return this.success(result);

    }

}
