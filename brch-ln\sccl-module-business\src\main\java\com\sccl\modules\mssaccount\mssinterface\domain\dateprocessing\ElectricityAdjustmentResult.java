package com.sccl.modules.mssaccount.mssinterface.domain.dateprocessing;

import com.sccl.modules.mssaccount.mssinterface.domain.CollectMeter;
import java.math.BigDecimal;
import java.util.List;
import java.util.Collections;

/**
 * 电量调整结果
 * 包含调整后的CollectMeter数据和调整统计信息
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
public class ElectricityAdjustmentResult {
    
    /**
     * 调整后的CollectMeter数据列表
     */
    private final List<CollectMeter> adjustedCollectMeterList;
    
    /**
     * 原始电量总和
     */
    private final BigDecimal originalTotalElectricity;
    
    /**
     * 目标电量（业务电量）
     */
    private final BigDecimal targetElectricity;
    
    /**
     * 调整后的电量总和
     */
    private final BigDecimal adjustedTotalElectricity;
    
    /**
     * 调整的电量差值（正数表示增加，负数表示减少）
     */
    private final BigDecimal adjustmentAmount;
    
    /**
     * 有效调整天数
     */
    private final int adjustedDaysCount;
    
    /**
     * 是否需要调整
     */
    private final boolean adjustmentNeeded;
    
    /**
     * 构造函数
     * 
     * @param adjustedCollectMeterList 调整后的数据列表
     * @param originalTotalElectricity 原始电量总和
     * @param targetElectricity 目标电量
     * @param adjustedTotalElectricity 调整后电量总和
     * @param adjustmentAmount 调整电量差值
     * @param adjustedDaysCount 调整天数
     * @param adjustmentNeeded 是否需要调整
     */
    public ElectricityAdjustmentResult(List<CollectMeter> adjustedCollectMeterList,
                                     BigDecimal originalTotalElectricity,
                                     BigDecimal targetElectricity,
                                     BigDecimal adjustedTotalElectricity,
                                     BigDecimal adjustmentAmount,
                                     int adjustedDaysCount,
                                     boolean adjustmentNeeded) {
        if (adjustedCollectMeterList == null) {
            throw new IllegalArgumentException("调整后的数据列表不能为空");
        }
        if (originalTotalElectricity == null || targetElectricity == null || 
            adjustedTotalElectricity == null || adjustmentAmount == null) {
            throw new IllegalArgumentException("电量参数不能为空");
        }
        if (adjustedDaysCount < 0) {
            throw new IllegalArgumentException("调整天数不能为负数");
        }
        
        this.adjustedCollectMeterList = Collections.unmodifiableList(adjustedCollectMeterList);
        this.originalTotalElectricity = originalTotalElectricity;
        this.targetElectricity = targetElectricity;
        this.adjustedTotalElectricity = adjustedTotalElectricity;
        this.adjustmentAmount = adjustmentAmount;
        this.adjustedDaysCount = adjustedDaysCount;
        this.adjustmentNeeded = adjustmentNeeded;
    }
    
    /**
     * 获取调整后的CollectMeter数据列表
     * 
     * @return 调整后的数据列表（不可修改）
     */
    public List<CollectMeter> getAdjustedCollectMeterList() {
        return adjustedCollectMeterList;
    }
    
    /**
     * 获取原始电量总和
     * 
     * @return 原始电量总和
     */
    public BigDecimal getOriginalTotalElectricity() {
        return originalTotalElectricity;
    }
    
    /**
     * 获取目标电量
     * 
     * @return 目标电量
     */
    public BigDecimal getTargetElectricity() {
        return targetElectricity;
    }
    
    /**
     * 获取调整后的电量总和
     * 
     * @return 调整后的电量总和
     */
    public BigDecimal getAdjustedTotalElectricity() {
        return adjustedTotalElectricity;
    }
    
    /**
     * 获取调整的电量差值
     * 
     * @return 调整电量差值
     */
    public BigDecimal getAdjustmentAmount() {
        return adjustmentAmount;
    }
    
    /**
     * 获取有效调整天数
     * 
     * @return 调整天数
     */
    public int getAdjustedDaysCount() {
        return adjustedDaysCount;
    }
    
    /**
     * 判断是否需要调整
     * 
     * @return true如果需要调整，false否则
     */
    public boolean isAdjustmentNeeded() {
        return adjustmentNeeded;
    }
    
    /**
     * 获取数据条数
     * 
     * @return 数据条数
     */
    public int getDataCount() {
        return adjustedCollectMeterList.size();
    }
    
    /**
     * 判断是否有数据
     * 
     * @return true如果有数据，false否则
     */
    public boolean hasData() {
        return !adjustedCollectMeterList.isEmpty();
    }
    
    /**
     * 获取调整摘要信息
     * 
     * @return 调整摘要
     */
    public String getSummary() {
        if (!adjustmentNeeded) {
            return String.format("无需调整：原始电量=%.2f，目标电量=%.2f，生成数据=%d条",
                    originalTotalElectricity.doubleValue(),
                    targetElectricity.doubleValue(),
                    getDataCount());
        }
        
        String adjustmentType = adjustmentAmount.compareTo(BigDecimal.ZERO) > 0 ? "增加" : "减少";
        return String.format("调整完成：原始电量=%.2f，目标电量=%.2f，调整后电量=%.2f，%s电量=%.2f，调整天数=%d，生成数据=%d条",
                originalTotalElectricity.doubleValue(),
                targetElectricity.doubleValue(),
                adjustedTotalElectricity.doubleValue(),
                adjustmentType,
                adjustmentAmount.abs().doubleValue(),
                adjustedDaysCount,
                getDataCount());
    }
    
    @Override
    public String toString() {
        return String.format("ElectricityAdjustmentResult{dataCount=%d, originalTotal=%.2f, target=%.2f, adjustedTotal=%.2f, adjustment=%.2f, adjustedDays=%d, needed=%s}",
                getDataCount(),
                originalTotalElectricity.doubleValue(),
                targetElectricity.doubleValue(),
                adjustedTotalElectricity.doubleValue(),
                adjustmentAmount.doubleValue(),
                adjustedDaysCount,
                adjustmentNeeded);
    }
}
