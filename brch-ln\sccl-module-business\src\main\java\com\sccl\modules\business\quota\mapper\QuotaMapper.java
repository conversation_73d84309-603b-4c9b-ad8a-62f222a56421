package com.sccl.modules.business.quota.mapper;

import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.quota.domain.Quota;
import com.sccl.modules.business.quota.domain.QuotaBaseResult;
import com.sccl.modules.business.quota.domain.QuotaCondition;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 定额 数据层
 * 
 * <AUTHOR>
 * @date 2019-05-13
 */
public interface QuotaMapper extends BaseMapper<Quota>
{

    /**
     * 定额列表
     * 根据条件查询数据
     * @param quotaCondition 查询条件
     * @return
     */
    public List<QuotaBaseResult> selectListBySearch(QuotaCondition quotaCondition);
    /**
     * 定额编辑
     * 根据id查询数据
     * @param id 查询条件
     * @return
     */
    public QuotaBaseResult getById(Long id);

    BigDecimal selectByMonth(Map<String,Object> map);
    public List<QuotaBaseResult> checkAmmProByQuota(Quota quota);

}

