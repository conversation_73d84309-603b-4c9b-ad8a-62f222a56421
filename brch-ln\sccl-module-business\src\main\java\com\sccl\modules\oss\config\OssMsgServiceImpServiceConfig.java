package com.sccl.modules.oss.config;

import com.enrising.dcarbon.manage.event.*;
import com.sccl.modules.oss.listener.*;
import com.sccl.modules.oss.service.impl.OssMsgServiceImp;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * ServiceOssMsgServiceImp的相关监听器配置
 *
 * <AUTHOR> Ge
 * @date 2023-06-29
 */
@Configuration
public class OssMsgServiceImpServiceConfig {
    @Bean
    public OssMsgServiceImp configOssMsgServiceImp() {
        OssMsgServiceImp serviceImp = new OssMsgServiceImp();
        serviceImp
                .getWebEventManger()
                .addListener(AddOneHandleEvent.class, new OssMsgEntityAddOneHandleEventListener())
                .addListener(AddListHandleEvent.class, new OssMsgEntityAddListHandleEventListener())
                .addListener(UpdateByEntityHandleEvent.class, new OssMsgEntityUpdateByEntityHandleEventListener())
                .addListener(UpdateByMapHandleEvent.class, new OssMsgEntityUpdateByMapHandleEventListener())
                .addListener(DelByEntityHandleEvent.class, new OssMsgEntityDelByEntityHandleEventListener())
                .addListener(DelByIdHandleEvent.class, new OssMsgEntityDelByIdHandleEventListener())
                .addListener(DelByMapHandleEvent.class, new OssMsgEntityDelByMapHandleEventListener())
                .addListener(DelLogicByEntityHandleEvent.class, new OssMsgEntityDelLogicByEntityHandleEventListener())
                .addListener(DelLogicByIdHandleEvent.class, new OssMsgEntityDelLogicByIdHandleEventListener())
                .addListener(DelLogicByMapHandleEvent.class, new OssMsgEntityDelLogicByMapHandleEventListener());
        return serviceImp;
    }
}