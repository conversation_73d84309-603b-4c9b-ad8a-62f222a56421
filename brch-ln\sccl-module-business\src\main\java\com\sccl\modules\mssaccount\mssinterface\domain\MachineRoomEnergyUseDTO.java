package com.sccl.modules.mssaccount.mssinterface.domain;

import com.enrising.dcarbon.collector.CollectedDatasource;
import com.sccl.modules.autojob.util.convert.StringUtils;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 机房用能传输对象
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-09-14 14:31
 * @email <EMAIL>
 */
@Getter
@Setter
public class MachineRoomEnergyUseDTO implements CollectedDatasource {
    private String subjectCode;
    private String msgId;
    private List<MachineRoomEnergyUseEntity> roomDatas;

    public MachineRoomEnergyUseDTO(String city, List<MachineRoomEnergyUseEntity> roomDatas) {
        this.subjectCode = ReportingSubjectCodes.getSubjectCode(city);
        this.msgId = subjectCode + System.currentTimeMillis() + StringUtils.getRandomStr(7);
        this.roomDatas = roomDatas;
    }

    public MachineRoomEnergyUseDTO() {
    }

    @Override
    public String getAuditKey() {
        return msgId;
    }

    @Override
    public boolean isAvailable() {
        return true;
    }
}
