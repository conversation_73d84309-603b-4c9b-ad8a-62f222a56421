package com.sccl.modules.mssaccount.mssinterface.domain;

import lombok.Getter;
import lombok.Setter;

/**
 * 机房用能实体
 */
@Getter
@Setter
public class StationEnergyUseEntity {
    /**
     * 用能月份，实际产生用能的时间。格式：YYYYMM。必填项。
     */
    private String statisDay;

    /**
     * 省级编码，双碳组织提供，见《附件二：双碳组织编码》。必填项。
     */
    private String provinceCode;

    /**
     * 市局组织编码，双碳组织提供，见《附件二：双碳组织编码》。必填项。
     */
    private String cityCode;

    /**
     * 市局组织名称，双碳组织提供，见《附件二：双碳组织编码》。必填项。
     */
    private String cityName;

    /**
     * 局站编码。必填项。
     */
    private String stationCode;

    /**
     * 局站名称，例如：**路**号**楼**机楼。必填项。
     */
    private String stationName;

    /**
     * 投产运行时间，格式：YYYYMM。必填项。
     */
    private String runningDate;

    /**
     * 上架率，=实际使用机架数/总机架数，百分比，保留小数点后2位。必填项。
     */
    private String idcutilization;

    /**
     * 数据中心设计PUE值，取小数点后2位。必填项。
     */
    private String idcdesignPUE;

    /**
     * 局站类型，按照集团网运部要求的四大级四大类划分，不能超出枚举值范围，必须为树形结构末端的枚举项。具体参考附件三：基础数据规范表。必填项。
     */
    private String stationType;

    /**
     * 规模，局站类型为“数据中心”时必填，枚举值：1=中小型、2=大型、3=超大型。必填项。
     */
    private String scale;

    /**
     * 局站地址，例如：**路**号**大楼。必填项。
     */
    private String stationLocation;

    /**
     * 标准机架数，局站内的标准机架总数。单位：个。必填项。
     */
    private String frameTotal;

    /**
     * IT设备数，局站内IT设备总数。单位：个。必填项。
     */
    private String produceDeviceTotal;

    /**
     * 空调设备数，局站内空调设备总数。单位：个。非必填项。
     */
    private String airDeviceTotal;

    /**
     * 动力设备数，局站内动力设备总数。单位：个。非必填项。
     */
    private String motiveDeviceTotal;

    /**
     * 照明及其他设备数，局站内照明及其他设备总数。单位：个。非必填项。
     */
    private String otherDeviceTotal;

    /**
     * 总能耗，局站内总能耗累计数值。单位：千瓦（kwh）。如无数据，则传0。必填项。
     */
    private String energyTotal;

    /**
     * IT能耗，局站内IT设备所用能耗的累计数值。单位：千瓦（kwh）。如无数据，则传0。必填项。
     */
    private String itEnergyTotal;

    /**
     * 空调能耗，局站内空调系统设备所用能耗的累计数值。单位：千瓦（kwh）。非必填项。
     */
    private String airEnergyTotal;

    /**
     * 动力能耗，局站内动力设备所用能耗的累计数值。单位：千瓦（kwh）。非必填项。
     */
    private String motiveEnergyTotal;

    /**
     * PUE值，推送局站实际PUE值。必填项。
     */
    private String pue;

    /**
     * WUE值，WUE=局站用水量/机楼IT设备用电量。单位：L/kwh。如无数据，则传0。必填项。
     */
    private String wue;

    /**
     * 煤炭消耗量，单位：吨。非必填项。
     */
    private String coal;

    /**
     * 发电用煤消耗量，单位：吨。非必填项。
     */
    private String electricCoal;

    /**
     * 焦炭消耗量，单位：吨。非必填项。
     */
    private String cokeConsumption;

    /**
     * 原油消耗量，单位：吨。非必填项。
     */
    private String oilConsumption;

    /**
     * 固定源-汽油消耗量，单位：升。非必填项。
     */
    private String gasolineConsumption;

    /**
     * 移动源-汽油消耗量，单位：升。非必填项。
     */
    private String carGasolineConsumption;

    /**
     * 煤油消耗量，单位：升。非必填项。
     */
    private String keroseneConsumption;

    /**
     * 液化石油气消耗量，单位：吨。非必填项。
     */
    private String lngConsumption;

    /**
     * 固定源-柴油消耗量，生产用途柴油消耗量。单位：升。非必填项。
     */
    private String dieselOilConsumption;

    /**
     * 移动源-柴油消耗量，移动源-柴油消耗量。单位：升。非必填项。
     */
    private String carDieselOilConsumption;

    /**
     * 燃料油消耗量，单位：升。非必填项。
     */
    private String fuelOilConsumption;

    /**
     * 天然气消耗量，单位：立方米。非必填项。
     */
    private String naturalGasConsumption;

    /**
     * 热力，单位：十亿焦。非必填项。
     */
    private String heatingPowerConsumption;

    /**
     * 其他能源，单位：吨标准煤。非必填项。
     */
    private String otherEnergyConsumption;

    /**
     * 新水用量，单位：吨。非必填项。
     */
    private String waterConsumption;

    /**
     * 节能量，单位：吨标准煤。非必填项。
     */
    private String conservationEnergyConsumption;

    /**
     * 节能电量，单位：千瓦时。非必填项。
     */
    private String conservationPowerConsumption;

    /**
     * 是否AI节能，枚举值：
     * 1=是
     * 2=否
     * 必填项。
     */
    private String aiConservation;
}

