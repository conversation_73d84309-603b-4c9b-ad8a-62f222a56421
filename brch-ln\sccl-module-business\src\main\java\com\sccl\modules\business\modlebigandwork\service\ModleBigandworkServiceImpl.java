package com.sccl.modules.business.modlebigandwork.service;

import com.sccl.common.lang.StringUtils;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.business.modlebigandwork.domain.ModleBigandwork;
import com.sccl.modules.business.modlebigandwork.mapper.ModleBigandworkMapper;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


/**
 * 单价-大工业-办公  服务层实现
 *
 * <AUTHOR>
 * @date 2023-03-13
 */
@Service
public class ModleBigandworkServiceImpl extends BaseServiceImpl<ModleBigandwork> implements IModleBigandworkService {
    @Autowired
    private ModleBigandworkMapper mapper;

    @Override
    public List<ModleBigandwork> importExcel(String sheetName, InputStream input) {


        List<ModleBigandwork> list = new ArrayList<>();

        Workbook workbook = null;
        try {
            workbook = WorkbookFactory.create(input);
        } catch (IOException e) {
            e.printStackTrace();
        } catch (InvalidFormatException e) {
            e.printStackTrace();
        }
        Sheet sheet = workbook.getSheet(sheetName);
        if (StringUtils.isNotEmpty(sheetName)) {
            // 如果指定sheet名,则取指定sheet中的内容.
            sheet = workbook.getSheet(sheetName);
        }
        if (sheet == null) {
            // 如果传入的sheet名不存在则默认指向第1个sheet.
            sheet = workbook.getSheetAt(0);
        }
        int rows = sheet.getPhysicalNumberOfRows();

        if (rows > 0) {
            Row row0 = sheet.getRow(0);
            for (int i = 1; i < rows; i++) {
                ModleBigandwork tower = new ModleBigandwork();
                Row rowo = sheet.getRow(0);
                Row row = sheet.getRow(i);
                int cellNum = sheet.getRow(0).getPhysicalNumberOfCells();
                for (int j = 0; j < cellNum; j++) {
                    Cell cello = rowo.getCell(j);
                    Cell cell = row.getCell(j);
                    if (cell == null) {
                        continue;
                    } else {
                        if ("月份".equals(cello.getStringCellValue())) {
                            tower.setMonth(String.valueOf(cell.getNumericCellValue()));
                        }
                        if ("地市编码".equals(cello.getStringCellValue())) {
                            String city = String.valueOf(cell.getNumericCellValue());
//                            city = city.replaceAll("市", "");
                            tower.setOrgcode(city);
                        }
                        if ("户号".equals(cello.getStringCellValue())) {
                            tower.setAccountno(String.valueOf(cell.getNumericCellValue()));
                        }
                        if ("变压器容量".equals(cello.getStringCellValue())) {
                            tower.setCapacitydemandbig1(new BigDecimal(String.valueOf(cell.getNumericCellValue())));
                        }
                        if ("当月变压器最大需量".equals(cello.getStringCellValue())) {
                            tower.setCapacitydemandbig2(new BigDecimal(String.valueOf(cell.getNumericCellValue())));
                        }
//                        if ("变压器计算方式".equals(cello.getStringCellValue())) {
//                            String stringCellValue = String.valueOf(cell.getNumericCellValue());
//                            tower.setBigflag(stringCellValue == null ? null : Integer.valueOf(stringCellValue));
//                        }
                    }
                }
//                if (!StringUtils.isEmpty(tower.getStationaddrcode()) && !StringUtils.isEmpty(tower.getCity()) && !StringUtils.isEmpty(tower.getStationaddrname()) && !StringUtils.isEmpty(tower.getAddress())) {
//                    if (StringUtils.isEmpty(tower.getProvice())) {
//                        if ("sc".equals(deployTo)) {
//                            tower.setProvice("四川省");
//                        } else if ("ln".equals(deployTo)) {
//                            tower.setProvice("辽宁省");
//                        }
//                    }
                    list.add(tower);
//                }
            }
        }

        return list;

    }

    @Override
    public int insertListForTemporary(List newList) {
        return mapper.insertListFortemporary(newList);
    }

    @Override
    public void deleteRepeat() {
        mapper.deleteRepeat();
    }

    @Override
    public int insertInfo() {
        return mapper.insertInfo();
    }

    @Override
    public String updatebitch(List<ModleBigandwork> modleBigandworks) {
        int n = mapper.updatebitch(modleBigandworks);
        return String.format("更新成功，更新了%d条", n);
    }
}
