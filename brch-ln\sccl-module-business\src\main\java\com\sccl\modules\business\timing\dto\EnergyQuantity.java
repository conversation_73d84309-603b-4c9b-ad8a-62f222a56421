package com.sccl.modules.business.timing.dto;

import com.sccl.timing.finder.framework.CacheData;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-03-07 13:40
 * @email <EMAIL>
 */
@Getter
@Setter
@ToString
public class EnergyQuantity implements CacheData {
    private String date;

    private Float _4g;

    private Float _5g;

    public EnergyQuantity(String date, Float _4g, Float _5g) {
        this.date = date;
        this._4g = _4g;
        this._5g = _5g;
    }

    public EnergyQuantity() {
    }
}
