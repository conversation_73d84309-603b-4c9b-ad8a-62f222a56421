package com.sccl.modules.mssaccount.mssconstractmain.controller;

import com.alibaba.fastjson.JSONObject;
import com.sccl.common.support.Convert;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.autojob.util.convert.StringUtils;
import com.sccl.modules.business.powermodel.entity.HistoryContractPricePro;
import com.sccl.modules.mssaccount.mssconstractmain.domain.HistoryContractPrice;
import com.sccl.modules.mssaccount.mssconstractmain.domain.MssConstractmain;
import com.sccl.modules.mssaccount.mssconstractmain.service.IMssConstractmainService;
import com.sccl.modules.system.role.domain.Role;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.service.IUserService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 合同 信息操作处理
 *
 * <AUTHOR>
 * @date 2019-05-01
 */
@Component("MssConstractmainControllerPlus")
@RestController
@RequestMapping("/mssaccount/mssConstractmain")
public class MssConstractmainController extends BaseController {
    private String prefix = "mssaccount/mssConstractmain";
    @Autowired
    private IUserService userService;
    @Autowired
    private IMssConstractmainService mssConstractmainService;

    @RequiresPermissions("mssaccount:mssConstractmain:view")
    @GetMapping()
    public String mssConstractmain() {
        return prefix + "/mssConstractmain";
    }

    /**
     * 查询合同列表
     */
    @RequiresPermissions("mssaccount:mssConstractmain:list")
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(MssConstractmain mssConstractmain) {
        mssConstractmain.setInfstatus("1");
        startPage();
        List<MssConstractmain> list = mssConstractmainService.selectByLike(mssConstractmain);
        return getDataTable(list);
    }

    /**
     * 新增合同
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存合同
     */
    @RequiresPermissions("mssaccount:mssConstractmain:add")
    //@Log(title = "合同", action = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody MssConstractmain mssConstractmain) {
        return toAjax(mssConstractmainService.insert(mssConstractmain));
    }

    /**
     * 修改合同
     */
    @GetMapping("/edit/{infid}")
    public AjaxResult edit(@PathVariable("infid") Long infid) {
        MssConstractmain mssConstractmain = mssConstractmainService.get(infid);

        Object object = JSONObject.toJSON(mssConstractmain);

        return this.success(object);
    }

    /**
     * 修改保存合同
     */
    @RequiresPermissions("mssaccount:mssConstractmain:edit")
    //@Log(title = "合同", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody MssConstractmain mssConstractmain) {
        return toAjax(mssConstractmainService.update(mssConstractmain));
    }

    /**
     * 删除合同
     */
    @RequiresPermissions("mssaccount:mssConstractmain:remove")
    //@Log(title = "合同", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(mssConstractmainService.deleteByIds(Convert.toStrArray(ids)));
    }


    /**
     * 查看合同
     */
    @RequiresPermissions("mssaccount:mssConstractmain:view")
    @GetMapping("/view/{infid}")
    @ResponseBody
    public AjaxResult view(@PathVariable("infid") Long infid) {
        MssConstractmain mssConstractmain = mssConstractmainService.get(infid);

        Object object = JSONObject.toJSON(mssConstractmain);

        return this.success(object);
    }

    /**
     * 根据转供电合同编号获取对应电费电价
     *
     * @param contractcode
     * @return
     */
    @GetMapping("/getContractPrice")
    public AjaxResult selectContractPrice(
      @RequestParam(value = "contractcode", required = false) String contractcode,
      @RequestParam(value = "ammeterid", required = false) String ammeterid
    ) {
        String price = "0";
        if (StringUtils.isNotBlank(contractcode)) {
            price = mssConstractmainService.selectContractPrice(contractcode);
        } else if (StringUtils.isNotBlank(ammeterid)) {
            price = mssConstractmainService.selectContractPricebyammeterid(ammeterid);
        }
        return AjaxResult.success(price);
    }

    @GetMapping("/getHistoryContractPrice")
    public TableDataInfo selectHistoryContractPrice(HistoryContractPrice historyContractPrice) {
        List<HistoryContractPrice> list = getHistoryContractPriceList(historyContractPrice);
        return getDataTable(list);
    }
    @GetMapping("/getHistoryContractPricePro")
    public TableDataInfo selectHistoryContractPricePro(HistoryContractPricePro historyContractPrice) {
        List<HistoryContractPricePro> list = getHistoryContractPriceListPro(historyContractPrice);
        return getDataTable(list);
    }

    private List<HistoryContractPrice> getHistoryContractPriceList(HistoryContractPrice historyContractPrice) {
        if (historyContractPrice == null) {
            historyContractPrice = new HistoryContractPrice();
        }

        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("PROVI_") || role.getCode().equals("admin")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
            if (role.getCode().startsWith("SUB_")) {//县能耗费管理员
                isSubAdmin = true;
            }
        }
        //  权限设置
        if (isProAdmin) {

        } else if (isCityAdmin) {
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0) {
                historyContractPrice.setCompany(companies.get(0).getId());
            }
        } else if (isSubAdmin) {
            List<IdNameVO> departments = user.getDepartments();
            if (departments != null && departments.size() > 0)
                historyContractPrice.setCountry(departments.get(0).getId());
        }
        startPage();
        List<HistoryContractPrice> list = mssConstractmainService.selectHistoryContractPrice(historyContractPrice);
        return list;
    }
    private List<HistoryContractPricePro> getHistoryContractPriceListPro(HistoryContractPricePro historyContractPrice) {
        if (historyContractPrice == null) {
            historyContractPrice = new HistoryContractPricePro();
        }

        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("PROVI_") || role.getCode().equals("admin")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
            if (role.getCode().startsWith("SUB_")) {//县能耗费管理员
                isSubAdmin = true;
            }
        }
        //  权限设置
        if (isProAdmin) {

        } else if (isCityAdmin) {
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0) {
                historyContractPrice.setCompany(companies.get(0).getId());
            }
        } else if (isSubAdmin) {
            List<IdNameVO> departments = user.getDepartments();
            if (departments != null && departments.size() > 0)
                historyContractPrice.setCountry(departments.get(0).getId());
        }
        startPage();
        List<HistoryContractPricePro> list = mssConstractmainService.selectHistoryContractPricePro(historyContractPrice);
        return list;
    }


}
