package com.sccl.modules.rental.rentalsupplycheckdetal.mapper;

import com.sccl.modules.rental.rentalsupplycheckdetal.domain.Rentalsupplycheckdetal;
import com.sccl.framework.mapper.BaseMapper;

import java.util.List;
import java.util.Map;

/**
 * 供应商 检验 数据层
 * 
 * <AUTHOR>
 * @date 2019-09-06
 */
public interface RentalsupplycheckdetalMapper extends BaseMapper<Rentalsupplycheckdetal>
{
    int deleteByRemids(String[] remids);

    /**
     * @Description: 查询展示页明细
     * @author: dongk
     * @date: 2019/9/7
     * @param:
     * @return:
     */
    List<Map<String,Object>> selectDetailed(Rentalsupplycheckdetal rentalsupplycheckdetal);
	
}