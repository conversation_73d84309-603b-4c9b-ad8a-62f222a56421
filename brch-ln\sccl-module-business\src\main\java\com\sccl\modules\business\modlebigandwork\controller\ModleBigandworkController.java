package com.sccl.modules.business.modlebigandwork.controller;

import com.alibaba.fastjson.JSONObject;
import com.sccl.common.lang.StringUtils;
import com.sccl.common.support.Convert;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.modlebigandwork.domain.ModleBigandwork;
import com.sccl.modules.business.modlebigandwork.service.IModleBigandworkService;
import com.sccl.modules.system.attachments.domain.UpLoadFile;
import com.sccl.modules.system.role.domain.Role;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.service.IUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.counting;
import static java.util.stream.Collectors.groupingBy;

/**
 * 单价-大工业-办公  信息操作处理
 *
 * <AUTHOR>
 * @date 2023-03-13
 */
@RestController
@Slf4j
@RequestMapping("/business/modleBigandwork")
//@RequestMapping("/business/temporarytower")
public class ModleBigandworkController extends BaseController {
    private String prefix = "business/modleBigandwork";
    @Autowired
    private IUserService userService;
    @Autowired
    private IModleBigandworkService modleBigandworkService;


    @RequiresPermissions("business:modleBigandwork:view")
    @GetMapping()
    public String modleBigandwork() {
        return prefix + "/modleBigandwork";
    }

    /**
     * 查询单价-大工业-办公 列表
     */
    @RequiresPermissions("business:modleBigandwork:list")
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(ModleBigandwork modleBigandwork) {
        //权限判定 组装
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("PROVI_")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
        }
        if (isProAdmin) {
            modleBigandwork = null;
            //  查询权限设置 分公司
        } else if (isCityAdmin) {
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0)
                modleBigandwork.setOrgcode(companies.get(0).getId());
        }

        startPage();
        List<ModleBigandwork> list = modleBigandworkService.selectList(modleBigandwork);
        return getDataTable(list);
    }

    /**
     * 新增单价-大工业-办公
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存单价-大工业-办公
     */
    @PostMapping("/addList")
    @ResponseBody
    public AjaxResult addSave(@RequestBody List<ModleBigandwork> modleBigandworks) {
        //权限判定 组装
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("PROVI_")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
        }
        if (isProAdmin) {
            modleBigandworks.forEach(
                    init -> init.setOrgcode("-1")
            );
            //  查询权限设置 分公司
        } else if (isCityAdmin) {
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0) {
                modleBigandworks.forEach(
                        init -> init.setOrgcode(companies.get(0).getId())
                );
            }
        }
        return toAjax(modleBigandworkService.insertList(modleBigandworks));
    }
    @PostMapping("/updatebitch")
    @ResponseBody
    public AjaxResult updatebitch(@RequestBody List<ModleBigandwork> modleBigandworks) {
        return AjaxResult.success(modleBigandworkService.updatebitch(modleBigandworks));
    }

    @PostMapping(value = "/uploadExcel")
    @ResponseBody
    public Map<String, Object> uploadExcel(HttpServletRequest request, HttpServletResponse response,
                                           UpLoadFile uploadFile)
            throws Exception {
        response.setContentType("text/html;charset=utf-8");
        Map<String, Object> map = new HashMap<>();
        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        Iterator<String> iterator = multiRequest.getFileNames();
        String str = "";
        List<ModleBigandwork> list = new ArrayList<>();
        while (iterator.hasNext()) {
            String name = iterator.next();
            List<MultipartFile> files = new LinkedList<>();
            files = multiRequest.getFiles(name);
            // 一次遍历所有文件
            for (MultipartFile file : files) {
                if (file == null) {
                    continue;
                }

                //如果文件大小为0则不上传
                long fileSize = file.getSize();
                if (fileSize <= 0L) {
                    throw new Exception("文件名称：【" + file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("\\") + 1, file.getOriginalFilename().length()) + "】的文件大小为0,请核对!");
                }
                list = modleBigandworkService.importExcel("sheet1", file.getInputStream());

                //校验
                //1.户号不能为空
                boolean emptyFlag = list.stream().anyMatch(modleBigandwork -> modleBigandwork.getAccountno() == null);
                if (emptyFlag) {
                    map.put("str", "导入数据存在户号为空的数据，请重新核查");
                    return map;
                }
                //2.户号不能重复
                boolean repeatFlag = list.stream().collect(groupingBy(
                        ModleBigandwork::getAccountno,
                        counting()
                )).entrySet().stream().anyMatch(
                        e -> e.getValue() > 1
                );
                if (repeatFlag) {
                    map.put("str", "导入数据存在户号重复的数据，请重新核查");
                    return map;
                }
                //3.不属于本市的数据过滤
                List<IdNameVO> companies = ShiroUtils.getUser().getCompanies();
                if (CollectionUtils.isNotEmpty(companies)) {
                    String orgCode = companies.get(0).getId();
                    list = list.stream().filter(modleBigandwork -> orgCode.equals(modleBigandwork.getOrgcode())).collect(Collectors.toList());
                }

            }

        }


        if (list != null && list.size() > 0) {
            str += "成功解析【" + list.size() + "】条数据；";
            int listSize = list.size();
            int toIndex = 1000;
            for (int i = 0; i < list.size(); i += 1000) {
                //作用为toIndex最后没有1000条数据则剩余几条newList中就装几条
                if (i + 1000 > listSize) {
                    toIndex = listSize - i;
                }
                List newList = list.subList(i, i + toIndex);
                int n = modleBigandworkService.insertListForTemporary(newList);//保存到临时表
            }

            //加入到正式表
            int num = 0;
            num = modleBigandworkService.insertInfo();
            str += "成功加入【" + num + "】条数据到单价大工业表；";

            //删除临时表
            modleBigandworkService.deleteRepeat();
            map.put("str", str);
        }

        if (StringUtils.isEmpty(str)) {
            map.put("str", "导入失败");
        } else {
            map.put("str", str);
        }

        return map;
    }


    /**
     * 修改单价-大工业-办公
     */
    @GetMapping("/edit/{id}")
    public AjaxResult edit(@PathVariable("id") Long id) {
        ModleBigandwork modleBigandwork = modleBigandworkService.get(id);

        Object object = JSONObject.toJSON(modleBigandwork);

        return this.success(object);
    }

    /**
     * 修改保存单价-大工业-办公
     */
    @RequiresPermissions("business:modleBigandwork:edit")
    @Log(title = "单价-大工业-办公 ", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody ModleBigandwork modleBigandwork) {
        //权限判定 组装
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("PROVI_")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
        }
        if (!isCityAdmin && !isProAdmin) {
            return AjaxResult.success("操作无权限,只有市或省管理由此权限");
        }

        return toAjax(modleBigandworkService.update(modleBigandwork));
    }


    /**
     * 删除单价-大工业-办公
     */
    @RequiresPermissions("business:modleBigandwork:remove")
    @Log(title = "单价-大工业-办公 ", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(modleBigandworkService.deleteByIds(Convert.toStrArray(ids)));
    }


    /**
     * 查看单价-大工业-办公
     */
    @RequiresPermissions("business:modleBigandwork:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id) {
        ModleBigandwork modleBigandwork = modleBigandworkService.get(id);

        Object object = JSONObject.toJSON(modleBigandwork);

        return this.success(object);
    }


    public MultipartFile getMultipartFile(InputStream inputStream, String fileName) {
        FileItem fileItem = createFileItem(inputStream, fileName);
        //CommonsMultipartFile是feign对multipartFile的封装，但是要FileItem类对象
        return new CommonsMultipartFile(fileItem);
    }

    public FileItem createFileItem(InputStream inputStream, String fileName) {
        FileItemFactory factory = new DiskFileItemFactory(16, null);
        String textFieldName = "file";
        FileItem item = factory.createItem(textFieldName, MediaType.MULTIPART_FORM_DATA_VALUE, true, fileName);
        int bytesRead = 0;
        byte[] buffer = new byte[10 * 1024 * 1024];
        OutputStream os = null;
        //使用输出流输出输入流的字节
        try {
            os = item.getOutputStream();
            while ((bytesRead = inputStream.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            inputStream.close();
        } catch (IOException e) {
            log.error("Stream copy exception", e);
            throw new IllegalArgumentException("文件上传失败");
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    log.error("Stream close exception", e);

                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("Stream close exception", e);
                }
            }
        }

        return item;
    }

}
