package com.sccl.modules.business.stationaudit.pstationchangesamemeter;

import com.enrising.dcarbon.audit.AbstractReferee;
import com.enrising.dcarbon.audit.Auditable;
import com.enrising.dcarbon.audit.RefereeDatasource;
import com.enrising.dcarbon.audit.RefereeResult;
import com.sccl.modules.business.stationaudit.msshistory.HistoryResult;
import com.sccl.modules.business.stationaudit.pstationaccountchange.StationAccountChangeRefereeContent;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class StationChangeSameMeterReferee extends AbstractReferee {
    @Override
    public RefereeResult doReferee(RefereeResult lastRefereeResult, Auditable auditable, Map<Class<?
                extends RefereeDatasource>, List<RefereeDatasource>> refereeDatasourceList) {
        //取出数据
        List<RefereeDatasource> list = refereeDatasourceList.get(StationChangeSameMeterRefereeContent.class)
                                                            .stream().filter(Objects::nonNull).collect(Collectors.toList());

        //放数据
        HistoryResult result = new HistoryResult();
        result.setTopic("站址变动");
        result.setList(list);
        return result;
    }


    public StationChangeSameMeterReferee(String refereeName) {
        super(refereeName);
    }

}
