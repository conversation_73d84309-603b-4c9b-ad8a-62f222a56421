package com.sccl.modules.ocr.controller;

import com.sccl.common.lang.StringUtils;
import com.sccl.common.utils.DateUtils;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.mssaccount.msssupplier.domain.MssSupplier;
import com.sccl.modules.mssaccount.msssupplier.service.IMssSupplierService;
import com.sccl.modules.ocr.service.IRapidOCRService;
import com.sccl.modules.system.attachments.domain.Attachments;
import com.sccl.modules.system.attachments.domain.UpLoadFile;
import com.sccl.modules.system.attachments.service.IAttachmentsService;
import com.sccl.modules.system.config.domain.Config;
import com.sccl.modules.system.config.service.IConfigService;
import com.sccl.modules.system.user.domain.User;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * RapidOCr进行OCR识别
 */
@Slf4j
@RestController
@RequestMapping("/rapid/ocr")
public class RapidOCRController {

    private static final Logger logger = LoggerFactory.getLogger(RapidOCRController.class);

    @Autowired
    private IRapidOCRService rapidOCRService;
    @Autowired
    private IAttachmentsService attachmentsService;
    @Autowired
    private IConfigService configService;
    @Autowired
    private IMssSupplierService mssSupplierService;

    /**
     * 执行发票上传 支持多张发票
     * （及自动识别供应商名称）
     * @param request    request对象
     * @param response   response对象
     * @param uploadFile 文件对象
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @Date 2025/4/16 10:33
     */
    @RequestMapping(value = "/uploadMultiFile")
    @ResponseBody
    public Map<String, Object> uploadMultiFile(HttpServletRequest request, HttpServletResponse response, UpLoadFile uploadFile)
            throws Exception {
        response.setContentType("text/html;charset=utf-8");

        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        String busiAlias = ServletRequestUtils.getStringParameter(request, "busiAlias", "");
        String busiId = ServletRequestUtils.getStringParameter(request, "busiId", "");
        String categoryCode = ServletRequestUtils.getStringParameter(request, "categoryCode", "");
        String areaCode = ServletRequestUtils.getStringParameter(request, "areaCode", "");

        //上传前先校验 每个文件不超过20M，总大小不超过50M
        //及自动识别供应商名称
        AjaxResult result = validBeforeUpLoad(multiRequest, busiId);
        if (!"0".equals(result.get("code").toString())) {
            return result;
        }

        //根据供应商名称获取供应商编码
        String supplier = result.get("data").toString();
        MssSupplier mssSupplier = mssSupplierService.selectOneByName(supplier);

        User user = ShiroUtils.getUser();
        //暂时用一个固定值测试分片
        uploadFile.setShardKey("SCCL");
        uploadFile.setCreatorId(user.getId());
        uploadFile.setCreatorName(user.getUserName());

        Long year = ServletRequestUtils.getLongParameter(request, "year", 0L);
        uploadFile.setBusiAliasCode(busiAlias);
        uploadFile.setCategoryCode(categoryCode);
        uploadFile.setBusiId(Long.valueOf(busiId));

        uploadFile.setAreaCode(areaCode);

        uploadFile.setYear(year.intValue());
        if (logger.isDebugEnabled()) {
            logger.debug("附件上传，busiId:{} ;busiAlias:{};categoryCode:{}", busiId, busiAlias, categoryCode);
        }
        String attachmentId = "";
        List<Object> retAttachments = new ArrayList<>();
        Iterator<String> iterator = multiRequest.getFileNames();
        while (iterator.hasNext()) {
            String name = iterator.next();
            List<MultipartFile> files = new LinkedList<>();
            files = multiRequest.getFiles(name);
            if (logger.isDebugEnabled()) {
                logger.debug("单个附件，files:{}", files);
            }
            // 一次遍历所有文件
            for (MultipartFile file : files) {
                if (file == null) {
                    continue;
                }
                String fileName = file.getOriginalFilename();
                fileName = fileName.toLowerCase();
                uploadFile.setFileName(fileName.substring(fileName.lastIndexOf("\\") + 1, fileName.length()));
                uploadFile.setFileSize(file.getSize());
                uploadFile.setInvoiceFlag("1");//发票标识 1 是 0 否（报账管理处用）
                uploadFile.setInvoiceSupplier(supplier);//发票供应商名称
                if (logger.isDebugEnabled()) {
                    logger.debug("time start:{} 文件传输(AttachmentsController.uploadMultiFile)", DateUtils.YYYY_MM_DD_HH_MM_SS);
                    logger.debug(file.getOriginalFilename());
                }
                //执行单文件的上传
                Attachments attachments = this.attachmentsService.upLoadFile(file, uploadFile);
                Map<String, String> map = new HashMap<>(4);
                attachmentId = String.valueOf(attachments.getId());
                map.put("id", attachmentId);
                map.put("fileName", fileName);
                retAttachments.add(map);
            }

        }
        Map<String, Object> map = new HashMap<>(8);
        map.put("success", "true");
        map.put("mssages", "上传文件成功");
        map.put("id", attachmentId);
        map.put("supplierExist", mssSupplier != null ? "1" : "0");//上传的发票的供应商在能耗系统是否存在 1 存在 0 不存在
        map.put("supplierName", supplier);                        //供应商名称
        map.put("supplierCode", mssSupplier != null ? mssSupplier.getLifnr() : "");//供应商编码
        map.put("rows", retAttachments);
        return map;
    }

    /**
     * 上传校验 默认每个文件不超过20M，总大小不超过50M,最多上传3个
     * @return
     */
    private AjaxResult validBeforeUpLoad(MultipartHttpServletRequest multiRequest, String busiId) {
        if(StringUtils.isBlank(busiId)){
            return AjaxResult.error("业务模块ID不能为空");
        }
        String msg = "";
        Long maxSize = 20L;     //附件上传-单个最大size 默认 20M
        Long maxTotalSize = 50L;//附件上传-总共最大size 默认 50M
        int maxCnt = 3;         //附件上传-最多上传个数  默认 3个
        //查询附件上传相关配置
        String[] configKeys = new String[]{"fjsc_cnt", "fjsc_size", "fjsc_total_size"};
        List<Config> configs = configService.getByConfigKeys(configKeys);
        if (configs != null && configs.size() > 0) {
            for (Config config : configs) {
                if (StringUtils.isNotBlank(config.getConfigValue())) {
                    if ("fjsc_cnt".equals(config.getConfigKey())) {
                        //附件上传-最多上传个数
                        maxCnt = Integer.valueOf(config.getConfigValue());
                    } else if ("fjsc_size".equals(config.getConfigKey())) {
                        //附件上传-单个最大size
                        maxSize = Long.valueOf(config.getConfigValue());
                    } else if ("fjsc_total_size".equals(config.getConfigKey())) {
                        //附件上传-单个最大size
                        maxTotalSize = Long.valueOf(config.getConfigValue());
                    }
                }
            }
        }
        Long totalSize = 0L;
        int cnt = 0;
        List<MultipartFile> mfiles = new ArrayList<>();
        Iterator<String> iterator = multiRequest.getFileNames();
        while (iterator.hasNext()) {
            String name = iterator.next();
            List<MultipartFile> files;
            files = multiRequest.getFiles(name);
            for (MultipartFile file : files) {
                if (file == null) {
                    continue;
                }
                //如果文件大小为0则不上传
                long fileSize = file.getSize();//文件大小
                if (fileSize <= 0L) {
                    msg = "文件名称：【" + file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("\\") + 1, file.getOriginalFilename().length()) + "】的文件大小为0,请核对!";
                    return AjaxResult.error(msg);
                }
                //文件格式验证 仅支持静态图片
                String fileName = file.getOriginalFilename();
                fileName = fileName.toLowerCase();
                if (!(fileName.endsWith(".jpg") || fileName.endsWith(".jpeg") || fileName.endsWith(".png")
                        || fileName.endsWith(".bmp"))) {
                    msg = "上传文件不符合要求";
                    return AjaxResult.error(msg);
                }
                if (fileSize > (maxSize * 1024 * 1024)) {
                    msg = "单个文件大小不能超过" + maxSize + "M，请确认！";
                    return AjaxResult.error(msg);
                }
                mfiles.add(file);
                totalSize += fileSize;
                cnt += 1;
            }
        }
        if (cnt > maxCnt) {
            msg = "最多可上传" + maxCnt + "个附件，请确认！";
            return AjaxResult.error(msg);
        }
        if (totalSize > (maxTotalSize * 1024 * 1024)) {
            msg = "所有上传文件大小总和不能超过" + maxTotalSize + "M，请确认！";
            return AjaxResult.error(msg);
        }

        String supplier = "";
        if(mfiles.size() > 0) {
            //识别发票获取供应商名称
            AjaxResult result = rapidOCRService.getSupplier(mfiles);
            mfiles = null;
            if (!"0".equals(result.get("code").toString())) {
                return result;
            }
            supplier = result.get("data").toString();

            //和已上传的发票的供应商比较
            msg = compareSupplier(busiId, supplier);
            if (StringUtils.isNotBlank(msg)) {
                return AjaxResult.error(msg);
            }
        }
        return AjaxResult.success("", supplier);
    }

    /**
     * 和已上传的发票的供应商比较
     * @param busyId   业务id
     * @param supplier 上传的发票供应商名称
     * @return
     */
    private String compareSupplier(String busyId, String supplier) {
        if (StringUtils.isNotBlank(supplier)) {
            //和已上传的发票的供应商比较
            Attachments attachments = attachmentsService.selectOneByBusyIdAndInvoiceFlag(Long.valueOf(busyId), "1");
            if (attachments != null) {
                if (!supplier.equals(attachments.getInvoiceSupplier())) {
                    return "上传的发票与已上传发票的供应商名称不一致，请确认！";
                }
            }
        }
        return "";
    }
}
