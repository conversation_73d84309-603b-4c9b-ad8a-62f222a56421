package com.sccl.modules.mssaccount.mssinterface.domain;

import java.util.List;

public class WriteoffInfo {
    private String otherSystemMainId;
    private String writeoffInstanceCode;
    //    type	String	同步类型	必填	1：新增，2：修改，3：作废
    private String type;

    public String getPickingMode() {
        return pickingMode;
    }

    public void setPickingMode(String pickingMode) {
        this.pickingMode = pickingMode;
    }

    private String pickingMode;

    private List<WriteoffDetailInfo> writeoffDetailInfos;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }


    public String getOtherSystemMainId() {
        return otherSystemMainId;
    }

    public void setOtherSystemMainId(String otherSystemMainId) {
        this.otherSystemMainId = otherSystemMainId;
    }

    public String getWriteoffInstanceCode() {
        return writeoffInstanceCode;
    }

    public void setWriteoffInstanceCode(String writeoffInstanceCode) {
        this.writeoffInstanceCode = writeoffInstanceCode;
    }

    public List<WriteoffDetailInfo> getWriteoffDetailInfos() {
        return writeoffDetailInfos;
    }

    public void setWriteoffDetailInfos(List<WriteoffDetailInfo> writeoffDetailInfos) {
        this.writeoffDetailInfos = writeoffDetailInfos;
    }

    @Override
    public String toString() {
        return "WriteoffInfo{" +
                "otherSystemMainId='" + otherSystemMainId + '\'' +
                ", writeoffInstanceCode='" + writeoffInstanceCode + '\'' +
                ", writeoffDetailInfos=" + writeoffDetailInfos +
                '}';
    }

}
