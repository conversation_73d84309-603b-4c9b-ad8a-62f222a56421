package com.sccl.modules.mssaccount.msssupplier.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.mssaccount.msssupplier.domain.MssSupplier;
import com.sccl.modules.mssaccount.msssupplier.service.IMssSupplierService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 供应商 信息操作处理
 * 
 * <AUTHOR>
 * @date 2019-05-01
 */
@RestController
@RequestMapping("/mssaccount/mssSupplier")
public class MssSupplierController extends BaseController
{
    private String prefix = "mssaccount/mssSupplier";
	
	@Autowired
	private IMssSupplierService mssSupplierService;
	
	@RequiresPermissions("mssaccount:mssSupplier:view")
	@GetMapping()
	public String mssSupplier()
	{
	    return prefix + "/mssSupplier";
	}
	
	/**
	 * 查询供应商列表 查询带上 有效状态
	 */
//	@RequiresPermissions("mssaccount:mssSupplier:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(MssSupplier mssSupplier)
	{
//		mssSupplier.setStatus("C");
		mssSupplier.setInfStatus("1");
		startPage();
        List<MssSupplier> list = mssSupplierService.selectByLike(mssSupplier);
		return getDataTable(list);
	}
	
	/**
	 * 新增供应商
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存供应商
	 */
	@RequiresPermissions("mssaccount:mssSupplier:add")
	//@Log(title = "供应商", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody MssSupplier mssSupplier)
	{		
		return toAjax(mssSupplierService.insert(mssSupplier));
	}

	/**
	 * 修改供应商
	 */
	@GetMapping("/edit/{msId}")
	public AjaxResult edit(@PathVariable("msId") Long msId)
	{
		MssSupplier mssSupplier = mssSupplierService.get(msId);

		Object object = JSONObject.toJSON(mssSupplier);

        return this.success(object);
	}
	
	/**
	 * 修改保存供应商
	 */
	@RequiresPermissions("mssaccount:mssSupplier:edit")
	//@Log(title = "供应商", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody MssSupplier mssSupplier)
	{		
		return toAjax(mssSupplierService.update(mssSupplier));
	}
	
	/**
	 * 删除供应商
	 */
	@RequiresPermissions("mssaccount:mssSupplier:remove")
	//@Log(title = "供应商", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(mssSupplierService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看供应商
     */
    @RequiresPermissions("mssaccount:mssSupplier:view")
    @GetMapping("/view/{msId}")
    @ResponseBody
    public AjaxResult view(@PathVariable("msId") Long msId)
    {
		MssSupplier mssSupplier = mssSupplierService.get(msId);

        Object object = JSONObject.toJSON(mssSupplier);

        return this.success(object);
    }

}
