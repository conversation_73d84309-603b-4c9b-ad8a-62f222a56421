package com.sccl.modules.business.statistical.account.controller;

import com.sccl.common.lang.StringUtils;
import com.sccl.common.thread.TimerThreadPoolExecutor;
import com.sccl.common.utils.DateUtils;
import com.sccl.common.utils.RegexUtil;
import com.sccl.common.utils.SystemClock;
import com.sccl.modules.autojob.util.convert.MessageMaster;
import com.sccl.modules.business.cache.utils.RedisUtil;
import com.sccl.modules.business.statistical.account.PowerAccountStatisticalIndexHandler;
import com.sccl.modules.business.statistical.account.domain.UnitPriceRank;
import com.sccl.modules.business.statistical.account.service.IPowerAccountStatisticalService;
import com.sccl.modules.business.statistical.framework.StatisticalIndex;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.FutureTask;

/**
 * 台账统计指标Controller层
 *
 * <AUTHOR> Yongxiang
 * @date 2022-12-05 16:01
 * @email <EMAIL>
 */
@RestController
@RequestMapping("/account_statistic")
@Slf4j
public class PowerAccountStatisticalIndexController {
    @Autowired
    private IPowerAccountStatisticalService service;
    @Autowired
    @Qualifier("unitPriceStat")
    private TimerThreadPoolExecutor threadPoolExecutor;

    @GetMapping(value = "/unit_price_rank/{company}", produces = "application/json;charset=UTF-8")
    public String doStatistic(@PathVariable("company") Long company) {
        if (company == null) {
            return MessageMaster.DefaultMessage.EMPTY_PARAMS.toString();
        }
        Runnable runnable = () -> {
            List<UnitPriceRank> unitPriceRanks = service.statUnitPriceRank(Collections.singletonList(company));
            final String title = unitPriceRanks
                    .get(0)
                    .getCompanyName() + "单价排名";
            PowerAccountStatisticalIndexHandler handler = new PowerAccountStatisticalIndexHandler(company);
            StatisticalIndex statisticalIndex = handler.wrapperIndex(title, unitPriceRanks, new Date());
            handler.createNewStatisticalIndex(title, unitPriceRanks, new Date());
            handler.loadInRedis(Collections.singletonList(statisticalIndex));
        };
        threadPoolExecutor.submit(runnable);
        return MessageMaster.getMessage(MessageMaster.Code.OK, "已成功启动" + company + "的单价统计");
    }


    @GetMapping(value = "/province_unit_price_rank", produces = "application/json;charset=UTF-8")
    public String doProvinceStatistic() {
        Runnable runnable = () -> {
            List<UnitPriceRank> unitPriceRanks = service.statProvinceUnitPriceRank();
            final String title = "全省单价排名";
            PowerAccountStatisticalIndexHandler handler = new PowerAccountStatisticalIndexHandler(null);
            StatisticalIndex statisticalIndex = handler.wrapperIndex(title, unitPriceRanks, new Date());
            handler.createNewStatisticalIndex(title, unitPriceRanks, new Date());
            handler.loadInRedis(Collections.singletonList(statisticalIndex));
        };
        threadPoolExecutor.submit(runnable);
        return MessageMaster.getMessage(MessageMaster.Code.OK, "已成功启动全省的单价统计");
    }

    @GetMapping(value = "/get_unit_price_rank", produces = "application/json;charset=UTF-8")
    public String getUnitPriceStat(@RequestParam("COMPANY") Long company, @RequestParam(value = "STATISTICAL_DATE", required = false) String statisticalDateString) {
        if (StringUtils.isEmpty(statisticalDateString)) {
            return MessageMaster.DefaultMessage.EMPTY_PARAMS.toString();
        }
        if (!RegexUtil.isMatch(statisticalDateString, RegexUtil.Type.DATE_YYYY_MM_DD)) {
            return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "日期格式应该为：yyyy-MM-dd");
        }
        Date statisticalDate = DateUtils.parseDate(statisticalDateString);
        PowerAccountStatisticalIndexHandler handler = new PowerAccountStatisticalIndexHandler(company);
        List<StatisticalIndex> indices = handler.takeOutStatisticalIndicesByDateAndTitle(statisticalDate, null);
        if (CollectionUtils.isEmpty(indices)) {
            String key = "STATISTICAL_LOCK:" + handler.getOwnerAs();
            FutureTask<Object> futureTask = null;
            try {
                boolean flag = RedisUtil.lock(key, SystemClock.now(), 30000, 5000, 5000);
                if (!flag) {
                    indices = handler.takeOutStatisticalIndicesByDateAndTitle(statisticalDate, null);
                    if (!CollectionUtils.isEmpty(indices)) {
                        return MessageMaster.getMessage(MessageMaster.Code.OK, "获取成功", indices, true, false);
                    }
                    return MessageMaster.getMessage(MessageMaster.Code.FORBIDDEN, "系统繁忙，请稍后重试");
                } else {
                    futureTask = RedisUtil.listenLock(key, 0.3, 3 * 60 * 1000);
                    log.info("加锁成功，将从数据库获取");
                    indices = handler.getStatisticalIndicesByDateAndTitle(statisticalDate, null);
                    if (CollectionUtils.isEmpty(indices)) {
                        return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "没有相关的统计记录");
                    } else {
                        handler.loadInRedis(indices);
                        return MessageMaster.getMessage(MessageMaster.Code.OK, "获取成功", indices, true, false);
                    }
                }
            } finally {
                RedisUtil.release(key);
                if (futureTask != null) {
                    futureTask.cancel(true);
                }
            }
        }
        return MessageMaster.getMessage(MessageMaster.Code.OK, "获取成功", indices, true, false);

    }


}
