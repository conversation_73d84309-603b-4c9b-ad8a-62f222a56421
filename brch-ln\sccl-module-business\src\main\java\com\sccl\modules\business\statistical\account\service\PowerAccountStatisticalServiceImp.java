package com.sccl.modules.business.statistical.account.service;

import com.sccl.modules.autojob.util.id.IdGenerator;
import com.sccl.modules.business.statistical.account.domain.UnitPriceRank;
import com.sccl.modules.business.statistical.account.mapper.PowerAccountStatisticalMapper;
import com.sccl.modules.monitor.operlog.domain.OperLog;
import com.sccl.modules.monitor.operlog.mapper.OperLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-12-05 15:04
 * @email <EMAIL>
 */
@Service
@Slf4j
public class PowerAccountStatisticalServiceImp implements IPowerAccountStatisticalService {
    @Autowired(required = false)
    PowerAccountStatisticalMapper mapper;
    @Autowired(required = false)
    private OperLogMapper operLogMapper;

    @Override
    public List<UnitPriceRank> statUnitPriceRank(List<Long> companies) {
        //统计四类单价排名
        //自有转供
        log.info("开始统计自有转供");
        BigDecimal provincePrivateTurn = mapper.selectProvinceAverageUnitPrice(true, false);
        List<UnitPriceRank> privateTurn = mapper.selectUnitPriceRankOnCompany(companies, true, false);
        if (!CollectionUtils.isEmpty(privateTurn) && provincePrivateTurn != null) {
            privateTurn.forEach(item -> {
                item.setProvinceAverageUnitPrice(provincePrivateTurn);
                item.setTitle((CollectionUtils.isEmpty(companies) ? "全省" : "") + "自有转供");
            });
        }
        log.info("自有转供统计完成");
        //自有直供
        log.info("开始统计自有直供");
        BigDecimal provincePrivateDirect = mapper.selectProvinceAverageUnitPrice(true, true);
        List<UnitPriceRank> privateDirect = mapper.selectUnitPriceRankOnCompany(companies, true, true);
        if (!CollectionUtils.isEmpty(privateDirect) && provincePrivateDirect != null) {
            privateDirect.forEach(item -> {
                item.setProvinceAverageUnitPrice(provincePrivateDirect);
                item.setTitle((CollectionUtils.isEmpty(companies) ? "全省" : "") + "自有直供");
            });
        }
        log.info("自有直供统计完成");
        //铁塔转供
        log.info("开始统计铁塔转供");
        BigDecimal provinceTowerTurn = mapper.selectProvinceAverageUnitPrice(false, false);
        List<UnitPriceRank> towerTurn = mapper.selectUnitPriceRankOnCompany(companies, false, false);
        if (!CollectionUtils.isEmpty(towerTurn) && provinceTowerTurn != null) {
            towerTurn.forEach(item -> {
                item.setProvinceAverageUnitPrice(provinceTowerTurn);
                item.setTitle((CollectionUtils.isEmpty(companies) ? "全省" : "") + "铁塔转供");
            });
        }
        log.info("铁塔转供统计完成");
        //铁塔直供
        log.info("开始统计铁塔直供");
        BigDecimal provinceTowerDirect = mapper.selectProvinceAverageUnitPrice(false, true);
        List<UnitPriceRank> towerDirect = mapper.selectUnitPriceRankOnCompany(companies, false, true);
        if (!CollectionUtils.isEmpty(towerDirect) && provinceTowerDirect != null) {
            towerDirect.forEach(item -> {
                item.setProvinceAverageUnitPrice(provinceTowerDirect);
                item.setTitle((CollectionUtils.isEmpty(companies) ? "全省" : "") + "铁塔直供");
            });
        }
        log.info("铁塔直供统计完成");
        List<UnitPriceRank> result = new ArrayList<>();
        result.addAll(privateTurn);
        result.addAll(privateDirect);
        result.addAll(towerTurn);
        result.addAll(towerDirect);
        OperLog operLog = new OperLog().setErrorMsg((companies == null ? "0" : companies.get(0) + "等" + companies.size()) + "个地市的单价排名统计完成，共计" + result.size() + "条");
        operLog.setId(IdGenerator.getNextIdAsLong());
        operLog
                .setStatus("1")
                .setMethod("statUnitPriceRank")
                .setOperTime(new Date());
        operLogMapper.insert(operLog);
        return result;
    }

    @Override
    public List<UnitPriceRank> statProvinceUnitPriceRank() {
        return statUnitPriceRank(null);
    }
}
