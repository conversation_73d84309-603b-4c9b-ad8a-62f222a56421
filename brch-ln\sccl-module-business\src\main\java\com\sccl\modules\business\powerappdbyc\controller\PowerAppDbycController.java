package com.sccl.modules.business.powerappdbyc.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.business.powerappdbyc.domain.PowerAppDbyc;
import com.sccl.modules.business.powerappdbyc.service.IPowerAppDbycService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 大数据基站异常 信息操作处理
 * 
 * <AUTHOR>
 * @date 2022-03-20
 */
@RestController
@RequestMapping("/business/powerAppDbyc")
public class PowerAppDbycController extends BaseController
{
    private String prefix = "business/powerAppDbyc";
	
	@Autowired
	private IPowerAppDbycService powerAppDbycService;
	
	@RequiresPermissions("business:powerAppDbyc:view")
	@GetMapping()
	public String powerAppDbyc()
	{
	    return prefix + "/powerAppDbyc";
	}
	
	/**
	 * 查询大数据基站异常列表
	 */
	@RequiresPermissions("business:powerAppDbyc:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(PowerAppDbyc powerAppDbyc)
	{
		startPage();
        List<PowerAppDbyc> list = powerAppDbycService.selectByList(powerAppDbyc);
		return getDataTable(list);
	}
	
	/**
	 * 新增大数据基站异常
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存大数据基站异常
	 */
	@RequiresPermissions("business:powerAppDbyc:add")
	@Log(title = "大数据基站异常", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody PowerAppDbyc powerAppDbyc)
	{		
		return toAjax(powerAppDbycService.insert(powerAppDbyc));
	}

	/**
	 * 修改大数据基站异常
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		PowerAppDbyc powerAppDbyc = powerAppDbycService.get(id);

		Object object = JSONObject.toJSON(powerAppDbyc);

        return this.success(object);
	}
	
	/**
	 * 修改保存大数据基站异常
	 */
	@RequiresPermissions("business:powerAppDbyc:edit")
	@Log(title = "大数据基站异常", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody PowerAppDbyc powerAppDbyc)
	{		
		return toAjax(powerAppDbycService.update(powerAppDbyc));
	}
	
	/**
	 * 删除大数据基站异常
	 */
	@RequiresPermissions("business:powerAppDbyc:remove")
	@Log(title = "大数据基站异常", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(powerAppDbycService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看大数据基站异常
     */
    @RequiresPermissions("business:powerAppDbyc:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		PowerAppDbyc powerAppDbyc = powerAppDbycService.get(id);

        Object object = JSONObject.toJSON(powerAppDbyc);

        return this.success(object);
    }

}
