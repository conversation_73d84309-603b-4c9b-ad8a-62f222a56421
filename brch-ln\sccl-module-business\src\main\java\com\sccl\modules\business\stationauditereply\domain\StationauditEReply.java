package com.sccl.modules.business.stationauditereply.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;


/**
 * 基站一站式稽核结果异常回复表 stationaudit_e_reply
 * 
 * <AUTHOR>
 * @date 2022-11-21
 */
public class StationauditEReply extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
    /** 基站一站式稽核结果主键 */
    private Long stationResultId;
    /** 评判的报账单 */
    private Long billid;
    /** 评判的时间 */
    private Date audittime;
    /** 节点类型
1-报账历史/2-电量历史/3-转供电合同是否失效/4-站址连续报账记录存在（转6直2）/5-电表不同站址/6-站址不同电表7-站址设备变动/8-站址评级 */
    private Integer nodetype;
    /** 节点评判信息 */
    private String auditmsg;
    /** 回复类型 1 来源category_type/ */
    private String replytype;
    /** 提交信息 */
    private String replymessage;

	public Long getTaskid() {
		return taskid;
	}

	public void setTaskid(Long taskid) {
		this.taskid = taskid;
	}

	public Long getCompany() {
		return company;
	}

	public void setCompany(Long company) {
		this.company = company;
	}

	public Long getCountry() {
		return country;
	}

	public void setCountry(Long country) {
		this.country = country;
	}

	/** 节点评判信息 */
	private Long taskid;
	/** 回复类型 1 来源category_type/ */
	private Long company;
	/** 提交信息 */
	private Long country;

    //todo :回复人 回复时间 台账


	public void setStationResultId(Long stationResultId)
	{
		this.stationResultId = stationResultId;
	}

	public Long getStationResultId() 
	{
		return stationResultId;
	}

	public void setBillid(Long billid)
	{
		this.billid = billid;
	}

	public Long getBillid() 
	{
		return billid;
	}

	public void setAudittime(Date audittime)
	{
		this.audittime = audittime;
	}

	public Date getAudittime() 
	{
		return audittime;
	}

	public void setNodetype(Integer nodetype)
	{
		this.nodetype = nodetype;
	}

	public Integer getNodetype() 
	{
		return nodetype;
	}

	public void setAuditmsg(String auditmsg)
	{
		this.auditmsg = auditmsg;
	}

	public String getAuditmsg() 
	{
		return auditmsg;
	}

	public void setReplytype(String replytype)
	{
		this.replytype = replytype;
	}

	public String getReplytype() 
	{
		return replytype;
	}

	public void setReplymessage(String replymessage)
	{
		this.replymessage = replymessage;
	}

	public String getReplymessage() 
	{
		return replymessage;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("stationResultId", getStationResultId())
            .append("billid", getBillid())
            .append("audittime", getAudittime())
            .append("nodetype", getNodetype())
            .append("auditmsg", getAuditmsg())
            .append("replytype", getReplytype())
            .append("replymessage", getReplymessage())
            .toString();
    }
}
