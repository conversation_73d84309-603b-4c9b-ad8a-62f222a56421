package com.sccl.modules.business.stationreportwhitelist.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/4/22 18:33
 * @describe 电表管理，用于mybatis-plus基本功能
 */
@Getter
@Setter
@TableName(value = "power_ammeterorprotocol")
public class PowerAmmeterorprotocol extends Model<PowerAmmeterorprotocol> {
    /**
     * 主键
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 类型
     * 在power_category_type表中type_category="ammeterCategory"
     * 1:电表 其他:协议
     */
    private String category;

    /**
     * 项目名称
     */
   private String projectname;

    /**
     * 电表户名
     */
   private String ammetername;

    /**
     * 关联协议名称
     */
   private String protocolname;

    /**
     * 局(站)编码
     */
    private Long stationcode;

    /**
     * 局站名称
     */
   private String stationName;

    /**
     * 局(站)类型
     */
   private Integer stationtype;

    /**
     * 分公司
     */
   private Long company;

    /**
     * 部门
     */
   private Long country;

    /**
     * 用电类型
     * 在power_electric_classification表中
     */
   private Integer electrotype;

    /**
     * 站址产权归属(1自留2铁塔)
     */
   private Integer property;

    /**
     * 状态
     */
   private String status;

    /**
     * 直供电标志
     * 1/2 直供电/转供电
     */
    private Integer directsupplyflag;

    /**
     * 单价
     */
    private BigDecimal price;

    @Override
    public String toString() {
    	return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }
}
