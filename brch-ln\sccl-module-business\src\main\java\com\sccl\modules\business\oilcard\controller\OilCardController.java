package com.sccl.modules.business.oilcard.controller;

import com.alibaba.fastjson.JSONObject;
import com.sccl.common.support.Convert;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.oilcard.domain.OilCard;
import com.sccl.modules.business.oilcard.domain.OilCardVo;
import com.sccl.modules.business.oilcard.mapper.OilCardMapper;
import com.sccl.modules.business.oilcard.service.IOilCardService;
import com.sccl.modules.business.oilexpense.domain.OilExpense;
import com.sccl.modules.system.user.domain.User;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 油卡基础信息
 *
 * <AUTHOR>
 * @date 2021-12-16
 */
@RestController
@RequestMapping("/business/oilCard")
public class OilCardController extends BaseController {
    private String prefix = "business/oilCard";

    @Autowired
    private IOilCardService oilCardService;

    @Resource
    OilCardMapper oilCardMapper;

    @RequiresPermissions("business:oilCard:view")
    @GetMapping()
    public String oilCard() {
        return prefix + "/oilCard";
    }

    /**
     * 查询油卡列表
     */
//    @RequiresPermissions("business:oilCard:list")
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(OilCard oilCard) {
        User currentUser = getCurrentUser();
        String country = currentUser.getDepartments().get(0).getId();
        String company = currentUser.getCompanies().get(0).getId();
        if (oilCard.getCompany() == null) {
            oilCard.setCompany(Long.valueOf(company));
        }
        if (oilCard.getCountry() == null || oilCard.getCountry()==0L) {
            oilCard.setCountry(Long.valueOf(country));
        }
        if (oilCard.getCompany() == -1) {
            oilCard.setCompany(null);
        }
        if (oilCard.getCountry() == -1) {
            oilCard.setCountry(null);
        }
        startPage();
        List<OilCard> list = oilCardService.selectList(oilCard);
        return getDataTable(list);
    }

    @RequestMapping("/listAll")
    @ResponseBody
    public TableDataInfo listAll(OilCard oilCard) {
        User currentUser = getCurrentUser();
        String country = currentUser.getDepartments().get(0).getId();
        String company = currentUser.getCompanies().get(0).getId();
        if (oilCard.getCompany() == null) {
            oilCard.setCompany(Long.valueOf(company));
        }
        if (oilCard.getCountry() == null || oilCard.getCountry()==0L) {
            oilCard.setCountry(Long.valueOf(country));
        }
        if (oilCard.getCompany() == -1) {
            oilCard.setCompany(null);
        }
        if (oilCard.getCountry() == -1) {
            oilCard.setCountry(null);
        }
        startPage();
        List<OilCardVo> list = oilCardService.selectListAll(oilCard);
        return getDataTable(list);
    }

    /**
     * 根据id查询油卡信息
     * <AUTHOR>
     * @date 2022/3/22
     */
    @RequestMapping("/viewListCard")
    @ResponseBody
    public TableDataInfo viewListCard(Long id) {
        startPage();
        OilCard oilCard = new OilCard();
        oilCard.setId(id);
        List<OilCard> list = oilCardService.selectListViewCard(oilCard);
        return getDataTable(list);
    }


    /**
     * 新增油卡
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存油卡
     */
//    @RequiresPermissions("business:oilCard:add")
    @Log(title = "油卡", action = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult addSave(OilCard oilCard) {
        try {
            OilCard oilCard1 = oilCardMapper.selectByOilCardId(oilCard.getOilCardId().toString());
            if (oilCard1 != null) {
                return AjaxResult.error("油卡编号重复，请重新输入");
            }
            oilCard.setCreateTime(new Date());
            oilCardService.insert(oilCard);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("操作失败");
        }
        return AjaxResult.success("添加成功");
    }

    /**
     * 修改油卡
     */
    @GetMapping("/edit/{id}")
    public AjaxResult edit(@PathVariable("id") Long id) {
        OilCard oilCard = oilCardService.get(id);
        Object object = JSONObject.toJSON(oilCard);
        return this.success(object);
    }

    /**
     * 修改油卡
     */
//    @RequiresPermissions("business:oilCard:edit")
    @Log(title = "油卡", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody OilCard oilCard) {
        //根据油卡编号查询(除开当前项)
        OilCard oilCard1 = oilCardMapper.selectByOilCardIdNoMe(oilCard.getId(),oilCard.getOilCardId().toString());
        if (oilCard1 != null) {
            return AjaxResult.error("油卡编号重复，请重新输入");
        }
        oilCard.setBillStatus(3);
        return toAjax(oilCardService.updateForModel(oilCard));
    }

    /**
     * 删除油卡
     */
//    @RequiresPermissions("business:oilCard:remove")
    @Log(title = "油卡", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return oilCardService.removeOilCard(ids);
    }


    /**
     * 查看油卡
     */
//    @RequiresPermissions("business:oilCard:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id) {
        OilCard oilCard = oilCardService.get(id);
        Object object = JSONObject.toJSON(oilCard);
        return this.success(object);
    }

}
