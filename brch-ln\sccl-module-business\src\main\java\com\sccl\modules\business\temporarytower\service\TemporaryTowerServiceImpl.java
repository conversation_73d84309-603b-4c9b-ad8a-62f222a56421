package com.sccl.modules.business.temporarytower.service;

import com.sccl.common.lang.StringUtils;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.business.temporarytower.domain.TemporaryTower;
import com.sccl.modules.business.temporarytower.mapper.TemporaryTowerMapper;
import com.sccl.modules.system.role.domain.Role;
import com.sccl.modules.system.user.domain.User;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * __临时铁塔数据__<br/>
 * 2019/10/23
 *
 * <AUTHOR>
 */
@Service
public class TemporaryTowerServiceImpl extends BaseServiceImpl<TemporaryTower> implements ITemporaryTowerService   {

    @Autowired
    TemporaryTowerMapper mapper;
    @Value("${sccl.deployTo}")
    private String deployTo;

    @Override
    public int deleteAll() {
        return mapper.deleteAll();
    }

    @Override
    public List<TemporaryTower> importExcel(String sheetName, InputStream input)throws Exception {
        List<TemporaryTower> list = new ArrayList<>();

        Workbook workbook = WorkbookFactory.create(input);
        Sheet sheet = workbook.getSheet(sheetName);
        if (StringUtils.isNotEmpty(sheetName))
        {
            // 如果指定sheet名,则取指定sheet中的内容.
            sheet = workbook.getSheet(sheetName);
        }
        if (sheet == null)
        {
            // 如果传入的sheet名不存在则默认指向第1个sheet.
            sheet = workbook.getSheetAt(0);
        }
        int rows = sheet.getPhysicalNumberOfRows();

        if (rows > 0)
        {
            Row row0 = sheet.getRow(0);
            for (int i = 1; i < rows; i++){
                TemporaryTower tower = new TemporaryTower();
                Row rowo = sheet.getRow(0);
                Row row = sheet.getRow(i);
                int cellNum = sheet.getRow(0).getPhysicalNumberOfCells();
                for (int j = 0; j < cellNum; j++){
                    Cell cello = rowo.getCell(j);
                    Cell cell = row.getCell(j);
                    if (cell == null)
                    {
                        continue;
                    }else{
                        if("省份".equals(cello.getStringCellValue())){
                            tower.setProvice(cell.getStringCellValue());
                        }
                        if("地市".equals(cello.getStringCellValue())){
                            String city = cell.getStringCellValue();
                            city = city.replaceAll("市","");
                            tower.setCity(city);
                        }
                        if("区县".equals(cello.getStringCellValue())){
                            tower.setDistrict(cell.getStringCellValue());
                        }
                        if("运营商".equals(cello.getStringCellValue())){
                            tower.setCarrier(cell.getStringCellValue());
                        }
                        if("需求类型".equals(cello.getStringCellValue())){
                            tower.setNeedstype(cell.getStringCellValue());
                        }
                        if("站址编码".equals(cello.getStringCellValue())){
                            tower.setStationaddrcode(cell.getStringCellValue());
                        }
                        if("站址名称".equals(cello.getStringCellValue())){
                            tower.setStationaddrname(cell.getStringCellValue());
                        }
                        if("详细地址".equals(cello.getStringCellValue())){
                            tower.setAddress(cell.getStringCellValue());
                        }
                        if("服务起始日期".equals(cello.getStringCellValue())){
                            tower.setServestartdate(cell.getStringCellValue());
                        }
                        if("服务结束日期".equals(cello.getStringCellValue())){
                            tower.setServeenddate(cell.getStringCellValue());
                        }
                        if("需求单号".equals(cello.getStringCellValue())){
                            tower.setOrderno(cell.getStringCellValue());
                        }
                    }
                }
                if(!StringUtils.isEmpty(tower.getStationaddrcode()) && !StringUtils.isEmpty(tower.getCity()) && !StringUtils.isEmpty(tower.getStationaddrname()) && !StringUtils.isEmpty(tower.getAddress())){
                    if(StringUtils.isEmpty(tower.getProvice())){
                        if("sc".equals(deployTo)){
                            tower.setProvice("四川省");
                        }else if("ln".equals(deployTo)){
                            tower.setProvice("辽宁省");
                        }
                    }
                    list.add(tower);
                }
            }
        }

        return list;
    }

    @Override
    public List<TemporaryTower> importExcelstop(String sheetName, InputStream input) throws Exception {
        List<TemporaryTower> list = new ArrayList<>();

        Workbook workbook = WorkbookFactory.create(input);
        Sheet sheet = workbook.getSheet(sheetName);
        if (StringUtils.isNotEmpty(sheetName))
        {
            // 如果指定sheet名,则取指定sheet中的内容.
            sheet = workbook.getSheet(sheetName);
        }
        if (sheet == null)
        {
            // 如果传入的sheet名不存在则默认指向第1个sheet.
            sheet = workbook.getSheetAt(0);
        }
        int rows = sheet.getPhysicalNumberOfRows();

        if (rows > 0)
        {
            Row row0 = sheet.getRow(0);
            for (int i = 1; i < rows; i++){
                TemporaryTower tower = new TemporaryTower();
                Row rowo = sheet.getRow(0);
                Row row = sheet.getRow(i);
                int cellNum = sheet.getRow(0).getPhysicalNumberOfCells();
                for (int j = 0; j < cellNum; j++){
                    Cell cello = rowo.getCell(j);
                    Cell cell = row.getCell(j);
                    if (cell == null)
                    {
                        continue;
                    }else{
                        if("省份".equals(cello.getStringCellValue())){
                            tower.setProvice(cell.getStringCellValue());
                        }
                        if("地市".equals(cello.getStringCellValue())){
                            String city = cell.getStringCellValue();
                            city = city.replaceAll("市","");
                            tower.setCity(city);
                        }
                        if("区县".equals(cello.getStringCellValue())){
                            tower.setDistrict(cell.getStringCellValue());
                        }
                        if("运营商".equals(cello.getStringCellValue())){
                            tower.setCarrier(cell.getStringCellValue());
                        }
                        if("需求类型".equals(cello.getStringCellValue())){
                            tower.setNeedstype(cell.getStringCellValue());
                        }
                        if("站址编码".equals(cello.getStringCellValue())){
                            tower.setStationaddrcode(cell.getStringCellValue());
                        }
                        if("站址名称".equals(cello.getStringCellValue())){
                            tower.setStationaddrname(cell.getStringCellValue());
                        }
                        if("详细地址".equals(cello.getStringCellValue())){
                            tower.setAddress(cell.getStringCellValue());
                        }
                        if("服务起始日期".equals(cello.getStringCellValue())){
                            tower.setServestartdate(cell.getStringCellValue());
                        }
                        if("服务结束日期".equals(cello.getStringCellValue())){
                            tower.setServeenddate(cell.getStringCellValue());
                        }
                        if("需求单号".equals(cello.getStringCellValue())){
                            tower.setOrderno(cell.getStringCellValue());
                        }
                    }
                }
                if(!StringUtils.isEmpty(tower.getStationaddrcode()) && !StringUtils.isEmpty(tower.getCity()) ){
                    if(StringUtils.isEmpty(tower.getProvice())){
                        if("sc".equals(deployTo)){
                            tower.setProvice("四川省");
                        }else if("ln".equals(deployTo)){
                            tower.setProvice("辽宁省");
                        }
                    }
                    list.add(tower);
                }
            }
        }

        return list;
    }

    @Override
    public int initTowerInfo() {
        return mapper.initTowerInfo();
    }

    @Override
    public Map<String,Object> importTower() {
        Map<String,Object> map = new HashMap<>();
        map.put("updateNum",null);
        map.put("insertNum",null);
        mapper.importTower(map);
        return map;
    }

    /**
     * *****局站管理 - 导入已过期
     * <AUTHOR>
     * @date 2022/4/21
     */
    @Override
    public Map<String,Object> importTowerTwo() {
        Map<String,Object> map = new HashMap<>();
        map.put("updateNum",null);
        map.put("insertNum",null);
        mapper.importTowerTwo(map);
        return map;
    }

    @Override
    public int deleteRepeat() {
        return mapper.deleteRepeat();
    }
    @Override
    public int deleteTwoAll() {
        return mapper.deleteTwoAll();
    }
    @Override
    public int deletetaAll() {
        return mapper.deletetaAll();
    }
    @Override
    public int insertTowerInfo() {
        return mapper.insertTowerInfo();
    }
    @Override
    public int insertTowerInfoTwo() {
        return mapper.insertTowerInfoTwo();
    }
    @Override
    public int insertTowerInfoAll() {
        return mapper.insertTowerInfoAll();
    }
    @Override
    public List<Role> getUserRole() {
        User user = ShiroUtils.getUser();
        return mapper.getUserRole(user==null?null:user.getId());
    }
}
