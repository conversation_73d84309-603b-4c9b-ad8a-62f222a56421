package com.sccl.modules.monitor.requestlog.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 请求日志实体类
 */
@Data
@Accessors(chain = true)
@TableName("sys_request_log")
public class RequestLog {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 请求时间
     */
    private Date requestTime;

    /**
     * 请求路径
     */
    private String requestPath;

    /**
     * 请求方法
     */
    private String requestMethod;

    /**
     * 请求用户ID
     */
    private Long userId;

    /**
     * 请求用户名
     */
    private String userName;

    /**
     * 请求参数
     */
    private String requestParams;

    /**
     * 请求耗时(毫秒)
     */
    private Long costTime;

    /**
     * 请求IP
     */
    private String requestIp;

    /**
     * 请求状态（0正常 1异常）
     */
    private String status;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 接口文档注释
     */
    private String apiDoc;
}
