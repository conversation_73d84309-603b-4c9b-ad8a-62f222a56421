package com.sccl.modules.monitor.requestlog.interceptor;

import com.alibaba.fastjson.JSON;
import com.sccl.common.utils.ServletUtils;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.modules.business.cache.utils.RedisUtil;
import com.sccl.modules.monitor.requestlog.domain.RequestLog;
import com.sccl.modules.monitor.requestlog.service.IRequestLogService;
import com.sccl.modules.system.config.domain.Config;
import com.sccl.modules.system.config.service.IConfigService;
import com.sccl.modules.system.user.domain.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 请求日志拦截器
 */
@Slf4j
@Component
public class RequestLogInterceptor implements HandlerInterceptor {

    private static final ThreadLocal<Long> startTimeThreadLocal = new ThreadLocal<>();
    // 系统参数键名
    private static final String REQUEST_LOG_ENABLED = "request.log.enabled";
    // Redis缓存键名
    private static final String REDIS_REQUEST_LOG_ENABLED = "request_log_enabled";
    // Redis缓存过期时间（1小时）
    private static final long REDIS_EXPIRE_TIME = 3600L;

    @Autowired
    private IRequestLogService requestLogService;

    @Autowired
    private IConfigService configService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 检查是否启用请求日志
        if (!isRequestLogEnabled()) {
            return true;
        }

        startTimeThreadLocal.set(System.currentTimeMillis());
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        // 检查是否启用请求日志
        if (!isRequestLogEnabled()) {
            return;
        }

        try {
            Long startTime = startTimeThreadLocal.get();
            if (startTime == null) {
                return;
            }

            long costTime = System.currentTimeMillis() - startTime;

            // 创建请求日志对象
            RequestLog requestLog = createRequestLog(request, handler, ex, startTime, costTime);

            // 安全地添加用户信息
            addUserInfoSafely(requestLog);

            // 异步保存日志
            requestLogService.asyncSave(requestLog);
        } catch (Exception e) {
            // 记录异常但不影响请求处理
            log.error("记录请求日志失败", e);
        } finally {
            startTimeThreadLocal.remove();
        }
    }

    /**
     * 创建请求日志对象
     */
    private RequestLog createRequestLog(HttpServletRequest request, Object handler,
                                        Exception ex, Long startTime, long costTime) {
        Map<String, String[]> params = request.getParameterMap();
        String requestParams = JSON.toJSONString(params);
        String apiDoc = extractApiDoc(handler);

        return new RequestLog()
                .setRequestTime(new Date(startTime))
                .setRequestPath(request.getRequestURI())
                .setRequestMethod(request.getMethod())
                .setRequestParams(requestParams)
                .setCostTime(costTime)
                .setRequestIp(ServletUtils.getRequestIp())
                .setStatus(ex == null ? "0" : "1")
                .setErrorMsg(ex == null ? null : StringUtils.substring(ex.getMessage(), 0, 2000))
                .setCreateTime(new Date())
                .setApiDoc(apiDoc);
    }

    /**
     * 安全地添加用户信息，避免会话无效异常
     */
    private void addUserInfoSafely(RequestLog requestLog) {
        try {
            // 先检查Subject是否存在且认证过
            Subject subject = SecurityUtils.getSubject();
            if (subject != null && subject.isAuthenticated() && subject.getSession(false) != null) {
                User user = ShiroUtils.getUser();
                if (user != null) {
                    requestLog.setUserId(user.getId());
                    requestLog.setUserName(user.getUserName());
                }
            }
        } catch (Exception e) {
            // 捕获但不影响日志记录流程
            log.debug("获取用户信息失败，记录匿名日志: {}", e.getMessage());
        }
    }

    /**
     * 提取API文档信息
     */
    private String extractApiDoc(Object handler) {
        if (!(handler instanceof HandlerMethod)) {
            return "";
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();

        try {
            // 获取@Log注解
            Log logAnnotation = method.getAnnotation(Log.class);
            if (logAnnotation != null) {
                return logAnnotation.title();
            }

            // 如果没有@Log注解，则使用类名和方法名
            String className = method.getDeclaringClass().getSimpleName();
            String methodName = method.getName();
            return className + "." + methodName;
        } catch (Exception e) {
            log.warn("获取方法文档注释失败: {}", method.getName(), e);
            return "";
        }
    }

    /**
     * 检查是否启用请求日志
     * @return true:启用 false:禁用
     */
    private boolean isRequestLogEnabled() {
        try {
            // 先从Redis获取
            String enabled = RedisUtil.getStr(REDIS_REQUEST_LOG_ENABLED);

            // 如果Redis中没有，则从数据库获取并缓存到Redis
            if (StringUtils.isEmpty(enabled)) {
                Config configEntity = configService.getByConfigKey(REQUEST_LOG_ENABLED);
                if (configEntity != null) {
                    enabled = configEntity.getConfigValue();
                    // 缓存到Redis，设置过期时间
                    if (StringUtils.isNotEmpty(enabled)) {
                        RedisUtil.set(REDIS_REQUEST_LOG_ENABLED, enabled, REDIS_EXPIRE_TIME, TimeUnit.SECONDS);
                    }
                }
            }

            return "true".equalsIgnoreCase(enabled);
        } catch (Exception e) {
            log.warn("获取请求日志开关配置失败", e);
            return false;
        }
    }
}
