package com.sccl.modules.business.powerauditstaiongrade.util;


import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;

public class ExcelExporter {
    public static void exportToExcel(List<? extends ExcelExport> excelExports, Map<String, String> columnMap, String outputPath) {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("数据表");

            // 创建标题行
            Row headerRow = sheet.createRow(0);
            int columnIndex = 0;
            for (String fieldName : columnMap.keySet()) {
                String columnHeader = columnMap.get(fieldName);
                Cell cell = headerRow.createCell(columnIndex++);
                cell.setCellValue(columnHeader);
            }

            // 填充数据行
            int rowIndex = 1;
            for (ExcelExport excelExport : excelExports) {
                Row dataRow = sheet.createRow(rowIndex++);
                columnIndex = 0;
                for (String fieldName : columnMap.keySet()) {
                    Cell cell = dataRow.createCell(columnIndex++);
                    String fieldValue = getFieldAsString(excelExport, fieldName);
                    cell.setCellValue(fieldValue);
                }
            }

            // 保存工作簿到文件
            try (FileOutputStream fileOut = new FileOutputStream(outputPath)) {
                workbook.write(fileOut);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static String getFieldAsString(ExcelExport excelExport, String fieldName) {
        // 使用 Java 反射获取对象的字段值
        try {

            Class<? extends ExcelExport> exportClass = excelExport.getClass();
            Field field = findFieldInHierarchy(exportClass, fieldName);
            field.setAccessible(true); // 设置字段可访问
            Object value = field.get(excelExport);

            return value != null ? value.toString() : "";
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
            return "";
        }
    }

    public static Field findFieldInHierarchy(Class<?> clazz, String fieldName) throws NoSuchFieldException {
        while (clazz != null) {
            try {
                return clazz.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                clazz = clazz.getSuperclass();
            }
        }
        throw new NoSuchFieldException(fieldName);
    }

}

