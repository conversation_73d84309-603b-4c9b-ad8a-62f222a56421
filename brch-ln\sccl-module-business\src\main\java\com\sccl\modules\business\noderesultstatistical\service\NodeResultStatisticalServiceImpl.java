package com.sccl.modules.business.noderesultstatistical.service;

import com.enrising.dcarbon.audit.AuditResult;
import com.enrising.dcarbon.audit.RefereeDatasource;
import com.enrising.dcarbon.codec.JsonUtil;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.utils.RedisUtil;
import com.sccl.modules.autojob.util.convert.StringUtils;
import com.sccl.modules.business.noderesultstatistical.domain.NodeResultStatistical;
import com.sccl.modules.business.noderesultstatistical.mapper.NodeResultStatisticalMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
 * 统计指标 服务层实现
 *
 * <AUTHOR>
 * @date 2022-11-24
 */
@Service
@Slf4j
public class NodeResultStatisticalServiceImpl extends BaseServiceImpl<NodeResultStatistical> implements INodeResultStatisticalService {
    @Autowired
    NodeResultStatisticalMapper nodeResultStatisticalMapper;

    @Override
    public List<? extends RefereeDatasource> contentList(String nodeKey) {
        log.info("contenlist 获取到的nodekey为{}", nodeKey);
        if (StringUtils.isBlank(nodeKey)) {
            return null;
        }

        log.info("优先从redis获取明细");
        List<AuditResult> json = (List<AuditResult>) RedisUtil.getObj(nodeKey);
        if (json != null) {
            log.info("redis存在数据");
            json.forEach(
              item -> {
                  item.setContent(
                    item.getContent().replace(
                      "实际功耗或标准功耗为0，无法评级",
                      "未采集到"
                    ));
              }
            );
            return json;
        }
        log.info("从数据库获取明细");

        String[] arr = nodeKey.split("_");
        String nodeType = arr[0].replace("node", "");
        String billId = arr[1];
        String level = arr[2];
        //1.拿到power_audit_result的id集合
        String idsTemp = nodeResultStatisticalMapper.selectIds(nodeType, billId, level);
        //1.1判空
        if (StringUtils.isBlank(idsTemp)) {
            return null;
        }

        List<String> ids = new ArrayList<>();
        ids = JsonUtil.jsonString2List(idsTemp, String.class);
        //2 取出对应的明细
        List<AuditResult> auditResults = nodeResultStatisticalMapper.selectIdsByAuditResult(ids);
        auditResults.forEach(
          item->{
              item.setContent(
                item.getContent().replace(
                  "实际功耗或标准功耗为0，无法评级",
                  "未采集到"
                )
              );
          }
        );
        log.info("获取到明细{}条", auditResults.size());
        //3 返回结果List
        return auditResults;

    }
}

