package com.sccl.modules.rental.rentalsupplier.controller;

import java.util.List;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.rental.rentalsupplier.domain.Rentalsupplier;
import com.sccl.modules.rental.rentalsupplier.service.IRentalsupplierService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 供应商 信息操作处理
 *
 * <AUTHOR>
 * @date 2019-08-23
 */
@RestController
@RequestMapping("/rental/rentalsupplier")
public class RentalsupplierController extends BaseController {
    private String prefix = "rental/rentalsupplier";

    @Autowired
    private IRentalsupplierService rentalsupplierService;

    @RequiresPermissions("rental:rentalsupplier:view")
    @GetMapping()
    public String rentalsupplier() {
        return prefix + "/rentalsupplier";
    }

    /**
     * 查询供应商列表
     */
    @RequiresPermissions("rental:rentalsupplier:list")
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(Rentalsupplier rentalsupplier) {
        startPage();
        List<Rentalsupplier> list = rentalsupplierService.selectList(rentalsupplier);
        return getDataTable(list);
    }

    /**
     * 新增供应商
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存供应商
     */
    @RequiresPermissions("rental:rentalsupplier:add")
    //@Log(title = "供应商", action = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody List<Rentalsupplier> rentalsupplier) {
        return toAjax(rentalsupplierService.batchInsert(rentalsupplier));
    }

    /**
     * 修改供应商
     */
    @GetMapping("/edit/{lifnr}")
    public AjaxResult edit(@PathVariable("lifnr") String lifnr) {
        Rentalsupplier r = new Rentalsupplier();
        r.setLifnr(lifnr);
        Rentalsupplier rentalsupplier = rentalsupplierService.selectUniqueBy(r);

        Object object = JSONObject.toJSON(rentalsupplier);

        return this.success(object);
    }

    /**
     * 修改保存供应商
     */
    @RequiresPermissions("rental:rentalsupplier:edit")
    //@Log(title = "供应商", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody Rentalsupplier rentalsupplier) {
        return toAjax(rentalsupplierService.update(rentalsupplier));
    }

    /**
     * 删除供应商
     */
    @RequiresPermissions("rental:rentalsupplier:remove")
    //@Log(title = "供应商", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(rentalsupplierService.deleteByIds(Convert.toStrArray(ids)));
    }


    /**
     * 查看供应商
     */
    @RequiresPermissions("rental:rentalsupplier:view")
    @GetMapping("/view/{lifnr}")
    @ResponseBody
    public AjaxResult view(@PathVariable("lifnr") String lifnr) {
        Rentalsupplier r = new Rentalsupplier();
        r.setLifnr(lifnr);
        Rentalsupplier rentalsupplier = rentalsupplierService.selectUniqueBy(r);

        Object object = JSONObject.toJSON(rentalsupplier);

        return this.success(object);
    }

}
