package com.sccl.modules.mssaccount.mssaccountbillitem.mapper;

import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.mssaccount.mssaccountbillitem.domain.MssAccountbillitem;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 报账明细 数据层
 * 
 * <AUTHOR>
 * @date 2019-04-24
 */
public interface MssAccountbillitemMapper extends BaseMapper<MssAccountbillitem>
{
    String selectUsageName(String usageId);

    List<MssAccountbillitem> selectListByIds(@Param("ids") String[] ids);

    int deleteByBillId(@Param("billId") String billId);
}