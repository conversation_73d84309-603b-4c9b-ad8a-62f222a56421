package com.sccl.modules.business.modlepricesp.domain;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ModlePriceSp2 {
    private String accountNo;
    private BigDecimal priceSp;
    private Integer work;
    private Integer dire;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ModlePriceSp2 that = (ModlePriceSp2) o;

        if (accountNo != null ? !accountNo.equals(that.accountNo) : that.accountNo != null) return false;
        if (priceSp != null ? !priceSp.equals(that.priceSp) : that.priceSp != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        return accountNo != null ? accountNo.hashCode() : 0;
    }
}
