package com.sccl.modules.business.examine.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 转供电考核-基础数据表
 */
@Data
@EqualsAndHashCode
@TableName(value = "transfer_examine_base")
public class TransferExamineBase implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	private Long id;

	/**
	 * 报账单id
	 */
	private Long billId;

	/**
	 * 财辅报账编码
	 */
	private String writeoffInstanceCode;

	/**
	 * 台账id
	 */
	private Long pcid;

	/**
	 * 所属分公司
	 */
	private String company;

	/**
	 * 电表协议ID
	 */
	private Long ammeterid;

	/**
	 * 结算类型 直供电、转供电 在power_category_type表中type_category="directSupplyFlag"
	 */
	private int directsupplyflag;

	/**
	 * 账期 yyyy-MM
	 */
	private String accountPeriod;

	/**
	 * 台账期号 yyyyMM
	 */
	private String accountno;

	/**
	 * 起始日期-格式为 yyyyMMdd
	 */
	private String startdate;

	/**
	 * 截止日期-格式为 yyyyMMdd
	 */
	private String enddate;

	/**
	 * 总电量
	 */
	private BigDecimal totalusedreadings;

	/**
	 * 总金额
	 */
	private BigDecimal accountmoney;

	/**
	 * 单价
	 */
	private BigDecimal unitpirce;

	/**
	 * 状态
	 */
	private String status;

	/**
	 * 备注1
	 */
	private String remark1;

	/**
	 * 备注2
	 */
	private String remark2;

	/**
	 * 备注3
	 */
	private String remark3;

	/**
	 * 备注4
	 */
	private String remark4;

	/**
	 * 删除标志 0/1 正常/删除
	 */
	private String delFlag;
}
