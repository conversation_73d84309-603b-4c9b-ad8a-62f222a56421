package com.sccl.modules.rental.rentalcarmodel.mapper;

import com.sccl.modules.rental.rentalcarmodel.domain.Rentalcarmodel;
import com.sccl.framework.mapper.BaseMapper;

import java.util.List;
import java.util.Map;

/**
 * 车辆 （model） 数据层
 *
 * <AUTHOR>
 * @date 2019-08-23
 */
public interface RentalcarmodelMapper extends BaseMapper<Rentalcarmodel> {


    void deleteByRmmids(String[] toStrArray);

    List<Rentalcarmodel> selectListByIds(String[] toStrArray);
    List<Map<String,Object>> statistical(Map<String,Object> params);
}