package com.sccl.modules.business.heataccount.domain;

import com.sccl.modules.common.domain.BaseRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: 热力台账请求参数
 * @date 2024/8/28  15:03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class HeatAccountRequest extends BaseRequest {

    /**
     * 用能主体
     */
    private String heatUseBody;

    /**
     * 请求来源 1--归集单点击事项详情处 2--报账单--关联归集单查询台账明细处
     */
    private Integer queryFrom;

    /**
     * 归集单id
     */
    private Long parid;

    /**
     * 状态  报账单--关联的归集单--》台账明细处使用
     */
    private String status;

    /**
     * 是否合并的归集单 报账单--关联的归集单--》台账明细处使用
     */
    private boolean union;


}
