package com.sccl.modules.business.poweraudit.entity;

import com.sccl.framework.aspectj.lang.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: 芮永恒
 * @CreateTime: 2024-02-23  09:39
 * @Description: 台账稽核报表返回值
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PowerAuditDTO {


    /**
     * 地市
     */
    @Excel(name = "地市")
    private String city;

    /**
     * 地市
     */
//    @Excel(name = "地市")
    private String cityCode;

    /**
     * 时间（月）
     */
    @Excel(name = "时间（月）")
    private String month;

    /**
     * 区县
     */
//    @Excel(name = "区县")
    private String countyCompanies;

    /**
     * 区县code
     */
    private String countyCompaniesCode;

    /**
     * 运营分局
     */
    @Excel(name = "运营分局")
    private String operationsBranch;

    /**
     *  异常电表总表
     */
    @Excel(name = "异常电表总数")
    private Integer sums;

    /**
     * 一表多站/一站多表
     */
    @Excel(name = "一表多站/一站多表")
    private Integer mutiJtlteCodes;

    /**
     * 电价合理性
     */
    @Excel(name = "电价合理性")
    private Integer electricityPrices;

    /**
     * 电表站址一致性
     */
    @Excel(name = "电表站址一致性")
    private Integer addressConsistence;


    /**
     * 台账周期异常
     */
    @Excel(name = "台账周期连续性")
    private Integer periodicAnomaly;

    /**
     * 电表度数连续性
     */
    @Excel(name = "电表度数连续性")
    private Integer electricityContinuity;

    /**
     * 台账电量合理性
     */
    @Excel(name = "电量合理性")
    private Integer electricityRationality;

    /**
     * 台账日均电量波动合理性
     */
    @Excel(name = "日均电量波动合理性")
    private Integer fluctuateContinuity;


    /**
     * 台账日均耗电量合理性
     */
    @Excel(name = "日均耗电量合理性")
    private Integer consumeContinuity;

    /**
     * 共享站分摊比例准确性
     */
    @Excel(name = "共享站分摊比例准确性")
    private Integer shareAccuracy;

    /**
     * 报账周期合理性
     */
    @Excel(name = "报账周期合理性")
    private Integer reimbursementCycle;


    /**
     * 沉默电表
     */
//    @Excel(name = "沉默电表")
    private Integer electricityMeter;


    /**
     * 电表与支付对象一致性
     */
//    @Excel(name = "电表与支付对象一致性")
    private Integer paymentConsistence;

    /**
     * 重复交叉缴费
     */
//    @Excel(name = "重复交叉缴费")
    private Integer repeats;

    /**
     * 1:成功，0:失败
     */
    private Integer ifSuccess;


    /**
     * 1:基站类，0:非基站
     */
    private Integer siteType;
}
