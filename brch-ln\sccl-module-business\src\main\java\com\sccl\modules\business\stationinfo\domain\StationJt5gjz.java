package com.sccl.modules.business.stationinfo.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sccl.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * 集团LTE网管基站表 station_jt5gjz
 *
 * <AUTHOR>
 * @date 2021-05-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StationJt5gjz extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	private Long id;
    /**  */
    private String enbid;
    /**  */
    private String jtcode;
    /**  */
    private String jtname;
    /**  */
    private String tacode;
    /**  */
    private String taname;
    /**  */
    private String citycode;
    /**  */
    private String areacode;
    /**  */
    private String enbidname;
    /**  */
    private String enbid采集名称;
    /**  */
    private String 基站类型;
    /**  */
    private String 基站经度;
    /**  */
    private String 基站纬度;

    /**
     * 局站id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long stationid;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
	}
}
