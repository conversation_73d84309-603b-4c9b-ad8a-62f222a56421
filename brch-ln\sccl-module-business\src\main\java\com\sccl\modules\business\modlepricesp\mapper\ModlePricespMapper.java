package com.sccl.modules.business.modlepricesp.mapper;

import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.modlepricesp.domain.ModlePriceSp2;
import com.sccl.modules.business.modlepricesp.domain.ModlePricesp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 单价输配电价 数据层
 *
 * <AUTHOR>
 * @date 2023-03-08
 */
public interface ModlePricespMapper extends BaseMapper<ModlePricesp> {


    List<ModlePriceSp2> selctPrisp(List<String> accountNos);

    int updatebitch(@Param("list") List<ModlePricesp> modlePricesps);
}