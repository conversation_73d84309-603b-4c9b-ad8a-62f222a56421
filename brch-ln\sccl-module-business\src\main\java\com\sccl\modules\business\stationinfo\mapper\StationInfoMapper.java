package com.sccl.modules.business.stationinfo.mapper;

import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.account.domain.Ltestation;
import com.sccl.modules.business.account.domain.NhSite;
import com.sccl.modules.business.stationinfo.domain.StationInfo;
import com.sccl.modules.business.stationinfo.dto.ResStationQueryDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 局站 数据层
 *
 * <AUTHOR>
 * @date 2019-05-29
 */
public interface StationInfoMapper extends BaseMapper<StationInfo>
{

    public List<StationInfo> getMotherList(StationInfo stationInfo);
    public List<StationInfo> selectListByAmmeter(Map<String, Object> paramMap);
    public List<StationInfo> selectCheckListByAmmeter(Map<String, Object> paramMap);
    public List<Ltestation> getListLtestation(Map<String, Object> paramMap);
    public  List<StationInfo> selectOld(Long id);
    public List<StationInfo> getResStation(StationInfo stationInfo);
    public List<StationInfo> getResStationSC(StationInfo stationInfo);
    public List<String> getUserRoleAuth(@Param(value="id")Long id);
    public int countExitSelf(StationInfo stationInfo);
    public int countExitInRecord(StationInfo stationInfo);
    public int countExitSelfRecord(StationInfo stationInfo);
    public String seleectStationCode(@Param(value="stationCode")String stationCode);
    public StationInfo getStationByName(@Param(value="name")String name);
    public List isInTodoList(Map<String, Object> paramMap);
    public String getOrgParentId(@Param(value="id")String id);
    public String selectIds(@Param(value="company")String company);
    public String getOrgtype(@Param(value="id")String id);
    public int getRecordCount(StationInfo stationInfo);
    public List<StationInfo> getStationAddr(StationInfo stationInfo);
    public List<StationInfo> getStationAddrSC(StationInfo stationInfo);
    public List<StationInfo> getStationHousing(StationInfo stationInfo);
    public List<StationInfo> selectObjectBy(StationInfo stationInfo);
    public List<StationInfo> selectbaseList(StationInfo stationInfo);

    StationInfo selectStationFromTwo(String id);

    StationInfo select5grforinitid(@Param("stationcode") String stationcode5gr);

    NhSite get5grCodeByStation(@Param("id") Long id);

    public List<StationInfo> getValidStationSC(StationInfo stationInfo);

    public List<StationInfo> getResStationList(ResStationQueryDto queryDto);

    public List<StationInfo> getResRoomList(ResStationQueryDto queryDto);
}
