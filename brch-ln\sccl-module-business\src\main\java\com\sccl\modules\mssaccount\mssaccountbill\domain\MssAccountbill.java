package com.sccl.modules.mssaccount.mssaccountbill.domain;

import com.enrising.dcarbon.audit.Auditable;
import com.enrising.dcarbon.audit.RefereeDatasource;
import com.sccl.framework.web.domain.BaseEntity;
import com.sccl.modules.business.accountbillpre.domain.Accountbillpre;
import com.sccl.modules.mssaccount.mssabccustomerbank.domain.MssAbccustomerBank;
import com.sccl.modules.mssaccount.mssaccountbillitem.domain.MssAccountbillitem;
import com.sccl.modules.mssaccount.mssaccountbillpayinfo.domain.MssAccountbillpayinfo;
import com.sccl.modules.mssaccount.mssaccountclearitem.domain.MssAccountclearitem;
import com.sccl.modules.mssaccount.mssaccountclearitemaccount.domain.MssAccountclearitemAccount;
import com.sccl.modules.mssaccount.mssinterface.domain.BillExecuteState;
import com.sccl.modules.mssaccount.msssupplieritem2.domain.MssSupplierItem2;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


/**
 * 报账表 MSS_ACCOUNTBILL
 *
 * <AUTHOR>
 * @date 2019-04-24
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MssAccountbill extends BaseEntity implements RefereeDatasource, Auditable {

    private static final Long serialVersionUID = 1L;

    private Boolean consumption;
    /**
     * 明细
     */
    List<MssAccountbillitem> item;
    /**
     * 供应商银行信息
     */
    List<MssSupplierItem2> supplierItem2;
    /**
     * 客户银行信息
     */
    List<MssAbccustomerBank> customerBank;
    /**
     * 外部收款人信息
     */
    List<MssAccountbillpayinfo> payinfo;
    /**
     * 挑对
     */
    List<MssAccountclearitem> clearitem;
    /**
     * 挑对台账
     */
    List<MssAccountclearitemAccount> clearitemAccount;
    /**
     * 归集单
     */
    Accountbillpre accountbillpre;

    /**
     * 支付方式:：1-分公司委托划扣、2-分公司主动支付、3-省公司委托划扣、4-省公司主动支付、5--汇票支付、6-分公司收款、7-省公司收款、8-不涉及银行收付
     */
    private BigDecimal paymentType;
    /**
     * 状态1,'草稿',2,'待办',3,'生成报帐单',4,'生成凭证',7,'完成',5,'财务通过',-1,'报帐单删除',-2,'退单',-4,'等待生成,-3,'送财辅失败',8 '财辅退单'
     */
    private Integer status;
    /**
     * 客户名称
     */
    private String clientName;
    /**
     * 客户编码
     */
    private String clientCode;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 供应商编码
     */
    private String supplierCode;
    /**
     * 供应商开会银行
     */
    private String supplierBank;
    /**
     * 商开会银行帐号
     */
    private String supplierAccount;
    /**
     * 预算组项目id
     */
    private String budgetItemGroupId;
    /**
     * 打包报帐档案id
     */
    private String packupArchiveId;
    /**
     * 业务类型
     */
    private BigDecimal bizType;
    /**
     * 是否还款
     */
    private BigDecimal isPay;
    /**
     * 是否关联交易
     */
    private String conTrans;
    /**
     * 是否打包
     */
    private BigDecimal isToPack;
    /**
     * 预算事项code
     */
    private String budgetCode;
    /**
     * 预算事项名称
     */
    private String budgetName;
    /**
     * 预算项目 code
     */
    private String budgetItemCode;
    /**
     * 预算项目 名称
     */
    private String budgetItemName;
    /**
     * 所属市局id
     */
    private String cityId;
    /**
     * 所属市局名称
     */
    private String cityName;
    /**
     * 所属利润中心id
     */
    private String profitCenterCode;
    /**
     * 所属利润中心名称
     */
    private String profitCenterName;
    /**
     * 所属责任中心id
     */
    private String costCenterId;
    /**
     * 所属责任中心名称
     */
    private String costCenterName;
    /**
     * 创建日期
     */
    private Date createDate;
    /**
     * 公司代码 (公司主体、账套)
     */
    private String companyCode;
    /**
     * 是否归口
     */
    private BigDecimal isCategory;
    /**
     * 贷方会计科目
     */
    private String creditAccountCode;
    /**
     * 贷方会计科目名称
     */
    private String creditAccountName;
    /**
     * 是否预付款
     */
    private String isPrepay;
    /**
     * 纳税调整类别
     */
    private String taxAdjustType;
    /**
     * 纳税调整金额
     */
    private BigDecimal taxAdjustSum;
    /**
     * 纳税调整说明
     */
    private String taxAdjustComments;
    /**
     * 查看考核表url
     */
    private String assessDataUrl;
    /**
     * 省级编码
     */
    private String provinceCode;
    /**
     * 挑对模式
     */
    private String pickingMode;
    /**
     * 项目预算编码（以前的活动预算）
     */
    private String budgetGroupItemCode;
    /**
     * 预算活动组名称
     */
    private String budgetGroupItemName;
    /**
     * 预算责任中心编码
     */
    private String responseCenterCode;
    /**
     * 预算责任中心名称
     */
    private String responseCenterName;
    /**
     * 显示查看业务数据（为空时不显示）
     */
    private String attchmentgAdd;
    /**
     * 是否需纳税调整
     */
    private String isTaxAdjust;
    /**
     * 摘要
     */
    private String abstractValue;
    /**
     * 填报人所属利润中心id
     */
    private String fillInProfitCenterId;
    /**
     * 填报人所属责任中心id
     */
    private String fillInCostCenterId;
    /**
     * 填报人所属利润中心名称
     */
    private String fillInProfitCenterName;
    /**
     * 填报人所属责任中心名称
     */
    private String fillInCostCenterName;
    /**
     * 是否补录报帐
     */
    private BigDecimal replenishFillIn;
    /**
     * 填报人角色id
     */
    private String fillInRoleId;
    /**
     * 被后续操作的金额
     */
    private BigDecimal beAfterOperateSum;
    /**
     * 费用形式
     */
    private String feeType;
    /**
     * 费用发生日
     */
    private String happenDate;
    /**
     * SAP记帐日期
     */
    private Date sapRemarkDate;
    /**
     * 清帐金额
     */
    private BigDecimal clearSum;
    /**
     * 预提成本报账、全额还款、全额付款、摊销的标识
     */
    private String preusedAndShareLabel;
    /**
     * 本年度核定摊销金额/预提成本报账的预提总金额
     */
    private BigDecimal auditShareSum;
    /**
     * 首次摊销金额
     */
    private BigDecimal firstShareSum;
    /**
     * 摊销费用科目编码
     */
    private String shareAccountCode;
    /**
     * 摊销费用科目名称
     */
    private String shareAccountName;
    /**
     * 合同编号
     */
    private String contractno;
    /**
     * 移动类型
     */
    private String moveTypeCode;
    /**
     * 移动类型说明
     */
    private String moveTypeName;
    /**
     * 领料用途
     */
    private String usage;
    /**
     * 领料用途明细
     */
    private String usagedetail;
    /**
     * 预算期间名称
     */
    private String budgetsetname;
    /**
     * 是否可修改
     */
    private String disposemode;
    /**
     * 标识是否能够修改会计科目
     */
    private String iscanupdate;
    /**
     * 用途明细编码
     */
    private String usagedetailCode;
    /**
     * 档案入口类型（标识成本报帐还是建管费报帐）
     */
    private BigDecimal archiveEntryType;
    /**
     * 预算期间ID
     */
    private String budgetsetid;
    /**
     * 现金流预算事项编码
     */
    private String cashBudgetInstanceCode;
    /**
     * 现金流预算事项名称
     */
    private String cashBudgetInstanceName;
    /**
     * 借款事项编码
     */
    private String loanBudgetInstanceCode;
    /**
     * 借款事项名称
     */
    private String loanBudgetInstanceName;
    /**
     * 经办人电话
     */
    private String telephone;
    /**
     * 一次性供应商城市
     */
    private String onceSupplierCity;
    /**
     * 一次性供应商名称
     */
    private String onceSupplierName;
    /**
     * 是否一次性供应商
     */
    private String isOnceSupplier;
    /**
     * 是否统付
     */
    private String isUnifyPay;
    /**
     * 贷方会计所属公司编码
     */
    private String creditAccountCompanyCode;
    /**
     * 薪酬清册编码
     */
    private String salaryCode;
    /**
     * 是否省公司支付
     */
    private String isProvincePay;
    /**
     * 合同名称
     */
    private String contractName;
    /**
     * 合同金额
     */
    private BigDecimal contractSum;
    /**
     * 业务编码
     */
    private String bizTypeCode;
    /**
     * 入口编码(经济事项id)
     */
    private String bizEntryCode;
    /**
     * 合同执行进度说明
     */
    private String contractDescription;
    /**
     * 付款条款摘要
     */
    private String payoffDescription;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 供应商公司编码
     */
    private String supplierCompanyCode;
    /**
     * 银行信息
     */
    private String bankCode;
    /**
     * 是否拆分贷方
     */
    private String isSplitCredit;
    /**
     * 不见单编码
     */
    private String unDisplayCode;
    /**
     * 是否建管费
     */
    private String buildFee;
    /**
     * 报备信息id
     */
    private String writeoffCheckInfoId;
    /**
     * 是否稽核
     */
    private String isImageAudited;
    /**
     * 合同清账标识
     */
    private String clearAccount;
    /**
     * 是否存在合同清账
     */
    private String existClearAccount;
    /**
     * 结转单id
     */
    private String carryoverInstanceid;
    /**
     * 核定原因
     */
    private String auditReason;
    /**
     * 合同发票金额
     */
    private BigDecimal contractInvoiceSum;
    /**
     * sap凭证号
     */
    private String sapCertificateCode;
    /**
     * 调账类型
     */
    private String adjustType;
    /**
     * 是否付款
     */
    private String isPayment;
    /**
     * 是否加急
     */
    private String isEmergency;
    /**
     * 摊销收益期间开始年
     */
    private BigDecimal shareBeginYear;
    /**
     * 摊销收益期间开始月
     */
    private BigDecimal shareBeginMonth;
    /**
     * 摊销收益期间结束年
     */
    private BigDecimal shareEndYear;
    /**
     * 摊销收益期间结束月
     */
    private BigDecimal shareEndMonth;
    /**
     * 营业类型
     */
    private String businessType;
    /**
     * 代办点
     */
    private String location;
    /**
     * 审定费用发生日
     */
    private Date auditHappenDate;
    /**
     * 公司名称文本 人工成本优化审批需要
     */
    private String companyNameTxt;
    /**
     * 是否电信代开发票
     */
    private String isGdtelInvoice;
    /**
     * 关联交易类型编码
     */
    private String relativeTypeCode;
    /**
     * 关联交易类型名称
     */
    private String relativeTypeName;
    /**
     * 是否新合同
     */
    private BigDecimal isNewContract;
    /**
     * 发出商品金额
     */
    private BigDecimal outGoodsSum;
    /**
     * 增值税金额
     */
    private BigDecimal payableTaxfeeSum;
    /**
     * 入口名称（经济事项名称）
     */
    private String bizEntryName;
    /**
     * 往来交易类型
     */
    private String tradeType;
    /**
     * 币种
     */
    private String currency;
    /**
     * 是否在SAP系统直接制证
     */
    private BigDecimal isSapFlag;
    /**
     *
     */
    private String cashItemName;
    /**
     * 人工成本---代扣分公司编码
     */
    private String withholdingCityId;
    /**
     * 人工成本---代扣分公司名称
     */
    private String withholdingCityName;
    /**
     * 业务事项id
     */
    private String bizItemInstId;
    /**
     * 会计科目编码
     */
    private String accountCode;
    /**
     * 会计科目名称
     */
    private String accountName;
    /**
     * 会计所属公司编码
     */
    private String accountCompanyCode;
    /**
     * 是否借方、贷方（1标识为是借方、0标识是贷方）
     */
    private String isDebitCredit;
    /**
     * 业务事项类型
     */
    private String bizItemType;
    /**
     * 是否员工代垫（0为否、1为是）
     */
    private String isStaffPayment;
    /**
     * 是否结转 0或者空 为未结转 1:列并付凭证结转 2:付款凭证记账日期结转
     */
    private String isCarryover;
    /**
     * sysdate
     */
    private Date timestamp;
    /**
     * 税费主单ID
     */
    private String taxFeeMainId;
    /**
     * 风险等级标识
     */
    private String riskLevelFlag;
    /**
     * 是否合同首次付款标识
     */
    private String isContractFirstPay;
    /**
     * 外围系统主单id
     */
    private String otherSysMainId;
    /**
     * CBS集成报账税金（CBS集成专用）
     */
    private BigDecimal proxyTaxAmount;
    /**
     * （营改增）票据类型
     */
    private String invoiceType;
    /**
     * （营改增）是否存在实物赠送
     */
    private String isExistKindGift;
    /**
     * （营改增）实物赠送金额
     */
    private BigDecimal kindGiftSum;
    /**
     * （营改增）实物赠送税额
     */
    private BigDecimal kindGiftTaxSum;
    /**
     * （营改增）是否涉及进项税转出
     */
    private String isInputTax;

    /**
     * （营改增）进项税金额
     */
    private BigDecimal inputTaxSum;


    /**
     * （营改增）是否认证标识（1：待认证、2：已认证、3：认证不通过）
     */
    private String isVatAuthenticated;
    /**
     * （营改增）进项税转出金额
     */
    private BigDecimal inputTaxTurnSum;
    /**
     * （营改增）进项税转出业务类型
     */
    private String inputTaxTurnBizType;
    /**
     * 是否例外付款
     */
    private String isExcpayment;
    /**
     * （合同预审）合同是否多次使用
     */
    private String contractUsedTimes;
    /**
     * 供应商是否一般纳税人
     */
    private String isGeneralPayer;
    /**
     * 是否走影像（仅限能耗使用，表示预提业务）
     */
    private BigDecimal isNeedImage;
    /**
     * 能耗业务数据显示连接
     */
    private String showDataUrl;
    /**
     * 报帐档案id
     */
    private String writeoffArchiveId;
    /**
     * 报帐单号
     */
    private String writeoffInstanceCode;
    /**
     * 报帐单类型
     */
    private String formTypeCode;
    /**
     * 合计金额
     */
    private BigDecimal sum;
    /**
     * 财务核定金额
     */
    private BigDecimal auditSum;
    /**
     * 还款金额
     */
    private BigDecimal payoffSum;
    /**
     * 核定还款金额
     */
    private BigDecimal auditPayoffSum;
    /**
     * 填报人帐号
     */
    private String fillInAccount;
    /**
     * 填报人名称
     */
    private String fillInName;
    /**
     * 填报人所属部门
     */
    private String fillInDep;
    /**
     * 附单据张数
     */
    private BigDecimal formAmount;
    /**
     * 纳税属性
     */
    private String paytaxattr;
    /**
     * 业务发生时间点标记
     */
    private String busihappendtimeflag;
    /**
     * 原用户id
     */
    private BigDecimal operatorid;
    /**
     * 流程id
     */
    private Long processinstid;
    /**
     *
     */
    private BigDecimal selectpreid;
    /**
     * 主表guid
     */
    private String guid;
    /**
     * 合并主单
     */
    private BigDecimal parentid;
    /**
     *
     */
    private BigDecimal exceptiontype;
    /**
     * 制证会计XX@sc
     */
    private String sapcreator;
    /**
     * 公司代码
     */
    private String sapcompanycode;
    /**
     * 年度
     */
    private String year;
    /**
     * 单据类型 1 报销 2 挂账 3挂账支付 4预付 5预付冲销 6借款冲销7前期预付冲销8收款9预估10预估冲销
     */
    private BigDecimal billtype;
    /**
     * 审批退回 0 财辅退回 -1 正常 1
     */
    private String iresult;
    /**
     * 供应商类型  1供应商 2客户
     */
    private String suppliertype;
    /**
     * 所在机构
     */
    private Long orgid;
    /**
     * 能耗类型1水 2 气 3 油 null 电
     */
    private BigDecimal energytype;

    /**
     * 判断是否需要稽核 1：要稽核 0：不稽核
     */
    private String jh;


    /**
     * 判断来自哪个页面
     */
    private String ymmc;

    /**
     * 统计明细电量
     */
    private BigDecimal sumAmount;


    /**
     * 报账单稽核出大数据
     */
    private BigDecimal businessElectricity;


    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("paymentType", getPaymentType())
                .append("status", getStatus())
                .append("clientName", getClientName())
                .append("clientCode", getClientCode())
                .append("supplierName", getSupplierName())
                .append("supplierCode", getSupplierCode())
                .append("supplierBank", getSupplierBank())
                .append("supplierAccount", getSupplierAccount())
                .append("budgetItemGroupId", getBudgetItemGroupId())
                .append("packupArchiveId", getPackupArchiveId())
                .append("bizType", getBizType())
                .append("isPay", getIsPay())
                .append("conTrans", getConTrans())
                .append("isToPack", getIsToPack())
                .append("budgetCode", getBudgetCode())
                .append("budgetName", getBudgetName())
                .append("budgetItemCode", getBudgetItemCode())
                .append("budgetItemName", getBudgetItemName())
                .append("cityId", getCityId())
                .append("cityName", getCityName())
                .append("profitCenterCode", getProfitCenterCode())
                .append("profitCenterName", getProfitCenterName())
                .append("costCenterId", getCostCenterId())
                .append("costCenterName", getCostCenterName())
                .append("createDate", getCreateDate())
                .append("companyCode", getCompanyCode())
                .append("isCategory", getIsCategory())
                .append("creditAccountCode", getCreditAccountCode())
                .append("creditAccountName", getCreditAccountName())
                .append("id", getId())
                .append("isPrepay", getIsPrepay())
                .append("taxAdjustType", getTaxAdjustType())
                .append("taxAdjustSum", getTaxAdjustSum())
                .append("taxAdjustComments", getTaxAdjustComments())
                .append("assessDataUrl", getAssessDataUrl())
                .append("provinceCode", getProvinceCode())
                .append("pickingMode", getPickingMode())
                .append("budgetGroupItemCode", getBudgetGroupItemCode())
                .append("budgetGroupItemName", getBudgetGroupItemName())
                .append("responseCenterCode", getResponseCenterCode())
                .append("responseCenterName", getResponseCenterName())
                .append("attchmentgAdd", getAttchmentgAdd())
                .append("isTaxAdjust", getIsTaxAdjust())
                .append("abstractValue", getAbstractValue())
                .append("fillInProfitCenterId", getFillInProfitCenterId())
                .append("fillInCostCenterId", getFillInCostCenterId())
                .append("fillInProfitCenterName", getFillInProfitCenterName())
                .append("fillInCostCenterName", getFillInCostCenterName())
                .append("replenishFillIn", getReplenishFillIn())
                .append("fillInRoleId", getFillInRoleId())
                .append("beAfterOperateSum", getBeAfterOperateSum())
                .append("feeType", getFeeType())
                .append("happenDate", getHappenDate())
                .append("sapRemarkDate", getSapRemarkDate())
                .append("clearSum", getClearSum())
                .append("preusedAndShareLabel", getPreusedAndShareLabel())
                .append("auditShareSum", getAuditShareSum())
                .append("firstShareSum", getFirstShareSum())
                .append("shareAccountCode", getShareAccountCode())
                .append("shareAccountName", getShareAccountName())
                .append("contractno", getContractno())
                .append("moveTypeCode", getMoveTypeCode())
                .append("moveTypeName", getMoveTypeName())
                .append("usage", getUsage())
                .append("usagedetail", getUsagedetail())
                .append("budgetsetname", getBudgetsetname())
                .append("disposemode", getDisposemode())
                .append("iscanupdate", getIscanupdate())
                .append("usagedetailCode", getUsagedetailCode())
                .append("archiveEntryType", getArchiveEntryType())
                .append("budgetsetid", getBudgetsetid())
                .append("cashBudgetInstanceCode", getCashBudgetInstanceCode())
                .append("cashBudgetInstanceName", getCashBudgetInstanceName())
                .append("loanBudgetInstanceCode", getLoanBudgetInstanceCode())
                .append("loanBudgetInstanceName", getLoanBudgetInstanceName())
                .append("telephone", getTelephone())
                .append("onceSupplierCity", getOnceSupplierCity())
                .append("onceSupplierName", getOnceSupplierName())
                .append("isOnceSupplier", getIsOnceSupplier())
                .append("isUnifyPay", getIsUnifyPay())
                .append("creditAccountCompanyCode", getCreditAccountCompanyCode())
                .append("salaryCode", getSalaryCode())
                .append("isProvincePay", getIsProvincePay())
                .append("contractName", getContractName())
                .append("contractSum", getContractSum())
                .append("bizTypeCode", getBizTypeCode())
                .append("bizEntryCode", getBizEntryCode())
                .append("contractDescription", getContractDescription())
                .append("payoffDescription", getPayoffDescription())
                .append("projectName", getProjectName())
                .append("supplierCompanyCode", getSupplierCompanyCode())
                .append("bankCode", getBankCode())
                .append("isSplitCredit", getIsSplitCredit())
                .append("unDisplayCode", getUnDisplayCode())
                .append("buildFee", getBuildFee())
                .append("writeoffCheckInfoId", getWriteoffCheckInfoId())
                .append("isImageAudited", getIsImageAudited())
                .append("clearAccount", getClearAccount())
                .append("existClearAccount", getExistClearAccount())
                .append("carryoverInstanceid", getCarryoverInstanceid())
                .append("auditReason", getAuditReason())
                .append("contractInvoiceSum", getContractInvoiceSum())
                .append("sapCertificateCode", getSapCertificateCode())
                .append("adjustType", getAdjustType())
                .append("isPayment", getIsPayment())
                .append("isEmergency", getIsEmergency())
                .append("shareBeginYear", getShareBeginYear())
                .append("shareBeginMonth", getShareBeginMonth())
                .append("shareEndYear", getShareEndYear())
                .append("shareEndMonth", getShareEndMonth())
                .append("businessType", getBusinessType())
                .append("location", getLocation())
                .append("auditHappenDate", getAuditHappenDate())
                .append("companyNameTxt", getCompanyNameTxt())
                .append("isGdtelInvoice", getIsGdtelInvoice())
                .append("relativeTypeCode", getRelativeTypeCode())
                .append("relativeTypeName", getRelativeTypeName())
                .append("isNewContract", getIsNewContract())
                .append("outGoodsSum", getOutGoodsSum())
                .append("payableTaxfeeSum", getPayableTaxfeeSum())
                .append("bizEntryName", getBizEntryName())
                .append("tradeType", getTradeType())
                .append("currency", getCurrency())
                .append("isSapFlag", getIsSapFlag())
                .append("cashItemName", getCashItemName())
                .append("withholdingCityId", getWithholdingCityId())
                .append("withholdingCityName", getWithholdingCityName())
                .append("bizItemInstId", getBizItemInstId())
                .append("accountCode", getAccountCode())
                .append("accountName", getAccountName())
                .append("accountCompanyCode", getAccountCompanyCode())
                .append("isDebitCredit", getIsDebitCredit())
                .append("bizItemType", getBizItemType())
                .append("isStaffPayment", getIsStaffPayment())
                .append("isCarryover", getIsCarryover())
                .append("timestamp", getTimestamp())
                .append("taxFeeMainId", getTaxFeeMainId())
                .append("riskLevelFlag", getRiskLevelFlag())
                .append("isContractFirstPay", getIsContractFirstPay())
                .append("otherSysMainId", getOtherSysMainId())
                .append("proxyTaxAmount", getProxyTaxAmount())
                .append("invoiceType", getInvoiceType())
                .append("isExistKindGift", getIsExistKindGift())
                .append("kindGiftSum", getKindGiftSum())
                .append("kindGiftTaxSum", getKindGiftTaxSum())
                .append("isInputTax", getIsInputTax())
                .append("inputTaxSum", getInputTaxSum())
                .append("isVatAuthenticated", getIsVatAuthenticated())
                .append("inputTaxTurnSum", getInputTaxTurnSum())
                .append("inputTaxTurnBizType", getInputTaxTurnBizType())
                .append("isExcpayment", getIsExcpayment())
                .append("contractUsedTimes", getContractUsedTimes())
                .append("isGeneralPayer", getIsGeneralPayer())
                .append("isNeedImage", getIsNeedImage())
                .append("showDataUrl", getShowDataUrl())
                .append("writeoffArchiveId", getWriteoffArchiveId())
                .append("writeoffInstanceCode", getWriteoffInstanceCode())
                .append("formTypeCode", getFormTypeCode())
                .append("sum", getSum())
                .append("auditSum", getAuditSum())
                .append("payoffSum", getPayoffSum())
                .append("auditPayoffSum", getAuditPayoffSum())
                .append("fillInAccount", getFillInAccount())
                .append("fillInName", getFillInName())
                .append("fillInDep", getFillInDep())
                .append("formAmount", getFormAmount())
                .append("paytaxattr", getPaytaxattr())
                .append("busihappendtimeflag", getBusihappendtimeflag())
                .append("operatorid", getOperatorid())
                .append("processinstid", getProcessinstid())
                .append("selectpreid", getSelectpreid())
                .append("guid", getGuid())
                .append("parentid", getParentid())
                .append("exceptiontype", getExceptiontype())
                .append("sapcreator", getSapcreator())
                .append("sapcompanycode", getSapcompanycode())
                .append("year", getYear())
                .append("billtype", getBilltype())
                .append("iresult", getIresult())
                .append("suppliertype", getSuppliertype())
                .append("orgid", getOrgid())
                .append("energytype", getEnergytype())
                .toString();
    }

    private Date createDateStart;
    private Date createDateEnd;
    private Boolean _disabled;

    //责任中心 1001,1002,1003
    private List<Map<String, Object>> countrys;
    private Long country;


    @Override
    public String getAuditKey() {
        return this.getId() + "";
    }

    @Override
    public boolean isAvailable() {
        return RefereeDatasource.super.isAvailable();
    }

    public boolean Unfinsh(Long id, ConcurrentHashMap<Long, BillExecuteState> executeMap) {
        if (executeMap.get(id) == null) {
            return true;
        }
        if (executeMap.get(id) == BillExecuteState.EXCEPTION) {
            return true;
        }
        if (executeMap.get(id) == BillExecuteState.GetStatusFail) {
            return true;
        }
        if (executeMap.get(id) == BillExecuteState.EXECUTE) {
            return false;
        }
        if (executeMap.get(id) == BillExecuteState.EXIST) {
            return false;
        }
        if (executeMap.get(id) == BillExecuteState.GetStatusSuccess) {
            return false;
        }
        return true;
    }

    public boolean unfinshPool(ConcurrentHashMap<Long, BillExecuteState> pollMap) {
        if (pollMap.get(this.getId()) == null) {
            return true;
        }
        if (pollMap.get(this.getId()) == BillExecuteState.EXECUTE) {
            return true;
        }
        if (pollMap.get(this.getId()) == BillExecuteState.EXCEPTION) {
            return false;
        }
        if (pollMap.get(this.getId()) == BillExecuteState.GetStatusFail) {
            return false;
        }
        return false;

    }
}
