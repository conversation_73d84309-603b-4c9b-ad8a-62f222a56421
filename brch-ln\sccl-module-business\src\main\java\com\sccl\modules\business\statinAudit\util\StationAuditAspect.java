package com.sccl.modules.business.statinAudit.util;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.business.account.domain.Account;
import com.sccl.modules.business.account.domain.AccountBaseResult;
import com.sccl.modules.business.account.mapper.AccountMapper;
import com.sccl.modules.business.account.service.IAccountService;
import com.sccl.modules.business.accountEs.domain.AccountEsResult;
import com.sccl.modules.business.accountbillitempre.service.IAccountbillitempreService;
import com.sccl.modules.business.accountbillpre.domain.Accountbillpre;
import com.sccl.modules.business.poweraudit.entity.PowerAuditEntity;
import com.sccl.modules.business.statinAudit.controller.WebSocket;
import com.sccl.modules.business.statinAudit.domain.AuditResults;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;
import com.sccl.modules.mssaccount.rbillitemaccount.domain.RBillitemAccount;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @Author: 李佳杰
 * @CreateTime: 2024-02-19  15:09
 * @Description: 环绕通知调用稽核内容
 * @Version: 1.0
 */
@Aspect
@Component
@Data
@Order(1)
@Slf4j
public class StationAuditAspect {
    @Pointcut("@annotation(com.sccl.modules.business.statinAudit.aspct.StationAuditAnnotation)")
    private void stationAuditAspect() {
    }

    @Autowired
    private IAccountbillitempreService accountbillitempreService;
    @Autowired
    private IAccountService aaccountService;
    @Value("${sccl.deployTo}")
    private String deployTo;
    @Resource
    private WebSocket webSocket;

    @Autowired
    private AccountMapper accountmapper;

    /**
     * @param joinPoint
     * @Description: 环绕通知拦截非法请求（拦截）
     * @return: java.lang.Object
     * @throws: none
     */
    @Around("@annotation(com.sccl.modules.business.statinAudit.aspct.StationAuditAnnotation)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        StationAuditUtil.loadBean();
        Object[] args = new Object[0];
        args = new Object[0];

        /**数据查询预估台账还是自有台账*/
        Boolean yugu = false;
        Boolean jh = false;
        args = joinPoint.getArgs();
        String ymmc = null;
        try {
            //获取object中的data属性
            Object target1 = joinPoint.getTarget();
            /**方法名*/
            Field field = null;
            Field field2 = null;
            String checkJh = null;


            if (args[0].toString().startsWith("[") && args[0].toString().endsWith("]")) {
                field = ((ArrayList) args[0]).get(0).getClass().getDeclaredField("jh");
                field2 = ((ArrayList) args[0]).get(0).getClass().getDeclaredField("ymmc");
                field.setAccessible(true);//设置data属性为可访问的
                field2.setAccessible(true);//设置data属性为可访问的
                checkJh = (String) field.get(((ArrayList) args[0]).get(0));
                ymmc = (String) field2.get(((ArrayList) args[0]).get(0));

            } else {
                field = args[0].getClass().getDeclaredField("jh");
                field.setAccessible(true);//设置data属性为可访问的
                checkJh = (String) field.get(args[0]);
                field2 = args[0].getClass().getDeclaredField("jh");
                field2.setAccessible(true);//设置data属性为可访问的
                ymmc = (String) field.get(args[0]);

            }


            if (checkJh != null && "1".equals(checkJh)) {
                jh = true;
            }

        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
        }

        if ("sc".equals(deployTo)) {
            if (jh) {
                AtomicReference<Map<String, Object>> map = new AtomicReference<Map<String, Object>>();
                AtomicReference<BigDecimal> sum = new AtomicReference<>(new BigDecimal(0));
                Boolean checkMssAccountbill = false;
                Object target = joinPoint.getArgs()[0];
                ;
                List<Account> modes = new ArrayList();
                Object target1 = joinPoint.getTarget();
                String name = target1.getClass().getName();
                List<MssAccountbill> mssAccountbills = new ArrayList<>();
                /**判断是否是报账单还是台账*/
                if ("com.sccl.modules.mssaccount.mssaccountbill.controller.MssAccountbillController".equals(name)) {
                    checkMssAccountbill = true;
                    /**保障单集合*/
                    if (target.toString().startsWith("[") && target.toString().endsWith("]")) {
                        mssAccountbills = JSON
                                .parseArray(JSONObject.toJSONString(target), MssAccountbill.class);
                        for (MssAccountbill mssAccountbill : mssAccountbills) {
                            //                           Accountbillpre accountbillpre = mssAccountbill.getAccountbillpre();
                            //                           List<Long> longs = selectPcidsBygjd(accountbillpre);
                            /**台账Id*/
                            List<Long> accountIds = mssAccountbill.getItem().stream().flatMap(
                                    item -> item.getRbillitemaccount().stream().map(RBillitemAccount::getAccountId)
                            ).collect(Collectors.toList());
                            if (CollectionUtils.isEmpty(accountIds)) {
                                throw new RuntimeException("当前稽核没有选择相应台账明细数据，无法开启");
                            }
                            //自有台账
                            List<AccountBaseResult> getids = aaccountService.getids(accountIds);
                            //自有预估台账
                            List<AccountBaseResult> getEsIds = aaccountService.getEsIds(accountIds);
                            getids.addAll(getEsIds);
                            if (CollectionUtils.isEmpty(getids)) {
                                throw new RuntimeException("当前稽核没有选择相应台账明细数据，无法开启");
                            }
                            //
                            List<Account> getidsAccount = new ArrayList<Account>();

                            getids.stream().forEach(accountBaseResult -> {
                                Account account = new Account();
                                try {
                                    account = copyProperties(accountBaseResult, account);
                                    getidsAccount.add(account);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }

                            });
                            getidsAccount.stream().forEach(account -> account.setBillId(mssAccountbill.getId()));
                            modes.addAll(getidsAccount);
                        }

                    } else {
                        MssAccountbill mssAccountbill = JSON
                                .parseObject(JSONObject.toJSONString(target), MssAccountbill.class);
                        mssAccountbills.add(mssAccountbill);
                        //                       Accountbillpre accountbillpre = mssAccountbill.getAccountbillpre();
                        //                       accountbillpre.setId(Long.valueOf(accountbillpre.getPabrid()));
                        //                       List<Long> longs = selectPcidsBygjd(accountbillpre);
                        /**台账Id*/
                        List<Long> accountIds = mssAccountbill.getItem().stream().flatMap(
                                item -> item.getRbillitemaccount().stream().map(RBillitemAccount::getAccountId)
                        ).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(accountIds)) {
                            throw new RuntimeException("当前稽核没有选择相应台账明细数据，无法开启");
                        }
                        List<AccountBaseResult> getids = aaccountService.getids(accountIds);
                        List<AccountBaseResult> getEsIds = aaccountService.getEsIds(accountIds);
                        getids.addAll(getEsIds);
                        if (CollectionUtils.isEmpty(getids)) {
                            throw new RuntimeException("当前稽核没有选择相应台账明细数据，无法开启");
                        }
                        List<Account> getidsAccount = new ArrayList<Account>();
                        getids.stream().forEach(accountBaseResult -> {
                            Account account = new Account();
                            try {
                                account = copyProperties(accountBaseResult, account);
                                getidsAccount.add(account);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                        });
                        getidsAccount.stream().forEach(account -> account.setBillId(mssAccountbill.getId()));
                        modes.addAll(getidsAccount);


                    }
                    /**对应相应的台账*/
                } else {
                    /**获取相对于的台账数据*/
                    if (target.toString().startsWith("[") && target.toString().endsWith("]")) {
                        modes = JSON
                                .parseArray(JSONObject.toJSONString(target), Account.class);
                    } else {
                        modes.add(JSON
                                .parseObject(JSONObject.toJSONString(target), Account.class));
                    }
                }

                /**获取入参对象数据*/
                Boolean finalCheckMssAccountbill = checkMssAccountbill;
                List<MssAccountbill> finalMssAccountbills = mssAccountbills;

                Integer totalSize = modes.size();
                Integer succsessSize = 0;
                Integer fialSize = 0;
                final Integer[] page = {1};


                if (modes.size() != 0 && modes.get(0).getPcid() == null) {
                    modes.stream().forEach(account -> {
                        if (account.getPcid() == null) {
                            account.setPcid(IdWorker.getId());
                        }
                    });
                    yugu = true;
                }
                // 新增 只针对电表协议中用电类型类型为生产用电—移动基站的，其他的不用稽核
                List<String> coinResultIds = StationAuditUtil.filterEleType(modes);


                /**稽核数据*/
                String finalYmmc = ymmc;
                modes.stream().forEach(account -> {
                    try {
                        // 若是包含 则做稽核
                        if(CollectionUtil.isNotEmpty(coinResultIds)&&
                                (coinResultIds.contains(account.getAmmetercode())||
                                        coinResultIds.contains(account.getAmmetername()) )){
                            map.set(StationAuditUtil.checkStationAudit(finalCheckMssAccountbill, account,
                                    finalMssAccountbills, finalYmmc));
                        } else {
                            map.set(StationAuditUtil.successStationAudit(finalCheckMssAccountbill, account,
                                    finalMssAccountbills, finalYmmc));
                        }
                        PowerAuditEntity powerAuditEntity  = (PowerAuditEntity) map.get().get("稽核结果");
                        /**websocket数据传输封装*/
                        AuditResults auditResults = new AuditResults();
                        auditResults.setPowerAuditEntity(powerAuditEntity);
                        Integer ifQkSuccess = powerAuditEntity.getIfQkSuccess();
                        /**总量*/
                        auditResults.setTotalSize(totalSize);
                        /**台账稽核结果列表*/
                        auditResults.setMsg(powerAuditEntity.getAmmeterid() + "电表、期号" + powerAuditEntity.getLedgerPeriod() + "已稽核、" + ((ifQkSuccess == 1) ? "通过" : "未通过"));
                        auditResults.setProgress(new BigDecimal(page[0]).divide(new BigDecimal(totalSize), 2, RoundingMode.HALF_UP).toString());

                        /***/
                        if (1 == ifQkSuccess) {
                            auditResults.setStaute("成功");
                            auditResults.setSuccessCount(succsessSize + 1);

                        } else {
                            auditResults.setStaute("失败");
                            auditResults.setFailCount(fialSize + 1);

                        }

                        //创建业务消息信息


                        //单个用户发送 (userId为用户id)
                        String loginId = ShiroUtils.getUser().getLoginId();
                        webSocket.sendOneMessage(ShiroUtils.getUser().getLoginId(), JSONObject.toJSONString(auditResults));
                        page[0]++;
                        if (finalCheckMssAccountbill) {
                            if (map.get().get("台账总和") == null) {
                                sum.set(new BigDecimal(0));
                            } else {
                                String sumInt = String.valueOf(map.get().get("台账总和"));
                                sum.set(new BigDecimal(String.valueOf(map.get().get("台账总和"))).add(sum.get()));
                            }


                        }
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                });


                /***报账单设置业务电量*/
                if (finalCheckMssAccountbill) {
                    /**设置总和*/
                    Object additionalParam = sum.get();
                    try {
                        //获取object中的data属性
                        Field field = args[0].getClass().getDeclaredField("businessElectricity");
                        field.setAccessible(true);//设置data属性为可访问的
                        if (additionalParam != null) {
                            field.set(args[0], additionalParam);
                        }
                    } catch (NoSuchFieldException | IllegalAccessException e) {
                        e.printStackTrace();
                    }

                }

                return AjaxResult.success();
            }
        }


        return joinPoint.proceed(args);//回调连接点方法
    }

    private static HashMap<String, Class> map = new HashMap<String, Class>() {
        {
            put("java.lang.Integer", int.class);
            put("java.lang.Double", double.class);
            put("java.lang.Float", float.class);
            put("java.lang.Long", long.class);
            put("java.lang.Short", short.class);
            put("java.lang.Boolean", boolean.class);
            put("java.lang.Char", char.class);
        }
    };

    //返回方法的参数名
    private static String[] getFieldsName(ProceedingJoinPoint joinPoint) throws ClassNotFoundException, NoSuchMethodException {

        String classType = joinPoint.getTarget().getClass().getName();
        String methodName = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();
        Class<?>[] classes = new Class[args.length];
        for (int k = 0; k < args.length; k++) {
            if (!args[k].getClass().isPrimitive()) {
                //获取的是封装类型而不是基础类型
                String result = args[k].getClass().getName();
                Class s = map.get(result);
                classes[k] = s == null ? args[k].getClass() : s;
            }
        }
        ParameterNameDiscoverer pnd = new DefaultParameterNameDiscoverer();
        //获取指定的方法，第二个参数可以不传，但是为了防止有重载的现象，还是需要传入参数的类型
        Method method = Class.forName(classType).getMethod(methodName, classes);
        String[] parameterNames = pnd.getParameterNames(method);
        return parameterNames;
    }

    public List<Long> selectPcidsBygjd(Accountbillpre accountbillitempre) {
        List list = new ArrayList();
        Map<String, Object> map = new HashMap<>();
        map.put("parid", accountbillitempre.getId());
        map.put("status", -1);

        String status = String.valueOf(accountbillitempre.getStatus());
        accountbillitempre.setStatus(null);
        if ("1".equals(status) || "3".equals(status) || "9".equals(status)) {// 自有/铁塔台账、包干
            map.put("union", false);
            List<AccountBaseResult> accountBaseResults = accountbillitempreService
                    .selectAccountByAutoMap(map);
            list = accountBaseResults.stream().map(AccountBaseResult::getPcid)
                    .collect(Collectors.toList());
//            list = accountbillitempreService.selectList(accountbillitempre);
        } else if ("2".equals(status) || "7".equals(status) || "11".equals(status) || "13".equals(status) || "15".equals(status) || "17".equals(status)) {// 预估/铁塔预估台账
            map.put("union", false);
            List<AccountEsResult> accountEsResults = accountbillitempreService
                    .selectAccountEsByAutoMap(map);
            list = accountEsResults.stream().map(AccountEsResult::getPcid)
                    .collect(Collectors.toList());
        } else if ("4".equals(status) || "6".equals(status) || "10".equals(status)) {// 自有/铁塔、包干 合并归集单
            map.put("union", true);
            List<AccountBaseResult> accountBaseResults = accountbillitempreService
                    .selectAccountByAutoMap(map);
            list = accountBaseResults.stream().map(AccountBaseResult::getPcid)
                    .collect(Collectors.toList());
        } else if ("5".equals(status) || "8".equals(status) || "12".equals(status) || "14".equals(status) || "16".equals(status) || "18".equals(status)) {// 预估/铁塔预估 合并归集单
            map.put("union", true);
            List<AccountEsResult> accountEsResults = accountbillitempreService
                    .selectAccountEsByAutoMap(map);
            list = accountEsResults.stream().map(AccountEsResult::getPcid)
                    .collect(Collectors.toList());
        } else {// 默认查询 自有台账
            map.put("union", false);
            List<AccountBaseResult> accountBaseResults = accountbillitempreService
                    .selectAccountByAutoMap(map);
            list = accountBaseResults.stream().map(AccountBaseResult::getPcid)
                    .collect(Collectors.toList());
        }
        return list;
    }

    public static <T, S> T copyProperties(S source, T target) throws Exception {
        Class<?> sourceClass = source.getClass();
        Class<?> targetClass = target.getClass();

        Field[] sourceFields = sourceClass.getDeclaredFields();
        for (Field sourceField : sourceFields) {
            String fieldName = sourceField.getName();
            Field targetField = null;
            try {
                targetField = targetClass.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                // 目标对象不存在该属性，忽略
                continue;
            }

            sourceField.setAccessible(true);
            targetField.setAccessible(true);
            String s = sourceField.getType().toString();
            if (!sourceField.getType().toString().equals(targetField.getType().toString())) {
                continue;
            }

            Object value = sourceField.get(source);
            targetField.set(target, value);
        }

        return target;
    }

}
