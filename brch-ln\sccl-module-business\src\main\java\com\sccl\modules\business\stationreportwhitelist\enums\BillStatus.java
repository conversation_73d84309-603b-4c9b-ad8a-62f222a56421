package com.sccl.modules.business.stationreportwhitelist.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/4/22 16:32
 * @describe 流程状态
 */
@Getter
@AllArgsConstructor
public enum BillStatus {
    DRAFT(0, "草稿"),
    PROCESSING(1, "流程中"),
    COMPLETED(2, "已完成"),
    MODIFYING(3, "修改中"),
    MODIFY_PROCESSING(4, "修改流程中"),
    REMOVE(5,"移除")
    ;

    /**
     * 状态码
     */
    private final Integer code;

     /**
     * 状态名称
     */
    private final String name;

    /**
     * 根据code获取name
     */
    public static String getNameByCode(Integer code) {
        for (BillStatus billStatus : BillStatus.values()) {
            if (billStatus.getCode().equals(code)) {
                return billStatus.getName();
            }
        }
        return null;
    }
}
