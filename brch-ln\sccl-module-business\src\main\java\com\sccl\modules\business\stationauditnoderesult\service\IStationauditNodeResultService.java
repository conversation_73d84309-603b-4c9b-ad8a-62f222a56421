package com.sccl.modules.business.stationauditnoderesult.service;

import com.enrising.dcarbon.audit.RefereeResult;
import com.sccl.modules.business.stationauditnoderesult.domain.StationauditNodeResult;
import com.sccl.framework.service.IBaseService;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;

import java.util.List;

/**
 * 基站一站式稽核结果 服务层
 * 
 * <AUTHOR>
 * @date 2022-11-16
 */
public interface IStationauditNodeResultService extends IBaseService<StationauditNodeResult>
{


    /**
     * 将责任链的结果 插入到数据库
     * @param refereeResults
     * @param mssAccountbill
     * @return
     */
    int insertListResults(List<RefereeResult> refereeResults);
}
