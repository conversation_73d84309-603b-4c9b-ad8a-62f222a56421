package com.sccl.modules.business.noderesult.mapper;

import com.sccl.modules.business.noderesult.domain.NodeResult;
import com.sccl.framework.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 基站一站式稽核结果 数据层
 * 
 * <AUTHOR>
 * @date 2022-11-22
 */
public interface NodeResultMapper extends BaseMapper<NodeResult>
{


    /**
     * 获取 billId 最新时间的 noderesult
     * @param nodeResult
     * @return
     */
    List<NodeResult> selectListLatestTime( NodeResult nodeResult);

    /**
     * 去node result表 找 台账数据 如果有多条，找最新的
     * @param nodeResult
     * @return
     */
    NodeResult selectupToDateNode(NodeResult nodeResult);

    /**
     * 去找 本次台账 同一站址的 上次台账
     * @param powerNode
     * @return
     */
    NodeResult selectNodeLast(NodeResult powerNode);

    List<NodeResult> getIds(List<Long> ids);
}