package com.sccl.modules.rental.rentalordercarmodel.service;

import com.sccl.modules.rental.rentalordercarmodel.domain.RentalorderCarmodel;
import com.sccl.framework.service.IBaseService;

import java.util.List;
import java.util.Map;

/**
 * 车型数据 服务层
 * 
 * <AUTHOR>
 * @date 2019-08-23
 */
public interface IRentalorderCarmodelService extends IBaseService<RentalorderCarmodel>
{


    List<RentalorderCarmodel> selectAndNameByRcoid(Long id);
}
