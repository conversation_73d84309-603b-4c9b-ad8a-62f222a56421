package com.sccl.modules.business.stationaudit.pstationchangesamemeter;


import com.enrising.dcarbon.audit.AbstractRefereeContent;
import com.enrising.dcarbon.audit.RefereeDatasource;
import com.enrising.dcarbon.audit.RefereeResult;
import lombok.Data;

@Data
public class StationChangeSameMeterRefereeContent extends AbstractRefereeContent implements RefereeDatasource {
    private Long billId;
    private Long pcid;
    private String stationCode;
    /**
     * 台账对应的电表id
     */
    private Long ammeterid;
    /**
     * 项目名称
     */
    private String projectname;
    /**
     * 局站名称
     */
    private String stationname;
    /**
     * 局站地址
     */
    private String stationaddress;
    /**
     * 同一电表 的异常站址信息
     *
     * @return
     */
    private String stationcodelast_samemeter;

    public StationChangeSameMeterRefereeContent(RefereeResult refereeResult, int step, String auditKey) {
        super(refereeResult, step, auditKey);
    }

    public StationChangeSameMeterRefereeContent() {
    }
}
