package com.sccl.modules.business.powerstationquarru.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.business.powerstationquarru.domain.PowerStationQuaRru;
import com.sccl.modules.business.powerstationquarru.service.IPowerStationQuaRruService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 站址rru能耗指标 信息操作处理
 * 
 * <AUTHOR>
 * @date 2021-09-14
 */
@RestController
@RequestMapping("/business/powerStationQuaRru")
public class PowerStationQuaRruController extends BaseController
{
    private String prefix = "business/powerStationQuaRru";
	
	@Autowired
	private IPowerStationQuaRruService powerStationQuaRruService;
	
	@RequiresPermissions("business:powerStationQuaRru:view")
	@GetMapping()
	public String powerStationQuaRru()
	{
	    return prefix + "/powerStationQuaRru";
	}
	
	/**
	 * 查询站址rru能耗指标列表
	 */
	@RequiresPermissions("business:powerStationQuaRru:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(PowerStationQuaRru powerStationQuaRru)
	{
		startPage();
        List<PowerStationQuaRru> list = powerStationQuaRruService.selectList(powerStationQuaRru);
		return getDataTable(list);
	}
	
	/**
	 * 新增站址rru能耗指标
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存站址rru能耗指标
	 */
	@RequiresPermissions("business:powerStationQuaRru:add")
	@Log(title = "站址rru能耗指标", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody PowerStationQuaRru powerStationQuaRru)
	{		
		return toAjax(powerStationQuaRruService.insert(powerStationQuaRru));
	}

	/**
	 * 修改站址rru能耗指标
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		PowerStationQuaRru powerStationQuaRru = powerStationQuaRruService.get(id);

		Object object = JSONObject.toJSON(powerStationQuaRru);

        return this.success(object);
	}
	
	/**
	 * 修改保存站址rru能耗指标
	 */
	@RequiresPermissions("business:powerStationQuaRru:edit")
	@Log(title = "站址rru能耗指标", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody PowerStationQuaRru powerStationQuaRru)
	{		
		return toAjax(powerStationQuaRruService.update(powerStationQuaRru));
	}
	
	/**
	 * 删除站址rru能耗指标
	 */
	@RequiresPermissions("business:powerStationQuaRru:remove")
	@Log(title = "站址rru能耗指标", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(powerStationQuaRruService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看站址rru能耗指标
     */
    @RequiresPermissions("business:powerStationQuaRru:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		PowerStationQuaRru powerStationQuaRru = powerStationQuaRruService.get(id);

        Object object = JSONObject.toJSON(powerStationQuaRru);

        return this.success(object);
    }

}
