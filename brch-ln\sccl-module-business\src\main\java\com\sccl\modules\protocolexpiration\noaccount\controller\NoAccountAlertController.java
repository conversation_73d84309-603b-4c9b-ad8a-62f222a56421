package com.sccl.modules.protocolexpiration.noaccount.controller;

import com.sccl.common.utils.ShiroUtils;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol;
import com.sccl.modules.protocolexpiration.noaccount.service.INoAccountAlertService;
import com.sccl.modules.system.organization.domain.Organization;
import com.sccl.modules.system.user.domain.User;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 台账 信息操作处理
 * 
 * <AUTHOR>
 * @date 2019-05-20
 */
@RestController
	@RequestMapping("/alertnoaccount/account")
public class NoAccountAlertController extends BaseController
{
    private String prefix = "protocolexpiration/account";
	
	@Autowired
	private INoAccountAlertService noAccountAlertService;
	
	@RequiresPermissions("protocolexpiration:account:view")
	@GetMapping()
	public String account()
	{
	    return prefix + "/account";
	}
	
	/**
	 * 查询台账列表
	 */
	@RequiresPermissions("alertnoaccount:account:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(Ammeterorprotocol ammeterorprotocol)
	{
		startPage();
        List<Ammeterorprotocol> list = noAccountAlertService.selectNoAccountList(ammeterorprotocol);
		return getDataTable(list);
	}

	/**
	 * 获取责任中心
	 * @param parentcompanyno
	 * @return
	 */
	@RequiresPermissions("alertnoaccount:account:centerlist")
	@RequestMapping("/centerlist")
	@ResponseBody
	public List<Organization> list(@RequestParam("parentcompanyno") String parentcompanyno)
	{
		User user = ShiroUtils.getUser();
		user.getCompanies();
		List<Organization> organizationList = noAccountAlertService.organizationList(parentcompanyno);
		return organizationList;
	}

	//@RequiresPermissions("alertnoaccount:account:compnyList")
	@RequestMapping("/compnyList")
	@ResponseBody
	public List<IdNameVO> compnyList()
	{
		User user = ShiroUtils.getUser();

		return user.getCompanies();
	}

	/**
	 * 获取责任中心
	 * @param parentcompanyno
	 * @return
	 */
	@RequestMapping("/centerIdNamelist")
	@ResponseBody
	public List<Organization> reslist(@RequestParam("parentcompanyno") String parentcompanyno)
	{
		User user = ShiroUtils.getUser();
		user.getCompanies();
		List retList = new ArrayList();
		List<Organization> organizationList = noAccountAlertService.organizationList(parentcompanyno);
		for(int i=0;i<organizationList.size();i++){
			Map tempMap=new HashMap();
			tempMap.put("id",organizationList.get(i).getIdForStr());
			tempMap.put("name",organizationList.get(i).getOrgName());
			retList.add(tempMap);
		}
		return retList;
	}
}
