package com.sccl.modules.business.oilreimbursement.controller.imp;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.sccl.common.lang.StringUtils;
import com.sccl.common.utils.*;
import com.sccl.framework.service.IdGenerator;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.modules.business.energyaccountbillpre.domain.EnergyAccountbillpre;
import com.sccl.modules.business.energyaccountbillpre.service.IEnergyAccountbillpreService;
import com.sccl.modules.business.oilreimbursement.controller.OilReimbursementController;
import com.sccl.modules.business.oilreimbursement.service.OilReimbursementService;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;
import com.sccl.modules.mssaccount.mssaccountbill.service.IMssAccountbillService;
import com.sccl.modules.mssaccount.mssaccountbillitem.domain.MssAccountbillitem;
import com.sccl.modules.mssaccount.rbillitemaccount.domain.RBillitemAccount;
import com.sccl.modules.system.user.domain.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;

/**
 * @Description 报账控制层实现类
 * @Auther Huang Yongxiang
 * @Date 2021/12/21 10:45
 */
@RestController
@RequestMapping("/business/energy_reimbursement")
public class OilReimbursementControllerImp implements OilReimbursementController {

    @Autowired
    private OilReimbursementService service;
    @Autowired
    private IEnergyAccountbillpreService accountbillpreServiceservice;
    @Autowired
    private IMssAccountbillService mssAccountbillService;

    /**
     * 是否开启测试模式，开启后接口将不会验证User信息
     */
    private final boolean isOpenTest = true;


    @Override
    @PostMapping(value = "/create_new_reimbursement", produces = "application/json;charset=UTF-8")
    public String createNewReimbursementBill(@RequestParam("PABRID") String pabrid, @RequestParam("TYPE") String type) {
        User user = null;
        String deptName = null;
        String orgid = null;
        if (!isOpenTest) {
            user = ShiroUtils.getUser();
            if (user == null) {
                return MessageMaster.getMessage(MessageMaster.Code.FORBIDDEN, "登录已过期，请重新登录");
            }
            List<IdNameVO> departments = user.getDepartments();
            if (departments != null && departments.size() > 0) {
                deptName = departments.get(0).getName();
                orgid = departments.get(0).getId();
            } else {
                return MessageMaster.getMessage(MessageMaster.Code.FORBIDDEN, "没有查询到部门信息");
            }
        }

        if (StringUtils.isEmpty(pabrid) || !RegexUtil.isMatch(pabrid, RegexUtil.Type.INTEGER) || (!StringUtils.isEmpty(type) && !RegexUtil.isMatch(type, "^[1-3]$"))) {
            return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "参数为空或不合法:" + pabrid);
        }
        /*=======================生成报账单基础信息======================>*/
        MssAccountbill mssAccountbill = new MssAccountbill();
        long nextId = IdGenerator.getNextId();
        mssAccountbill.setId(nextId);
        mssAccountbill.setStatus(1);
        mssAccountbill.setBusihappendtimeflag("2");
        mssAccountbill.setEnergytype(new BigDecimal(type));
        mssAccountbill.setBudgetsetname(DateUtils.formatDate(new Date(), "yyyy-MM"));
        mssAccountbill.setHappenDate(DateUtils.formatDate(new Date(), "yyyy-MM-dd"));
        if (!isOpenTest) {
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0) {
                mssAccountbill.setCompanyCode(companies.get(0).getId());
            } else {
                return MessageMaster.getMessage(MessageMaster.Code.FORBIDDEN, "没有查询到公司信息");
            }
            mssAccountbill.setOperatorid(BigDecimal.valueOf(user.getId()));
            mssAccountbill.setFillInAccount(user.getLoginId());
            mssAccountbill.setGuid(user.getHrLoginId());//报账 需要 用到 的account 为 hrlogingId 放到 guid里面
            mssAccountbill.setFillInName(user.getUserName());
            mssAccountbill.setFillInDep(deptName);
            mssAccountbill.setOrgid(Long.valueOf(orgid));
            mssAccountbill.setTelephone(user.getPhone());//
            try {
                MssAccountbill m = new MssAccountbill();
                m.setFillInAccount(user.getLoginId());
                m = mssAccountbillService.getByOldOne(m);
                if (m != null && StringUtils.isNotEmpty(m.getCompanyNameTxt()))
                    mssAccountbill.setCompanyNameTxt(m.getCompanyNameTxt());
                if (m != null && StringUtils.isNotEmpty(m.getFillInCostCenterId()))
                    mssAccountbill.setFillInCostCenterId(m.getFillInCostCenterId());
                if (m != null && StringUtils.isNotEmpty(m.getFillInCostCenterName()))
                    mssAccountbill.setFillInCostCenterName(m.getFillInCostCenterName());
            } catch (Exception e) {
                e.printStackTrace();
            }
            mssAccountbill.setAbstractValue(deptName + user.getUserName() + ".油费");
        }
        mssAccountbill.setIsStaffPayment("0");
        mssAccountbill.setIsExistKindGift("0");
        mssAccountbill.setPaytaxattr("2");
        mssAccountbill.setIsEmergency("0");//是否加急：" prop="isEmergency" 0 否 1是
        // 改默认为否，四川，辽宁都是
        mssAccountbill.setIsInputTax("0");
        /*=============================================<*/

        /*=======================将该报账单id添加到归集单的关联======================>*/
        //查询归集单详细信息
        EnergyAccountbillpre energyAccountbillpre = accountbillpreServiceservice.selectEnergyAccountBill(Long.parseLong(pabrid));

        if (energyAccountbillpre == null) {
            return MessageMaster.getMessage(MessageMaster.Code.ERROR, "没有相关归集单信息");
        }
        if (StringUtils.isEmpty(energyAccountbillpre.getPabid())) {
            energyAccountbillpre.setPabid(String.valueOf(mssAccountbill.getId()));
        } else {
            energyAccountbillpre.setPabid(energyAccountbillpre.getPabid() + "," + mssAccountbill.getId());
        }
        mssAccountbill.setSum(energyAccountbillpre.getMoney());
        /*=============================================<*/

        /*=======================设置明细对象======================>*/
        MssAccountbillitem mssAccountbillitem = new MssAccountbillitem();
        //设置主表ID
        mssAccountbillitem.setWriteoffInstanceId(new BigDecimal(mssAccountbill.getId()));
        //设置总金额
        mssAccountbillitem.setSum(energyAccountbillpre.getMoney());
        //设置税额
        mssAccountbillitem.setTaxAdjustSum(new BigDecimal("0.0"));
        mssAccountbillitem.setCreateDate(new Date());
        mssAccountbillitem.setCreateTime(new Date());
        if (!isOpenTest) {
            mssAccountbillitem.setCreatorId(user.getId());
            mssAccountbillitem.setCreatorName(user.getUserName());
        }
        /*=============================================<*/


        Map<String, Object> data = new HashMap<>();
        data.put("energyAccountbillpre", energyAccountbillpre);
        data.put("mssAccountbill", mssAccountbill);
        data.put("mssAccountbillitem", mssAccountbillitem);
        MessageMaster master = new MessageMaster();
        master.setCode(MessageMaster.Code.OK);
        master.setMessage("初始化报账单成功");
        master.setData(data);
        return master.toString();
    }

    @Override
    @PostMapping(value = "/create_item_reimbursement", produces = "application/json;charset=UTF-8")
    public String createNewItemReimbursementBill(@RequestBody List<Map<String, Object>> mssAccountItemParams) {
        User user = null;
        if (!isOpenTest) {
            user = ShiroUtils.getUser();
            if (user == null) {
                return MessageMaster.getMessage(MessageMaster.Code.FORBIDDEN, "登录已过期，请重新登录");
            }
        }
        if (mssAccountItemParams == null || mssAccountItemParams.size() == 0) {
            return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "参数为空，创建报账单明细失败");
        }
        int count = 0;
        List<MssAccountbillitem> mssAccountbillitems = new LinkedList<>();
        try {
            /*=======================解析参数======================>*/
            Long mssId = null;
            BigDecimal sum = new BigDecimal("0.0");
            BigDecimal tax = new BigDecimal("0.0");
            List<RBillitemAccount> rBillitemAccountsD = new LinkedList<>();
            for (Map<String, Object> ms : mssAccountItemParams) {
                JsonObject jsonObject = JsonUtil.mapToJsonObj(ms);
                //获取明细对象
                JsonObject item = jsonObject.getAsJsonObject("mssAccountbillitem");
                MssAccountbillitem mssAccountbillitem = JsonUtil.jsonObjToPojo(item.toString(), MssAccountbillitem.class);
                if (mssAccountbillitem.getWriteoffInstanceId() == null) {
                    return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "参数中存在明细对象的主单ID为空，无法新增明细");
                }
                if (mssId == null) {
                    mssId = mssAccountbillitem.getWriteoffInstanceId().longValue();
                } else {
                    if (mssId != mssAccountbillitem.getWriteoffInstanceId().longValue()) {
                        return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "新增的明细只能归属于同一个主单");
                    }
                }
                mssAccountbillitem.setId(IdGenerator.getNextId());

                //获取关联对象，并记录总金额和总税额
                List<RBillitemAccount> rBillitemAccounts = new LinkedList<>();
                JsonArray jsonArray = jsonObject.getAsJsonArray("rBillitemAccount");
                for (int i = 0; i < jsonArray.size(); i++) {
                    RBillitemAccount rBillitemAccount = JsonUtil.jsonObjToPojo(jsonArray.get(i).toString(), RBillitemAccount.class);
                    if (rBillitemAccount.getTaxmoney() == null || rBillitemAccount.getMoney() == null || rBillitemAccount.getAccountMoney() == null) {
                        return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "新增明细失败，关联金额、关联税额以及汇总金额不可为空");
                    }
                    if (rBillitemAccount.getAccountId() == null) {
                        return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "明细信息关联的台账信息为空");
                    }
                    if (rBillitemAccount.getMoney() + rBillitemAccount.getTaxmoney() > rBillitemAccount.getAccountMoney()) {
                        return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "关联金额+关联税额不得大于台账金额");
                    }
                    //汇总报账金额和税额
                    sum = sum.add(BigDecimal.valueOf(rBillitemAccount.getMoney()));
                    tax = tax.add(BigDecimal.valueOf(rBillitemAccount.getTaxmoney()));
                    rBillitemAccount.setId(IdGenerator.getNextId());
                    rBillitemAccount.setBillId(Long.valueOf(String.valueOf(mssAccountbillitem.getWriteoffInstanceId())));
                    rBillitemAccount.setBillitemId(mssAccountbillitem.getId());
                    rBillitemAccount.setCreateTime(new Date());
                    rBillitemAccount.setIfall(rBillitemAccount.getIfall() == null ? 1L : rBillitemAccount.getIfall());
                    if (!isOpenTest) {
                        rBillitemAccount.setCreatorId(user.getId());
                        rBillitemAccount.setCreatorName(user.getUserName());
                    }
                    rBillitemAccounts.add(rBillitemAccount);
                    rBillitemAccountsD.add(rBillitemAccount);
                }
                mssAccountbillitem.setSum(sum);
                mssAccountbillitem.setTaxAdjustSum(tax);
                mssAccountbillitem.setRbillitemaccount(rBillitemAccounts);
                mssAccountbillitems.add(mssAccountbillitem);
                if (service.createNewMssAccountIteBill(mssAccountbillitem, rBillitemAccounts)) {
                    count++;
                }
            }
            //更新报账单的报账金额和税额
            MssAccountbill mssAccountbill = new MssAccountbill();
            mssAccountbill.setId(mssId);
            mssAccountbill.setSum(sum);
            mssAccountbill.setInputTaxSum(tax);
            int upCount = service.updateMssAccountMoney(mssAccountbill);
            if (upCount == 0) {
                service.deleteMssAccountIteBill(mssAccountbillitems, rBillitemAccountsD);
                return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "新增明细失败，没有找到明细所关联的报账单主表", mssAccountbillitems);
            }
            /*=============================================<*/
        } catch (Exception e) {
            e.printStackTrace();
            return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "新增明细失败：" + e.getMessage());
        }
        MessageMaster master = new MessageMaster();
        if (count > 0) {
            master.setCode(MessageMaster.Code.OK);
            master.setMessage("新增明细成功");
        } else {
            master.setCode(MessageMaster.Code.ERROR);
            master.setMessage("新增明细失败");
        }
        master.insertNewMessage("totalnum", count);
        master.setData(mssAccountbillitems);
        return master.toString();
    }

    @Override
    @PostMapping(value = "/save_mss_account_bill", produces = "application/json;charset=UTF-8")
    public String saveMssAccount(@RequestBody MssAccountbill mssAccountbill, @RequestParam("PABRID") String pabrid) {
        if (mssAccountbill == null || mssAccountbill.getId() == null || mssAccountbill.getEnergytype() == null) {
            return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "保存报账基础信息失败，参数不存在，必须参数：id、energyType");
        }
        //如果为水、气报账，检测相应供应商、合同、外部收款人信息

        mssAccountbill.setHappenDate(DateUtils.formatDate(DateUtils.parseDate(mssAccountbill.getHappenDate()), "yyyy-MM-dd"));
        mssAccountbill.setStatus(1);
        mssAccountbill.setTimestamp(new Date());
        mssAccountbill.setCreateDate(new Date());
        //第一次生成基础信息初始金额为0
        mssAccountbill.setSum(new BigDecimal("0.0"));
        mssAccountbill.setInputTaxSum(new BigDecimal("0.0"));
        /*=======================将该报账单id添加到归集单的关联======================>*/
        //查询归集单详细信息
        EnergyAccountbillpre energyAccountbillpre = accountbillpreServiceservice.selectEnergyAccountBill(Long.parseLong(pabrid));

        if (energyAccountbillpre == null) {
            return MessageMaster.getMessage(MessageMaster.Code.ERROR, "没有相关归集单信息");
        }
        if (StringUtils.isEmpty(energyAccountbillpre.getPabid())) {
            energyAccountbillpre.setPabid(String.valueOf(mssAccountbill.getId()));
        } else {
            energyAccountbillpre.setPabid(energyAccountbillpre.getPabid() + "," + mssAccountbill.getId());
            //已生成报账单
            energyAccountbillpre.setStatus(2);
        }
        /*=======================Finished======================<*/

        //事务插入基础信息、将基础信息关联到归集单
        boolean flag = service.createNewMssAccountBill(energyAccountbillpre, mssAccountbill);
        MessageMaster master = new MessageMaster();
        if (flag) {
            master.setCode(MessageMaster.Code.OK);
            master.setMessage("新建报账单成功");
        } else {
            master.setCode(MessageMaster.Code.ERROR);
            master.setMessage("新建报账单失败");
        }
        master.setData(mssAccountbill);
        return master.toString();
    }

    @Override
    @GetMapping("/get_energy_account_bill_datailed")
    public String getEnerguAccountBillDetailed(@RequestParam("PABRID") String pabrid, @RequestParam("TYPE") String type) {
        if (StringUtils.isEmpty(pabrid) || !RegexUtil.isMatch(pabrid, RegexUtil.Type.NUM_STRING) || (!StringUtils.isEmpty(type) && !RegexUtil.isMatch(type, "^[1-3]$"))) {
            return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "参数为空或不合法:" + pabrid);
        }
        //查询归集单详细信息
        EnergyAccountbillpre energyAccountbillpre = accountbillpreServiceservice.selectEnergyAccountBill(Long.parseLong(pabrid));
        if (energyAccountbillpre == null) {
            return MessageMaster.getMessage(MessageMaster.Code.ERROR, "没有相关归集单信息");
        }
        //查询归集单关联的报账单
        String mssIdString = energyAccountbillpre.getPabid();
        if (StringUtils.isEmpty(mssIdString)) {
            return MessageMaster.getMessage(MessageMaster.Code.OK, "你所查询的归集单暂无关联报账单", energyAccountbillpre);
        }
        mssIdString = mssIdString.replace("，", ",");
        mssIdString = mssIdString.replace(" ", "");
        String[] mssIds = mssIdString.split(",");
        //查询报账单列表
        List<MssAccountbill> mssAccountbills = mssAccountbillService.selectListByIdsAndType(mssIds, new BigDecimal(type));
        if (mssAccountbills == null) {
            return MessageMaster.getMessage(MessageMaster.Code.OK, "没有查找到相关报账单信息", energyAccountbillpre);
        }
        //查询报账单、台账关联信息
        List<RBillitemAccount> rBillitemAccounts = service.getRBillAccount(mssIds);
        if (rBillitemAccounts == null) {
            Map<String, Object> data = new HashMap<>();
            data.put("energyAccountbillpre", energyAccountbillpre);
            data.put("mssAccountbills", mssAccountbills);
            return MessageMaster.getMessage(MessageMaster.Code.OK, "没有相关明细信息", data);
        }
        BigDecimal itemId = new BigDecimal("-1");
        List<String> mssItemIds = new LinkedList<>();
        for (RBillitemAccount rbillAccount : rBillitemAccounts) {
            //如果该报账单明细ID和上一次不同，说明为新需要查的
            if (itemId.compareTo(new BigDecimal(rbillAccount.getBillitemId())) != 0) {
                mssItemIds.add(String.valueOf(rbillAccount.getBillitemId()));
                itemId = new BigDecimal(rbillAccount.getBillitemId());
            }
        }
        //查询明细信息，并将其对应关联的台账信息添加进去
        List<MssAccountbillitem> mssAccountbillitems = service.getMssAcountBillItem(mssItemIds.toArray(new String[mssItemIds.size()]));
        if (mssAccountbillitems == null) {
            Map<String, Object> data = new HashMap<>();
            data.put("energyAccountbillpre", energyAccountbillpre);
            data.put("mssAccountbills", mssAccountbills);
            return MessageMaster.getMessage(MessageMaster.Code.OK, "没有相关明细信息", data);
        }
        for (MssAccountbillitem ms : mssAccountbillitems) {
            if (ms.getRbillitemaccount().size() > 0) {
                continue;
            }
            List<RBillitemAccount> rBillitemAccounts1 = new LinkedList<>();
            for (RBillitemAccount rb : rBillitemAccounts) {
                if (rb.getBillitemId().longValue() == ms.getId()) {
                    rBillitemAccounts1.add(rb);
                }
            }
            ms.setRbillitemaccount(rBillitemAccounts1);
        }
        for (MssAccountbill ms : mssAccountbills) {
            List<MssAccountbillitem> mssAccountbillitems1 = new LinkedList<>();
            for (MssAccountbillitem msi : mssAccountbillitems) {
                if (msi.getWriteoffInstanceId().compareTo(new BigDecimal(ms.getId())) == 0) {
                    mssAccountbillitems1.add(msi);
                }
            }
            ms.setItem(mssAccountbillitems1);
        }
        Map<String, Object> data = new HashMap<>();
        data.put("mssAccountbills", mssAccountbills);
        data.put("energyAccountbillpre", energyAccountbillpre);
        MessageMaster master = new MessageMaster();
        master.setCode(MessageMaster.Code.OK);
        master.setMessage("查询成功");
        master.setData(data);
        return master.toString();
    }

    @Override
    @GetMapping("/select_mss_account_like")
    public String getMssAccountBillLike(@RequestBody MssAccountbill mssAccountbill) {

        if (mssAccountbill == null) {
            return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "参数为空，查询失败");
        }
        User user = null;
        //模糊查询必须验证身份，不然会导致查询出所有信息
        if (!isOpenTest) {
            user = ShiroUtils.getUser();
            if (user == null) {
                return MessageMaster.getMessage(MessageMaster.Code.FORBIDDEN, "登录已过期，请重新登录");
            }
            mssAccountbill.setFillInAccount(user.getLoginId());
        }

        List<MssAccountbill> mssAccountbills = mssAccountbillService.selectByLike(mssAccountbill);
        MessageMaster master = new MessageMaster();
        master.setCode(MessageMaster.Code.OK);
        master.setMessage("查询成功");
        master.setData(mssAccountbills);
        return master.toString();
    }
}
