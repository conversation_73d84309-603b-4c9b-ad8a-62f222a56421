package com.sccl.modules.business.stationreportwhitelist.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sccl.modules.business.stationreportwhitelist.domain.StationReportWhitelistBill;
import com.sccl.modules.business.stationreportwhitelist.dto.StationReportWhitelistBillDTO;
import com.sccl.modules.business.stationreportwhitelist.dto.StationReportWhitelistBillQuery;
import com.sccl.modules.business.stationreportwhitelist.vo.OneStopIsMoreThanOneWatchExport;
import com.sccl.modules.business.stationreportwhitelist.vo.StationReportWhitelistBillVO;
import com.sccl.modules.uniflow.common.WFModel;

import java.util.List;

public interface StationReportWhitelistBillService extends IService<StationReportWhitelistBill> {

    /**
     * 添加白名单单据
     */
    String add(StationReportWhitelistBillDTO dto);

    /**
     * 修改白名单单据
     */
    String edit(StationReportWhitelistBillDTO dto);

    /**
     * 详情
     */
    StationReportWhitelistBillVO findById(Long id);



    /**
     * 流程回调
     */
    void uniflowCallBack(WFModel wfModel);

    IPage<StationReportWhitelistBillVO> selectList(Page<StationReportWhitelistBillVO> page, StationReportWhitelistBillQuery query);

    /**
     * 导出
     */
    List<OneStopIsMoreThanOneWatchExport> export(Page<StationReportWhitelistBillVO> page, StationReportWhitelistBillQuery query);

    /**
     * 移除白名单
     */
    void notAvailable(Long id);

    /**
     * 删除白名单单据
     */
    void del(Long id);
}
