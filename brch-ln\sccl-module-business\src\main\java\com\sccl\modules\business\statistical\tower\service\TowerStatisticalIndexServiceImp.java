package com.sccl.modules.business.statistical.tower.service;

import com.sccl.common.lang.StringUtils;
import com.sccl.common.thread.SyncHelper;
import com.sccl.common.utils.DateUtils;
import com.sccl.modules.business.msg.domain.Message;
import com.sccl.modules.business.msg.mapper.MsgMapper;
import com.sccl.modules.business.statistical.framework.StatisticalIndex;
import com.sccl.modules.business.statistical.tower.TowerAuditResultStatisticalIndexGroupHandler;
import com.sccl.modules.business.statistical.tower.domain.TowerBillAuditResult;
import com.sccl.modules.business.statistical.tower.mapper.TowerStatisticalMapper;
import com.sccl.modules.business.statistical.tower.query.ContributionPercentQuery;
import com.sccl.modules.business.statistical.tower.vo.ContributionPercentVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Yongxiang
 * @Date 2022/10/31 16:51
 * @Email <EMAIL>
 */
@Service
@Slf4j
public class TowerStatisticalIndexServiceImp implements ITowerStatisticalIndexService {
    @Autowired(required = false)
    private MsgMapper msgMapper;

    @Autowired(required = false)
    private TowerStatisticalMapper statisticalMapper;

    private final ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(5, 10, 60, TimeUnit.SECONDS, new LinkedBlockingQueue<>(Integer.MAX_VALUE));

    @Override
    public TowerBillAuditResult doStatistical(Long company, Long country, Date from, Date to) {
        if (from == null) {
            return null;
        }
        if (to == null) {
            to = DateUtils.addDays(from, 1);
        }
        List<Message> messageList = msgMapper.selectListByCompanyAndCountryBetween(company, country, DateUtils.formatDateTime(from), DateUtils.formatDateTime(to));
        if (CollectionUtils.isEmpty(messageList)) {
            return null;
        }
        TowerBillAuditResult result = new TowerBillAuditResult();
        result.setCity(messageList
                .get(0)
                .getCity());
        result.setCompany(company);
        result.setCountry(country);
        if (country != null) {
            result.setDistrict(messageList
                    .get(0)
                    .getDistrict());
        } else {
            result.setDistrict("全市");
        }
        result.setStatisticalDate(new Date());
        result.setAccountedCount(statisticalMapper.selectAccountedCount(messageList
                .stream()
                .map(Message::getTowerKey)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList())));
        result.setUpdateTime(DateUtils.getTime());
        result.setContributionPercentVOMap(getContributionPercentFloating(country, company, from));
        if (result.getContributionPercentVOMap() != null) {
            result.setErrContributionPercentCount(result
                    .getContributionPercentVOMap()
                    .size());
        }
        return wrapper(result, messageList);
    }

    @Override
    public TowerBillAuditResult doStatistical(Date from, Date to, boolean isLoadInRedis) {
        if (from == null) {
            return null;
        }
        if (to == null) {
            to = DateUtils.addDays(from, 1);
        }
        int count = msgMapper.countBetween(DateUtils.formatDateTime(from), DateUtils.formatDateTime(to));
        if (count == 0) {
            return new TowerBillAuditResult();
        }
        Date finalTo = to;
        TowerBillAuditResult result = new TowerBillAuditResult();
        AtomicBoolean isTowStatFinish = new AtomicBoolean(false);
        Runnable contribution = () -> {
            result.setContributionPercentVOMap(getProvinceContributionPercentFloating(from));
            if (result.getContributionPercentVOMap() != null) {
                result.setErrContributionPercentCount(result
                        .getContributionPercentVOMap()
                        .size());
            }
            isTowStatFinish.set(true);
        };
        Runnable runnable = () -> {
            try {

                log.info("总需统计数：{}", count);
                final int size = 2000;
                int pageCount = (int) Math.ceil((count * 1.0) / size);
                for (int i = 1; i <= pageCount; i++) {
                    int skip = size * (i - 1);
                    List<Message> pagedMsg = msgMapper.selectListBetween(DateUtils.formatDateTime(from), DateUtils.formatDateTime(finalTo), skip, size);
                    if (CollectionUtils.isEmpty(pagedMsg)) {
                        continue;
                    }
                    log.info("当前已统计：{}/{}", result.getTotalCount(), count);
                    result.setCity("全省");
                    result.setDistrict("地市");
                    result.setStatisticalDate(new Date());
                    result.setAccountedCount(result.getAccountedCount() + statisticalMapper.selectAccountedCount(pagedMsg
                            .stream()
                            .map(Message::getTowerKey)
                            .filter(Objects::nonNull)
                            .distinct()
                            .collect(Collectors.toList())));
                    wrapper(result, pagedMsg);
                }
                SyncHelper.aWait(isTowStatFinish::get);
                TowerAuditResultStatisticalIndexGroupHandler handler = new TowerAuditResultStatisticalIndexGroupHandler(null, null);
                boolean flag = handler.createNewStatisticalIndex(result);
                if (flag) {
                    log.info("保存统计结果成功");
                } else {
                    log.error("保存统计结果失败");
                }
                if (isLoadInRedis) {
                    StatisticalIndex index = result.getIndexObject();
                    index.setGroupAlias(handler.getGroupName());
                    index.setOwnerAs(handler.getOwnerAs());
                    handler.loadInRedis(Collections.singletonList(index));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        };
        threadPoolExecutor.submit(contribution);
        threadPoolExecutor.submit(runnable);
        TowerBillAuditResult result1 = new TowerBillAuditResult();
        result1.setTotalCount(count);
        result1.setCity("全省（统计中）");
        result1.setDistrict("地市（统计中）");
        return result1;
    }

    private TowerBillAuditResult wrapper(TowerBillAuditResult result, List<Message> messages) {
        result.setTotalCount(result.getTotalCount() + messages.size());
        result.setPassCount(result.getPassCount() + (int) messages
                .stream()
                .filter(msg -> msg.getStatus() == 0)
                .count());
        result.setFailCount(result.getFailCount() + (int) messages
                .stream()
                .filter(msg -> msg.getStatus() == 1)
                .count());
        result.setUpdateTime(DateUtils.getTime());
        for (int i = 0; i < 9; i++) {
            int finalI = i;
            result.putCollectClassified(i, (int) messages
                    .stream()
                    .filter(msg -> msg.getRongStep() != null && msg.getRongStep() == finalI)
                    .count());

        }
        return result;
    }

    public Map<String, ContributionPercentVO> getContributionPercentFloating(Long country, long company, Date date) {
        //获取当月分摊比例信息
        Date firstDay = DateUtils.getFirstDayOfMonth(date);
        List<ContributionPercentQuery> contributionPercents = statisticalMapper.selectContributionPercentQuery(null, DateUtils.formatDateTime(firstDay), DateUtils.getTime(), company, country, null, null, null);
        if (CollectionUtils.isEmpty(contributionPercents)) {
            return null;
        }
        //获取上月分摊比例信息
        Date lastMonthToday = DateUtils.addMonths(date, -1);
        Date lastMonthFirstDay = DateUtils.getFirstDayOfMonth(lastMonthToday);
        Date lastMonthEndDay = DateUtils.getLastDayOfMonth(lastMonthToday);
        List<ContributionPercentQuery> lastMonthContributionPercents = new ArrayList<>();
        List<ContributionPercentQuery> lastMonthContributionPercents1 = statisticalMapper.selectContributionPercentQuery(contributionPercents
                .stream()
                .filter(item -> "转供".equals(item.getPowerSupply()))
                .collect(Collectors.toList()), DateUtils.formatDateTime(lastMonthFirstDay), DateUtils.formatDateTime(lastMonthEndDay), company, country, null, null, true);
        List<ContributionPercentQuery> lastMonthContributionPercents2 = statisticalMapper.selectContributionPercentQuery(contributionPercents
                .stream()
                .filter(item -> "直供".equals(item.getPowerSupply()))
                .collect(Collectors.toList()), DateUtils.formatDateTime(lastMonthFirstDay), DateUtils.formatDateTime(lastMonthEndDay), company, country, null, null, false);
        lastMonthContributionPercents.addAll(lastMonthContributionPercents1);
        lastMonthContributionPercents.addAll(lastMonthContributionPercents2);
        if (CollectionUtils.isEmpty(lastMonthContributionPercents)) {
            return null;
        }

        Map<String, BigDecimal> contributionPercentMap = new HashMap<>();
        lastMonthContributionPercents.forEach(item -> {
            if (!StringUtils.isEmpty(item.getTowerSiteCode()) && item.getContributionPercent() != null) {
                contributionPercentMap.put(key(item.getTowerSiteCode(), item.getMeterCode()), item.getContributionPercent());
            }
        });
        Map<String, ContributionPercentVO> result = new HashMap<>();
        contributionPercents.forEach(item -> {
            BigDecimal last = contributionPercentMap.get(key(item.getTowerSiteCode(), item.getMeterCode()));
            if (last == null || item
                    .getContributionPercent()
                    .compareTo(last) == 0) {
                return;
            }
            Double percent = calculateFloatingPercent(item.getContributionPercent(), last, 4);
            if (percent == null || percent.equals(0.0)) {
                return;
            }
            ContributionPercentVO contributionPercentVO = new ContributionPercentVO();
            contributionPercentVO.setFloating(percent);
            contributionPercentVO.setLastPeriodContributionPercent(last);
            contributionPercentVO.setThisPeriodContributionPercnt(item.getContributionPercent());
            result.put(key(item.getTowerSiteCode(), item.getMeterCode()), contributionPercentVO);
        });
        return result;
    }

    public Map<String, ContributionPercentVO> getProvinceContributionPercentFloating(Date date) {
        Date firstDay = DateUtils.getFirstDayOfMonth(date);
        //获取当月统计量
        Integer count = statisticalMapper
                .selectContributionPercentQuery(null, DateUtils.formatDateTime(firstDay), DateUtils.getTime(), null, null, null, null, null)
                .size();
        if (count == 0) {
            return null;
        }
        final int size = 2000;
        int pageCount = (int) Math.ceil((count * 1.0) / size);
        Map<String, BigDecimal> contributionPercentMap = new HashMap<>();
        Map<String, ContributionPercentVO> result = new HashMap<>();
        log.info("总需统计数目：{}，分页大小：{}，统计轮次：{}", count, size, pageCount);
        int finished = 0;
        for (int i = 1; i <= pageCount; i++) {
            int skip = size * (i - 1);
            List<ContributionPercentQuery> contributionPercents = statisticalMapper.selectContributionPercentQuery(null, DateUtils.formatDateTime(firstDay), DateUtils.getTime(), null, null, skip, size, null);
            finished += contributionPercents.size();
            //获取上月分摊比例信息
            Date lastMonthToday = DateUtils.addMonths(date, -1);
            Date lastMonthFirstDay = DateUtils.getFirstDayOfMonth(lastMonthToday);
            Date lastMonthEndDay = DateUtils.getLastDayOfMonth(lastMonthToday);

            List<ContributionPercentQuery> lastMonthContributionPercents = new ArrayList<>();
            List<ContributionPercentQuery> lastMonthContributionPercents1 = statisticalMapper.selectContributionPercentQuery(contributionPercents
                    .stream()
                    .filter(item -> "转供".equals(item.getPowerSupply()))
                    .collect(Collectors.toList()), DateUtils.formatDateTime(lastMonthFirstDay), DateUtils.formatDateTime(lastMonthEndDay), null, null, null, null, true);
            List<ContributionPercentQuery> lastMonthContributionPercents2 = statisticalMapper.selectContributionPercentQuery(contributionPercents
                    .stream()
                    .filter(item -> "直供".equals(item.getPowerSupply()))
                    .collect(Collectors.toList()), DateUtils.formatDateTime(lastMonthFirstDay), DateUtils.formatDateTime(lastMonthEndDay), null, null, null, null, false);
            lastMonthContributionPercents.addAll(lastMonthContributionPercents1);
            lastMonthContributionPercents.addAll(lastMonthContributionPercents2);
            if (CollectionUtils.isEmpty(lastMonthContributionPercents)) {
                continue;
            }
            lastMonthContributionPercents.forEach(item -> {
                if (!StringUtils.isEmpty(item.getTowerSiteCode()) && item.getContributionPercent() != null) {
                    contributionPercentMap.put(key(item.getTowerSiteCode(), item.getMeterCode()), item.getContributionPercent());
                }
            });
            contributionPercents.forEach(item -> {
                BigDecimal last = contributionPercentMap.get(key(item.getTowerSiteCode(), item.getMeterCode()));
                if (last == null || item
                        .getContributionPercent()
                        .compareTo(last) == 0) {
                    return;
                }
                Double percent = calculateFloatingPercent(item.getContributionPercent(), last, 4);
                if (percent == null || percent.equals(0.0)) {
                    return;
                }
                ContributionPercentVO contributionPercentVO = new ContributionPercentVO();
                contributionPercentVO.setFloating(percent);
                contributionPercentVO.setLastPeriodContributionPercent(last);
                contributionPercentVO.setThisPeriodContributionPercnt(item.getContributionPercent());
                result.put(key(item.getTowerSiteCode(), item.getMeterCode()), contributionPercentVO);
            });
            log.info("skip:{}，size:{}；已统计{}/{}", skip, size, finished, count);
        }
        return result;
    }


    private static Double calculateFloatingPercent(BigDecimal value, BigDecimal refer, int precision) {
        if (value == null || refer == null) {
            return null;
        }
        value = value.divide(new BigDecimal("1.0"), 4, BigDecimal.ROUND_HALF_EVEN);
        refer = refer.divide(new BigDecimal("1.0"), 4, BigDecimal.ROUND_HALF_EVEN);
        BigDecimal poor = value.subtract(refer);
        if (refer.compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }
        return poor
                .divide(refer, 4, BigDecimal.ROUND_HALF_EVEN)
                .doubleValue();
    }

    private static String key(String towerStationCode, String meterCode) {
        return towerStationCode + "_" + meterCode;
    }

}
