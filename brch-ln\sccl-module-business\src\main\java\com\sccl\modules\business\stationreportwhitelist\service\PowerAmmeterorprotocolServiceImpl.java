package com.sccl.modules.business.stationreportwhitelist.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.business.statinAudit.domain.SysOrganizations;
import com.sccl.modules.business.statinAudit.mapper.PowerStationInfoMapper;
import com.sccl.modules.business.stationreportwhitelist.domain.PowerAmmeterorprotocol;
import com.sccl.modules.business.stationreportwhitelist.domain.PowerElectricClassification;
import com.sccl.modules.business.stationreportwhitelist.domain.StationReportWhitelist;
import com.sccl.modules.business.stationreportwhitelist.dto.FindWhitelistIsNotAddedQuery;
import com.sccl.modules.business.stationreportwhitelist.dto.PowerAmmeterorprotocolQuery;
import com.sccl.modules.business.stationreportwhitelist.enums.MyDict;
import com.sccl.modules.business.stationreportwhitelist.enums.PowerAmmeterorprotocolStatus;
import com.sccl.modules.business.stationreportwhitelist.enums.WhitelistType;
import com.sccl.modules.business.stationreportwhitelist.mapper.PowerAmmeterorprotocolMapper;
import com.sccl.modules.business.stationreportwhitelist.mapper.PowerElectricClassificationMapper;
import com.sccl.modules.business.stationreportwhitelist.vo.PowerAmmeterorprotocolVO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PowerAmmeterorprotocolServiceImpl extends ServiceImpl<PowerAmmeterorprotocolMapper, PowerAmmeterorprotocol> implements PowerAmmeterorprotocolService {

    private final PowerStationInfoMapper stationInfoMapper;
    private final PowerElectricClassificationMapper electricClassificationMapper;

    /**
     * 详情
     *
     * @param id id
     */
    @Override
    public PowerAmmeterorprotocolVO findById(Long id) {
        PowerAmmeterorprotocolQuery query = new PowerAmmeterorprotocolQuery();
        query.setId(id);
        PowerAmmeterorprotocolVO vo = baseMapper.selectJoinOne(PowerAmmeterorprotocolVO.class, getWrapper(query));
        if (vo != null) {
            setAdditionalParameters(vo);
        } else {
            // 电表应该被删除，防止空指针
            vo = new PowerAmmeterorprotocolVO();
            vo.setId(id);
        }
        return vo;
    }

    private void setAdditionalParameters(PowerAmmeterorprotocolVO vo) {
        // 对外结算类型
        vo.setDirectsupplyflagName(MyDict.findDictLabel(MyDict.TYPE.directSupplyFlag,String.valueOf(vo.getDirectsupplyflag())));
        vo.setPropertyName(MyDict.findDictLabel(MyDict.TYPE.property, String.valueOf(vo.getProperty())));
        this.getOrgName(vo);
        this.getElectrotypeName(vo);
    }

    @Override
    public AjaxResult findWhitelistIsNotAddedByStation(FindWhitelistIsNotAddedQuery query) {
        int alreadyJoined = 0;
        MPJLambdaWrapper<PowerAmmeterorprotocol> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(PowerAmmeterorprotocol.class);
        wrapper.eq(PowerAmmeterorprotocol::getStationcode, query.getStationId());
        wrapper.eq(PowerAmmeterorprotocol::getStatus, PowerAmmeterorprotocolStatus.IN_USE.getCode());
        wrapper.orderByDesc(PowerAmmeterorprotocol::getId);
        List<PowerAmmeterorprotocolVO> list = baseMapper.selectJoinList(PowerAmmeterorprotocolVO.class, wrapper);
        for (PowerAmmeterorprotocolVO vo : list) {
            this.getOrgName(vo);
            this.getElectrotypeName(vo);
            vo.setDirectsupplyflagName(MyDict.findDictLabel(MyDict.TYPE.directSupplyFlag, String.valueOf(vo.getDirectsupplyflag())));
            vo.setPropertyName(MyDict.findDictLabel(MyDict.TYPE.property, String.valueOf(vo.getProperty())));
        }
        // 排除已在白名单的电表
        List<Long> idList = list.stream().map(PowerAmmeterorprotocolVO::getId).collect(Collectors.toList());
        if (idList.isEmpty()) {
            return AjaxResult.success("没有数据", list);
        }
        List<StationReportWhitelist> stationReportWhitelist = new StationReportWhitelist().selectList(Wrappers.<StationReportWhitelist>lambdaQuery()
                .select(StationReportWhitelist::getMeterId)
                .in(StationReportWhitelist::getMeterId, idList)
                .eq(StationReportWhitelist::getType, query.getWhitelistType())
        );
        for (PowerAmmeterorprotocolVO ammeterorprotocolVO : list) {
            ammeterorprotocolVO.setIsWhitelist(false);
            for (StationReportWhitelist whitelist : stationReportWhitelist) {
                if (ammeterorprotocolVO.getId().equals(whitelist.getMeterId())) {
                    ammeterorprotocolVO.setIsWhitelist(true);
                    alreadyJoined++;
                }

            }
        }

        String msg = StrUtil.format("该局站电表{}个已加入白名单有{}个", list.size(), alreadyJoined);
        return AjaxResult.success(msg, list);
    }

    @Override
    public IPage<PowerAmmeterorprotocolVO> oneTableMultiStationList(Page<PowerAmmeterorprotocolVO> page, PowerAmmeterorprotocolQuery query) {
        QueryWrapper<PowerAmmeterorprotocolQuery> wrapper = new QueryWrapper<>();
        wrapper.eq("pa.status", PowerAmmeterorprotocolStatus.IN_USE.getCode());
        wrapper.eq(StrUtil.isNotBlank(query.getProjectname()), "pa.projectname", query.getProjectname());
        wrapper.eq(query.getCompany() != null, "pa.company", query.getCompany());
        wrapper.eq(query.getCountry() != null, "pa.country", query.getCountry());
        IPage<PowerAmmeterorprotocolVO> iPage = baseMapper.oneTableMultiStationList(page, wrapper);
        // 全部分组查询太慢，这里分别查询详情
        // 一表多站的电表id集合
        List<Long> idList = iPage.getRecords().stream().map(PowerAmmeterorprotocolVO::getId).collect(Collectors.toList());
        if (idList.isEmpty()) {
            return iPage;
        }
        // 电表列表赋值详细数据
        List<PowerAmmeterorprotocol> ammeterorprotocolList = this.listByIds(idList);
        for (PowerAmmeterorprotocolVO vo : iPage.getRecords()) {
            for (PowerAmmeterorprotocol ammeterorprotocol : ammeterorprotocolList) {
                if (vo.getId().equals(ammeterorprotocol.getId())) {
                    BeanUtils.copyProperties(ammeterorprotocol, vo);
                }
            }
        }

        // 如果已在白名单进行标志
        // 根据电表id列表查询白名单
        List<StationReportWhitelist> stationReportWhitelist = new StationReportWhitelist().selectList(Wrappers.<StationReportWhitelist>lambdaQuery()
                .select(StationReportWhitelist::getMeterId)
                .in(StationReportWhitelist::getMeterId, idList)
                .eq(StationReportWhitelist::getType, WhitelistType.ONE_WATCH_HAS_MANY_STATIONS.getCode())
        );
        for (PowerAmmeterorprotocolVO ammeterorprotocolVO : iPage.getRecords()) {
            ammeterorprotocolVO.setIsWhitelist(false);
            for (StationReportWhitelist whitelist : stationReportWhitelist) {
                if (ammeterorprotocolVO.getId().equals(whitelist.getMeterId())) {
                    ammeterorprotocolVO.setIsWhitelist(true);
                }
            }
            setAdditionalParameters(ammeterorprotocolVO);
        }
        return iPage;
    }

    @Override
    public IPage<PowerAmmeterorprotocolVO> excludeWhitelistList(Page<PowerAmmeterorprotocolVO> page, PowerAmmeterorprotocolQuery query) {
        MPJLambdaWrapper<PowerAmmeterorprotocol> wrapper = new MPJLambdaWrapper<>();
        // 状态为已启用
        wrapper.eq(PowerAmmeterorprotocol::getStatus, PowerAmmeterorprotocolStatus.IN_USE.getCode());
        wrapper.leftJoin(StationReportWhitelist.class, "srw", StationReportWhitelist::getMeterId, PowerAmmeterorprotocol::getId);
//        wrapper.isNull(StationReportWhitelist::getMeterId);
        wrapper.like(StrUtil.isNotBlank(query.getProjectname()), PowerAmmeterorprotocol::getProjectname, query.getProjectname());
        wrapper.like(query.getCompany() != null, PowerAmmeterorprotocol::getCompany, query.getCompany());
        wrapper.like(query.getCountry() != null, PowerAmmeterorprotocol::getCountry, query.getCountry());
        wrapper.and(query.getAmmetername() != null, w -> w
                .last(StrUtil.format("(case when t.category = 1 then t.ammetername = '%{}%' else t.protocolname LIKE '%{}%' end)",
                        query.getAmmetername(), query.getAmmetername())
                )
        );

        IPage<PowerAmmeterorprotocolVO> iPage = baseMapper.selectJoinPage(page, PowerAmmeterorprotocolVO.class, wrapper);
        // 是否在白名单
        List<Long> idList = iPage.getRecords().stream().map(PowerAmmeterorprotocolVO::getId).collect(Collectors.toList());
        if (idList.isEmpty()) {
            return iPage;
        }
        // 前端没有传当前白名单类型，这里查询不了，提交时候拦截
//        List<StationReportWhitelist> whitelistList = new StationReportWhitelist().selectList(Wrappers.<StationReportWhitelist>lambdaQuery()
//                .in(StationReportWhitelist::getMeterId, idList)
//        );
        for (PowerAmmeterorprotocolVO vo : iPage.getRecords()) {
            vo.setIsWhitelist(false);
            // 前端没有传当前白名单类型，这里查询不了，提交时候拦截
//            for (StationReportWhitelist whitelist : whitelistList) {
//                if (vo.getId().equals(whitelist.getMeterId())) {
//                    vo.setIsWhitelist(true);
//                }
//            }
            // 设置其他属性
            setAdditionalParameters(vo);
        }

        return iPage;
    }

    public MPJLambdaWrapper<PowerAmmeterorprotocol> getWrapper(PowerAmmeterorprotocolQuery query) {
        MPJLambdaWrapper<PowerAmmeterorprotocol> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(PowerAmmeterorprotocol.class);

        // 电表信息关联
//        wrapper.selectAll(PowerAmmeterorprotocol.class);
//        wrapper.leftJoin(PowerAmmeterorprotocol.class, PowerAmmeterorprotocol::getId, StationReportWhitelist::getMeterId);
        wrapper.eq(query.getId() != null, StationReportWhitelist::getId, query.getId());


        return wrapper;
    }

    /**
     * 获取公司名称
     */
    private void getOrgName(PowerAmmeterorprotocolVO vo) {
        // 分公司
        if (vo.getCompany() != null) {
            SysOrganizations orgCompany = stationInfoMapper.selectsysOrgNameById(vo.getCompany());
            if (orgCompany != null) {
                vo.setCompanyName(orgCompany.getOrgName());
            }
        }

        // 部门
        if (vo.getCountry() != null) {
            SysOrganizations orgCountry = stationInfoMapper.selectsysOrgNameById(vo.getCountry());
            if (orgCountry != null) {
                String countryName = "";
                SysOrganizations superOrg = stationInfoMapper.selectsysOrgNameById(Long.valueOf(orgCountry.getParentCompanyNo()));
                if (superOrg != null) {
                    countryName = StrUtil.format("{}-{}", superOrg.getOrgName(), orgCountry.getOrgName());
                } else {
                    countryName = orgCountry.getOrgName();
                }
                vo.setCountryName(countryName);
            }
        }
    }

    /**
     * 获取用电类型
     * 目前根据表结构有3层级，暂只查询2层
     *
     * @param vo 白名单
     */
    private void getElectrotypeName(PowerAmmeterorprotocolVO vo) {
        String electrotypeName = "";
        String electrotypeName2 = "";
        if (vo.getElectrotype() != null) {
            // 第一层
            PowerElectricClassification classification = electricClassificationMapper.selectById(vo.getElectrotype());
            electrotypeName = classification.getTypeName();
            if (ObjectUtil.isNotNull(classification) && classification.getParentId() != null) {
                //第二层
                PowerElectricClassification classification2 = electricClassificationMapper.selectById(classification.getParentId());
                electrotypeName2 = StrUtil.isNotBlank(classification2.getTypeName()) ? classification2.getTypeName() + "/" : "";
            }
        }
        vo.setElectrotypeName(electrotypeName2 + electrotypeName);
    }
}
