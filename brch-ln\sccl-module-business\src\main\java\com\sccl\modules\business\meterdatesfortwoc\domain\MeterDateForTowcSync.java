package com.sccl.modules.business.meterdatesfortwoc.domain;

import com.sccl.modules.business.twoc.domain.TwoCFlag;
import lombok.Data;

@Data
public class MeterDateForTowcSync implements TwoCFlag {
    private static final long serialVersionUID = 1L;

    /** 地市编码 */
    private String subjectCode;
    /** 用能月份 YYYYMM */
    private String statisPeriod;
    /** 归属类型 1:集团存续 2:股份上市 */
    private String groupType;
    /** 财辅系统报账单号 */
    private String billCode;
    /** 用量属性 1：预估量 2：实际使用量 */
    private String amountType;
    /** 电表编码 */
    private String energyMeterCode;
    /** 电表名称 */
    private String energyMeterName;
    /** 用电开始时间 YYYYMMDD 用电区间不可重叠，具体看协议 */
    private String electricityStartDate;
    /** 用电截止时间 YYYYMMDD 同上 */
    private String electricityEndDate;
    /** 本次电量 */
    private String thisQuantityOfElectricity;
    /** 本次电费 */
    private String thisElectricityCharge;
    /** 设备电费 */
    private String powerConsumption;
    /** it设备电费 */
    private String itDevicePowerTotal;
    /** 回收电费标识 ：1为回收电费 0为非回收电费标识 */
    private String recoveryElectricityFlag;
    /** 合同电价 */
    private String contractPrice;
}
