package com.sccl.modules.mssaccount.rbillitemaccount.service;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.enrising.dcarbon.redis.RedisUtil;
import com.github.pagehelper.PageHelper;
import com.sccl.common.lang.StringUtils;
import com.sccl.common.support.Convert;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.common.exception.base.BaseException;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.service.IdGenerator;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.modules.business.account.domain.*;
import com.sccl.modules.business.account.mapper.AccountMapper;
import com.sccl.modules.business.accountEs.domain.AccountEsResult;
import com.sccl.modules.business.accountEs.domain.PowerAccountEs;
import com.sccl.modules.business.accountEs.mapper.PowerAccountEsMapper;
import com.sccl.modules.business.accountbillitempre.domain.Accountbillitempre;
import com.sccl.modules.business.accountbillpre.domain.Accountbillpre;
import com.sccl.modules.business.accountbillpre.mapper.AccountbillpreMapper;
import com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol;
import com.sccl.modules.business.coalaccount.domain.CoalAccount;
import com.sccl.modules.business.coalaccount.domain.CoalAccountRequest;
import com.sccl.modules.business.coalaccount.domain.CoalAccountVo;
import com.sccl.modules.business.coalaccount.mapper.CoalAccountMapper;
import com.sccl.modules.business.heataccount.domain.HeatAccount;
import com.sccl.modules.business.heataccount.domain.HeatAccountRequest;
import com.sccl.modules.business.heataccount.domain.HeatAccountVo;
import com.sccl.modules.business.heataccount.mapper.HeatAccountMapper;
import com.sccl.modules.business.mssaccountprepaid.service.IMssAccountPrepaidService;
import com.sccl.modules.business.oilaccount.domain.OilAccount;
import com.sccl.modules.business.oilaccount.domain.OilAccountRequest;
import com.sccl.modules.business.oilaccount.domain.OilAccountVo;
import com.sccl.modules.business.oilaccount.mapper.OilAccountMapper;
import com.sccl.modules.business.stationinfo.service.StationInfoServiceImpl;
import com.sccl.modules.business.stationreportwhitelist.domain.PowerAmmeterorprotocol;
import com.sccl.modules.monitor.operlog.domain.OperLog;
import com.sccl.modules.monitor.operlog.mapper.OperLogMapper;
import com.sccl.modules.mssaccount.mssabccustomer.service.IMssAbccustomerService;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;
import com.sccl.modules.mssaccount.mssaccountbill.mapper.MssAccountbillMapper;
import com.sccl.modules.mssaccount.mssaccountbillitem.domain.MssAccountbillitem;
import com.sccl.modules.mssaccount.rbillitemaccount.domain.OrgName;
import com.sccl.modules.mssaccount.rbillitemaccount.domain.RBillitemAccount;
import com.sccl.modules.mssaccount.rbillitemaccount.mapper.RBillitemAccountMapper;
import com.sccl.modules.system.user.domain.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 报账明细 台账 关联 服务层实现
 *
 * <AUTHOR>
 * @date 2019-06-01
 */
@Service
@Slf4j
public class RBillitemAccountServiceImpl extends BaseServiceImpl<RBillitemAccount> implements IRBillitemAccountService {

    /*浮动金额*/
    private static Double FLOATMONEY = 1.0;
    @Autowired
    MssAccountbillMapper mssAccountbillMapper;
    @Autowired
    AccountbillpreMapper accountbillpreMapper;
    @Autowired
    RBillitemAccountMapper rBillitemAccountMapper;
    @Autowired
    AccountMapper accountMapper;
    @Autowired
    HeatAccountMapper heataccountMapper;
    @Autowired
    CoalAccountMapper coalaccountMapper;
    @Autowired
    OilAccountMapper oilaccountMapper;
    @Autowired
    PowerAccountEsMapper accountEsMapper;
    @Value("${sccl.deployTo}")
    private String deployTo;
    @Autowired
    private OperLogMapper operLogMapper;
    @Autowired
    private IMssAbccustomerService mssAbccustomerService;
    @Autowired
    private IMssAccountPrepaidService mssAccountPrepaidService;
    @Autowired
    private StationInfoServiceImpl stationInfoService;
    public static boolean hsFlag = true;

    @Override // 获取 归集单类型！！！ -- 电费
    /** 类型 1自有 2自有预估 3铁塔 4自有合并,5预估合并 6铁塔合并 7 铁塔预估 8铁塔预估合并 */
    public String getBillType(RBillitemAccount rBillitemAccount) {
        Accountbillpre auto = new Accountbillpre();
        auto.setPabid(rBillitemAccount.getBillId().toString());
        List<Accountbillpre> accountbillpres = accountbillpreMapper.selectListByAuto(auto);
        if (accountbillpres != null && accountbillpres.size() == 1) {
            return accountbillpres.get(0).getBilltype();
        } else {
            return null;
        }
    }

 // 获取 归集单类型！！！ -- 热力、煤炭、用油
    /** 类型 19热力 20煤  21用油 22热力合并 23煤炭合并 24用油合并 */
    public String getNewBillType(RBillitemAccount rBillitemAccount) {
        Accountbillpre auto = new Accountbillpre();
        auto.setPabid(rBillitemAccount.getBillId().toString());
        List<Accountbillpre> accountbillpres = accountbillpreMapper.selectNewPreType(auto);
        if (accountbillpres != null && accountbillpres.size() == 1) {
            return accountbillpres.get(0).getBilltype();
        } else {
            return null;
        }
    }

    /*根据接口 更新报账 系列状态 开始*/
    /*根据接口 更新报账 系列状态 开始*/
    @Override
    public void updateAccountsByBill(MssAccountbill bill) {
        try {
            RBillitemAccount rBillitemAccount = new RBillitemAccount();
            rBillitemAccount.setBillId(bill.getId());
            String type = getBillType(rBillitemAccount);
            /** 类型 1自有 2自有预估 3铁塔 4自有合并,5自有预估合并 6铁塔合并 7 铁塔预估 8铁塔预估合并 */
            if ("1".equals(type) || "3".equals(type) || "4".equals(type) || "6".equals(type)) {// 更新 自有，铁塔 （合并）
                List<Account> accounts = handeUpdateAccountsByBill(bill, rBillitemAccount);
                for (Account account : accounts) {
                    accountMapper.updateForModel(account);
                }
            } else if ("2".equals(type) || "5".equals(type) || "7".equals(type) || "8".equals(type)) { // 更新 预估
                List<PowerAccountEs> accountEs = handeUpdateAccountsEsByBill(bill, rBillitemAccount);
                for (PowerAccountEs account : accountEs) {
                    accountEsMapper.updateForModel(account);
                }
            } else if ("11".equals(type) || "12".equals(type) || "13".equals(type) || "14".equals(type) || "15".equals(type) || "16".equals(type) || "17".equals(type) || "18".equals(type)) {
                // 新增的 11 自有挂账 12自有挂账合并 13自有预付 14自有预付合并 15铁塔挂账 16铁塔挂账合并
                // 都是 查询 预估台账表 es
                List<PowerAccountEs> accountEs = handeUpdateAccountsEsByBill(bill, rBillitemAccount);
                for (PowerAccountEs account : accountEs) {
                    accountEsMapper.updateForModel(account);
                }
            } else {
                List<Account> accounts = handeUpdateAccountsByBill(bill, rBillitemAccount);
                for (Account account : accounts) {
                    accountMapper.updateForModel(account);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            OperLog model = new OperLog();
            model.setOperName("mss");
            model.setTitle("报账流程回调更新台账状态异常");
            model.setMethod("updateAccountsByBill");
            model.setErrorMsg(bill.getId() + e.getMessage());
            model.setOperTime(new Date());
            operLogMapper.insert(model);
        }
    }

    @Override
    public void updateNewAccountsByBill(MssAccountbill bill) {
        try {
            RBillitemAccount rBillitemAccount = new RBillitemAccount();
            rBillitemAccount.setBillId(bill.getId());
            String type = getNewBillType(rBillitemAccount);
            /** 类型 19热力 20煤  21用油 22热力合并 23煤炭合并 24用油合并 */
            if ("19".equals(type) || "22".equals(type)) {
                List<HeatAccount> heatAccounts = handeUpdateHeatAccountsByBill(bill, rBillitemAccount);
                for (HeatAccount account : heatAccounts) {
                    heataccountMapper.updateByPrimaryKey(account);
                }
            } else if ("20".equals(type) || "23".equals(type)) {
                List<CoalAccount> coalAccounts = handeUpdateCoalAccountsByBill(bill, rBillitemAccount);
               for (CoalAccount account : coalAccounts) {
                   coalaccountMapper.updateByPrimaryKey(account);
                }
            }  else {
                List<OilAccount> oilAccounts = handeUpdateOilAccountsByBill(bill, rBillitemAccount);
                for (OilAccount account : oilAccounts) {
                    oilaccountMapper.updateByPrimaryKey(account);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            OperLog model = new OperLog();
            model.setOperName("mss");
            model.setTitle("报账流程回调更新台账状态异常");
            model.setMethod("updateAccountsByBill");
            model.setErrorMsg(bill.getId() + e.getMessage());
            model.setOperTime(new Date());
            operLogMapper.insert(model);
        }
    }

    @Override // 如果 所有报账都完成 更新 归集单 台账 为报账完成
    public void updatePreByBill(MssAccountbill bill) {
        if (bill.getStatus() == -1) {// 删除 解除关联关系 和归集单
            rBillitemAccountMapper.deleteRbillitemAccountByBillId(bill.getId());
            Accountbillpre auto = new Accountbillpre();
            auto.setPabid(bill.getId().toString());
            List<Accountbillpre> accountbillpres = accountbillpreMapper.selectListByAuto(auto);
            if (accountbillpres != null && accountbillpres.size() > 0) {
                Accountbillpre a = accountbillpres.get(0);
                String pabid = a.getPabid();
                StringBuffer newpabid = handelsplitIds(bill.getId(), pabid);
                a.setPabid(newpabid.toString());// 设置值
                if (StringUtils.isNotEmpty(a.getPabid())) {
                    a.setStatus(2);//已生成报账单
                } else {
                    a.setStatus(1);//未生成报账单
                }
                accountbillpreMapper.updateByPrimaryKey(a);// 更新旧的归集单
                //判断是否 合并归集单 并更新状态
                updatepreByParentPre(a, 1);// 解除关联关系
            }
        } else if (bill.getStatus() == 7) {// 完成
            Accountbillpre auto = new Accountbillpre();
            auto.setPabid(bill.getId().toString());
            List<Accountbillpre> accountbillpres = accountbillpreMapper.selectListByAuto(auto);
            if (accountbillpres != null && accountbillpres.size() > 0) {
                Accountbillpre accountbillpre = accountbillpres.get(0);
                if (accountbillpre.getUseableMoney().doubleValue() <= FLOATMONEY) {// 浮动一块钱
                    // 剩余 可报账金额 为 0 关联全部报账单状态都为7
                    String pabid = accountbillpre.getPabid();
                    boolean flag = true;
                    List<MssAccountbill> mssAccountbills = mssAccountbillMapper.selectListByIds(Convert.toStrArray(pabid));
                    for (MssAccountbill item : mssAccountbills) {
                        if (item.getStatus() != 7) {
                            flag = false;
                        }
                    }
                    if (flag) {//关联全部报账单状态都为7
                        accountbillpre.setStatus(3);/** 状态 1正常 2已生成报账单 3已完成*/
                        accountbillpreMapper.updateForModel(accountbillpre);
                        Accountbillpre accountbillpreChild = new Accountbillpre();
                        accountbillpreChild.setParentid(accountbillpre.getPabrid());
                        accountbillpreChild.setStatus(3);
                        // 根据父归集单id 更新归集单
                        accountbillpreMapper.updateForParentId(accountbillpreChild);
                        // 更新 关联的所有 台账 为 已完成 报账
                        List<AccountBaseResult> accountBaseResults = null;
                        List<AccountEsResult> accountEsResults = null;
                        Map<String, Object> map = new HashMap<>();
                        map.put("deployTo", deployTo);
                        map.put("parid", accountbillpre.getPabrid());
                        String status = accountbillpre.getBilltype();
                        if ("1".equals(status) || "3".equals(status) || "9".equals(status)) {// 自有/铁塔台账、包干
                            map.put("union", false);
                            accountBaseResults = accountMapper.selectAccountByAutoMap(map);
                        } else if ("2".equals(status) || "7".equals(status) || "11".equals(status) || "13".equals(status) || "15".equals(status) || "17".equals(status)) {// 自有预估/铁塔预估 台账
                            map.put("union", false);
                            accountEsResults = accountEsMapper.selectAccountEsByAutoMap(map);
                        } else if ("4".equals(status) || "6".equals(status) || "10".equals(status)) {// 自有/铁塔、包干 合并归集单
                            map.put("union", true);
                            accountBaseResults = accountMapper.selectAccountByAutoMap(map);
                        } else if ("5".equals(status) || "8".equals(status) || "12".equals(status) || "14".equals(status) || "16".equals(status) || "18".equals(status)) {// 自有预估/铁塔预估 合并归集单
                            map.put("union", true);
                            accountEsResults = accountEsMapper.selectAccountEsByAutoMap(map);
                        } else {// 默认查询 自有台账
                            map.put("union", false);
                            accountBaseResults = accountMapper.selectAccountByAutoMap(map);
                        }
                        // 更新 自有,铁塔 台账 数据
                        if (accountBaseResults != null && accountBaseResults.size() > 0) {
                            String ids[] = new String[accountBaseResults.size()];
                            for (int i = 0; i < accountBaseResults.size(); i++) {
                                ids[i] = accountBaseResults.get(i).getPcid().toString();
                            }
                            updateAccountStatusBypcids(ids, null);
                        }
                        // 更新 预估 台账 数据
                        if (accountEsResults != null && accountEsResults.size() > 0) {
                            String ids[] = new String[accountEsResults.size()];
                            for (int i = 0; i < accountEsResults.size(); i++) {
                                ids[i] = accountEsResults.get(i).getPcid().toString();
                            }
                            updateAccountStatusBypcids(ids, "es");// 预估
                        }
                    }
                }
            }
        }
    }

    private StringBuffer handelsplitIds(Long id, String pabid) {
        String[] split = pabid.split(",");
        StringBuffer newpabid = new StringBuffer();
        for (String str : split) {
            if (!str.equals(id.toString())) {
                newpabid.append(str);
                newpabid.append(",");
            }
        }
        if (StringUtils.isNotEmpty(newpabid.toString())) newpabid.deleteCharAt(newpabid.length() - 1);
        return newpabid;
    }

    //判断是否 合并归集单 并更新状态
    private void updatepreByParentPre(Accountbillpre accountbillpre, Integer status) {
        if ("4".equals(accountbillpre.getBilltype()) || "5".equals(accountbillpre.getBilltype()) || "6".equals(accountbillpre.getBilltype())) {
            List<Accountbillpre> accountbillpres = accountbillpreMapper.selectByParentid(accountbillpre.getPabrid());
            for (Accountbillpre pre : accountbillpres) {
                pre.setStatus(status);
                accountbillpreMapper.updateForModel(pre);
            }
        }
    }

    // 根据 台账 ids更新台账状态 为 完成报账
    private void updateAccountStatusBypcids(String[] ids, String type) {
        // type  es 为预估台账
        if (ids != null) {
            Map<String, Object> mapa = new HashMap<>();
            mapa.put("ids", ids);
            mapa.put("status", 3);
            if ("es".equals(type)) {
                accountEsMapper.updateStatus(mapa);
            } else {
                accountMapper.updateStatus(mapa);
            }
        }
    }

    private List<Account> handeUpdateAccountsByBill(MssAccountbill bill, RBillitemAccount rBillitemAccount) {
        List<Account> account = new ArrayList<>();
        List<RBillitemAccount> rBillitemAccounts = rBillitemAccountMapper.selectList(rBillitemAccount);

        if (rBillitemAccounts != null && rBillitemAccounts.size() > 0) {
            for (RBillitemAccount rb : rBillitemAccounts) {
                Account a = new Account();
                a.setLasteditdate(new Date());
                a.setPcid(rb.getAccountId());
                if (bill.getStatus() == 7) {// 报账完成
                    a.setStatus(3);//状态1为正常;2流程中 3报账完成 4已生成归集单 5已退回*/
                } else if (bill.getStatus() == 2) {// 代办
                    a.setStatus(2); // 2流程中
                } else if (bill.getStatus() == -1) {//报账单删除
                    a.setStatus(1); // 状态1为正常
                } else {
                    a.setStatus(4); // 状态4为正常,加入归集单
                }
                if (ifAll(rb)) {
                    account.add(a);// 已经完成报账的台账 id
                } else {// 验证 部分金额
                    Double aDouble = null;
                    Map<String, Object> param = new HashMap<>();
                    param.put("pcid", rb.getAccountId());
                    param.put("flag", "power_account");
                    if (bill.getStatus() == 2) {//  验证台账是否所有金额都处于报账中或报账完成
                        param.put("status", new String[]{"2", "3", "4", "7"});
                        aDouble = rBillitemAccountMapper.validateMoney(param);
                        a.setStatus(2); // 2流程中
                    }
                    if (bill.getStatus() == 7) {// 验证是否 所有关联金额 都报账完成
                        param.put("status", new String[]{"7"});
                        aDouble = rBillitemAccountMapper.validateMoney(param);
                        a.setStatus(3); // 3报账完成
                    }
                    if (aDouble != null && aDouble <= FLOATMONEY && aDouble >= -FLOATMONEY) {
                        account.add(a);// 已经完成报账的台账 id
                    }
                }
            }
        }
        return account;
    }


    private List<HeatAccount> handeUpdateHeatAccountsByBill(MssAccountbill bill, RBillitemAccount rBillitemAccount) {
        List<HeatAccount> account = new ArrayList<>();
        List<RBillitemAccount> rBillitemAccounts = rBillitemAccountMapper.selectList(rBillitemAccount);
        if (CollectionUtil.isNotEmpty(rBillitemAccounts)) {
            for (RBillitemAccount rb : rBillitemAccounts) {
                HeatAccount a = new HeatAccount();
                a.setLasteditDate(new Date());
                a.setId(rb.getAccountId());
                // 报账完成
                if (bill.getStatus() == 7) {
                    //状态1为正常;2流程中 3报账完成 4已生成归集单 5已退回*/
                    a.setStatus((byte) 3);
                    // 代办
                } else if (bill.getStatus() == 2) {
                    // 2流程中
                    a.setStatus((byte) 2);
                    //报账单删除
                } else if (bill.getStatus() == -1) {
                    // 状态1为正常
                    a.setStatus((byte) 1);
                } else {
                    // 状态4为正常,加入归集单
                    a.setStatus((byte) 4);
                }
                if (ifAll(rb)) {
                    // 已经完成报账的台账 id
                    account.add(a);
                } else {// 验证 部分金额
                    Double aDouble = null;
                    Map<String, Object> param = new HashMap<>();
                    param.put("pcid", rb.getAccountId());
                    param.put("flag", "power_account");
                    //  验证台账是否所有金额都处于报账中或报账完成
                    if (bill.getStatus() == 2) {
                        param.put("status", new String[]{"2", "3", "4", "7"});
                        aDouble = rBillitemAccountMapper.validateMoney(param);
                        // 2流程中
                        a.setStatus((byte) 2);
                    }
                    // 验证是否 所有关联金额 都报账完成
                    if (bill.getStatus() == 7) {
                        param.put("status", new String[]{"7"});
                        aDouble = rBillitemAccountMapper.validateMoney(param);
                        // 3报账完成
                        a.setStatus((byte) 3);
                    }
                    if (aDouble != null && aDouble <= FLOATMONEY && aDouble >= -FLOATMONEY) {
                        // 已经完成报账的台账 id
                        account.add(a);
                    }
                }
            }
        }
        return account;
    }

    private List<CoalAccount> handeUpdateCoalAccountsByBill(MssAccountbill bill, RBillitemAccount rBillitemAccount) {
        List<CoalAccount> account = new ArrayList<>();
        List<RBillitemAccount> rBillitemAccounts = rBillitemAccountMapper.selectList(rBillitemAccount);
        if (CollectionUtil.isNotEmpty(rBillitemAccounts)) {
            for (RBillitemAccount rb : rBillitemAccounts) {
                CoalAccount a = new CoalAccount();
                a.setLasteditDate(new Date());
                a.setId(rb.getAccountId());
                // 报账完成
                if (bill.getStatus() == 7) {
                    //状态1为正常;2流程中 3报账完成 4已生成归集单 5已退回*/
                    a.setStatus((byte) 3);
                    // 代办
                } else if (bill.getStatus() == 2) {
                    // 2流程中
                    a.setStatus((byte) 2);
                    //报账单删除
                } else if (bill.getStatus() == -1) {
                    // 状态1为正常
                    a.setStatus((byte) 1);
                } else {
                    // 状态4为正常,加入归集单
                    a.setStatus((byte) 4);
                }
                if (ifAll(rb)) {
                    // 已经完成报账的台账 id
                    account.add(a);
                } else {// 验证 部分金额
                    Double aDouble = null;
                    Map<String, Object> param = new HashMap<>();
                    param.put("pcid", rb.getAccountId());
                    param.put("flag", "power_account");
                    //  验证台账是否所有金额都处于报账中或报账完成
                    if (bill.getStatus() == 2) {
                        param.put("status", new String[]{"2", "3", "4", "7"});
                        aDouble = rBillitemAccountMapper.validateMoney(param);
                        // 2流程中
                        a.setStatus((byte) 2);
                    }
                    // 验证是否 所有关联金额 都报账完成
                    if (bill.getStatus() == 7) {
                        param.put("status", new String[]{"7"});
                        aDouble = rBillitemAccountMapper.validateMoney(param);
                        // 3报账完成
                        a.setStatus((byte) 3);
                    }
                    if (aDouble != null && aDouble <= FLOATMONEY && aDouble >= -FLOATMONEY) {
                        // 已经完成报账的台账 id
                        account.add(a);
                    }
                }
            }
        }
        return account;
    }

    private List<OilAccount> handeUpdateOilAccountsByBill(MssAccountbill bill, RBillitemAccount rBillitemAccount) {
        List<OilAccount> account = new ArrayList<>();
        List<RBillitemAccount> rBillitemAccounts = rBillitemAccountMapper.selectList(rBillitemAccount);
        if (CollectionUtil.isNotEmpty(rBillitemAccounts)) {
            for (RBillitemAccount rb : rBillitemAccounts) {
                OilAccount a = new OilAccount();
                a.setLasteditDate(new Date());
                a.setId(rb.getAccountId());
                // 报账完成
                if (bill.getStatus() == 7) {
                    //状态1为正常;2流程中 3报账完成 4已生成归集单 5已退回*/
                    a.setStatus((byte) 3);
                    // 代办
                } else if (bill.getStatus() == 2) {
                    // 2流程中
                    a.setStatus((byte) 2);
                    //报账单删除
                } else if (bill.getStatus() == -1) {
                    // 状态1为正常
                    a.setStatus((byte) 1);
                } else {
                    // 状态4为正常,加入归集单
                    a.setStatus((byte) 4);
                }
                if (ifAll(rb)) {
                    // 已经完成报账的台账 id
                    account.add(a);
                } else {// 验证 部分金额
                    Double aDouble = null;
                    Map<String, Object> param = new HashMap<>();
                    param.put("pcid", rb.getAccountId());
                    param.put("flag", "power_account");
                    //  验证台账是否所有金额都处于报账中或报账完成
                    if (bill.getStatus() == 2) {
                        param.put("status", new String[]{"2", "3", "4", "7"});
                        aDouble = rBillitemAccountMapper.validateMoney(param);
                        // 2流程中
                        a.setStatus((byte) 2);
                    }
                    // 验证是否 所有关联金额 都报账完成
                    if (bill.getStatus() == 7) {
                        param.put("status", new String[]{"7"});
                        aDouble = rBillitemAccountMapper.validateMoney(param);
                        // 3报账完成
                        a.setStatus((byte) 3);
                    }
                    if (aDouble != null && aDouble <= FLOATMONEY && aDouble >= -FLOATMONEY) {
                        // 已经完成报账的台账 id
                        account.add(a);
                    }
                }
            }
        }
        return account;
    }
    private List<PowerAccountEs> handeUpdateAccountsEsByBill(MssAccountbill bill, RBillitemAccount rBillitemAccount) {
        List<PowerAccountEs> account = new ArrayList<>();
        List<RBillitemAccount> rBillitemAccounts = rBillitemAccountMapper.selectList(rBillitemAccount);
        if (rBillitemAccounts != null && rBillitemAccounts.size() > 0) {
            for (RBillitemAccount rb : rBillitemAccounts) {
                PowerAccountEs a = new PowerAccountEs();
                a.setLasteditdate(new Date());
                a.setPcid(rb.getAccountId());
                if (bill.getStatus() == 7) {// 报账完成
                    a.setStatus(3);//状态1为正常;2流程中 3报账完成 4已生成归集单 5已退回*/
                } else if (bill.getStatus() == 2) {// 代办
                    a.setStatus(2); // 2流程中
                } else if (bill.getStatus() == -1) {//报账单删除
                    a.setStatus(1); // 状态1为正常
                } else {
                    a.setStatus(4); // 状态1为正常
                }
                if (ifAll(rb)) {
                    account.add(a);// 已经完成报账的台账 id
                } else {// 验证 部分金额
                    Double aDouble = null;
                    Map<String, Object> param = new HashMap<>();
                    param.put("pcid", rb.getAccountId());
                    param.put("flag", "power_account");
                    if (bill.getStatus() == 2) {//  验证台账是否所有金额都处于报账中或报账完成
                        param.put("status", new String[]{"2", "3", "4", "7"});
                        aDouble = rBillitemAccountMapper.validateMoney(param);
                        a.setStatus(2); // 2流程中
                    }
                    if (bill.getStatus() == 7) {// 验证是否 所有关联金额 都报账完成
                        param.put("status", new String[]{"7"});
                        aDouble = rBillitemAccountMapper.validateMoney(param);
                        a.setStatus(3); // 3报账完成
                    }
                    if (aDouble != null && aDouble <= FLOATMONEY && aDouble >= -FLOATMONEY) {
                        account.add(a);// 已经完成报账的台账 id
                    }
                }
            }
        }
        return account;
    }
    /*根据接口 更新报账 系列状态结束*/

    //////////////////////////////////////////////////////////////
    //////////////////////////////////////////////////////////////////
    /*验证台账可关联金额 开始*/
    /*验证台账可关联金额 开始*/
    @Override // 计算 可关联金额
    public Map<String, Double> listNoPage(String[] toStrArray) {
        Map<String, Double> map = new HashMap<>();
        List<RBillitemAccount> rblist = rBillitemAccountMapper.selectListByPcids(toStrArray);
        countUseMoney(map, rblist);
        subddmoney(toStrArray, map);// 刨除代垫的钱
        return map;
    }

    @Override
    public Map<String, Double> countUseMoneyedit(Map<String, Object> reqMap) {
        String[] ids = Convert.toStrArray(reqMap.get("ids").toString());
        reqMap.put("ids", ids);
        List<RBillitemAccount> rblist = rBillitemAccountMapper.countUseMoneyedit(reqMap);
        Map<String, Double> map = new HashMap<>();
        countUseMoney(map, rblist);
        subddmoney(ids, map);// 刨除代垫的钱
        return map;
    }

    // 验证已经关联金额
    private void countUseMoney(Map<String, Double> map, List<RBillitemAccount> rblist) {
        if (rblist != null && rblist.size() > 0) {
            for (RBillitemAccount rb : rblist) {
                if (ifAll(rb)) {
                    map.put(rb.getAccountId().toString(), rb.getMoney());
                    if (rb.getTaxmoney() != null) {
                        map.put(rb.getAccountId() + "tax", rb.getTaxmoney());
                    } else {
                        map.put(rb.getAccountId() + "tax", 0.0);
                    }
                } else {
                    Double aDouble = map.get(rb.getAccountId().toString());
                    if (aDouble == null) {
                        map.put(rb.getAccountId().toString(), rb.getMoney());
                    } else {
                        map.put(rb.getAccountId().toString(), rb.getMoney() + aDouble);
                    }
                    Double aDoubletax = map.get(rb.getAccountId() + "tax");
                    if (aDoubletax == null) {
                        map.put(rb.getAccountId() + "tax", rb.getTaxmoney());
                    } else {
                        map.put(rb.getAccountId() + "tax", rb.getTaxmoney() + aDoubletax);
                    }
                }
            }
        }
    }

    // 判断台账 金额 是否关联完
    private boolean ifAll(RBillitemAccount rb) {
        Double mm = 0.0;
        if (rb.getTaxmoney() != null) mm = rb.getMoney() + rb.getTaxmoney();
        else mm = rb.getMoney();
        return /*rb.getIfall() == 1L ||*/ rb.getAccountMoney() < mm + FLOATMONEY && rb.getAccountMoney() > mm - FLOATMONEY;
    }

    // 刨除代垫的钱
    private void subddmoney(String[] toStrArray, Map<String, Double> map) {
        List<AccountBaseResult> accountBaseResults = rBillitemAccountMapper.selectAccountAmmeterParent(toStrArray);
        if (accountBaseResults != null && accountBaseResults.size() > 0) {
            for (AccountBaseResult item : accountBaseResults) {// 代垫的台账
                Double oldmoney = map.get(item.getParentPcid().toString());
                Double oldmoneytax = map.get(item.getParentPcid() + "tax");
//                Double money = item.getTaxticketmoney().add(item.getTicketmoney()).add(item.getUllagemoney()).abs().subtract(item.getTaxamount()).doubleValue();
                Double money = item.getAccountmoney().abs().subtract(item.getTaxamount().abs()).doubleValue();
                Double taxmoney = item.getTaxamount().abs().doubleValue();
                map.put(item.getParentPcid().toString(), (oldmoney == null ? 0.0 : oldmoney) + money);
                map.put(item.getParentPcid() + "tax", (oldmoneytax == null ? 0.0 : oldmoneytax) + taxmoney);
            }
        }
    }
    /*验证台账可关联金额 结束*/
    /*验证台账可关联金额 结束*/

    /*生成报账明细开始*/
    /*生成报账明细开始*/
    // 选择归集单后 根据所选 台账自动生成 报账明细
    public List<MssAccountbillitem> addItemByaccounts2(Accountbillitempre accountbillitempre) throws Exception {
        List<AccountBaseResult> accountBaseResults = new ArrayList<>();
        List<AccountEsResult> accountEsResults = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        map.put("parid", accountbillitempre.getParid());
        map.put("deployTo", deployTo);
        String status = accountbillitempre.getStatus();
        log.info("状态：{}", status);
        PageHelper.clearPage();
        if ("1".equals(status) || "3".equals(status) || "9".equals(status)) {// 自有/铁塔台账、包干
            log.info("进入分支1");
            map.put("union", false);
            //accountBaseResults = PageaccountBaseResult(map, accountBaseResults);
            accountBaseResults = accountMapper.selectAccountByAutoMap(map);
        } else if ("2".equals(status) || "7".equals(status) || "11".equals(status) || "13".equals(status) || "15".equals(status) || "17".equals(status)) {// 自有预估/铁塔预估 台账
            log.info("进入分支2");
            map.put("union", false);
            accountEsResults = accountEsMapper.selectAccountEsByAutoMap(map);
        } else if ("4".equals(status) || "6".equals(status) || "10".equals(status)) {// 自有/铁塔台账、包干合并归集单
            log.info("进入分支3");
            map.put("union", true);
            accountBaseResults = accountMapper.selectAccountByAutoMap(map);
        } else if ("5".equals(status) || "8".equals(status) || "12".equals(status) || "14".equals(status) || "16".equals(status) || "18".equals(status)) {// 自有预估/铁塔预估 合并归集单
            log.info("进入分支4");
            map.put("union", true);
            accountEsResults = accountEsMapper.selectAccountEsByAutoMap(map);
        } else {// 默认查询 自有台账
            log.info("进入分支5");
            map.put("union", false);
            accountBaseResults = accountMapper.selectAccountByAutoMap(map);
        }
        log.info("accountBaseResults size:{}", accountBaseResults.size());
        List<MssAccountbillitem> ilist = new ArrayList<>();
        // 处理 自有 台账 数据
        if (accountBaseResults != null) {
            handerRAccount(accountBaseResults, ilist, status, accountbillitempre);
        }
        // 处理 预估 台账 数据
        if (accountEsResults != null) {
            handerRAccount(accountEsResults, ilist, status, accountbillitempre);
        }
        return ilist;
    }

    // 选择归集单后 根据所选 台账自动生成 报账明细
    @Override
    public List<MssAccountbillitem> addItemByaccounts(Accountbillitempre accountbillitempre) throws Exception {
        List<AccountBaseResult> accountBaseResults = new ArrayList<>();
        List<AccountEsResult> accountEsResults = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        map.put("parid", accountbillitempre.getParid());
        map.put("deployTo", deployTo);
        String status = accountbillitempre.getStatus();
        log.info("状态：{}", status);
        PageHelper.clearPage();

        if ("1".equals(status) || "3".equals(status) || "9".equals(status)) {
            // 自有/铁塔台账、包干
            log.info("进入分支1");
            map.put("union", false);
            PageaccountBaseResult(map, accountBaseResults);
            log.info("台账明细列表11 :{}s", accountBaseResults);
        } else if ("2".equals(status) || "7".equals(status) || "11".equals(status) || "13".equals(status) || "15".equals(status) || "17".equals(status)) {
            // 自有预估/铁塔预估 台账
            log.info("进入分支2");
            map.put("union", false);
            PageaccountEsResults(map, accountEsResults);
        } else if ("4".equals(status) || "6".equals(status) || "10".equals(status)) {
            // 自有/铁塔台账、包干合并归集单
            log.info("进入分支3");
            map.put("union", true);
           PageaccountBaseResult(map, accountBaseResults);
        } else if ("5".equals(status) || "8".equals(status) || "12".equals(status) || "14".equals(status) || "16".equals(status) || "18".equals(status)) {
            // 自有预估/铁塔预估 合并归集单
            log.info("进入分支4");
            map.put("union", true);
            PageaccountEsResults(map, accountEsResults);
        } else {
            // 默认查询 自有台账
            log.info("进入分支5");
            map.put("union", false);
            PageaccountBaseResult(map, accountBaseResults);
        }
        log.info("accountBaseResults size:{}", accountBaseResults.size());
        List<MssAccountbillitem> ilist = new ArrayList<>();
        if(!accountBaseResults.isEmpty()){
            // 处理 自有 台账 数据
            handerRAccount(accountBaseResults, ilist, status, accountbillitempre);
        }
        if(!accountEsResults.isEmpty()){
            // 处理 预估 台账 数据
            handerRAccount(accountEsResults, ilist, status, accountbillitempre);
        }
        log.info("ilist size:{}", ilist.size());

        log.info("将台账对应的电表用途写入响应");
        setAmmeuseForLn(ilist);

        return ilist;
    }

    /**
     * 选择归集单后 根据所选 台账自动生成 报账明细  --热力、煤炭、油费报账单
     */
    @Override
    public List<MssAccountbillitem> addNewItemAccounts(Accountbillitempre accountbillitempre) throws Exception {
        List<MssAccountbillitem> ilist = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        map.put("parid", accountbillitempre.getParid());
        String status = accountbillitempre.getStatus();
        PageHelper.clearPage();
        // 热力、煤炭、油费
        if ("19".equals(status) || "20".equals(status) || "21".equals(status)) {
            map.put("union", false);
            // 热力、煤炭、油费合并归集单
        } else if ("22".equals(status) || "23".equals(status) || "24".equals(status)) {
            map.put("union", true);
        }
        ilist = PageNewaccountBaseResult(status,map, ilist,accountbillitempre);
         // 需要纳税调整金额  taxAdjustSum  合计金额 sum  用途id usageId  被后续操作的金额 beAfterOperateSum
        log.info("将台账对应的电表用途写入响应");
        setAmmeuseForLn(ilist);
        return ilist;
    }
    public List<MssAccountbillitem> addItemByaccountsExcluedePcids(Accountbillitempre accountbillitempre, List<Long> accountIds) throws Exception {
        List<AccountBaseResult> accountBaseResults = new ArrayList<>();
        List<AccountEsResult> accountEsResults = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        map.put("parid", accountbillitempre.getParid());
        map.put("deployTo", deployTo);
        String status = accountbillitempre.getStatus();
        log.info("状态：{}", status);
        PageHelper.clearPage();
        if ("1".equals(status) || "3".equals(status) || "9".equals(status)) {// 自有/铁塔台账、包干
            log.info("进入分支1");
            map.put("union", false);
            accountBaseResults = PageaccountBaseResultExcludePcids(map, accountBaseResults, accountIds);
        } else if ("2".equals(status) || "7".equals(status) || "11".equals(status) || "13".equals(status) || "15".equals(status) || "17".equals(status)) {// 自有预估/铁塔预估 台账
            log.info("进入分支2");
            map.put("union", false);
            accountEsResults = PageaccountEsResultsExcludePcids(map, accountEsResults, accountIds);
        } else if ("4".equals(status) || "6".equals(status) || "10".equals(status)) {// 自有/铁塔台账、包干合并归集单
            log.info("进入分支3");
            map.put("union", true);
            accountBaseResults = PageaccountBaseResultExcludePcids(map, accountBaseResults, accountIds);
        } else if ("5".equals(status) || "8".equals(status) || "12".equals(status) || "14".equals(status) || "16".equals(status) || "18".equals(status)) {// 自有预估/铁塔预估 合并归集单
            log.info("进入分支4");
            map.put("union", true);
            accountEsResults = PageaccountEsResultsExcludePcids(map, accountEsResults, accountIds);
        } else {// 默认查询 自有台账
            log.info("进入分支5");
            map.put("union", false);
            accountBaseResults = PageaccountBaseResultExcludePcids(map, accountBaseResults, accountIds);
        }
        log.info("accountBaseResults size:{}", accountBaseResults.size());
        List<MssAccountbillitem> ilist = new ArrayList<>();
        // 处理 自有 台账 数据
        if (accountBaseResults != null) {
            handerRAccount(accountBaseResults, ilist, status, accountbillitempre);
        }
        // 处理 预估 台账 数据
        if (accountEsResults != null) {
            handerRAccount(accountEsResults, ilist, status, accountbillitempre);
        }


        log.info("将台账对应的电表用途写入响应");
        setAmmeuseForLn(ilist);

        return ilist;
    }

    private void setAmmeuseForLn(List<MssAccountbillitem> ilist) {
        if ("ln".equals(deployTo)) {
            ilist.forEach(
                    item -> {
                        List<RBillitemAccount> rbillitemaccounts = item.getRbillitemaccount();
                        rbillitemaccounts.forEach(
                                rBillitemAccount -> {
                                    rBillitemAccount.setAmmeteruse(
                                            mssAccountbillMapper.getAmmeUseForPcid(rBillitemAccount.getAccountId())
                                    );
                                }
                        );
                    }
            );
        }
    }

    // 选择归集单后 根据所选 台账自动生成 报账明细
    public List<MssAccountbillitem> addItemByaccountsgz(Accountbillitempre accountbillitempre) throws Exception {
        List<AccountBaseResult> accountBaseResults = new ArrayList<>();
        List<AccountEsResult> accountEsResults = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        map.put("parid", accountbillitempre.getParid());
        map.put("deployTo", deployTo);
        String status = accountbillitempre.getStatus();
        log.info("状态：{}", status);
        PageHelper.clearPage();
        if ("1".equals(status) || "3".equals(status) || "9".equals(status)) {// 自有/铁塔台账、包干
            log.info("进入分支1");
            map.put("union", false);
            accountBaseResults = PageaccountBaseResult(map, accountBaseResults);
        } else if ("2".equals(status) || "7".equals(status) || "11".equals(status) || "13".equals(status) || "15".equals(status) || "17".equals(status)) {// 自有预估/铁塔预估 台账
            log.info("进入分支2");
            map.put("union", false);
            accountEsResults = PageaccountEsResults(map, accountEsResults);
        } else if ("4".equals(status) || "6".equals(status) || "10".equals(status)) {// 自有/铁塔台账、包干合并归集单
            log.info("进入分支3");
            map.put("union", true);
            accountBaseResults = PageaccountBaseResult(map, accountBaseResults);
        } else if ("5".equals(status) || "8".equals(status) || "12".equals(status) || "14".equals(status) || "16".equals(status) || "18".equals(status)) {// 自有预估/铁塔预估 合并归集单
            log.info("进入分支4");
            map.put("union", true);
            accountEsResults = PageaccountEsResults(map, accountEsResults);
        } else {// 默认查询 自有台账
            log.info("进入分支5");
            map.put("union", false);
            accountBaseResults = PageaccountBaseResult(map, accountBaseResults);
        }
        log.info("accountBaseResults size:{}", accountBaseResults.size());
        List<MssAccountbillitem> ilist = new ArrayList<>();
        // 处理 自有 台账 数据
        if (accountBaseResults != null) {
            handerRAccountgz(accountBaseResults, ilist, status, accountbillitempre);
        }
        // 处理 预估 台账 数据
        if (accountEsResults != null) {
            handerRAccountgz(accountEsResults, ilist, status, accountbillitempre);
        }
        return ilist;
    }

    private List<AccountEsResult> PageaccountEsResults(Map<String, Object> map, List<AccountEsResult> accountEsResults) {
        int limit = 100;
        int offset = 0;
        boolean pageFlag = true;
        map.put("deployTo", deployTo);
        while (pageFlag) {
            List<AccountEsResult> temp = accountEsMapper.selectAccountEsByAutoMap2(map, limit, offset);
            accountEsResults.addAll(temp);
            offset += limit;
            if (temp.size() < limit) {
                pageFlag = false;
            }
        }
        return accountEsResults;
    }

    private List<AccountEsResult> PageaccountEsResultsExcludePcids(Map<String, Object> map, List<AccountEsResult> accountEsResults, List<Long> accountIds) {
        int limit = 100;
        int offset = 0;
        boolean pageFlag = true;
        map.put("deployTo", deployTo);
        while (pageFlag) {
            List<AccountEsResult> temp = accountEsMapper.selectAccountEsByAutoMap2(map, limit, offset);
            //排除指定的台账id
            temp = temp.stream().filter(item -> !accountIds.contains(item.getPcid())).collect(Collectors.toList());
            accountEsResults.addAll(temp);
            offset += limit;
            if (temp.size() < limit) {
                pageFlag = false;
            }
        }
        return accountEsResults;
    }

    private List<AccountBaseResult> PageaccountBaseResult(Map<String, Object> map, List<AccountBaseResult> accountBaseResults) {
        int limit = 300;
        int offset = 0;
        boolean pageFlag = true;
        while (pageFlag) {
            List<AccountBaseResult> ab121 = accountMapper.selectAccountByAutoMap4(map, limit, offset);
            List<AccountBaseResult> aball = accountMapper.selectAccountByAutoMap2(map, limit, offset);
            //List<AccountBaseResult> temp = accountMapper.selectAccountByAutoMap2(map, limit, offset);
            List<AccountBaseResult> newlist = new ArrayList<>();
            if (ab121.size() > 0) {
                for (int i = 0; i < ab121.size(); i++) {
                    AccountBaseResult t = (AccountBaseResult) ab121.get(i);
                    for (int j = 0; j < aball.size(); j++) {
                        AccountBaseResult t1 = (AccountBaseResult) aball.get(j);
                        if (t1.getPcid().longValue() == t.getPcid().longValue()) {
                            t1.setAccountmoney(t1.getAccountmoney().subtract(t.getAccountmoney()));
                            t1.setTotalusedreadings(t1.getTotalusedreadings().subtract(t.getTotalusedreadings()));
                            t1.setTaxamount(t1.getTaxamount().subtract(t.getTaxamount()));
                            t1.setTaxticketmoney(t1.getTaxticketmoney().subtract(t.getTaxticketmoney()));
                            t1.setInputtaxticketmoney(t1.getInputtaxticketmoney().subtract(t.getInputtaxticketmoney()));
                            t1.setInputticketmoney(t1.getInputticketmoney().subtract(t.getInputticketmoney()));
                            t1.setTicketmoney(t1.getTicketmoney().subtract(t.getTicketmoney()));
                            newlist.add(t1);
                        } else
                            newlist.add(t1);
                    }
                    newlist.add(t);
                }
            } else
                newlist = aball;
            //BeanUtils.copyProperties(aball, newlist);
/*
            List<AccountBaseResult> temp1=new ArrayList<>();
            AccountBaseResult t1=new AccountBaseResult();
            BeanUtils.copyProperties(temp, temp1);
            for (int i = 0; i < temp.size(); i++){
                AccountBaseResult ab12=new AccountBaseResult();
                AccountBaseResult t = (AccountBaseResult)temp.get(i);
                  if(t.getElectrotype()==12) {
                      BeanUtils.copyProperties(t, ab12);
                      for (int j = 0; j < temp1.size(); j++) {
                          t1 = (AccountBaseResult) temp1.get(j);
                          if (t1.getPcid()==ab12.getPcid()) {
                              t1.setAccountmoney(t1.getAccountmoney().subtract(ab12.getAccountmoney()));
                              t1.setTotalusedreadings(t1.getTotalusedreadings().subtract(ab12.getTotalusedreadings()));
                              t1.setTaxamount(t1.getTaxamount().subtract(ab12.getTaxamount()));
                              t1.setTaxticketmoney(t1.getTaxticketmoney().subtract(ab12.getTaxticketmoney()));
                              t1.setInputtaxticketmoney(t1.getInputtaxticketmoney().subtract(ab12.getInputtaxticketmoney()));
                              t1.setInputticketmoney(t1.getInputticketmoney().subtract(ab12.getInputticketmoney()));
                              t1.setTicketmoney(t1.getTicketmoney().subtract(ab12.getTicketmoney()));
                              newlist.add(t1);
                          }
                      }
                  }
            }*/
            accountBaseResults.addAll(newlist);
            offset += limit;
            if (aball.size() < limit) {
                pageFlag = false;
            }
        }
        return accountBaseResults;
    }


    private List<MssAccountbillitem> PageNewaccountBaseResult(String status,Map<String, Object> map
            , List<MssAccountbillitem> ilist,Accountbillitempre accountbillitempre) {
        List<MssAccountbillitem> newlist = new ArrayList<>();
        int limit = 300;
        int offset = 0;
        boolean pageFlag = true;
        while (pageFlag) {
            if("19".equals(status) || "22".equals(status)){
                HeatAccountRequest heatAccountRequest = new HeatAccountRequest();
                heatAccountRequest.setUnion((boolean)map.get("union"));
                heatAccountRequest.setParid((Long)map.get("parid"));
                List<HeatAccountVo> heatAccountVos = accountMapper.selectWarmList(heatAccountRequest);
                if(CollectionUtil.isNotEmpty(heatAccountVos)){
                    heatAccountVos.forEach(node->{
                        // 总金额
                        BigDecimal paidMoney = node.getPaidMoney() == null ? new BigDecimal(0) : node.getPaidMoney();
                        // 专票税额
                        BigDecimal taxAmount = node.getTaxAmount() == null ? new BigDecimal(0) : node.getTaxAmount();
                        MssAccountbillitem data = new MssAccountbillitem();
                        if(accountbillitempre.getPlaceid() == null){
                            // 计算全部金额 为 总金额 减去税额
                            data.setSum(paidMoney.subtract(taxAmount));
                            data.setTaxAdjustSum(node.getTaxAmount());
                        }
                        else if (accountbillitempre.getPlaceid() == 1) {
                            // 计算专票金额  专票金额 减去税额
                            data.setSum(node.getTaxTicketMoney().subtract(taxAmount));
                            data.setTaxAdjustSum(node.getTaxAmount());
                        } else if (accountbillitempre.getPlaceid() == 2) {
                              // 计算普票金额 普票加其他费用
                            data.setSum(node.getTicketMoney().add(node.getOtherFee()));
                            // 税额为0
                            data.setTaxAdjustSum(new BigDecimal(0));
                        } else {
                            // 计算全部金额 为 总金额 减去税额
                            data.setSum(paidMoney.subtract(taxAmount));
                            data.setTaxAdjustSum(node.getTaxAmount());
                        }
                        // 固定为经营管理用
                        data.setUsageId("1");
                        // 固定为使用成本预算
                        data.setBudgetType("1");
                        // 固定为生产用
                        data.setBudgetItemId("1000");
                        // 数量 采用的是取暖面积
                        data.setAmount(node.getHeatAreaSize());
                        data.setPrice(node.getUnitPrice());
                        newlist.add(data);
                    });
                }
            }

            if("20".equals(status) || "23".equals(status)){
                CoalAccountRequest coalAccountRequest = new CoalAccountRequest();
                coalAccountRequest.setUnion((boolean)map.get("union"));
                coalAccountRequest.setParid((Long)map.get("parid"));
                List<CoalAccountVo> coalAccountVos = accountMapper.selectCoalList(coalAccountRequest);
                if(CollectionUtil.isNotEmpty(coalAccountVos)){
                    coalAccountVos.forEach(node->{
                        MssAccountbillitem data = new MssAccountbillitem();
                        // 总金额
                        BigDecimal paidMoney = node.getPaidMoney() == null ? new BigDecimal(0) : node.getPaidMoney();
                        // 专票税额
                        BigDecimal taxAmount = node.getTaxAmount() == null ? new BigDecimal(0) : node.getTaxAmount();
                        if(accountbillitempre.getPlaceid() == null){
                            // 计算全部金额 为 总金额 减去税额
                            data.setSum(paidMoney.subtract(taxAmount));
                            data.setTaxAdjustSum(node.getTaxAmount());
                        }
                        else if (accountbillitempre.getPlaceid() == 1) {
                            // 计算专票金额  专票金额 减去税额
                            data.setSum(node.getTaxTicketMoney().subtract(taxAmount));
                            data.setTaxAdjustSum(node.getTaxAmount());
                        } else if (accountbillitempre.getPlaceid() == 2) {
                            // 计算普票金额 普票加其他费用
                            data.setSum(node.getTicketMoney().add(node.getOtherFee()));
                            // 税额为0
                            data.setTaxAdjustSum(new BigDecimal(0));
                        } else {
                            // 计算全部金额 为 总金额 减去税额
                            data.setSum(paidMoney.subtract(taxAmount));
                            data.setTaxAdjustSum(node.getTaxAmount());
                        }
                        // 固定为经营管理用
                        data.setUsageId("1");
                        // 固定为使用成本预算
                        data.setBudgetType("1");
                        // 固定为生产用
                        data.setBudgetItemId("1000");
                        data.setAmount(node.getCoalAmount());
                        data.setPrice(node.getUnitPrice());
                        newlist.add(data);
                    });
                }
            }

            if("21".equals(status) || "24".equals(status)){
                OilAccountRequest oilAccountRequest = new OilAccountRequest();
                oilAccountRequest.setUnion((boolean)map.get("union"));
                oilAccountRequest.setParid((Long)map.get("parid"));
                List<OilAccountVo> oilAccountVos = accountMapper.selectOilList(oilAccountRequest);
                if(CollectionUtil.isNotEmpty(oilAccountVos)){
                    oilAccountVos.forEach(node->{
                        MssAccountbillitem data = new MssAccountbillitem();
                        // 总金额
                        BigDecimal paidMoney = node.getPaidMoney() == null ? new BigDecimal(0) : node.getPaidMoney();
                        // 专票税额
                        BigDecimal taxAmount = node.getTaxAmount() == null ? new BigDecimal(0) : node.getTaxAmount();
                        if(accountbillitempre.getPlaceid() == null){
                            // 计算全部金额 为 总金额 减去税额
                            data.setSum(paidMoney.subtract(taxAmount));
                            data.setTaxAdjustSum(node.getTaxAmount());
                        }
                        else if (accountbillitempre.getPlaceid() == 1) {
                            // 计算专票金额  专票金额 减去税额
                            data.setSum(node.getTaxTicketMoney().subtract(taxAmount));
                            data.setTaxAdjustSum(node.getTaxAmount());
                        } else if (accountbillitempre.getPlaceid() == 2) {
                            // 计算普票金额 普票加其他费用
                            data.setSum(node.getTicketMoney().add(node.getOtherFee()));
                            // 税额为0
                            data.setTaxAdjustSum(new BigDecimal(0));
                        } else {
                            // 计算全部金额 为 总金额 减去税额
                            data.setSum(paidMoney.subtract(taxAmount));
                            data.setTaxAdjustSum(node.getTaxAmount());
                        }
                        // 固定为经营管理用
                        data.setUsageId("1");
                        // 固定为使用成本预算
                        data.setBudgetType("1");
                        // 固定为生产用
                        data.setBudgetItemId("1000");
                        data.setAmount(node.getOilAmount());
                        data.setPrice(node.getUnitPrice());
                        newlist.add(data);
                    });
                }
            }

            ilist.addAll(newlist);
            offset += limit;
            if (newlist.size() < limit) {
                pageFlag = false;
            }
        }
        return ilist;
    }

    private List<AccountBaseResult> PageaccountBaseResultExcludePcids(Map<String, Object> map, List<AccountBaseResult> accountBaseResults, List<Long> accountIds) {
        int limit = 300;
        int offset = 0;
        boolean pageFlag = true;
        while (pageFlag) {
            List<AccountBaseResult> ab121 = accountMapper.selectAccountByAutoMap4(map, limit, offset);
            List<AccountBaseResult> aball = accountMapper.selectAccountByAutoMap2(map, limit, offset);
            //排除指定的台账id
            ab121 = ab121.stream().filter(item -> !accountIds.contains(item.getPcid())).collect(Collectors.toList());
            aball = aball.stream().filter(item -> !accountIds.contains(item.getPcid())).collect(Collectors.toList());
            //List<AccountBaseResult> temp = accountMapper.selectAccountByAutoMap2(map, limit, offset);
            List<AccountBaseResult> newlist = new ArrayList<>();
            if (ab121.size() > 0) {
                for (int i = 0; i < ab121.size(); i++) {
                    AccountBaseResult t = (AccountBaseResult) ab121.get(i);
                    for (int j = 0; j < aball.size(); j++) {
                        AccountBaseResult t1 = (AccountBaseResult) aball.get(j);
                        if (t1.getPcid().longValue() == t.getPcid().longValue()) {
                            t1.setAccountmoney(t1.getAccountmoney().subtract(t.getAccountmoney()));
                            t1.setTotalusedreadings(t1.getTotalusedreadings().subtract(t.getTotalusedreadings()));
                            t1.setTaxamount(t1.getTaxamount().subtract(t.getTaxamount()));
                            t1.setTaxticketmoney(t1.getTaxticketmoney().subtract(t.getTaxticketmoney()));
                            t1.setInputtaxticketmoney(t1.getInputtaxticketmoney().subtract(t.getInputtaxticketmoney()));
                            t1.setInputticketmoney(t1.getInputticketmoney().subtract(t.getInputticketmoney()));
                            t1.setTicketmoney(t1.getTicketmoney().subtract(t.getTicketmoney()));
                            newlist.add(t1);
                        } else
                            newlist.add(t1);
                    }
                    newlist.add(t);
                }
            } else
                newlist = aball;
            //BeanUtils.copyProperties(aball, newlist);
/*
            List<AccountBaseResult> temp1=new ArrayList<>();
            AccountBaseResult t1=new AccountBaseResult();
            BeanUtils.copyProperties(temp, temp1);
            for (int i = 0; i < temp.size(); i++){
                AccountBaseResult ab12=new AccountBaseResult();
                AccountBaseResult t = (AccountBaseResult)temp.get(i);
                  if(t.getElectrotype()==12) {
                      BeanUtils.copyProperties(t, ab12);
                      for (int j = 0; j < temp1.size(); j++) {
                          t1 = (AccountBaseResult) temp1.get(j);
                          if (t1.getPcid()==ab12.getPcid()) {
                              t1.setAccountmoney(t1.getAccountmoney().subtract(ab12.getAccountmoney()));
                              t1.setTotalusedreadings(t1.getTotalusedreadings().subtract(ab12.getTotalusedreadings()));
                              t1.setTaxamount(t1.getTaxamount().subtract(ab12.getTaxamount()));
                              t1.setTaxticketmoney(t1.getTaxticketmoney().subtract(ab12.getTaxticketmoney()));
                              t1.setInputtaxticketmoney(t1.getInputtaxticketmoney().subtract(ab12.getInputtaxticketmoney()));
                              t1.setInputticketmoney(t1.getInputticketmoney().subtract(ab12.getInputticketmoney()));
                              t1.setTicketmoney(t1.getTicketmoney().subtract(ab12.getTicketmoney()));
                              newlist.add(t1);
                          }
                      }
                  }
            }*/
            accountBaseResults.addAll(newlist);
            offset += limit;
            if (aball.size() < limit) {
                pageFlag = false;
            }
        }
        return accountBaseResults;
    }

    private List<AccountBaseResult> PageaccountBaseResult1(Map<String, Object> map, List<AccountBaseResult> accountBaseResults) {
        int limit = 100;
        int offset = 0;
        boolean pageFlag = true;
        while (pageFlag) {
            List<AccountBaseResult> ab121 = accountMapper.selectAccountByAutoMap4(map, limit, offset);
            List<AccountBaseResult> aball = accountMapper.selectAccountByAutoMap2(map, limit, offset);
            List<AccountBaseResult> newlist = new ArrayList<>();
            if (ab121.size() > 0) {
                Map<Long, List<AccountBaseResult>> ab121Map = ab121.stream().filter(result -> {
                    return result.getPcid() != null;
                }).collect(Collectors.groupingBy(result -> {
                    return result.getPcid();
                }));
                BigDecimal percent = null;
                BigDecimal accountmoney = null;
                BigDecimal totalusedreadings = null;
                BigDecimal taxamount = null;
                BigDecimal taxticketmoney = null;
                BigDecimal inputtaxticketmoney = null;
                BigDecimal inputticketmoney = null;
                BigDecimal ticketmoney = null;
                AccountBaseResult t2 = null;
                for (int j = 0; j < aball.size(); j++) {
                    AccountBaseResult t = (AccountBaseResult) aball.get(j);
                    percent = t.getPercent();
                    accountmoney = t.getAccountmoney();
                    totalusedreadings = t.getTotalusedreadings();
                    taxamount = t.getTaxamount();
                    taxticketmoney = t.getTaxticketmoney();
                    inputtaxticketmoney = t.getInputtaxticketmoney();
                    inputticketmoney = t.getInputticketmoney();
                    ticketmoney = t.getTicketmoney();
                    for (Map.Entry<Long, List<AccountBaseResult>> entry : ab121Map.entrySet()) {
                        Long pcid = entry.getKey();
                        List<AccountBaseResult> list = entry.getValue();
                        if (t.getPcid().longValue() == pcid.longValue()) {
                            for (int i = 0; i < list.size(); i++) {
                                percent = percent.subtract(list.get(i).getPercent());
                                if (percent.compareTo(new BigDecimal("0")) <= 0) {
                                    break;
                                }
                                accountmoney = accountmoney.subtract(list.get(i).getAccountmoney());
                                totalusedreadings = totalusedreadings.subtract(list.get(i).getTotalusedreadings());
                                taxamount = taxamount.subtract(list.get(i).getTaxamount());
                                taxticketmoney = taxticketmoney.subtract(list.get(i).getTaxticketmoney());
                                inputtaxticketmoney = inputtaxticketmoney.subtract(list.get(i).getInputtaxticketmoney());
                                inputticketmoney = inputticketmoney.subtract(list.get(i).getInputticketmoney());
                                ticketmoney = ticketmoney.subtract(list.get(i).getTicketmoney());
                                if (i == list.size() - 1) {
                                    t2 = list.get(i);
                                    t2.setPercent(percent);
                                    t2.setAccountmoney(accountmoney);
                                    t2.setTotalusedreadings(totalusedreadings);
                                    t2.setTaxamount(taxamount);
                                    t2.setTaxticketmoney(taxticketmoney);
                                    t2.setInputtaxticketmoney(inputtaxticketmoney);
                                    t2.setInputticketmoney(inputticketmoney);
                                    t2.setTicketmoney(ticketmoney);
                                    newlist.add(t2);
                                    newlist.add(list.get(i));
                                } else {
                                    newlist.add(list.get(i));
                                }
                            }
                        } else {
                            newlist.add(t);
                        }
                    }
                }
            } else {
                newlist.addAll(aball);
            }
            accountBaseResults.addAll(newlist);
            offset += limit;
            if (aball.size() < limit) {
                pageFlag = false;
            }
        }
        return accountBaseResults;
    }

    private void handerRAccountgz(List<?> results, List<MssAccountbillitem> ilist, String status, Accountbillitempre accountbillitempre) throws Exception {
        if ("sc".equals(deployTo)) {
            validIfUnionStation(results);
        }
        List<String> pcidlist = new ArrayList<>();// 所有的 台账 ids
        List<Long> pcidlistdd = new ArrayList<>();// 代垫 台账 ids
        //
        List<Object> sclist = new ArrayList<>();//生产用
        List<Object> sc11list = new ArrayList<>();//生产用 固网机房电费
        List<Object> sc12list = new ArrayList<>();//生产用 IDC电费
        List<Object> sc13list = new ArrayList<>();//生产用 其他
        List<Object> sc14list = new ArrayList<>();//生产用 移动基站电费
        List<Object> sc20list = new ArrayList<>();//管理办公
        List<Object> gllist = new ArrayList<>();//其他用
        List<Object> ttlist = new ArrayList<>();//铁塔用
        List<Object> pflist = new ArrayList<>();//四川普服用
        List<AccountBaseResult> ddlist = new ArrayList<>();//代垫台账
        int i = 0;
        for (Object o : results) {
            if (o instanceof AccountBaseResult) {
                AccountBaseResult item = (AccountBaseResult) o;
                pcidlist.add(item.getPcid().toString());
                i++;
                if (item.getStatus() != 5) {// 退回的 不加入
                    if ("sc".equals(deployTo) && item.getPustatus() != null && item.getPustatus() == 1) {
                        pflist.add(item);
                    } else {
                        if (item.getCategory() != null && (item.getCategory() == 4 || item.getCategory() == 5 || "3".equals(item.getAmmeteruse()))) { // 回收电费
                            ddlist.add(item);
                            pcidlistdd.add(item.getPcid());//代垫的 加入 pcidlistdd
                        } else if (item.getElectrotype() != null && item.getElectrotype() <= 20) {
                            //sclist.add(item);
                            if (item.getElectrotype() == 11)
                                sc11list.add(item);
                            else if (item.getElectrotype() == 12)
                                sc12list.add(item);
                            else if (item.getElectrotype() == 13)
                                sc13list.add(item);
                            else if (item.getElectrotype() == 14)
                                sc14list.add(item);
                            else
                                sc20list.add(item);
                        } else {
                            gllist.add(item);
                        }
                        ttlist.add(item);// 铁塔 不区分生产管理用 全部加入
                    }
                }
            }
            if (o instanceof AccountEsResult) {
                AccountEsResult item = (AccountEsResult) o;
                pcidlist.add(item.getPcid().toString());
                i++;
                if (item.getStatus() != 5) {// 退回的 不加入
                    if ("sc".equals(deployTo) && item.getPustatus() != null && item.getPustatus() == 1) {
                        pflist.add(item);
                    } else {
                        if (item.getElectrotype() != null && item.getElectrotype() <= 20) {
                            //sclist.add(item);
                            if (item.getElectrotype() == 11)
                                sc11list.add(item);
                            else if (item.getElectrotype() == 12)
                                sc12list.add(item);
                            else if (item.getElectrotype() == 13)
                                sc13list.add(item);
                            else if (item.getElectrotype() == 14)
                                sc14list.add(item);
                            else
                                sc20list.add(item);
                        } else {
                            gllist.add(item);
                        }
                        ttlist.add(item);// 铁塔 不区分生产管理用 全部加入
                    }
                }
            }
        }
        // 查询是否已关联报账明细 封装 已关联金额
        if (pcidlist.size() > 0) {
            String[] strs1 = pcidlist.toArray(new String[pcidlist.size()]);
            List<RBillitemAccount> rblist = new ArrayList<>();
            List<RBillitemAccount> ddrblist = new ArrayList<>();// 代垫的关联关系
            List<RBillitemAccount> rBillitemAccounts = rBillitemAccountMapper.selectListByPcids(strs1);
            for (RBillitemAccount rb : rBillitemAccounts) {// 区分 出代垫的list
                if (pcidlistdd.contains(rb.getAccountId())) {
                    ddrblist.add(rb);// 代垫的关联关系
                } else {
                    rblist.add(rb);
                }
            }
            if (ttlist.size() > 0 && ("3".equals(status) || "6".equals(status) || "7".equals(status) || "8".equals(status) || "9".equals(status) || "10".equals(status) || "15".equals(status) || "16".equals(status) || "17".equals(status) || "18".equals(status))) {// 铁塔 不区分生产管理用
//                3铁塔  6铁塔合并 7 铁塔预估 8铁塔预估合并 9铁塔包干 10铁塔包干合并  15铁塔挂账 16铁塔挂账合并
                setDatagz(ilist, ttlist, rblist, ddrblist, ddlist, "2000", accountbillitempre);//铁塔用
            } else {
              /*  if (sclist.size() > 0)
                    setData(ilist, sclist, rblist, ddrblist, ddlist, "1000", accountbillitempre);*/
                if (sc11list.size() > 0)
                    setDatagz(ilist, sc11list, rblist, ddrblist, ddlist, "1000", accountbillitempre);
                if (sc12list.size() > 0)
                    setData12(ilist, sc12list, rblist, ddrblist, ddlist, "1000", accountbillitempre);
                if (sc13list.size() > 0)
                    setDatagz(ilist, sc13list, rblist, ddrblist, ddlist, "1000", accountbillitempre);
                if (sc14list.size() > 0)
                    setDatagz(ilist, sc14list, rblist, ddrblist, ddlist, "1000", accountbillitempre);
                if (sc20list.size() > 0)
                    setDatagz(ilist, sc20list, rblist, ddrblist, ddlist, "1000", accountbillitempre);
                if (gllist.size() > 0) setDatagz(ilist, gllist, rblist, ddrblist, ddlist, "1145", accountbillitempre);
            }
            if (pflist.size() > 0)//普服
                setDatagz(ilist, pflist, rblist, ddrblist, ddlist, "pf", accountbillitempre);
            if (ddlist.size() > 0) {// 代垫
                setDataDD(ilist, ddlist, ddrblist);
                setDataddLn(ilist, sclist, gllist);
            }
        }
    }

    private void handerRAccount(List<?> results, List<MssAccountbillitem> ilist, String status, Accountbillitempre accountbillitempre) throws Exception {
        if ("sc".equals(deployTo)) {
            validIfUnionStation(results);
        }
        // 所有的 台账 ids
        List<String> pcidlist = new ArrayList<>();
        // 代垫 台账 ids
        List<Long> pcidlistdd = new ArrayList<>();
        //生产用
        List<Object> sclist = new ArrayList<>();
        //回收用
        List<Object> list = new ArrayList<>();
        //生产用 固网机房电费
        List<Object> sc11list = new ArrayList<>();
        //生产用 IDC电费
        List<Object> sc12list = new ArrayList<>();
        //生产用 其他
        List<Object> sc13list = new ArrayList<>();
        //生产用 移动基站电费
        List<Object> sc14list = new ArrayList<>();
        //管理办公
        List<Object> sc20list = new ArrayList<>();
        //其他用
        List<Object> gllist = new ArrayList<>();
        //铁塔用
        List<Object> ttlist = new ArrayList<>();
        //四川普服用
        List<Object> pflist = new ArrayList<>();
        //代垫台账
        List<AccountBaseResult> ddlist = new ArrayList<>();
        int i = 0;
        for (Object o : results) {
            if (o instanceof AccountBaseResult) {
                AccountBaseResult item = (AccountBaseResult) o;
                pcidlist.add(item.getPcid().toString());
                i++;
                // 退回的 不加入
                if (item.getStatus() != 5) {
                    if ("sc".equals(deployTo) && item.getPustatus() != null && item.getPustatus() == 1) {
                        pflist.add(item);
                    } else {
                        // 回收电费
                        if (item.getCategory() != null && (item.getCategory() == 4 || item.getCategory() == 5 || "3".equals(item.getAmmeteruse()))) {
                            ddlist.add(item);
                            //代垫的 加入 pcidlistdd
                            pcidlistdd.add(item.getPcid());
                            list.add(item);
                        } else if (item.getElectrotype() != null && item.getElectrotype() <= 20) {
                            //sclist.add(item);
                            if (item.getElectrotype() == 11){
                                sc11list.add(item);
                            } else if (item.getElectrotype() == 12){
                                sc12list.add(item);
                            } else if (item.getElectrotype() == 13){
                                sc13list.add(item);

                            } else if (item.getElectrotype() == 14){
                                sc14list.add(item);
                            } else {
                                sc20list.add(item);

                            }
                        } else {
                            gllist.add(item);
                        }
                        // 铁塔 不区分生产管理用 全部加入
                        ttlist.add(item);
                    }
                }
            }
            if (o instanceof AccountEsResult) {
                AccountEsResult item = (AccountEsResult) o;
                pcidlist.add(item.getPcid().toString());
                i++;
                // 退回的 不加入
                if (item.getStatus() != 5) {
                    if ("sc".equals(deployTo) && item.getPustatus() != null && item.getPustatus() == 1) {
                        pflist.add(item);
                    } else {
                        if (item.getElectrotype() != null && item.getElectrotype() <= 20) {
                            //sclist.add(item);
                            if (item.getElectrotype() == 11){
                                sc11list.add(item);
                            }else if (item.getElectrotype() == 12){
                                sc12list.add(item);
                            } else if (item.getElectrotype() == 13){
                                sc13list.add(item);
                            } else if (item.getElectrotype() == 14){
                                sc14list.add(item);
                            } else{
                                sc20list.add(item);
                            }
                        } else {
                            gllist.add(item);
                        }
                        // 铁塔 不区分生产管理用 全部加入
                        ttlist.add(item);
                    }
                }
            }
        }
        // 查询是否已关联报账明细 封装 已关联金额
        if (!pcidlist.isEmpty()) {
            String[] strs1 = pcidlist.toArray(new String[pcidlist.size()]);
            List<RBillitemAccount> rblist = new ArrayList<>();
            // 代垫的关联关系
            List<RBillitemAccount> ddrblist = new ArrayList<>();
            List<RBillitemAccount> rBillitemAccounts = rBillitemAccountMapper.selectListByPcids(strs1);
            // 区分 出代垫的list
            for (RBillitemAccount rb : rBillitemAccounts) {
                if (pcidlistdd.contains(rb.getAccountId())) {
                    // 代垫的关联关系
                    ddrblist.add(rb);
                } else {
                    rblist.add(rb);
                }
            }
            //  3铁塔  6铁塔合并 7 铁塔预估 8铁塔预估合并 9铁塔包干 10铁塔包干合并  15铁塔挂账 16铁塔挂账合并
            if (ttlist.size() > 0 && ("3".equals(status) || "6".equals(status) || "7".equals(status) || "8".equals(status) || "9".equals(status) || "10".equals(status) || "15".equals(status) || "16".equals(status) || "17".equals(status) || "18".equals(status))) {
                // 铁塔 不区分生产管理用
                //铁塔用
                setData(ilist, ttlist, rblist, ddrblist, ddlist, "2000", accountbillitempre);
            } else {
              /*  if (sclist.size() > 0)
                    setData(ilist, sclist, rblist, ddrblist, ddlist, "1000", accountbillitempre);*/
                if (!sc11list.isEmpty()){
                    setData(ilist, sc11list, rblist, ddrblist, ddlist, "1000", accountbillitempre);
                }
                if (!sc12list.isEmpty()){
                    setData12(ilist, sc12list, rblist, ddrblist, ddlist, "1000", accountbillitempre);
                }
                if (!sc13list.isEmpty()){
                    setData(ilist, sc13list, rblist, ddrblist, ddlist, "1000", accountbillitempre);
                }
                if (!sc14list.isEmpty()){
                    setData(ilist, sc14list, rblist, ddrblist, ddlist, "1000", accountbillitempre);
                }
                if (!sc20list.isEmpty()){
                    setData(ilist, sc20list, rblist, ddrblist, ddlist, "1000", accountbillitempre);
                }
                if (!gllist.isEmpty()){
                    setData(ilist, gllist, rblist, ddrblist, ddlist, "1145", accountbillitempre);
                }
            }
            //普服
            if (!pflist.isEmpty()){
                setData(ilist, pflist, rblist, ddrblist, ddlist, "pf", accountbillitempre);
            }
            // 代垫
            if (!ddlist.isEmpty()) {
                //只有在辽宁环境并且 是基站回收电费才执行 hsFlag=true的分支
                boolean flag1 = "ln".equals(deployTo);
                boolean flag2 = flag1 && mssAccountbillMapper.verdictJZ(accountbillitempre);
                boolean jzhsFlag = flag1 && flag2;
                if (jzhsFlag) {
                    if ("sc".equals(deployTo)) {
                        setDataDD(ilist, ddlist, ddrblist);
                    } else {
                        if (StringUtils.isEmpty(ilist)) {
                           // setDataDD(ilist, ddlist, ddrblist);
                            setData(ilist, list, rblist, ddrblist, ddlist, "1000", accountbillitempre);
                        }
                        setDataddLn(ilist, sclist, gllist);
                    }
                } else {
                    if (StringUtils.isEmpty(ilist)) {
                        // setDataDD(ilist, ddlist, ddrblist);
                        setData(ilist, list, rblist, ddrblist, ddlist, "1000", accountbillitempre);
                    }
                    setDataddLn(ilist, sclist, gllist);
                }

            }
        }
    }


    private void handerRAccountNew(List<?> results, List<MssAccountbillitem> ilist, String status, Accountbillitempre accountbillitempre) throws Exception {
        // 所有的 台账 ids
        List<String> pcidlist = new ArrayList<>();
        // 代垫 台账 ids
        List<Long> pcidlistdd = new ArrayList<>();
        //生产用
        List<Object> sclist = new ArrayList<>();
        //其他用
        List<Object> gllist = new ArrayList<>();
        //代垫台账
        List<AccountBaseResult> ddlist = new ArrayList<>();
        int i = 0;
        for (Object o : results) {
            if (o instanceof AccountBaseResult) {
                AccountBaseResult item = (AccountBaseResult) o;
                pcidlist.add(item.getPcid().toString());
                i++;
                gllist.add(item);
            }
        }
        // 查询是否已关联报账明细 封装 已关联金额
        if (pcidlist.size() > 0) {
            String[] strs1 = pcidlist.toArray(new String[pcidlist.size()]);
            List<RBillitemAccount> rblist = new ArrayList<>();
            // 代垫的关联关系
            List<RBillitemAccount> ddrblist = new ArrayList<>();
            List<RBillitemAccount> rBillitemAccounts = rBillitemAccountMapper.selectListByPcids(strs1);
            // 区分 出代垫的list
            for (RBillitemAccount rb : rBillitemAccounts) {
                if (pcidlistdd.contains(rb.getAccountId())) {
                    // 代垫的关联关系
                    ddrblist.add(rb);
                } else {
                    rblist.add(rb);
                }
            }
            setDataDD(ilist, ddlist, ddrblist);
            setDataddLn(ilist, sclist, gllist);
        }
    }

    private void setDataddLn(List<MssAccountbillitem> ilist, List<Object> sclist, List<Object> gllist) {
        // 收款的税是不看类型的，只要有税就是进项税
        if ("ln".equals(deployTo) && sclist.isEmpty() && gllist.isEmpty()) {
            //辽宁 收款 代垫拆分成两个明细
            List<MssAccountbillitem> ilisttax = new ArrayList<>();
            for (MssAccountbillitem item : ilist) {
                //无发票 没有 税
                if (null != item.getTaxAdjustSum() && item.getTaxAdjustSum().compareTo(BigDecimal.ZERO) != 0) {
                    MssAccountbillitem tax = new MssAccountbillitem();
                    tax.setBudgetItemId(item.getBudgetItemId());
                    tax.setBudgetType("2");
                    tax.setSum(BigDecimal.ZERO);
                    //U889进项税（集成）
                    tax.setUsageId("8");
                    tax.setTaxAdjustSum(item.getTaxAdjustSum());
                    List<RBillitemAccount> rlist = item.getRbillitemaccount();
                    List<RBillitemAccount> taxrlist = new ArrayList<>();
                    for (RBillitemAccount rb : rlist) {
                        if (rb.getTaxmoney() != 0.0) {
                            RBillitemAccount rbtax = new RBillitemAccount();
                            rbtax.setIfall(0L);
                            rbtax.setAccountMoney(rb.getAccountMoney());
                            rbtax.setAccountId(rb.getAccountId());
                            rbtax.setMoney(0.0);
                            rbtax.setTaxmoney(rb.getTaxmoney());
                            rb.setTaxmoney(0.0);
                            rb.setIfall(0L);
                            taxrlist.add(rbtax);
                        }
                    }
                    tax.setRbillitemaccount(taxrlist);
                    //  关联的电量 除以 关联含税金额
                    BigDecimal amount = BigDecimal.ZERO;
                    amount = item.getAmount().multiply(item.getTaxAdjustSum().divide(item.getSum().add(item.getTaxAdjustSum()), 6, BigDecimal.ROUND_HALF_UP)).setScale(2, BigDecimal.ROUND_HALF_UP);
                    tax.setAmount(amount);
                    tax.setPrice(item.getPrice());
                    tax.setAmount(amount);
                    item.setAmount(item.getAmount().subtract(amount));
//                    item.setUsageId("11");
                    item.setBudgetType("1");
                    item.setTaxAdjustSum(BigDecimal.ZERO);
                    ilisttax.add(tax);
                } else {
                    // 如收入表台账全是普票金额，归集单生成报账单账单类型默认为“收款”，
                    // 并自动生成一条明细，用途为“经营管理用”，列账属性默认为“不使用成本”。
                    // 该收款报账单没有U889进行税明细，但必须选择供应商/客户，如没选择供应商/客户，提示用户需选择供应商/客户才能保存单据
//                    item.setUsageId("11");
                    item.setBudgetType("2");
                }
            }
            ilist.addAll(ilisttax);
        }
    }

    // 四川的报账单反选归集单时要做个控制： 用电类型是生产类用电的
    // 新增报账单反选归集单时需校验归集单内的电表/协议是否关联局站，如果没有关联局站，
    // 不能生成报账单，提示：“（电表/协议项目名称）没有关联局站信息，请先到基础数据中关联局站信息
    private void validIfUnionStation(List<?> results) throws Exception {
        for (Object o : results) {
            if (o instanceof AccountBaseResult) {
                AccountBaseResult item = (AccountBaseResult) o;
                if (item.getElectrotype() != null && item.getElectrotype() == 1 && StringUtils.isEmpty(item.getStationName())) {//生产用
                    throw new BaseException("没有关联局站信息，请先到基础数据中关联局站信息");
                }
            }
            if (o instanceof AccountEsResult) {
                AccountEsResult item = (AccountEsResult) o;
                if (item.getElectrotype() != null && item.getElectrotype() == 1 && StringUtils.isEmpty(item.getStationName())) {//生产用
                    throw new BaseException("没有关联局站信息，请先到基础数据中关联局站信息");
                }
            }
        }
    }

    //生成  报账明细 和报账明细台账关联关系
    private void setData12(List<MssAccountbillitem> ilist, List<Object> sclist, List<RBillitemAccount> rblist, List<RBillitemAccount> ddrblist, List<AccountBaseResult> ddlist, String type, Accountbillitempre accountbillitempre) throws Exception {
        List<RBillitemAccount> rlist = new ArrayList<>();
        MssAccountbillitem mssitem = new MssAccountbillitem();
        if (type.equals("pf")) {
            mssitem.setBudgetItemId("1000");
            mssitem.setOrderNo("************");//四川普服项目
        } else {
            mssitem.setBudgetItemId(type);
        }
        BigDecimal total = new BigDecimal(0);
        BigDecimal taxtotal = new BigDecimal(0);
        BigDecimal totalusedreadings = new BigDecimal(0);// 计算 单价
        Integer uasgeid = 0;
        for (Object o : sclist) {
            RBillitemAccount rb = new RBillitemAccount();
            double accountMoney = 0;
            double money = 0;
            double taxmoney = 0;
            BigDecimal unitpirce = new BigDecimal(0);
            if (o instanceof AccountBaseResult) {
                AccountBaseResult item = (AccountBaseResult) o;
                rb.setAccountId(item.getPcid());
                uasgeid = item.getElectrotype();
//                accountMoney = item.getTaxticketmoney().add(item.getTicketmoney()).add(item.getUllagemoney()).subtract(item.getTaxamount()).doubleValue();
                // placeid 生成专票金额为1，普票为2 默认为 null
                if (accountbillitempre.getPlaceid() == null) {
                    // 修改 计算规则 为 总金额 减去税额 适应预估台账规则
                    accountMoney = item.getAccountmoney().subtract(item.getTaxamount()).doubleValue();
                    taxmoney = item.getTaxamount().doubleValue();
                } else if (accountbillitempre.getPlaceid() == 1) {
                    accountMoney = item.getTaxticketmoney().subtract(item.getTaxamount()).doubleValue();
                    taxmoney = item.getTaxamount().doubleValue();
                } else if (accountbillitempre.getPlaceid() == 2) {// 普票加其他
                    accountMoney = item.getTicketmoney().add(item.getUllagemoney()).doubleValue();
                } else {
                    // 修改 计算规则 为 总金额 减去税额 适应预估台账规则
                    accountMoney = item.getAccountmoney().subtract(item.getTaxamount()).doubleValue();
                    taxmoney = item.getTaxamount().doubleValue();
                }
                unitpirce = item.getUnitpirce();// 单价 关联的金额 除单价 就是关联的电量
                if (accountbillitempre.getPlaceid() == null) {
                    money = accountMoney - handerMoney(rblist, item.getPcid());// 计算 已关联金额
                    taxmoney = taxmoney - handerTaxMoney(rblist, item.getPcid());// 计算 已关联税额
                } else {
                    money = accountMoney;//已关联金额中包含了普票金额，未分开记录专票、普票各自关联金额，无法分别计算。
                }

                // 判断是否包含代垫的台账
/*                for (AccountBaseResult dd : ddlist) {
                    if (dd.getAmmparentid() != null && dd.getAmmparentid().equals(item.getAmmeterid())) {
                        if (accountbillitempre.getPlaceid() == null) {
                            money = new BigDecimal(money).subtract(dd.getAccountmoney().abs().subtract(dd.getTaxamount().abs())).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();// 计算代垫 已关联金额;
//                                dd.getTaxticketmoney().add(dd.getTicketmoney()).add(dd.getUllagemoney()).abs().subtract(dd.getTaxamount()).doubleValue();
                            taxmoney = new BigDecimal(taxmoney).subtract(dd.getTaxamount().abs()).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                        } else {
                            //已关联金额中包含了普票金额，未分开记录专票、普票各自关联金额，无法分别计算。
                            //System.out.println("dd.getAccountmoney().abs()="+dd.getAccountmoney().abs());
                            //System.out.println("dd.getTaxamount().abs()="+dd.getTaxamount().abs().abs());
                            money -= dd.getAccountmoney().abs().subtract(dd.getTaxamount().abs()).doubleValue() - handerMoney(ddrblist, dd.getPcid());// 计算代垫 已关联金额;
                            taxmoney -= dd.getTaxamount().abs().doubleValue() - handerTaxMoney(ddrblist, dd.getPcid());
                        }
                        // 避免计算造成的精度丢失，保留两位小数
                        money = new BigDecimal(money).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                        taxmoney = new BigDecimal(taxmoney).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                        if (money < 0) {
                            throw new BaseException(item.getProjectname() + "电表【" + item.getAmmetercode() + "】代垫后 报账不含税金额小于0！！！");
                        }
                        if (taxmoney < -0.1) { // 优化为不超过10分
                            throw new BaseException(item.getProjectname() + "电表【" + item.getAmmetercode() + "】代垫后 报账税额小于0！！！");
                        }
//                        break;
                    }
                }*/
                // 修改 计算电量为根据 关联总金额的百分比
                totalusedreadings = totalusedreadings.add(item.getTotalusedreadings().multiply(BigDecimal.valueOf(money + taxmoney).divide(item.getAccountmoney(), 6, BigDecimal.ROUND_HALF_UP)));
            }
            if (o instanceof AccountEsResult) {
                AccountEsResult item = (AccountEsResult) o;
                rb.setAccountId(item.getPcid());
                uasgeid = item.getElectrotype();
                if (item.getTaxamount() != null) {
                    accountMoney = item.getAccountmoney().subtract(item.getTaxamount()).doubleValue();
                    taxmoney = item.getTaxamount().doubleValue();
                    if (accountbillitempre.getPlaceid() == null) {
                        money = accountMoney - handerMoney(rblist, item.getPcid());// 计算 已关联金额
                        taxmoney = taxmoney - handerTaxMoney(rblist, item.getPcid());// 计算 已关联税额
                    } else {
                        money = accountMoney;//已关联金额中包含了普票金额，未分开记录专票、普票各自关联金额，无法分别计算。
                    }
                } else {
                    accountMoney = item.getAccountmoney().doubleValue();
                    money = accountMoney - handerMoney(rblist, item.getPcid());
                }
                unitpirce = item.getUnitpirce();// 单价
                // 修改 计算电量为根据 关联总金额的百分比
                totalusedreadings = totalusedreadings.add(item.getCurusedreadings().multiply(BigDecimal.valueOf(money + taxmoney).divide(item.getAccountmoney(), 6, BigDecimal.ROUND_HALF_UP)));
            }

            rb.setAccountMoney(accountMoney);//不含税价
            rb.setMoney(money);
            rb.setTaxmoney(taxmoney);

            total = total.add(BigDecimal.valueOf(money));
            taxtotal = taxtotal.add(BigDecimal.valueOf(taxmoney));
            // 计算 关联的 电量 关联的金额 除单价 就是关联的电量
//            BigDecimal ttt = BigDecimal.valueOf(money).add(BigDecimal.valueOf(taxmoney));
//            totalusedreadings = totalusedreadings.add(ttt.divide(unitpirce, 2, BigDecimal.ROUND_HALF_UP));
            if (rb.getAccountMoney().equals(rb.getMoney())) {
                rb.setIfall(1L);
            } else {
                rb.setIfall(0L);
            }
            // if (rb.getMoney() > 0)//有金额才关联  负数处理
            rlist.add(rb);

        }
        if (total.doubleValue() != 0)  //可以增加负数
        {
            mssitem.setSum(total);
            mssitem.setTaxAdjustSum(taxtotal);
            mssitem.setRbillitemaccount(rlist);
/*            if (totalusedreadings.compareTo(BigDecimal.ZERO) == 0) {
                throw new BaseException(mssitem.getSum() + "电量为0");
            }*/ //可以增加负数
            BigDecimal price = new BigDecimal("0");

            if ("sc".equals(deployTo))
                price = total.add(taxtotal).divide(totalusedreadings, 2, BigDecimal.ROUND_HALF_UP);
            else

                price = total.divide(totalusedreadings, 2, BigDecimal.ROUND_HALF_UP);

            //  关联的电量 除以 关联含税金额
            mssitem.setPrice(price);
            totalusedreadings = totalusedreadings.setScale(2, BigDecimal.ROUND_HALF_UP);
            mssitem.setAmount(totalusedreadings);
            mssitem.setBeAfterOperateSum(BigDecimal.ZERO);
            mssitem.setUsageId(Integer.toString(uasgeid));//23年用途
            ilist.add(mssitem);
        }
    }

    //生成  报账明细 和报账明细台账关联关系
    private void setDatagz(List<MssAccountbillitem> ilist, List<Object> sclist, List<RBillitemAccount> rblist, List<RBillitemAccount> ddrblist, List<AccountBaseResult> ddlist, String type, Accountbillitempre accountbillitempre) throws Exception {
        List<RBillitemAccount> rlist = new ArrayList<>();
        MssAccountbillitem mssitem = new MssAccountbillitem();
        if (type.equals("pf")) {
            mssitem.setBudgetItemId("1000");
            mssitem.setOrderNo("************");//四川普服项目
        } else {
            mssitem.setBudgetItemId(type);
        }
        BigDecimal total = new BigDecimal(0);
        BigDecimal taxtotal = new BigDecimal(0);
        BigDecimal totalusedreadings = new BigDecimal(0);// 计算 单价
        Integer uasgeid = 0;
        for (Object o : sclist) {
            RBillitemAccount rb = new RBillitemAccount();
            double accountMoney = 0;
            double money = 0;
            double taxmoney = 0;
            BigDecimal unitpirce = new BigDecimal(0);
            if (o instanceof AccountBaseResult) {
                AccountBaseResult item = (AccountBaseResult) o;
                rb.setAccountId(item.getPcid());
                if (type.equals("2000"))
                    uasgeid = 6;
                else
                    uasgeid = item.getElectrotype();
//                accountMoney = item.getTaxticketmoney().add(item.getTicketmoney()).add(item.getUllagemoney()).subtract(item.getTaxamount()).doubleValue();
                // placeid 生成专票金额为1，普票为2 默认为 null
                if (accountbillitempre.getPlaceid() == null) {
                    // 修改 计算规则 为 总金额 减去税额 适应预估台账规则
                    accountMoney = item.getAccountmoney().subtract(item.getTaxamount()).doubleValue();
                    taxmoney = item.getTaxamount().doubleValue();
                } else if (accountbillitempre.getPlaceid() == 1) {
                    accountMoney = item.getTaxticketmoney().subtract(item.getTaxamount()).doubleValue();
                    taxmoney = item.getTaxamount().doubleValue();
                } else if (accountbillitempre.getPlaceid() == 2) {// 普票加其他
                    accountMoney = item.getTicketmoney().add(item.getUllagemoney()).doubleValue();
                } else {
                    // 修改 计算规则 为 总金额 减去税额 适应预估台账规则
                    accountMoney = item.getAccountmoney().subtract(item.getTaxamount()).doubleValue();
                    taxmoney = item.getTaxamount().doubleValue();
                }
                unitpirce = item.getUnitpirce();// 单价 关联的金额 除单价 就是关联的电量
                if (accountbillitempre.getPlaceid() == null) {
                    /*money = accountMoney - handerMoney(rblist, item.getPcid());// 计算 已关联金额*/
                    money = accountMoney;
                    /*taxmoney = taxmoney - handerTaxMoney(rblist, item.getPcid());// 计算 已关联税额*/
                    taxmoney = taxmoney;
                } else {
                    money = accountMoney;//已关联金额中包含了普票金额，未分开记录专票、普票各自关联金额，无法分别计算。
                }

                // 判断是否包含代垫的台账
                for (AccountBaseResult dd : ddlist) {
                    if (dd.getAmmparentid() != null && dd.getAmmparentid().equals(item.getAmmeterid())) {
                        if (accountbillitempre.getPlaceid() == null) {
                            money = new BigDecimal(money).subtract(dd.getAccountmoney().abs().subtract(dd.getTaxamount().abs())).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();// 计算代垫 已关联金额;
//                                dd.getTaxticketmoney().add(dd.getTicketmoney()).add(dd.getUllagemoney()).abs().subtract(dd.getTaxamount()).doubleValue();
                            taxmoney = new BigDecimal(taxmoney).subtract(dd.getTaxamount().abs()).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                        } else {
                            //已关联金额中包含了普票金额，未分开记录专票、普票各自关联金额，无法分别计算。
                            //System.out.println("dd.getAccountmoney().abs()="+dd.getAccountmoney().abs());
                            //System.out.println("dd.getTaxamount().abs()="+dd.getTaxamount().abs().abs());
                            money -= dd.getAccountmoney().abs().subtract(dd.getTaxamount().abs()).doubleValue() - handerMoney(ddrblist, dd.getPcid());// 计算代垫 已关联金额;
                            taxmoney -= dd.getTaxamount().abs().doubleValue() - handerTaxMoney(ddrblist, dd.getPcid());
                        }
                        // 避免计算造成的精度丢失，保留两位小数
                        money = new BigDecimal(money).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                        taxmoney = new BigDecimal(taxmoney).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                        if (money < 0) {
                            throw new BaseException(item.getProjectname() + "电表【" + item.getAmmetercode() + "】代垫后 报账不含税金额小于0！！！");
                        }
                        if (taxmoney < -0.1) { // 优化为不超过10分
                            throw new BaseException(item.getProjectname() + "电表【" + item.getAmmetercode() + "】代垫后 报账税额小于0！！！");
                        }
//                        break;
                    }
                }
                // 修改 计算电量为根据 关联总金额的百分比
                totalusedreadings = totalusedreadings.add(item.getTotalusedreadings().multiply(BigDecimal.valueOf(money + taxmoney).divide(item.getAccountmoney(), 6, BigDecimal.ROUND_HALF_UP)));
            }
            if (o instanceof AccountEsResult) {
                AccountEsResult item = (AccountEsResult) o;
                rb.setAccountId(item.getPcid());
                if (type.equals("2000"))
                    uasgeid = 6;
                else
                    uasgeid = item.getElectrotype();
                if (item.getTaxamount() != null) {
                    accountMoney = item.getAccountmoney().subtract(item.getTaxamount()).doubleValue();
                    taxmoney = item.getTaxamount().doubleValue();
                    if (accountbillitempre.getPlaceid() == null) {
                        money = accountMoney - handerMoney(rblist, item.getPcid());// 计算 已关联金额
                        taxmoney = taxmoney - handerTaxMoney(rblist, item.getPcid());// 计算 已关联税额
                    } else {
                        money = accountMoney;//已关联金额中包含了普票金额，未分开记录专票、普票各自关联金额，无法分别计算。
                    }
                } else {
                    accountMoney = item.getAccountmoney().doubleValue();
                    money = accountMoney - handerMoney(rblist, item.getPcid());
                }
                unitpirce = item.getUnitpirce();// 单价
                // 修改 计算电量为根据 关联总金额的百分比
                totalusedreadings = totalusedreadings.add(item.getCurusedreadings().multiply(BigDecimal.valueOf(money + taxmoney).divide(item.getAccountmoney(), 6, BigDecimal.ROUND_HALF_UP)));
            }

            rb.setAccountMoney(accountMoney);//不含税价
            rb.setMoney(money);
            rb.setTaxmoney(taxmoney);

            total = total.add(BigDecimal.valueOf(money));
            taxtotal = taxtotal.add(BigDecimal.valueOf(taxmoney));
            // 计算 关联的 电量 关联的金额 除单价 就是关联的电量
//            BigDecimal ttt = BigDecimal.valueOf(money).add(BigDecimal.valueOf(taxmoney));
//            totalusedreadings = totalusedreadings.add(ttt.divide(unitpirce, 2, BigDecimal.ROUND_HALF_UP));
            if (rb.getAccountMoney().equals(rb.getMoney())) {
                rb.setIfall(1L);
            } else {
                rb.setIfall(0L);
            }
            // if (rb.getMoney() > 0)//有金额才关联  负数处理
            rlist.add(rb);

        }
        if (total.doubleValue() != 0)  //可以增加负数
        {
            mssitem.setSum(total);
            mssitem.setTaxAdjustSum(taxtotal);
            mssitem.setRbillitemaccount(rlist);
/*            if (totalusedreadings.compareTo(BigDecimal.ZERO) == 0) {
                throw new BaseException(mssitem.getSum() + "电量为0");
            }*/ //可以增加负数
            BigDecimal price = new BigDecimal("0");

            if ("sc".equals(deployTo))
                price = total.add(taxtotal).divide(totalusedreadings, 2, BigDecimal.ROUND_HALF_UP);
            else

                price = total.divide(totalusedreadings, 2, BigDecimal.ROUND_HALF_UP);

            //  关联的电量 除以 关联含税金额
            mssitem.setPrice(price);
            totalusedreadings = totalusedreadings.setScale(2, BigDecimal.ROUND_HALF_UP);
            mssitem.setAmount(totalusedreadings);
            mssitem.setBeAfterOperateSum(BigDecimal.ZERO);
            mssitem.setUsageId(Integer.toString(uasgeid));//23年用途
            ilist.add(mssitem);
        }
    }

    //生成  报账明细 和报账明细台账关联关系
    private void setData(List<MssAccountbillitem> ilist, List<Object> sclist, List<RBillitemAccount> rblist, List<RBillitemAccount> ddrblist, List<AccountBaseResult> ddlist, String type, Accountbillitempre accountbillitempre) throws Exception {
        List<RBillitemAccount> rlist = new ArrayList<>();
        MssAccountbillitem mssitem = new MssAccountbillitem();
        if (type.equals("pf")) {
            mssitem.setBudgetItemId("1000");
            //四川普服项目
            mssitem.setOrderNo("************");
        } else {
            mssitem.setBudgetItemId(type);
        }
        BigDecimal total = new BigDecimal(0);
        BigDecimal taxtotal = new BigDecimal(0);
        // 计算 单价
        BigDecimal totalusedreadings = new BigDecimal(0);
        Integer uasgeid = 0;
        Integer electrotype = 0;
        Integer category = 0;
        Long electroId = 0L;
        for (Object o : sclist) {
            RBillitemAccount rb = new RBillitemAccount();
            double accountMoney = 0;
            double money = 0;
            double taxmoney = 0;
            BigDecimal unitpirce = new BigDecimal(0);
            if (o instanceof AccountBaseResult) {
                AccountBaseResult item = (AccountBaseResult) o;
                rb.setAccountId(item.getPcid());
                electrotype = item.getElectrotype();
                category = item.getCategory();
                electroId = item.getElectroId();
                if (type.equals("2000")){
                    uasgeid = 6;
                } else {
                    uasgeid = item.getElectrotype();
                }
//                accountMoney = item.getTaxticketmoney().add(item.getTicketmoney()).add(item.getUllagemoney()).subtract(item.getTaxamount()).doubleValue();
                // placeid 生成专票金额为1，普票为2 默认为 null
                if (accountbillitempre.getPlaceid() == null) {
                    // 修改 计算规则 为 总金额 减去税额 适应预估台账规则
                    accountMoney = item.getAccountmoney().subtract(item.getTaxamount()).doubleValue();
                    taxmoney = item.getTaxamount().doubleValue();
                } else if (accountbillitempre.getPlaceid() == 1) {
                    accountMoney = item.getTaxticketmoney().subtract(item.getTaxamount()).doubleValue();
                    taxmoney = item.getTaxamount().doubleValue();
                    // 普票加其他
                } else if (accountbillitempre.getPlaceid() == 2) {
                    accountMoney = item.getTicketmoney().add(item.getUllagemoney()).doubleValue();
                } else {
                    // 修改 计算规则 为 总金额 减去税额 适应预估台账规则
                    accountMoney = item.getAccountmoney().subtract(item.getTaxamount()).doubleValue();
                    taxmoney = item.getTaxamount().doubleValue();
                }
                // 单价 关联的金额 除单价 就是关联的电量
                unitpirce = item.getUnitpirce();
                if (accountbillitempre.getPlaceid() == null) {
                    // 计算 已关联金额
                    money = accountMoney - handerMoney(rblist, item.getPcid());
                    // 计算 已关联税额
                    taxmoney = taxmoney - handerTaxMoney(rblist, item.getPcid());
                } else {
                    //已关联金额中包含了普票金额，未分开记录专票、普票各自关联金额，无法分别计算。
                    money = accountMoney;
                }
                if (money==0.0)
                    continue;
                // 判断是否包含代垫的台账
                for (AccountBaseResult dd : ddlist) {
                    if (dd.getAmmparentid() != null && dd.getAmmparentid().equals(item.getAmmeterid())) {
                        if (accountbillitempre.getPlaceid() == null) {
                            // 计算代垫 已关联金额;
                            money = new BigDecimal(money).subtract(dd.getAccountmoney().abs().subtract(dd.getTaxamount().abs())).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
//                                dd.getTaxticketmoney().add(dd.getTicketmoney()).add(dd.getUllagemoney()).abs().subtract(dd.getTaxamount()).doubleValue();
                            taxmoney = new BigDecimal(taxmoney).subtract(dd.getTaxamount().abs()).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                        } else {
                            //已关联金额中包含了普票金额，未分开记录专票、普票各自关联金额，无法分别计算。
                            //System.out.println("dd.getAccountmoney().abs()="+dd.getAccountmoney().abs());
                            //System.out.println("dd.getTaxamount().abs()="+dd.getTaxamount().abs().abs());
                            // 计算代垫 已关联金额;
                            money -= dd.getAccountmoney().abs().subtract(dd.getTaxamount().abs()).doubleValue() - handerMoney(ddrblist, dd.getPcid());
                            taxmoney -= dd.getTaxamount().abs().doubleValue() - handerTaxMoney(ddrblist, dd.getPcid());
                        }
                        // 避免计算造成的精度丢失，保留两位小数
                        money = new BigDecimal(money).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                        taxmoney = new BigDecimal(taxmoney).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                        if (money < 0) {
                            throw new BaseException(item.getProjectname() + "电表【" + item.getAmmetercode() + "】代垫后 报账不含税金额小于0！！！");
                        }
                        // 优化为不超过10分
                        if (taxmoney < -0.1) {
                            throw new BaseException(item.getProjectname() + "电表【" + item.getAmmetercode() + "】代垫后 报账税额小于0！！！");
                        }
//                        break;
                    }
                }
                // 修改 计算电量为根据 关联总金额的百分比
                totalusedreadings = totalusedreadings.add(item.getTotalusedreadings().multiply(BigDecimal.valueOf(money + taxmoney).divide(item.getAccountmoney(), 6, BigDecimal.ROUND_HALF_UP)));
            }
            if (o instanceof AccountEsResult) {
                AccountEsResult item = (AccountEsResult) o;
                rb.setAccountId(item.getPcid());
                electrotype = item.getElectrotype();
                category = item.getCategory();
                electroId = item.getElectroId();
                if (type.equals("2000")) {
                    uasgeid = 6;
                }
                else {
                    uasgeid = item.getElectrotype();
                }
                if (item.getTaxamount() != null) {
                    accountMoney = item.getAccountmoney().subtract(item.getTaxamount()).doubleValue();
                    taxmoney = item.getTaxamount().doubleValue();
                    if (accountbillitempre.getPlaceid() == null) {
                        // 计算 已关联金额
                        money = accountMoney - handerMoney(rblist, item.getPcid());
                        // 计算 已关联税额
                        taxmoney = taxmoney - handerTaxMoney(rblist, item.getPcid());
                    } else {
                        //已关联金额中包含了普票金额，未分开记录专票、普票各自关联金额，无法分别计算。
                        money = accountMoney;
                    }
                } else {
                    accountMoney = item.getAccountmoney().doubleValue();
                    money = accountMoney - handerMoney(rblist, item.getPcid());
                }
                // 单价
                unitpirce = item.getUnitpirce();
                // 修改 计算电量为根据 关联总金额的百分比
                totalusedreadings = totalusedreadings.add(item.getCurusedreadings().multiply(BigDecimal.valueOf(money + taxmoney).divide(item.getAccountmoney(), 6, BigDecimal.ROUND_HALF_UP)));
            }

            rb.setAccountMoney(accountMoney);//不含税价
            rb.setMoney(money);
            rb.setTaxmoney(taxmoney);

            total = total.add(BigDecimal.valueOf(money));
            taxtotal = taxtotal.add(BigDecimal.valueOf(taxmoney));
            // 计算 关联的 电量 关联的金额 除单价 就是关联的电量
//            BigDecimal ttt = BigDecimal.valueOf(money).add(BigDecimal.valueOf(taxmoney));
//            totalusedreadings = totalusedreadings.add(ttt.divide(unitpirce, 2, BigDecimal.ROUND_HALF_UP));
            if (rb.getAccountMoney().equals(rb.getMoney())) {
                rb.setIfall(1L);
            } else {
                rb.setIfall(0L);
            }
            // if (rb.getMoney() > 0)//有金额才关联  负数处理
            rlist.add(rb);

        }
        //可以增加负数
        if (total.doubleValue() != 0) {
            mssitem.setSum(total);
            mssitem.setTaxAdjustSum(taxtotal);
            mssitem.setRbillitemaccount(rlist);
/*            if (totalusedreadings.compareTo(BigDecimal.ZERO) == 0) {
                throw new BaseException(mssitem.getSum() + "电量为0");
            }*/ //可以增加负数
            BigDecimal price = new BigDecimal("0");

            if ("sc".equals(deployTo)) {
                price = total.add(taxtotal).divide(totalusedreadings, 2, BigDecimal.ROUND_HALF_UP);
            } else {
                price = total.divide(totalusedreadings, 2, BigDecimal.ROUND_HALF_UP);
            }

            //  关联的电量 除以 关联含税金额
            mssitem.setPrice(price);
            totalusedreadings = totalusedreadings.setScale(2, BigDecimal.ROUND_HALF_UP);
            mssitem.setAmount(totalusedreadings);
            mssitem.setBeAfterOperateSum(BigDecimal.ZERO);
            // 当协议为类型为收入有表协议，
            // 电表为用途回收电费，且用电类型为其它用电时，
            // 若是税额为0，则报账单明细处的用途则为其它，若是税额大于0，则明细处的用途则为 ：U889进顶税(集成)
            if("13".equals(electrotype.toString()) && category == 5 && electroId == 4L) {
                // 若是税额大于0，则明细处的用途则为 ：U889进顶税(集成)
                if(null != mssitem.getTaxAdjustSum() && mssitem.getTaxAdjustSum().compareTo(BigDecimal.ZERO) >0){
                    mssitem.setUsageId("8");
                }else {
                    mssitem.setUsageId("13");
                }
            } else {
                //23年用途
                mssitem.setUsageId(Integer.toString(uasgeid));
            }
            ilist.add(mssitem);
        }
    }

    private void setData2(List<MssAccountbillitem> ilist, List<Object> sclist, List<RBillitemAccount> rblist, List<RBillitemAccount> ddrblist, List<AccountBaseResult> ddlist, String type, Accountbillitempre accountbillitempre) throws Exception {
        List<RBillitemAccount> rlist = new ArrayList<>();
        MssAccountbillitem mssitem = new MssAccountbillitem();
        if (type.equals("pf")) {
            mssitem.setBudgetItemId("1000");
            mssitem.setOrderNo("************");//四川普服项目
        } else {
            mssitem.setBudgetItemId(type);
        }
        BigDecimal total = new BigDecimal(0);
        BigDecimal taxtotal = new BigDecimal(0);
        BigDecimal totalusedreadings = new BigDecimal(0);// 计算 单价
        Integer uasgeid = 0;
        for (Object o : sclist) {
            RBillitemAccount rb = new RBillitemAccount();
            BigDecimal accountMoney = BigDecimal.ZERO;
            BigDecimal money = BigDecimal.ZERO;
            double taxmoney = 0;
            BigDecimal unitpirce = new BigDecimal(0);
            if (o instanceof AccountBaseResult) {
                AccountBaseResult item = (AccountBaseResult) o;
                rb.setAccountId(item.getPcid());
                uasgeid = item.getElectrotype();
//                accountMoney = item.getTaxticketmoney().add(item.getTicketmoney()).add(item.getUllagemoney()).subtract(item.getTaxamount()).doubleValue();
                // placeid 生成专票金额为1，普票为2 默认为 null
                if (accountbillitempre.getPlaceid() == null) {
                    // 修改 计算规则 为 总金额 减去税额 适应预估台账规则
                    accountMoney = item.getAccountmoney().subtract(item.getTaxamount());
                    taxmoney = item.getTaxamount().doubleValue();
                } else if (accountbillitempre.getPlaceid() == 1) {
                    accountMoney = item.getTaxticketmoney().subtract(item.getTaxamount());
                    taxmoney = item.getTaxamount().doubleValue();
                } else if (accountbillitempre.getPlaceid() == 2) {// 普票加其他
                    accountMoney = item.getTicketmoney().add(item.getUllagemoney());
                } else {
                    // 修改 计算规则 为 总金额 减去税额 适应预估台账规则
                    accountMoney = item.getAccountmoney().subtract(item.getTaxamount());
                    taxmoney = item.getTaxamount().doubleValue();
                }
                unitpirce = item.getUnitpirce();// 单价 关联的金额 除单价 就是关联的电量
                if (accountbillitempre.getPlaceid() == null) {
                    money = accountMoney.subtract(handerMoney2(rblist, item.getPcid()));// 计算 已关联金额
                    taxmoney = taxmoney - handerTaxMoney(rblist, item.getPcid());// 计算 已关联税额
                } else {
                    money = accountMoney;//已关联金额中包含了普票金额，未分开记录专票、普票各自关联金额，无法分别计算。
                }

                // 判断是否包含代垫的台账
                for (AccountBaseResult dd : ddlist) {
                    if (dd.getAmmparentid() != null && dd.getAmmparentid().equals(item.getAmmeterid())) {
                        if (accountbillitempre.getPlaceid() == null) {
                            money = money.subtract(dd.getAccountmoney().abs().subtract(dd.getTaxamount().abs()));// 计算代垫 已关联金额;
//                                dd.getTaxticketmoney().add(dd.getTicketmoney()).add(dd.getUllagemoney()).abs().subtract(dd.getTaxamount()).doubleValue();
                            taxmoney = new BigDecimal(taxmoney).subtract(dd.getTaxamount().abs()).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                        } else {
                            //已关联金额中包含了普票金额，未分开记录专票、普票各自关联金额，无法分别计算。
                            //System.out.println("dd.getAccountmoney().abs()="+dd.getAccountmoney().abs());
                            //System.out.println("dd.getTaxamount().abs()="+dd.getTaxamount().abs().abs());
                            BigDecimal abs = dd.getAccountmoney().abs();
                            BigDecimal abs1 = dd.getTaxamount().abs();
                            BigDecimal v = handerMoney2(ddrblist, dd.getPcid());
                            money = money.subtract(abs.subtract(abs1).subtract(v));// 计算代垫 已关联金额;

                            taxmoney -= dd.getTaxamount().abs().doubleValue() - handerTaxMoney(ddrblist, dd.getPcid());
                        }
                        // 避免计算造成的精度丢失，保留两位小数
                        taxmoney = new BigDecimal(taxmoney).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                        if (money.compareTo(BigDecimal.ZERO) == -1) {
                            throw new BaseException(item.getProjectname() + "电表【" + item.getAmmetercode() + "】代垫后 报账不含税金额小于0！！！");
                        }
                        if (taxmoney < -0.1) { // 优化为不超过10分
                            throw new BaseException(item.getProjectname() + "电表【" + item.getAmmetercode() + "】代垫后 报账税额小于0！！！");
                        }
//                        break;
                    }
                }
                // 修改 计算电量为根据 关联总金额的百分比
                totalusedreadings = totalusedreadings.add(item.getTotalusedreadings().multiply(BigDecimal.valueOf(money.doubleValue() + taxmoney).divide(item.getAccountmoney(), 6, BigDecimal.ROUND_HALF_UP)));
            }
            if (o instanceof AccountEsResult) {
                AccountEsResult item = (AccountEsResult) o;
                rb.setAccountId(item.getPcid());
                uasgeid = item.getElectrotype();
                if (item.getTaxamount() != null) {
                    accountMoney = item.getAccountmoney().subtract(item.getTaxamount());
                    taxmoney = item.getTaxamount().doubleValue();
                    if (accountbillitempre.getPlaceid() == null) {
                        money = accountMoney.subtract(handerMoney2(rblist, item.getPcid()));// 计算 已关联金额
                        taxmoney = taxmoney - handerTaxMoney(rblist, item.getPcid());// 计算 已关联税额
                    } else {
                        money = accountMoney;//已关联金额中包含了普票金额，未分开记录专票、普票各自关联金额，无法分别计算。
                    }
                } else {
                    accountMoney = item.getAccountmoney();
                    money = accountMoney.subtract(handerMoney2(rblist, item.getPcid()));
                }
                unitpirce = item.getUnitpirce();// 单价
                // 修改 计算电量为根据 关联总金额的百分比
                totalusedreadings = totalusedreadings.add(item.getCurusedreadings().multiply(BigDecimal.valueOf(money.doubleValue() + taxmoney).divide(item.getAccountmoney(), 6, BigDecimal.ROUND_HALF_UP)));
            }

            rb.setAccountMoney(accountMoney.doubleValue());//不含税价
            rb.setMoney(money.doubleValue());
            rb.setTaxmoney(taxmoney);

            total = total.add(BigDecimal.valueOf(money.doubleValue()));
            taxtotal = taxtotal.add(BigDecimal.valueOf(taxmoney));
            // 计算 关联的 电量 关联的金额 除单价 就是关联的电量
//            BigDecimal ttt = BigDecimal.valueOf(money).add(BigDecimal.valueOf(taxmoney));
//            totalusedreadings = totalusedreadings.add(ttt.divide(unitpirce, 2, BigDecimal.ROUND_HALF_UP));
            if (rb.getAccountMoney().equals(rb.getMoney())) {
                rb.setIfall(1L);
            } else {
                rb.setIfall(0L);
            }
            // if (rb.getMoney() > 0)//有金额才关联  负数处理
            rlist.add(rb);

        }
        if (total.doubleValue() != 0)  //可以增加负数
        {
            mssitem.setSum(total);
            mssitem.setTaxAdjustSum(taxtotal);
            mssitem.setRbillitemaccount(rlist);
/*            if (totalusedreadings.compareTo(BigDecimal.ZERO) == 0) {
                throw new BaseException(mssitem.getSum() + "电量为0");
            }*/ //可以增加负数
            BigDecimal price = new BigDecimal("0");

            if ("sc".equals(deployTo))
                price = total.add(taxtotal).divide(totalusedreadings, 2, BigDecimal.ROUND_HALF_UP);
            else

                price = total.divide(totalusedreadings, 2, BigDecimal.ROUND_HALF_UP);

            //  关联的电量 除以 关联含税金额
            mssitem.setPrice(price);
            totalusedreadings = totalusedreadings.setScale(2, BigDecimal.ROUND_HALF_UP);
            mssitem.setAmount(totalusedreadings);
            mssitem.setBeAfterOperateSum(BigDecimal.ZERO);
            mssitem.setUsageId(Integer.toString(uasgeid));//23年用途
            ilist.add(mssitem);
        }
    }

    //生成 代垫 明细 和关联关系
    private void setDataDD(List<MssAccountbillitem> ilist, List<AccountBaseResult> ddlist, List<RBillitemAccount> rblist) throws Exception {
        log.info("ddlist size:{},list:{}", ddlist.size(), ddlist);
        //获取代垫的客户list
        List<String> customerList = new ArrayList<>();
        for (AccountBaseResult item : ddlist) {
            RBillitemAccount rb = new RBillitemAccount();
            double accountMoney = 0;
            double money = 0;
            double taxmoney = 0;
            Integer uasgeId = item.getElectrotype();
            Integer electrotype = item.getElectrotype();
            Integer category = item.getCategory();
            Long electroId = item.getElectroId();
            BigDecimal beAfterOperateSum = new BigDecimal(BigInteger.ZERO);
            rb.setAccountId(item.getPcid());
            if (item.getTaxamount() != null) {
                taxmoney = item.getTaxamount().abs().doubleValue();
                rb.setTaxmoney(taxmoney - handerTaxMoney(rblist, item.getPcid()));
//                accountMoney = item.getTaxticketmoney().add(item.getTicketmoney()).add(item.getUllagemoney()).abs().subtract(item.getTaxamount()).doubleValue();
                // 修改 计算规则 为 总金额 减去税额 适应预估台账规则
                accountMoney = item.getAccountmoney().abs().subtract(item.getTaxamount().abs()).doubleValue();
            } else {
//                accountMoney = item.getTaxticketmoney().add(item.getTicketmoney()).add(item.getUllagemoney()).abs().doubleValue();
                accountMoney = item.getAccountmoney().abs().doubleValue();
            }//.abs() 绝对值
            // 计算 已关联金额
            money = accountMoney - handerMoney(rblist, item.getPcid());
            // 专票金额
            beAfterOperateSum = beAfterOperateSum.add(item.getTaxticketmoney() == null ? BigDecimal.ZERO : item.getTaxticketmoney().abs());
            //不含税价
            rb.setAccountMoney(accountMoney);
            rb.setMoney(money);
            if (rb.getAccountMoney().equals(rb.getMoney())) {
                rb.setIfall(1L);
            } else {
                rb.setIfall(0L);
            }
            if (StringUtils.isNotEmpty(item.getCustomerId())) {
                // 不包含 就添加一个报账明细
                if (!customerList.contains(item.getCustomerId())) {
                    // 记录 客户
                    customerList.add(item.getCustomerId());
                    //有金额才关联
                    if (rb.getMoney() > 0) {
                        MssAccountbillitem mssitem = new MssAccountbillitem();
                        // 代垫转售金额
                        mssitem.setBeAfterOperateSum(beAfterOperateSum);
                        mssitem.setDebitAccountCode(item.getCustomerId());
                        mssitem.setDebitAccountName(item.getCustomerName());
                        //生产用
                        if (item.getElectrotype() != null && item.getElectrotype() == 1) {
                            mssitem.setBudgetItemId("1000");
                        } else {
                            mssitem.setBudgetItemId("1145");
                        }
                        //支付代垫外单位或员工电费
                        // 当协议为类型为收入有表协议，
                        // 电表为用途回收电费，且用电类型为其它用电时，
                        // 若是税额为0，则报账单明细处的用途则为其它，若是税额大于0，则明细处的用途则为 ：U889进顶税(集成)
                        if("13".equals(electrotype.toString()) && category == 5 && electroId == 4L) {
                            // 若是税额大于0，则明细处的用途则为 ：U889进顶税(集成)
                            if(null != mssitem.getTaxAdjustSum() && mssitem.getTaxAdjustSum().compareTo(BigDecimal.ZERO) >0){
                                mssitem.setUsageId("8");
                            }else {
                                mssitem.setUsageId("13");
                            }
                        } else {
                            //23年用途
                            mssitem.setUsageId(Integer.toString(uasgeId));
                        }

                        mssitem.setSum(BigDecimal.valueOf(money));
                        mssitem.setTaxAdjustSum(BigDecimal.valueOf(taxmoney));
                        mssitem.setAmount(item.getTotalusedreadings() == null ? BigDecimal.ZERO : item.getTotalusedreadings().abs());
                        mssitem.setPrice(item.getUnitpirce() == null ? BigDecimal.ZERO : item.getUnitpirce().abs());
                        List<RBillitemAccount> rlist = new ArrayList<>();
                        rlist.add(rb);
                        mssitem.setRbillitemaccount(rlist);
                        mssitem.setCompanyId(item.getCompanyId());
                        mssitem.setCountryId(item.getCountryId());
                        ilist.add(mssitem);
                    }
                } else {// 包含 在明细当中就直接累加金额
                    for (MssAccountbillitem mssitem : ilist) {
                        if (item.getCustomerId().equals(mssitem.getDebitAccountCode())) {
                            mssitem.setSum(mssitem.getSum().add(BigDecimal.valueOf(money)));
                            mssitem.setTaxAdjustSum(mssitem.getTaxAdjustSum().add(BigDecimal.valueOf(taxmoney)));
                            List<RBillitemAccount> rbillitemaccount = mssitem.getRbillitemaccount();
                            rbillitemaccount.add(rb);
                            mssitem.setRbillitemaccount(rbillitemaccount);
                            // 代垫 就 一条台账
                            mssitem.setAmount(mssitem.getAmount().add(item.getTotalusedreadings() == null ? BigDecimal.ZERO : item.getTotalusedreadings().abs()));
                            mssitem.setPrice(mssitem.getSum().add(mssitem.getTaxAdjustSum()).divide(mssitem.getAmount(), 4, BigDecimal.ROUND_HALF_UP));
                            // 代垫转售金额
                            mssitem.setBeAfterOperateSum(mssitem.getBeAfterOperateSum().add(beAfterOperateSum));
                        } else {
//                            if ("2".equals(mssitem.getUsageId()) && StringUtils.isEmpty(mssitem.getDebitAccountCode()))
//                                throw new BaseException(item.getProjectname() + "代垫没有客户信息！！！");
                        }
                    }
                }
            } else {
                //有金额才关联
                if (rb.getMoney() > 0) {
                    MssAccountbillitem mssitem = new MssAccountbillitem();
                    // 代垫转售金额
                    mssitem.setBeAfterOperateSum(beAfterOperateSum);
                    //生产用
                    if (item.getElectrotype() != null && item.getElectrotype() == 1) {
                        mssitem.setBudgetItemId("1000");
                    } else {
                        mssitem.setBudgetItemId("1145");
                    }
                    //支付代垫外单位或员工电费
                    // 当协议为类型为收入有表协议，
                    // 电表为用途回收电费，且用电类型为其它用电时，
                    // 若是税额为0，则报账单明细处的用途则为其它，若是税额大于0，则明细处的用途则为 ：U889进顶税(集成)
                    if("13".equals(electrotype.toString()) && category == 5 && electroId == 4L) {
                        // 若是税额大于0，则明细处的用途则为 ：U889进顶税(集成)
                        if(null != mssitem.getTaxAdjustSum() && mssitem.getTaxAdjustSum().compareTo(BigDecimal.ZERO) >0){
                            mssitem.setUsageId("8");
                        }else {
                            mssitem.setUsageId("13");
                        }
                    } else {
                        //23年用途
                        mssitem.setUsageId(Integer.toString(uasgeId));
                    }
                    mssitem.setSum(BigDecimal.valueOf(money));
                    mssitem.setTaxAdjustSum(BigDecimal.valueOf(taxmoney));
                    List<RBillitemAccount> rlist = new ArrayList<>();
                    rlist.add(rb);
                    mssitem.setRbillitemaccount(rlist);
                    //  关联的电量 除以 关联含税金额 // 代垫的 单价 就是 台账单价
                    mssitem.setPrice(item.getUnitpirce());
                    // 代垫 就 一条台账
                    mssitem.setAmount(item.getTotalusedreadings());
                        ilist.add(mssitem);
                }
            }
        }
    }

    public static void main(String[] args) {
        System.out.println(0.1 + 0.2);
    }

    private double handerMoney(List<RBillitemAccount> rblist, Long accountId) {
        double res = 0;
        if (rblist != null && rblist.size() > 0) {
            for (RBillitemAccount rb : rblist) {
                if (accountId.equals(rb.getAccountId())) {
                    res += (rb.getMoney() == null ? 0 : rb.getMoney());
                    if (ifAll(rb)) {
                        break;
                    }
                }
            }
        }
        return res;
    }

    private BigDecimal handerMoney2(List<RBillitemAccount> rblist, Long accountId) {
        BigDecimal res = BigDecimal.ZERO;
        if (rblist != null && rblist.size() > 0) {
            for (RBillitemAccount rb : rblist) {
                if (accountId.equals(rb.getAccountId())) {
                    double v = rb.getMoney() == null ? 0 : rb.getMoney();
                    BigDecimal temp = new BigDecimal(String.valueOf(v));
                    res = res.add(temp);
                    if (ifAll(rb)) {
                        break;
                    }
                }
            }
        }
        return res;
    }

    private double handerTaxMoney(List<RBillitemAccount> rblist, Long accountId) {
        double res = 0;
        if (rblist != null && rblist.size() > 0) {
            for (RBillitemAccount rb : rblist) {
                if (accountId.equals(rb.getAccountId())) {
                    res += (rb.getTaxmoney() == null ? 0 : rb.getTaxmoney());
                    if (ifAll(rb)) {
                        break;
                    }
                }
            }
        }
        return res;
    }

    private BigDecimal handerMoney1(List<RBillitemAccount> rblist, Long accountId) {
        BigDecimal res = new BigDecimal("0");
        double money = 0;
        if (rblist != null && rblist.size() > 0) {
            for (RBillitemAccount rb : rblist) {
                if (accountId.equals(rb.getAccountId())) {
                    money = rb.getMoney() == null ? 0 : rb.getMoney();
                    res = res.add(new BigDecimal(Double.toString(money)));
                    if (ifAll(rb)) {
                        break;
                    }
                }
            }
        }
        return res;
    }

    private BigDecimal handerTaxMoney1(List<RBillitemAccount> rblist, Long accountId) {
        BigDecimal res = new BigDecimal("0");
        double money = 0;
        if (rblist != null && rblist.size() > 0) {
            for (RBillitemAccount rb : rblist) {
                if (accountId.equals(rb.getAccountId())) {
                    money = rb.getTaxmoney() == null ? 0 : rb.getTaxmoney();
                    res = res.add(new BigDecimal(Double.toString(money)));
                    if (ifAll(rb)) {
                        break;
                    }
                }
            }
        }
        return res;
    }
    /*生成报账明细结束*/
    /*生成报账明细结束*/

    @Override
    public List<AccountBaseResult> accountlistBybillId(RBillitemAccount rBillitemAccount) {
        rBillitemAccount.setSearchValue(deployTo);
        if (rBillitemAccount.getSearchValue().equals("ln")) {
            return rBillitemAccountMapper.accountlistLnBybillId(rBillitemAccount);
        } else {
            List<AccountBaseResult> list = rBillitemAccountMapper.accountlistBybillId(rBillitemAccount);
            return list;
        }
    }

    @Override
    public List<StationbybillResult> stationlistBybillId(RBillitemAccount rBillitemAccount) {
        rBillitemAccount.setSearchValue(deployTo);
        List<StationbybillResult> stationbybillResults = rBillitemAccountMapper.stationlistBybillId(rBillitemAccount);
        // 根据局站id获取对应的电表/协议编号和项目名称
        getMeterInformation(stationbybillResults);

        return stationbybillResults;
    }

/*    private void getMeterInformation(List<StationbybillResult> stationbybillResults) {
        for (StationbybillResult stationbybillResult : stationbybillResults) {
            // 电表列表
            List<PowerAmmeterorprotocol> ammeterorprotocolList = new PowerAmmeterorprotocol().selectList(Wrappers.<PowerAmmeterorprotocol>lambdaQuery()
                    .select(
                            PowerAmmeterorprotocol::getCategory,
                            PowerAmmeterorprotocol::getAmmetername,
                            PowerAmmeterorprotocol::getProtocolname,
                            PowerAmmeterorprotocol::getProjectname
                    )
                    .eq(PowerAmmeterorprotocol::getStationcode, stationbybillResult.getId())
            );
            StringBuilder nameBuilder = new StringBuilder();
            for (PowerAmmeterorprotocol ammeterorprotocol : ammeterorprotocolList) {
                if (ammeterorprotocol.getCategory().equals("1")) {
                    nameBuilder.append(ammeterorprotocol.getAmmetername()).append(",");
                } else {
                    nameBuilder.append(ammeterorprotocol.getProtocolname()).append(",");
                }
            }
            if (nameBuilder.length() > 0) {
                nameBuilder.setLength(nameBuilder.length() - 1);
            }
            stationbybillResult.setAmmetername(nameBuilder.toString().trim());

            // 设置项目名称
            stationbybillResult.setProjectname(
                    ammeterorprotocolList.stream().map(PowerAmmeterorprotocol::getProjectname).collect(Collectors.joining(","))
            );
        }
    }*/

    private void getMeterInformation(List<StationbybillResult> stationbybillResults) {
        if (stationbybillResults == null || stationbybillResults.isEmpty()) {
            return;
        }

        // 1. 收集所有 stationcode
        List<Long> stationCodes = stationbybillResults.stream().map(StationbybillResult::getId).collect(Collectors.toList());

        // 2. 批量查询电表信息
        List<PowerAmmeterorprotocol> allAmmeterList = new PowerAmmeterorprotocol().selectList(
                Wrappers.<PowerAmmeterorprotocol>lambdaQuery()
                        .select(
                                PowerAmmeterorprotocol::getCategory,
                                PowerAmmeterorprotocol::getAmmetername,
                                PowerAmmeterorprotocol::getProtocolname,
                                PowerAmmeterorprotocol::getProjectname,
                                PowerAmmeterorprotocol::getStationcode
                        )
                        .in(PowerAmmeterorprotocol::getStationcode, stationCodes)
        );

        // 3. 按 stationcode 分组
        Map<Long, List<PowerAmmeterorprotocol>> stationAmmeterMap = allAmmeterList.stream()
                .collect(Collectors.groupingBy(PowerAmmeterorprotocol::getStationcode));

        // 4. 处理每个 station 的结果
        for (StationbybillResult stationbybillResult : stationbybillResults) {
            List<PowerAmmeterorprotocol> ammeterorprotocolList = stationAmmeterMap.getOrDefault(
                    stationbybillResult.getId(),
                    Collections.emptyList()
            );

            // 构建电表名称字符串
            StringBuilder nameBuilder = new StringBuilder();
            for (PowerAmmeterorprotocol ammeterorprotocol : ammeterorprotocolList) {
                if ("1".equals(ammeterorprotocol.getCategory())) {
                    nameBuilder.append(ammeterorprotocol.getAmmetername()).append(",");
                } else {
                    nameBuilder.append(ammeterorprotocol.getProtocolname()).append(",");
                }
            }
            if (nameBuilder.length() > 0) {
                nameBuilder.setLength(nameBuilder.length() - 1);
            }
            stationbybillResult.setAmmetername(nameBuilder.toString().trim());

            // 设置项目名称
            stationbybillResult.setProjectname(
                    ammeterorprotocolList.stream().map(PowerAmmeterorprotocol::getProjectname).collect(Collectors.joining(","))
            );
        }
    }


    @Override
    public List<StationbybillResult> stationlistBycompany(Ammeterorprotocol ammeterorprotocol) {
        if (ammeterorprotocol.getCompany() == null) ammeterorprotocol.setCompany(0L);
        if (deployTo.equals("ln"))
            return rBillitemAccountMapper.stationlistBylncompany(ammeterorprotocol);
        else
            return rBillitemAccountMapper.stationlistBycompany(ammeterorprotocol);
    }


    @Override
    public List<AccountEsResult> accountEslistBybillId(RBillitemAccount rBillitemAccount) {
        rBillitemAccount.setSearchValue(deployTo);
        return rBillitemAccountMapper.accountEslistBybillId(rBillitemAccount);
    }

    @Override
    public List<AccountEsResult> accountEslistBybillIds(String[] ids) {
        return rBillitemAccountMapper.accountEslistBybillIds(ids);
    }

    @Override
    public AccountAmount getAccountAmount(AccountAmount accountAmount) {
        return rBillitemAccountMapper.getAccountAmount(accountAmount);
    }

    @Override
    public int saveAccountAmount(AccountAmount accountAmount) {
        List<AccountAmount> list = rBillitemAccountMapper.ifhaveNoAccountAmountByPcId(accountAmount.getPcid());
        if (list != null && list.size() > 0) {
            AccountAmount amount = list.get(0);
            BigDecimal quantity = BigDecimal.valueOf(amount.getThisQuantityOfElectricity());
            BigDecimal accountamount = BigDecimal.valueOf(accountAmount.getAmount());
            if (accountamount.compareTo(BigDecimal.valueOf(0)) > 0 && accountamount.compareTo(quantity) >= 0) {
                throw new BaseException("设备耗电量" + accountAmount.getAmount() + "超过用电量" + quantity + "【注意关联用电类型比例！！】");
            }
            if (accountamount.compareTo(BigDecimal.valueOf(0)) < 0 && accountamount.compareTo(quantity) <= 0) {
                throw new BaseException("设备耗电量" + accountAmount.getAmount() + "超过用电量" + quantity + "【注意关联用电类型比例！！】");
            }
        }
        if (accountAmount != null && accountAmount.getId() != null) {
            return rBillitemAccountMapper.updateAccountAmount(accountAmount);
        } else {
            accountAmount.setId(IdGenerator.getNextId());
            return rBillitemAccountMapper.insertAccountAmount(accountAmount);
        }
    }

    @Override
    public int saveAccountAmounts(List<AccountAmount> list) {
        for (AccountAmount accountAmount : list) {
            saveAccountAmount(accountAmount);
        }
        return list.size();
    }

    @Override
    public List<AccountAmountStationInfo> getAccountAmountStationInfo(AccountAmountStationInfo accountAmount) {
        return rBillitemAccountMapper.getAccountAmountStationInfo(accountAmount.getBillid());
    }

    @Override
    public int countqutoaBybillId(Long id) {
        return rBillitemAccountMapper.countqutoaBybillId(id);
    }

    @Override
    public StationListTop stationlistTop(String company) {
        StringRedisTemplate redisTemplate = RedisUtil.getStringRedisTemplate();
        HashOperations<String, String, String> hashOperations = redisTemplate.opsForHash();

        StationListTop top = new StationListTop();

        if (StringUtils.isBlank(company)) {
            throw new RuntimeException(" -->当前查询地市不存在，请核实");
        }
        List<OrgName> orgNames = rBillitemAccountMapper.selectOrgName(company);
        Map<String, String> orgMap = orgNames.stream().filter(item -> item != null).collect(Collectors.toMap(
                item -> item.getCompany(),
                item -> item.getCompanyName(),
                (v1, v2) -> v1
        ));

        if ("-1".equals(company)) {
            Map<String, String> stationlistTopRelevance = hashOperations.entries("stationlistTopRelevance");
            Map<String, String> stationlistTopRelevanceNot = hashOperations.entries("stationlistTopRelevanceNot");
            stationlistTopRelevance.forEach(
                    (companyTemp, CountTemp) -> {
                        stationlistTopRelevance.put(
                                companyTemp,
                                orgMap.getOrDefault(companyTemp, "未知的地市") + ":" + CountTemp
                        );
                    }
            );
            stationlistTopRelevanceNot.forEach(
                    (companyTemp, CountTemp) -> {
                        stationlistTopRelevanceNot.put(
                                companyTemp,
                                orgMap.getOrDefault(companyTemp, "未知的地市") + ":" + CountTemp
                        );
                    }
            );
            top.setRelevanceMap(stationlistTopRelevance);
            top.setRelevanceNotMap(stationlistTopRelevanceNot);
        } else {
            Map<String, String> stationlistTopRelevance = new HashMap<>();
            Map<String, String> stationlistTopRelevanceNot = new HashMap<>();

            String relevanceCount = hashOperations.get("stationlistTopRelevance", company);
            String relevanceNotCount = hashOperations.get("stationlistTopRelevanceNot", company);

            stationlistTopRelevance.put(company, relevanceCount == null ? "0" : relevanceCount);
            stationlistTopRelevanceNot.put(company, relevanceNotCount == null ? "0" : relevanceNotCount);

            stationlistTopRelevance.forEach(
                    (companyTemp, CountTemp) -> {
                        stationlistTopRelevance.put(
                                companyTemp,
                                orgMap.getOrDefault(companyTemp, "未知的地市") + ":" + CountTemp
                        );
                    }
            );
            stationlistTopRelevanceNot.forEach(
                    (companyTemp, CountTemp) -> {
                        stationlistTopRelevanceNot.put(
                                companyTemp,
                                orgMap.getOrDefault(companyTemp, "未知的地市") + ":" + CountTemp
                        );
                    }
            );

            top.setRelevanceMap(stationlistTopRelevance);
            top.setRelevanceNotMap(stationlistTopRelevanceNot);
        }

        return top;
    }

    @Override
    public String stationlistTopGenerate(String company) {
        List<StationListTopRedis> relevanceRedis = rBillitemAccountMapper.selectRelevanceRedis(company);
        List<StationListTopRedis> relevanceNotRedis = rBillitemAccountMapper.selectRelevanceNotRedis(company);
        Map<String, String> relevanceMap = relevanceRedis.stream().collect(Collectors.toMap(
                item -> item.getCompany(),
                item -> item.getCount(),
                (value1, value2) -> value1
        ));
        Map<String, String> relevanceNotMap = relevanceNotRedis.stream().collect(Collectors.toMap(
                item -> item.getCompany(),
                item -> item.getCount(),
                (value1, value2) -> value1
        ));
        StringRedisTemplate redisTemplate = RedisUtil.getStringRedisTemplate();
        HashOperations<String, Object, Object> hashOperations = redisTemplate.opsForHash();
        hashOperations.putAll("stationlistTopRelevance", relevanceMap);
        hashOperations.putAll("stationlistTopRelevanceNot", relevanceNotMap);
        return "站址关联地市数量redis设置完毕";
    }

    @Override
    public List<StationbybillResult> stationlistTwo(String company, String country, boolean relevanceFlag) {
        if (relevanceFlag) {
            return rBillitemAccountMapper.stationlistTwoRelevance(company, country);
        } else {
            return rBillitemAccountMapper.stationlistTwoRelevanceNot(company, country);
        }
    }

    @Override
    public List<NhSite> getStationFor5G(NhSite nhSite) {
        User user = ShiroUtils.getUser();
        if (user == null) {
            throw new RuntimeException(" -->当前系统无登录用户信息");
        }
        String quercompany = "";
        List<IdNameVO> companies = new ArrayList<>();
        if (null != user.getCompany()) {
            IdNameVO company = new IdNameVO();
            company.setName(user.getCompanies().get(0).getName());
            companies.add(company);
        }
        String city = user.getCompanies().get(0).getId();
        nhSite.setCity(city);
        List<NhSite> nhSites = rBillitemAccountMapper.getStationFor5G(nhSite);

        return nhSites;
    }



    @Override
    public int saveAccountAmountStationInfo(List<AccountAmountStationInfo> list) {
        List<AccountAmountStationInfo> listnew = new ArrayList<AccountAmountStationInfo>();
        for (AccountAmountStationInfo item : list) {
            item.setState(1);
            item.setProv("26");// 辽宁省份编码
            if (item != null && item.getId() != null) {
                rBillitemAccountMapper.updateAccountAmountStationInfo(item);
            } else {
                item.setId(IdGenerator.getNextId());
                listnew.add(item);
            }
        }
        if (listnew.size() > 0) rBillitemAccountMapper.insertAccountAmountStationInfo(listnew);
        return list.size();
    }


    //生成  报账明细 和报账明细台账关联关系
    private List<MssAccountbillitem> setData1(List<MssAccountbillitem> ilist, List<Object> alllist, List<Object> sclist, List<RBillitemAccount> rblist, List<RBillitemAccount> ddrblist, List<AccountBaseResult> ddlist, String type, Accountbillitempre accountbillitempre) throws Exception {
        List<RBillitemAccount> rlist = new ArrayList<>();
        MssAccountbillitem mssitem = new MssAccountbillitem();
        List<BigDecimal> totalList = new ArrayList();
        List<BigDecimal> taxtotalList = new ArrayList();
        List<BigDecimal> totalusedreadingsList = new ArrayList();
        List<Integer> uasgeidList = new ArrayList();

        if (type.equals("pf")) {
            mssitem.setBudgetItemId("1000");
            mssitem.setOrderNo("************");//四川普服项目
        } else {
            mssitem.setBudgetItemId(type);
        }
        List<Object> moneyFirst = findMoneyFirst(alllist, rblist, type, accountbillitempre);
        Map<Long, List<Object>> listMap = moneyFirst.stream().collect(Collectors.groupingBy(obj -> {
            Long ammeterid = null;
            if (obj instanceof AccountBaseResult) {
                AccountBaseResult item = (AccountBaseResult) obj;
                ammeterid = item.getAmmeterid();
            }
            if (obj instanceof AccountEsResult) {
                AccountEsResult item = (AccountEsResult) obj;
                ammeterid = item.getAmmeterid();
            }
            return ammeterid;
        }));

        listMap.forEach((ammeterid, list) -> {
            RBillitemAccount rb = new RBillitemAccount();
            BigDecimal money = null;
            BigDecimal taxmoney = null;
            BigDecimal accountmoney = null;
            BigDecimal moneyall = new BigDecimal("0");
            BigDecimal taxmoneyall = new BigDecimal("0");
            BigDecimal total = new BigDecimal(0);
            BigDecimal taxtotal = new BigDecimal(0);
            BigDecimal totalusedreadings = new BigDecimal(0);// 计算 单价
            Integer uasgeid = 0;
            List<String> projectName = new ArrayList<>();
            List<String> ammeterCode = new ArrayList<>();
            for (Object o : list) {
                if (o instanceof AccountBaseResult) {
                    AccountBaseResult item = (AccountBaseResult) o;
                    money = item.getMoney();
                    taxmoney = item.getTaxmoney();
                    uasgeid = item.getElectrotype();
                    for (AccountBaseResult dd : ddlist) {
                        // TODO: 2023/12/13 AccountBaseResult的money明细未减去
                    }
                    totalusedreadings = totalusedreadings.add(item.getTotalusedreadings().multiply(money.add(taxmoney).divide(item.getAccountmoney(), 6, BigDecimal.ROUND_HALF_UP)));
                    moneyall = moneyall.add(item.getMoney());
                    taxmoneyall = taxmoneyall.add(item.getTaxmoney());
                    projectName.add(item.getProjectname());
                    ammeterCode.add(item.getAmmetercode());
                }
                if (o instanceof AccountEsResult) {
                    AccountEsResult item = (AccountEsResult) o;
                    rb.setAccountId(item.getPcid());
                    uasgeid = item.getElectrotype();
                    money = item.getMoney();
                    taxmoney = item.getTaxmoney();
                    totalusedreadings = totalusedreadings.add(item.getCurusedreadings().multiply(money.add(taxmoney).divide(item.getAccountmoney(), 6, BigDecimal.ROUND_HALF_UP)));
                }

                rb.setAccountMoney(accountmoney.doubleValue());//不含税价
                rb.setMoney(money.doubleValue());
                rb.setTaxmoney(taxmoney.doubleValue());

                // 计算 关联的 电量 关联的金额 除单价 就是关联的电量
                if (rb.getAccountMoney().equals(rb.getMoney())) {
                    rb.setIfall(1L);
                } else {
                    rb.setIfall(0L);
                }
                // if (rb.getMoney() > 0)//有金额才关联  负数处理
                rlist.add(rb);
                total = total.add(money);
                taxtotal = taxtotal.add(taxmoney);
                totalList.add(total);
                taxtotalList.add(taxtotal);
                totalusedreadingsList.add(totalusedreadings);
                uasgeidList.add(uasgeid);
            }
            String projectNameStr = projectName.stream().filter(name -> {
                return StringUtils.isNotBlank(name);
            }).collect(Collectors.joining("、"));
            String ammeterCodeStr = ammeterCode.stream().filter(code -> {
                return StringUtils.isNotBlank(code);
            }).collect(Collectors.joining("、"));

            if (moneyall.compareTo(new BigDecimal("0")) > 0) {
                for (AccountBaseResult dd : ddlist) {
                    if (dd.getAmmparentid() != null && dd.getAmmparentid().equals(ammeterid)) {
                        if (accountbillitempre.getPlaceid() == null) {
                            moneyall = moneyall.subtract(dd.getAccountmoney().abs().subtract(dd.getTaxamount().abs()));
                            taxmoneyall = taxmoneyall.subtract(dd.getTaxamount().abs());
                        } else {
                            //已关联金额中包含了普票金额，未分开记录专票、普票各自关联金额，无法分别计算。
                            moneyall = moneyall.subtract(dd.getAccountmoney().abs().subtract(dd.getTaxamount().abs()).subtract(handerMoney1(ddrblist, dd.getPcid())));
                            taxmoneyall = taxmoneyall.subtract(dd.getTaxamount().abs().subtract(handerTaxMoney1(ddrblist, dd.getPcid())));
                        }
                        // 避免计算造成的精度丢失，保留两位小数
                        if (moneyall.compareTo(new BigDecimal("0")) < 0) {
                            throw new BaseException(projectNameStr + "电表【" + ammeterCodeStr + "】代垫后 报账不含税金额小于0！！！");
                        }
                        if (taxmoneyall.compareTo(new BigDecimal("-0.1")) < 0) { // 优化为不超过10分
                            throw new BaseException(projectNameStr + "电表【" + ammeterCodeStr + "】代垫后 报账税额小于0！！！");
                        }
                    }
                }
            }
        });
        BigDecimal total = totalList.get(0);
        BigDecimal taxtotal = taxtotalList.get(0);
        if (total.doubleValue() != 0)  //可以增加负数
        {
            mssitem.setSum(total);
            mssitem.setTaxAdjustSum(taxtotal);
            mssitem.setRbillitemaccount(rlist);
            //可以增加负数
            BigDecimal price = new BigDecimal("0");

            BigDecimal totalusedreadings = totalusedreadingsList.get(0);
            if ("sc".equals(deployTo))
                price = total.add(taxtotal).divide(totalusedreadings, 2, BigDecimal.ROUND_HALF_UP);
            else

                price = total.divide(totalusedreadings, 2, BigDecimal.ROUND_HALF_UP);

            //  关联的电量 除以 关联含税金额
            mssitem.setPrice(price);
            totalusedreadings = totalusedreadings.setScale(2, BigDecimal.ROUND_HALF_UP);
            mssitem.setAmount(totalusedreadings);
            mssitem.setBeAfterOperateSum(BigDecimal.ZERO);
            mssitem.setUsageId(Integer.toString(uasgeidList.get(0) != null ? uasgeidList.get(0) : 0));//23年用途
            ilist.add(mssitem);
        }
        return ilist;
    }

    private List<Object> findMoneyFirst(List<Object> alllist, List<RBillitemAccount> rblist, String type, Accountbillitempre accountbillitempre) {
        List<Object> rlist = new ArrayList<>();
        Map<String, Object> rb = null;
        Integer uasgeid = 0;
        for (Object o : alllist) {
            rb = new HashMap<>();
            BigDecimal accountMoney = new BigDecimal("0");
            BigDecimal money = new BigDecimal("0");
            BigDecimal taxmoney = new BigDecimal("0");
            BigDecimal unitpirce = new BigDecimal(0);
            if (o instanceof AccountBaseResult) {
                AccountBaseResult item = (AccountBaseResult) o;
                //rb.put("accountId",item.getPcid());
                uasgeid = item.getElectrotype();
                if (accountbillitempre.getPlaceid() == null) {
                    // 修改 计算规则 为 总金额 减去税额 适应预估台账规则
                    accountMoney = item.getAccountmoney().subtract(item.getTaxamount());
                    taxmoney = item.getTaxamount();
                } else if (accountbillitempre.getPlaceid() == 1) {
                    accountMoney = item.getTaxticketmoney().subtract(item.getTaxamount());
                    taxmoney = item.getTaxamount();
                } else if (accountbillitempre.getPlaceid() == 2) {// 普票加其他
                    accountMoney = item.getTicketmoney().add(item.getUllagemoney());
                } else {
                    // 修改 计算规则 为 总金额 减去税额 适应预估台账规则
                    accountMoney = item.getAccountmoney().subtract(item.getTaxamount());
                    taxmoney = item.getTaxamount();
                }
                if (accountbillitempre.getPlaceid() == null) {
                    money = accountMoney.subtract(handerMoney1(rblist, item.getPcid()));
                    taxmoney = taxmoney.subtract(handerTaxMoney1(rblist, item.getPcid()));
                } else {
                    money = accountMoney;//已关联金额中包含了普票金额，未分开记录专票、普票各自关联金额，无法分别计算。
                }
                item.setAccountmoney(accountMoney);
                item.setMoney(money);
                item.setTaxmoney(taxmoney);
                rlist.add(item);
            }
            if (o instanceof AccountEsResult) {
                AccountEsResult item = (AccountEsResult) o;
                rb.put("accountId", item.getPcid());
                uasgeid = item.getElectrotype();
                if (item.getTaxamount() != null) {
                    accountMoney = item.getAccountmoney().subtract(item.getTaxamount());
                    taxmoney = item.getTaxamount();
                    if (accountbillitempre.getPlaceid() == null) {
                        money = accountMoney.subtract(handerMoney1(rblist, item.getPcid()));
                        taxmoney = taxmoney.subtract(handerTaxMoney1(rblist, item.getPcid()));
                    } else {
                        money = accountMoney;//已关联金额中包含了普票金额，未分开记录专票、普票各自关联金额，无法分别计算。
                    }
                } else {
                    accountMoney = item.getAccountmoney();
                    money = accountMoney.subtract(handerMoney1(rblist, item.getPcid()));
                }
                item.setAccountmoney(accountMoney);
                item.setMoney(money);
                item.setTaxmoney(taxmoney);
                rlist.add(item);
            }
        }
        return rlist;
    }

    //生成  报账明细 和报账明细台账关联关系
    private void setDataTwo(List<MssAccountbillitem> ilist, List<Object> sclist, List<RBillitemAccount> rblist, List<RBillitemAccount> ddrblist, List<AccountBaseResult> ddlist, String type, Accountbillitempre accountbillitempre) throws Exception {
        List<RBillitemAccount> rlist = new ArrayList<>();
        MssAccountbillitem mssitem = new MssAccountbillitem();
        if (type.equals("pf")) {
            mssitem.setBudgetItemId("1000");
            mssitem.setOrderNo("************");//四川普服项目
        } else {
            mssitem.setBudgetItemId(type);
        }
        BigDecimal total = new BigDecimal(0);
        BigDecimal taxtotal = new BigDecimal(0);
        BigDecimal totalusedreadings = new BigDecimal(0);// 计算 单价
        Integer uasgeid = 0;
        RBillitemAccount rb = null;
        BigDecimal accountMoney = null;
        BigDecimal money = null;
        BigDecimal taxmoney = null;
        BigDecimal unitpirce = null;
        BigDecimal percent = null;
        for (Object o : sclist) {
            rb = new RBillitemAccount();
            accountMoney = new BigDecimal("0");
            money = new BigDecimal("0");
            taxmoney = new BigDecimal("0");
            unitpirce = new BigDecimal(0);
            if (o instanceof AccountBaseResult) {
                AccountBaseResult item = (AccountBaseResult) o;
                percent = item.getPercent();
                rb.setAccountId(item.getPcid());
                uasgeid = item.getElectrotype();
                if (accountbillitempre.getPlaceid() == null) {
                    // 修改 计算规则 为 总金额 减去税额 适应预估台账规则
                    accountMoney = item.getAccountmoney().subtract(item.getTaxamount());
                    taxmoney = item.getTaxamount();
                } else if (accountbillitempre.getPlaceid() == 1) {
                    accountMoney = item.getTaxticketmoney().subtract(item.getTaxamount());
                    taxmoney = item.getTaxamount();
                } else if (accountbillitempre.getPlaceid() == 2) {// 普票加其他
                    accountMoney = item.getTicketmoney().add(item.getUllagemoney());
                } else {
                    // 修改 计算规则 为 总金额 减去税额 适应预估台账规则
                    accountMoney = item.getAccountmoney().subtract(item.getTaxamount());
                    taxmoney = item.getTaxamount();
                }
                unitpirce = item.getUnitpirce();// 单价 关联的金额 除单价 就是关联的电量
                if (accountbillitempre.getPlaceid() == null) {
                    if (percent.intValue() == 100) {
                        money = accountMoney.subtract(handerMoney1(rblist, item.getPcid()));
                        taxmoney = taxmoney.subtract(handerTaxMoney1(rblist, item.getPcid()));
                    } else {
                        money = accountMoney.subtract(handerMoney1(rblist, item.getPcid()).multiply(percent));
                        taxmoney = taxmoney.subtract(handerTaxMoney1(rblist, item.getPcid()).multiply(percent));
                    }
                } else {
                    money = accountMoney;//已关联金额中包含了普票金额，未分开记录专票、普票各自关联金额，无法分别计算。
                }

                // 判断是否包含代垫的台账
                for (AccountBaseResult dd : ddlist) {
                    if (dd.getAmmparentid() != null && dd.getAmmparentid().equals(item.getAmmeterid())) {
                        if (accountbillitempre.getPlaceid() == null) {
                            if (percent.intValue() == 100) {
                                //if (percent.equals(new BigDecimal("100"))){
                                money = money.subtract(dd.getAccountmoney().abs().subtract(dd.getTaxamount().abs()));
                                taxmoney = taxmoney.subtract(dd.getTaxamount().abs());
                            } else {
                                money = money.subtract(dd.getAccountmoney().abs().multiply(percent).subtract(dd.getTaxamount().abs().multiply(percent)));//todo 四捨五入
                                taxmoney = taxmoney.subtract(dd.getTaxamount().abs().multiply(percent));
                            }
                        } else {
                            //已关联金额中包含了普票金额，未分开记录专票、普票各自关联金额，无法分别计算。
                            //System.out.println("dd.getAccountmoney().abs()="+dd.getAccountmoney().abs());
                            //System.out.println("dd.getTaxamount().abs()="+dd.getTaxamount().abs().abs());
                            if (percent.intValue() == 100) {
                                money = money.subtract(dd.getAccountmoney().abs().subtract(dd.getTaxamount().abs()).subtract(handerMoney1(ddrblist, dd.getPcid())));
                                taxmoney = taxmoney.subtract(dd.getTaxamount().abs().subtract(handerTaxMoney1(ddrblist, dd.getPcid())));
                            } else {
                                money = money.subtract(dd.getAccountmoney().abs().multiply(percent).subtract(dd.getTaxamount().abs().multiply(percent)).subtract(handerMoney1(ddrblist, dd.getPcid()).multiply(percent)));
                                taxmoney = taxmoney.subtract(dd.getTaxamount().abs().multiply(percent).subtract(handerTaxMoney1(ddrblist, dd.getPcid()).multiply(percent)));
                            }
                        }
                        // 避免计算造成的精度丢失，保留两位小数
                        if (money.compareTo(new BigDecimal("0")) < 0) {
                            throw new BaseException(item.getProjectname() + "电表【" + item.getAmmetercode() + "】代垫后 报账不含税金额小于0！！！");
                        }
                        if (taxmoney.compareTo(new BigDecimal("-0.1")) < 0) { // 优化为不超过10分
                            throw new BaseException(item.getProjectname() + "电表【" + item.getAmmetercode() + "】代垫后 报账税额小于0！！！");
                        }
                    }
                }
                // 修改 计算电量为根据 关联总金额的百分比
                totalusedreadings = totalusedreadings.add(item.getTotalusedreadings().multiply(money.add(taxmoney).divide(item.getAccountmoney(), 6, BigDecimal.ROUND_HALF_UP)));
            }
            if (o instanceof AccountEsResult) {
                AccountEsResult item = (AccountEsResult) o;
                rb.setAccountId(item.getPcid());
                uasgeid = item.getElectrotype();
                if (item.getTaxamount() != null) {
                    accountMoney = item.getAccountmoney().subtract(item.getTaxamount());
                    taxmoney = item.getTaxamount();
                    if (accountbillitempre.getPlaceid() == null) {
                        money = accountMoney.subtract(handerMoney1(rblist, item.getPcid()));
                        taxmoney = taxmoney.subtract(handerTaxMoney1(rblist, item.getPcid()));
                    } else {
                        money = accountMoney;//已关联金额中包含了普票金额，未分开记录专票、普票各自关联金额，无法分别计算。
                    }
                } else {
                    accountMoney = item.getAccountmoney();
                    money = accountMoney.subtract(handerMoney1(rblist, item.getPcid()));
                }
                unitpirce = item.getUnitpirce();// 单价
                // 修改 计算电量为根据 关联总金额的百分比
                totalusedreadings = totalusedreadings.add(item.getCurusedreadings().multiply(money.add(taxmoney).divide(item.getAccountmoney(), 6, BigDecimal.ROUND_HALF_UP)));

            }

            rb.setAccountMoney(accountMoney.doubleValue());//不含税价
            rb.setMoney(money.doubleValue());
            rb.setTaxmoney(taxmoney.doubleValue());

            total = total.add(money);
            taxtotal = taxtotal.add(taxmoney);
            // 计算 关联的 电量 关联的金额 除单价 就是关联的电量
            if (rb.getAccountMoney().equals(rb.getMoney())) {
                rb.setIfall(1L);
            } else {
                rb.setIfall(0L);
            }
            // if (rb.getMoney() > 0)//有金额才关联  负数处理
            rlist.add(rb);

        }
        if (total.doubleValue() != 0)  //可以增加负数
        {
            mssitem.setSum(total);
            mssitem.setTaxAdjustSum(taxtotal);
            mssitem.setRbillitemaccount(rlist);
            //可以增加负数
            BigDecimal price = new BigDecimal("0");

            if ("sc".equals(deployTo))
                price = total.add(taxtotal).divide(totalusedreadings, 2, BigDecimal.ROUND_HALF_UP);
            else
                price = total.divide(totalusedreadings, 2, BigDecimal.ROUND_HALF_UP);

            //  关联的电量 除以 关联含税金额
            mssitem.setPrice(price);
            totalusedreadings = totalusedreadings.setScale(2, BigDecimal.ROUND_HALF_UP);
            mssitem.setAmount(totalusedreadings);
            mssitem.setBeAfterOperateSum(BigDecimal.ZERO);
            mssitem.setUsageId(Integer.toString(uasgeid));//23年用途
            ilist.add(mssitem);
        }
    }

}
