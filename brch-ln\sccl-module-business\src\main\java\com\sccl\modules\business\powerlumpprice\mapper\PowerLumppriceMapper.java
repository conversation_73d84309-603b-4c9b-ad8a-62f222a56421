package com.sccl.modules.business.powerlumpprice.mapper;

import com.sccl.modules.business.powerlumpprice.domain.PowerLumpprice;
import com.sccl.framework.mapper.BaseMapper;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 铁塔包干单价维护 数据层
 * 
 * <AUTHOR>
 * @date 2019-06-19
 */
public interface PowerLumppriceMapper extends BaseMapper<PowerLumpprice>
{

    List<Map<String,Object>> selectByList(PowerLumpprice powerLumpprice);
    Map<String,Object> selectById(Map<String,Object> params);
    List<Map<String,Object>> checkDate(Map<String,Object> params);

    /**
     * @Description: 查询指定部门下某个时间段的单价
     * @author: dongk
     * @date: 2019/7/24
     * @param:
     * @return:
     */
    BigDecimal selectlumpprice(PowerLumpprice lumpprice);

}