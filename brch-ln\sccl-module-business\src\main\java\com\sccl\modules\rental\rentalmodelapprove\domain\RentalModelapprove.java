package com.sccl.modules.rental.rentalmodelapprove.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;
import java.math.BigDecimal;


/**
 * 审批记录表 rental_modelapprove
 * 
 * <AUTHOR>
 * @date 2019-08-23
 */
public class RentalModelapprove extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	private Long pabaid;

	public Long getPabaid() {
		return pabaid;
	}

	public void setPabaid(Long pabaid) {
		this.pabaid = pabaid;
	}

	/** 报销单id */
    private BigDecimal paid;
    /**  */
    private BigDecimal processinstid;
    /** 审批人id */
    private BigDecimal operatorid;
    /** 备注 */
    private String memo;
    /**  */
    private Date dapprovedate;
    /**  */
    private BigDecimal iyorn;
    /** 审批用户机构 */
    private BigDecimal orgid;


	public void setPaid(BigDecimal paid)
	{
		this.paid = paid;
	}

	public BigDecimal getPaid() 
	{
		return paid;
	}

	public void setProcessinstid(BigDecimal processinstid)
	{
		this.processinstid = processinstid;
	}

	public BigDecimal getProcessinstid() 
	{
		return processinstid;
	}

	public void setOperatorid(BigDecimal operatorid)
	{
		this.operatorid = operatorid;
	}

	public BigDecimal getOperatorid() 
	{
		return operatorid;
	}

	public void setMemo(String memo)
	{
		this.memo = memo;
	}

	public String getMemo() 
	{
		return memo;
	}

	public void setDapprovedate(Date dapprovedate)
	{
		this.dapprovedate = dapprovedate;
	}

	public Date getDapprovedate() 
	{
		return dapprovedate;
	}

	public void setIyorn(BigDecimal iyorn)
	{
		this.iyorn = iyorn;
	}

	public BigDecimal getIyorn() 
	{
		return iyorn;
	}

	public void setOrgid(BigDecimal orgid)
	{
		this.orgid = orgid;
	}

	public BigDecimal getOrgid() 
	{
		return orgid;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("pabaid", getPabaid())
            .append("paid", getPaid())
            .append("processinstid", getProcessinstid())
            .append("operatorid", getOperatorid())
            .append("memo", getMemo())
            .append("dapprovedate", getDapprovedate())
            .append("iyorn", getIyorn())
            .append("orgid", getOrgid())
            .toString();
    }
}
