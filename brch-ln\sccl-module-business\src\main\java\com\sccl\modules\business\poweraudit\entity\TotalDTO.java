package com.sccl.modules.business.poweraudit.entity;

import com.sccl.framework.aspectj.lang.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: 芮永恒
 * @CreateTime: 2024-03-15  15:12
 * @Description:  异常电表总数
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TotalDTO {

    /**
     * 地市
     */
    @Excel(name = "所属部门")
    private String city;


    /**
     * 运营分局
     */
    @Excel(name = "运营分局")
    private String operationsBranch;

    /**
     * 电表协议编号
     */
    @Excel(name = "电表户名/协议号码")
    private String ammeterid;


    /**
     * 局站编码
     */
    @Excel(name = "集团站址编码")
    private String stationcode;

    /**
     * 铁塔站址编码
     */
    @Excel(name = "铁塔站址编码")
    private String towerSiteCode;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private String status;

    /**
     * 异常项
     */
    @Excel(name = "异常项")
    private String abnormal;

    /**
     * 电表负责人
     */
    @Excel(name = "电表负责人")
    private String headPeople;

}
