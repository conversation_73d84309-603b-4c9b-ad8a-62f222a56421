package com.sccl.modules.mssaccount.rbillitemaccount.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.sccl.common.lang.StringUtils;
import com.sccl.common.support.Convert;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.utils.ExcelUtil;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.account.domain.*;
import com.sccl.modules.business.accountbillitempre.domain.Accountbillitempre;
import com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol;
import com.sccl.modules.mssaccount.mssaccountbillitem.domain.MssAccountbillitem;
import com.sccl.modules.mssaccount.mssinterface.service.IMssInterfaceService;
import com.sccl.modules.mssaccount.rbillitemaccount.domain.RBillitemAccount;
import com.sccl.modules.mssaccount.rbillitemaccount.service.IRBillitemAccountService;
import com.sccl.modules.mssaccount.rbillitemaccount.service.RBillitemAccountServiceImpl;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.xssf.usermodel.*;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * 报账明细 台账 关联 信息操作处理
 *
 * <AUTHOR>
 * @date 2019-06-01
 */
@RestController
@RequestMapping("/mssaccount/rBillitemAccount")
public class RBillitemAccountController extends BaseController {
    private String prefix = "mssaccount/rBillitemAccount";

    @Value("${sccl.deployTo}")
    private String deployTo;

    @Autowired
    private IRBillitemAccountService rBillitemAccountService;

    @Autowired
    private IMssInterfaceService mssInterfaceService;

    @RequiresPermissions("mssaccount:rBillitemAccount:view")
    @GetMapping()
    public String rBillitemAccount() {
        return prefix + "/rBillitemAccount";
    }

    /**
     * 查询报账明细 台账 关联列表
     */
    @RequiresPermissions("mssaccount:rBillitemAccount:list")
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(RBillitemAccount rBillitemAccount) {
        startPage();
        List<RBillitemAccount> list = rBillitemAccountService.selectList(rBillitemAccount);
        return getDataTable(list);
    }

    /**
     * 新增报账明细 台账 关联
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存报账明细 台账 关联
     */
    @RequiresPermissions("mssaccount:rBillitemAccount:add")
    //@Log(title = "报账明细 台账 关联", action = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody RBillitemAccount rBillitemAccount) {
        return toAjax(rBillitemAccountService.insert(rBillitemAccount));
    }

    /**
     * 修改报账明细 台账 关联
     */
    @GetMapping("/edit/{id}")
    public AjaxResult edit(@PathVariable("id") Long id) {
        RBillitemAccount rBillitemAccount = rBillitemAccountService.get(id);

        Object object = JSONObject.toJSON(rBillitemAccount);

        return this.success(object);
    }

    /**
     * 修改保存报账明细 台账 关联
     */
    @RequiresPermissions("mssaccount:rBillitemAccount:edit")
    //@Log(title = "报账明细 台账 关联", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody RBillitemAccount rBillitemAccount) {
        return toAjax(rBillitemAccountService.update(rBillitemAccount));
    }

    /**
     * 删除报账明细 台账 关联
     */
    @RequiresPermissions("mssaccount:rBillitemAccount:remove")
    //@Log(title = "报账明细 台账 关联", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(rBillitemAccountService.deleteByIds(Convert.toStrArray(ids)));
    }


    /**
     * 查看报账明细 台账 关联
     */
    @RequiresPermissions("mssaccount:rBillitemAccount:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id) {
        RBillitemAccount rBillitemAccount = rBillitemAccountService.get(id);

        Object object = JSONObject.toJSON(rBillitemAccount);

        return this.success(object);
    }

    /**
     * 查询报账明细 台账 关联列表
     */
//    @RequiresPermissions("mssaccount:rBillitemAccount:list")
    @RequestMapping("/listNoPage")
    @ResponseBody
    public AjaxResult listNoPage(String ids) {
        Map<String, Double> map = rBillitemAccountService.listNoPage(Convert.toStrArray(ids));
        return this.success(map);
    }

    /**
     * 选择 关联归集单 后 生成 报账明细（台账关联关系） --- 电费报账单
     */
//    @RequiresPermissions("mssaccount:rBillitemAccount:list")
    @RequestMapping("/addItemByaccounts")
    @ResponseBody
    public AjaxResult addItemByaccounts(Accountbillitempre accountbillitempre) {
        AjaxResult res = new AjaxResult();
        List<MssAccountbillitem> mssAccountbillitems = null;
        try {
            mssAccountbillitems = rBillitemAccountService.addItemByaccounts(accountbillitempre);
            if (mssAccountbillitems != null && mssAccountbillitems.size() > 0) {

                res.put("list", mssAccountbillitems);
                res.put("success", true);
            } else {
                res.put("success", false);
                res.put("msg", "没有金额可生成报账明细");
            }
        } catch (Exception e) {
            e.printStackTrace();
            res.put("success", false);
            res.put("msg", "根据归集单，生成报账明细异常！" + e.getMessage());
        }
        return res;
    }


    /**
     * 选择 关联归集单 后 生成 报账明细（台账关联关系） --- 热力、煤炭、油费报账单
     */
    @RequestMapping("/addNewItemAccounts")
    @ResponseBody
    public AjaxResult addNewItemAccounts(Accountbillitempre accountbillitempre) {
        AjaxResult res = new AjaxResult();
        List<MssAccountbillitem> mssAccountbillitems = null;
        try {
            mssAccountbillitems = rBillitemAccountService.addNewItemAccounts(accountbillitempre);
            if (CollectionUtil.isNotEmpty(mssAccountbillitems)) {

                res.put("list", mssAccountbillitems);
                res.put("success", true);
            } else {
                res.put("success", false);
                res.put("msg", "没有金额可生成报账明细");
            }
        } catch (Exception e) {
            e.printStackTrace();
            res.put("success", false);
            res.put("msg", "根据归集单，生成报账明细异常！" + e.getMessage());
        }
        return res;
    }

    @RequestMapping("/addItemByaccountsgz")
    @ResponseBody
    public AjaxResult addItemByaccountsgz(Accountbillitempre accountbillitempre) {
        AjaxResult res = new AjaxResult();
        List<MssAccountbillitem> mssAccountbillitems = null;
        try {
            mssAccountbillitems = rBillitemAccountService.addItemByaccountsgz(accountbillitempre);
            if (mssAccountbillitems != null && mssAccountbillitems.size() > 0) {

                res.put("list", mssAccountbillitems);
                res.put("success", true);
            } else {
                res.put("success", false);
                res.put("msg", "没有金额可生成报账明细");
            }
        } catch (Exception e) {
            e.printStackTrace();
            res.put("success", false);
            res.put("msg", "根据归集单，生成报账明细异常！" + e.getMessage());
        }
        return res;
    }

    @RequestMapping("/updateHsFlag")
    @ResponseBody
    public String updateHsFlag(boolean flag) {
        boolean hsFlag = RBillitemAccountServiceImpl.hsFlag;
        System.out.println("回收电费标识为" + hsFlag + " 修改为" + flag);
        RBillitemAccountServiceImpl.hsFlag=flag;
        System.out.println("修改成功");
        return "回收电费标识为" + hsFlag + " 修改为" + flag;
    }

    @RequestMapping("/countUseMoneyedit")
    @ResponseBody
    public AjaxResult countUseMoneyedit(@RequestBody Map<String, Object> reqMap) {
        Map<String, Double> map = rBillitemAccountService.countUseMoneyedit(reqMap);
        return this.success(map);
    }

    // 根据 报账单ID 查询台账明细
//    @RequiresPermissions("mssaccount:rBillitemAccount:list")
    @RequestMapping("/accountlistBybillId")
    @ResponseBody
    public TableDataInfo accountlistBybillId(RBillitemAccount rBillitemAccount) {
        String type = rBillitemAccountService.getBillType(rBillitemAccount);// 查询报账单关联台账为 自有 还是预估台账；
        startPage();
//        1自有 2自有预估 3铁塔 4自有合并,5预估合并 6铁塔合并 7 铁塔预估 8铁塔预估合并 9铁塔包干、10包干合并
        if ("1".equals(type) || "3".equals(type) || "4".equals(type) || "6".equals(type) || "9".equals(type) || "10".equals(type)) {// 非预估
            return getDataTable(rBillitemAccountService.accountlistBybillId(rBillitemAccount));
        } else if ("2".equals(type) || "5".equals(type) || "7".equals(type) || "8".equals(type)) {// 预估
            return getDataTable(rBillitemAccountService.accountEslistBybillId(rBillitemAccount));
        } else if ("11".equals(type) || "12".equals(type) || "13".equals(type) || "14".equals(type) || "15".equals(type) || "16".equals(type) || "17".equals(type) || "18".equals(type)) {
            // 新增的 11 自有挂账 12自有挂账合并 13自有预付 14自有预付合并 15铁塔挂账 16铁塔挂账合并
            // 都是 查询 预估台账表 es
            return getDataTable(rBillitemAccountService.accountEslistBybillId(rBillitemAccount));
        } else {
            return getDataTable(rBillitemAccountService.accountlistBybillId(rBillitemAccount));
        }
    }

    @RequestMapping("/stationlistBybillId")
    @ResponseBody
    public TableDataInfo stationlistBybillId(RBillitemAccount rBillitemAccount) {
        //  String type = rBillitemAccountService.getBillType(rBillitemAccount);// 查询报账单关联台账为 自有 还是预估台账；
        startPage();
        return getDataTable(rBillitemAccountService.stationlistBybillId(rBillitemAccount));

    }

    @RequestMapping("/stationlistBycompany")
    @ResponseBody
    public TableDataInfo stationlistBycompany(Ammeterorprotocol ammeterorprotocol) {

        startPage();
        List<StationbybillResult> list = rBillitemAccountService.stationlistBycompany(ammeterorprotocol);
        return getDataTable(list);
    }


    @RequestMapping("/stationlistTop")
    @ResponseBody
    public StationListTop stationlistTop(String company) {

        startPage();
        StationListTop top = rBillitemAccountService.stationlistTop(company);
        return top;
    }


    @RequestMapping("/stationlistTwo")
    @ResponseBody
    public TableDataInfo stationlistTwo(
            @RequestParam("company") String company,
            @RequestParam("country") String country,
            @RequestParam("relevanceFlag") Boolean relevanceFlag
    ) {
        if ("sc".equals(deployTo)) {
            throw new RuntimeException(" -->当前环境为sc，不启用");
        }
        if (StringUtils.isBlank(company) || StringUtils.isBlank(country) || relevanceFlag == null) {
            throw new RuntimeException(" -->错误的查询标识，必须指定地市与关联标识");
        }
        startPage();
        List<StationbybillResult> list = rBillitemAccountService.stationlistTwo(company, country, relevanceFlag);
        return getDataTable(list);
    }

    @RequestMapping("/getStationFor5G")
    @ResponseBody
    public TableDataInfo getStationFor5G(NhSite nhSite) {
        if ("sc".equals(deployTo)) {
            throw new RuntimeException(" -->当前环境为sc，不启用");
        }
        startPage();
        List<NhSite> list = rBillitemAccountService.getStationFor5G(nhSite);
        return getDataTable(list);
    }

    @RequestMapping("/stationlistTopGenerate")
    @ResponseBody
    public AjaxResult stationlistTopGenerate(@RequestParam(required = false, value = "company") String company) {

        String result = rBillitemAccountService.stationlistTopGenerate(company);
        return AjaxResult.success("站址关联数量统计完毕，并放入redis中,每天早上4.00、中午13.00重新统计");
    }

    @RequestMapping("/stationJtsync")
    @ResponseBody
    public AjaxResult stationJtsync(Long company) {
        String a = "";
        try {
            Ammeterorprotocol ammeterorprotocol = new Ammeterorprotocol();
            ammeterorprotocol.setCompany(company);
            a = mssInterfaceService.syncEnergyMeterInfoscompany(ammeterorprotocol);
        } catch (Exception e) {
            System.out.println("call e.getMessage:" + e.getMessage());
            return this.success(e.getMessage());
        }
        return this.success(a);
    }

    @RequestMapping("/getAccountAmount")
    @ResponseBody
    public AjaxResult getAccountAmount(AccountAmount accountAmount) {
        AccountAmount a = rBillitemAccountService.getAccountAmount(accountAmount);
        return this.success(a);
    }

    @RequestMapping("/saveAccountAmount")
    @ResponseBody
    public AjaxResult saveAccountAmount(AccountAmount accountAmount) {
        int a = 0;
        try {
            a = rBillitemAccountService.saveAccountAmount(accountAmount);
        } catch (Exception e) {
            return this.success(e.getMessage());
        }
        return this.success(a);
    }

    @RequestMapping("/saveAccountAmounts")
    @ResponseBody
    public AjaxResult saveAccountAmounts(@RequestBody List<AccountAmount> list) {
        int a = rBillitemAccountService.saveAccountAmounts(list);
        return this.success(a);
    }

    @RequestMapping("/getAccountAmountStationInfo")
    @ResponseBody
    public TableDataInfo getAccountAmountStationInfo(AccountAmountStationInfo accountAmount) {
        startPage();
        List<AccountAmountStationInfo> list = rBillitemAccountService.getAccountAmountStationInfo(accountAmount);
        return getDataTable(list);
    }

    @RequestMapping("/saveAccountAmountStationInfo")
    @ResponseBody
    public AjaxResult saveAccountAmountStationInfo(@RequestBody List<AccountAmountStationInfo> list) {
        AjaxResult rs = new AjaxResult();
        try {
            int a = rBillitemAccountService.saveAccountAmountStationInfo(list);
            rs.put("success", true);
        } catch (Exception e) {
            rs.put("success", false);
            e.printStackTrace();
        }
        return rs;
    }

    @RequestMapping("/accountAmountStationInfoExport")
    @ResponseBody
    public void accountAmountStationInfoExport(HttpServletResponse response, AccountAmountStationInfo accountAmount) {
        try {
            List<AccountAmountStationInfo> list = rBillitemAccountService.getAccountAmountStationInfo(accountAmount);
            XSSFWorkbook wb = new XSSFWorkbook();
            // 生成一个表格
            XSSFSheet sheet = wb.createSheet("sheet1");
            // 生成表格的第一行
            // 第一行表头
            String[] header = new String[]{"唯一编码(不可修改)", "报账单ID", "期号", "局站ID", "局站编码", "局站名称", "本地网（分公司）编码", "区县编码", "总电量（本期）", "局站总设备耗电量（本期）[修改项]"};
            XSSFRow row = sheet.createRow(0);
            for (int i = 0; i < header.length; i++) {
                XSSFCell cell = row.createCell(i);
                cell.setCellValue(header[i]);
                XSSFCellStyle styled = wb.createCellStyle();
                XSSFFont font = wb.createFont();
                font.setFontName("微软雅黑");
                font.setFontHeightInPoints((short) 12);
                font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
                styled.setFont(font);
                cell.setCellStyle(styled);
                sheet.autoSizeColumn(i, true);
            }
            XSSFCellStyle style = wb.createCellStyle();
            // 设置样式
            style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
            style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
            style.setBorderRight(HSSFCellStyle.BORDER_THIN);
            style.setBorderTop(HSSFCellStyle.BORDER_THIN);
            style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
            style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
            if (list != null && list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    AccountAmountStationInfo info = list.get(i);
                    row = sheet.createRow(i + 1);
                    XSSFCell cell = row.createCell(0);
                    cell.setCellValue(info.getId() == null ? null : info.getId().toString());
                    cell = row.createCell(1);
                    cell.setCellValue(accountAmount.getBillid().toString());
                    cell = row.createCell(2);
                    cell.setCellValue(info.getAccountno());
                    cell = row.createCell(3);
                    cell.setCellValue(info.getStationid().toString());
                    cell = row.createCell(4);
                    cell.setCellValue(info.getStationcode());
                    cell = row.createCell(5);
                    cell.setCellValue(info.getStationname());
                    cell = row.createCell(6);
                    cell.setCellValue(info.getCompany());
                    cell = row.createCell(7);
                    cell.setCellValue(info.getCountry());
                    cell = row.createCell(8);
                    cell.setCellValue(info.getTotal().doubleValue());
                    cell = row.createCell(9);
                    cell.setCellValue(info.getAmount() == null ? 0 : info.getAmount().doubleValue());
                    cell.setCellStyle(style);
                }
            }
            String filename = "台账设备耗电量.xlsx";
            ExcelUtil.export(response, wb, filename);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @RequestMapping(value = "/accountAmountStationInfoImport")
    @ResponseBody
    public Map<String, Object> accountAmountStationInfoImport(HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        response.setContentType("text/html;charset=utf-8");
        Map<String, Object> map = new HashMap<>();
        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        Long billId = ServletRequestUtils.getLongParameter(request, "billId", 0);
        Iterator<String> iterator = multiRequest.getFileNames();
        while (iterator.hasNext()) {
            String name = iterator.next();
            List<MultipartFile> files = new LinkedList<>();
            files = multiRequest.getFiles(name);
            // 一次遍历所有文件
            for (MultipartFile file : files) {
                if (file == null) {
                    continue;
                }
                //如果文件大小为0则不上传
                long fileSize = file.getSize();
                if (fileSize <= 0L) {
                    throw new Exception("文件名称：【" + file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("\\") + 1, file.getOriginalFilename().length()) + "】的文件大小为0,请核对!");
                }
                Workbook workbook = WorkbookFactory.create(file.getInputStream());
                Sheet sheet = workbook.getSheet("sheet1");
                if (sheet == null) {
                    sheet = workbook.getSheetAt(0);
                }
                int rows = sheet.getPhysicalNumberOfRows();
                if (rows > 1) {
                    List<AccountAmountStationInfo> list = new ArrayList<>();
                    StringBuilder errors = new StringBuilder();
                    for (int i = 1; i < rows; i++) {
                        AccountAmountStationInfo info = new AccountAmountStationInfo();
                        // 从第2行开始取数据,默认第一行是表头.
                        Row row = sheet.getRow(i);
                        if (row.getCell(0) != null && StringUtils.isNotEmpty(row.getCell(0).getStringCellValue()))
                            info.setId(Long.valueOf(row.getCell(0).getStringCellValue()));
                        info.setBillid(Long.valueOf(row.getCell(1).getStringCellValue()));
                        info.setAccountno(row.getCell(2).getStringCellValue());
                        info.setStationid(Long.valueOf(row.getCell(3).getStringCellValue()));
                        info.setStationcode(row.getCell(4).getStringCellValue());
                        info.setStationname(row.getCell(5).getStringCellValue());
                        info.setCompany(row.getCell(6).getStringCellValue());
                        info.setCountry(row.getCell(7).getStringCellValue());
                        info.setTotal(Double.valueOf(row.getCell(8).getNumericCellValue()));
                        info.setAmount(Double.valueOf(row.getCell(9).getNumericCellValue()));
                        if (info.getTotal() <= info.getAmount()) {
                            errors.append("局站" + info.getStationcode() + "设备耗电量(" +
                                    info.getAmount() + ")不能超过总电量(" + info.getTotal() + "),不符合录入条件！");
                        } else if (!billId.equals(info.getBillid())) {
                            errors.append("局站" + info.getStationcode() + "报账单ID不一致(" +
                                    info.getBillid() + ")当前报账单ID(" + billId + "),不符合录入条件！");
                            break;
                        } else {
                            list.add(info);
                        }
                    }
                    int a = rBillitemAccountService.saveAccountAmountStationInfo(list);
                    map.put("errors", errors.toString());
                    map.put("number", a);
                }
            }
        }
        return map;
    }

    @RequestMapping("/countqutoaBybillId/{id}")
    @ResponseBody
    public int countqutoaBybillId(@PathVariable("id") Long id) {
        return rBillitemAccountService.countqutoaBybillId(id);
    }
}
