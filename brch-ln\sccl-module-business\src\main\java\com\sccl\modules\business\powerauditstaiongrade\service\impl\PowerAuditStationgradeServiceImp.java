package com.sccl.modules.business.powerauditstaiongrade.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sccl.framework.utils.ExcelUtil;
import com.sccl.framework.utils.FileUploadUtils;
import com.sccl.modules.autojob.util.convert.StringUtils;
import com.sccl.modules.business.msg.domain.Message;
import com.sccl.modules.business.powerauditstaiongrade.entity.*;
import com.sccl.modules.business.powerauditstaiongrade.mapper.PowerAuditStationgradeMapper;
import com.sccl.modules.business.powerauditstaiongrade.service.PowerAuditStationgradeService;
import com.sccl.modules.business.powerauditstaiongrade.util.ExcelExporter;
import com.sccl.modules.business.stationinfo.domain.PowerStationInfoRJtlte;
import com.sccl.modules.system.attachments.domain.Attachments;
import com.sccl.modules.system.attachments.mapper.AttachmentsMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Service
public class PowerAuditStationgradeServiceImp extends ServiceImpl<PowerAuditStationgradeMapper, PowerAuditStationgradeEntity> implements PowerAuditStationgradeService {
    @Autowired
    private PowerAuditStationgradeMapper auditStationgradeMapper;
    @Autowired(required = false)
    private AttachmentsMapper attachmentsMapper;

    @Override
    public List<PowerAuditStationgradeEntity> selectList(PowerAuditStationgradeEntity stationgradeEntity) {
        List<PowerAuditStationgradeEntity> list = auditStationgradeMapper.selectListByMybatis(stationgradeEntity);
        list.forEach(
                item -> {
                    item.setContent(
                            item.getContent().replace(
                                    "实际功耗或标准功耗为0，无法评级",
                                    "未采集到"
                            )
                    );
                }
        );
        return list;
    }

    @Override
    public List<TowerStationGradeaudit> selectListForTowerStaionGrade(TowerStationGradeaudit towerStationGrade) {
        return auditStationgradeMapper.selectListForTowerStaionGrade(towerStationGrade);
    }

    @Override
    public Attachments exportExcel(List<TowerStationGradeaudit> towerStationGradeList, Map<String, String> columnMap, Map<String, String> promptMap) {
        ExcelUtil<TowerStationGradeaudit> excelUtil = new ExcelUtil<>(TowerStationGradeaudit.class);
        Date currentDate = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy_MM_dd_HH_mm_ss");
        String formattedDate = dateFormat.format(currentDate);
        String fileName = "分路计量评级表" + formattedDate + ".xlsx";
        String outPath = "E:\\" + fileName;
        try {
            ExcelExporter.exportToExcel(towerStationGradeList, columnMap, outPath);
            Attachments attachments = new Attachments();
            String filedIdName = FileUploadUtils.encodingFilename(fileName, ".xls");
            attachments.setFileName(fileName);
            attachments.setBusiAlias("分路计量评级表)");
            attachments.setYear(Integer.valueOf(com.sccl.common.utils.DateUtils.getYear()));
            attachments.setDelFlag("0");
            if (attachmentsMapper.insert(attachments) == 1) {
                return attachments;
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;

    }

    @Override
    public Attachments exportExcelForStaionMapBill(List<StaionMapBill> rJtltes, Map<String, String> columnMap, Map<String, String> promptMap) {
        ExcelUtil<PowerStationInfoRJtlte> excelUtil = new ExcelUtil<>(PowerStationInfoRJtlte.class);
        Date currentDate = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy_MM_dd_HH_mm_ss");
        String formattedDate = dateFormat.format(currentDate);
        String fileName = "站址对应报账单表" + formattedDate + ".xlsx";
        String outPath = "E:\\" + fileName;
        try {
            ExcelExporter.exportToExcel(rJtltes, columnMap, outPath);
            Attachments attachments = new Attachments();
            String filedIdName = FileUploadUtils.encodingFilename(fileName, ".xls");
            attachments.setFileName(fileName);
            attachments.setBusiAlias("站址对应报账单表)");
            attachments.setYear(Integer.valueOf(com.sccl.common.utils.DateUtils.getYear()));
            attachments.setDelFlag("0");
            if (attachmentsMapper.insert(attachments) == 1) {
                return attachments;
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;

    }

    @Override
    public List<TowerResultSummary> selecTowerResultSummaryList(Message message) {
        return auditStationgradeMapper.selecTowerResultSummaryList(message);
    }

    @Override
    public List<TowerResultSummary> selecTowerResultSummary(Message message) {
        return auditStationgradeMapper.selecTowerResultSummary(message);
    }

    @Override
    public List<TowerAuditPro> selecTowerAuditList(Message message) {
        List<TowerAudit> auditList = auditStationgradeMapper.selectAuditList(message);
        List<TowerAudit> auditOneList = auditStationgradeMapper.selectAuditOneList(message);
        List<TowerAudit> mergeList = TowerAudit.mergeAuditList(auditList, auditOneList);
        mergeList.forEach(
                item -> {
                    Integer auditNum = item.getAuditNum() != null ? Integer.valueOf(item.getAuditNum()) : 0;
                    Integer auditNumOne = item.getAuditNumOne() != null ? Integer.valueOf(item.getAuditNumOne()) : 0;
                    Integer totalNum = item.getTotalNum() != null ? Integer.valueOf(item.getTotalNum()) : 0;
                    item.setAuditNum(auditNum + "");
                    item.setAuditNumOne(auditNumOne + "");
                    item.setTotalNum(totalNum + "");
                    item.setAuditScale(TowerAudit.calculatePercentage(auditNum, totalNum));
                    item.setAuditOneScale(TowerAudit.calculatePercentage(auditNumOne, totalNum));
                }
        );
        mergeList.forEach(
                item -> {
                    item.setAuditScale(item.getAuditScale() + "%");
                    item.setAuditOneScale(item.getAuditOneScale() + "%");
                }
        );
        List<TowerAuditPro> proList = TowerAuditPro.mergeAuditList(mergeList);
        return proList;
    }

    @Override
    public List<TowerAudit> selecTowerAuditListPro(Message message) {
        List<TowerAudit> auditList = auditStationgradeMapper.selectAuditList(message);
        List<TowerAudit> auditOneList = auditStationgradeMapper.selectAuditOneList(message);
        List<TowerAudit> mergeList = TowerAudit.mergeAuditList(auditList, auditOneList);
        mergeList.forEach(
                item -> {
                    Integer auditNum = item.getAuditNum() != null ? Integer.valueOf(item.getAuditNum()) : 0;
                    Integer auditNumOne = item.getAuditNumOne() != null ? Integer.valueOf(item.getAuditNumOne()) : 0;
                    Integer totalNum = item.getTotalNum() != null ? Integer.valueOf(item.getTotalNum()) : 0;
                    item.setAuditNum(auditNum + "");
                    item.setAuditNumOne(auditNumOne + "");
                    item.setTotalNum(totalNum + "");
                    item.setAuditScale(TowerAudit.calculatePercentage(auditNum, totalNum));
                    item.setAuditOneScale(TowerAudit.calculatePercentage(auditNumOne, totalNum));
                }
        );
        return mergeList;
    }

    @Override
    public List<StaionAnalysisPro> selectStaionAnalysisList(StaionAnalysis message) {
        List<StaionAnalysis> staionAnalysisList = auditStationgradeMapper.selectStaionAnalysisList(message);
        List<TowerAudit> auditList = getTowerAudits(message);
        StaionAnalysis.addEvaluationKey(staionAnalysisList);
        TowerAudit.addAuditNumOneKey(auditList);
        List<StaionAnalysisPro> proList = StaionAnalysisPro.mergeStaionAnalysisProList(staionAnalysisList, auditList);
        return proList;
    }

    @Override
    public List<ExceptionAnalysis> selectExceptionAnalysis(StaionAnalysis message) {
        List<ExceptionAnalysis> exceptionAnalyses = auditStationgradeMapper.selectExceptionAnalysis(message);
        exceptionAnalyses.forEach(
                item -> {
                    String exptionNum = item.getExptionNum();
                    String exptionReplyNum = item.getExptionReplyNum();
                    int temp = StringUtils.isBlank(exptionNum) || StringUtils.isBlank(exptionReplyNum) ?
                            0
                            :
                            (Integer.parseInt(exptionNum) - Integer.parseInt(exptionReplyNum));
                    item.setExptionReplyNotNum("" + temp);
                }
        );
        return exceptionAnalyses;

    }

    @Override
    public List<StaionAnalysisDetail> getStaionAnalysisDetailForAudit(StaionAnalysis keyOb) {
        List<StaionAnalysisDetail> list = auditStationgradeMapper.getStaionAnalysisDetailForAudit(keyOb);
        return list;
    }

    @Override
    public List<StaionAnalysisDetail> getStaionAnalysisDetailForGrade(StaionAnalysis keyOb) {
        List<StaionAnalysisDetail> list = auditStationgradeMapper.getStaionAnalysisDetailForGrade(keyOb);
        return list;
    }

    @Override
    public String generatPrice(String month) {
        Integer n1 = auditStationgradeMapper.truncatePrice(month);
        Integer n2 = auditStationgradeMapper.generatPrice(month);
        return String.format("时间%s:\n\t" +
                "物理删除了power_ammeter_price表%d条数据\n\t" +
                "重新生成了%d条数据", month, n1, n2);
    }


    private List<TowerAudit> getTowerAudits(StaionAnalysis message) {
        List<TowerAudit> auditList = auditStationgradeMapper.selectAuditListForStaionAnaly(message);
        List<TowerAudit> auditOneList = auditStationgradeMapper.selectAuditOneListForStaionAnaly(message);
        List<TowerAudit> mergeList = TowerAudit.mergeAuditList(auditList, auditOneList);
        mergeList.forEach(
                item -> {
                    Integer auditNum = item.getAuditNum() != null ? Integer.valueOf(item.getAuditNum()) : 0;
                    Integer auditNumOne = item.getAuditNumOne() != null ? Integer.valueOf(item.getAuditNumOne()) : 0;
                    Integer totalNum = item.getTotalNum() != null ? Integer.valueOf(item.getTotalNum()) : 0;
                    item.setAuditNum(auditNum + "");
                    item.setAuditNumOne(auditNumOne + "");
                    item.setTotalNum(totalNum + "");
                    item.setAuditScale(TowerAudit.calculatePercentage(auditNum, totalNum));
                    item.setAuditOneScale(TowerAudit.calculatePercentage(auditNumOne, totalNum));
                }
        );
        mergeList.forEach(
                item -> {
                    item.setAuditScale(item.getAuditScale() + "%");
                    item.setAuditOneScale(item.getAuditOneScale() + "%");
                }
        );
        return mergeList;
    }


    @Override
    public List<StaionMapBill> selectListForStationMapBill(PowerStationInfoRJtlte rJtlte) {
        return auditStationgradeMapper.selectListForStationMapBill(rJtlte);

    }


}
