package com.sccl.modules.business.stationinfovalidity.mapper;

import com.sccl.modules.business.stationinfovalidity.domain.StationInfoValidity;
import com.sccl.framework.mapper.BaseMapper;

import java.util.List;

/**
 * 电站址有效 数据层
 * 
 * <AUTHOR>
 * @date 2023-03-31
 */
public interface StationInfoValidityMapper extends BaseMapper<StationInfoValidity>
{


    List<StationInfoValidity> getStationInfoValidity(StationInfoValidity parm);
}