package com.sccl.modules.business.oilexpense.service;

import com.sccl.common.lang.StringUtils;
import com.sccl.common.support.Convert;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.modules.business.energyaccount.domain.EnergyAccount;
import com.sccl.modules.business.energyaccount.domain.EnergyAccountVo;
import com.sccl.modules.business.energyaccount.mapper.EnergyAccountMapper;
import com.sccl.modules.business.oilcard.domain.OilCard;
import com.sccl.modules.business.oilexpense.domain.OilExpense;
import com.sccl.modules.business.oilexpense.domain.OilExpenseRecord;
import com.sccl.modules.business.oilexpense.domain.OilExpenseVo;
import com.sccl.modules.business.oilexpense.mapper.OilExpenseMapper;
import com.sccl.modules.business.order.domain.Order;
import com.sccl.modules.business.order.service.IOrderService;
import com.sccl.modules.system.organization.domain.Organization;
import com.sccl.modules.system.organization.mapper.OrganizationMapper;
import com.sccl.modules.system.organization.service.IOrganizationService;
import com.sccl.modules.system.role.domain.Role;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.service.IUserService;
import com.sccl.modules.uniflow.common.WFModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;


/**
 * 油机基础 服务层实现
 *
 * <AUTHOR>
 * @date 2021-12-14
 */
@Service
@Transactional
public class OilExpenseServiceImpl extends BaseServiceImpl<OilExpense> implements IOilExpenseService {

    @Autowired
    private OilExpenseMapper oilExpenseMapper;
    @Autowired
    private EnergyAccountMapper energyAccountMapper;
    @Autowired
    private IUserService userService;
    @Value("${sccl.deployTo}")
    private String configVersion;
    @Autowired
    private IOrganizationService organizationService;
    @Autowired
    private OrganizationMapper organizationMapper;

    @Override
    public int updateSysstock(EnergyAccount e,int i) {
        OilExpense oilExpense = oilExpenseMapper.selectById(e.getCardid().toString());
        BigDecimal sysstock = oilExpense.getSysstock(); //当前油机系统库存
        if (sysstock == null) { //如果系统库存为null
            BigDecimal bigDecimal = new BigDecimal(0);
            sysstock = bigDecimal;
        }
        if (i == 1) { //增加油机库存
            sysstock = sysstock.add(e.getCurusedreadings());
        }
        if (i == 2) { //减少油机库存
            if (e.getCurusedreadings().signum() == -1) { //油量为负数,代表耗油库存减少
                sysstock = sysstock.add(e.getCurusedreadings());
            }else {
                sysstock = sysstock.subtract(e.getCurusedreadings());
            }
        }
        oilExpense.setSysstock(sysstock);
        int insert = oilExpenseMapper.updateForModel(oilExpense);
        if (insert != 0) {
            return 1;
        }
        return 0;
    }

    /**
     * 修改油机基础信息
     * <AUTHOR>
     * @date 2022/3/17
     */
    @Override
    public AjaxResult editSave(OilExpense oilExpense) {
        //根据油机编号查询(除开当前项)
        OilExpense oilExpense1 = oilExpenseMapper.selectByOilExpenseIdNoMe(oilExpense.getId(),oilExpense.getOilEngineId());
        if (oilExpense1 != null) {
            return AjaxResult.error("油机编号重复，请重新输入");
        }
        if (oilExpense.getStationCode() != null && !oilExpense.getStationCode().equals("")) {
            oilExpenseMapper.updateMachineAndStation(oilExpense.getId()); // 删除
            String[] split = oilExpense.getStationCode().split(",");
            for (String stationId : split) {
                oilExpenseMapper.saveMachineAndStation(Long.valueOf(stationId), oilExpense.getId());
            }
        }
        //保存旧数据记录
        OilExpense oilExpense2 = oilExpenseMapper.selectById(oilExpense.getId().toString());
        OilExpenseRecord oilExpenseRecord = new OilExpenseRecord();
        BeanUtils.copyProperties(oilExpense2, oilExpenseRecord);
        oilExpenseRecord.setId(null);
        oilExpenseRecord.setOilid(oilExpense.getId());
        oilExpenseRecord.setUpdatetime(new Date());
        oilExpenseRecord.setDelFlag("0");
        oilExpenseMapper.delNewOilExpense(oilExpense.getId());
        oilExpenseMapper.insertRecord(oilExpenseRecord);
        //更新数据
        int i = oilExpenseMapper.updateForModel(oilExpense);
        if (i != 0) {
            return AjaxResult.success("修改成功");
        }
        return AjaxResult.error("修改失败");

    }

    /**
     * 删除油机基础
     * 1.查询当前油机是否被使用
     * 2.删除油机
     * <AUTHOR>
     * @date 2022/3/17
     */
    @Override
    public AjaxResult removeOilExpense(String ids) {
        String[] split = ids.split(",");
        for (String id : split) {
            /** 查询该油机是否被使用 */
            int i = energyAccountMapper.selectUsedByOilEngineId(id);
            if (i != 0) {
                return AjaxResult.error("当前油机已被使用,无法删除");
            }
        }
        //删除油机
        int i = oilExpenseMapper.deleteByIds(Convert.toStrArray(ids));
        if (i != 0) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.error("删除失败");
    }

    @Override
    public List<OilExpense> selectListView(OilExpense oilExpense) {
        return oilExpenseMapper.selectListView(oilExpense);
    }

    @Override
    public List<OilExpenseVo> selectListVo(OilExpense oilExpense) {
        return oilExpenseMapper.selectListVo(oilExpense);
    }
    @Override
    public List<OilExpenseVo> selectListAuditVo(OilExpenseVo oilExpense) {
        return oilExpenseMapper.selectListAuditVo(oilExpense);
    }

    @Override
    public List<OilExpenseVo> selectListHavedAuditVo(OilExpenseVo oilExpense) {
        return oilExpenseMapper.selectListHavedAuditVo(oilExpense);
    }

    /**
     * 汇总单查询台账 - impl
     * <AUTHOR>
     * @date 2022/4/8
     */
    @Override
    public List<EnergyAccountVo> listAccountDetail(OilExpenseVo oilExpense) {
       return oilExpenseMapper.listAccountDetail(oilExpense);
    }

    @Override
    public List<OilExpenseVo> selectListAuditVo4(OilExpenseVo oilExpense) {
        return oilExpenseMapper.selectListAuditVo4(oilExpense);
    }

    @Override
    public List<OilExpenseVo> selectListAll(OilExpense oilExpense) {
        return oilExpenseMapper.selectListAll(oilExpense);
    }

    @Override
    public List<OilExpenseRecord> selectListViewOld(OilExpenseRecord oilExpense) {
        return oilExpenseMapper.selectListViewOld(oilExpense);
    }

    @Override
    public AjaxResult getUserByUserRole() {
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        boolean isEditAdmin = false;
        for (Role role : roles) {
            //用于判断能耗费市管理员、动力管理员、本地网能耗管理员可编辑责任中心) {
            if (null != role && StringUtils.isNotEmpty(role.getCode())) {
                String code = role.getCode();
                if ("CITY_ADMIN".equalsIgnoreCase(code) || "POWER_ADMIN".equalsIgnoreCase(code) || "LOCALNET_ADMIN".equalsIgnoreCase(code)) {
                    isEditAdmin = true;
                    break;
                }
            }
        }
        for (Role role : roles) {
            if (null != role && StringUtils.isNotEmpty(role.getCode()) && ("admin".equalsIgnoreCase(role.getCode()))) {
                isProAdmin = true;
                break;
            } else if (null != role && StringUtils.isNotEmpty(role.getCode()) && role.getCode().contains("_")) {
                String code = role.getCode().substring(0, role.getCode().indexOf("_", role.getCode().indexOf("_")));
                if ("PROVI".equalsIgnoreCase(code)) {//省能耗费管理员
                    isProAdmin = true;
                    break;
                } else if ("CITY".equalsIgnoreCase(code) || "POWER".equalsIgnoreCase(code)) {//省能耗费管理员
                    isCityAdmin = true;
                } else if ("SUB".equalsIgnoreCase(code)) {//县能耗费管理员
                    isSubAdmin = true;
                }
            }
        }
        List<IdNameVO> companies = new ArrayList<>();
        if (isProAdmin) {
            Organization organization = new Organization();
            if ("ln".equalsIgnoreCase(configVersion)) {
                organization.setParentCompanyNo("2600000000");
            } else {
                organization.setParentCompanyNo("9999999991");
            }
            organization.setOrgType("1");
            List<Organization> orgLists = organizationService.selectList(organization);
            for (Organization value : orgLists) {
                IdNameVO company = new IdNameVO();
                company.setId(value.getId().toString());
                company.setName(value.getOrgName());
                if (!companies.contains(company)) {
                    companies.add(company);
                }
            }
        } else {
            companies = user.getCompanies();
        }
        AjaxResult json = new AjaxResult();
        Map<String, Object> map = new HashMap<>();
        map.put("companies", companies);
        map.put("userId", user.getId());
        map.put("isProAdmin", isProAdmin);
        map.put("isCityAdmin", isCityAdmin);
        map.put("isSubAdmin", isSubAdmin);
        map.put("isEditAdmin", isEditAdmin);
        json.put("data", map);
        return json;
    }
    /**
     * 将LIST转换为前台能识别的tree
     * @param organizationList 待转换的list
     * @return tree
     * <AUTHOR>
     * @Date 2019/3/3 18:01
     */
    private List<Map<String, Object>> convertToTree(List<Organization> organizationList) {
//        if (organizationList == null || organizationList.size() == 0) {
//            return null;
//        }
        List<Map<String, Object>> trees = new ArrayList<Map<String, Object>>();

        for (Organization organization : organizationList) {
            // 可查詢老部门
//            if (UserConstants.DEPT_NORMAL.equals(organization.getStatus())) {
            Map<String, Object> deptMap = new HashMap<String, Object>();
            deptMap.put("id" , organization.getId());
            deptMap.put("pId" , organization.getParentId());
            deptMap.put("name" , organization.getOrgName());
            deptMap.put("title" , organization.getOrgName());
            deptMap.put("orgCode",organization.getOrgCode());
            deptMap.put("childNum",organization.getChildNum());
            deptMap.put("orgType", organization.getOrgType());
            trees.add(deptMap);
//            }
        }
        return trees;
    }
    /**
     * 根据当前节点编码查询下属的组织机构
     *
     * @param orgCode 节点编码
     * @return 当前节点下的所有组织（只含一级）
     * <AUTHOR>
     * @Date 2019/3/3 17:53
     */
    public List<Organization> selectAllSubRole(String orgCode,User user) {
        List<Organization> listOrg = new ArrayList<>();
        List<Organization> listOrgSub =  this.organizationMapper.selectDepByUserId(user.getId().toString());
        List<Organization> orgs = this.organizationMapper.selectOrgByCompany(orgCode);
        for (Organization value:listOrgSub) {
            for (Organization org:orgs) {
                if ("1".equals(org.getOrgType())) {
                    if (orgCode.equals(value.getParentId().toString())) {
                        listOrg.addAll(this.organizationMapper.selectSubordinateOrganization(value.getId()+""));
                        if(!listOrg.contains(value)){
                            listOrg.add(value);
                        }
                    }else{
                        listOrg.addAll(this.organizationMapper.selectOrgByCompany(value.getParentId()+""));
                        listOrg.addAll(this.organizationMapper.selectSubordinateOrganization(value.getParentId()+""));
                    }
                }else{
                    if (orgCode.equals(value.getId().toString())) {
                        listOrg.addAll(this.organizationMapper.selectSubordinateOrganization(value.getId()+""));
                        if(!listOrg.contains(value)){
                            listOrg.add(value);
                        }
                    }else{
                        listOrg.addAll(this.organizationMapper.selectOrgByCompany(value.getParentId()+""));
                        listOrg.addAll(this.organizationMapper.selectSubordinateOrganization(value.getParentId()+""));
                    }
                }
            }
        }
        return listOrg;
    }
    @Override
    public List<Map<String, Object>> selectSubordinateOrgByRole(String orgCode, String type){
        List<Organization> listOrg = new ArrayList<>();
        if (StringUtils.isBlank(orgCode)) {
            return null;
        }
        if("-1".equals(type)){//报账 不根据角色，获取分公司下的所有数据
            return this.convertToTree(this.organizationMapper.selectSubordinateOrganization(orgCode));
        }
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());

        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        for (Role role:roles) {
            if (null != role && StringUtils.isNotEmpty(role.getCode()) && ("admin".equalsIgnoreCase(role.getCode()))){
                isProAdmin = true;break;
            }else if (null != role && StringUtils.isNotEmpty(role.getCode()) && role.getCode().contains("_")){
                String code = role.getCode().substring(0, role.getCode().indexOf("_",role.getCode().indexOf("_")));
                if("PROVI".equalsIgnoreCase(code)){//省能耗费管理员
                    isProAdmin = true;break;
                }else if("CITY".equalsIgnoreCase(code)|| "POWER".equalsIgnoreCase(code)){//市能耗费管理员
                    isCityAdmin = true;
                }else if("SUB".equalsIgnoreCase(code)){//县能耗费管理员
                    isSubAdmin = true;
                }
            }
        }
//        List<Organization> listOrgSub = new ArrayList<>();
        if(isProAdmin || isCityAdmin){
            if(!"1".equals(type)){
                if ("1000085".equals(orgCode))
                listOrg = this.organizationMapper.selectSubordinateOrganizationoil(orgCode);
                else
                listOrg = this.organizationMapper.selectSubordinateOrganizationoilsub(orgCode);
            }
        }else if(isSubAdmin){
//            listOrgSub =  this.organizationMapper.selectDepByUserId(user.getId().toString());
            if("1".equals(type)){
                listOrg = this.selectAllSubRole(orgCode,user);
            }else{
                listOrg =  this.organizationMapper.selectDepByUserId(user.getId().toString());
                List<Organization> orgs = this.organizationMapper.selectOrgByCompany(orgCode);
                for (Organization value:listOrg) {
                    for (Organization org:orgs) {
                        if ("1".equals(org.getOrgType())) {
                            if (orgCode.equals(value.getParentId().toString())) {//一级部门
                                listOrg = this.organizationMapper.selectOrgByCompany(value.getId().toString());
                            } else {
                                listOrg = this.organizationMapper.selectOrgByCompanyoil(value.getParentId().toString());
                            }
                        }else{
                            listOrg = this.organizationMapper.selectSubordinateOrganization(orgCode);
                        }
                    }
//                    if (orgCode.equals(value.getId().toString())){
//                        listOrg = this.organizationMapper.selectSubordinateOrganization(orgCode);
//                    }else{
//                        listOrg = this.organizationMapper.selectSubordinateOrganization(value.getParentId().toString());
//                    }
                }
            }
        }else{
            listOrg =  this.organizationMapper.selectDepByUserId(user.getId().toString());
        }
        return this.convertToTree(listOrg);
    }
}
