package com.sccl.modules.mssaccount.mssaccountclearitemaccount.controller;

import java.util.List;

import com.sccl.framework.common.exception.base.BaseException;
import com.sccl.modules.business.accountEs.domain.AccountEsResult;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;
import com.sccl.modules.mssaccount.mssaccountbill.service.IMssAccountbillService;
import com.sccl.modules.mssaccount.rbillitemaccount.domain.RBillitemAccount;
import com.sccl.modules.mssaccount.rbillitemaccount.service.IRBillitemAccountService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.mssaccount.mssaccountclearitemaccount.domain.MssAccountclearitemAccount;
import com.sccl.modules.mssaccount.mssaccountclearitemaccount.service.IMssAccountclearitemAccountService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 预付冲销 挑对台账 信息操作处理
 *
 * <AUTHOR>
 * @date 2019-11-25
 */
@RestController
@RequestMapping("/mssaccount/mssAccountclearitemAccount")
public class MssAccountclearitemAccountController extends BaseController {
    private String prefix = "mssaccount/mssAccountclearitemAccount";

    @Autowired
    private IMssAccountclearitemAccountService mssAccountclearitemAccountService;

    //	@RequiresPermissions("mssaccount:mssAccountclearitemAccount:view")
    @GetMapping()
    public String mssAccountclearitemAccount() {
        return prefix + "/mssAccountclearitemAccount";
    }

    /**
     * 查询预付冲销 挑对台账列表
     */
//	@RequiresPermissions("mssaccount:mssAccountclearitemAccount:list")
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(MssAccountclearitemAccount mssAccountclearitemAccount) {
        startPage();
        List<MssAccountclearitemAccount> list = mssAccountclearitemAccountService.selectList(mssAccountclearitemAccount);
        return getDataTable(list);
    }

    @RequestMapping("/listNoPage")
    @ResponseBody
    public AjaxResult listNoPage(MssAccountclearitemAccount mssAccountclearitemAccount) {
        List<MssAccountclearitemAccount> list = mssAccountclearitemAccountService.selectList(mssAccountclearitemAccount);
        return this.success(list);
    }
    /**
     * 新增预付冲销 挑对台账
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存预付冲销 挑对台账
     */
//	@RequiresPermissions("mssaccount:mssAccountclearitemAccount:add")
    @Log(title = "预付冲销 挑对台账", action = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody MssAccountclearitemAccount mssAccountclearitemAccount) {
        return toAjax(mssAccountclearitemAccountService.insert(mssAccountclearitemAccount));
    }

    /**
     * 修改预付冲销 挑对台账
     */
    @GetMapping("/edit/{id}")
    public AjaxResult edit(@PathVariable("id") Long id) {
        MssAccountclearitemAccount mssAccountclearitemAccount = mssAccountclearitemAccountService.get(id);

        Object object = JSONObject.toJSON(mssAccountclearitemAccount);

        return this.success(object);
    }

    /**
     * 修改保存预付冲销 挑对台账
     */
//	@RequiresPermissions("mssaccount:mssAccountclearitemAccount:edit")
    @Log(title = "预付冲销 挑对台账", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody MssAccountclearitemAccount mssAccountclearitemAccount) {
        return toAjax(mssAccountclearitemAccountService.update(mssAccountclearitemAccount));
    }

    /**
     * 删除预付冲销 挑对台账
     */
//	@RequiresPermissions("mssaccount:mssAccountclearitemAccount:remove")
//	@Log(title = "预付冲销 挑对台账", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(mssAccountclearitemAccountService.deleteByIdsDB(Convert.toStrArray(ids)));
    }


    /**
     * 查看预付冲销 挑对台账
     */
//    @RequiresPermissions("mssaccount:mssAccountclearitemAccount:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id) {
        MssAccountclearitemAccount mssAccountclearitemAccount = mssAccountclearitemAccountService.get(id);

        Object object = JSONObject.toJSON(mssAccountclearitemAccount);

        return this.success(object);
    }

    @PostMapping("/listBypcids")
    @ResponseBody
    public AjaxResult listBypcids(String ids) {
        List<MssAccountclearitemAccount> list = mssAccountclearitemAccountService.listBypcids(Convert.toStrArray(ids));
        return this.success(list);
    }

    @PostMapping("/listBybillIds")
    @ResponseBody
    public AjaxResult listBybillIds(String ids) {
        List<MssAccountclearitemAccount> list = mssAccountclearitemAccountService.listBybillIds(Convert.toStrArray(ids));
        return this.success(list);
    }

    @Autowired
    private IRBillitemAccountService rBillitemAccountService;
    @Autowired
    private IMssAccountbillService mssAccountbillService;

    @PostMapping("/accountEslistBybillIds")
    @ResponseBody
    public AjaxResult accountEslistBybillIds(String ids) {
        String[] strings = Convert.toStrArray(ids);
        String[] newIds = new String[strings.length];
        for (int i = 0; i < strings.length; i++) {
            String str = strings[i];
            if (str.matches("^[0-9]*$")) {
                newIds[i] = str;
            } else {
                MssAccountbill m = new MssAccountbill();
                m.setWriteoffInstanceCode(str);
                List<MssAccountbill> mssAccountbills = mssAccountbillService.selectList(m);
                if (mssAccountbills != null && mssAccountbills.size() > 0)
                    newIds[i] = mssAccountbills.get(0).getId().toString();
                else
                    throw new BaseException("根据【" + str + "】未获取到报账单");
            }
        }
        List<AccountEsResult> list = rBillitemAccountService.accountEslistBybillIds(newIds);
        return this.success(list);
    }

    @GetMapping("/listBybillId/{id}")
    @ResponseBody
    public AjaxResult listBybillId(@PathVariable("id") Long id) {
        List<MssAccountclearitemAccount> list = mssAccountclearitemAccountService.listBybillId(id);
        return this.success(list);
    }

}
