package com.sccl.modules.mssaccount.dataanalysis.service;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sccl.exception.BusinessException;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.statinAudit.domain.SysOrganizations;
import com.sccl.modules.business.statinAudit.mapper.SysOrganizationsMapper;
import com.sccl.modules.mssaccount.dataanalysis.dto.StatisticsListDTO;
import com.sccl.modules.mssaccount.dataanalysis.dto.StatisticsMeterListDTO;
import com.sccl.modules.mssaccount.dataanalysis.mapper.StatisticalAnalysisMapper;
import com.sccl.modules.mssaccount.dataanalysis.vo.PowerStationInfoDetailListVO;
import com.sccl.modules.mssaccount.dataanalysis.vo.PowerStationInfoMeterDetailListVO;
import com.sccl.modules.mssaccount.dataanalysis.vo.StatisticsListVO;
import com.sccl.modules.mssaccount.dataanalysis.vo.StatisticsMeterListVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class StationMeterAnalysisServiceImpl implements StationMeterAnalysisService {

    private final StatisticalAnalysisMapper statisticAnalysisMapper;

    private final SysOrganizationsMapper organizationMapper;

    @Override
    public TableDataInfo statisticsList(StatisticsListDTO dto) {

        /**
         * 全省电用类型统计
         */
        // 查询全省数据
        StatisticsListDTO params = new StatisticsListDTO();
        BeanUtils.copyProperties(dto, params);
        params.setCompany(null);
        StatisticsListVO provinceStatistics = statisticAnalysisMapper.statisticsList(params);
        if (ObjUtil.isNotEmpty(provinceStatistics)) {
            provinceStatistics.setOrgName("全省");
            if (provinceStatistics.getProductionBuilding10001() == 0) {
                provinceStatistics.setProductionBuilding10001Percentage("0%");
            } else {
                provinceStatistics.setProductionBuilding10001Percentage("100%");
            }

            if (provinceStatistics.getProductionBuilding10002() == 0) {
                provinceStatistics.setProductionBuilding10002Percentage("0%");
            } else {
                provinceStatistics.setProductionBuilding10002Percentage("100%");
            }

            if (provinceStatistics.getProductionBuilding10003() == 0) {
                provinceStatistics.setProductionBuilding10003Percentage("0%");
            } else {
                provinceStatistics.setProductionBuilding10003Percentage("100%");
            }

            if (provinceStatistics.getProductionBuilding10004() == 0) {
                provinceStatistics.setProductionBuilding10004Percentage("0%");
            } else {
                provinceStatistics.setProductionBuilding10004Percentage("100%");
            }

            if (provinceStatistics.getProductionBuilding10005() == 0) {
                provinceStatistics.setProductionBuilding10005Percentage("0%");
            } else {
                provinceStatistics.setProductionBuilding10005Percentage("100%");
            }

            if (provinceStatistics.getNonProductionBuilding20001() == 0) {
                provinceStatistics.setNonProductionBuilding20001Percentage("0%");
            } else {
                provinceStatistics.setNonProductionBuilding20001Percentage("100%");
            }

            if (provinceStatistics.getNonProductionBuilding20002() == 0) {
                provinceStatistics.setNonProductionBuilding20002Percentage("0%");
            } else {
                provinceStatistics.setNonProductionBuilding20002Percentage("100%");
            }

            if (provinceStatistics.getProductionBuildingOther() == 0) {
                provinceStatistics.setProductionBuildingOtherPercentage("0%");
            } else {
                provinceStatistics.setProductionBuildingOtherPercentage("100%");
            }

            if (provinceStatistics.getNonProductionBuildingOther() == 0) {
                provinceStatistics.setNonProductionBuildingOtherPercentage("0%");
            } else {
                provinceStatistics.setNonProductionBuildingOtherPercentage("100%");
            }
        } else {
            provinceStatistics = new StatisticsListVO();
            provinceStatistics.setOrgName("全省");
            provinceStatistics.setProductionBuilding10001Percentage("0%");
            provinceStatistics.setProductionBuilding10002Percentage("0%");
            provinceStatistics.setProductionBuilding10003Percentage("0%");
            provinceStatistics.setProductionBuilding10004Percentage("0%");
            provinceStatistics.setProductionBuilding10005Percentage("0%");
            provinceStatistics.setNonProductionBuilding20001Percentage("0%");
            provinceStatistics.setNonProductionBuilding20002Percentage("0%");
            provinceStatistics.setProductionBuildingOtherPercentage("0%");
            provinceStatistics.setNonProductionBuildingOtherPercentage("0%");
        }
        List<StatisticsListVO> resultList = new ArrayList<>();
        resultList.add(provinceStatistics);
        log.info("全省数据:{}", provinceStatistics);

        // 根据机构分页
        IPage<SysOrganizations> page = new Page<>(dto.getPageNumber(), dto.getPageSize());
        IPage<SysOrganizations> iPage = organizationMapper.selectPage(page, Wrappers.<SysOrganizations>lambdaQuery()
                .select(SysOrganizations::getId, SysOrganizations::getOrgName, SysOrganizations::getOrgType)
                .eq(dto.getCompany() != null, SysOrganizations::getId, dto.getCompany())
                .orderByAsc(SysOrganizations::getOrgType)
        );
        for (SysOrganizations org : iPage.getRecords()) {
            log.info("机构：{}", org);
            // 2. 查询统计数据
            dto.setCompany(org.getId());
            StatisticsListVO vo = statisticAnalysisMapper.statisticsList(dto);
            if (vo == null) {
                vo = new StatisticsListVO();
            }
            vo.setOrgId(org.getId());
            vo.setOrgName(org.getOrgName());
            resultList.add(vo);

            // 根据全省数据，计算每个机构的类型占比
            Integer total10001 = provinceStatistics.getProductionBuilding10001();
            Integer total10002 = provinceStatistics.getProductionBuilding10002();
            Integer total10003 = provinceStatistics.getProductionBuilding10003();
            Integer total10004 = provinceStatistics.getProductionBuilding10004();
            Integer total10005 = provinceStatistics.getProductionBuilding10005();
            Integer totalNon20001 = provinceStatistics.getNonProductionBuilding20001();
            Integer totalNon20002 = provinceStatistics.getNonProductionBuilding20002();
            Integer totalOther = provinceStatistics.getProductionBuildingOther();
            Integer totalNonOther = provinceStatistics.getNonProductionBuildingOther();

            // 优化：处理除数为 0 的情况
            vo.setProductionBuilding10001Percentage(calculatePercentage(vo.getProductionBuilding10001(), total10001) + "%");
            vo.setProductionBuilding10002Percentage(calculatePercentage(vo.getProductionBuilding10002(), total10002) + "%");
            vo.setProductionBuilding10003Percentage(calculatePercentage(vo.getProductionBuilding10003(), total10003) + "%");
            vo.setProductionBuilding10004Percentage(calculatePercentage(vo.getProductionBuilding10004(), total10004) + "%");
            vo.setProductionBuilding10005Percentage(calculatePercentage(vo.getProductionBuilding10005(), total10005) + "%");
            vo.setNonProductionBuilding20001Percentage(calculatePercentage(vo.getNonProductionBuilding20001(), totalNon20001) + "%");
            vo.setNonProductionBuilding20002Percentage(calculatePercentage(vo.getNonProductionBuilding20002(), totalNon20002) + "%");
            vo.setProductionBuildingOtherPercentage(calculatePercentage(vo.getProductionBuildingOther(), totalOther) + "%");
            vo.setNonProductionBuildingOtherPercentage(calculatePercentage(vo.getNonProductionBuildingOther(), totalNonOther) + "%");
        }
        TableDataInfo tableDataInfo = new TableDataInfo();
        tableDataInfo.setCode(0);
        tableDataInfo.setRows(resultList);
        tableDataInfo.setTotal(iPage.getTotal());
        return tableDataInfo;
    }

    /**
     * 计算百分比的方法，处理除数为 0 的情况
     *
     * @param numerator   分子
     * @param denominator 分母
     * @return 计算后的百分比字符串，格式为 "xx.xx%"
     */
    private String calculatePercentage(Integer numerator, Integer denominator) {
        if (numerator == null || denominator == 0) {
            return "0.00";
        }
        double percentage = (numerator * 100.0 / denominator);
        return String.format("%.2f", percentage);
    }


    @Override
    public List<PowerStationInfoDetailListVO> stationStatisticsDetailList(StatisticsListDTO dto) {
        return statisticAnalysisMapper.stationStatisticsDetailList(dto);
    }

    @Override
    public Dict statisticsMeterList(StatisticsMeterListDTO dto) {
        // 先查询全省数据
        StatisticsMeterListDTO provinceDto = new StatisticsMeterListDTO();
        BeanUtils.copyProperties(dto, provinceDto);
        provinceDto.setCompany(null);
        StatisticsMeterListVO provinceMeterListVO = statisticAnalysisMapper.statisticsMeterList(provinceDto);
        // 全省电表数据初始化
        this.initializationOfProvincialMeterData(provinceMeterListVO);
        List<StatisticsMeterListVO> resultList = new ArrayList<>();
        resultList.add(provinceMeterListVO);
        // 根据机构分页
        IPage<SysOrganizations> page = new Page<>(dto.getPageNumber(), dto.getPageSize());
        IPage<SysOrganizations> iPage = organizationMapper.selectPage(page, Wrappers.<SysOrganizations>lambdaQuery()
                .select(SysOrganizations::getId, SysOrganizations::getOrgName, SysOrganizations::getOrgType)
                .eq(dto.getCompany() != null, SysOrganizations::getId, dto.getCompany())
                .orderByAsc(SysOrganizations::getOrgType)
        );
        for (SysOrganizations org : iPage.getRecords()) {
            log.info("机构：{}", org);
            // 2. 查询统计数据
            dto.setCompany(org.getId());
            StatisticsMeterListVO vo = statisticAnalysisMapper.statisticsMeterList(dto);
            if (vo == null) {
                vo = new StatisticsMeterListVO();
            }
            vo.setOrgId(org.getId());
            vo.setOrgName(org.getOrgName());
            resultList.add(vo);
            Integer total10001 = provinceMeterListVO.getProductionBuilding10001();
            Integer totalSelf10002 = provinceMeterListVO.getProductionBuildingSelf10002();
            Integer totalTower10002 = provinceMeterListVO.getProductionBuildingTower10002();
            Integer total10003 = provinceMeterListVO.getProductionBuilding10003();
            Integer total10004 = provinceMeterListVO.getProductionBuilding10004();
            Integer total10005 = provinceMeterListVO.getProductionBuilding10005();
            Integer totalNon20001 = provinceMeterListVO.getNonProductionBuilding20001();
            Integer totalNon20002 = provinceMeterListVO.getNonProductionBuilding20002();
            Integer totalOther = provinceMeterListVO.getProductionBuildingOther();
            Integer totalNonOther = provinceMeterListVO.getNonProductionBuildingOther();

            // 优化：处理除数为 0 的情况
            vo.setProductionBuilding10001Percentage(calculatePercentage(vo.getProductionBuilding10001(), total10001) + "%");
            vo.setProductionBuildingSelf10002Percentage(calculatePercentage(vo.getProductionBuildingSelf10002(), totalSelf10002) + "%");
            vo.setProductionBuildingTower10002Percentage(calculatePercentage(vo.getProductionBuildingTower10002(), totalTower10002) + "%");
            vo.setProductionBuilding10003Percentage(calculatePercentage(vo.getProductionBuilding10003(), total10003) + "%");
            vo.setProductionBuilding10004Percentage(calculatePercentage(vo.getProductionBuilding10004(), total10004) + "%");
            vo.setProductionBuilding10005Percentage(calculatePercentage(vo.getProductionBuilding10005(), total10005) + "%");
            vo.setNonProductionBuilding20001Percentage(calculatePercentage(vo.getNonProductionBuilding20001(), totalNon20001) + "%");
            vo.setNonProductionBuilding20002Percentage(calculatePercentage(vo.getNonProductionBuilding20002(), totalNon20002) + "%");
            vo.setProductionBuildingOtherPercentage(calculatePercentage(vo.getProductionBuildingOther(), totalOther) + "%");
            vo.setNonProductionBuildingOtherPercentage(calculatePercentage(vo.getNonProductionBuildingOther(), totalNonOther) + "%");

        }
        return Dict.create().set("rows", resultList).set("total", iPage.getTotal());
    }



    /**
     * 全省电表数据初始化
     * @param provinceMeterListVO 全省电表数据
     */
    private void initializationOfProvincialMeterData(StatisticsMeterListVO provinceMeterListVO) {
        if (provinceMeterListVO != null) {
            provinceMeterListVO.setOrgName("全省");
            if (provinceMeterListVO.getProductionBuilding10001() == 0) {
                provinceMeterListVO.setProductionBuilding10001Percentage("0%");
            }else {
                provinceMeterListVO.setProductionBuilding10001Percentage("100%");
            }
            if (provinceMeterListVO.getProductionBuildingSelf10002() == 0) {
                provinceMeterListVO.setProductionBuildingSelf10002Percentage("0%");
            }else {
                provinceMeterListVO.setProductionBuildingSelf10002Percentage("100%");
            }
            if (provinceMeterListVO.getProductionBuildingTower10002() == 0) {
                provinceMeterListVO.setProductionBuildingTower10002Percentage("0%");
            }else {
                provinceMeterListVO.setProductionBuildingTower10002Percentage("100%");
            }
            if (provinceMeterListVO.getProductionBuilding10003() == 0) {
                provinceMeterListVO.setProductionBuilding10003Percentage("0%");
            }else {
                provinceMeterListVO.setProductionBuilding10003Percentage("100%");
            }
            if (provinceMeterListVO.getProductionBuilding10004() == 0) {
                provinceMeterListVO.setProductionBuilding10004Percentage("0%");
            }else {
                provinceMeterListVO.setProductionBuilding10004Percentage("100%");
            }
            if (provinceMeterListVO.getProductionBuilding10005() == 0) {
                provinceMeterListVO.setProductionBuilding10005Percentage("0%");
            }else {
                provinceMeterListVO.setProductionBuilding10005Percentage("100%");
            }
            if (provinceMeterListVO.getNonProductionBuilding20001() == 0) {
                provinceMeterListVO.setNonProductionBuilding20001Percentage("0%");
            }else {
                provinceMeterListVO.setNonProductionBuilding20001Percentage("100%");
            }
            if (provinceMeterListVO.getNonProductionBuilding20002() == 0) {
                provinceMeterListVO.setNonProductionBuilding20002Percentage("0%");
            }else {
                provinceMeterListVO.setNonProductionBuilding20002Percentage("100%");
            }
            if (provinceMeterListVO.getProductionBuildingOther() == 0) {
                provinceMeterListVO.setProductionBuildingOtherPercentage("0%");
            }else {
                provinceMeterListVO.setProductionBuildingOtherPercentage("100%");
            }
            if (provinceMeterListVO.getNonProductionBuildingOther() == 0) {
                provinceMeterListVO.setNonProductionBuildingOtherPercentage("0%");
            }else {
                provinceMeterListVO.setNonProductionBuildingOtherPercentage("100%");
            }
        }
        // 没有数据填充默认值
        else {
            provinceMeterListVO = new StatisticsMeterListVO();
            provinceMeterListVO.setProductionBuilding10001Percentage("0%");
            provinceMeterListVO.setProductionBuildingSelf10002Percentage("0%");
            provinceMeterListVO.setProductionBuildingTower10002Percentage("0%");
            provinceMeterListVO.setProductionBuilding10003Percentage("0%");
            provinceMeterListVO.setProductionBuilding10004Percentage("0%");
            provinceMeterListVO.setProductionBuilding10005Percentage("0%");
            provinceMeterListVO.setNonProductionBuilding20001Percentage("0%");
            provinceMeterListVO.setNonProductionBuilding20002Percentage("0%");
            provinceMeterListVO.setProductionBuildingOtherPercentage("0%");
            provinceMeterListVO.setNonProductionBuildingOtherPercentage("0%");
        }
    }


    @Override
    public List<PowerStationInfoMeterDetailListVO> stationStatisticsMeterDetailList(StatisticsMeterListDTO dto) {
        if (dto.getCompany() == null) {
            throw new BusinessException("机构/部门ID不能为空");
        }
        if (dto.getStationtype() == null) {
            throw new BusinessException("局站类型不能为空");
        }
        return statisticAnalysisMapper.stationStatisticsMeterDetailList(dto);
    }

}
