package com.sccl.modules.business.oilcardaccount.mapper;

import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.oilcardaccount.domain.OilCardAccount;
import com.sccl.modules.business.oilcardaccount.domain.OilCardAccountVo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 油卡台账 数据层
 *
 * <AUTHOR>
 * @date 2021-12-20
 */
public interface OilCardAccountMapper extends BaseMapper<OilCardAccount> {
    /**
     * 批量查询指定id的台账
     *
     * @param ids
     * @return java.util.List<com.sccl.modules.business.oilcardaccount.domain.OilCardAccount>
     * <AUTHOR>
     * @date 2021/12/21 13:36
     */
    List<OilCardAccount> selectAllById(List<Long> ids);

    OilCardAccount querySumAccount(OilCardAccount oilCardAccount);

    // 修改油卡台账状态
    int updateStatusById(Map<String, Object> map);

    OilCardAccount queryById(String id);

    List<OilCardAccountVo> selectListVo(OilCardAccount oilCardAccount);

    //批量查询油卡
    List<OilCardAccount> queryByIdList(ArrayList<Object> list);

    //修改购油台账库存量
    void updateSurplus(OilCardAccount oilCardAccount);

    //查询购油台账是否使用该油卡
    int selectUsedbyCardid(String id);
}
