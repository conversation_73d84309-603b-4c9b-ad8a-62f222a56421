package com.sccl.modules.mssaccount.dataanalysis.vo;

import lombok.Data;

/**
 * 局站统计列表
 */
@Data
public class StatisticsListVO {

    public StatisticsListVO() {
        this.setProductionBuilding10001(0);
        this.setProductionBuilding10002(0);
        this.setProductionBuilding10003(0);
        this.setProductionBuilding10004(0);
        this.setProductionBuilding10005(0);
        this.setNonProductionBuilding20001(0);
        this.setNonProductionBuilding20002(0);
        this.setProductionBuildingOther(0);
        this.setNonProductionBuildingOther(0);
    }

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 生产用房-通信机房
     */
    private Integer productionBuilding10001;

    /**
     * 生产用房-通信机房-占比
     */
    private String productionBuilding10001Percentage;

    /**
     * 生产用房-移动基站
     */
    private Integer productionBuilding10002;

    /**
     * 生产用房-移动基站-占比
     */
    private String productionBuilding10002Percentage;

    /**
     * 生产用房-数据中心-对外IDC机柜机房
     */
    private Integer productionBuilding10003;

    /**
     * 生产用房-数据中心-对外IDC机柜机房-占比
     */
    private String productionBuilding10003Percentage;

    /**
     * 生产用房-数据中心-自用业务平台和IT支撑用房
     */
    private Integer productionBuilding10004;

    /**
     * 生产用房-数据中心-自用业务平台和IT支撑用房-占比
     */
    private String productionBuilding10004Percentage;

    /**
     * 生产用房-接入局所及室外机柜
     */
    private Integer productionBuilding10005;

    /**
     * 生产用房-接入局所及室外机柜-占比
     */
    private String productionBuilding10005Percentage;

    /**
     * 非生产用房-管理用房
     */
    private Integer nonProductionBuilding20001;

    /**
     * 非生产用房-管理用房-占比
     */
    private String nonProductionBuilding20001Percentage;

    /**
     * 非生产用房-渠道用房
     */
    private Integer nonProductionBuilding20002;

    /**
     * 非生产用房-渠道用房-占比
     */
    private String nonProductionBuilding20002Percentage;

    /**
     * 生产用房-其他
     */
    private Integer productionBuildingOther;

    /**
     * 生产用房-其他-占比
     */
    private String productionBuildingOtherPercentage;

    /**
     * 非生产用房-其他
     */
    private Integer nonProductionBuildingOther;

    /**
     * 非生产用房-其他-占比
     */
    private String nonProductionBuildingOtherPercentage;
}
