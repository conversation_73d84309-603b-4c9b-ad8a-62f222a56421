package com.sccl.modules.business.meterinfoalljt_new.controller;

import com.sccl.common.lang.StringUtils;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.utils.ExcelUtil;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.cost.service.IConsistencyGkService;
import com.sccl.modules.business.meterinfoalljt_new.service.IMeterinfoAllJtApplyService;
import com.sccl.modules.business.meterinfoalljt_new.vo.MeterinfoAllJtApplyDelVo;
import com.sccl.modules.business.meterinfoalljt_new.vo.MeterinfoAllJtApplyResultVo;
import com.sccl.modules.business.meterinfoalljt_new.vo.MeterinfoAllJtApplySaveVo;
import com.sccl.modules.business.meterinfoalljt_new.vo.MeterinfoAllJtApplySearchVo;
import com.sccl.modules.business.statinAudit.mapper.PowerStationInfoMapper;
import com.sccl.modules.mssaccount.mssinterface.service.IMssInterfaceService;
import com.sccl.modules.system.user.domain.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 在网表计数据清单-新增申请
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@RestController
@RequestMapping("/business/meterinfoAllJtApply")
@Slf4j
public class MeterinfoAllJtApplyController extends BaseController {

    @Autowired
    private IMeterinfoAllJtApplyService meterinfoAllJtApplyService;

    @Autowired
    private IConsistencyGkService consistencyGkService;
    @Autowired
    private PowerStationInfoMapper stationInfoMapper;
    @Autowired
    private IMssInterfaceService mssInterfaceService;

    @Value("${sccl.deployTo}")
    private String deployTo;

    /**
     * 一览查询
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(@RequestBody MeterinfoAllJtApplySearchVo searchVo) {
        setParam(searchVo);//参数处理
        startPage(searchVo.getPageNum(), searchVo.getPageSize());
        List<MeterinfoAllJtApplyResultVo> result = meterinfoAllJtApplyService.list(searchVo);
        return getDataTable(result);
    }

    /**
     * 一览 导出
     */
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(@RequestBody MeterinfoAllJtApplySearchVo searchVo, HttpServletRequest request, HttpServletResponse response) throws Exception {
        setParam(searchVo);//参数处理
        startPage(searchVo.getPageNum(), searchVo.getPageSize());
        List<MeterinfoAllJtApplyResultVo> list = meterinfoAllJtApplyService.list(searchVo);
        ExcelUtil<MeterinfoAllJtApplyResultVo> util = new ExcelUtil<>(MeterinfoAllJtApplyResultVo.class);
        return util.exportExcelToBrowser(response, list, "在网表计数据清单-新增申请");
    }

    /**
     * 待添加电表一览查询
     */
    @PostMapping("/ammeter/list")
    @ResponseBody
    public TableDataInfo ammeterList(@RequestBody MeterinfoAllJtApplySearchVo searchVo) {
        setParam(searchVo);//参数处理
        startPage(searchVo.getPageNum(), searchVo.getPageSize());
        List<MeterinfoAllJtApplyResultVo> result = meterinfoAllJtApplyService.ammeterList(searchVo);
        return getDataTable(result);
    }

    /**
     * 查询参数处理
     * @param searchVo
     */
    private void setParam(MeterinfoAllJtApplySearchVo searchVo) {
        //所属公司和所属部门处理
        User user = ShiroUtils.getUser();
        String admin = consistencyGkService.getAdmin(user);//判断登录用是省/市/区级
        searchVo.setAdmin(admin);
        if (StringUtils.isBlank(searchVo.getCompany()) || StringUtils.isBlank(searchVo.getCountry())) {
            if ("2".equals(admin)) {
                //市级
                if (StringUtils.isBlank(searchVo.getCompany())) {
                    List<IdNameVO> companies = user.getCompanies();
                    if (companies != null && companies.size() > 0) {
                        List<String> ids = new ArrayList<>();
                        for (IdNameVO company : companies) {
                            ids.add(company.getId());
                        }
                        searchVo.setCompanys(ids.toArray(new String[companies.size()]));
                    }
                }
            } else if ("3".equals(admin)) {
                //区县级
                if (StringUtils.isBlank(searchVo.getCompany())) {
                    List<IdNameVO> companies = user.getCompanies();
                    if (companies != null && companies.size() > 0) {
                        List<String> ids = new ArrayList<>();
                        for (IdNameVO company : companies) {
                            ids.add(company.getId());
                        }
                        searchVo.setCompanys(ids.toArray(new String[companies.size()]));
                    }
                }
                if (StringUtils.isBlank(searchVo.getCountry())) {
                    List<IdNameVO> countrys = user.getDepartments();
                    if (countrys != null && countrys.size() > 0) {
                        List<String> ids = new ArrayList<>();
                        for (IdNameVO country : countrys) {
                            ids.add(country.getId());
                        }
                        searchVo.setCountrys(ids.toArray(new String[countrys.size()]));
                    }
                }
            }
        }

        //数据部门处理
        String prov = "9999999991";              //四川登录用户所属省份id
        if ("ln".equals(deployTo)) {
            prov = "2600000000";                 //辽宁登录用户所属省份id
        }
        if ("-1".equals(searchVo.getCompany()) || prov.equals(searchVo.getCompany())) {
            searchVo.setCompany("");
        }
        if ("-1".equals(searchVo.getCountry())) {
            searchVo.setCountry("");
        }
        //电表编号
        if (StringUtils.isNotBlank(searchVo.getAmmeterCode())) {
            searchVo.setAmmeterCodes(StringUtils.split(searchVo.getAmmeterCode(), ","));
        }
        //站址编码
        if (StringUtils.isNotBlank(searchVo.getResstationcode())) {
            searchVo.setResstationcodes(StringUtils.split(searchVo.getResstationcode(), ","));
        }
        //状态
        if("-1".equals(searchVo.getStatus())){
            searchVo.setStatus("");
        }
    }

    /**
     * 添加-保存
     * @param vo
     * @return
     */
    @Log(title = "在网表计数据清单-新增申请-添加保存", action = BusinessType.INSERT)
    @PostMapping("/save")
    @ResponseBody
    public AjaxResult save(@Validated @RequestBody MeterinfoAllJtApplySaveVo vo) {
        if (vo == null) {
            return AjaxResult.error("没有需要提交的数据");
        }
        //保存前校验
        String msg = validBeforeSave(vo);
        if (StringUtils.isNotBlank(msg)) {
            return AjaxResult.error(msg);
        }
        //执行保存
        msg = meterinfoAllJtApplyService.save(vo);
        if (StringUtils.isNotBlank(msg)) {
            return AjaxResult.error(msg);
        }
        return AjaxResult.success("添加成功");
    }

    /**
     * 参数处理
     * @param vo
     */
    private String validBeforeSave(MeterinfoAllJtApplySaveVo vo) {
        User user = ShiroUtils.getUser();
        if (user == null) {
            return "登录用户不存在，添加失败";
        }
        //全选时设置查询参数
        if ("1".equals(vo.getAll())) {
            //所属公司和所属部门处理
            if (StringUtils.isBlank(vo.getCompany()) || StringUtils.isBlank(vo.getCountry())) {
                String admin = consistencyGkService.getAdmin(user);//判断登录用是省/市/区级
                if ("2".equals(admin)) {
                    //市级
                    if (StringUtils.isBlank(vo.getCompany())) {
                        List<IdNameVO> companies = user.getCompanies();
                        if (companies != null && companies.size() > 0) {
                            List<String> ids = new ArrayList<>();
                            for (IdNameVO company : companies) {
                                ids.add(company.getId());
                            }
                            vo.setCompanys(ids.toArray(new String[companies.size()]));
                        }
                    }
                } else if ("3".equals(admin)) {
                    //区县级
                    if (StringUtils.isBlank(vo.getCompany())) {
                        List<IdNameVO> companies = user.getCompanies();
                        if (companies != null && companies.size() > 0) {
                            List<String> ids = new ArrayList<>();
                            for (IdNameVO company : companies) {
                                ids.add(company.getId());
                            }
                            vo.setCompanys(ids.toArray(new String[companies.size()]));
                        }
                    }
                    if (StringUtils.isBlank(vo.getCountry())) {
                        List<IdNameVO> countrys = user.getDepartments();
                        if (countrys != null && countrys.size() > 0) {
                            List<String> ids = new ArrayList<>();
                            for (IdNameVO country : countrys) {
                                ids.add(country.getId());
                            }
                            vo.setCountrys(ids.toArray(new String[countrys.size()]));
                        }
                    }
                }
            }

            //数据部门处理
            String prov = "9999999991";              //四川登录用户所属省份id
            if ("ln".equals(deployTo)) {
                prov = "2600000000";                 //辽宁登录用户所属省份id
            }
            if ("-1".equals(vo.getCompany()) || prov.equals(vo.getCompany())) {
                vo.setCompany("");
            }
            if ("-1".equals(vo.getCountry())) {
                vo.setCountry("");
            }
            //电表编号
            if (StringUtils.isNotBlank(vo.getAmmeterCode())) {
                vo.setAmmeterCodes(StringUtils.split(vo.getAmmeterCode(), ","));
            }
            //站址编码
            if (StringUtils.isNotBlank(vo.getResstationcode())) {
                vo.setResstationcodes(StringUtils.split(vo.getResstationcode(), ","));
            }
        } else {
            //非全选时，电表id不能为空
            if(StringUtils.isBlank(vo.getId())){
                return "电表id不能为空";
            }
            //电表id
            if (StringUtils.isNotBlank(vo.getId())) {
                vo.setIds(StringUtils.split(vo.getId(), ","));
            }
            //置空其他参数
            vo.setCompany("");
            vo.setCompanys(null);
            vo.setCountry("");
            vo.setCountrys(null);
            vo.setKey("");
            vo.setAmmeterCode("");
            vo.setAmmeterCodes(null);
            vo.setResstationcode("");
            vo.setResstationcodes(null);
        }
        vo.initInsert(user.getUserName());
        return "";
    }

    /**
     * 提交
     * @param vo
     * @return
     */
    @Log(title = "在网表计数据清单-新增申请-提交", action = BusinessType.DELETE)
    @PostMapping("/submit")
    @ResponseBody
    public AjaxResult submit(@Validated @RequestBody MeterinfoAllJtApplyDelVo vo) {
        if (vo == null) {
            return AjaxResult.error("没有需要提交的数据");
        }
        String msg = meterinfoAllJtApplyService.submit(vo);
        if (StringUtils.isNotBlank(msg)) {
            return AjaxResult.error(msg);
        }
        return AjaxResult.success("提交成功");
    }

    /**
     * 删除
     * @param vo
     * @return
     */
    @Log(title = "在网表计数据清单-新增申请-删除", action = BusinessType.DELETE)
    @PostMapping("/del")
    @ResponseBody
    public AjaxResult del(@Validated @RequestBody MeterinfoAllJtApplyDelVo vo) {
        if (vo == null) {
            return AjaxResult.error("没有需要删除的数据");
        }
        String msg = meterinfoAllJtApplyService.del(vo);
        if (StringUtils.isNotBlank(msg)) {
            return AjaxResult.error(msg);
        }
        return AjaxResult.success("删除成功");
    }

    /**
     * 推送
     * @return
     */
    @Log(title = "在网表计数据清单-新增申请-推送", action = BusinessType.UPDATE)
    @PostMapping("/push")
    @ResponseBody
    public AjaxResult push() {
        return AjaxResult.success("推送成功");
    }

    /**
     * 根据网表计清单，更新数据状态为已处理
     * @return
     */
    @Log(title = "在网表计数据清单-新增申请-根据网表计清单，更新数据状态为已处理", action = BusinessType.UPDATE)
    @PostMapping("/completed")
    @ResponseBody
    public AjaxResult completed() {
        meterinfoAllJtApplyService.completed();
        return AjaxResult.success("更新成功");
    }
}
