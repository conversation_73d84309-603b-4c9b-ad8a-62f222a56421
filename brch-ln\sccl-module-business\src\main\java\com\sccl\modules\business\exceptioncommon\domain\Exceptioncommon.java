package com.sccl.modules.business.exceptioncommon.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;


/**
 * 公共异常表 public_exceptioncommon
 * 
 * <AUTHOR>
 * @date 2023-03-22
 */
public class Exceptioncommon extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
    /** 基站属性 1自有/2三方/3铁塔  powercategory type_category=baseStationType */
    private String basestationType;
    /** 异常来源 1基础数据/2集团下发/3省内大数据/4铁塔对账/5一站式稽核 powercategory type_category=exceptionSource  */
    private String exceptionSource;
    /** 异常类型 */
    private String exceptiontype;
    /** 1未处理/2已处理/ */
    private String handleresult;
    /** 所属地市名称 */
    private String cityname;
    /** 所属地市编码 */
    private String citycode;
    /** 所属单位名称 */
    private String companyname;
    /** 所属单位编码 */
    private String companycode;
    /** 局站名称 */
    private String stationname;
    /** 局站编码 */
    private String stationcode;
    /** 电表id */
    private String ammeterId;
    /** 报账单id */
    private String billId;
    /** 创建时间 */
    private Date creationtime;
    /** 更新时间 */
    private Date updatetime;
    /** 流程id */
    private String processid;


	public void setBasestationType(String basestationType)
	{
		this.basestationType = basestationType;
	}

	public String getBasestationType() 
	{
		return basestationType;
	}

	public void setExceptionSource(String exceptionSource)
	{
		this.exceptionSource = exceptionSource;
	}

	public String getExceptionSource() 
	{
		return exceptionSource;
	}

	public void setExceptiontype(String exceptiontype)
	{
		this.exceptiontype = exceptiontype;
	}

	public String getExceptiontype() 
	{
		return exceptiontype;
	}

	public void setHandleresult(String handleresult)
	{
		this.handleresult = handleresult;
	}

	public String getHandleresult() 
	{
		return handleresult;
	}

	public void setCityname(String cityname)
	{
		this.cityname = cityname;
	}

	public String getCityname() 
	{
		return cityname;
	}

	public void setCitycode(String citycode)
	{
		this.citycode = citycode;
	}

	public String getCitycode() 
	{
		return citycode;
	}

	public void setCompanyname(String companyname)
	{
		this.companyname = companyname;
	}

	public String getCompanyname() 
	{
		return companyname;
	}

	public void setCompanycode(String companycode)
	{
		this.companycode = companycode;
	}

	public String getCompanycode() 
	{
		return companycode;
	}

	public void setStationname(String stationname)
	{
		this.stationname = stationname;
	}

	public String getStationname() 
	{
		return stationname;
	}

	public void setStationcode(String stationcode)
	{
		this.stationcode = stationcode;
	}

	public String getStationcode() 
	{
		return stationcode;
	}

	public void setAmmeterId(String ammeterId)
	{
		this.ammeterId = ammeterId;
	}

	public String getAmmeterId() 
	{
		return ammeterId;
	}

	public void setBillId(String billId)
	{
		this.billId = billId;
	}

	public String getBillId() 
	{
		return billId;
	}

	public void setCreationtime(Date creationtime)
	{
		this.creationtime = creationtime;
	}

	public Date getCreationtime() 
	{
		return creationtime;
	}

	public void setUpdatetime(Date updatetime)
	{
		this.updatetime = updatetime;
	}

	public Date getUpdatetime() 
	{
		return updatetime;
	}


	public void setProcessid(String processid)
	{
		this.processid = processid;
	}

	public String getProcessid() 
	{
		return processid;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("basestationType", getBasestationType())
            .append("exceptionSource", getExceptionSource())
            .append("exceptiontype", getExceptiontype())
            .append("handleresult", getHandleresult())
            .append("cityname", getCityname())
            .append("citycode", getCitycode())
            .append("companyname", getCompanyname())
            .append("companycode", getCompanycode())
            .append("stationname", getStationname())
            .append("stationcode", getStationcode())
            .append("ammeterId", getAmmeterId())
            .append("billId", getBillId())
            .append("creationtime", getCreationtime())
            .append("updatetime", getUpdatetime())
            .append("delFlag", getDelFlag())
            .append("processid", getProcessid())
            .toString();
    }
}
