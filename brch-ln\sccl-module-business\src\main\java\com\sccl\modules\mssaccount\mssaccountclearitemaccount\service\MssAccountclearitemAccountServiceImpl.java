package com.sccl.modules.mssaccount.mssaccountclearitemaccount.service;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.mssaccount.mssaccountclearitemaccount.mapper.MssAccountclearitemAccountMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sccl.modules.mssaccount.mssaccountclearitemaccount.domain.MssAccountclearitemAccount;

import java.util.List;


/**
 * 预付冲销 挑对台账 服务层实现
 *
 * <AUTHOR>
 * @date 2019-11-25
 */
@Service
public class MssAccountclearitemAccountServiceImpl extends BaseServiceImpl<MssAccountclearitemAccount> implements IMssAccountclearitemAccountService {
    @Autowired
    MssAccountclearitemAccountMapper mssAccountclearitemAccountMapper;

    @Override
    public List<MssAccountclearitemAccount> listBybillId(Long id) {
        return mssAccountclearitemAccountMapper.listBybillId(id);
    }

    @Override
    public List<MssAccountclearitemAccount> listBypcids(String[] toStrArray) {
        return mssAccountclearitemAccountMapper.listBypcids(toStrArray);
    }

    @Override
    public List<MssAccountclearitemAccount> listBybillIds(String[] toStrArray) {
        return mssAccountclearitemAccountMapper.listBybillIds(toStrArray);
    }

}
