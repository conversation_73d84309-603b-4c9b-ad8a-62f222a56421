package com.sccl.modules.business.powerintelligentinf2.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.math.BigDecimal;
import java.util.Date;


/**
 * PUE管控详情表 power_intelligent_relate
 * 
 * <AUTHOR>
 * @date 2019-07-18
 */
public class PowerIntelligentRelate
{
	private static final long serialVersionUID = 1L;
	
    /** id */
    private Long detaiid;
    /** 电表id */
    private Long ammeterid;
    /** power_intellingent_info2中的id */
    private Long pinid;
    /** 百分比 */
    private BigDecimal percent;
    /** 录入时间 */
    private Date intputdate;
    /** 状态 */
    private String status;
    /** 发送时间 */
    private Date senddate;
    /** 上月电量 */
    private BigDecimal lastmonthelectricity;
    /** 本月电量 */
    private BigDecimal monthelectricity;

	public BigDecimal getLastmonthelectricity() {
		return lastmonthelectricity;
	}

	public void setLastmonthelectricity(BigDecimal lastmonthelectricity) {
		this.lastmonthelectricity = lastmonthelectricity;
	}

	public BigDecimal getMonthelectricity() {
		return monthelectricity;
	}

	public void setMonthelectricity(BigDecimal monthelectricity) {
		this.monthelectricity = monthelectricity;
	}

	public Long getDetaiid() {
		return detaiid;
	}

	public void setDetaiid(Long detaiid) {
		this.detaiid = detaiid;
	}

	public void setAmmeterid(Long ammeterid)
	{
		this.ammeterid = ammeterid;
	}

	public Long getAmmeterid() 
	{
		return ammeterid;
	}

	public void setPinid(Long pinid)
	{
		this.pinid = pinid;
	}

	public Long getPinid() 
	{
		return pinid;
	}

	public void setPercent(BigDecimal percent)
	{
		this.percent = percent;
	}

	public BigDecimal getPercent() 
	{
		return percent;
	}

	public void setIntputdate(Date intputdate)
	{
		this.intputdate = intputdate;
	}

	public Date getIntputdate() 
	{
		return intputdate;
	}

	public void setStatus(String status)
	{
		this.status = status;
	}

	public String getStatus() 
	{
		return status;
	}

	public void setSenddate(Date senddate)
	{
		this.senddate = senddate;
	}

	public Date getSenddate() 
	{
		return senddate;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("detaiid", getDetaiid())
            .append("ammeterid", getAmmeterid())
            .append("pinid", getPinid())
            .append("percent", getPercent())
            .append("intputdate", getIntputdate())
            .append("status", getStatus())
            .append("senddate", getSenddate())
            .append("lastmonthelectricity", getLastmonthelectricity())
            .append("monthelectricity", getMonthelectricity())
            .toString();
    }
	public PowerIntelligentRelate() {}
	public PowerIntelligentRelate(Long id, Long pinid, Long ammeterId, BigDecimal percent,BigDecimal lastmonthelectricity,BigDecimal monthelectricity) {
		this.setDetaiid(id);
		this.pinid = pinid;
		this.ammeterid = ammeterId;
		this.percent = percent;
		this.lastmonthelectricity = lastmonthelectricity;
		this.monthelectricity = monthelectricity;
	}
}
