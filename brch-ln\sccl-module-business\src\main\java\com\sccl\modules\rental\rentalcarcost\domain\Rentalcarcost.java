package com.sccl.modules.rental.rentalcarcost.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;
import java.math.BigDecimal;


/**
 * 租赁费用
 * 
 * <AUTHOR>
 * @date 2019-08-28
 */
public class Rentalcarcost extends BaseEntity
{
	private static final long serialVersionUID = 1L;

	private Long rccid;

	public Long getRccid() {
		return rccid;
	}

	public void setRccid(Long rccid) {
		this.rccid = rccid;
	}

	/** 供应商编号 */
    private String lifnr;
    /**  车型 */
    private BigDecimal modelid;
    /** 车牌号 */
    private String vin;
    /** 交车时间 */
    private Date handovertime;
    /** 月租金 */
    private BigDecimal rentmonthly;
    /** 日租金 */
    private BigDecimal dailyrent;
    /** 调整 */
    private BigDecimal adjustment;
    /** 总金额 */
    private BigDecimal totalamount;
    /**  */
    private Long inputuserid;
    /**  */
    private Date inputdate;
    /**  */
    private String inputusername;
    /**  */
    private BigDecimal iprocessinstid;
    /**  */
    private String status;
    /**  */
    private Long company;
    /**  */
    private Long country;
    /**  */
    private String memo;
    /** mainid */
    private Long rcmid;
    /** 主表所选对应季度第一个月租金 */
    private BigDecimal onerentmonthly;
	/** 主表所选对应季度第二个月租金 */
    private BigDecimal tworentmonthly;
	/** 主表所选对应季度第三个月租金 */
    private BigDecimal threerentmonthly;
	private String modelname;
	private String lifnrname;

	public String getModelname() {
		return modelname;
	}

	public void setModelname(String modelname) {
		this.modelname = modelname;
	}

	public String getLifnrname() {
		return lifnrname;
	}

	public void setLifnrname(String lifnrname) {
		this.lifnrname = lifnrname;
	}

	public BigDecimal getOnerentmonthly() {
		return onerentmonthly;
	}

	public void setOnerentmonthly(BigDecimal onerentmonthly) {
		this.onerentmonthly = onerentmonthly;
	}

	public BigDecimal getTworentmonthly() {
		return tworentmonthly;
	}

	public void setTworentmonthly(BigDecimal tworentmonthly) {
		this.tworentmonthly = tworentmonthly;
	}

	public BigDecimal getThreerentmonthly() {
		return threerentmonthly;
	}

	public void setThreerentmonthly(BigDecimal threerentmonthly) {
		this.threerentmonthly = threerentmonthly;
	}

	public void setLifnr(String lifnr)
	{
		this.lifnr = lifnr;
	}

	public String getLifnr() 
	{
		return lifnr;
	}

	public void setModelid(BigDecimal modelid)
	{
		this.modelid = modelid;
	}

	public BigDecimal getModelid() 
	{
		return modelid;
	}

	public void setVin(String vin)
	{
		this.vin = vin;
	}

	public String getVin() 
	{
		return vin;
	}

	public void setHandovertime(Date handovertime)
	{
		this.handovertime = handovertime;
	}

	public Date getHandovertime() 
	{
		return handovertime;
	}

	public void setRentmonthly(BigDecimal rentmonthly)
	{
		this.rentmonthly = rentmonthly;
	}

	public BigDecimal getRentmonthly() 
	{
		return rentmonthly;
	}

	public void setDailyrent(BigDecimal dailyrent)
	{
		this.dailyrent = dailyrent;
	}

	public BigDecimal getDailyrent() 
	{
		return dailyrent;
	}

	public void setAdjustment(BigDecimal adjustment)
	{
		this.adjustment = adjustment;
	}

	public BigDecimal getAdjustment() 
	{
		return adjustment;
	}

	public void setTotalamount(BigDecimal totalamount)
	{
		this.totalamount = totalamount;
	}

	public BigDecimal getTotalamount() 
	{
		return totalamount;
	}


	public void setInputuserid(Long inputuserid)
	{
		this.inputuserid = inputuserid;
	}

	public Long getInputuserid()
	{
		return inputuserid;
	}

	public void setInputdate(Date inputdate)
	{
		this.inputdate = inputdate;
	}

	public Date getInputdate() 
	{
		return inputdate;
	}

	public void setInputusername(String inputusername)
	{
		this.inputusername = inputusername;
	}

	public String getInputusername() 
	{
		return inputusername;
	}

	public void setIprocessinstid(BigDecimal iprocessinstid)
	{
		this.iprocessinstid = iprocessinstid;
	}

	public BigDecimal getIprocessinstid() 
	{
		return iprocessinstid;
	}

	public void setStatus(String status)
	{
		this.status = status;
	}

	public String getStatus() 
	{
		return status;
	}

	public void setCompany(Long company)
	{
		this.company = company;
	}

	public Long getCompany()
	{
		return company;
	}

	public void setCountry(Long country)
	{
		this.country = country;
	}

	public Long getCountry()
	{
		return country;
	}

	public void setMemo(String memo)
	{
		this.memo = memo;
	}

	public String getMemo() 
	{
		return memo;
	}

	public void setRcmid(Long rcmid)
	{
		this.rcmid = rcmid;
	}

	public Long getRcmid()
	{
		return rcmid;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("rccid", getRccid())
            .append("lifnr", getLifnr())
            .append("modelid", getModelid())
            .append("vin", getVin())
            .append("handovertime", getHandovertime())
            .append("rentmonthly", getRentmonthly())
            .append("dailyrent", getDailyrent())
            .append("adjustment", getAdjustment())
            .append("totalamount", getTotalamount())
            .append("remark", getRemark())
            .append("inputuserid", getInputuserid())
            .append("inputdate", getInputdate())
            .append("inputusername", getInputusername())
            .append("iprocessinstid", getIprocessinstid())
            .append("status", getStatus())
            .append("company", getCompany())
            .append("country", getCountry())
            .append("memo", getMemo())
            .append("rcmid", getRcmid())
            .toString();
    }
}
