package com.sccl.modules.mssaccount.mssinterface.domain;

import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 修复超出报账电量请求DTO
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Data
public class SyncCollectMeterWithActualRequest implements Serializable {

    /**
     * 期号
     */
    private String periodNumber;

    /**
     * 报账单编码
     */
    private String writeoffInstanceCode;

    /**
     * 局站编码
     */
    private String stationCode;

    /**
     * 业务电量（包含90天外数据）
     */
    private BigDecimal businessElectricity;

    /**
     * 参数验证
     * 
     * @return 验证结果消息，null表示验证通过
     */
    public String validate() {
        if (periodNumber == null || periodNumber.trim().isEmpty()) {
            return "期号不能为空";
        }
        
        if (writeoffInstanceCode == null || writeoffInstanceCode.trim().isEmpty()) {
            return "报账单编码不能为空";
        }
        
        if (stationCode == null || stationCode.trim().isEmpty()) {
            return "局站编码不能为空";
        }
        
        if (businessElectricity == null) {
            return "业务电量不能为空";
        }
        
        if (businessElectricity.compareTo(BigDecimal.ZERO) < 0) {
            return "业务电量不能为负数";
        }
        
        return null; // 验证通过
    }

    @Override
    public String toString() {
        return String.format("SyncCollectMeterWithActualRequest{periodNumber='%s', writeoffInstanceCode='%s', stationCode='%s', businessElectricity=%s}",
                periodNumber, writeoffInstanceCode, stationCode, businessElectricity);
    }
}
