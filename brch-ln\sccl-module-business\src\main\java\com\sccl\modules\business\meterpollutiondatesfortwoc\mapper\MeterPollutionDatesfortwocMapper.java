package com.sccl.modules.business.meterpollutiondatesfortwoc.mapper;

import com.sccl.modules.business.meterpollutiondatesfortwoc.domain.MeterPollutionDatesfortwoc;
import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.twoc.domain.TwoCFlag;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 双碳接口废水废气数据 数据层
 * 
 * <AUTHOR>
 * @date 2023-04-17
 */
public interface MeterPollutionDatesfortwocMapper extends BaseMapper<MeterPollutionDatesfortwoc>
{


    Integer countForMeterinfoAllFail();


    List<? extends TwoCFlag> selectMeterInfo(@Param("id") Long id,@Param("offset") int offset,
                                             @Param("size") int syncsum);

    int updateForMeterInfoAll(MeterPollutionDatesfortwoc infoDb);
}