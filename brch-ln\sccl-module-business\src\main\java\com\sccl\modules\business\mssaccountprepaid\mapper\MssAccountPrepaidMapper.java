package com.sccl.modules.business.mssaccountprepaid.mapper;

import com.sccl.modules.business.mssaccountprepaid.domain.MssAccountPrepaid;
import com.sccl.framework.mapper.BaseMapper;

import java.math.BigDecimal;

/**
 * 关联交易客户代垫及收款 数据层
 * 
 * <AUTHOR>
 * @date 2021-10-24
 */
public interface MssAccountPrepaidMapper extends BaseMapper<MssAccountPrepaid>
{
 public BigDecimal computeBalance(MssAccountPrepaid mssAccountPrepaid);
 public BigDecimal computeBalanceorg(MssAccountPrepaid mssAccountPrepaid);
 String ifBalanceorg(MssAccountPrepaid mssAccountPrepaid);
	
}