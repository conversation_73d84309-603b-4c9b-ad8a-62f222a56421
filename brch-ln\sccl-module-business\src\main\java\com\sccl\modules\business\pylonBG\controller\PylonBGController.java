package com.sccl.modules.business.pylonBG.controller;

import com.sccl.common.support.Convert;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.account.domain.Account;
import com.sccl.modules.business.account.domain.AccountBaseResult;
import com.sccl.modules.business.account.domain.AccountCondition;
import com.sccl.modules.business.accountbillitempre.domain.Accountbillitempre;
import com.sccl.modules.business.pylonBG.service.IPylonBGService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * __铁塔包干__<br/>
 * 2019/7/18
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/business/pylonbg")
public class PylonBGController extends BaseController {
    private String prefix = "business/pylonbg";

    @RequiresPermissions("business:pylonbg:view")
    @GetMapping()
    public String account()
    {
        return prefix + "/pylonbg";
    }

    @Autowired
    IPylonBGService pylonBGService;

    /**
     * @Description: 铁塔包干列表
     * @author: dongk
     * @date: 2019/7/18
     * @param:
     * @return:
     */
    @RequiresPermissions("business:pylonbg:list")
    @RequestMapping("/selectByParams")
    @ResponseBody
    public TableDataInfo selectByParams(AccountCondition condition)
    {
        startPage();
        List<AccountBaseResult> list = pylonBGService.selectByParams(condition);
        return getDataTable(list);

    }

    //@Log(title = "铁塔包干台账", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public Map<String,Object> edit(@RequestBody List<Account> accountList)
    {
        return pylonBGService.updateData(accountList);
    }

    //@Log(title = "台账", action = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(@RequestBody HashMap<String, String> map)
    {
        return this.success(pylonBGService.deleteAccountByIds(Convert.toStrArray(map.get("ids"))));
    }

    //@Log(title = "铁塔包干台账查询单价", action = BusinessType.DELETE)
    @RequestMapping( "/selectlumpprice")
    @ResponseBody
    public AccountBaseResult selectlumpprice(@RequestBody Account account)
    {
        AccountBaseResult result = new AccountBaseResult();
        result.setStartdate(account.getStartdate());
        result.setEnddate(account.getEnddate());
        pylonBGService.selectlumpprice(account.getCountry(),account.getCompany(),result);

        return result;
    }

    /**
     * @Description: 铁塔包干台账总合计
     * @author: dongk
     * @date: 2019/7/23
     * @param:
     * @return:
     */
    @RequestMapping("/accountTotal")
    @ResponseBody
    public AjaxResult accountTotal(@RequestBody AccountCondition condition)
    {
        AccountBaseResult result = pylonBGService.accountTotal(condition);
        if(result != null){
            return this.success(result);
        }else {
            return this.success(new AccountBaseResult());
        }
    }

    /**
     * @Description: 根据归集单id查台账
     * @author: dongk
     * @date: 2019/7/23
     * @param:
     * @return:
     */
    @RequestMapping("/selectListByPre")
    @ResponseBody
    public TableDataInfo selectListByPre(Accountbillitempre accountbillitempre)
    {
        startPage();
        List<AccountBaseResult> list = pylonBGService.selectListByPre(accountbillitempre);
        return getDataTable(list);

    }

    /**
     * @Description: 包干查询功能列表
     * @author: dongk
     * @date: 2019/7/24
     * @param:
     * @return:
     */
    @RequiresPermissions("business:pylonbgquery:list")
    @RequestMapping("/selectQuery")
    @ResponseBody
    public TableDataInfo selectQuery(AccountCondition condition)
    {
        startPage();
        List<AccountBaseResult> list = pylonBGService.selectQuery(condition);
        return getDataTable(list);

    }

    /**
     * @Description: 铁塔包干台账合计
     * @author: dongk
     * @date: 2019/7/25
     * @param:
     * @return:
     */
    @RequestMapping("/queryTotal")
    @ResponseBody
    public AjaxResult queryTotal(@RequestBody AccountCondition condition)
    {
        AccountBaseResult result = pylonBGService.queryTotal(condition);
        if(result != null){
            return this.success(result);
        }else {
            return this.success(new AccountBaseResult());
        }
    }

}
