package com.sccl.modules.business.stationinfo.domain;

import com.sccl.framework.web.domain.BaseEntity;
import com.sccl.modules.business.powerauditstaiongrade.util.ExcelExport;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;


/**
 * 集团LTE和局站对应表 power_station_info_r_jtlte
 *
 * <AUTHOR>
 * @date 2021-05-13
 */
@Data
public class PowerStationInfoRJtlte extends BaseEntity implements ExcelExport {
	private static final long serialVersionUID = 1L;

	/**
	 * 局站id
	 */
	private Long stationid;
	/**
	 * 集团LTE站址编码
	 */
	private String jtlteCode;
	/**
	 * 集团LTE站址名称
	 */
	private String jtlteName;
	/**
	 * 集团LTE铁塔站址
	 */
	private String jtlteTacode;
	/**
	 * 集团LTE铁塔站址名称
	 */
	private String jtlteTaname;
	/**
	 * 关联时间
	 */
	private Date updatetime;
	private String updatetimePro;
	/**
	 * 关联用户
	 */
	private Long updateuserid;
	/**
	 * 一表多站
	 */
	private String mutiJtlteCodes;
	/**
	 * 一表对局站
	 */
	private String mutiStationids;
	/**
	 * 状态
	 */
	private String status;
	/**
	 * 报账单id
	 */
	private Long billId;
	private String sendstatus;
	private Long jtid;
	/**
	 * 前端查询字段
	 *
	 * @return
	 */
	private String company;
	private String country;
	private Integer pageSize;
	private Integer pageNum;
	private Integer offset;
	/**
	 * 电表编码
	 */
	private String ammetername;
	/**
	 * 账期
	 */
	private String budget;

	public String getSendstatus() {
		return sendstatus;
	}

	public void setSendstatus(String sendstatus) {
		this.sendstatus = sendstatus;
	}

	public Long getJtid() {
		return jtid;
	}

	public void setJtid(Long jtid) {
		this.jtid = jtid;
	}

	public Long getStationid() {
		return stationid;
	}

	public void setStationid(Long stationid) {
		this.stationid = stationid;
	}

	public String getJtlteCode() {
		return jtlteCode;
	}

	public void setJtlteCode(String jtlteCode) {
		this.jtlteCode = jtlteCode;
	}

	public String getJtlteName() {
		return jtlteName;
	}

	public void setJtlteName(String jtlteName) {
		this.jtlteName = jtlteName;
	}

	public String getJtlteTacode() {
		return jtlteTacode;
	}

	public void setJtlteTacode(String jtlteTacode) {
		this.jtlteTacode = jtlteTacode;
	}

	public String getJtlteTaname() {
		return jtlteTaname;
	}

	public void setJtlteTaname(String jtlteTaname) {
		this.jtlteTaname = jtlteTaname;
	}

	public Date getUpdatetime() {
		return updatetime;
	}

	public void setUpdatetime(Date updatetime) {
		this.updatetime = updatetime;
	}

	public Long getUpdateuserid() {
		return updateuserid;
	}

	public void setUpdateuserid(Long updateuserid) {
		this.updateuserid = updateuserid;
	}

	public String getMutiJtlteCodes() {
		return mutiJtlteCodes;
	}

	public void setMutiJtlteCodes(String mutiJtlteCodes) {
		this.mutiJtlteCodes = mutiJtlteCodes;
	}

	public String getMutiStationids() {
		return mutiStationids;
	}

	public void setMutiStationids(String mutiStationids) {
		this.mutiStationids = mutiStationids;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Long getBillId() {
		return billId;
	}

	public void setBillId(Long billId) {
		this.billId = billId;
	}

	@Override
	public String toString() {
		return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
				.append("id", getId())
				.append("stationid", getStationid())
				.append("jtlteCode", getJtlteCode())
				.append("jtlteName", getJtlteName())
				.append("jtlteTacode", getJtlteTacode())
				.append("jtlteTaname", getJtlteTaname())
				.append("updatetime", getUpdatetime())
				.append("updateuserid", getUpdateuserid())
				.append("mutiJtlteCodes", getMutiJtlteCodes())
				.append("mutiStationids", getMutiStationids())
				.append("status", getStatus())
				.append("billId", getBillId())
				.toString();
	}
}
