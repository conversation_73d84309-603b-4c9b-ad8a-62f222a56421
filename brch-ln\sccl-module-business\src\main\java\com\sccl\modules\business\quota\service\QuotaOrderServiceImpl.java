package com.sccl.modules.business.quota.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.sccl.common.lang.StringUtils;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.business.order.domain.Order;
import com.sccl.modules.business.order.service.IOrderService;
import com.sccl.modules.business.quota.domain.Quota;
import com.sccl.modules.business.quota.domain.QuotaRecord;
import com.sccl.modules.business.quota.mapper.QuotaMapper;
import com.sccl.modules.business.quota.mapper.QuotaRecordMapper;
import com.sccl.modules.uniflow.common.WFModel;


/**
 * 订单 服务层实现 报账单流程回调
 *
 * <AUTHOR>
 * @date 2019-05-25
 */
@Service
public class QuotaOrderServiceImpl extends BaseServiceImpl<Order> implements IOrderService {
    private static final Logger logger = LoggerFactory.getLogger(QuotaOrderServiceImpl.class);
    @Autowired
    private QuotaRecordMapper quotaRecordMapper;
    @Autowired
    private QuotaMapper quotaMapper;

    /**
     * 流程引擎回调（通过MQ）
     *
     * @param wfModel 流程消息对象
     */
    @Override
    public void uniflowCallBack(WFModel wfModel) {
        System.out.println("-----------------lt:" + wfModel.toString());
        try{
            if(StringUtils.isNotEmpty(wfModel.getCallbackType()) && "PROCESS_STARTED".equals(wfModel.getCallbackType()) && wfModel.getVariables().containsKey("firstNode") && wfModel.getVariables().get("firstNode").equals(true)){
                if(wfModel.getBusiAlias().equals("ADD_QUOTA")){
                    this.updateStatus(wfModel.getBusiId(),1);//修改单据状态为流程中
                }else{
                    this.updateStatus(wfModel.getBusiId(),4);//修改单据状态为修改流程中
                }
            }else if(StringUtils.isNotEmpty(wfModel.getCallbackType()) && "PROCESS_COMPLETED".equals(wfModel.getCallbackType())){
                this.updateQuota(wfModel.getBusiId(),2,wfModel.getApplyUserId());//修改单据状态为已完成并更新数据
            }else if(StringUtils.isNotEmpty(wfModel.getCallbackType()) && "PROCESS_CANCELLED".equals(wfModel.getCallbackType())){
                //只有驳回才更改状态，不同意时还是在流程中
                Map<String,Object> params= new HashMap<>();
                params.put("id",wfModel.getBusiId());
                Quota quota = quotaMapper.selectByPrimaryKey(params);
                if(null != quota.getBillStatus()&& quota.getBillStatus() == 1){
                    this.updateStatus(wfModel.getBusiId(),0);//修改单据状态为草稿
                }else if(null != quota.getBillStatus()&& quota.getBillStatus() == 4){
                    this.updateStatus(wfModel.getBusiId(),3);//修改单据状态为修改中
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    private void updateStatus(String id,int billStatus) throws Exception{
        Map<String,Object> parms = new HashMap<>();
        parms.put("id",id);
        Quota quota = quotaMapper.selectByPrimaryKey(parms);
        if(null == quota){
            logger.error("没有找到对应的基础数据，或已被删除");
        }else{
            quota.setBillStatus(billStatus);
            quotaMapper.updateForModel(quota);
        }
    }

    private int updateQuota(String id,int billStatus,String applyUserId) throws Exception{
        QuotaRecord quotaRecord = new QuotaRecord();
        quotaRecord.setQuotaId(Long.parseLong(id));
        quotaRecord.setCreatorId(StringUtils.isNotEmpty(applyUserId)?Long.parseLong(applyUserId):null);
        List<QuotaRecord> lists = quotaRecordMapper.getByQuotaId(quotaRecord);
        if(null != lists && lists.size() != 0){
            quotaRecord = lists.get(0);
        }
        if (null == quotaRecord){
            logger.error("没有找到对应的记录数据，或已被删除");
            return 0;
        }
        //修改历史记录状态
        quotaRecord.setBillStatus(billStatus);
        quotaRecordMapper.updateForModel(quotaRecord);
        //删除无效的历史数据
        Map<String,Object> params = new HashMap<>();
        params.put("quotaId",quotaRecord.getQuotaId());
        quotaRecordMapper.deleteByDateDB(params);

        //保存定额记录
        Quota quota = new Quota();
        BeanUtils.copyProperties(quotaRecord, quota);
        quota.setBillStatus(billStatus);
        quota.setId(quotaRecord.getQuotaId());
        quota.setUpdateTime(quotaRecord.getCreateTime());
        quota.setUpdateById(StringUtils.isNotEmpty(applyUserId)?Long.parseLong(applyUserId):null);
        return quotaMapper.updateByPrimaryKey(quota);
    }
}
