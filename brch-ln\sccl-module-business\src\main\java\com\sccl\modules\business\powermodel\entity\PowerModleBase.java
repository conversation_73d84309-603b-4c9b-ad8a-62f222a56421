package com.sccl.modules.business.powermodel.entity;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 电量模型基本表(PowerModleBase)实体类
 *
 * <AUTHOR>
 * @since 2022-10-19 17:44:40
 */
@Data
public class PowerModleBase implements Serializable {
    private static final long serialVersionUID = 619559217654179625L;
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 购电途径
     */
    private Integer purtype;
    /**
     * 用电分类
     */
    private Integer purclass;
    /**
     * 电量计算方式
     */
    private Integer calmethod;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标记  0正常 / 1删除
     * @return
     */
    private Integer delFlag;




    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getPurtype() {
        return purtype;
    }

    public void setPurtype(Integer purtype) {
        this.purtype = purtype;
    }

    public Integer getPurclass() {
        return purclass;
    }

    public void setPurclass(Integer purclass) {
        this.purclass = purclass;
    }

    public Integer getCalmethod() {
        return calmethod;
    }

    public void setCalmethod(Integer calmethod) {
        this.calmethod = calmethod;
    }

}

