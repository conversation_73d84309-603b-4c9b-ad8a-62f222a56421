package com.sccl.modules.mssaccount.mssinterface.domain.dateprocessing;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 能耗分配结果
 * 包含重新分配后的各种能耗数据
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
public class EnergyDistribution {
    
    /**
     * 总能耗（重新分配后的每日值）
     */
    private final double energyData;
    
    /**
     * 市电电量（重新分配后的每日值）
     */
    private final double acData;
    
    /**
     * 油机发电电量（重新分配后的每日值）
     */
    private final double oepgData;
    
    /**
     * 光伏发电电量（重新分配后的每日值）
     */
    private final double pvpgData;
    
    /**
     * 设备能耗（重新分配后的每日值）
     */
    private final double deviceData;
    
    /**
     * 生产分摊能耗（重新分配后的每日值）
     */
    private final double productionData;
    
    /**
     * 管理分摊能耗（重新分配后的每日值）
     */
    private final double managementData;
    
    /**
     * 营业分摊能耗（重新分配后的每日值）
     */
    private final double businessData;
    
    /**
     * 其他分摊能耗（重新分配后的每日值）
     */
    private final double otherData;
    
    /**
     * 构造函数
     * 
     * @param energyData 总能耗
     * @param acData 市电电量
     * @param oepgData 油机发电电量
     * @param pvpgData 光伏发电电量
     * @param deviceData 设备能耗
     * @param productionData 生产分摊能耗
     * @param managementData 管理分摊能耗
     * @param businessData 营业分摊能耗
     * @param otherData 其他分摊能耗
     */
    public EnergyDistribution(double energyData, double acData, double oepgData, double pvpgData,
                            double deviceData, double productionData, double managementData,
                            double businessData, double otherData) {
        this.energyData = validateAndRound(energyData);
        this.acData = validateAndRound(acData);
        this.oepgData = validateAndRound(oepgData);
        this.pvpgData = validateAndRound(pvpgData);
        this.deviceData = validateAndRound(deviceData);
        this.productionData = validateAndRound(productionData);
        this.managementData = validateAndRound(managementData);
        this.businessData = validateAndRound(businessData);
        this.otherData = validateAndRound(otherData);
    }
    
    /**
     * 验证并四舍五入到2位小数
     * 
     * @param value 原始值
     * @return 处理后的值
     */
    private double validateAndRound(double value) {
        if (Double.isNaN(value) || Double.isInfinite(value)) {
            return 0.0;
        }
        if (value < 0) {
            return 0.0;
        }
        return BigDecimal.valueOf(value)
                .setScale(2, RoundingMode.HALF_UP)
                .doubleValue();
    }
    
    /**
     * 获取总能耗
     * 
     * @return 总能耗
     */
    public double getEnergyData() {
        return energyData;
    }
    
    /**
     * 获取市电电量
     * 
     * @return 市电电量
     */
    public double getAcData() {
        return acData;
    }
    
    /**
     * 获取油机发电电量
     * 
     * @return 油机发电电量
     */
    public double getOepgData() {
        return oepgData;
    }
    
    /**
     * 获取光伏发电电量
     * 
     * @return 光伏发电电量
     */
    public double getPvpgData() {
        return pvpgData;
    }
    
    /**
     * 获取设备能耗
     * 
     * @return 设备能耗
     */
    public double getDeviceData() {
        return deviceData;
    }
    
    /**
     * 获取生产分摊能耗
     * 
     * @return 生产分摊能耗
     */
    public double getProductionData() {
        return productionData;
    }
    
    /**
     * 获取管理分摊能耗
     * 
     * @return 管理分摊能耗
     */
    public double getManagementData() {
        return managementData;
    }
    
    /**
     * 获取营业分摊能耗
     * 
     * @return 营业分摊能耗
     */
    public double getBusinessData() {
        return businessData;
    }
    
    /**
     * 获取其他分摊能耗
     * 
     * @return 其他分摊能耗
     */
    public double getOtherData() {
        return otherData;
    }
    
    /**
     * 计算总的分摊能耗
     * 
     * @return 总分摊能耗
     */
    public double getTotalDistributedData() {
        return deviceData + productionData + managementData + businessData + otherData;
    }
    
    /**
     * 判断是否为零值分配
     * 
     * @return true如果所有值都为0，false否则
     */
    public boolean isZeroDistribution() {
        return energyData == 0.0 && acData == 0.0 && oepgData == 0.0 && pvpgData == 0.0 &&
               deviceData == 0.0 && productionData == 0.0 && managementData == 0.0 &&
               businessData == 0.0 && otherData == 0.0;
    }
    
    @Override
    public String toString() {
        return String.format("EnergyDistribution{energy=%.2f, ac=%.2f, oepg=%.2f, pvpg=%.2f, " +
                           "device=%.2f, production=%.2f, management=%.2f, business=%.2f, other=%.2f}",
                energyData, acData, oepgData, pvpgData, deviceData, 
                productionData, managementData, businessData, otherData);
    }
}
