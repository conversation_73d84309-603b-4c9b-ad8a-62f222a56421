package com.sccl.modules.business.stationreportwhitelist.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseMapper;
import com.sccl.modules.business.stationreportwhitelist.domain.PowerStationInfo;
import com.sccl.modules.business.stationreportwhitelist.dto.PowerStationInfoQuery;
import com.sccl.modules.business.stationreportwhitelist.vo.PowerStationInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MpPowerStationInfoMapper extends MPJBaseMapper<PowerStationInfo> {


    Page<PowerStationInfoVO> oneStopIsMoreThanOneWatch(Page<PowerStationInfoVO> page, @Param(Constants.WRAPPER) Wrapper<PowerStationInfoQuery> wrapper);
}
