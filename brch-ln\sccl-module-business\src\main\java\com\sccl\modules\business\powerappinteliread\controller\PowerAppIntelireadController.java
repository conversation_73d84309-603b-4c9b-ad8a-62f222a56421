package com.sccl.modules.business.powerappinteliread.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.modules.business.accountEs.domain.PowerAccountEs;
import com.sccl.modules.system.notice.domain.StationError;
import com.sccl.modules.system.organization.domain.Organization;
import com.sccl.modules.system.organization.mapper.OrganizationMapper;
import com.sccl.modules.system.organization.service.IOrganizationService;
import com.sccl.modules.system.role.domain.Role;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.service.IUserService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.business.powerappinteliread.domain.PowerAppInteliread;
import com.sccl.modules.business.powerappinteliread.service.IPowerAppIntelireadService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 远程抄记录 信息操作处理
 * 
 * <AUTHOR>
 * @date 2022-03-08
 */
@RestController
@RequestMapping("/business/powerAppInteliread")
public class PowerAppIntelireadController extends BaseController
{
    private String prefix = "business/powerAppInteliread";
	
	@Autowired
	private IPowerAppIntelireadService powerAppIntelireadService;
	@Autowired
	private IUserService userService;
	@Autowired
	private IOrganizationService organizationService;
	@Autowired
	private OrganizationMapper organizationMapper;
	@RequiresPermissions("business:powerAppInteliread:view")
	@GetMapping()
	public String powerAppInteliread()
	{
	    return prefix + "/powerAppInteliread";
	}
	
	/**
	 * 查询远程抄记录列表
	 */
	@RequiresPermissions("business:powerAppInteliread:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(PowerAppInteliread powerAppInteliread)
	{
		setParams(powerAppInteliread);
		startPage();
        List<PowerAppInteliread> list = powerAppIntelireadService.selectByList(powerAppInteliread);
		return getDataTable(list);
	}
	
	/**
	 * 新增远程抄记录
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存远程抄记录
	 */
	@RequiresPermissions("business:powerAppInteliread:add")
	@Log(title = "远程抄记录", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody List<PowerAccountEs> accountList )
	{		
		return toAjax(powerAppIntelireadService.insertAccount(accountList));
	}

	/**
	 * 修改远程抄记录
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		PowerAppInteliread powerAppInteliread = powerAppIntelireadService.get(id);

		Object object = JSONObject.toJSON(powerAppInteliread);

        return this.success(object);
	}
	
	/**
	 * 修改保存远程抄记录
	 */
	@RequiresPermissions("business:powerAppInteliread:edit")
	@Log(title = "远程抄记录", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody PowerAppInteliread powerAppInteliread)
	{		
		return toAjax(powerAppIntelireadService.update(powerAppInteliread));
	}
	
	/**
	 * 删除远程抄记录
	 */
	@RequiresPermissions("business:powerAppInteliread:remove")
	@Log(title = "远程抄记录", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(powerAppIntelireadService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看远程抄记录
     */
/*    @RequiresPermissions("business:powerAppInteliread:view")*/
    @GetMapping("/view")
    @ResponseBody
    public AjaxResult view(@RequestParam(required = false)   Long id)
    {

		PowerAppInteliread powerAppInteliread = powerAppIntelireadService.viewDetail(id);
        Object object = JSONObject.toJSON(powerAppInteliread);

        return this.success(object);
    }
	@RequestMapping("/listneed")
	@ResponseBody
	public TableDataInfo listneed(PowerAppInteliread powerAppInteliread)
	{
		setParams(powerAppInteliread);
		startPage();
		List<PowerAppInteliread> list = powerAppIntelireadService.selectByNeed(powerAppInteliread);
		return getDataTable(list);
	}


	@RequestMapping("/listDetail")
	@ResponseBody
	public TableDataInfo listDetail(PowerAppInteliread powerAppInteliread)
	{
		setParams(powerAppInteliread);
		startPage();
		List<PowerAppInteliread> list = powerAppIntelireadService.listDetail(powerAppInteliread);
		return getDataTable(list);
	}
	private void setParams(PowerAppInteliread powerAppInteliread) {
		User user = ShiroUtils.getUser();
		List<Organization> listOrg = new ArrayList<>();
		List<Role> roles = userService.selectUserRole(user.getId());
		boolean isProAdmin = false;
		boolean isCityAdmin = false;
		boolean isSubAdmin = false;
		for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
			if (role.getCode().startsWith("PROVI_")) {//省能耗费管理员
				isProAdmin = true;
			}
			if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
				isCityAdmin = true;
			}
			if (role.getCode().startsWith("SUB_")) {//县能耗费管理员
				isSubAdmin = true;
			}
		}
		if (isProAdmin) {//  查询权限设置 分公司
		} else if (isCityAdmin) {
			List<IdNameVO> companies = user.getCompanies();
			if (companies != null && companies.size() > 0)
				powerAppInteliread.setCompany(Long.parseLong(companies.get(0).getId()));
		} else if (isSubAdmin) {
			List<IdNameVO> departments = user.getDepartments();
			List<IdNameVO> companies = user.getCompanies();
			if (companies != null && companies.size() > 0)
				powerAppInteliread.setCompany(Long.parseLong(companies.get(0).getId()));
			//List<Map<String, Object>> countrys = organizationService.selectByUserId(user.getId());
			listOrg  = this.organizationMapper.selectDepByUserId(user.getId().toString());
			if (listOrg != null && listOrg.size() > 0) {
				powerAppInteliread.setCountrys(listOrg);
			} else {
				if (departments != null && departments.size() > 0)
					powerAppInteliread.setCountry(Long.parseLong(departments.get(0).getId()));
			}
		}
	}

	/**
	 * 修改数据前验证台账
	 *
	 * @param id 电表协议id
	 * @return
	 */
	@RequestMapping("/insertireaddata")
	@ResponseBody
	public AjaxResult insertireaddata(PowerAppInteliread powerAppInteliread) {
		int i = 0;
		try {
			i = powerAppIntelireadService.insertireaddata(powerAppInteliread);
          if (i>0)

		return this.success("加入(" + i + ")条");
          else
		return this.success("已加入，在抄表列表处理");
	} catch (Exception e) {
	e.printStackTrace();
	return this.error(1, "加入失败:" + e.getMessage());
}
	}
}
