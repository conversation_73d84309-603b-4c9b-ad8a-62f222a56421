package com.sccl.modules.business.stationaudit.powerhistory;

import com.enrising.dcarbon.audit.AbstractReferee;
import com.enrising.dcarbon.audit.Auditable;
import com.enrising.dcarbon.audit.RefereeDatasource;
import com.enrising.dcarbon.audit.RefereeResult;
import com.sccl.modules.business.stationaudit.msshistory.HistoryResult;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;

import java.util.List;
import java.util.Map;

public class PowerHistoryReferee extends AbstractReferee {
    public PowerHistoryReferee() {
        super("powerHistory");
    }

    @Override
    public RefereeResult doReferee(RefereeResult lastRefereeResult, Auditable currentAuditBill, Map<Class<?
            extends RefereeDatasource>, List<RefereeDatasource>> refereeDatasourceList) {


        HistoryResult result = new HistoryResult();
        result.setRefereeMessage("过去6月电量历史已出");
        result.setTrue(true);
        result.setList(refereeDatasourceList.get(PowerHistory.class));
        return result;
    }

}
