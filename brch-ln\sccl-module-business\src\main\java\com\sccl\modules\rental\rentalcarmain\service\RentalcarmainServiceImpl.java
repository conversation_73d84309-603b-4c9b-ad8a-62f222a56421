package com.sccl.modules.rental.rentalcarmain.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.common.exception.base.BaseException;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.service.IdGenerator;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.rental.rentalcar.domain.Rentalcar;
import com.sccl.modules.rental.rentalcar.mapper.RentalcarMapper;
import com.sccl.modules.rental.rentalcarmain.domain.Rentalcarmain;
import com.sccl.modules.rental.rentalcarmain.mapper.RentalcarmainMapper;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.uniflow.common.WFModel;


/**
 * 车辆 所属 租赁项目 服务层实现
 *
 * <AUTHOR>
 * @date 2019-08-23
 */
@Service
public class RentalcarmainServiceImpl extends BaseServiceImpl<Rentalcarmain> implements IRentalcarmainService {

    private static final Logger logger = LoggerFactory.getLogger(RentalcarmainServiceImpl.class);


    @Autowired
    RentalcarmainMapper rentalcarmainMapper;
    @Autowired
    RentalcarMapper rentalcarMapper;

    @Override
    public List<Rentalcarmain> selectListByIds(String[] toStrArray) {
        return rentalcarmainMapper.selectListByIds(toStrArray);
    }

    @Override
    public AjaxResult saveRentalcarmain(Rentalcarmain rentalcarmain) {
        AjaxResult rs = new AjaxResult();
        if (rentalcarmain.getRcmid() == null) {
            insertRentalcarmain(rentalcarmain, rs);
        } else {
            updateRentalcarmain(rentalcarmain, rs);
        }
        return rs;
    }

    private void insertRentalcarmain(Rentalcarmain rentalcarmain, AjaxResult rs) {
        long nextId = IdGenerator.getNextId();
        User user = ShiroUtils.getUser();
        Long userid = user.getId();
        String company = user.getCompanies().get(0).getId();
        String country = user.getDepartments().get(0).getId();
        String name = user.getUserName();
        Date date = new Date();
        rentalcarmain.setRcmid(nextId);
        rentalcarmain.setInputusername(name);
        rentalcarmain.setInputuserid(userid);
        rentalcarmain.setCompany(company);
        rentalcarmain.setCountry(country);
        rentalcarmain.setInputdate(date);
        rentalcarmain.setStatus("1");
        rentalcarmainMapper.insert(rentalcarmain);
        List<Rentalcar> rentalcarList = rentalcarmain.getRentalcarList();
        for (Rentalcar m : rentalcarList) {
            m.setRcmid(nextId);
            m.setRcid(IdGenerator.getNextId());
            m.setInputdate(new Date());
            m.setInputuserid(userid);
            m.setInputusername(name);
            m.setCompany(company);
            m.setCountry(country);
            m.setStatus("1");
        }
        if (rentalcarList != null && rentalcarList.size() > 0)
            rentalcarMapper.insertList(rentalcarList);
        rentalcarmain.setRentalcarList(rentalcarList);
        rs.put("data", rentalcarmain);
    }

    private void updateRentalcarmain(Rentalcarmain rentalcarmain, AjaxResult rs) {
        rentalcarmainMapper.updateForModel(rentalcarmain);
        Long rcmid = rentalcarmain.getRcmid();
        User user = ShiroUtils.getUser();
        Long userid = user.getId();
        String company = user.getCompanies().get(0).getId();
        String country = user.getDepartments().get(0).getId();
        String name = user.getUserName();
        Date date = new Date();
        List<Rentalcar> rentalcarList = rentalcarmain.getRentalcarList();
        List<Rentalcar> newlist = new ArrayList<>();
        for (Rentalcar m : rentalcarList) {
            if (m.getRcid() == null) {
                m.setRcmid(rcmid);
                m.setRcid(IdGenerator.getNextId());
                m.setInputdate(date);
                m.setInputuserid(userid);
                m.setInputusername(name);
                m.setCompany(company);
                m.setCountry(country);
                m.setStatus("1");
                newlist.add(m);
            } else {
                rentalcarMapper.updateForModel(m);//更新旧的
            }
        }
        // 插入 新的
        if (newlist.size() > 0)
            rentalcarMapper.insertList(newlist);
        rentalcarmain.setRentalcarList(rentalcarList);
        rs.put("data", rentalcarmain);
    }

    @Override
    public int deleteAndItemByIds(String[] toStrArray) {
        rentalcarMapper.deleteByRcmids(toStrArray);
        return rentalcarmainMapper.deleteByIdsDB(toStrArray);
    }

    /**
     * 流程引擎回调（通过MQ）
     *
     * @param wfModel 流程消息对象
     */
//    @Override
    public void uniflowCallBack(WFModel wfModel) {
//        System.out.println("-----------------lt:" + wfModel.toString());
        logger.debug("-----------------lt:" + wfModel.toString());
        if ("PROCESS_STARTED".equals(wfModel.getCallbackType())) {//更新流程Id  流程 提交
            try {
//                草稿	1，代办	2,退回	3，删除	4,完成	5
                setStatus(wfModel, "2");
                logger.debug("车辆租赁信息录入审批新增" + wfModel.getBusiId());
            } catch (NumberFormatException e) {
                e.printStackTrace();
                logger.error("提交流程失败:" + e.getMessage());
                throw new BaseException("提交流程失败:" + e.getMessage());//
            }
        } else if ("PROCESS_COMPLETED".equals(wfModel.getCallbackType())) {//
//            流程完成后执行的回调  //                草稿	1，代办	2,退回	3，删除	4,完成	5
            setStatus(wfModel, "5");
        } else if ("TURNBACK_TO_START".equals(wfModel.getCallbackType())) {//流程 退回
            //                草稿	1，代办	2,退回	3，删除	4,完成	5
            setStatus(wfModel, "1");// 改为草稿 可重新提交
        } else if ("PROCESS_CANCELLED".equals(wfModel.getCallbackType())) {//流程 终止
            //                草稿	1，代办	2,退回	3，删除	4,完成	5
            setStatus(wfModel, "1");
        }
    }

    private void setStatus(WFModel wfModel, String status) {
        Rentalcarmain m = new Rentalcarmain();
        m.setIprocessinstid(wfModel.getProcInstId());
        m.setRcmid(Long.valueOf(wfModel.getBusiId()));
        m.setStatus(status);// 流程中
        rentalcarmainMapper.updateForModel(m);
        Long rcmid = m.getRcmid();
        List<Rentalcar> rentalcars = rentalcarMapper.selectAndNameByRcmid(rcmid);
        if (rentalcars != null && rentalcars.size() > 0) {
            for (Rentalcar c : rentalcars) {
                c.setStatus(status);
                rentalcarMapper.updateForModel(c);
            }
        }
    }
}
