package com.sccl.modules.mssaccount.msssupplier.service;

import com.sccl.modules.mssaccount.msssupplier.domain.MssSupplier;
import com.sccl.framework.service.IBaseService;
import org.apache.ibatis.annotations.Param;

/**
 * 供应商 服务层
 * 
 * <AUTHOR>
 * @date 2019-05-01
 */
public interface IMssSupplierService extends IBaseService<MssSupplier>
{

    /**
     * 根据供货商名称获取供货商信息
     * @param name 供货商名称
     */
    MssSupplier selectOneByName(@Param("name") String name);
}
