package com.sccl.modules.mssaccount.mssabccustomerbank.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.mssaccount.mssabccustomerbank.domain.MssAbccustomerBank;
import com.sccl.modules.mssaccount.mssabccustomerbank.service.IMssAbccustomerBankService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 客户银行 信息操作处理
 * 
 * <AUTHOR>
 * @date 2019-05-01
 */
@RestController
@RequestMapping("/mssaccount/mssAbccustomerBank")
public class MssAbccustomerBankController extends BaseController
{
    private String prefix = "mssaccount/mssAbccustomerBank";
	
	@Autowired
	private IMssAbccustomerBankService mssAbccustomerBankService;
	
	@RequiresPermissions("mssaccount:mssAbccustomerBank:view")
	@GetMapping()
	public String mssAbccustomerBank()
	{
	    return prefix + "/mssAbccustomerBank";
	}
	
	/**
	 * 查询客户银行列表
	 */
	@RequiresPermissions("mssaccount:mssAbccustomerBank:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(MssAbccustomerBank mssAbccustomerBank)
	{
		mssAbccustomerBank.setInfStatus("1");
		startPage();
        List<MssAbccustomerBank> list = mssAbccustomerBankService.selectList(mssAbccustomerBank);
		return getDataTable(list);
	}
	
	/**
	 * 新增客户银行
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存客户银行
	 */
	@RequiresPermissions("mssaccount:mssAbccustomerBank:add")
	//@Log(title = "客户银行", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody MssAbccustomerBank mssAbccustomerBank)
	{		
		return toAjax(mssAbccustomerBankService.insert(mssAbccustomerBank));
	}

	/**
	 * 修改客户银行
	 */
	@GetMapping("/edit/{iId}")
	public AjaxResult edit(@PathVariable("iId") Long iId)
	{
		MssAbccustomerBank mssAbccustomerBank = mssAbccustomerBankService.get(iId);

		Object object = JSONObject.toJSON(mssAbccustomerBank);

        return this.success(object);
	}
	
	/**
	 * 修改保存客户银行
	 */
	@RequiresPermissions("mssaccount:mssAbccustomerBank:edit")
	//@Log(title = "客户银行", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody MssAbccustomerBank mssAbccustomerBank)
	{		
		return toAjax(mssAbccustomerBankService.update(mssAbccustomerBank));
	}
	
	/**
	 * 删除客户银行
	 */
	@RequiresPermissions("mssaccount:mssAbccustomerBank:remove")
	//@Log(title = "客户银行", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(mssAbccustomerBankService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看客户银行
     */
    @RequiresPermissions("mssaccount:mssAbccustomerBank:view")
    @GetMapping("/view/{iId}")
    @ResponseBody
    public AjaxResult view(@PathVariable("iId") Long iId)
    {
		MssAbccustomerBank mssAbccustomerBank = mssAbccustomerBankService.get(iId);

        Object object = JSONObject.toJSON(mssAbccustomerBank);

        return this.success(object);
    }

	/**
	 * 查看客户银行
	 */
	@RequiresPermissions("mssaccount:mssAbccustomerBank:list")
	@PostMapping("/query")
	@ResponseBody
	public AjaxResult query(@RequestBody MssAbccustomerBank mssAbccustomerBank)
	{
		Object object = JSONObject.toJSON(mssAbccustomerBankService.selectList(mssAbccustomerBank));

		return this.success(object);
	}
}
