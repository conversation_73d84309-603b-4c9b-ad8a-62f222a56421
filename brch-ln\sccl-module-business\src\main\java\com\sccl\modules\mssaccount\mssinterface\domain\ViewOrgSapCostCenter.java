package com.sccl.modules.mssaccount.mssinterface.domain;

public class ViewOrgSapCostCenter {
    private String id;
    private String orgId;
    private String sapCostCenterID;
    private String provinceCode;
    private String name;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getSapCostCenterID() {
        return sapCostCenterID;
    }

    public void setSapCostCenterID(String sapCostCenterID) {
        this.sapCostCenterID = sapCostCenterID;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return "ViewOrgSapCostCenter{" +
                "id='" + id + '\'' +
                ", orgId='" + orgId + '\'' +
                ", sapCostCenterID='" + sapCostCenterID + '\'' +
                ", provinceCode='" + provinceCode + '\'' +
                ", name='" + name + '\'' +
                '}';
    }
}
