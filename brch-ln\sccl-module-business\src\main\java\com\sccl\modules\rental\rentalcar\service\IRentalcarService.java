package com.sccl.modules.rental.rentalcar.service;

import com.sccl.modules.rental.rentalcar.domain.Rentalcar;
import com.sccl.framework.service.IBaseService;

import java.util.List;

import java.math.BigDecimal;

/**
 * 车辆  服务层
 * 
 * <AUTHOR>
 * @date 2019-08-23
 */
public interface IRentalcarService extends IBaseService<Rentalcar>
{


    List<Rentalcar> selectAndNameByRcmid(Long rcmid);
    /**
     * @Description: 根据车牌号查月租金
     * @author: dongk
     * @date: 2019/9/4
     * @param:
     * @return:
     */
    Rentalcar selectByVin(Rentalcar vin);
}
