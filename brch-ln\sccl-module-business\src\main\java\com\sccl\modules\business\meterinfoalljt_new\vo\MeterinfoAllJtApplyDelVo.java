package com.sccl.modules.business.meterinfoalljt_new.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 删除/提交 保存对象
 */
@Data
public class MeterinfoAllJtApplyDelVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 在网表计数据清单申请表id（多个逗号隔开）
     */
    @NotBlank(message = "在网表计数据清单申请表id不能为空")
    private String ids;
}
