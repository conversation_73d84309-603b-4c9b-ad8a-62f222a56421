package com.sccl.modules.business.twoc.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.business.twoc.domain.Twoc;
import com.sccl.modules.business.twoc.service.ITwocService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 计量设备 信息操作处理
 * 
 * <AUTHOR>
 * @date 2023-04-13
 */
@RestController
@RequestMapping("/business/twoc")
public class Twoc<PERSON>ontroller extends BaseController
{
    private String prefix = "business/twoc";
	
	@Autowired
	private ITwocService twocService;
	
	@RequiresPermissions("business:twoc:view")
	@GetMapping()
	public String twoc()
	{
	    return prefix + "/twoc";
	}
	
	/**
	 * 查询计量设备列表
	 */
	@RequiresPermissions("business:twoc:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(Twoc twoc)
	{
		startPage();
        List<Twoc> list = twocService.selectList(twoc);
		return getDataTable(list);
	}
	
	/**
	 * 新增计量设备
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存计量设备
	 */
	@RequiresPermissions("business:twoc:add")
	@Log(title = "计量设备", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody Twoc twoc)
	{		
		return toAjax(twocService.insert(twoc));
	}

	/**
	 * 修改计量设备
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		Twoc twoc = twocService.get(id);

		Object object = JSONObject.toJSON(twoc);

        return this.success(object);
	}
	
	/**
	 * 修改保存计量设备
	 */
	@RequiresPermissions("business:twoc:edit")
	@Log(title = "计量设备", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody Twoc twoc)
	{		
		return toAjax(twocService.update(twoc));
	}
	
	/**
	 * 删除计量设备
	 */
	@RequiresPermissions("business:twoc:remove")
	@Log(title = "计量设备", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(twocService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看计量设备
     */
    @RequiresPermissions("business:twoc:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		Twoc twoc = twocService.get(id);

        Object object = JSONObject.toJSON(twoc);

        return this.success(object);
    }

}
