package com.sccl.modules.mssaccount.msssapinfomain.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.mssaccount.msssapinfomain.domain.MssSapinfomain;
import com.sccl.modules.mssaccount.msssapinfomain.service.IMssSapinfomainService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 报账回传 信息操作处理
 * 
 * <AUTHOR>
 * @date 2019-05-01
 */
@RestController
@RequestMapping("/mssaccount/mssSapinfomain")
public class MssSapinfomainController extends BaseController
{
    private String prefix = "mssaccount/mssSapinfomain";
	
	@Autowired
	private IMssSapinfomainService mssSapinfomainService;
	
	@RequiresPermissions("mssaccount:mssSapinfomain:view")
	@GetMapping()
	public String mssSapinfomain()
	{
	    return prefix + "/mssSapinfomain";
	}
	
	/**
	 * 查询报账回传列表
	 */
	@RequiresPermissions("mssaccount:mssSapinfomain:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(MssSapinfomain mssSapinfomain)
	{
		startPage();
        List<MssSapinfomain> list = mssSapinfomainService.selectList(mssSapinfomain);
		return getDataTable(list);
	}
	
	/**
	 * 新增报账回传
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存报账回传
	 */
	@RequiresPermissions("mssaccount:mssSapinfomain:add")
	//@Log(title = "报账回传", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody MssSapinfomain mssSapinfomain)
	{		
		return toAjax(mssSapinfomainService.insert(mssSapinfomain));
	}

	/**
	 * 修改报账回传
	 */
	@GetMapping("/edit/{msmid}")
	public AjaxResult edit(@PathVariable("msmid") Long msmid)
	{
		MssSapinfomain mssSapinfomain = mssSapinfomainService.get(msmid);

		Object object = JSONObject.toJSON(mssSapinfomain);

        return this.success(object);
	}
	
	/**
	 * 修改保存报账回传
	 */
	@RequiresPermissions("mssaccount:mssSapinfomain:edit")
	//@Log(title = "报账回传", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody MssSapinfomain mssSapinfomain)
	{		
		return toAjax(mssSapinfomainService.update(mssSapinfomain));
	}
	
	/**
	 * 删除报账回传
	 */
	@RequiresPermissions("mssaccount:mssSapinfomain:remove")
	//@Log(title = "报账回传", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(mssSapinfomainService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看报账回传
     */
    @RequiresPermissions("mssaccount:mssSapinfomain:view")
    @GetMapping("/view/{msmid}")
    @ResponseBody
    public AjaxResult view(@PathVariable("msmid") Long msmid)
    {
		MssSapinfomain mssSapinfomain = mssSapinfomainService.get(msmid);

        Object object = JSONObject.toJSON(mssSapinfomain);

        return this.success(object);
    }

}
