package com.sccl.modules.business.stationinfo.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;


/**
 * 集团lte网管表 station_jt5g
 * 
 * <AUTHOR>
 * @date 2021-05-13
 */
public class StationJt5g extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
    /** 集团编码 */
    private String jtcode;
    /** 地市 */
    private String citycode;
    /** 区县 */
    private String areacode;
    /** 铁塔编号 */
    private String tacode;
    /** 铁塔名称 */
    private String taname;
    /** 集团名称 */
    private String jtname;
    /** 经度 */
    private String stationjd;
    /** 维度 */
    private String 站址纬度;
    /** 地址 */
    private String address;
    /**  */
    private String 设备共站类型;
    /**  */
    private String 引电方式;
    /**  */
    private String 引电类型;
    /**  */
    private String 站址机房共用情况;
    /**  */
    private String 站址机房产权方;
    /**  */
    private String 站址塔桅产权方;
    /**  */
    private String 数据更新人;
    /**  */
    private Date updatedate;
    /**  */
    private String status;


	public void setJtcode(String jtcode)
	{
		this.jtcode = jtcode;
	}

	public String getJtcode() 
	{
		return jtcode;
	}

	public void setCitycode(String citycode)
	{
		this.citycode = citycode;
	}

	public String getCitycode() 
	{
		return citycode;
	}

	public void setAreacode(String areacode)
	{
		this.areacode = areacode;
	}

	public String getAreacode() 
	{
		return areacode;
	}

	public void setTacode(String tacode)
	{
		this.tacode = tacode;
	}

	public String getTacode() 
	{
		return tacode;
	}

	public void setTaname(String taname)
	{
		this.taname = taname;
	}

	public String getTaname() 
	{
		return taname;
	}

	public void setJtname(String jtname)
	{
		this.jtname = jtname;
	}

	public String getJtname() 
	{
		return jtname;
	}

	public void setStationjd(String stationjd)
	{
		this.stationjd = stationjd;
	}

	public String getStationjd() 
	{
		return stationjd;
	}

	public void set站址纬度(String 站址纬度)
	{
		this.站址纬度 = 站址纬度;
	}

	public String get站址纬度() 
	{
		return 站址纬度;
	}

	public void setAddress(String address)
	{
		this.address = address;
	}

	public String getAddress() 
	{
		return address;
	}

	public void set设备共站类型(String 设备共站类型)
	{
		this.设备共站类型 = 设备共站类型;
	}

	public String get设备共站类型() 
	{
		return 设备共站类型;
	}

	public void set引电方式(String 引电方式)
	{
		this.引电方式 = 引电方式;
	}

	public String get引电方式() 
	{
		return 引电方式;
	}

	public void set引电类型(String 引电类型)
	{
		this.引电类型 = 引电类型;
	}

	public String get引电类型() 
	{
		return 引电类型;
	}

	public void set站址机房共用情况(String 站址机房共用情况)
	{
		this.站址机房共用情况 = 站址机房共用情况;
	}

	public String get站址机房共用情况() 
	{
		return 站址机房共用情况;
	}

	public void set站址机房产权方(String 站址机房产权方)
	{
		this.站址机房产权方 = 站址机房产权方;
	}

	public String get站址机房产权方() 
	{
		return 站址机房产权方;
	}

	public void set站址塔桅产权方(String 站址塔桅产权方)
	{
		this.站址塔桅产权方 = 站址塔桅产权方;
	}

	public String get站址塔桅产权方() 
	{
		return 站址塔桅产权方;
	}

	public void set数据更新人(String 数据更新人)
	{
		this.数据更新人 = 数据更新人;
	}

	public String get数据更新人() 
	{
		return 数据更新人;
	}

	public void setUpdatedate(Date updatedate)
	{
		this.updatedate = updatedate;
	}

	public Date getUpdatedate() 
	{
		return updatedate;
	}

	public void setStatus(String status)
	{
		this.status = status;
	}

	public String getStatus() 
	{
		return status;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("jtcode", getJtcode())
            .append("citycode", getCitycode())
            .append("areacode", getAreacode())
            .append("tacode", getTacode())
            .append("taname", getTaname())
            .append("jtname", getJtname())
            .append("stationjd", getStationjd())
            .append("站址纬度", get站址纬度())
            .append("address", getAddress())
            .append("设备共站类型", get设备共站类型())
            .append("引电方式", get引电方式())
            .append("引电类型", get引电类型())
            .append("站址机房共用情况", get站址机房共用情况())
            .append("站址机房产权方", get站址机房产权方())
            .append("站址塔桅产权方", get站址塔桅产权方())
            .append("数据更新人", get数据更新人())
            .append("updatedate", getUpdatedate())
            .append("status", getStatus())
            .toString();
    }
}
