package com.sccl.modules.mssaccount.mssaccountclearitem.mapper;

import com.sccl.modules.mssaccount.mssaccountclearitem.domain.MssAccountclearitem;
import com.sccl.framework.mapper.BaseMapper;

import java.util.List;

/**
 * 挑对报账单 数据层
 * 
 * <AUTHOR>
 * @date 2019-05-01
 */
public interface MssAccountclearitemMapper extends BaseMapper<MssAccountclearitem>
{


    List<MssAccountclearitem> selectListAuto(MssAccountclearitem mssAccountclearitem);

    List<MssAccountclearitem> queryClearitem(MssAccountclearitem mssAccountclearitem);
}