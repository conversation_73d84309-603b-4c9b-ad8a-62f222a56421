package com.sccl.modules.mssaccount.mssinterface.domain;

import com.enrising.dcarbon.collector.CollectedDatasource;
import com.sccl.modules.autojob.util.convert.StringUtils;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class StationEnergyUseDTO implements CollectedDatasource {
    private String subjectCode;
    private String msgId;
    private List<StationEnergyUseEntity> roomDatas;

    public StationEnergyUseDTO(String city, List<StationEnergyUseEntity> roomDatas) {
        this.subjectCode = ReportingSubjectCodes.getSubjectCode(city);
        this.msgId = subjectCode + System.currentTimeMillis() + StringUtils.getRandomStr(7);
        this.roomDatas = roomDatas;
    }

    public StationEnergyUseDTO() {
    }

    @Override
    public String getAuditKey() {
        return msgId;
    }

    @Override
    public boolean isAvailable() {
        return true;
    }
}
