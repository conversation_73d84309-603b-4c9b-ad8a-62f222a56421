package com.sccl.modules.dataperfect.domain;

import com.sccl.common.utils.BigDecimlUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Data
public class StationGateway {
    private String powerStartTime;
    private String powerEndTime;
    private String devicePower;
    private String stationCode;
    private String billPower;

    private String ammeterid;
    private String ammetername;
    private String company;
    private String country;
    private String staPower;
    private String time;

    public StationGateway() {
    }

    // 构造方法
    public StationGateway(String powerStartTime, String powerEndTime, String devicePower, String stationCode) {
        this.powerStartTime = powerStartTime;
        this.powerEndTime = powerEndTime;
        this.devicePower = devicePower;
        this.stationCode = stationCode;
    }

    public static void process(StationGateway item, List<StationGateway> times) {
        List<StationGateway> timestemp = times.stream().filter(i -> i.getStationCode().equals(item.getStationCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(timestemp)) {
            return;
        }
        String min = timestemp.stream().map(StationGateway::getPowerStartTime).min(String::compareTo).orElse("");
        String max = timestemp.stream().map(StationGateway::getPowerEndTime).max(String::compareTo).orElse("");
        double sum = timestemp.stream()
                .mapToDouble(stationGateway -> {
                    // 转换为 double，如果字符串不是有效的数字，则为 0
                    try {
                        return Double.parseDouble(stationGateway.getBillPower());
                    } catch (NumberFormatException e) {
                        return 0.0;
                    }
                })
                .sum();
        item.setPowerStartTime(min);
        item.setPowerEndTime(max);
        item.setBillPower(String.valueOf(sum));
    }

    public static List<StationGateway> statisticalDeviation(List<StationGateway> tempts, ArrayList<StationGateway> staavgs) {
        ArrayList<StationGateway> result = new ArrayList<>();

        if (CollectionUtils.isEmpty(staavgs) || CollectionUtils.isEmpty(tempts)) {
            return result;
        }

        Map<String, String> staAvgMap = staavgs.stream().collect(Collectors.toMap(
                StationGateway::getStationCode,
                StationGateway::getStaPower,
                (item1, item2) -> item1
        ));

        tempts.forEach(
                item -> {
                    String staPower1 = staAvgMap.get(item.getStationCode());
                    if (staPower1 != null) {
                        item.setStaPower(staPower1);
                        boolean flag = BigDecimlUtil.isDiffGreaterThanOrEqualTo(
                                new BigDecimal(item.getBillPower()).multiply(new BigDecimal("1.3")),
                                new BigDecimal(item.getStaPower()),
                                new BigDecimal("30")
                        );
                        boolean addFalg = flag ? result.add(item) : false;
                    }
                }
        );

        return result;
    }

    // Getter 和 Setter 方法
    public String getPowerStartTime() {
        return powerStartTime;
    }

    public void setPowerStartTime(String powerStartTime) {
        this.powerStartTime = powerStartTime;
    }

    public String getPowerEndTime() {
        return powerEndTime;
    }

    public void setPowerEndTime(String powerEndTime) {
        this.powerEndTime = powerEndTime;
    }

    public String getDevicePower() {
        return devicePower;
    }

    public void setDevicePower(String devicePower) {
        this.devicePower = devicePower;
    }

    public String getStationCode() {
        return stationCode;
    }

    public void setStationCode(String stationCode) {
        this.stationCode = stationCode;
    }

    // 其他方法（如果需要）

    @Override
    public String toString() {
        return "StationGateway{" +
                "powerStartTime='" + powerStartTime + '\'' +
                ", powerEndTime='" + powerEndTime + '\'' +
                ", devicePower='" + devicePower + '\'' +
                ", stationCode='" + stationCode + '\'' +
                '}';
    }


    public static Map<String, Integer> calculateDays(List<StationGateway> stationGateways) {
        Map<String, Integer> result = new HashMap<>();
        HashMap<String, Boolean> tempMap = new HashMap<>();

        for (StationGateway stationGateway : stationGateways) {
            String stationCode = stationGateway.getStationCode();
            String powerStartTime = stationGateway.getPowerStartTime();
            String powerEndTime = stationGateway.getPowerEndTime();

            try {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
                Date startDate = dateFormat.parse(powerStartTime);
                Date endDate = dateFormat.parse(powerEndTime);

                Calendar calendar = Calendar.getInstance();
                calendar.setTime(startDate);

                while (!calendar.getTime().after(endDate)) {
                    String monthKey = new SimpleDateFormat("yyyyMM").format(calendar.getTime());
                    String dayKey = new SimpleDateFormat("yyyyMMdd").format(calendar.getTime());
                    if (!tempMap.containsKey(dayKey)) {
                        tempMap.put(dayKey, true);
                        result.put(monthKey, result.getOrDefault(monthKey, 0) + 1);
                    }
                    calendar.add(Calendar.DAY_OF_MONTH, 1);
                }

            } catch (ParseException e) {
                e.printStackTrace();
            }
        }

        return result;
    }


    public static void main(String[] args) {
        List<StationGateway> stationGateways = new ArrayList<>();
        stationGateways.add(new StationGateway("20230130", "20230323", "", "aaa"));
        stationGateways.add(new StationGateway("20230403", "20230523", "", "aaa"));

        Map<String, Integer> result = calculateDays(stationGateways);

        for (Map.Entry<String, Integer> entry : result.entrySet()) {
            System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
        }
    }
}

