package com.sccl.modules.business.powermodel.mapper;

import com.sccl.modules.business.powermodel.entity.PowerModleBase;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 电量模型基本表(PowerModleBase)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-10-19 17:44:40
 */
@Mapper
public interface PowerModleBaseMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    PowerModleBase queryById(Long id);

    /**
     * 查询指定行数据
     *
     * @param powerModleBase 查询条件
     * @param pageable       分页对象
     * @return 对象列表
     */
    List<PowerModleBase> queryAllByLimit(PowerModleBase powerModleBase, @Param("pageable") Pageable pageable);

    /**
     * 统计总行数
     *
     * @param powerModleBase 查询条件
     * @return 总行数
     */
    long count(PowerModleBase powerModleBase);

    /**
     * 新增数据
     *
     * @param powerModleBase 实例对象
     * @return 影响行数
     */
    int insert(PowerModleBase powerModleBase);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<PowerModleBase> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<PowerModleBase> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<PowerModleBase> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<PowerModleBase> entities);

    /**
     * 修改数据
     *
     * @param powerModleBase 实例对象
     * @return 影响行数
     */
    int update(PowerModleBase powerModleBase);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

}

