package com.sccl.modules.mssaccount.mssaccountbill.domain;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 报账查询
 * @date 2024/10/21  14:41
 */
@Data
public class AccountBillRequest {

    /**
     * 账期
     */
    private String budgetsetname;

    /**
     * 公司名称
     */
    private String company;

    /**
     * 供应商关键字
     */
    private String keySupplierWord;

    /**
     * 部门关键字
     */
    private String keyOrgNameWord;

    /**
     * 导出类型
     */
    private Integer exportType;

    /**
     * 能耗类型
     */
    private Integer energyType;

    /**
     * 时间周期-开始
     */
    private String startDate;

    /**
     * 时间周期-结束
     */
    private String endDate;

    private List<String> countrys;

}
