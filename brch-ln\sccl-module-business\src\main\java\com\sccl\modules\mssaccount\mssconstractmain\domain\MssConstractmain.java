package com.sccl.modules.mssaccount.mssconstractmain.domain;

import com.sccl.framework.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * 合同表 MSS_CONSTRACTMAIN
 *
 * <AUTHOR>
 * @date 2019-05-01
 */
public class MssConstractmain extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long infid;
    /**
     *
     */
    private Date infdate;
    /**
     *
     */
    private String infstatus;
    /**
     *
     */
    private String contractid;
    /**
     *
     */
    private String contractcode;
    /**
     *
     */
    private String signcode;
    /**
     *
     */
    private String operatetype;
    /**
     *
     */
    private String frameflag;
    /**
     *
     */
    private String partatype;
    /**
     *
     */
    private String fundflag;
    /**
     *
     */
    private String contractname;
    /**
     *
     */
    private String contractfactbook;
    /**
     *
     */
    private String tradesum;
    /**
     *
     */
    private String currencytype;
    /**
     *
     */
    private String preContractcode;
    /**
     *
     */
    private String preContractname;
    /**
     *
     */
    private String preTradesum;
    /**
     *
     */
    private String tradesumChange;
    /**
     *
     */
    private String concode3class;
    /**
     *
     */
    private String partaname;
    /**
     *
     */
    private String emergencydegree;
    /**
     *
     */
    private String multsubject;
    /**
     *
     */
    private String frametype;
    /**
     *
     */
    private String frameareas;
    /**
     *
     */
    private String applyareaou;
    /**
     *
     */
    private String applydeptcode;
    /**
     *
     */
    private String applydept;
    /**
     *
     */
    private String applyuserid;
    /**
     *
     */
    private String applyusername;
    /**
     *
     */
    private String tel;
    /**
     *
     */
    private String status;
    /**
     *
     */
    private String poweranalyzeflag;
    /**
     *
     */
    private String pbasecontext;
    /**
     *
     */
    private String usecontext;
    /**
     *
     */
    private String subusecontext;
    /**
     *
     */
    private String creadate;
    /**
     *
     */
    private String finaldate;
    /**
     *
     */
    private String stampdate;
    /**
     *
     */
    private String signenddate;
    /**
     *
     */
    private String archivedate;
    /**
     *
     */
    private String signdate;
    /**
     *
     */
    private String enddate;
    /**
     *
     */
    private String extendedterms;
    /**
     *
     */
    private String extendedtype;
    /**
     *
     */
    private String extendedate;
    /**
     *
     */
    private String sourceSystem;
    /**
     *
     */
    private String stockmode;
    /**
     *
     */
    private String lastupdatetime;
    /**
     *
     */
    private String ifictproject;
    /**
     *
     */
    private String ictprojectnum;
    /**
     *
     */
    private String taxamount;
    /**
     *
     */
    private String ifperiodic;
    /**
     *
     */
    private String perifrequency;
    /**
     *
     */
    private String perieachamount;
    /**
     *
     */
    private String manuscriptid;
    /**
     *
     */
    private String ifoutsourccon;
    /**
     *
     */
    private String companyleader;
    /**
     *
     */
    private String companyleaderdesc;
    /**
     *
     */
    private String changedate;
    /**
     *
     */
    private String iftradechange;
    /**
     *
     */
    private String ifmaintenance;
    /**
     *
     */
    private String maintenyear;
    /**
     *
     */
    private String costtype;
    /**
     *
     */
    private String costtype2;
    /**
     *
     */
    private String taxitem;
    /**
     *
     */
    private String taxableproperty;
    /**
     *
     */
    private String internalappitems;
    /**
     *
     */
    private String renewcontractflag;
    /**
     *
     */
    private String ifframeagree;
    /**
     *
     */
    private String ifkeyconract;
    /**
     *
     */
    private String ifupapproval;
    /**
     *
     */
    private String rentalpurposes;
    /**
     *
     */
    private String homeaddress;
    /**
     *
     */
    private String rentalarea;
    /**
     *
     */
    private String permeterrent;
    /**
     *
     */
    private String landhousenum;
    /**
     *
     */
    private String landhouseSrcId;
    /**
     *
     */
    private String contractcodeJt;
    /**
     *
     */
    private String stampuserid;
    /**
     *
     */
    private String signenduserid;
    /**
     *
     */
    private String archiveuserid;
    /**
     *
     */
    private String pcProjcode;

    public static String extractTextWithRegex(String input, String regex) {
        StringBuilder extractedText = new StringBuilder();
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);

        while (matcher.find()) {
            extractedText.append(matcher.group()).append(" ");
        }

        return extractedText.toString().trim();
    }

    public Long getInfid() {
        return infid;
    }

    public void setInfid(Long infid) {
        this.infid = infid;
    }

    public Date getInfdate() {
        return infdate;
    }

    public void setInfdate(Date infdate) {
        this.infdate = infdate;
    }

    public String getInfstatus() {
        return infstatus;
    }

    public void setInfstatus(String infstatus) {
        this.infstatus = infstatus;
    }

    public String getContractid() {
        return contractid;
    }

    public void setContractid(String contractid) {
        this.contractid = contractid;
    }

    public String getContractcode() {
        return contractcode;
    }

    public void setContractcode(String contractcode) {
        this.contractcode = contractcode;
    }

    public String getSigncode() {
        return signcode;
    }

    public void setSigncode(String signcode) {
        this.signcode = signcode;
    }

    public String getOperatetype() {
        return operatetype;
    }

    public void setOperatetype(String operatetype) {
        this.operatetype = operatetype;
    }

    public String getFrameflag() {
        return frameflag;
    }

    public void setFrameflag(String frameflag) {
        this.frameflag = frameflag;
    }

    public String getPartatype() {
        return partatype;
    }

    public void setPartatype(String partatype) {
        this.partatype = partatype;
    }

    public String getFundflag() {
        return fundflag;
    }

    public void setFundflag(String fundflag) {
        this.fundflag = fundflag;
    }

    public String getContractname() {
        return contractname;
    }

    public void setContractname(String contractname) {
        this.contractname = contractname;
    }

    public String getContractfactbook() {
        return contractfactbook;
    }

    public void setContractfactbook(String contractfactbook) {
        this.contractfactbook = contractfactbook;
    }

    public String getTradesum() {
        return tradesum;
    }

    public void setTradesum(String tradesum) {
        this.tradesum = tradesum;
    }

    public String getCurrencytype() {
        return currencytype;
    }

    public void setCurrencytype(String currencytype) {
        this.currencytype = currencytype;
    }

    public String getPreContractcode() {
        return preContractcode;
    }

    public void setPreContractcode(String preContractcode) {
        this.preContractcode = preContractcode;
    }

    public String getPreContractname() {
        return preContractname;
    }

    public void setPreContractname(String preContractname) {
        this.preContractname = preContractname;
    }

    public String getPreTradesum() {
        return preTradesum;
    }

    public void setPreTradesum(String preTradesum) {
        this.preTradesum = preTradesum;
    }

    public String getTradesumChange() {
        return tradesumChange;
    }

    public void setTradesumChange(String tradesumChange) {
        this.tradesumChange = tradesumChange;
    }

    public String getConcode3class() {
        return concode3class;
    }

    public void setConcode3class(String concode3class) {
        this.concode3class = concode3class;
    }

    public String getPartaname() {
        return partaname;
    }

    public void setPartaname(String partaname) {
        this.partaname = partaname;
    }

    public String getEmergencydegree() {
        return emergencydegree;
    }

    public void setEmergencydegree(String emergencydegree) {
        this.emergencydegree = emergencydegree;
    }

    public String getMultsubject() {
        return multsubject;
    }

    public void setMultsubject(String multsubject) {
        this.multsubject = multsubject;
    }

    public String getFrametype() {
        return frametype;
    }

    public void setFrametype(String frametype) {
        this.frametype = frametype;
    }

    public String getFrameareas() {
        return frameareas;
    }

    public void setFrameareas(String frameareas) {
        this.frameareas = frameareas;
    }

    public String getApplyareaou() {
        return applyareaou;
    }

    public void setApplyareaou(String applyareaou) {
        this.applyareaou = applyareaou;
    }

    public String getApplydeptcode() {
        return applydeptcode;
    }

    public void setApplydeptcode(String applydeptcode) {
        this.applydeptcode = applydeptcode;
    }

    public String getApplydept() {
        return applydept;
    }

    public void setApplydept(String applydept) {
        this.applydept = applydept;
    }

    public String getApplyuserid() {
        return applyuserid;
    }

    public void setApplyuserid(String applyuserid) {
        this.applyuserid = applyuserid;
    }

    public String getApplyusername() {
        return applyusername;
    }

    public void setApplyusername(String applyusername) {
        this.applyusername = applyusername;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPoweranalyzeflag() {
        return poweranalyzeflag;
    }

    public void setPoweranalyzeflag(String poweranalyzeflag) {
        this.poweranalyzeflag = poweranalyzeflag;
    }

    public String getPbasecontext() {
        return pbasecontext;
    }

    public void setPbasecontext(String pbasecontext) {
        this.pbasecontext = pbasecontext;
    }

    public String getUsecontext() {
        return usecontext;
    }

    public void setUsecontext(String usecontext) {
        this.usecontext = usecontext;
    }

    public String getSubusecontext() {
        return subusecontext;
    }

    public void setSubusecontext(String subusecontext) {
        this.subusecontext = subusecontext;
    }

    public String getCreadate() {
        return creadate;
    }

    public void setCreadate(String creadate) {
        this.creadate = creadate;
    }

    public String getFinaldate() {
        return finaldate;
    }

    public void setFinaldate(String finaldate) {
        this.finaldate = finaldate;
    }

    public String getStampdate() {
        return stampdate;
    }

    public void setStampdate(String stampdate) {
        this.stampdate = stampdate;
    }

    public String getSignenddate() {
        return signenddate;
    }

    public void setSignenddate(String signenddate) {
        this.signenddate = signenddate;
    }

    public String getArchivedate() {
        return archivedate;
    }

    public void setArchivedate(String archivedate) {
        this.archivedate = archivedate;
    }

    public String getSigndate() {
        return signdate;
    }

    public void setSigndate(String signdate) {
        this.signdate = signdate;
    }

    public String getEnddate() {
        return enddate;
    }

    public void setEnddate(String enddate) {
        this.enddate = enddate;
    }

    public String getExtendedterms() {
        return extendedterms;
    }

    public void setExtendedterms(String extendedterms) {
        this.extendedterms = extendedterms;
    }

    public String getExtendedtype() {
        return extendedtype;
    }

    public void setExtendedtype(String extendedtype) {
        this.extendedtype = extendedtype;
    }

    public String getExtendedate() {
        return extendedate;
    }

    public void setExtendedate(String extendedate) {
        this.extendedate = extendedate;
    }

    public String getSourceSystem() {
        return sourceSystem;
    }

    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem;
    }

    public String getStockmode() {
        return stockmode;
    }

    public void setStockmode(String stockmode) {
        this.stockmode = stockmode;
    }

    public String getLastupdatetime() {
        return lastupdatetime;
    }

    public void setLastupdatetime(String lastupdatetime) {
        this.lastupdatetime = lastupdatetime;
    }

    public String getIfictproject() {
        return ifictproject;
    }

    public void setIfictproject(String ifictproject) {
        this.ifictproject = ifictproject;
    }

    public String getIctprojectnum() {
        return ictprojectnum;
    }

    public void setIctprojectnum(String ictprojectnum) {
        this.ictprojectnum = ictprojectnum;
    }

    public String getTaxamount() {
        return taxamount;
    }

    public void setTaxamount(String taxamount) {
        this.taxamount = taxamount;
    }

    public String getIfperiodic() {
        return ifperiodic;
    }

    public void setIfperiodic(String ifperiodic) {
        this.ifperiodic = ifperiodic;
    }

    public String getPerifrequency() {
        return perifrequency;
    }

    public void setPerifrequency(String perifrequency) {
        this.perifrequency = perifrequency;
    }

    public String getPerieachamount() {
        return perieachamount;
    }

    public void setPerieachamount(String perieachamount) {
        this.perieachamount = perieachamount;
    }

    public String getManuscriptid() {
        return manuscriptid;
    }

    public void setManuscriptid(String manuscriptid) {
        this.manuscriptid = manuscriptid;
    }

    public String getIfoutsourccon() {
        return ifoutsourccon;
    }

    public void setIfoutsourccon(String ifoutsourccon) {
        this.ifoutsourccon = ifoutsourccon;
    }

    public String getCompanyleader() {
        return companyleader;
    }

    public void setCompanyleader(String companyleader) {
        this.companyleader = companyleader;
    }

    public String getCompanyleaderdesc() {
        return companyleaderdesc;
    }

    public void setCompanyleaderdesc(String companyleaderdesc) {
        this.companyleaderdesc = companyleaderdesc;
    }

    public String getChangedate() {
        return changedate;
    }

    public void setChangedate(String changedate) {
        this.changedate = changedate;
    }

    public String getIftradechange() {
        return iftradechange;
    }

    public void setIftradechange(String iftradechange) {
        this.iftradechange = iftradechange;
    }

    public String getIfmaintenance() {
        return ifmaintenance;
    }

    public void setIfmaintenance(String ifmaintenance) {
        this.ifmaintenance = ifmaintenance;
    }

    public String getMaintenyear() {
        return maintenyear;
    }

    public void setMaintenyear(String maintenyear) {
        this.maintenyear = maintenyear;
    }

    public String getCosttype() {
        return costtype;
    }

    public void setCosttype(String costtype) {
        this.costtype = costtype;
    }

    public String getCosttype2() {
        return costtype2;
    }

    public void setCosttype2(String costtype2) {
        this.costtype2 = costtype2;
    }

    public String getTaxitem() {
        return taxitem;
    }

    public void setTaxitem(String taxitem) {
        this.taxitem = taxitem;
    }

    public String getTaxableproperty() {
        return taxableproperty;
    }

    public void setTaxableproperty(String taxableproperty) {
        this.taxableproperty = taxableproperty;
    }

    public String getInternalappitems() {
        return internalappitems;
    }

    public void setInternalappitems(String internalappitems) {
        this.internalappitems = internalappitems;
    }

    public String getRenewcontractflag() {
        return renewcontractflag;
    }

    public void setRenewcontractflag(String renewcontractflag) {
        this.renewcontractflag = renewcontractflag;
    }

    public String getIfframeagree() {
        return ifframeagree;
    }

    public void setIfframeagree(String ifframeagree) {
        this.ifframeagree = ifframeagree;
    }

    public String getIfkeyconract() {
        return ifkeyconract;
    }

    public void setIfkeyconract(String ifkeyconract) {
        this.ifkeyconract = ifkeyconract;
    }

    public String getIfupapproval() {
        return ifupapproval;
    }

    public void setIfupapproval(String ifupapproval) {
        this.ifupapproval = ifupapproval;
    }

    public String getRentalpurposes() {
        return rentalpurposes;
    }

    public void setRentalpurposes(String rentalpurposes) {
        this.rentalpurposes = rentalpurposes;
    }

    public String getHomeaddress() {
        return homeaddress;
    }

    public void setHomeaddress(String homeaddress) {
        this.homeaddress = homeaddress;
    }

    public String getRentalarea() {
        return rentalarea;
    }

    public void setRentalarea(String rentalarea) {
        this.rentalarea = rentalarea;
    }

    public String getPermeterrent() {
        return permeterrent;
    }

    public void setPermeterrent(String permeterrent) {
        this.permeterrent = permeterrent;
    }

    public String getLandhousenum() {
        return landhousenum;
    }

    public void setLandhousenum(String landhousenum) {
        this.landhousenum = landhousenum;
    }

    public String getLandhouseSrcId() {
        return landhouseSrcId;
    }

    public void setLandhouseSrcId(String landhouseSrcId) {
        this.landhouseSrcId = landhouseSrcId;
    }

    public String getContractcodeJt() {
        return contractcodeJt;
    }

    public void setContractcodeJt(String contractcodeJt) {
        this.contractcodeJt = contractcodeJt;
    }

    public String getStampuserid() {
        return stampuserid;
    }

    public void setStampuserid(String stampuserid) {
        this.stampuserid = stampuserid;
    }

    public String getSignenduserid() {
        return signenduserid;
    }

    public void setSignenduserid(String signenduserid) {
        this.signenduserid = signenduserid;
    }

    public String getArchiveuserid() {
        return archiveuserid;
    }

    public void setArchiveuserid(String archiveuserid) {
        this.archiveuserid = archiveuserid;
    }

    public String getPcProjcode() {
        return pcProjcode;
    }

    public void setPcProjcode(String pcProjcode) {
        this.pcProjcode = pcProjcode;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
          .append("infid", getInfid())
          .append("infdate", getInfdate())
          .append("infstatus", getInfstatus())
          .append("contractid", getContractid())
          .append("contractcode", getContractcode())
          .append("signcode", getSigncode())
          .append("operatetype", getOperatetype())
          .append("frameflag", getFrameflag())
          .append("partatype", getPartatype())
          .append("fundflag", getFundflag())
          .append("contractname", getContractname())
          .append("contractfactbook", getContractfactbook())
          .append("tradesum", getTradesum())
          .append("currencytype", getCurrencytype())
          .append("preContractcode", getPreContractcode())
          .append("preContractname", getPreContractname())
          .append("preTradesum", getPreTradesum())
          .append("tradesumChange", getTradesumChange())
          .append("concode3class", getConcode3class())
          .append("partaname", getPartaname())
          .append("emergencydegree", getEmergencydegree())
          .append("multsubject", getMultsubject())
          .append("frametype", getFrametype())
          .append("frameareas", getFrameareas())
          .append("applyareaou", getApplyareaou())
          .append("applydeptcode", getApplydeptcode())
          .append("applydept", getApplydept())
          .append("applyuserid", getApplyuserid())
          .append("applyusername", getApplyusername())
          .append("tel", getTel())
          .append("status", getStatus())
          .append("poweranalyzeflag", getPoweranalyzeflag())
          .append("pbasecontext", getPbasecontext())
          .append("usecontext", getUsecontext())
          .append("subusecontext", getSubusecontext())
          .append("creadate", getCreadate())
          .append("finaldate", getFinaldate())
          .append("stampdate", getStampdate())
          .append("signenddate", getSignenddate())
          .append("archivedate", getArchivedate())
          .append("signdate", getSigndate())
          .append("enddate", getEnddate())
          .append("extendedterms", getExtendedterms())
          .append("extendedtype", getExtendedtype())
          .append("extendedate", getExtendedate())
          .append("sourceSystem", getSourceSystem())
          .append("stockmode", getStockmode())
          .append("lastupdatetime", getLastupdatetime())
          .append("ifictproject", getIfictproject())
          .append("ictprojectnum", getIctprojectnum())
          .append("taxamount", getTaxamount())
          .append("ifperiodic", getIfperiodic())
          .append("perifrequency", getPerifrequency())
          .append("perieachamount", getPerieachamount())
          .append("manuscriptid", getManuscriptid())
          .append("ifoutsourccon", getIfoutsourccon())
          .append("companyleader", getCompanyleader())
          .append("companyleaderdesc", getCompanyleaderdesc())
          .append("changedate", getChangedate())
          .append("iftradechange", getIftradechange())
          .append("ifmaintenance", getIfmaintenance())
          .append("maintenyear", getMaintenyear())
          .append("costtype", getCosttype())
          .append("costtype2", getCosttype2())
          .append("taxitem", getTaxitem())
          .append("taxableproperty", getTaxableproperty())
          .append("internalappitems", getInternalappitems())
          .append("renewcontractflag", getRenewcontractflag())
          .append("ifframeagree", getIfframeagree())
          .append("ifkeyconract", getIfkeyconract())
          .append("ifupapproval", getIfupapproval())
          .append("rentalpurposes", getRentalpurposes())
          .append("homeaddress", getHomeaddress())
          .append("rentalarea", getRentalarea())
          .append("permeterrent", getPermeterrent())
          .append("landhousenum", getLandhousenum())
          .append("landhouseSrcId", getLandhouseSrcId())
          .append("contractcodeJt", getContractcodeJt())
          .append("stampuserid", getStampuserid())
          .append("signenduserid", getSignenduserid())
          .append("archiveuserid", getArchiveuserid())
          .append("pcProjcode", getPcProjcode())
          .toString();
    }

}
