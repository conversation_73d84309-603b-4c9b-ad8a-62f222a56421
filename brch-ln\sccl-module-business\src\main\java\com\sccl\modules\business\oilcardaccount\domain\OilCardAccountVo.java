package com.sccl.modules.business.oilcardaccount.domain;

import com.sccl.framework.web.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2021/12/20 14:03
 **/
@Getter
@Setter
public class OilCardAccountVo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 所属分公司
     */
    private Long company;
    /**
     * 所属部门
     */
    private Long country;
    /**
     * 油卡数据库ID
     */
    private Long cardId;
    /**
     * 专用油卡管理负责人
     */
    private String cardMaster;
    /**
     * 购买日期
     */
    private Date buyDate;
    /**
     * 购买时间
     */
    private Date buyTime;
    /**
     * 购油量
     */
    private BigDecimal buyQuantity;
    /**
     * 购买油品类型
     */
    private String oilType;
    /**
     * 购油金额
     */
    private BigDecimal cost;
    /**
     * 油卡余额
     */
    private BigDecimal balance;
    /**
     * 附件
     */
    private String attachment;
    /**
     * 油卡台账类型 1充值 2购油
     */
    private String accountType;

    private String companyName;

    private String countryName;

    // 油卡编号 用户录入
    private String oilCardId;

    private BigDecimal status;


    private BigDecimal useAccount;

    //购油台账剩余量
    private BigDecimal surplus;

    //购油单价
    private BigDecimal unitprice;


}
