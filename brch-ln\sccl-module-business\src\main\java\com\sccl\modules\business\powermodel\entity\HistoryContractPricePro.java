package com.sccl.modules.business.powermodel.entity;

import com.sccl.modules.autojob.util.convert.StringUtils;
import com.sccl.modules.mssaccount.mssconstractmain.domain.MssConstractmain;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class HistoryContractPricePro {
    private String company;
    private String companyname;
    private String country;
    private String countryname;
    private String ammeterid;
    private String ammename;
    private String budget;
    private String startdate;
    private String enddate;
    private String power;
    private String money;
    private String billprice;
    private String transferpowersupplycontractprice;
    private String diff;


    public static void ProcessPrice(HistoryContractPricePro item) {
        String transferpowersupplycontractprice = MssConstractmain.extractTextWithRegex(item.getTransferpowersupplycontractprice(), "\\d+\\.\\d+元/度");
        item.setTransferpowersupplycontractprice(transferpowersupplycontractprice);
        String billprice = item.getBillprice();

        if (StringUtils.isBlank(transferpowersupplycontractprice)) {
            item.setDiff("合同单价不存在,未计算");
        } else   if (StringUtils.isBlank(billprice)) {
            item.setDiff("报账单价不存在,未计算");
        } else {
            String diff = calcDiff(transferpowersupplycontractprice, billprice);
            item.setDiff(diff);
        }
    }

    private static String calcDiff(String transferpowersupplycontractprice, String billprice) {
        BigDecimal transPrrce = new BigDecimal(transferpowersupplycontractprice.replace("元/度", ""));
        BigDecimal bill = new BigDecimal(billprice);
        BigDecimal divide = transPrrce.subtract(bill).divide(bill, 2).multiply(new BigDecimal(100));
        String diff = divide.compareTo(BigDecimal.ZERO) >= 0 ?
         /* String.format("转供电相对于报账单价向上浮动:%s%%", divide.abs().toString())
          :
          String.format("转供电相对于报账单价向下浮动:%s%%", divide.abs().toString());*/
                String.format("%s%%", divide.abs().toString())
                :
                String.format("%s%%", divide.abs().toString());

        return diff;
    }
}
