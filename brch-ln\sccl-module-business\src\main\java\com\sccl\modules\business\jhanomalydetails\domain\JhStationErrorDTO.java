package com.sccl.modules.business.jhanomalydetails.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.sccl.framework.aspectj.lang.annotation.Excel;
import lombok.Data;


@Data
public class JhStationErrorDTO {


    private static final long serialVersionUID = 1L;


    /** 报账单期号 */
    @Excel(name = "报账单期号_bz")
    private String bzdqh;
    /** 报账单id */
    @Excel(name = "报账单id_bz")
    private String bzdid;


    /**
     * 台账期号
     */
    @TableField("tzqh")
    @Excel(name = "台账期号_tz_bz")
    private String tzqh;

    /**
     * 类型
     */
    @TableField("lx")
    @Excel(name = "类型_tz_bz")
    private String lx;

    /**
     * 电表户名/协议编码
     */
    @TableField("dbhm")
    @Excel(name = "电表户名/协议编码_tz_bz")
    private String dbhm;

    /**
     * 集团站址编码
     */
    @TableField("jtzzbm")
    @Excel(name = "集团站址编码_tz_bz")
    private String jtzzbm;

    /**
     * 铁塔站址编码
     */
    @TableField("ttzzbm")
    @Excel(name = "铁塔站址编码_tz_bz")
    private String ttzzbm;


}
