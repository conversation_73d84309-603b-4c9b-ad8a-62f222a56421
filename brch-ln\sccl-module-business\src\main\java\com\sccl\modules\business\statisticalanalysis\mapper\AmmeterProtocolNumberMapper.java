package com.sccl.modules.business.statisticalanalysis.mapper;

import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.statisticalanalysis.domain.AmmeterProtocolNumber;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface AmmeterProtocolNumberMapper extends BaseMapper<AmmeterProtocolNumber> {
    List<AmmeterProtocolNumber> selectGroupByCompany(AmmeterProtocolNumber ammeterProtocolNumber);
    List<AmmeterProtocolNumber> selectGroupByCountry(AmmeterProtocolNumber ammeterProtocolNumber);
}
