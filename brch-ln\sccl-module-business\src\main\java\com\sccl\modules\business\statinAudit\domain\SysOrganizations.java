package com.sccl.modules.business.statinAudit.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * 组织机构表
 */
@Getter
@Setter
@ToString
@TableName("rmp.sys_organizations")
public class SysOrganizations extends Model<SysOrganizations> {

    /** 主键 */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;
    /** 组织机构唯一编码 */
    private String orgCode;
    /** 组织机构名称 */
    private String orgName;
    /** 1分公司 2部门 */
    private String orgType;
    /** 上级公司编码 */
    private Long parentCompanyNo;
    /** 上级部门编码 */
    private String parentGroupNo;
    /** 排序编号 */
    private Integer idxNum;
    /** 状态（0正常 1停用） */
    private String status;
    /** 组织正职领导 */
    private String orgMainLeader;
    /** 组织副职领导，多值用,隔开 */
    private String orgOtherLeader;
    /** 公司类型 */
    private String companyType;
    /** 部门类型 */
    private String groupType;
    /** 省份 */
    private String provinceCode;
    /** 部门层级  */
    private String deptLevel;
    /** 组织机构英文名称 */
    private String orgNameEng;
    /** 创建者ID */
    private Long creatorId;
    /** 创建者NAME */
    private String creatorName;
    /** 创建时间 */
    private Date createTime;
    /** 更新者ID */
    private Long updateById;
    /** 更新者NAME */
    private String updateByName;
    /** 更新时间 */
    private Date updateTime;
    /** 删除标识(0正常，1删除) */
    private String delFlag;

    @TableField(exist = false)
    private Long childNum;

    @TableField(exist = false)
    private List<SysOrganizations> children;
}
