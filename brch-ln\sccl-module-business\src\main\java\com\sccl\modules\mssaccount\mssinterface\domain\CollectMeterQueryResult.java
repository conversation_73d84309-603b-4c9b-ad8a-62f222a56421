package com.sccl.modules.mssaccount.mssinterface.domain;

import lombok.Data;
import java.io.Serializable;

/**
 * 查询结果DTO - 用于存储通过county_name和WRITEOFF_INSTANCE_CODE查询的结果
 */
@Data
public class CollectMeterQueryResult implements Serializable {

    /**
     * 开始日期
     */
    private String startdate;

    /**
     * 结束日期
     */
    private String enddate;

    /**
     * 报账电量
     */
    private Double thisQuantityOfElectricity;

    /**
     * 省份编码
     */
    private Integer provinceCode;

    /**
     * 市局编码
     */
    private String cityCode;

    /**
     * 市局名称
     */
    private String cityName;

    /**
     * 区县编码
     */
    private String countyCode;

    /**
     * 区县名称
     */
    private String countyName;

    /**
     * 局站编码
     */
    private String stationCode;

    /**
     * 局站名称
     */
    private String stationName;

    /**
     * 县名称
     */
    private String county_name;

    /**
     * 市电电量
     */
    private Double acData;

    /**
     * 市电数据来源
     */
    private Integer acDataSource;

    /**
     * 油机发电电量
     */
    private Double oepgData;

    /**
     * 油机发电数据来源
     */
    private Integer oepgDataSource;

    /**
     * 光伏发电电量
     */
    private Double pvpgData;

    /**
     * 光伏发电数据来源
     */
    private Integer pvpgDataSource;

    /**
     * 父局站编码
     */
    private String parentStationCode;

    /**
     * 折标系数
     */
    private String ccoer;

    /**
     * 碳排放因子
     */
    private String cdcf;

    /**
     * 总能耗
     */
    private Double energyData;

    /**
     * 总能耗数据来源
     */
    private Integer energyDataSource;

    /**
     * 设备能耗
     */
    private Double deviceData;

    /**
     * 设备能耗数据来源
     */
    private Integer deviceDataSource;

    /**
     * 生产分摊能耗
     */
    private Double productionData;

    /**
     * 生产分摊数据来源
     */
    private Integer productionDataSource;

    /**
     * 管理分摊能耗
     */
    private Double managementData;

    /**
     * 管理分摊数据来源
     */
    private Integer managementDataSource;

    /**
     * 营业分摊能耗
     */
    private Double businessData;

    /**
     * 营业分摊数据来源
     */
    private Integer businessDataSource;

    /**
     * 其他分摊能耗
     */
    private Double otherData;

    /**
     * 其他分摊数据来源
     */
    private Integer otherDataSource;

    /**
     * 删除标志
     */
    private Integer del_flag;

    /**
     * 同步标志
     */
    private Integer syncFlag;
}
