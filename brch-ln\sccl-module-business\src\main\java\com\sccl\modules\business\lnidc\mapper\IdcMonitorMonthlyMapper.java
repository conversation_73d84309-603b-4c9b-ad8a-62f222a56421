package com.sccl.modules.business.lnidc.mapper;

import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.lnidc.domain.IdcMonitorMonthly;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IdcMonitorMonthlyMapper extends BaseMapper<IdcMonitorMonthly> {

    List<IdcMonitorMonthly> getListByIds(@Param("year")String year,@Param("statisticsIds") List<Long> statisticsIds);

    void batchUpdateMonthlyData(@Param("monthlyList") List<IdcMonitorMonthly> monthlyList);
}