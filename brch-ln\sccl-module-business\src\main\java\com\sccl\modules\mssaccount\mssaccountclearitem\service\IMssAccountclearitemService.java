package com.sccl.modules.mssaccount.mssaccountclearitem.service;

import com.sccl.modules.mssaccount.mssaccountclearitem.domain.MssAccountclearitem;
import com.sccl.framework.service.IBaseService;

import java.util.List;

/**
 * 挑对报账单 服务层
 * 
 * <AUTHOR>
 * @date 2019-05-01
 */
public interface IMssAccountclearitemService extends IBaseService<MssAccountclearitem>
{


    List<MssAccountclearitem> selectListAuto(MssAccountclearitem mssAccountclearitem);

    // 验证 可挑对金额
    List<MssAccountclearitem> queryClearitem(MssAccountclearitem mssAccountclearitem);
}
