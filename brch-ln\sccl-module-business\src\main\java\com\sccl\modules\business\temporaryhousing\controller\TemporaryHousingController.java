package com.sccl.modules.business.temporaryhousing.controller;

import com.sccl.common.lang.StringUtils;
import com.sccl.common.web.controller.BaseController;
import com.sccl.modules.business.temporaryhousing.domain.TemporaryHousing;
import com.sccl.modules.business.temporaryhousing.service.ITemporaryHousingService;
import com.sccl.modules.system.attachments.domain.UpLoadFile;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * __房屋临时表__<br/>
 * 2019/10/31
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/business/temporaryhousing")
public class TemporaryHousingController extends BaseController {

    @Autowired
    ITemporaryHousingService service;


    @RequestMapping(value = "/uploadExcel")
    @ResponseBody
    public Map<String, Object> uploadExcel(HttpServletRequest request, HttpServletResponse response, UpLoadFile uploadFile)
            throws Exception {
        response.setContentType("text/html;charset=utf-8");
        Map<String, Object> map = new HashMap<>();
        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        Iterator<String> iterator = multiRequest.getFileNames();

        String type = ServletRequestUtils.getStringParameter(request, "type", "");
        List<TemporaryHousing> list = new ArrayList<>();

        String str = "";
        while (iterator.hasNext()) {
            String name = iterator.next();
            List<MultipartFile> files = new LinkedList<>();
            files = multiRequest.getFiles(name);
            // 一次遍历所有文件
            for (MultipartFile file : files) {
                if (file == null) {
                    continue;
                }

                //如果文件大小为0则不上传
                long fileSize = file.getSize();
                if (fileSize <= 0L) {
                    throw new Exception("文件名称：【" + file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("\\") + 1, file.getOriginalFilename().length()) + "】的文件大小为0,请核对!");
                }
                list = service.importExcel("sheet1",file.getInputStream(),Integer.parseInt(type));

            }

        }
        if(list != null && list.size() > 0){
            str += "成功解析【" + list.size() +"】条数据；";
            int listSize=list.size();
            int toIndex=1000;
            for(int i = 0;i<list.size();i+=1000) {
                //作用为toIndex最后没有1000条数据则剩余几条newList中就装几条
                if (i + 1000 > listSize) {
                    toIndex = listSize - i;
                }
                List newList = list.subList(i, i + toIndex);
                service.insertList(newList);
            }
            //去除重复
            service.deleteRepeat();
            //加入房屋表
            int num = 0;
            num = service.insertHousingInfo();
            str += "成功加入【" + num +"】条数据到房屋表；";
            //加入局站
            Map<String,Object> m = new HashMap<>();
            m = service.importHousing();
            str += "成功加入【" + m.get("insertNum") +"】条数据到局站表；";
            service.deleteAll();
        }

        if(StringUtils.isEmpty(str)){
            map.put("str","导入失败");
        }else {
            map.put("str",str);
        }
        return map;
    }

}
