package com.sccl.modules.business.oilexpense.service;

import com.sccl.common.lang.StringUtils;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol;
import com.sccl.modules.business.ammeterorprotocol.domain.AmmeterorprotocolRecord;
import com.sccl.modules.business.oilexpense.domain.OilExpense;
import com.sccl.modules.business.oilexpense.domain.OilExpenseRecord;
import com.sccl.modules.business.oilexpense.mapper.OilExpenseMapper;
import com.sccl.modules.business.order.domain.Order;
import com.sccl.modules.business.order.service.IOrderService;
import com.sccl.modules.monitor.operlog.domain.OperLog;
import com.sccl.modules.monitor.operlog.mapper.OperLogMapper;
import com.sccl.modules.uniflow.common.WFModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022/2/15 10:03
 **/
@Service
public class OilExpenseOrderServiceImpl extends BaseServiceImpl<Order> implements IOrderService {

    private static final Logger logger = LoggerFactory.getLogger(OilExpenseOrderServiceImpl.class);

    @Resource
    OilExpenseMapper oilExpenseMapper;
    @Autowired
    private OperLogMapper operLogMapper;

    // 提交流程
    @Override
    public void uniflowCallBack(WFModel wfModel) {
        if (StringUtils.isNotEmpty(wfModel.getCallbackType())
                && "PROCESS_STARTED".equals(wfModel.getCallbackType())
                && wfModel.getVariables().containsKey("firstNode")
                && wfModel.getVariables().get("firstNode").equals(true)) {
            if ("ADD_OIL_ENGINE".equals(wfModel.getBusiAlias())) {
                this.updateStatus(wfModel.getBusiId(), 1,wfModel.getProcInstId()); // 修改单据状态为流程中
            } else {
                this.updateStatus(wfModel.getBusiId(), 4); // 修改单据状态为修改流程中
            }
        } else if (StringUtils.isNotEmpty(wfModel.getCallbackType())
                && "PROCESS_CANCELLED".equals(wfModel.getCallbackType())) {
            // 只有驳回才更改状态，不同意时还是在流程中
            OilExpense oilExpense = oilExpenseMapper.selectById(wfModel.getBusiId());
            if (null != oilExpense.getBillStatus() && oilExpense.getBillStatus() == 1) {
                this.updateStatus(wfModel.getBusiId(), 0); // 修改单据状态为草稿
            } else if (null != oilExpense.getBillStatus() && oilExpense.getBillStatus() == 4) {
                this.updateStatus(wfModel.getBusiId(), 3); // 修改单据状态为修改中
            }
        }
        //***审批通过
        else if (StringUtils.isNotEmpty(wfModel.getCallbackType()) && "PROCESS_COMPLETED".equals(wfModel.getCallbackType())) {
//            try {
//                //修改油机时修改数据
//                this.updateDateByChange(wfModel.getBusiId(), wfModel.getBusiAlias(), wfModel.getApplyUserId());
//            } catch (Exception e) {
//                e.printStackTrace();
//                OperLog model = new OperLog();
//                model.setOperName("ammeter");
//                model.setTitle("换表流程回调更新旧表异常");
//                model.setMethod("updateDateByChange");
//                model.setErrorMsg("电表id:" + wfModel.getBusiId() + " 流程：" + wfModel.getBusiAlias() + " 用户：" + wfModel.getApplyUserId());
//                model.setOperTime(new Date());
//                operLogMapper.insert(model);
//                throw e;
//            }


            try {
                //修改单据状态为已完成并更新数据
                this.updateStatus(wfModel.getBusiId(), 2);
            } catch (Exception e) {
                e.printStackTrace();
                OperLog model = new OperLog();
                model.setOperName("ammeter");
                model.setTitle("油机新增流程回调修改单据状态为已完成并更新数据异常");
                model.setMethod("updateDateByChange");
                model.setErrorMsg("油机id:" + wfModel.getBusiId() + " 流程：" + wfModel.getBusiAlias() + " 用户：" + wfModel.getApplyUserId());
                model.setOperTime(new Date());
                operLogMapper.insert(model);
                throw e;
            }
        }
    }

    private void updateDateByChange(String id, String busiAlias, String applyUserId) {
        if (StringUtils.isNotEmpty(busiAlias) && ("EDIT_OIL_ENGINE".equals(busiAlias))) {
           //OilExpense oilExpense = new OilExpense();
           //oilExpense.setId(Long.parseLong(id));
            OilExpense oilExpense = oilExpenseMapper.selectById(id);//查询修改旧数据
                if (null != oilExpense) {
                    if (oilExpense.getBillStatus() == 4) {
                      //查询新数据
                        Long oldId = oilExpense.getId();
                        OilExpenseRecord newOilExpense = oilExpenseMapper.selectNewOilExpense(oilExpense.getId());
                        OilExpenseRecord oilExpenseRecord = new OilExpenseRecord();
                        BeanUtils.copyProperties(oilExpense,oilExpenseRecord);
                        oilExpenseRecord.setUpdatetime(newOilExpense.getUpdatetime());
                        oilExpenseRecord.setId(null);
                        oilExpenseRecord.setStatus(0);
                        oilExpenseRecord.setOilid(oldId);
                        oilExpenseRecord.setDelFlag("1");
                        //删除新表新数据
                        oilExpenseMapper.delNewOilExpense(oilExpense.getId());
                        oilExpenseMapper.insertRecord(oilExpenseRecord); //被修改的旧数据插入记录表
                        //修改油机数据为新数据

                        BeanUtils.copyProperties(newOilExpense,oilExpense);
                        oilExpense.setId(oldId);
                        oilExpense.setBillStatus(2);//审批通过
                        oilExpenseMapper.updateForModel(oilExpense);
                }
            }
        }
    }

    // 更新单据状态
    private void updateStatus(String id, int billStatus,Long procInstId) {
        OilExpense oilExpense = oilExpenseMapper.selectById(id);
        if (null != oilExpense) {
            oilExpense.setBillStatus(billStatus);
            oilExpense.setProcInstId(procInstId);
            oilExpenseMapper.updateForModel(oilExpense);
        } else {
            logger.error("没有找到对应的油机基础数据，或已被删除");
        }
    }
    // 更新单据状态
    private void updateStatus(String id, int billStatus) {
        OilExpense oilExpense = oilExpenseMapper.selectById(id);
        if (null != oilExpense) {
            oilExpense.setBillStatus(billStatus);
            oilExpenseMapper.updateForModel(oilExpense);
        } else {
            logger.error("没有找到对应的基础数据，或已被删除");
        }
    }
}
