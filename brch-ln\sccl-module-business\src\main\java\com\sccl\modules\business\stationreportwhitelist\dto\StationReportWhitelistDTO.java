package com.sccl.modules.business.stationreportwhitelist.dto;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 2024/4/9 16:23
 * @describe 一站多表白名单
 */
@Getter
@Setter
public class StationReportWhitelistDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 电表表号
     */
    @NotBlank(message = "电表表号不能为空")
    private String meterCode;

    /**
     * 对外结算类型
     */
    @NotBlank(message = "对外结算类型不能为空")
    private String dwjslx;

    /**
     * 附件业务id
     */
    private Long fileBusiId;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
