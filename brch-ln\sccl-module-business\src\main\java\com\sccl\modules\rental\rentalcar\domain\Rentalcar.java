package com.sccl.modules.rental.rentalcar.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;
import java.math.BigDecimal;


/**
 * 车辆 表 rentalcar
 * 
 * <AUTHOR>
 * @date 2019-08-23
 */
public class Rentalcar extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	private Long rcid;

	public Long getRcid() {
		return rcid;
	}

	public void setRcid(Long rcid) {
		this.rcid = rcid;
	}


	/**  车型 */
    private Long modelid;
    /** 供应商编号 */
    private String lifnr;
    /** 颜色 */
    private String color;
    /** 车牌号 */
    private String vin;
    /** 车架号 */
    private String platenumbers;
    /** 卡号iccid */
    private String iccid;
    /** 设备id */
    private String deviceid;
    /** 租期 */
    private String rentalterm;
    /** 交车时间 */
    private Date handovertime;
    /** 退租时间 */
    private Date terminatetime;
    /** 月租金 */
    private BigDecimal rentmonthly;
    /** 保险购置时间 */
    private Date insurancebegin;
    /**  */
    private Long inputuserid;
    /**  */
    private Date inputdate;
    /**  */
    private String inputusername;
    /**  */
    private BigDecimal iprocessinstid;
    /**  */
    private String status;
    /**  */
    private String company;
    /**  */
    private String country;
    /**  */
    private String memo;
    /** mainid */
    private Long rcmid;


	public void setModelid(Long modelid)
	{
		this.modelid = modelid;
	}

	public Long getModelid()
	{
		return modelid;
	}

	public void setLifnr(String lifnr)
	{
		this.lifnr = lifnr;
	}

	public String getLifnr() 
	{
		return lifnr;
	}

	public void setColor(String color)
	{
		this.color = color;
	}

	public String getColor() 
	{
		return color;
	}

	public void setVin(String vin)
	{
		this.vin = vin;
	}

	public String getVin() 
	{
		return vin;
	}

	public void setPlatenumbers(String platenumbers)
	{
		this.platenumbers = platenumbers;
	}

	public String getPlatenumbers() 
	{
		return platenumbers;
	}

	public void setIccid(String iccid)
	{
		this.iccid = iccid;
	}

	public String getIccid() 
	{
		return iccid;
	}

	public void setDeviceid(String deviceid)
	{
		this.deviceid = deviceid;
	}

	public String getDeviceid() 
	{
		return deviceid;
	}

	public void setRentalterm(String rentalterm)
	{
		this.rentalterm = rentalterm;
	}

	public String getRentalterm() 
	{
		return rentalterm;
	}

	public void setHandovertime(Date handovertime)
	{
		this.handovertime = handovertime;
	}

	public Date getHandovertime() 
	{
		return handovertime;
	}

	public void setTerminatetime(Date terminatetime)
	{
		this.terminatetime = terminatetime;
	}

	public Date getTerminatetime() 
	{
		return terminatetime;
	}

	public void setRentmonthly(BigDecimal rentmonthly)
	{
		this.rentmonthly = rentmonthly;
	}

	public BigDecimal getRentmonthly() 
	{
		return rentmonthly;
	}

	public void setInsurancebegin(Date insurancebegin)
	{
		this.insurancebegin = insurancebegin;
	}

	public Date getInsurancebegin() 
	{
		return insurancebegin;
	}

	public void setInputuserid(Long inputuserid)
	{
		this.inputuserid = inputuserid;
	}

	public Long getInputuserid()
	{
		return inputuserid;
	}

	public void setInputdate(Date inputdate)
	{
		this.inputdate = inputdate;
	}

	public Date getInputdate() 
	{
		return inputdate;
	}

	public void setInputusername(String inputusername)
	{
		this.inputusername = inputusername;
	}

	public String getInputusername() 
	{
		return inputusername;
	}

	public void setIprocessinstid(BigDecimal iprocessinstid)
	{
		this.iprocessinstid = iprocessinstid;
	}

	public BigDecimal getIprocessinstid() 
	{
		return iprocessinstid;
	}

	public void setStatus(String status)
	{
		this.status = status;
	}

	public String getStatus() 
	{
		return status;
	}

	public void setCompany(String company)
	{
		this.company = company;
	}

	public String getCompany()
	{
		return company;
	}

	public void setCountry(String country)
	{
		this.country = country;
	}

	public String getCountry()
	{
		return country;
	}

	public void setMemo(String memo)
	{
		this.memo = memo;
	}

	public String getMemo() 
	{
		return memo;
	}

	public void setRcmid(Long rcmid)
	{
		this.rcmid = rcmid;
	}

	public Long getRcmid()
	{
		return rcmid;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("rcid", getRcid())
            .append("modelid", getModelid())
            .append("lifnr", getLifnr())
            .append("color", getColor())
            .append("vin", getVin())
            .append("platenumbers", getPlatenumbers())
            .append("iccid", getIccid())
            .append("deviceid", getDeviceid())
            .append("rentalterm", getRentalterm())
            .append("handovertime", getHandovertime())
            .append("terminatetime", getTerminatetime())
            .append("rentmonthly", getRentmonthly())
            .append("insurancebegin", getInsurancebegin())
            .append("inputuserid", getInputuserid())
            .append("inputdate", getInputdate())
            .append("inputusername", getInputusername())
            .append("iprocessinstid", getIprocessinstid())
            .append("status", getStatus())
            .append("company", getCompany())
            .append("country", getCountry())
            .append("memo", getMemo())
            .append("rcmid", getRcmid())
            .toString();
    }

	private String modelname;
	private String lifnrname;
	private String companyname;
	private String setitle;// 用于查询
	private Date inputdateStart;// 用于查询
	private Date inputdateEnd;// 用于查询

	public Date getInputdateStart() {
		return inputdateStart;
	}

	public void setInputdateStart(Date inputdateStart) {
		this.inputdateStart = inputdateStart;
	}

	public Date getInputdateEnd() {
		return inputdateEnd;
	}

	public void setInputdateEnd(Date inputdateEnd) {
		this.inputdateEnd = inputdateEnd;
	}

	public String getSetitle() {
		return setitle;
	}

	public void setSetitle(String setitle) {
		this.setitle = setitle;
	}

	public String getCompanyname() {
		return companyname;
	}

	public void setCompanyname(String companyname) {
		this.companyname = companyname;
	}

	public String getModelname() {
		return modelname;
	}

	public void setModelname(String modelname) {
		this.modelname = modelname;
	}

	public String getLifnrname() {
		return lifnrname;
	}

	public void setLifnrname(String lifnrname) {
		this.lifnrname = lifnrname;
	}
}
