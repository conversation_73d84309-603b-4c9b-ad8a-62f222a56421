package com.sccl.modules.business.meterotherdatesfortwoc.mapper;

import com.sccl.modules.business.meterotherdatesfortwoc.domain.MeterOtherDatesfortwoc;
import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.twoc.domain.TwoCFlag;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 双碳接口其它能耗数据 数据层
 * 
 * <AUTHOR>
 * @date 2023-04-17
 */
public interface MeterOtherDatesfortwocMapper extends BaseMapper<MeterOtherDatesfortwoc>
{


    Integer countForMeterinfoAllFail();

    List<? extends TwoCFlag> selectMeterInfo(@Param("id") Long id, @Param("offset") int offset,
                                             @Param("size") int syncsum);

    void updateForMeterInfoAll(MeterOtherDatesfortwoc infoDb);
}