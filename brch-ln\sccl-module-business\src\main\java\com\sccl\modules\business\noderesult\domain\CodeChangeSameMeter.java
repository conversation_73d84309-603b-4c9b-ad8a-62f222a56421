package com.sccl.modules.business.noderesult.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CodeChangeSameMeter {
    /**
     * 电表id
     */
    private Long ammeterId;
    /**
     * 能耗站址
     */
    private Long stationcode;
    /**
     * 历史表 能耗站址
     */
    private long rStationcode;
    /**
     * 历史表 集团站址
     */
    private String jStationcode;
    /**
     * 历史表 站址创建时间
     */
    private LocalDateTime createTime;

    public Long getAmmeterId() {
        return ammeterId;
    }

    public void setAmmeterId(Long ammeterId) {
        this.ammeterId = ammeterId;
    }

    public Long getStationcode() {
        return stationcode;
    }

    public void setStationcode(Long stationcode) {
        this.stationcode = stationcode;
    }

    public long getrStationcode() {
        return rStationcode;
    }

    public void setrStationcode(long rStationcode) {
        this.rStationcode = rStationcode;
    }

    public String getjStationcode() {
        return jStationcode;
    }

    public void setjStationcode(String jStationcode) {
        this.jStationcode = jStationcode;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
}
