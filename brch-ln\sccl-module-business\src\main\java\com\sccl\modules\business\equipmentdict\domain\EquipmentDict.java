package com.sccl.modules.business.equipmentdict.domain;

import com.sccl.framework.aspectj.lang.annotation.Excel;
import com.sccl.framework.web.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;


/**
 * 铁塔站址设备字典表 tower_equipment_dict
 *
 * <AUTHOR>
 * @date 2022-08-09
 */
@Getter
@Setter
@ToString
public class EquipmentDict extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 组Id，同一组属于同一个设备
     */
    @Excel(name = "设备组Id（请勿修改）", isAllowEdit = false)
    private Long groupId;
    /**
     * 设备型号
     */
    private String model;
    /**
     * 设备类型
     */
    private String type;
    /**
     * 设备厂家
     */
    private String factory;
    /**
     * 每日参考能耗 KWh/日
     */
    private BigDecimal energyCostPerDay;
    /**
     * 版本号
     */
    @Excel(name = "版本号（请勿修改）",isAllowEdit = false)
    private Long version;

}
