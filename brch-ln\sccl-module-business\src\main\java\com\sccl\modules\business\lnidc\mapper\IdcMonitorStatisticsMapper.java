package com.sccl.modules.business.lnidc.mapper;

import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.lnidc.domain.IdcMonitorStatistics;
import com.sccl.modules.business.lnidc.domain.IdcMonitorStatisticsBo;
import com.sccl.modules.business.lnidc.domain.IdcMonitorStatisticsVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IdcMonitorStatisticsMapper extends BaseMapper<IdcMonitorStatistics> {

    List<IdcMonitorStatisticsVo> selfIdcEnergyList(@Param("bo") IdcMonitorStatisticsBo bo);
}