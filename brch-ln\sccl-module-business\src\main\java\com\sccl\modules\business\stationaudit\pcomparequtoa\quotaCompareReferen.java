package com.sccl.modules.business.stationaudit.pcomparequtoa;


import com.enrising.dcarbon.audit.AbstractReferee;
import com.enrising.dcarbon.audit.Auditable;
import com.enrising.dcarbon.audit.RefereeDatasource;
import com.enrising.dcarbon.audit.RefereeResult;
import com.sccl.modules.business.stationaudit.msshistory.HistoryResult;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class quotaCompareReferen extends AbstractReferee {
    public quotaCompareReferen(String refereeName) {
        super(refereeName);
    }

    @Override
    public RefereeResult doReferee(RefereeResult lastRefereeResult, Auditable auditable, Map<Class<?
            extends RefereeDatasource>, List<RefereeDatasource>> refereeDatasourceList) {
        //取出数据
        List<RefereeDatasource> refereeDatasources = refereeDatasourceList.get(quotaCompareContent.class);
        if (refereeDatasources==null)
            return null;
        List<RefereeDatasource> list = refereeDatasources
                                                            .stream().filter(Objects::nonNull).collect(Collectors.toList());

        //放数据
        HistoryResult result = new HistoryResult();
        result.setTopic("网管日均电量比对");
        result.setList(list);
        return result;
    }

}
