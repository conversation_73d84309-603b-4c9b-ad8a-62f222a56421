package com.sccl.modules.device.gwx.controller;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSONObject;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.domain.AjaxResult;

import io.swagger.annotations.ApiOperation;

/**
 *  信息操作处理
 * 
 * <AUTHOR>
 * @date 2019-03-11
 */
@Controller
@RequestMapping("/api/gwx")
public class GwxController extends BaseController
{
	  /**
     * FTTX查询光网络箱
     */
	@ApiOperation(value="FTTX接口查询光网络箱信息",
			notes="入参:<br/>"+"localid:6位本地网ID:<br/>" +
					"id:光网络箱ID")
    @GetMapping("/get")
    @ResponseBody
    public AjaxResult getFttxGwxResult(@PathVariable("localid") String localid,@PathVariable("id") String id)
    {
		Map<String, String> map=new HashMap<String, String>();
		map.put("key1", "111");
        Object object = JSONObject.toJSON(map);

        return this.success(object);
    }
	
}
