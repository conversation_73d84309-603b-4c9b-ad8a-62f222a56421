package com.sccl.modules.business.stationequipment.service;

import com.sccl.framework.service.IBaseService;
import com.sccl.modules.business.budget.domain.PageBean;
import com.sccl.modules.business.stationequipment.domain.StationEquipment;
import com.sccl.modules.business.stationequipment.domain.TowerStationEquipment2;
import com.sccl.modules.business.temporarytower.domain.TemporaryTower;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;
import com.sccl.modules.system.attachments.domain.Attachments;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;

/**
 * 铁塔站址设备 服务层
 *
 * <AUTHOR>
 * @date 2022-08-09
 */
public interface IStationEquipmentService extends IBaseService<StationEquipment> {
    StationEquipment getLatestByGroupId(long groupId);

    List<TowerStationEquipment2> getAllLatestByGroupId(List<Long> groupIds);

    List<StationEquipment> listLatest();

    List<TowerStationEquipment2> listLatestConditional(TowerStationEquipment2 stationEquipment);

    List<StationEquipment> listHistoryByGroupId(long groupId);

    int deleteByGroupId(long groupId);

    Attachments exportExcel(List<TowerStationEquipment2> stationEquipments, Map<String, String> columnMap, Map<String, String> promptMap);

    int insertList2(List<TowerStationEquipment2> content);

    List<TowerStationEquipment2> selectList1(TowerStationEquipment2 towerStationEquipment2s);
    List<TowerStationEquipment2> selectList2(TowerStationEquipment2 towerStationEquipment2s);
    List<TowerStationEquipment2> selectList3(TowerStationEquipment2 towerStationEquipment2s);
    Long selectList3num(TowerStationEquipment2 towerStationEquipment2s);
    /**
     * 依据 towerStationEquipment2 计算 对应账期 实际功率
     * @param towerStationEquipment2
     * @return
     */
    BigDecimal calcActual(TowerStationEquipment2 towerStationEquipment2);

    /**
     * @param towerStationEquipment2 计算对应 站址 标准功率
     * @return
     */
    BigDecimal caclStandard(TowerStationEquipment2 towerStationEquipment2);

    /**
     * 根据报账 单号 查询最近一条 站址设备 信息
     * @param id
     * @return
     */
    TowerStationEquipment2 getNewestStation(Long id);

    /**
     *报账时，根据报账信息 生成 站址评级 信息，并插入
     *  @param mssAccountbill
     * @return
     */
    String generateGrade(MssAccountbill mssAccountbill);


    /**
     * @param list 批量插入
     * @param i
     */
    void batchSql(List<TowerStationEquipment2> list, int i);

    /**
     * get all
     * @return
     * @param pageBean
     */
    List<TowerStationEquipment2> getAll(PageBean pageBean);

    /**
     * 计算 事前 站址设备模型 标准
     * @param set
     * @param days
     * @return
     */
    BigDecimal caclStandardBefore(TreeSet<StationEquipment> set, long days);

    /**
     * 导入
     * @param sheet1
     * @param inputStream
     * @return
     */
    List<TowerStationEquipment2> importExcel(String sheet1, InputStream inputStream) throws IOException, InvalidFormatException;

    int deleteRepeat();

    int insertTowerInfo();

    int deleteAll();

    int insertList2_Temporary(List newList);
    String selCity(String orgcode);

    String selCity2(String company);
}
