package com.sccl.modules.business.powerlumpprice.service;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.business.powerlumpprice.mapper.PowerLumppriceMapper;
import com.sccl.modules.system.organization.domain.Organization;
import com.sccl.modules.system.organization.mapper.OrganizationMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sccl.modules.business.powerlumpprice.domain.PowerLumpprice;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 铁塔包干单价维护 服务层实现
 * 
 * <AUTHOR>
 * @date 2019-06-19
 */
@Service
public class PowerLumppriceServiceImpl extends BaseServiceImpl<PowerLumpprice> implements IPowerLumppriceService
{

    @Autowired
    private PowerLumppriceMapper powerLumppriceMapper;
    @Autowired
    private OrganizationMapper organizationMapper;

    @Override
    public List<Map<String,Object>> selectByList(PowerLumpprice powerLumpprice){
        return powerLumppriceMapper.selectByList(powerLumpprice);
    }
    @Override
    public Map<String,Object> selectById(Long id){
        Map<String,Object> params = new HashMap<>();
        params.put("id",id);
        return powerLumppriceMapper.selectById( params);
    }
    @Override
    public List<Map<String,Object>> checkDate(PowerLumpprice powerLumpprice){
        Map<String,Object> params = new HashMap<>();
        params.put("startdate",powerLumpprice.getStartdate());
        params.put("enddate",powerLumpprice.getEnddate());
        params.put("countrys",this.getCountryByCountry(powerLumpprice.getOrgid()));
        return powerLumppriceMapper.checkDate( params);
    }
    //通过部门查询所有下级部门
    private List<Organization> getCountryByCountry(Long country){
        List<Organization> result = new ArrayList<>();
        List<Organization> orgs = organizationMapper.selectOrgByCompany(country.toString());//查询当前部门
        result.addAll(orgs);
        for (Organization org: orgs) {
            if(!"0".equals(org.getChildNum())){//有下级部门
                result.addAll(organizationMapper.selectSubordinateOrganization(org.getId().toString()));
            }else{//判断是否有上级部门
                result.addAll(organizationMapper.selectOrgByCompany(org.getParentId().toString()));
            }
        }
        return result;
    }
}
