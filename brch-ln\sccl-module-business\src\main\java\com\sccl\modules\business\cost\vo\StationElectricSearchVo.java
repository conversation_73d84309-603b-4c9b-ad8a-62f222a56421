package com.sccl.modules.business.cost.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 局站业务电量查询 一览 查询条件对象
 */
@Data
public class StationElectricSearchVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 当前记录起始索引
     */
    private Integer pageNum;
    /**
     * 每页显示记录数
     */
    private Integer pageSize;

    /**
     * 市局编码
     */
    private String cityCode;

    /**
     * 区县编码
     */
    private String countyCode;

    /**
     * 区县名称
     */
    private String countryName;

    /**
     * 关键字 局站名称模糊查询
     */
    private String station;

    /**
     * 编码查询 局站编码/5gr站址编码/资源编码
     */
    private String stationCode;

    /**
     * 统计月份开始【yyyy-MM】
     */
    private String tjyfks;

    /**
     * 统计月份结束【yyyy-MM】
     */
    private String tjyfjs;

    /**
     * 数据来源 1 无线大数据 2 智慧机房
     */
    private String source;
}
