package com.sccl.modules.mssaccount.mssgro.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;


/**
 * 财务辅助提供的组织级次 预算责任中心表 MSS_GRO
 * 
 * <AUTHOR>
 * @date 2019-05-01
 */
public class MssGro extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
	private String orgcode;
    public String getOrgcode() {
		return orgcode;
	}

	public void setOrgcode(String orgcode) {
		this.orgcode = orgcode;
	}

	/**  */
    private String orgname;
    /**  */
    private String parorgcode;
    /**  */
    private String company;
    /**  */
    private String typecode;
    /**  */
    private String typename;
    /**  */
    private String status;
    /**  */
    private String sscb;
    /**  */
    private String sslr;
    /**  */
    private String sslrz;
    /**  */
    private String cxcb;
    /**  */
    private String cxlr;
    /**  */
    private String cxlrz;
    /**  */
    private String wfcb;
    /**  */
    private String wflr;
    /**  */
    private String wflrz;


	public void setOrgname(String orgname)
	{
		this.orgname = orgname;
	}

	public String getOrgname() 
	{
		return orgname;
	}

	public void setParorgcode(String parorgcode)
	{
		this.parorgcode = parorgcode;
	}

	public String getParorgcode() 
	{
		return parorgcode;
	}

	public void setCompany(String company)
	{
		this.company = company;
	}

	public String getCompany() 
	{
		return company;
	}

	public void setTypecode(String typecode)
	{
		this.typecode = typecode;
	}

	public String getTypecode() 
	{
		return typecode;
	}

	public void setTypename(String typename)
	{
		this.typename = typename;
	}

	public String getTypename() 
	{
		return typename;
	}

	public void setStatus(String status)
	{
		this.status = status;
	}

	public String getStatus() 
	{
		return status;
	}

	public void setSscb(String sscb)
	{
		this.sscb = sscb;
	}

	public String getSscb() 
	{
		return sscb;
	}

	public void setSslr(String sslr)
	{
		this.sslr = sslr;
	}

	public String getSslr() 
	{
		return sslr;
	}

	public void setSslrz(String sslrz)
	{
		this.sslrz = sslrz;
	}

	public String getSslrz() 
	{
		return sslrz;
	}

	public void setCxcb(String cxcb)
	{
		this.cxcb = cxcb;
	}

	public String getCxcb() 
	{
		return cxcb;
	}

	public void setCxlr(String cxlr)
	{
		this.cxlr = cxlr;
	}

	public String getCxlr() 
	{
		return cxlr;
	}

	public void setCxlrz(String cxlrz)
	{
		this.cxlrz = cxlrz;
	}

	public String getCxlrz() 
	{
		return cxlrz;
	}

	public void setWfcb(String wfcb)
	{
		this.wfcb = wfcb;
	}

	public String getWfcb() 
	{
		return wfcb;
	}

	public void setWflr(String wflr)
	{
		this.wflr = wflr;
	}

	public String getWflr() 
	{
		return wflr;
	}

	public void setWflrz(String wflrz)
	{
		this.wflrz = wflrz;
	}

	public String getWflrz() 
	{
		return wflrz;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("orgcode", getOrgcode())
            .append("orgname", getOrgname())
            .append("parorgcode", getParorgcode())
            .append("company", getCompany())
            .append("typecode", getTypecode())
            .append("typename", getTypename())
            .append("status", getStatus())
            .append("sscb", getSscb())
            .append("sslr", getSslr())
            .append("sslrz", getSslrz())
            .append("cxcb", getCxcb())
            .append("cxlr", getCxlr())
            .append("cxlrz", getCxlrz())
            .append("wfcb", getWfcb())
            .append("wflr", getWflr())
            .append("wflrz", getWflrz())
            .toString();
    }
}
