package com.sccl.modules.rental.rentalordercarmodel.domain;

import com.sccl.framework.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * 车型数据表 rentalorder_carmodel
 *
 * <AUTHOR>
 * @date 2019-08-23
 */
public class RentalorderCarmodel extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 数量
     */
    private Long ordernum;
    /**
     * 订单id
     */
    private Long rcoid;
    /**
     *
     */
    private Long modelid;
    /**
     * 车型type
     */
    private Long modeltype;


    public void setOrdernum(Long ordernum) {
        this.ordernum = ordernum;
    }

    public Long getOrdernum() {
        return ordernum;
    }

    public void setRcoid(Long rcoid) {
        this.rcoid = rcoid;
    }

    public Long getRcoid() {
        return rcoid;
    }

    public void setModelid(Long modelid) {
        this.modelid = modelid;
    }

    public Long getModelid() {
        return modelid;
    }

    public void setModeltype(Long modeltype) {
        this.modeltype = modeltype;
    }

    public Long getModeltype() {
        return modeltype;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("ordernum", getOrdernum())
                .append("rcoid", getRcoid())
                .append("modelid", getModelid())
                .append("modeltype", getModeltype())
                .toString();
    }

    private String modelname;
    private String color;
    private String rentalterm;
    private String memo;

    public String getModelname() {
        return modelname;
    }

    public void setModelname(String modelname) {
        this.modelname = modelname;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getRentalterm() {
        return rentalterm;
    }

    public void setRentalterm(String rentalterm) {
        this.rentalterm = rentalterm;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }
}
