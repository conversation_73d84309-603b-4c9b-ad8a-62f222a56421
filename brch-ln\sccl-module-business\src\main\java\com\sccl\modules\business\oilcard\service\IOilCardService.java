package com.sccl.modules.business.oilcard.service;

import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.business.oilcard.domain.OilCard;
import com.sccl.framework.service.IBaseService;
import com.sccl.modules.business.oilcard.domain.OilCardVo;
import com.sccl.modules.business.oilexpense.domain.OilExpense;

import java.util.List;

/**
 * 油卡 服务层
 *
 * <AUTHOR>
 * @date 2021-12-16
 */
public interface IOilCardService extends IBaseService<OilCard> {

    /** 删除油卡基础数据 */
    AjaxResult removeOilCard(String ids);

    List<OilCard> selectListViewCard(OilCard oilCard);

    List<OilCardVo> selectListAll(OilCard oilCard);
}
