package com.sccl.modules.business.jhanomalydetails.util;


import com.sccl.modules.business.jhanomalydetails.domain.*;
import com.sccl.modules.business.jhanomalydetails.mapper.JhAnomalyDetailsMapper;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletResponse;
import java.util.EnumSet;
import java.util.List;

public enum ErrorEnum {
    //局站与电表关联异常
    A(){
        @Override
        public void exportExcel(HttpServletResponse response,JhPowerErrorVO jhPowerErrorVO,JhAnomalyDetails jhAnomalyDetails) {
            JhExcelUtil.exportExcelToBrowser(response,jhAnomalyDetailsMapper.selectList(jhAnomalyDetails),
                    "局站与电表关联异常",jhPowerErrorVO.getTask(),JhStationErrorDTO.class);

        }

    },
    //电价合理性
    B(){
        @Override
        public void exportExcel(HttpServletResponse response,JhPowerErrorVO jhPowerErrorVO,JhAnomalyDetails jhAnomalyDetails)  {
            JhExcelUtil.exportExcelToBrowser(response,jhAnomalyDetailsMapper.selectList(jhAnomalyDetails),
                    "电价合理性",jhPowerErrorVO.getTask(),JhReasonableErrorDTO.class);

        }

    },
    //电表站址一致性异常查看
    C(){
        @Override
        public void exportExcel(HttpServletResponse response,JhPowerErrorVO jhPowerErrorVO,JhAnomalyDetails jhAnomalyDetails) {
            JhExcelUtil.exportExcelToBrowser(response,jhAnomalyDetailsMapper.selectList(jhAnomalyDetails),
                    "电表站址一致性异常",jhPowerErrorVO.getTask(),JhAddressErrorDTO.class);


        }

    },
    //支付对象一致性
    D(){
        @Override
        public void exportExcel(HttpServletResponse response,JhPowerErrorVO jhPowerErrorVO,JhAnomalyDetails jhAnomalyDetails) {
            JhExcelUtil.exportExcelToBrowser(response,jhAnomalyDetailsMapper.selectList(jhAnomalyDetails),
                    "支付对象一致性",jhPowerErrorVO.getTask(),JhPayErrorDTO.class);


        }
    },
    //台账周期连续性异常查看
    E(){
        @Override
        public void exportExcel(HttpServletResponse response,JhPowerErrorVO jhPowerErrorVO,JhAnomalyDetails jhAnomalyDetails)  {
            JhExcelUtil.exportExcelToBrowser(response,jhAnomalyDetailsMapper.selectList(jhAnomalyDetails),
                    "台账周期连续性异常",jhPowerErrorVO.getTask(),JhAccountErrorDTO.class);


        }

    },
    //电表度数连续性异常查看
    F(){
        @Override
        public void exportExcel(HttpServletResponse response,JhPowerErrorVO jhPowerErrorVO,JhAnomalyDetails jhAnomalyDetails)   {
            JhExcelUtil.exportExcelToBrowser(response,jhAnomalyDetailsMapper.selectList(jhAnomalyDetails),
                    "电表度数连续性异常",jhPowerErrorVO.getTask(),JhAmmeterErrorDTO.class);


        }

    },
    /** 台账电量合理性 */
    G(){
        @Override
        public void exportExcel(HttpServletResponse response,JhPowerErrorVO jhPowerErrorVO,JhAnomalyDetails jhAnomalyDetails) {


        }
    },
    //日均电量
    H(){
        @Override
        public void exportExcel(HttpServletResponse response,JhPowerErrorVO jhPowerErrorVO,JhAnomalyDetails jhAnomalyDetails)  {
            JhExcelUtil.exportExcelToBrowser(response,jhAnomalyDetailsMapper.selectList(jhAnomalyDetails),
                    "日均电量波动性异常",jhPowerErrorVO.getTask(),JhDailyElectricityDTO.class);


        }

    },
    /** 台账日均耗电量 */
    I(){
        @Override
        public void exportExcel(HttpServletResponse response,JhPowerErrorVO jhPowerErrorVO,JhAnomalyDetails jhAnomalyDetails) {
            JhExcelUtil.exportExcelToBrowser(response,jhAnomalyDetailsMapper.selectList(jhAnomalyDetails),
                    "日均耗电量波动性异常",jhPowerErrorVO.getTask(),JhDailyElectricityDTO.class);


        }
    },
    //共享站分摊比例
    J(){
        @Override
        public void exportExcel(HttpServletResponse response,JhPowerErrorVO jhPowerErrorVO,JhAnomalyDetails jhAnomalyDetails)  {
            JhExcelUtil.exportExcelToBrowser(response,jhAnomalyDetailsMapper.selectList(jhAnomalyDetails),
                    "共享站分摊比例异常",jhPowerErrorVO.getTask(),JhTogetherErrorDTO.class);

        }

    },
    //独享站分摊比例
    k(){
        @Override
        public void exportExcel(HttpServletResponse response,JhPowerErrorVO jhPowerErrorVO,JhAnomalyDetails jhAnomalyDetails)  {
            JhExcelUtil.exportExcelToBrowser(response,jhAnomalyDetailsMapper.selectList(jhAnomalyDetails),
                    "独享站分摊比例异常",jhPowerErrorVO.getTask(),JhAloneErrorDTO.class);

        }

    },

    //台账周期异常查看
    L(){
        @Override
        public void exportExcel(HttpServletResponse response,JhPowerErrorVO jhPowerErrorVO,JhAnomalyDetails jhAnomalyDetails)  {
            JhExcelUtil.exportExcelToBrowser(response,jhAnomalyDetailsMapper.selectList(jhAnomalyDetails),
                    "台账周期异常",jhPowerErrorVO.getTask(),JhAccountWeekErrorDTO.class);

        }

    };



    public JhAnomalyDetailsMapper jhAnomalyDetailsMapper;

    public void setErrorDetailMapper(JhAnomalyDetailsMapper jhAnomalyDetailsMapper){
        this.jhAnomalyDetailsMapper = jhAnomalyDetailsMapper;
    }


    /**
     * 依赖注入
     * */

    @Data
    @Component
    static  class ErrorMapperContainer{
        @Autowired
        private JhAnomalyDetailsMapper jhAnomalyDetailsMapper;

        @PostConstruct
        public void postConstruct(){
            for (ErrorEnum tempEnum : EnumSet.allOf(ErrorEnum.class)) {
                tempEnum.setErrorDetailMapper(jhAnomalyDetailsMapper);
            }

        }


    }


    public abstract void exportExcel(HttpServletResponse response, JhPowerErrorVO jhPowerErrorVO,JhAnomalyDetails jhAnomalyDetails);




}
