package com.sccl.modules.business.powerammeterprice.controller;

import com.alibaba.fastjson.JSONObject;
import com.sccl.common.support.Convert;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.powerammeterprice.domain.PowerAmmeterPrice;
import com.sccl.modules.business.powerammeterprice.service.IPowerAmmeterPriceService;
import com.sccl.modules.mssaccount.mssinterface.domain.PowerElePriceItem;
import com.sccl.modules.mssaccount.mssinterface.service.MssJsonClient;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 电价上传 信息操作处理
 *
 * <AUTHOR>
 * @date 2022-09-18
 */
@RestController
@RequestMapping("/business/powerAmmeterPrice")
public class PowerAmmeterPriceController extends BaseController {
    @Autowired
    MssJsonClient mssJsonClient;
    private String prefix = "business/powerAmmeterPrice";
    @Autowired
    private IPowerAmmeterPriceService powerAmmeterPriceService;

    @RequiresPermissions("business:powerAmmeterPrice:view")
    @GetMapping()
    public String powerAmmeterPrice() {
        return prefix + "/powerAmmeterPrice";
    }

    /**
     * 查询电价上传列表
     */
    @RequiresPermissions("business:powerAmmeterPrice:list")
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(PowerAmmeterPrice powerAmmeterPrice) {

        String year = "";
        String month = "";
        String dateflag = "1";
        int countupjted = 0;
        int countinited = 0;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");


        List<PowerAmmeterPrice> list = null;
        if (powerAmmeterPrice.getFlag().equals("1"))
            powerAmmeterPrice.setDirectsupplyflag(1);
        if (powerAmmeterPrice.getFlag().equals("2"))
            powerAmmeterPrice.setDirectsupplyflag(2);
        if (powerAmmeterPrice.getFlag().equals("3"))
            powerAmmeterPrice.setDirectsupplyflag(1);
        if (powerAmmeterPrice.getFlag().equals("4"))
            powerAmmeterPrice.setDirectsupplyflag(2);
        if (powerAmmeterPrice.getStartDate() != null && powerAmmeterPrice.getStartDate().length() > 0) {
            year = powerAmmeterPrice.getStartDate().substring(0, 4);
            month = powerAmmeterPrice.getStartDate().substring(5, 7);
            try {
                Date specdate = sdf.parse(powerAmmeterPrice.getStartDate() + "-" + "01");

                Calendar cal_1 = Calendar.getInstance();//获取当前日期
                cal_1.setTime(new Date());
                cal_1.set(Calendar.DAY_OF_MONTH, 1);
                Date nowdate = sdf.parse(sdf.format(cal_1.getTime()));
                if (specdate.compareTo(nowdate) > 0) {
                    System.out.println("specdate 时间在 nowdate 之后");
                    dateflag = "1";
                } else if (specdate.compareTo(nowdate) < 0) {
                    System.out.println("specdate 时间在 nowdate 之前");
                    dateflag = "0";
                } else {
                    System.out.println("specdate 时间在 nowdate 相等");
                    dateflag = "0";
                }
            } catch (ParseException e) {
                e.printStackTrace();
            }
            powerAmmeterPrice.setYear(year);
            powerAmmeterPrice.setMonth(month);
            powerAmmeterPrice.setStatus("3");
            if (powerAmmeterPrice.getFlag().equals("1") || powerAmmeterPrice.getFlag().equals("2"))
                countupjted = powerAmmeterPriceService.countPricestatus(powerAmmeterPrice);
            if (powerAmmeterPrice.getFlag().equals("3") || powerAmmeterPrice.getFlag().equals("4"))
                countupjted = powerAmmeterPriceService.countPricetastatus(powerAmmeterPrice);
            powerAmmeterPrice.setStatus("1");
            if (powerAmmeterPrice.getFlag().equals("1") || powerAmmeterPrice.getFlag().equals("2"))
                countinited = powerAmmeterPriceService.countPricestatus(powerAmmeterPrice);
            if (powerAmmeterPrice.getFlag().equals("3") || powerAmmeterPrice.getFlag().equals("4"))
                countinited = powerAmmeterPriceService.countPricetastatus(powerAmmeterPrice);
        }
        startPage();
        if (countupjted > 0) {
            powerAmmeterPrice.setStatus("3");
            list = powerAmmeterPriceService.selectbyList(powerAmmeterPrice);
        } else if (countinited > 0) {
            powerAmmeterPrice.setStatus("1");
            if (powerAmmeterPrice.getFlag().equals("1") || powerAmmeterPrice.getFlag().equals("2"))
                list = powerAmmeterPriceService.selectbyList(powerAmmeterPrice);
            if (powerAmmeterPrice.getFlag().equals("3") || powerAmmeterPrice.getFlag().equals("4"))
                list = powerAmmeterPriceService.selecttabyList(powerAmmeterPrice);
        } else {
            if (dateflag.equals("1")) {
                if (powerAmmeterPrice.getFlag().equals("1") || powerAmmeterPrice.getFlag().equals("2"))
                    powerAmmeterPriceService.priceinit(powerAmmeterPrice);
                if (powerAmmeterPrice.getFlag().equals("3") || powerAmmeterPrice.getFlag().equals("4"))
                    powerAmmeterPriceService.priceinitta(powerAmmeterPrice);
            }
            powerAmmeterPrice.setStatus("1");
            if (powerAmmeterPrice.getFlag().equals("1") || powerAmmeterPrice.getFlag().equals("2"))
                list = powerAmmeterPriceService.selectbyList(powerAmmeterPrice);
            if (powerAmmeterPrice.getFlag().equals("3") || powerAmmeterPrice.getFlag().equals("4"))
                list = powerAmmeterPriceService.selecttabyList(powerAmmeterPrice);

        }
        return getDataTable(list);
    }

    public String getFisrtDayOfMonth(int year, int month) {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR, year);
        //设置月份
        cal.set(Calendar.MONTH, month - 1);
        //获取某月最小天数
        int firstDay = cal.getActualMinimum(Calendar.DAY_OF_MONTH);
        //设置日历中月份的最小天数
        cal.set(Calendar.DAY_OF_MONTH, firstDay);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String firstDayOfMonth = sdf.format(cal.getTime());
        return firstDayOfMonth;
    }

    /**
     * 新增电价上传
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存电价上传
     */
    @RequiresPermissions("business:powerAmmeterPrice:add")
    @Log(title = "电价上传", action = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody PowerAmmeterPrice powerAmmeterPrice) {
        return toAjax(powerAmmeterPriceService.insert(powerAmmeterPrice));
    }

    /**
     * 修改电价上传
     */
    @GetMapping("/edit/{id}")
    public AjaxResult edit(@PathVariable("id") Long id) {
        PowerAmmeterPrice powerAmmeterPrice = powerAmmeterPriceService.get(id);

        Object object = JSONObject.toJSON(powerAmmeterPrice);

        return this.success(object);
    }

    /**
     * 修改保存电价上传
     */
    @RequiresPermissions("business:powerAmmeterPrice:edit")
    @Log(title = "电价上传", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody List<PowerAmmeterPrice> powerAmmeterPrice) {

        AjaxResult ar = new AjaxResult();
        try {
            ar.put("data", powerAmmeterPriceService.update(powerAmmeterPrice));
            //alertControlService.updateMatchs(alertObj);
            ar.put("flag", "1");
            ar.put("msg", "修改成功");
        } catch (Exception e) {
            e.printStackTrace();
            ar.put("flag", "0");
            ar.put("msg", "修改失败" + e.getMessage());

        }


        return ar;

    }

    /**
     * 删除电价上传
     */
    @RequiresPermissions("business:powerAmmeterPrice:remove")
    @Log(title = "电价上传", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(powerAmmeterPriceService.deleteByIds(Convert.toStrArray(ids)));
    }


    /**
     * 查看电价上传
     */
    @RequiresPermissions("business:powerAmmeterPrice:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id) {
        PowerAmmeterPrice powerAmmeterPrice = powerAmmeterPriceService.get(id);

        Object object = JSONObject.toJSON(powerAmmeterPrice);

        return this.success(object);
    }

    @PostMapping("/sendprice")
    @ResponseBody
    public AjaxResult sendprice(String ids) {
        int i = 0;
        try {
            List<PowerElePriceItem> item = powerAmmeterPriceService.selectListByIds(Convert.toStrArray(ids));
            String rtl = powerAmmeterPriceService.sendPricedetailInfo(item);
/*			for (PowerAmmeterPrice towerData : tdlist) {
				if (towerData != null )
				{

					System.out.println(JSON.toJSON(tdlist));
					//System.out.println("nh percent "+map.get("percent").toString()+"ApportionmentRatio "+towerData
					.getApportionmentRatio());
					//System.out.println("TowerSiteCode:"+towerData.getTowerSiteCode()+" ,UseEndTime:"+towerData
					.getUseEndTime()+"nh lst end date"+map.get("startdate").toString() +" ,getUseStartTime:"+towerData
					.getUseStartTime()+"nh lst enddate"+map.get("enddate").toString() + " ,UseEndTime:"+towerData
					.getUseEndTime()+" ,UseStartDegree:"+towerData.getUseStartDegree() +"nh lst prevtotalreadings"+map
					.get("prevtotalreadings").toString()+towerData.getUseEndDegree() +"nh lst prevtotalreadings"+map
					.get("curtotalreadings").toString());
				}
				i++;
			}*/


            return this.success("上传电价 (" + rtl + ")条");
        } catch (Exception e) {
            e.printStackTrace();
            return this.error(1, "上传电价失败:" + e.getMessage());
        }
    }

    @PostMapping("/searchprice")
    @ResponseBody
    public AjaxResult searchprice(String ids) {
        int i = 0;
        try {
            List<PowerAmmeterPrice> item = powerAmmeterPriceService.selectListapByIds(Convert.toStrArray(ids));
            AjaxResult rtl = powerAmmeterPriceService.searchPricedetailInfo(item);
/*			for (PowerAmmeterPrice towerData : tdlist) {
				if (towerData != null )
				{

					System.out.println(JSON.toJSON(tdlist));
					//System.out.println("nh percent "+map.get("percent").toString()+"ApportionmentRatio "+towerData
					.getApportionmentRatio());
					//System.out.println("TowerSiteCode:"+towerData.getTowerSiteCode()+" ,UseEndTime:"+towerData
					.getUseEndTime()+"nh lst end date"+map.get("startdate").toString() +" ,getUseStartTime:"+towerData
					.getUseStartTime()+"nh lst enddate"+map.get("enddate").toString() + " ,UseEndTime:"+towerData
					.getUseEndTime()+" ,UseStartDegree:"+towerData.getUseStartDegree() +"nh lst prevtotalreadings"+map
					.get("prevtotalreadings").toString()+towerData.getUseEndDegree() +"nh lst prevtotalreadings"+map
					.get("curtotalreadings").toString());
				}
				i++;
			}*/


            return rtl;//this.success("查询电价 (" + rtl + ")");
        } catch (Exception e) {
            e.printStackTrace();
            return this.error(1, "查询电价失败:" + e.getMessage());
        }
    }


    @PostMapping("/sendpricebatch")
    @ResponseBody
    public AjaxResult sendpricebatch(String ids) {
        try {
            String realstr = "";
            if (ids.contains("-"))
                realstr = ids.replace("-", "");
            else
                realstr = ids;
            List<PowerElePriceItem> item = powerAmmeterPriceService.selectListall(realstr);

            String rtl = "";
            powerAmmeterPriceService.sendpricebatch(item);
            return this.success("上传电价 (" + rtl + ")条");
        } catch (Exception e) {
            e.printStackTrace();
            return this.error(1, "上传电价失败:" + e.getMessage());
        }
    }

    @GetMapping("/createPrice")
    @ResponseBody
    public AjaxResult createPrice(@RequestParam(required = false) String time) {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        if (!StringUtils.isBlank(time)) {
            String[] arr = time.split("/");
            year = Integer.parseInt(arr[0]);
            month = Integer.parseInt(arr[1]);
        }

        return powerAmmeterPriceService.createPrice(year, month);

    }
}
