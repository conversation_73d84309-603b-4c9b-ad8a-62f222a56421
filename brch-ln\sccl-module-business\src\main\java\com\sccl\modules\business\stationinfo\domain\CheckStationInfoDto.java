package com.sccl.modules.business.stationinfo.domain;

import com.sccl.framework.web.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * *****验证局站信息 - domain
 *
 * <AUTHOR>
 * @date 2022/4/22
 */
@Getter
@Setter
public class CheckStationInfoDto extends BaseEntity {
    /**
     * 服务起始时间
     */
    private String servestartdate;

    /**
     * 服务到期时间
     */
    private String serveenddate;

    /**
     * 局站状态
     */
    private String iftimeout;

    /** 局站编码 */
    private String stationaddr_code;

    private String type;

    private String ids;

    private Long fileid;

}
