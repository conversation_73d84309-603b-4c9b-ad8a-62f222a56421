package com.sccl.modules.mssaccount.mssconstractmain.service;

import com.sccl.modules.business.powermodel.entity.HistoryContractPricePro;
import com.sccl.modules.mssaccount.mssconstractmain.domain.HistoryContractPrice;
import com.sccl.modules.mssaccount.mssconstractmain.domain.MssConstractmain;
import com.sccl.framework.service.IBaseService;

import java.util.List;

/**
 * 合同 服务层
 * 
 * <AUTHOR>
 * @date 2019-05-01
 */
public interface IMssConstractmainService extends IBaseService<MssConstractmain>
{


    String selectContractPrice(String contractcode);

    String selectContractPricebyammeterid(String contractcode);

    List<HistoryContractPrice> selectHistoryContractPrice(HistoryContractPrice historyContractPrice);

    List<HistoryContractPricePro> selectHistoryContractPricePro(HistoryContractPricePro historyContractPrice);
}
