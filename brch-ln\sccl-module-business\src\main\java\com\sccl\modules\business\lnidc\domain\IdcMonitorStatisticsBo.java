package com.sccl.modules.business.lnidc.domain;


import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class IdcMonitorStatisticsBo {

    /**
     * 数据年份
     */
    private String year;

    /**
     * 需要保存的数据
     */
    private List<idcEnergy> items;

    /**
     * 地市
     */
    private String cityName;
    /**
     * 本省idc名称
     */
    private String idcName;

    /** 所属分公司 */
    private Long company;

    /** 责任中心 */
    private Long country;

    @Data
    public static class idcEnergy {

        /**
         * id
         */
        private Long id;

        /**
         * 修改的值
         */
        private String value;
    }


}
