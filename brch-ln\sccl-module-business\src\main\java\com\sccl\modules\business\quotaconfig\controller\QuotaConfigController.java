package com.sccl.modules.business.quotaconfig.controller;

import java.util.List;

import com.sccl.modules.business.alertcontrol.domain.AlertControl;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.business.quotaconfig.domain.QuotaConfig;
import com.sccl.modules.business.quotaconfig.service.IQuotaConfigService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 基站定额分公司设置 信息操作处理
 * 
 * <AUTHOR>
 * @date 2023-06-07
 */
@RestController
@RequestMapping("/business/quotaConfig")
public class QuotaConfigController extends BaseController
{
    private String prefix = "business/quotaConfig";
	
	@Autowired
	private IQuotaConfigService quotaConfigService;
	
	@RequiresPermissions("business:quotaConfig:view")
	@GetMapping()
	public String quotaConfig()
	{
	    return prefix + "/quotaConfig";
	}
	
	/**
	 * 查询基站定额分公司设置列表
	 */
	@RequiresPermissions("business:quotaConfig:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(QuotaConfig quotaConfig)
	{

		String ids=quotaConfigService.selectOrgAllChidren(quotaConfig);
		String countryIds=quotaConfigService.selectCountryAllChidren(quotaConfig);

 		startPage();
        List<QuotaConfig> list =  quotaConfigService.selectListCustomized(quotaConfig,ids,countryIds);
		return getDataTable(list);
	}
	
	/**
	 * 新增基站定额分公司设置
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存基站定额分公司设置
	 */
	@RequiresPermissions("business:quotaConfig:add")
	@Log(title = "基站定额分公司设置", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody QuotaConfig quotaConfig)
	{		
		return toAjax(quotaConfigService.insert(quotaConfig));
	}

	/**
	 * 修改基站定额分公司设置
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		QuotaConfig quotaConfig = quotaConfigService.get(id);

		Object object = JSONObject.toJSON(quotaConfig);

        return this.success(object);
	}
	
	/**
	 * 修改保存基站定额分公司设置
	 */
	@RequiresPermissions("business:quotaConfig:edit")
	@Log(title = "基站定额分公司设置", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody QuotaConfig quotaConfig)
	{		
		return toAjax(quotaConfigService.update(quotaConfig));
	}
	
	/**
	 * 删除基站定额分公司设置
	 */
	@RequiresPermissions("business:quotaConfig:remove")
	@Log(title = "基站定额分公司设置", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(quotaConfigService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看基站定额分公司设置
     */
    @RequiresPermissions("business:quotaConfig:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		QuotaConfig quotaConfig = quotaConfigService.get(id);

        Object object = JSONObject.toJSON(quotaConfig);

        return this.success(object);
    }

}
