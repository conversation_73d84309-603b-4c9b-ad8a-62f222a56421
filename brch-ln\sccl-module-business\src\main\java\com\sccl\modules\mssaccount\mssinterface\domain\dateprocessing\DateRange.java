package com.sccl.modules.mssaccount.mssinterface.domain.dateprocessing;

import java.time.LocalDate;

/**
 * 日期范围类
 * 用于封装开始日期和结束日期
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
public class DateRange {
    
    /**
     * 开始日期
     */
    private final LocalDate startDate;
    
    /**
     * 结束日期
     */
    private final LocalDate endDate;
    
    /**
     * 构造函数
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    public DateRange(LocalDate startDate, LocalDate endDate) {
        if (startDate == null || endDate == null) {
            throw new IllegalArgumentException("开始日期和结束日期不能为空");
        }
        if (startDate.isAfter(endDate)) {
            throw new IllegalArgumentException("开始日期不能晚于结束日期");
        }
        this.startDate = startDate;
        this.endDate = endDate;
    }
    
    /**
     * 获取开始日期
     * 
     * @return 开始日期
     */
    public LocalDate getStartDate() {
        return startDate;
    }
    
    /**
     * 获取结束日期
     * 
     * @return 结束日期
     */
    public LocalDate getEndDate() {
        return endDate;
    }
    
    /**
     * 计算日期范围的天数
     * 
     * @return 天数（包含开始和结束日期）
     */
    public long getDayCount() {
        return java.time.temporal.ChronoUnit.DAYS.between(startDate, endDate) + 1;
    }
    
    /**
     * 判断指定日期是否在范围内
     * 
     * @param date 要判断的日期
     * @return true如果在范围内，false否则
     */
    public boolean contains(LocalDate date) {
        return date != null && !date.isBefore(startDate) && !date.isAfter(endDate);
    }
    
    @Override
    public String toString() {
        return String.format("DateRange{startDate=%s, endDate=%s, days=%d}", 
            startDate, endDate, getDayCount());
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        DateRange dateRange = (DateRange) obj;
        return startDate.equals(dateRange.startDate) && endDate.equals(dateRange.endDate);
    }
    
    @Override
    public int hashCode() {
        return java.util.Objects.hash(startDate, endDate);
    }
}
