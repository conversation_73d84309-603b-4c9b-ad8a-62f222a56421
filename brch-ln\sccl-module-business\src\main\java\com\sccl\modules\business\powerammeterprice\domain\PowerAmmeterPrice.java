package com.sccl.modules.business.powerammeterprice.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 电价上传表 power_ammeter_price
 * 
 * <AUTHOR>
 * @date 2022-09-18
 */
public class PowerAmmeterPrice extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
    /** 上报年 */
    private String year;
    /** 上报月 */
    private String month;
    /** 电表编号 */
    private Long ammeterid;
    /** 对外结算 */
    private Integer directsupplyflag;
    /** 供电局电表编号 */
    private String supplybureauammetercode;
    /** 用电类型 */
    private Long electrotype;
    /** 单价 */
    private BigDecimal price;
    /** 站址编码  */
    private String stationcode;
    /** 站址名称 */
    private String stationname;
    /** 状态 */
    private String status;

	public String getFlag() {
		return flag;
	}

	public void setFlag(String flag) {
		this.flag = flag;
	}

	private String flag;
	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	private String startDate;

	public BigDecimal getLastunitpirce() {
		return lastunitpirce;
	}

	public void setLastunitpirce(BigDecimal lastunitpirce) {
		this.lastunitpirce = lastunitpirce;
	}

	public Long getLastpcid() {
		return lastpcid;
	}

	public void setLastpcid(Long lastpcid) {
		this.lastpcid = lastpcid;
	}

	/**
	 * 最近报账单价
	 */
	private BigDecimal lastunitpirce;
	/**
	 * pcid  最近报账pcid
	 */
	private Long lastpcid;

	public String getProjectname() {
		return projectname;
	}

	public void setProjectname(String projectname) {
		this.projectname = projectname;
	}

	private String projectname;
	public Long getCountry() {
		return country;
	}

	public void setCountry(Long country) {
		this.country = country;
	}

	public Long getCompany() {
		return company;
	}

	public void setCompany(Long company) {
		this.company = company;
	}

	/** 责任中心ID */
	private Long country;
	private String countryname;
	/** 所属市公司ID */
	private Long company;
	private String ammetername;
	private String electrotypename;
	private String companyname;
	private String directsupplyflagname;

	/** 录入人ID */
	private Long inputuserid;

	public Long getInputuserid() {
		return inputuserid;
	}

	public void setInputuserid(Long inputuserid) {
		this.inputuserid = inputuserid;
	}

	public Date getInputdate() {
		return inputdate;
	}

	public void setInputdate(Date inputdate) {
		this.inputdate = inputdate;
	}

	public String getModifyrecord() {
		return modifyrecord;
	}

	public void setModifyrecord(String modifyrecord) {
		this.modifyrecord = modifyrecord;
	}

	/** 录入时间 */
	private Date inputdate;
	/** 修改记录 */
	private String modifyrecord;
	public String getElectrotypename() {
		return electrotypename;
	}

	public void setElectrotypename(String electrotypename) {
		this.electrotypename = electrotypename;
	}

	public String getDirectsupplyflagname() {
		return directsupplyflagname;
	}

	public void setDirectsupplyflagname(String directsupplyflagname) {
		this.directsupplyflagname = directsupplyflagname;
	}


	public String getAmmetername() {
		return ammetername;
	}

	public void setAmmetername(String ammetername) {
		this.ammetername = ammetername;
	}


	public String getCountryname() {
		return countryname;
	}

	public void setCountryname(String countryname) {
		this.countryname = countryname;
	}

	public String getCompanyname() {
		return companyname;
	}

	public void setCompanyname(String companyname) {
		this.companyname = companyname;
	}




	public void setYear(String year)
	{
		this.year = year;
	}

	public String getYear() 
	{
		return year;
	}

	public void setMonth(String month)
	{
		this.month = month;
	}

	public String getMonth() 
	{
		return month;
	}

	public void setAmmeterid(Long ammeterid)
	{
		this.ammeterid = ammeterid;
	}

	public Long getAmmeterid() 
	{
		return ammeterid;
	}

	public void setDirectsupplyflag(Integer directsupplyflag)
	{
		this.directsupplyflag = directsupplyflag;
	}

	public Integer getDirectsupplyflag() 
	{
		return directsupplyflag;
	}

	public void setSupplybureauammetercode(String supplybureauammetercode)
	{
		this.supplybureauammetercode = supplybureauammetercode;
	}

	public String getSupplybureauammetercode() 
	{
		return supplybureauammetercode;
	}

	public void setElectrotype(Long electrotype)
	{
		this.electrotype = electrotype;
	}

	public Long getElectrotype() 
	{
		return electrotype;
	}

	public void setPrice(BigDecimal price)
	{
		this.price = price;
	}

	public BigDecimal getPrice() 
	{
		return price;
	}

	public void setStationcode(String stationcode)
	{
		this.stationcode = stationcode;
	}

	public String getStationcode() 
	{
		return stationcode;
	}

	public void setStationname(String stationname)
	{
		this.stationname = stationname;
	}

	public String getStationname() 
	{
		return stationname;
	}

	public void setStatus(String status)
	{
		this.status = status;
	}

	public String getStatus() 
	{
		return status;
	}


	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("year", getYear())
            .append("month", getMonth())
            .append("ammeterid", getAmmeterid())
            .append("directsupplyflag", getDirectsupplyflag())
            .append("supplybureauammetercode", getSupplybureauammetercode())
            .append("electrotype", getElectrotype())
            .append("price", getPrice())
            .append("stationcode", getStationcode())
            .append("stationname", getStationname())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
