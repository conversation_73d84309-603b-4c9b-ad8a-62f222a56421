package com.sccl.modules.business.powerauditstaiongrade.entity;

import com.sccl.modules.business.powerauditstaiongrade.util.ExcelExport;
import lombok.Data;

@Data
public class TowerStationGradeaudit implements ExcelExport {
    /**
     * 铁塔地市
     */
    private String city;
    private String cityName;
    /**
     * 铁塔区县
     */
    private String district;
    /**
     * 铁塔站址编码
     */
    private String towersitecode;
    /**
     * 铁塔站址名称
     */
    private String towersitename;
    /**
     *缴费户号
     */
    private String payaccountnum;
    /**
     * 分摊比例
     */
    private String nh_persent;
    /**
     * 电信实际结算分摊比例
     */
    private String tower_persent;
    /**
     * 分摊比例差异
     */
    private String diff;

    private String company;
    private String companyName;
    private String country;
    private String countryName;
}
