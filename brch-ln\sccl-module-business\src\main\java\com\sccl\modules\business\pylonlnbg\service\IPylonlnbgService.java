package com.sccl.modules.business.pylonlnbg.service;

import com.sccl.framework.service.IBaseService;
import com.sccl.modules.business.account.domain.Account;
import com.sccl.modules.business.account.domain.AccountBaseResult;
import com.sccl.modules.business.account.domain.AccountCondition;
import com.sccl.modules.business.accountbillitempre.domain.Accountbillitempre;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.util.List;
import java.util.Map;

/**
 * __辽宁铁塔包干__<br/>
 * 2019/12/24
 *
 * <AUTHOR>
 */
public interface IPylonlnbgService extends IBaseService<Account> {

    /**
     * @Description: 铁塔包干录入界面列表
     * @author: dongk
     * @date: 2019/7/18
     * @param:
     * @return:
     */
    List<AccountBaseResult> selectByParams(AccountCondition condition);

    /**
     * @Description: 保存更新数据
     * @author: dongk
     * @date: 2019/7/22
     * @param:
     * @return:
     */
    Map<String,Object> updateData(List<Account> accountList);

    /**
     * @Description: 删除
     * @author: dongk
     * @date: 2019/7/22
     * @param:
     * @return:
     */
    int deleteAccountByIds(String[] pcids);

    /**
     * @Description: 铁塔包干台账合计
     * @author: dongk
     * @date: 2019/7/23
     * @param:
     * @return:
     */
    AccountBaseResult accountTotal(AccountCondition condition);

    /**
     * @Description: 查询归集单下面所有台账
     * @author: dongk
     * @date: 2019/7/23
     * @param:
     * @return:
     */
    List<AccountBaseResult> selectListByPre(Accountbillitempre accountbillitempre);

    /**
     * @Description: 包干查询功能列表
     * @author: dongk
     * @date: 2019/7/24
     * @param:
     * @return:
     */
    List<AccountBaseResult> selectQuery(AccountCondition condition);

    /**
     * @Description: 铁塔包干台账合计
     * @author: dongk
     * @date: 2019/7/25
     * @param:
     * @return:
     */
    AccountBaseResult queryTotal(AccountCondition condition);

    /**
     * @Description: 通过页面查询条件查询pcid
     * @author: dongk
     * @date: 2019/7/29
     * @param:
     * @return:
     */
    Map<String,Object> selectIdsByParams(AccountCondition condition);

    Map<String, Object> dataVerification(List<AccountBaseResult> list, String version)throws Exception;

    XSSFWorkbook exportbg(List<AccountBaseResult> list, String version);
}
