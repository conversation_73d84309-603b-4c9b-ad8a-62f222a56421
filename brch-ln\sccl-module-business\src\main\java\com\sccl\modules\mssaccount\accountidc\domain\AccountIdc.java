package com.sccl.modules.mssaccount.accountidc.domain;


import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.math.BigDecimal;


/**
 * 推送数据平台IDC电量电费表 power_account_idc
 *
 * <AUTHOR>
 * @date 2020-06-22
 */
public class AccountIdc extends BaseEntity
{
	private static final long serialVersionUID = 1L;

    /**  */
    private Long pcid;
    /**  */
    private Long ammeterid;
    /**  */
    private Long billId;
    /**  */
    private String accountno;
    /**  */
    private String energymetercode;
    /**  */
    private Integer electroTypeId;
    /**  */
    private BigDecimal scale;
    /**  */
    private BigDecimal amount;
    /**  */
    private BigDecimal money;


	public void setPcid(Long pcid)
	{
		this.pcid = pcid;
	}

	public Long getPcid()
	{
		return pcid;
	}

	public void setAmmeterid(Long ammeterid)
	{
		this.ammeterid = ammeterid;
	}

	public Long getAmmeterid()
	{
		return ammeterid;
	}

	public void setBillId(Long billId)
	{
		this.billId = billId;
	}

	public Long getBillId()
	{
		return billId;
	}

	public void setAccountno(String accountno)
	{
		this.accountno = accountno;
	}

	public String getAccountno()
	{
		return accountno;
	}

	public void setEnergymetercode(String energymetercode)
	{
		this.energymetercode = energymetercode;
	}

	public String getEnergymetercode()
	{
		return energymetercode;
	}

	public void setElectroTypeId(Integer electroTypeId)
	{
		this.electroTypeId = electroTypeId;
	}

	public Integer getElectroTypeId()
	{
		return electroTypeId;
	}

	public void setScale(BigDecimal scale)
	{
		this.scale = scale;
	}

	public BigDecimal getScale()
	{
		return scale;
	}

	public void setAmount(BigDecimal amount)
	{
		this.amount = amount;
	}

	public BigDecimal getAmount()
	{
		return amount;
	}

	public void setMoney(BigDecimal money)
	{
		this.money = money;
	}

	public BigDecimal getMoney()
	{
		return money;
	}

	private Long stationId;

	public Long getStationId() {
		return stationId;
	}

	public void setStationId(Long stationId) {
		this.stationId = stationId;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("pcid", getPcid())
            .append("ammeterid", getAmmeterid())
            .append("billId", getBillId())
            .append("accountno", getAccountno())
            .append("energymetercode", getEnergymetercode())
            .append("electroTypeId", getElectroTypeId())
            .append("scale", getScale())
            .append("amount", getAmount())
            .append("money", getMoney())
            .append("createTime", getCreateTime())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
