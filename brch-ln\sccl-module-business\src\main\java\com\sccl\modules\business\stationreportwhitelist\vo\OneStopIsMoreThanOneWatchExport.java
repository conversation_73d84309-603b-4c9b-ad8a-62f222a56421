package com.sccl.modules.business.stationreportwhitelist.vo;

import com.sccl.framework.aspectj.lang.annotation.Excel;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;


/**
 * <AUTHOR>
 * @date 2024/4/9 16:23
 * @describe 一站多表白名单
 */
@Getter
@Setter
@Builder
public class OneStopIsMoreThanOneWatchExport {


    /**
     * 局站编码
     */
    @Excel(name = "局站编码")
    private String stationcode;

    /**
     * 局站名称
     */
    @Excel(name = "局站名称")
    private String stationName;

    /**
     * 局站类型名称
     */
    @Excel(name = "局站类型")
    private String stationtypeName;

    /**
     * 分公司名称
     */
    @Excel(name = "所属分公司")
    private String companyName;

    /**
     * 部门名称
     */
    @Excel(name = "所属部门")
    private String countryName;

    /**
     * 局站关联电表数量
     */
    @Excel(name = "关联电表数量")
    private Long stationMetersCount;

    /**
     * 流程单据状态名称
     */
    @Excel(name = "单据状态")
    private String billStatusName;


}
