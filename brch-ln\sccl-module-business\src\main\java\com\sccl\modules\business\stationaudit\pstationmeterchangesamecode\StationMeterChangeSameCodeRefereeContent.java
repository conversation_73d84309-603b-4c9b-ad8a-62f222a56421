package com.sccl.modules.business.stationaudit.pstationmeterchangesamecode;

import com.enrising.dcarbon.audit.AbstractRefereeContent;
import com.enrising.dcarbon.audit.RefereeDatasource;
import com.enrising.dcarbon.audit.RefereeResult;
import lombok.Data;

@Data
public class StationMeterChangeSameCodeRefereeContent extends AbstractRefereeContent implements RefereeDatasource {
    private Long billId;
    private Long pcid;
    private String stationCode;
    /**
     * 台账对应的电表id
     */
    private Long ammeterid;
    /**
     * 项目名称
     */
    private String projectname;
    /**
     * 局站名称
     */
    private String stationname;
    /**
     * 局站地址
     */
    private String stationaddress;
    /**
     * 当前台账的电表 对应 站址的上期 电表情况
     * @return
     */
    private String meterlast_samestationcode;

    public StationMeterChangeSameCodeRefereeContent(RefereeResult refereeResult, int step, String auditKey) {
        super(refereeResult, step, auditKey);
    }

    public StationMeterChangeSameCodeRefereeContent() {
    }
}
