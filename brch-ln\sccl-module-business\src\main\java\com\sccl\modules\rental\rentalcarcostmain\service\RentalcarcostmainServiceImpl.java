package com.sccl.modules.rental.rentalcarcostmain.service;

import com.sccl.common.lang.StringUtils;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.common.exception.base.BaseException;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.service.IdGenerator;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.rental.rentalcarcost.domain.Rentalcarcost;
import com.sccl.modules.rental.rentalcarcost.mapper.RentalcarcostMapper;
import com.sccl.modules.rental.rentalcarcostmain.mapper.RentalcarcostmainMapper;
import com.sccl.modules.rental.rentalcarmain.service.RentalcarmainServiceImpl;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.uniflow.common.WFModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sccl.modules.rental.rentalcarcostmain.domain.Rentalcarcostmain;

import java.math.BigDecimal;
import java.util.*;


/**
 * 租赁费用主 服务层实现
 * 
 * <AUTHOR>
 * @date 2019-08-29
 */
@Service
public class RentalcarcostmainServiceImpl extends BaseServiceImpl<Rentalcarcostmain> implements IRentalcarcostmainService
{
    private static final Logger logger = LoggerFactory.getLogger(RentalcarmainServiceImpl.class);
    @Autowired
    RentalcarcostmainMapper rentalcarcostmainMapper;

    @Autowired
    RentalcarcostMapper rentalcarcostMapper;

    @Override
    public AjaxResult savecostmain(Rentalcarcostmain rentalcarcostmain) {
        AjaxResult rs = new AjaxResult();
        if(rentalcarcostmain.getId() == null){
            insertcostmain(rentalcarcostmain,rs);
        }else {
            updatecostmain(rentalcarcostmain,rs);
        }
        return rs;
    }

    @Override
    public Rentalcarcostmain selectById(Long id) {
        Rentalcarcostmain main = rentalcarcostmainMapper.selectById(id);
        if(main != null){
            Rentalcarcost cost = new Rentalcarcost();
            cost.setRcmid(id);
            List<Rentalcarcost> costList = rentalcarcostMapper.selectList(cost);
            main.setRentalcarcostList(costList);
        }
        return main;
    }

    @Override
    public int deleteByIds(String[] ids) {
        rentalcarcostMapper.deleteByRcmids(ids);
        return rentalcarcostmainMapper.deleteByIdsDB(ids);
    }

    @Override
    public List<Rentalcarcostmain> selectByIds(String[] ids) {
        return rentalcarcostmainMapper.selectByIds(ids);
    }

    /**
     * 流程引擎回调（通过MQ）
     *
     * @param wfModel 流程消息对象
     */
//    @Override
    public void uniflowCallBack(WFModel wfModel) {
//        System.out.println("-----------------lt:" + wfModel.toString());
        logger.debug("-----------------lt:" + wfModel.toString());
        if ("PROCESS_STARTED".equals(wfModel.getCallbackType())) {//更新流程Id  流程 提交
            try {
//                草稿	1，代办	2,退回	3，删除	4,完成	5
                setStatus(wfModel, "2");
                logger.debug("车辆租赁信息录入审批新增" + wfModel.getBusiId());
            } catch (NumberFormatException e) {
                e.printStackTrace();
                logger.error("提交流程失败:" + e.getMessage());
                throw new BaseException("提交流程失败:" + e.getMessage());//
            }
        } else if ("PROCESS_COMPLETED".equals(wfModel.getCallbackType())) {//
//            流程完成后执行的回调  //                草稿	1，代办	2,退回	3，删除	4,完成	5
            setStatus(wfModel, "5");
        } else if ("TURNBACK_TO_START".equals(wfModel.getCallbackType())) {//流程 退回
            //                草稿	1，代办	2,退回	3，删除	4,完成	5
            setStatus(wfModel, "1");// 改为草稿 可重新提交
        } else if ("PROCESS_CANCELLED".equals(wfModel.getCallbackType())) {//流程 终止
            //                草稿	1，代办	2,退回	3，删除	4,完成	5
            setStatus(wfModel, "1");
        }
    }

    private void setStatus(WFModel wfModel, String status) {
        Rentalcarcostmain m = new Rentalcarcostmain();
        m.setIprocessinstid(wfModel.getProcInstId());
        m.setId(Long.valueOf(wfModel.getBusiId()));
        // 流程中
        m.setStatus(status);
        rentalcarcostmainMapper.updateForModel(m);
        Long id = m.getId();
        Rentalcarcost cost = new Rentalcarcost();
        cost.setRcmid(id);
        List<Rentalcarcost> costList = rentalcarcostMapper.selectList(cost);
        if(costList != null && costList.size() > 0){
            for(Rentalcarcost c : costList){
                c.setStatus(status);
                rentalcarcostMapper.updateForModel(c);
            }
        }

    }

    private void insertcostmain(Rentalcarcostmain rentalcarcostmain, AjaxResult rs){
        long nextId = IdGenerator.getNextId();
        User user = ShiroUtils.getUser();
        Long userid = user.getId();
        String name = user.getUserName();
        Date date = new Date();
        rentalcarcostmain.setId(nextId);
        rentalcarcostmain.setInputuserid(userid);
        rentalcarcostmain.setInputusername(name);
        rentalcarcostmain.setInputdate(date);
        rentalcarcostmain.setStatus("1");
        rentalcarcostmainMapper.insert(rentalcarcostmain);
        List<Rentalcarcost> rentalcarcostList = rentalcarcostmain.getRentalcarcostList();
        if(rentalcarcostList != null && rentalcarcostList.size() > 0){
            for(Rentalcarcost cost : rentalcarcostList){
                cost.setRcmid(nextId);
                cost.setRccid(IdGenerator.getNextId());
                cost.setInputuserid(userid);
                cost.setInputdate(date);
                cost.setInputusername(name);
                cost.setStatus("1");
                cost.setCompany(rentalcarcostmain.getCompany());
                cost.setCountry(rentalcarcostmain.getCountry());
            }

            rentalcarcostMapper.insertList(rentalcarcostList);
            rentalcarcostmain.setRentalcarcostList(rentalcarcostList);
        }
        rs.put("data",rentalcarcostmain);
    }

    private void updatecostmain(Rentalcarcostmain rentalcarcostmain,AjaxResult rs){
        rentalcarcostmainMapper.updateForModel(rentalcarcostmain);
        User user = ShiroUtils.getUser();
        Long userid = user.getId();
        String name = user.getUserName();
        Date date = new Date();
        Long id = rentalcarcostmain.getId();
        List<Rentalcarcost> rentalcarcostList = rentalcarcostmain.getRentalcarcostList();
        List<Rentalcarcost> newModel = new ArrayList<>();
        if(rentalcarcostList != null && rentalcarcostList.size() > 0) {
            for (Rentalcarcost cost : rentalcarcostList) {
                if(cost.getRccid() == null){
                    cost.setRcmid(id);
                    cost.setRccid(IdGenerator.getNextId());
                    cost.setInputuserid(userid);
                    cost.setInputdate(date);
                    cost.setInputusername(name);
                    cost.setStatus("1");
                    newModel.add(cost);
                }else {
                    rentalcarcostMapper.updateForModel(cost);
                }
            }
            if(newModel.size()> 0){
                rentalcarcostMapper.insertList(newModel);
            }
        }
        rs.put("data",rentalcarcostmain);
    }
    //租赁费用统计分析表
    @Override
    public List<Map<String,Object>> statistical(Integer startYear, Integer endYear, Integer startQuarter, Integer endQuarter,
                                                String company, String modelname, String suppliername){
        Map<String,Object> params = new HashMap<>();
        params.put("startYear",startYear);
        params.put("endYear",endYear);
        params.put("startQuarter",startQuarter);
        params.put("endQuarter",endQuarter);
        params.put("company","-1".equals(company)?null:company);
        params.put("modelname",modelname);
        params.put("suppliername",suppliername);
        params.put("startDate",startYear+setDate(startQuarter+""));
        params.put("endDate",endYear+setDate(endQuarter+""));
        List<Map<String,Object>> datas = rentalcarcostmainMapper.statistical(params);
        List<Map<String,Object>> result = new ArrayList<>();
        Map<String,Object> map = new HashMap<>();
        Map<String,Object> tmpMap = new HashMap<>();
        Map<String,Object> thisMap = new HashMap<>();
        Map<String,Object> map1 = new HashMap<>();
        int count =0;
        int num =0;
        BigDecimal sumTax = BigDecimal.ZERO;
        BigDecimal rowSum = BigDecimal.ZERO;
        Map<String,Object> mapTax = new HashMap<>();//计算每年的含税和不含税
        for (Map<String,Object> value: datas) {
            if(count == 0){
                tmpMap = value;
            }else{
                tmpMap = datas.get(count-1);
            }
            thisMap = value;
            String tmpModelId = rvZeroAndDot(tmpMap.get("modelid")+"");
            String tmpCompany1 = tmpMap.get("company")+"";
            String tmpYear = tmpMap.get("year")+"";
            String modelId = rvZeroAndDot(value.get("modelid")+"");
            String company1 = value.get("company")+"";
            String year = value.get("year")+"";
            boolean modelFlag = StringUtils.isNotEmpty(tmpModelId) && tmpModelId.equals(modelId);//车型相等
            boolean companyFlag = StringUtils.isNotEmpty(tmpCompany1) && tmpCompany1.equals(company1);//分公司相等
            boolean yearFlag = StringUtils.isNotEmpty(tmpYear) && tmpYear.equals(year);//分公司相等
            if((modelFlag && companyFlag && !yearFlag)||!modelFlag){
                mapTax.put(tmpYear+"noTax",sumTax);
                mapTax.put(tmpYear+"tax",sumTax.multiply(new BigDecimal(0.13)).setScale(2, BigDecimal.ROUND_HALF_UP));
                sumTax = BigDecimal.ZERO;
            }
            if(companyFlag && modelFlag){
                String monthTitle = value.get("year")+""+value.get("quarter");
                map.put(monthTitle,value.get("sum"));
                BigDecimal a = new BigDecimal(value.get("sum")+"");
                sumTax = sumTax.add(a);//不含税金额
                rowSum = rowSum.add(a);//行计
                num++;
            }else{
                map1.putAll(tmpMap);
                map1.putAll(map);
                map1.putAll(mapTax);
                map1.put("rowSum",rowSum);
                result.add(map1);
                map = new HashMap<>();
                map1 = new HashMap<>();
                mapTax = new HashMap<>();
                rowSum = BigDecimal.ZERO;
                num = 0;
                String monthTitle = value.get("year")+""+value.get("quarter");
                map.put(monthTitle,value.get("sum"));
                BigDecimal a = new BigDecimal(value.get("sum")+"");
                sumTax = sumTax.add(a);//不含税金额
                rowSum = rowSum.add(a);//行计
                mapTax.put(tmpYear+"noTax",sumTax);
                mapTax.put(tmpYear+"tax",sumTax.multiply(new BigDecimal(0.13)).setScale(2, BigDecimal.ROUND_HALF_UP));
                num++;
            }
            count ++;
        }
        if(num != 0){
            map1.putAll(mapTax);
            map1.putAll(thisMap);
            map1.putAll(map);
            map1.put("rowSum",rowSum);
            result.add(map1);
        }
        return result;
    }
    private String setDate(String date){
        String str = "";
        switch (date){
            case "1": str= "03";break;
            case "2": str= "06";break;
            case "3": str= "09";break;
            case "4": str= "12";break;
        }
        return str;
    }
    public String rvZeroAndDot(String s){
        if (s.isEmpty()) {
            return null;
        }
        if(s.indexOf(".") > 0){
            s = s.replaceAll("0+?$", "");//去掉多余的0
            s = s.replaceAll("[.]$", "");//如最后一位是.则去掉
        }
        return s;
    }

}
