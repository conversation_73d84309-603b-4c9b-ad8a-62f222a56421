package com.sccl.modules.business.eneregyaccountpoolpre.service;

import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.business.eneregyaccountpoolpre.domain.EneregyAccountpoolpre;
import com.sccl.framework.service.IBaseService;
import com.sccl.modules.business.eneregyaccountpoolpre.domain.EneregyAccountpoolpreDto;

/**
 * 油费汇总单头 服务层
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
public interface IEneregyAccountpoolpreService extends IBaseService<EneregyAccountpoolpre>
{


    /** 新增汇总单 */
    AjaxResult insertAudit(EneregyAccountpoolpreDto eneregyAccountpoolpre);

    /** 删除汇总单 */
    AjaxResult myDeleteByIds(String[] toStrArray);
}
