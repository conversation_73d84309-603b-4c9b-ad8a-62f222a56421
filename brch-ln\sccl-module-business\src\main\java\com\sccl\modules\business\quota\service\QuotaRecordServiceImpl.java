package com.sccl.modules.business.quota.service;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.business.quota.domain.Quota;
import com.sccl.modules.business.quota.domain.QuotaBaseResult;
import com.sccl.modules.business.quota.domain.QuotaCondition;
import com.sccl.modules.business.quota.domain.QuotaRecord;
import com.sccl.modules.business.quota.mapper.QuotaMapper;
import com.sccl.modules.business.quota.mapper.QuotaRecordMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


/**
 * 定额 服务层实现
 * 
 * <AUTHOR>
 * @date 2019-05-13
 */
@Service
public class QuotaRecordServiceImpl extends BaseServiceImpl<QuotaRecord> implements IQuotaRecordService
{
    @Autowired
    QuotaRecordMapper quotaRecordMapper;
    @Override
    public List<QuotaRecord> getByQuotaId(QuotaRecord quotaRecord){
        return quotaRecordMapper.getByQuotaId(quotaRecord);
    }
    @Override
    public List<QuotaRecord> getByQuotaDateId(QuotaRecord quotaRecord){
        return quotaRecordMapper.getByQuotaDateId(quotaRecord);
    }
    @Override
    public List<QuotaRecord> selectObjectByAmmPro(QuotaRecord quotaRecord){
        return quotaRecordMapper.selectObjectByAmmPro(quotaRecord);
    }
    @Override
    public String getByCreateTime(Map<String,Object> params){
        return quotaRecordMapper.getByCreateTime(params);
    }

}
