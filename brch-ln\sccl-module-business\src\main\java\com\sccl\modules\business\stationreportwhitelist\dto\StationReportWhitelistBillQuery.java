package com.sccl.modules.business.stationreportwhitelist.dto;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

/**
 * <AUTHOR>
 * @date 2024/5/6 10:08
 * @describe 白名单流程单据
 */
@Getter
@Setter
public class StationReportWhitelistBillQuery {

    /**
     * 局站id
     */
    private Long id;

    /**
     * 流程单据状态
     */
    private Integer billStatus;

    /**
     * 分公司
     */
    private Long company;

    /**
     * 部门
     */
    private Long country;

    /**
     * 项目名称
     */
    private String stationname;

    /**
     * 白名单类型 1:一表多站 2:一站多表 3:单价
     */
    private String type;

    /**
     * 是否导出全部
     */
    private boolean isExportAll;

    @Override
    public String toString() {
    	return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }
}
