package com.sccl.modules.business.meterinfoalljt_new.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 添加 保存对象
 */
@Data
public class MeterinfoAllJtApplySaveVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 全选标识 1 全选 2 非全选
     */
    private String all;

    /**
     * 所属分公司
     */
    private String company;
    private String[] companys;

    /**
     * 所属部门
     */
    private String country;
    private String[] countrys;

    /**
     * 关键字 站址名称模糊查询
     */
    private String key;

    /**
     * 电表编号 精确查询（多个逗号隔开）
     */
    private String ammeterCode;
    private String[] ammeterCodes;

    /**
     * 站址编码 5gr站址编码/站址编码精确查询（多个逗号隔开）
     */
    private String resstationcode;
    private String[] resstationcodes;

    /**
     * 电表id（多个逗号隔开）
     */
    private String id;
    private String[] ids;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 删除标志 0/1 正常/删除
     */
    private String delFlag;

    /**
     * 新增
     *
     * @param userName 当前登录用户
     */
    public void initInsert(String userName) {
        delFlag = "0";
        createdBy = userName;
        createTime = new Date();
        updateTime = new Date();
    }
}
