package com.sccl.modules.business.heataccount.domain;

import com.sccl.common.constant.ExcelColumn;
import com.sccl.common.constant.enums.TicketTaxRateTypeEnum;
import com.sccl.common.constant.enums.TicketTypeEnum;
import com.sccl.common.lang.StringUtils;
import com.sccl.common.utils.BigDecimlUtil;
import com.sccl.common.utils.DateUtils;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;

@Data
public class HeatAccountVo {

    private Long id;

    private String accountNo;

    private String startDate;

    private String endDate;

    private String heatUseBody;

    private BigDecimal heatAreaSize;

    private BigDecimal unitPrice;

    private Byte ticketType;

    private String ticketImportType;

    private BigDecimal heatAmount;

    private BigDecimal taxTicketMoney;

    private BigDecimal ticketMoney;

    private BigDecimal otherFee;

    private BigDecimal paidMoney;

    private String inputerId;

    private Date inputDate;

    private String lastediterId;

    private Date lasteditDate;

    private String feeStartDate;

    private Long orgId;

    private Byte status = 1;

    private Long company;

    private Long country;

    private String delFlag;

    private BigDecimal taxAmount;

    private BigDecimal taxImportRate;

    private Byte taxRate;

    private BigDecimal inputTaxTicketMoney;

    private BigDecimal inputTicketMoney;


    private String remark;

}
