package com.sccl.modules.business.stationequipment.domain;

import com.sccl.framework.aspectj.lang.annotation.Excel;
import com.sccl.framework.web.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


/**
 * 铁塔站址设备表 tower_station_equipment
 *
 * <AUTHOR>
 * @date 2022-08-09
 */
@Getter
@Setter
@ToString
public class StationEquipment extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 组Id，同一组属于同一个设备
     */
    @Excel(name = "设备组Id（请勿修改）", isAllowEdit = false)
    private Long groupId;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 铁塔站址编码
     */
    private String towerStationCode;
    /**
     * 4G综合网管/5GR系统站址编码
     */
    private String stationCode;
    /**
     * 站址编码老
     */
    private String oldStationCode;
    /**
     * 设备厂家
     */
    private String factory;
    /**
     * 设备型号
     */
    private String model;
    /**
     * 设备类型
     */
    private String type;
    /**
     * 设备数目
     */
    private Integer count;
    /**
     * 版本号
     */
    @Excel(name = "版本号（请勿修改",isAllowEdit = false)
    private Long version;

}
