package com.sccl.modules.business.jhanomalydetails.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.sccl.framework.aspectj.lang.annotation.Excel;
import lombok.Data;



/**
 * 台账/报账，日均电量，和日均耗电量
 * */
@Data
public class JhDailyElectricityDTO {
    /**
     * 电表户名/协议编码
     */
    @Excel(name = "电表户名/协议编码_tz_bz")
    private String dbhm;

    /**
     * 台账期号
     */
    @TableField("tzqh")
    @Excel(name = "台账期号_tz_bz")
    private String tzqh;



    /**
     * 台账日均电量
     */
    @TableField("tzrjdl")
    @Excel(name = "台账日均电量_tz_bz")
    private String tzrjdl;

    /**
     * 标准日均电量
     */
    @TableField("bzrjdl")
    @Excel(name = "标准日均电量_tz_bz")
    private String bzrjdl;

    /**
     * 波动幅度
     */
    @TableField("bdfd")
    @Excel(name = "波动幅度_tz_bz")
    private String bdfd;


    /**
     * 集团站址编码
     */
    @TableField("jtzzbm")
    @Excel(name = "集团站址编码_tz")
    private String jtzzbm;

    /**
     * 铁塔站址编码
     */
    @TableField("ttzzbm")
    @Excel(name = "铁塔站址编码_tz")
    private String ttzzbm;


    /** 报账电量 */
    @Excel(name = "报账电量_bz")
    private String bzdl;




}
