package com.sccl.modules.business.meterinfoalljt_new.vo;

import com.sccl.framework.aspectj.lang.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

/**
 * 在网表计数据 一览 结果列表
 */
@Data
public class MeterinfoAllJtResultVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 所属分公司
     */
    @Excel(name = "所属分公司")
    private String companyName;

    /**
     * 所属部门
     */
    @Excel(name = "所属部门")
    private String countryName;

    /**
     * 电表编号
     */
    @Excel(name = "电表编号")
    private String ammeterCode;

    /**
     * 资源局站id
     */
    @Excel(name = "资源局站id")
    private String resstationcode;

    /**
     * 5gr站址编码
     */
    @Excel(name = "5gr站址编码")
    private String stationcode5gr;

    /**
     * 站址名称
     */
    @Excel(name = "站址名称")
    private String resstationname;

    /**
     * 用电类型
     */
    @Excel(name = "用电类型")
    private String electrotype;
}
