package com.sccl.modules.business.oilcard.service;

import com.sccl.common.lang.StringUtils;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.business.oilcard.domain.OilCard;
import com.sccl.modules.business.oilcard.mapper.OilCardMapper;
import com.sccl.modules.business.order.domain.Order;
import com.sccl.modules.business.order.service.IOrderService;
import com.sccl.modules.monitor.operlog.domain.OperLog;
import com.sccl.modules.monitor.operlog.mapper.OperLogMapper;
import com.sccl.modules.uniflow.common.WFModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2022/2/15 10:03
 **/
@Service
public class OilCardOrderServiceImpl extends BaseServiceImpl<Order> implements IOrderService {

    private static final Logger logger = LoggerFactory.getLogger(OilCardOrderServiceImpl.class);

    @Resource
    OilCardMapper oilCardMapper;

    @Autowired
    private OperLogMapper operLogMapper;

    // 提交流程
    @Override
    public void uniflowCallBack(WFModel wfModel) {
        if (StringUtils.isNotEmpty(wfModel.getCallbackType())
                && "PROCESS_STARTED".equals(wfModel.getCallbackType())
                && wfModel.getVariables().containsKey("firstNode")
                && wfModel.getVariables().get("firstNode").equals(true)) {
            if ("ADD_OIL_CARD".equals(wfModel.getBusiAlias())) {
                this.updateStatus(wfModel.getBusiId(), 1,wfModel.getProcInstId()); // 修改单据状态为流程中
            } else {
                this.updateStatus(wfModel.getBusiId(), 4); // 修改单据状态为修改流程中
            }
        } else if (StringUtils.isNotEmpty(wfModel.getCallbackType())
                && "PROCESS_CANCELLED".equals(wfModel.getCallbackType())) {
            // 只有驳回才更改状态，不同意时还是在流程中
            OilCard oilCard = oilCardMapper.selectById(wfModel.getBusiId());
            if (null != oilCard.getBillStatus() && oilCard.getBillStatus() == 1) {
                this.updateStatus(wfModel.getBusiId(), 0); // 修改单据状态为草稿
            } else if (null != oilCard.getBillStatus() && oilCard.getBillStatus() == 4) {
                this.updateStatus(wfModel.getBusiId(), 3); // 修改单据状态为修改中
            }
        }
        else if (StringUtils.isNotEmpty(wfModel.getCallbackType()) && "PROCESS_COMPLETED".equals(wfModel.getCallbackType())) {
            try {
                //修改单据状态为已完成并更新数据
                this.processComplete(wfModel.getBusiId(), 2);
            } catch (Exception e) {
                e.printStackTrace();
                OperLog model = new OperLog();
                model.setOperName("ammeter");
                model.setTitle("油卡新增流程回调修改单据状态为已完成并更新数据异常");
                model.setMethod("updateDateByChange");
                model.setErrorMsg("油卡id:" + wfModel.getBusiId() + " 流程：" + wfModel.getBusiAlias() + " 用户：" + wfModel.getApplyUserId());
                model.setOperTime(new Date());
                operLogMapper.insert(model);
                throw e;
            }
        }
    }

    // 更新单据状态
    private void updateStatus(String id, int billStatus) {
        OilCard oilCard = oilCardMapper.selectById(id);
        if (null != oilCard) {
            oilCard.setBillStatus(billStatus);
            oilCardMapper.updateForModel(oilCard);
        } else {
            logger.error("没有找到对应的油卡基础数据，或已被删除");
        }
    }

    // 更新单据状态
    private void updateStatus(String id, int billStatus,Long procInstId) {
        OilCard oilCard = oilCardMapper.selectById(id);
        if (null != oilCard) {
            oilCard.setBillStatus(billStatus);
            oilCard.setProcInstId(procInstId);
            oilCardMapper.updateForModel(oilCard);
        } else {
            logger.error("没有找到对应的基础数据，或已被删除");
        }
    }

    // 更新单据状态
    private void processComplete(String id, int billStatus) {
        OilCard oilCard = oilCardMapper.selectById(id);
        if (null != oilCard) {
            oilCard.setBillStatus(billStatus);
            oilCardMapper.updateForRecharge(oilCard);
        } else {
            logger.error("没有找到对应的油卡基础数据，或已被删除");
        }
    }
}
