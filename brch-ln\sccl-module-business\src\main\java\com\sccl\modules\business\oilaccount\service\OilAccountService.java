package com.sccl.modules.business.oilaccount.service;

import com.sccl.framework.service.IBaseService;
import com.sccl.modules.business.oilaccount.domain.OilAccount;
import com.sccl.modules.business.oilaccount.domain.OilAccountRequest;
import com.sccl.modules.system.user.domain.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 用油服务层
 * @date 2024/8/27  11:44
 */
public interface OilAccountService extends IBaseService<OilAccount> {

    List<OilAccount> listOilAccount(OilAccountRequest request);

    Map<String, Object> importOilAccount(HttpServletRequest request, HttpServletResponse response) throws Exception;

    int batchAddOrUpdateOilAccount(List<OilAccount> accounts, User user);
}
