package com.sccl.modules.mssaccount.mssinterface.domain;

import lombok.Data;

import java.io.Serializable;

public class CopyMeterInner implements Serializable {
    public String getBudgetSet() {
        return budgetSet;
    }

    public void setBudgetSet(String budgetSet) {
        this.budgetSet = budgetSet;
    }

    public String getEnergyMeterCode() {
        return energyMeterCode;
    }

    public void setEnergyMeterCode(String energyMeterCode) {
        this.energyMeterCode = energyMeterCode;
    }

    public String getEnergyMeterName() {
        return energyMeterName;
    }

    public void setEnergyMeterName(String energyMeterName) {
        this.energyMeterName = energyMeterName;
    }

    public String getElectricityStartDate() {
        return electricityStartDate;
    }

    public void setElectricityStartDate(String electricityStartDate) {
        this.electricityStartDate = electricityStartDate;
    }

    public String getElectricityEndDate() {
        return electricityEndDate;
    }

    public void setElectricityEndDate(String electricityEndDate) {
        this.electricityEndDate = electricityEndDate;
    }

    public String getThisQuantityOfElectricity() {
        return thisQuantityOfElectricity;
    }

    public void setThisQuantityOfElectricity(String thisQuantityOfElectricity) {
        this.thisQuantityOfElectricity = thisQuantityOfElectricity;
    }

    public String getThisElectricityCharge() {
        return thisElectricityCharge;
    }

    public void setThisElectricityCharge(String thisElectricityCharge) {
        this.thisElectricityCharge = thisElectricityCharge;
    }

    public String getPowerConsumption() {
        return powerConsumption;
    }

    public void setPowerConsumption(String powerConsumption) {
        this.powerConsumption = powerConsumption;
    }

    public String getContractPrice() {
        return contractPrice;
    }

    public void setContractPrice(String contractPrice) {
        this.contractPrice = contractPrice;
    }

    public String getRecoveryElectricityFlag() {
        return recoveryElectricityFlag;
    }

    public void setRecoveryElectricityFlag(String recoveryElectricityFlag) {
        this.recoveryElectricityFlag = recoveryElectricityFlag;
    }

    public String getThisElectricityPrice() {
        return thisElectricityPrice;
    }

    public void setThisElectricityPrice(String thisElectricityPrice) {
        this.thisElectricityPrice = thisElectricityPrice;
    }

    public String getThisElectricityTax() {
        return thisElectricityTax;
    }

    public void setThisElectricityTax(String thisElectricityTax) {
        this.thisElectricityTax = thisElectricityTax;
    }

    public String getCcoer() {
        return ccoer;
    }

    public void setCcoer(String ccoer) {
        this.ccoer = ccoer;
    }

    public String getCdcf() {
        return cdcf;
    }

    public void setCdcf(String cdcf) {
        this.cdcf = cdcf;
    }

    public String getGeneratorCode() {
        return generatorCode;
    }

    public void setGeneratorCode(String generatorCode) {
        this.generatorCode = generatorCode;
    }

    public String getElectricPowerGeneration() {
        return electricPowerGeneration;
    }

    public void setElectricPowerGeneration(String electricPowerGeneration) {
        this.electricPowerGeneration = electricPowerGeneration;
    }

    public CopyMeterInner() {
    }

    public CopyMeterInner(String budgetSet, String energyMeterCode, String energyMeterName,
                          String electricityStartDate, String electricityEndDate, String thisQuantityOfElectricity,
                          String thisElectricityCharge, String powerConsumption, String contractPrice,
                          String recoveryElectricityFlag, String thisElectricityPrice, String thisElectricityTax,
                          String ccoer, String cdcf, String generatorCode, String electricPowerGeneration) {
        this.budgetSet = budgetSet;
        this.energyMeterCode = energyMeterCode;
        this.energyMeterName = energyMeterName;
        this.electricityStartDate = electricityStartDate;
        this.electricityEndDate = electricityEndDate;
        this.thisQuantityOfElectricity = thisQuantityOfElectricity;
        this.thisElectricityCharge = thisElectricityCharge;
        this.powerConsumption = powerConsumption;
        this.contractPrice = contractPrice;
        this.recoveryElectricityFlag = recoveryElectricityFlag;
        this.thisElectricityPrice = thisElectricityPrice;
        this.thisElectricityTax = thisElectricityTax;
        this.ccoer = ccoer;
        this.cdcf = cdcf;
        this.generatorCode = generatorCode;
        this.electricPowerGeneration = electricPowerGeneration;
    }

    /**
     * 账期
     */
    private String budgetSet;
    /**
     * 计量设备编码
     */
    private String energyMeterCode;
    /**
     * 计量设备名称
     */
    private String energyMeterName;
    /**
     * 开始时间
     */
    private String electricityStartDate;
    /**
     * 截止时间
     */
    private String electricityEndDate;
    /**
     * 本次能耗数量
     */
    private String thisQuantityOfElectricity;
    /**
     * 本次含税费用
     */
    private String thisElectricityCharge;
    /**
     * 设备耗能量
     */
    private String powerConsumption;
    /**
     * 能源单价
     */
    private String contractPrice;
    /**
     * 回收费用标识
     */
    private String recoveryElectricityFlag;
    /**
     * 本次不含税金额
     */
    private String thisElectricityPrice;
    /**
     * 本次税额
     */
    private String thisElectricityTax;
    /**
     * 折标系数
     */
    private String ccoer;
    /**
     * 碳排放因子
     */
    private String cdcf;
    /**
     * 油机编码
     */
    private String generatorCode;
    /**
     * 发电量
     */
    private String electricPowerGeneration;
}
