package com.sccl.modules.business.stationinfo.controller;

import com.alibaba.fastjson.JSONObject;
import com.sccl.common.support.Convert;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.stationinfo.domain.StationInfo;
import com.sccl.modules.business.stationinfo.domain.StationRecord;
import com.sccl.modules.business.stationinfo.service.IStationRecordService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 局站历史 信息操作处理
 * 
 * <AUTHOR>
 * @date 2019-05-31
 */
@RestController
@RequestMapping("/business/stationRecord")
public class StationRecordController extends BaseController
{
    private String prefix = "business/stationRecord";
	
	@Autowired
	private IStationRecordService stationRecordService;
	
	@RequiresPermissions("business:stationRecord:view")
	@GetMapping()
	public String stationRecord()
	{
	    return prefix + "/stationRecord";
	}
	
	/**
	 * 查询局站历史列表
	 */
	@RequiresPermissions("business:stationRecord:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(StationRecord stationRecord)
	{
		startPage();
        List<StationRecord> list = stationRecordService.selectList(stationRecord);
		return getDataTable(list);
	}
	
	/**
	 * 新增局站历史
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存局站历史
	 */
	@RequiresPermissions("business:stationRecord:add")
	//@Log(title = "局站历史", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody StationRecord stationRecord)
	{		
		return toAjax(stationRecordService.insert(stationRecord));
	}

	/**
	 * 修改局站历史
	 */
	@GetMapping("/edit/{rid}")
	public AjaxResult edit(@PathVariable("rid") Long rid)
	{
		StationRecord stationRecord = stationRecordService.get(rid);

		Object object = JSONObject.toJSON(stationRecord);

        return this.success(object);
	}
	
	/**
	 * 修改保存局站历史
	 */
	@RequiresPermissions("business:stationRecord:edit")
	//@Log(title = "局站历史", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody StationRecord stationRecord)
	{		
		return toAjax(stationRecordService.update(stationRecord));
	}
	
	/**
	 * 删除局站历史
	 */
	@RequiresPermissions("business:stationRecord:remove")
	//@Log(title = "局站历史", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(stationRecordService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看局站历史
     */
    @RequiresPermissions("business:stationRecord:view")
    @GetMapping("/view/{rid}")
    @ResponseBody
    public AjaxResult view(@PathVariable("rid") Long rid)
    {
		StationRecord stationRecord = stationRecordService.get(rid);

        Object object = JSONObject.toJSON(stationRecord);

        return this.success(object);
    }
	/**
	 * 获取最新一条展示修改前后的差异
	 * @param id
	 * @return
	 */
	@RequestMapping("/newlistview")
	@ResponseBody
	public StationRecord getNewStationToDisplay(@RequestParam("id") String id,@RequestParam("userid") String userid) {
		StationRecord stationRecord = stationRecordService.getStationNewInfoView(id,userid);
		return stationRecord;
	}
	/**
	 * 获取当前用户记录表最新一条再修改页面展示
	 * @param id
	 * @return
	 */
	@RequestMapping("/newlist")
	@ResponseBody
	public StationRecord getNewStation(@RequestParam("id") String id) {
		StationRecord stationRecord = stationRecordService.getStationNewInfo(id);
		return stationRecord;
	}

	@RequestMapping("/recordInfo")
	@ResponseBody
	public StationInfo getStationInfoFromRecord(@RequestParam("id") String id) {
		StationInfo stationInfo=new StationInfo();
		StationRecord stationRecord = stationRecordService.getStationNewInfo(id);
		if(stationRecord!=null){
			BeanUtils.copyProperties(stationRecord,stationInfo);
			stationInfo.setId(Long.parseLong(id));

		}else{
			stationInfo.setId(null);
		}

		return stationInfo;
	}
	@RequestMapping("/newrecordbyid")
	@ResponseBody
	public StationInfo getStationNewInfoById(@RequestParam("id") String id) {
		StationInfo stationInfo=new StationInfo();
		StationRecord stationRecord = stationRecordService.getStationNewInfoById(id);
		if(stationRecord!=null){
			BeanUtils.copyProperties(stationRecord,stationInfo);
			stationInfo.setId(Long.parseLong(id));

		}else{
			stationInfo.setId(null);
		}

		return stationInfo;
	}

}
