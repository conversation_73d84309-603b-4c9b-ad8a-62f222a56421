package com.sccl.modules.business.oilreimbursement.service;

import com.sccl.modules.business.energyaccountbillpre.domain.EnergyAccountbillpre;
import com.sccl.modules.business.oilcardaccount.domain.OilCardAccount;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;
import com.sccl.modules.mssaccount.mssaccountbillitem.domain.MssAccountbillitem;
import com.sccl.modules.mssaccount.rbillitemaccount.domain.RBillitemAccount;

import java.util.List;

/**
 * @Description
 * @Auther Huang Yongxiang
 * @Date 2021/12/21 10:46
 */
public interface OilReimbursementService {
    /**
     * 获取归集单关联的台账列表
     *
     * @param pabrid 归集单ID
     * @return java.util.List<com.sccl.modules.business.oilcardaccount.domain.OilCardAccount>
     * <AUTHOR>
     * @date 2021/12/21 12:51
     */
    List<OilCardAccount> getAccountsOfEnergyAccountBillpre(Long pabrid);

    /**
     * 插入一条新的报账基础信息到数据库
     *
     * @param mssAccountbill 报账单基础信息对象
     * @return int
     * <AUTHOR> Yongxiang
     * @date 2021/12/21 14:35
     */
    int insertNewMssAccountBill(MssAccountbill mssAccountbill);

    /**
     * 将报账单基础信息与归集单相关联
     *
     * @param energyAccountbillpre 包含关联关联报账单和归集单编号的对象
     * @return int
     * <AUTHOR> Yongxiang
     * @date 2021/12/21 14:56
     */
    int associateMssAccount(EnergyAccountbillpre energyAccountbillpre);

    /**
     * 初始化报账单，包含新建基础信息和明细信息、关联报账单到归集单、关联台账和报账单
     *
     * @param energyAccountbillmpre
     * @param mssAccountbill
     * @return boolean
     * <AUTHOR> Yongxiang
     * @date 2021/12/22 9:39
     */
    boolean createNewMssAccountBill(EnergyAccountbillpre energyAccountbillmpre, MssAccountbill mssAccountbill);

    /**
     * 新增一条明细信息
     *
     * @param mssAccountbillitem
     * @return boolean
     * <AUTHOR> Yongxiang
     * @date 2021/12/22 9:40
     */
    boolean createNewMssAccountIteBill(MssAccountbillitem mssAccountbillitem, List<RBillitemAccount> rBillitemAccounts);

    /**
     * 删除明细信息，包括删除明细信息以及相关的台账关联信息
     *
     * @param mssAccountbillitem
     * @param rBillitemAccounts
     * @return boolean
     * <AUTHOR> Yongxiang
     * @date 2021/12/28 16:31
     */
    boolean deleteMssAccountIteBill(List<MssAccountbillitem> mssAccountbillitem, List<RBillitemAccount> rBillitemAccounts);

    /**
     * 更新报账单主表的报账金额和税额
     *
     * @param mssAccountbill 包含主表ID、报账金额、税额的报账单对象
     * @return int
     * <AUTHOR> Yongxiang
     * @date 2021/12/23 16:07
     */
    int updateMssAccountMoney(MssAccountbill mssAccountbill);

    /**
     * 新增一条报账单明细到数据库
     *
     * @param mssAccountbillitem 要新增的明细对象
     * @return int
     * <AUTHOR> Yongxiang
     * @date 2021/12/21 16:37
     */
    int insertNewMssAccountIteBill(MssAccountbillitem mssAccountbillitem);

    /**
     * 更新一条报账单明细
     *
     * @param mssAccountbillitem 包含ID、不含税金额、税额等字段的报账单明细对象
     * @return int
     * <AUTHOR> Yongxiang
     * @date 2021/12/21 16:42
     */
    int saveMessAccountIteBill(MssAccountbillitem mssAccountbillitem);

    /**
     * 新增一条台账、报账单关联信息
     *
     * @param rBillitemAccounts 关联信息对象
     * @return int
     * <AUTHOR> Yongxiang
     * @date 2021/12/21 17:49
     */
    int insertNewRBillitemAccounts(List<RBillitemAccount> rBillitemAccounts);

    /**
     * 根据报账单ID查询相关的明细-台账关联信息
     *
     * @param billIds 账单ID
     * @return java.util.List<com.sccl.modules.mssaccount.mssaccountbillitem.domain.MssAccountbillitem>
     * <AUTHOR> Yongxiang
     * @date 2021/12/23 14:10
     */
    List<RBillitemAccount> getRBillAccount(String[] billIds);

    /**
     * 根据明细ID查询明细信息
     *
     * @param ids 明细ID
     * @return java.util.List<com.sccl.modules.mssaccount.mssaccountbillitem.domain.MssAccountbillitem>
     * <AUTHOR> Yongxiang
     * @date 2021/12/23 14:31
     */
    List<MssAccountbillitem> getMssAcountBillItem(String[] ids);

}
