package com.sccl.modules.business.standcostoil.controller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.business.standcostoil.domain.StandCostoil;
import com.sccl.modules.business.standcostoil.service.IStandCostoilService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 平均油耗 信息操作处理
 *
 * <AUTHOR>
 * @date 2022-05-09
 */
@RestController
@RequestMapping("/business/standCostoil")
public class StandCostoilController extends BaseController
{
    private String prefix = "business/standCostoil";

	@Autowired
	private IStandCostoilService standCostoilService;

	@RequiresPermissions("business:standCostoil:view")
	@GetMapping()
	public String standCostoil()
	{
	    return prefix + "/standCostoil";
	}

	/**
	 * *****单位油耗 - con
	 * <AUTHOR>
	 * @date 2022/5/9
	 */
	//@RequiresPermissions("business:standCostoil:list")
	@RequestMapping("/list")
	@ResponseBody
	public BigDecimal list(StandCostoil standCostoil)
	{
		BigDecimal power = standCostoil.getPower();
		standCostoil.setPower(null);
		List<StandCostoil> list = standCostoilService.selectList(standCostoil);
		List<BigDecimal> bigList = new ArrayList<>();
		for (StandCostoil costoil : list) {
			BigDecimal power1 = costoil.getPower();
			if (power.compareTo(power1) <= 0) {
				bigList.add(costoil.getUnitOilCost());
			}
		}
		if (bigList.size()==0)
			bigList.add(list.get(0).getUnitOilCost());
		Collections.sort(bigList);

		return bigList.get(0);
	}

	/**
	 * 新增平均油耗
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}

	/**
	 * 新增保存平均油耗
	 */
	//@RequiresPermissions("business:standCostoil:add")
	@Log(title = "平均油耗", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody StandCostoil standCostoil)
	{
		return toAjax(standCostoilService.insert(standCostoil));
	}

	/**
	 * 修改平均油耗
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		StandCostoil standCostoil = standCostoilService.get(id);

		Object object = JSONObject.toJSON(standCostoil);

        return this.success(object);
	}

	/**
	 * 修改保存平均油耗
	 */
	@RequiresPermissions("business:standCostoil:edit")
	@Log(title = "平均油耗", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody StandCostoil standCostoil)
	{
		return toAjax(standCostoilService.update(standCostoil));
	}

	/**
	 * 删除平均油耗
	 */
	@RequiresPermissions("business:standCostoil:remove")
	@Log(title = "平均油耗", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{
		return toAjax(standCostoilService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看平均油耗
     */
    @RequiresPermissions("business:standCostoil:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		StandCostoil standCostoil = standCostoilService.get(id);

        Object object = JSONObject.toJSON(standCostoil);

        return this.success(object);
    }

}
