package com.sccl.modules.mssaccount.mssinterface.mapper;

import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.meterdatesfortwoc.domain.WriteoffDetailInfo3;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;
import com.sccl.modules.mssaccount.mssinterface.domain.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * MSS财务接口 数据层
 *
 * <AUTHOR>
 * @date 2019-04-24
 */
public interface MssInterfaceMapper extends BaseMapper<MssInterface> {


    List<ViewOrgSapCostCenter> selectOrgSapCenterByOrgCode(String[] ids);

    List<ViewOrgSapCostCenter> selectOrgSapCenterByOrgCodeSC(String[] ids);

    void insertRUserViewOrg(RUserViewOrg rUserViewOrg);

    void updateRUserViewOrg(RUserViewOrg rUserViewOrg);

    List<RUserViewOrg> selectRUserViewOrg(RUserViewOrg rUserViewOrg);

    List<WriteoffDetailInfo> selectWriteoffDetailInfo(Long id);
    List<WriteoffDetailInfo> selectWriteoffDetailInfoAddPcids(Long id);
    List<WriteoffDetailInfo2> selectWriteoffDetailInfo2(@Param("ID") Long id);
    List<WriteoffDetailInfo2> selectWriteoffDetailInfo3(@Param("ID") Long id);
    List<WriteoffDetailInfo> selectWriteoffDetailInfohis(Long id);
    List<WriteoffDetailInfo2> selectWriteoffDetailInfohis2(@Param("ID") Long id);
    List<WriteoffDetailInfo2> selectWriteoffDetailInfohis3(@Param("ID") Long id);
    List<WriteoffDetailInfo> selectWriteoffDetailInforecovery(Long id);
    List<WriteoffDetailInfo> selectWriteoffDetailInforecoveryAddPcids(Long id);
    List<WriteoffDetailInfo2> selectWriteoffDetailInforecovery2(@Param("ID") Long id);
    List<WriteoffDetailInfo2> selectWriteoffDetailInforecovery3(@Param("ID") Long id);
    List<WriteoffDetailInfo> selectWriteoffDetailInforecoveryhis(Long id);
    List<WriteoffDetailInfo2> selectWriteoffDetailInforecoveryhis2(@Param("ID") Long id);
    List<WriteoffDetailInfo2> selectWriteoffDetailInforecoveryhis3(@Param("ID") Long id);

    /**
     * 抄表数据
     * @param id
     * @return
     */
    void updateAmmeterno(Long id);
}