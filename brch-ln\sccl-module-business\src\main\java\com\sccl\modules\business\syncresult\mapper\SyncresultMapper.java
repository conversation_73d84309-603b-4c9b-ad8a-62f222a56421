package com.sccl.modules.business.syncresult.mapper;

import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.stationaudit.delayedtask.DelayCache;
import com.sccl.modules.business.syncresult.domain.Syncresult;
import com.sccl.modules.dataperfect.domain.*;
import com.sccl.modules.mssaccount.mssinterface.domain.*;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;

/**
 * 同步结果 数据层
 *
 * <AUTHOR>
 * @date 2023-02-27
 */
public interface SyncresultMapper extends BaseMapper<Syncresult> {


    int insertMeterInfos(List<MeterInfoDb> dbs);

    int insertWriteoffInfos(List<WriteoffInfoDb> dbs);

    /**
     * 全量局站
     *
     * @param stationcode
     * @return
     */
    List<String> selectAllstation(@Param("stationcode") String stationcode);

    List<ColleterPerfect> selectcolleterPerfects(@Param("lastMonth") String lastMonth);

    int insertJzForMeterinfo(@Param("list") List<String> jzlist);

    int insertJznotForMeterinfo(@Param("list") List<String> notjzlist);

    int insertMeterinfoAll(@Param("list") List<String> insertStationcodes);


    List<? extends MeterInfo2> selectJzForMeterinfo(@Param("list") List<String> jzlist);

    List<? extends MeterInfo2> selectJznotForMeterinfo(@Param("list") List<String> notjzlist);

    List<? extends MeterInfo2> selectForMeterinfoAll(@Param("list") List<String> listAll);

    int insertMeterinfos(@Param("stationcode") String stationcode);

    List<? extends MeterInfo2> selectMeterinfo(@Param("stationcode") String stationcode);

    List<? extends MeterInfo2> selectDelete(@Param("list") List<MeterInfo> distinctMeterInfoList);

    List<String> selectNewStationcdoes(@Param("list") List<String> stationcodes);

    int delMeterinfoForStatoncode(List<String> listadd);

    List<String> selectJzStationcode(@Param("list") List<String> jzstationids);

    List<? extends MeterInfo2> selectSysnc(@Param("list") List<String> addAll);

    void insertMeterinfoAll2(List<? extends MeterInfo2> meterinAddAll);

    //方案2
    List<String> selectContractPrice(@Param("list") List<String> energyMeterCodes);

    boolean getauditFlag();

    int insertFailCollect(@Param("list") List<CollectMeter> infosDb);

    List<CollectMeterVo> selectFailCollectMeter(@Param("limit") int limit, @Param("offset") int offset);

    List<CollectMeter> selectFailCollectMeterByVo(CollectMeterVo vo);

    int updateRetryFailCollecter(@Param("list") List<CollectMeter> dbs);

    List<MeterInfo2> selectExists(@Param("list") List<MeterInfo2> deduplicateMeterInfoList);

    int insertMeterinfoBitch(@Param("list") List<MeterInfo2> notExists);

    int deleteMeterinoBitch(@Param("list") List<MeterInfo2> existStacodeNot);

    List<String> selectResstationcode(@Param("limit") int limit, @Param("offset") int offset);

    int insertStaTemp(@Param("list") List<String> resStationodes);

    int insertListForDelayCache(@Param("list") List<DelayCache> list);

    int updateDelayCache(@Param("key") String key, @Param("expireTime") long expireTime, @Param("status") String status);

    List<Tempt> selectTempt(@Param("limit") int limit, @Param("offset") int offset);

    int insertStaResult(@Param("list") List<StaResult> staResults);

    List<AccountTime> collectAll(@Param("budget") String budget);
    List<AccountTime> collectAllPro(@Param("budget") String budget);
    List<AccountTime> collectAllProGroupStationCode(@Param("budget") String budget);
    List<AccountTime> collectAllProGroupAmmeterpol(@Param("budget") String budget);
    // 查询条件优化，避免查不到数据
    List<AccountTime> collectAllProGroupAmmeterpoljtmss(@Param("budget") String budget);
    List<AccountTime> collectAllProGroupAll(@Param("budget") String budget);
    List<AccountTime> collectAllProGroupAmmeterpolConsist(@Param("budget") String budget);
    int updateRoomEnergyUse(@Param("list") List<MachineRoomEnergyUseEntity> dbs);
    int updateStationEnergyUse(@Param("list") List<StationEnergyUseEntity> dbs);

    int deleteAll(@Param("budget") String budget);

    List<CollectMeterFail> generateSyncCollect(@Param("budget") String budget);

    List<CollectMeter> getCollect();

    /**
     * 根据采集时间查询collectmeter表数据
     *
     * @return 采集数据列表
     */
    List<CollectMeter> getCollectMeterByTime();

    String getavgForGeteWay(@Param("resstationcode") String resstationcode, @Param("startdate")String startdate, @Param("enddate") String enddate);

    List<StationGateway> selectStationGateway(@Param("limit") int limit, @Param("offset") int offset);

    List<StationGateway> selectStationMapTime(List<String> stationcodes);

    List<StationDeviceAvg> selectStationDeviceAvg(List<String> stationcodes);

    int insertStagateway2(List<StationGateway> tempts);

    List<StationGateway> selectStationAccountAvgException(@Param("year") Integer year, @Param("month") Integer month, @Param("limit") int limit, @Param("offset") int offset);

    List<StationGateway> selectDayAvgs(@Param("list") List<String> stationcodes);

    List<StationGateway> getavgForGeteWayPro(@Param("list") List<StationGateway> dbs);

    int insertAccountAvgException(@Param("list") List<StationGateway> dbs);

    int  deleteAccountAvgException(@Param("time") String time);

    List<AccountTime> selectAllProGroupCheck(@Param("budget") String budget);

    void deleteNowBudget(@Param("budget") String budget);

    List<AccountTimeVo> collectAllProGroupChecknewAll(@Param("budget") String budget);

    void insetAllProGroupCheck(@Param("list") List<AccountTimeVo> list);

    List<HashMap<String, Object>> selectAllProGroupCheckQxm(@Param("budget") String budget);

    /**
     * 获取手动采集的数据
     * @return
     */
    List<AccountTime> manualAcquisition();

    /**
     * 查询 ycyz_null 表中的局站编码
     *
     * @return 局站编码列表
     */
    List<String> selectYcyzNullStationCodes();
}
