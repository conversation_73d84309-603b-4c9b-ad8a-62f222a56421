package com.sccl.modules.rental.rentalcarmodelmain.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.modules.rental.rentalcarmain.domain.Rentalcarmain;
import com.sccl.modules.rental.rentalcarmodel.domain.Rentalcarmodel;
import com.sccl.modules.rental.rentalcarmodel.service.IRentalcarmodelService;
import com.sccl.modules.system.role.domain.Role;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.service.IUserService;
import com.sccl.modules.uniflow.wfprocinst.service.IWfProcInstService;
import com.sccl.modules.uniflow.wftask.domain.WfTask;
import com.sccl.modules.uniflow.wftask.service.IWfTaskService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.rental.rentalcarmodelmain.domain.Rentalcarmodelmain;
import com.sccl.modules.rental.rentalcarmodelmain.service.IRentalcarmodelmainService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 车辆 （车型主单） 信息操作处理
 *
 * <AUTHOR>
 * @date 2019-08-23
 */
@RestController
@RequestMapping("/rental/rentalcarmodelmain")
public class RentalcarmodelmainController extends BaseController {
    private String prefix = "rental/rentalcarmodelmain";
    @Autowired
    private IUserService userService;
    @Autowired
    private IWfProcInstService wfProcInstService;// 流程
    @Autowired
    private IWfTaskService wfTaskService;// 流程
    @Autowired
    private IRentalcarmodelmainService rentalcarmodelmainService;
    @Autowired
    private IRentalcarmodelService rentalcarmodelService;

    @RequiresPermissions("rental:rentalcarmodelmain:view")
    @GetMapping()
    public String rentalcarmodelmain() {
        return prefix + "/rentalcarmodelmain";
    }

    /**
     * 查询车辆 （车型主单）列表
     */
    @RequiresPermissions("rental:rentalcarmodelmain:list")
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(Rentalcarmodelmain rentalcarmodelmain) {
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("PROVI_")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
            if (role.getCode().startsWith("SUB_")) {//县能耗费管理员
                isSubAdmin = true;
            }
        }
        if (isProAdmin) {//  查询权限设置 分公司
        } else if (isCityAdmin) {
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0)
                rentalcarmodelmain.setCompany(companies.get(0).getId());
        } else if (isSubAdmin) {
            List<IdNameVO> departments = user.getDepartments();
            if (departments != null && departments.size() > 0)
                rentalcarmodelmain.setCountry(departments.get(0).getId());
        } else {
            rentalcarmodelmain.setInputuserid(user.getId());
        }
        startPage();
        List<Rentalcarmodelmain> list = rentalcarmodelmainService.selectList(rentalcarmodelmain);
        for (Rentalcarmodelmain r : list) {
            if (!"1".equals(r.getStatus())) {
                r.set_disabled(true);
            } else {
                r.set_disabled(false);
            }
        }
        return getDataTable(list);
    }

    /**
     * 新增车辆 （车型主单）
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存车辆 （车型主单）
     */
    @RequiresPermissions("rental:rentalcarmodelmain:add")
    //@Log(title = "车辆 （车型主单）", action = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody Rentalcarmodelmain rentalcarmodelmain) {
        return rentalcarmodelmainService.saveRentalcarmodelmain(rentalcarmodelmain);
    }

    /**
     * 修改车辆 （车型主单）
     */
    @GetMapping("/edit/{rmmid}")
    public AjaxResult edit(@PathVariable("rmmid") Long rmmid) {
        Rentalcarmodelmain rentalcarmodelmain = rentalcarmodelmainService.get(rmmid);

        Object object = JSONObject.toJSON(rentalcarmodelmain);

        return this.success(object);
    }

    /**
     * 修改保存车辆 （车型主单）
     */
    @RequiresPermissions("rental:rentalcarmodelmain:edit")
    //@Log(title = "车辆 （车型主单）", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody Rentalcarmodelmain rentalcarmodelmain) {
        return toAjax(rentalcarmodelmainService.update(rentalcarmodelmain));
    }

    /**
     * 删除车辆 （车型主单）
     */
    @RequiresPermissions("rental:rentalcarmodelmain:remove")
    //@Log(title = "车辆 （车型主单）", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        int i = 0;
        try {
            List<Rentalcarmodelmain> rentalcarmodelmains = rentalcarmodelmainService.selectListByIds(Convert.toStrArray(ids));
            for (Rentalcarmodelmain b : rentalcarmodelmains) {
                if (!"1".equals(b.getStatus())) {
                    return this.error(1, b.getSetitle() + "(" + b.getRmmid() + ")" + "草稿状态才能删除");
                }
                // 终止流程
                killFlow(b);
            }
            i = rentalcarmodelmainService.deleteAndItemByIds(Convert.toStrArray(ids));
            return this.success("删除(" + i + ")条");
        } catch (Exception e) {
            e.printStackTrace();
            return this.error(1, "删除失败:" + e.getMessage());
        }
    }

    // 终止流程
    private void killFlow(Rentalcarmodelmain b) throws Exception {
        if (b.getIprocessinstid() != null) {
            WfTask wfTask = new WfTask();
            wfTask.setProcInstId(b.getIprocessinstid().toString());// 流程实例id
            List<WfTask> wfTasks = wfTaskService.selectList(wfTask);
            if (wfTasks != null && wfTasks.size() > 0) {
                Map<String, Object> param = new HashMap<>();
                param.put("procTaskId", wfTasks.get(0).getId());
                param.put("procInstId", b.getIprocessinstid());
                param.put("shardKey", "0000");
                param.put("stop", "sys");
                JSONObject jsonObject = wfProcInstService.stopTask(this.getCurrentUser(), param);
                System.out.println(jsonObject.toJSONString());
            }
        }
    }

    /**
     * 查看车辆 （车型主单）
     */
    @RequiresPermissions("rental:rentalcarmodelmain:view")
    @GetMapping("/view/{rmmid}")
    @ResponseBody
    public AjaxResult view(@PathVariable("rmmid") Long rmmid) {
        Rentalcarmodelmain rentalcarmodelmain = new Rentalcarmodelmain();
        rentalcarmodelmain.setRmmid(rmmid);
        List<Rentalcarmodelmain> rentalcarmodelmains = rentalcarmodelmainService.selectList(rentalcarmodelmain);
        if (rentalcarmodelmains != null && rentalcarmodelmains.size() > 0) {
            rentalcarmodelmain = rentalcarmodelmains.get(0);
            Map<String, Object> map = new HashMap<>();
            map.put("rmmid", rmmid);
            List<Rentalcarmodel> rentalcarmodels = rentalcarmodelService.selectBy(map);
            if (rentalcarmodels != null && rentalcarmodels.size() > 0)
                rentalcarmodelmain.setRentalcarmodelList(rentalcarmodels);
            else
                rentalcarmodelmain.setRentalcarmodelList(new ArrayList<Rentalcarmodel>());
            Object object = JSONObject.toJSON(rentalcarmodelmain);

            return this.success(object);
        } else {
            return this.error("没查询到车型 申请信息");
        }
    }

}
