package com.sccl.modules.rental.rentalcarmodelmain.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.common.exception.base.BaseException;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.service.IdGenerator;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.rental.rentalcarmodel.domain.Rentalcarmodel;
import com.sccl.modules.rental.rentalcarmodel.mapper.RentalcarmodelMapper;
import com.sccl.modules.rental.rentalcarmodelmain.domain.Rentalcarmodelmain;
import com.sccl.modules.rental.rentalcarmodelmain.mapper.RentalcarmodelmainMapper;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.uniflow.common.WFModel;


/**
 * 车辆 （车型主单） 服务层实现
 *
 * <AUTHOR>
 * @date 2019-08-23
 */
@Service
public class RentalcarmodelmainServiceImpl extends BaseServiceImpl<Rentalcarmodelmain> implements IRentalcarmodelmainService {
    @Autowired
    RentalcarmodelmainMapper rentalcarmodelmainMapper;
    @Autowired
    RentalcarmodelMapper rentalcarmodelMapper;
    private static final Logger logger = LoggerFactory.getLogger(RentalcarmodelmainServiceImpl.class);

    @Override
    public List<Rentalcarmodelmain> selectListByIds(String[] toStrArray) {
        return rentalcarmodelmainMapper.selectListByIds(toStrArray);
    }

    @Override
    public AjaxResult saveRentalcarmodelmain(Rentalcarmodelmain rentalcarmodelmain) {
        AjaxResult rs = new AjaxResult();
        if (rentalcarmodelmain.getRmmid() == null) {
            insertRentalcarmodelmain(rentalcarmodelmain, rs);
        } else {
            updateRentalcarmodelmain(rentalcarmodelmain, rs);
        }
        return rs;
    }

    @Override
    public int deleteAndItemByIds(String[] toStrArray) {
        rentalcarmodelMapper.deleteByRmmids(toStrArray);
        return rentalcarmodelmainMapper.deleteByIdsDB(toStrArray);
    }

    private void updateRentalcarmodelmain(Rentalcarmodelmain rentalcarmodelmain, AjaxResult rs) {
        rentalcarmodelmainMapper.updateForModel(rentalcarmodelmain);
        Long rmmid = rentalcarmodelmain.getRmmid();
        User user = ShiroUtils.getUser();
        Long userid = user.getId();
        String company = user.getCompanies().get(0).getId();
        String country = user.getDepartments().get(0).getId();
        String name = user.getUserName();
        Date date = new Date();
        List<Rentalcarmodel> rentalcarmodelList = rentalcarmodelmain.getRentalcarmodelList();
        List<Rentalcarmodel> newlist = new ArrayList<>();
        for (Rentalcarmodel m : rentalcarmodelList) {
            if (m.getModelid() == null) {
                m.setModelid(IdGenerator.getNextId());
                m.setRmmid(rmmid);
                m.setInputuserid(userid);
                m.setCompany(company);
                m.setCountry(country);
                m.setInputdate(date);
                m.setStatus("1");
                m.setInputusername(name);
                newlist.add(m);
            } else {
                rentalcarmodelMapper.updateForModel(m);//更新旧的
            }
        }
        // 插入 新的
        if (newlist.size() > 0)
            rentalcarmodelMapper.insertList(newlist);
        rentalcarmodelmain.setRentalcarmodelList(rentalcarmodelList);
        rs.put("data", rentalcarmodelmain);
    }

    private void insertRentalcarmodelmain(Rentalcarmodelmain rentalcarmodelmain, AjaxResult rs) {
        long nextId = IdGenerator.getNextId();
        User user = ShiroUtils.getUser();
        Long userid = user.getId();
        String company = user.getCompanies().get(0).getId();
        String country = user.getDepartments().get(0).getId();
        String name = user.getUserName();
        Date date = new Date();
        rentalcarmodelmain.setRmmid(nextId);
        rentalcarmodelmain.setInputusername(name);
        rentalcarmodelmain.setInputuserid(userid);
        rentalcarmodelmain.setCompany(company);
        rentalcarmodelmain.setCountry(country);
        rentalcarmodelmain.setInputdate(date);
        rentalcarmodelmain.setStatus("1");
        rentalcarmodelmainMapper.insert(rentalcarmodelmain);
        List<Rentalcarmodel> rentalcarmodelList = rentalcarmodelmain.getRentalcarmodelList();
        for (Rentalcarmodel m : rentalcarmodelList) {
            m.setModelid(IdGenerator.getNextId());
            m.setRmmid(nextId);
            m.setInputuserid(userid);
            m.setCompany(company);
            m.setCountry(country);
            m.setInputdate(date);
            m.setStatus("1");
            m.setInputusername(name);
        }
        if (rentalcarmodelList != null && rentalcarmodelList.size() > 0)
            rentalcarmodelMapper.insertList(rentalcarmodelList);
        rentalcarmodelmain.setRentalcarmodelList(rentalcarmodelList);
        rs.put("data", rentalcarmodelmain);
    }


    /**
     * 流程引擎回调（通过MQ）
     *
     * @param wfModel 流程消息对象
     */
//    @Override
    public void uniflowCallBack(WFModel wfModel) {
//        System.out.println("-----------------lt:" + wfModel.toString());
        logger.debug("-----------------lt:" + wfModel.toString());
        if ("PROCESS_STARTED".equals(wfModel.getCallbackType())) {//更新流程Id  流程 提交
            try {
//                草稿	1，代办	2,退回	3，删除	4,完成	5
                setStatus(wfModel, "2");
                logger.debug("车辆租赁信息录入审批新增" + wfModel.getBusiId());
            } catch (NumberFormatException e) {
                e.printStackTrace();
                logger.error("提交流程失败:" + e.getMessage());
                throw new BaseException("提交流程失败:" + e.getMessage());//
            }
        } else if ("PROCESS_COMPLETED".equals(wfModel.getCallbackType())) {//
//            流程完成后执行的回调  //                草稿	1，代办	2,退回	3，删除	4,完成	5
            setStatus(wfModel, "5");
        } else if ("TURNBACK_TO_START".equals(wfModel.getCallbackType())) {//流程 退回
            //                草稿	1，代办	2,退回	3，删除	4,完成	5
            setStatus(wfModel, "1");// 改为草稿 可重新提交
        } else if ("PROCESS_CANCELLED".equals(wfModel.getCallbackType())) {//流程 终止
            //                草稿	1，代办	2,退回	3，删除	4,完成	5
            setStatus(wfModel, "1");
        }
    }

    private void setStatus(WFModel wfModel, String status) {
        Rentalcarmodelmain m = new Rentalcarmodelmain();
        m.setIprocessinstid(wfModel.getProcInstId());
        m.setRmmid(Long.valueOf(wfModel.getBusiId()));
        m.setStatus(status);// 流程中
        rentalcarmodelmainMapper.updateForModel(m);

        Long rmmid = m.getRmmid();
        Rentalcarmodel ms = new Rentalcarmodel();
        ms.setRmmid(rmmid);
        List<Rentalcarmodel> rentalcarmodels = rentalcarmodelMapper.selectList(ms);
        if (rentalcarmodels != null && rentalcarmodels.size() > 0) {
            for (Rentalcarmodel c : rentalcarmodels) {
                c.setStatus(status);
                rentalcarmodelMapper.updateForModel(c);
            }
        }
    }
}
