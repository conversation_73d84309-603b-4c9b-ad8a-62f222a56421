package com.sccl.modules.business.noderesult.domain;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 汇总 台账波动
 */
@Data
public class CollectPowerChange {
    /**
     * 同一站址 本次台账
     */
    private NodeResult powerNow;
    /**
     * 同一站址 上次台账
     */
    private NodeResult powerLast;
    /**
     *  本次相对于上次 偏离 电量
     */
    private BigDecimal widePower;
    /**
     * 本次相对于上次 偏离 金额
     */
    private BigDecimal wideAccount;
    /**
     * 附加信息
     */
    private String msg;
}
