package com.sccl.modules.business.poweraudit.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.sccl.framework.utils.ExcelUtil;
import com.sccl.modules.business.account.domain.AccountBaseResult;
import com.sccl.modules.business.account.domain.AccountQua;
import com.sccl.modules.business.account.mapper.AccountMapper;
import com.sccl.modules.business.accountEs.domain.AccountEsResult;
import com.sccl.modules.business.accountEs.mapper.PowerAccountEsMapper;
import com.sccl.modules.business.poweraudit.entity.*;
import com.sccl.modules.business.poweraudit.mapper.PowerAuditMapper;
import com.sccl.modules.business.poweraudit.service.PowerAuditService;
import com.sccl.modules.business.statinAudit.domain.AuditResults;
import com.sccl.modules.business.statinAudit.util.StationAuditUtil;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-22
 */
@Service
public class PowerAuditServiceImp extends ServiceImpl<PowerAuditMapper, PowerAuditEntity> implements PowerAuditService {


    @Autowired
    private PowerAuditMapper powerAuditMapper;

    @Autowired
    private AccountMapper accountMapper;
    @Autowired
    private PowerAccountEsMapper powerAccountEsMapper;




    @Override
    public List<PowerAuditDTO> getPowerAuditByCity(PowerAuditVO powerAuditVO) {

        // 只查询当前用户下的组织架构
//        User user = ShiroUtils.getUser();
//        if(user.getOrganization() != null && powerAuditVO.getCity() == null) powerAuditVO.setCity(user.getOrganization().getOrgName());
//        if(user.getOrganization() == null) return null;
        List<PowerAuditDTO> list = powerAuditMapper.getPowerAuditByCity(powerAuditVO);

        return list;
    }

    @Override
    public List<PowerAuditDTO> getPowerAuditCompanies(PowerAuditVO powerAuditVO) {
        if(StringUtils.isEmpty(powerAuditVO.getCityCode())) return new ArrayList<>();
        List<PowerAuditDTO> list = powerAuditMapper.getPowerAuditCompanies(powerAuditVO);
        return list;
    }

    @Override
    public List<PowerAuditDTO> getAuditOperationsBranch(PowerAuditVO powerAuditVO) {
        List<PowerAuditDTO> list = powerAuditMapper.getAuditOperationsBranch(powerAuditVO);
        return list;
    }

    @Override
    public void exportPowerAudit(HttpServletResponse response,PowerAuditVO powerAuditVO, String fileName) {
        List<PowerAuditDTO> list;
//        if(powerAuditVO.getType() == null ) powerAuditVO.setType("地市");
//        if(powerAuditVO.getType().equals("地市")) list = powerAuditMapper.getPowerAuditByCity(powerAuditVO);
        if(ObjectUtil.isNotEmpty(powerAuditVO.getType()) && powerAuditVO.getType().equals("运营分局")) {
            list = powerAuditMapper.getPowerAuditCompanies(powerAuditVO);
        } else {
            list = powerAuditMapper.getPowerAuditByCity(powerAuditVO);
        }

        ExcelUtil<PowerAuditDTO> excelUtil = new ExcelUtil<>(PowerAuditDTO.class);
        excelUtil.exportExcelToBrowser(response,list,fileName);
    }

    @Override
    public PowerAuditDTO auditTotal(PowerAuditVO powerAuditVO) {

        List<PowerAuditDTO> listPowerAudit = new ArrayList<>();
        if(powerAuditVO.getType() == null ) powerAuditVO.setType("区县");
        listPowerAudit = powerAuditMapper.getPowerAuditByCity(powerAuditVO);
//        if(powerAuditVO.getType().equals("地市")) listPowerAudit = powerAuditMapper.getPowerAuditByCity(powerAuditVO);
//        if(powerAuditVO.getType().equals("运营分局")) listPowerAudit = powerAuditMapper.getPowerAuditCompanies(powerAuditVO);

        PowerAuditDTO powerAudit = new PowerAuditDTO();
        powerAudit.setCityCode(powerAuditVO.getCityCode());
        powerAudit.setMonth(powerAuditVO.getMonth());
        powerAudit.setSiteType(powerAuditVO.getSiteType());
        powerAudit.setSums(listPowerAudit.stream().mapToInt(PowerAuditDTO::getSums).sum());
        powerAudit.setMutiJtlteCodes(listPowerAudit.stream().mapToInt(PowerAuditDTO::getMutiJtlteCodes).sum());
        powerAudit.setElectricityPrices(listPowerAudit.stream().mapToInt(PowerAuditDTO::getElectricityPrices).sum());
        powerAudit.setAddressConsistence(listPowerAudit.stream().mapToInt(PowerAuditDTO::getAddressConsistence).sum());
        powerAudit.setPeriodicAnomaly(listPowerAudit.stream().mapToInt(PowerAuditDTO::getPeriodicAnomaly).sum());
        powerAudit.setElectricityContinuity(listPowerAudit.stream().mapToInt(PowerAuditDTO::getElectricityContinuity).sum());
        powerAudit.setElectricityRationality(listPowerAudit.stream().mapToInt(PowerAuditDTO::getElectricityRationality).sum());
        powerAudit.setElectricityContinuity(listPowerAudit.stream().mapToInt(PowerAuditDTO::getElectricityContinuity).sum());
        powerAudit.setFluctuateContinuity(listPowerAudit.stream().mapToInt(PowerAuditDTO::getFluctuateContinuity).sum());
        powerAudit.setConsumeContinuity(listPowerAudit.stream().mapToInt(PowerAuditDTO::getConsumeContinuity).sum());
        powerAudit.setShareAccuracy(listPowerAudit.stream().mapToInt(PowerAuditDTO::getShareAccuracy).sum());
        powerAudit.setReimbursementCycle(listPowerAudit.stream().mapToInt(PowerAuditDTO::getReimbursementCycle).sum());
        powerAudit.setElectricityMeter(listPowerAudit.stream().mapToInt(PowerAuditDTO::getElectricityMeter).sum());

        return powerAudit;
    }

    @Override
    public List<PowerAuditEntity> getAuditResult(List<String> ammeterids) {
        if (CollectionUtil.isEmpty(ammeterids)) return new ArrayList<>();
        List<PowerAuditEntity> list = powerAuditMapper.getAuditResult(ammeterids);
        return list;
    }

    @Override
    public PowerAuditEntity getAuditByMssAccountId(Long mssAccountId) {
        if(mssAccountId == null) return new PowerAuditEntity();
        PowerAuditEntity powerAuditEntity = powerAuditMapper.getAuditByMssAccountId(mssAccountId);
        return powerAuditEntity;
    }

    @Override
    public PowerAuditEntity getAuditByPcid(String pcid, String type) {
        if(pcid == null) return new PowerAuditEntity();
        PowerAuditEntity powerAuditEntity = powerAuditMapper.getAuditByPcid(pcid, type);
        return powerAuditEntity;
    }

    @Override
    public <T> PageInfo<T> getAuditDetails(PowerAuditVO powerAuditVO) {
//        List<PowerAuditEntity> list = powerAuditMapper.getAuditDetails(powerAuditVO);
        if(StringUtils.isEmpty(powerAuditVO.getExportName())) return null;
        powerAuditVO = setPowerAuditVOExceptionType(powerAuditVO);
        List<DetailsDTO> detailsDTOS = getDetailsDTOS(powerAuditVO);
        List<T> objects = new ArrayList<>();
        switch (powerAuditVO.getExportName()) {
            case "一站多表/一表多站":
            case "一站多表/多站多表":
                detailsDTOS = detailsDTOS.stream().filter(detailsDTO -> detailsDTO.getMutiJtlteCodes() != null && "是".equals(detailsDTO.getMutiJtlteCodes())).collect(Collectors.toList());
                objects = convertListObject(detailsDTOS,MutiJtlteDTO.class);
                break;
            case "电表站址一致性":
                detailsDTOS = detailsDTOS.stream().filter(detailsDTO ->detailsDTO.getAddressConsistence() != null &&  detailsDTO.getAddressConsistence().equals("否")).collect(Collectors.toList());
                objects = convertListObject(detailsDTOS,AddressDTO.class);
                break;
            case "台账周期连续性":
                detailsDTOS = detailsDTOS.stream().filter(detailsDTO -> detailsDTO.getPeriodicAnomaly() != null &&  detailsDTO.getPeriodicAnomaly().equals("否")).collect(Collectors.toList());
                objects = convertListObject(detailsDTOS,PeriodicDTO.class);
                break;
            case "电表度数连续性":
                detailsDTOS = detailsDTOS.stream().filter(detailsDTO -> detailsDTO.getElectricityContinuity() != null &&  detailsDTO.getElectricityContinuity().equals("否")).collect(Collectors.toList());
                objects = convertListObject(detailsDTOS,ContinuityDTO.class);
                break;
            case "电价合理性":
                detailsDTOS = detailsDTOS.stream().filter(detailsDTO -> detailsDTO.getElectricityPrices().equals("否")).collect(Collectors.toList());
                objects = convertListObject(detailsDTOS,PriceAuditDTO.class);
                break;
            case "日均电量的波动合理性(集团5gr)":
                detailsDTOS = detailsDTOS.stream().filter(detailsDTO -> detailsDTO.getFluctuateContinuity() != null &&  detailsDTO.getFluctuateContinuity().equals("否")).collect(Collectors.toList());
                objects = convertListObject(detailsDTOS,FluctuateDTO.class);
                break;
            case "共享站分摊比例准确性":
            case "分摊比例准确性":
                detailsDTOS = detailsDTOS.stream().filter(detailsDTO -> detailsDTO.getShareAccuracy() != null &&  detailsDTO.getShareAccuracy().equals("否")).collect(Collectors.toList());
                objects = convertListObject(detailsDTOS,ShareAccuracyDTO.class);
                break;
            case "台账周期合理性":
                detailsDTOS = detailsDTOS.stream().filter(detailsDTO -> detailsDTO.getReimbursementCycle() != null && detailsDTO.getReimbursementCycle().equals("否")).collect(Collectors.toList());
                objects = convertListObject(detailsDTOS,AnomalyDTO.class);
                break;
            case "分摊比例一致性":
                detailsDTOS = detailsDTOS.stream().filter(detailsDTO ->detailsDTO.getConsistencyProportion() != null &&  detailsDTO.getConsistencyProportion().equals("否")).collect(Collectors.toList());
                objects = convertListObject(detailsDTOS,ShareAccuracyDTO.class);
                break;
            case "地市和运营分局":
                objects = convertListObject(detailsDTOS,TotalDTO.class);
                break;
        }

        PageInfo<T> pageInfo = new PageInfo<>(objects);
        List<T> subList = objects.stream() .skip((powerAuditVO.getPageNum() - 1) * powerAuditVO.getPageSize()).limit(powerAuditVO.getPageSize()).collect(Collectors.toList());
        pageInfo.setList(subList);
        return pageInfo;
    }

    private PowerAuditVO setPowerAuditVOExceptionType(PowerAuditVO powerAuditVO) {
        if(StrUtil.isBlank(powerAuditVO.getExportName())) return powerAuditVO;
        switch (powerAuditVO.getExportName()) {
            case "一站多表/一表多站":
            case "一站多表/多站多表":
                powerAuditVO.setMutiJtlteCodes("是");
                break;
            case "电表站址一致性":
                powerAuditVO.setAddressConsistence("否");
                break;
            case "台账周期连续性":
                powerAuditVO.setPeriodicAnomaly("否");
                break;
            case "电表度数连续性":
                powerAuditVO.setElectricityContinuity("否");
                break;
            case "电价合理性":
                powerAuditVO.setElectricityPrices("否");
                break;
            case "日均电量的波动合理性(集团5gr)":
                powerAuditVO.setFluctuateContinuity("否");
                break;
            case "共享站分摊比例准确性":
            case "分摊比例准确性":
                powerAuditVO.setShareAccuracy("否");
                break;
            case "台账周期合理性":
                powerAuditVO.setReimbursementCycle("否");
                break;
            case "分摊比例一致性":
                powerAuditVO.setConsistencyProportion("否");
                break;
        }
        return powerAuditVO;
    }

    @NotNull
    private List<DetailsDTO> getDetailsDTOS(PowerAuditVO powerAuditVO) {
        List<DetailsDTO> detailsDTOS = powerAuditMapper.getAuditDetailsNew(powerAuditVO);

        // 异常项赋值
        for (DetailsDTO detailsDTO : detailsDTOS) {
            String str = "";
            if(detailsDTO.getMutiJtlteCodes() != null && detailsDTO.getMutiJtlteCodes().equals("是")) str += "一站多表/多站多表、";
            if(detailsDTO.getElectricityPrices() != null && detailsDTO.getElectricityPrices().equals("否")) str += "电价合理性、";
            if(detailsDTO.getAddressConsistence() != null && detailsDTO.getAddressConsistence().equals("否")) str += "电表站址一致性、";
            if(detailsDTO.getPeriodicAnomaly() != null && detailsDTO.getPeriodicAnomaly().equals("否")) str += "台账周期连续性、";
            if(detailsDTO.getElectricityContinuity() != null && detailsDTO.getElectricityContinuity().equals("否")) str += "电表度数连续性、";
            if(detailsDTO.getElectricityRationality() != null && detailsDTO.getElectricityRationality().equals("否")) str += "电量合理性、";
            if(detailsDTO.getFluctuateContinuity() != null && detailsDTO. getFluctuateContinuity().equals("否")) str += "日均电量波动合理性、";
            if(detailsDTO.getConsumeContinuity() != null && detailsDTO.getConsumeContinuity().equals("否")) str += "日均耗电量合理性、";
            if(detailsDTO.getShareAccuracy() != null && detailsDTO.getShareAccuracy().equals("否")) str += "共享站分摊比例准确性、";
            if(detailsDTO.getReimbursementCycle() != null && detailsDTO.getReimbursementCycle().equals("否")) str += "报账周期合理性、";
            if(detailsDTO.getConsistencyProportion() != null && detailsDTO.getConsistencyProportion().equals("否")) str += "分摊比例一致性、";
            if(!str.equals("")) {
                int lastIndexOf = str.lastIndexOf("、");
                str = str.substring(0,lastIndexOf);
            }
            detailsDTO.setAbnormal(str);
        }

        List<String> pcids = new ArrayList<>();
        List<String> finalPcids = pcids;
        detailsDTOS.forEach(detailsDTO -> { if(detailsDTO.getPcid() != null) finalPcids.add(detailsDTO.getPcid());});
        // finalPcids 没有值，直接返回为 原始数据
        if(finalPcids.isEmpty()) return detailsDTOS;
        pcids = finalPcids.stream().distinct().collect(Collectors.toList());

        List<DetailsDTO> mores =  powerAuditMapper.getAuditDetailsMore(pcids, powerAuditVO.getMonth());
        // 台账日均电量波动合理性（5gr）设置集团5gr日均电量 和波动幅度
        List<String> stationcodeintidList = mores.stream().map(DetailsDTO::getStationcodeintid).filter(Objects::nonNull).collect(Collectors.toList());
        if(!stationcodeintidList.isEmpty()){
            List<AccountQua> accountQuaList = powerAuditMapper.getQuaJt5grList(stationcodeintidList);
            // 过滤掉集团5gr为空的
            List<DetailsDTO> detailsDTOList = mores.stream().filter(node->node.getStationcodeintid() != null).collect(Collectors.toList());
            detailsDTOList.forEach(node->{
                List<AccountQua> quaList = accountQuaList.stream().filter(item -> node.getStationcodeintid().equals(item.getStationcodeintId())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(quaList)) {
                    // 获取到5gr的总电量
                    String jt5grAll = get5grDayAvg(node, quaList);
                    if (StrUtil.isNotBlank(jt5grAll)) {
                        BigDecimal jt5grAllBg = new BigDecimal(jt5grAll);
                        // 集团5gr日均电量（标准电量）
                        BigDecimal degreesDay = new BigDecimal(jt5grAll).divide(new BigDecimal(node.getDays()), 2, RoundingMode.HALF_UP);
                        node.setDegreesDay(degreesDay.toString());

                        if (jt5grAllBg.compareTo(BigDecimal.ZERO) > 0 && StrUtil.isNotBlank(node.getDegrees())) {
                            // 波动幅度
                            BigDecimal degreesFluctuate = ((new BigDecimal(node.getDegrees()).subtract(new BigDecimal(jt5grAll))).divide(new BigDecimal(jt5grAll), RoundingMode.HALF_UP)
                                    .setScale(2, RoundingMode.HALF_UP)).multiply(new BigDecimal(100));
                            node.setDegreesFluctuate(degreesFluctuate.toString());
                        } else {
                            node.setDegreesFluctuate("0");
                        }
                    }
                }
            });

        }

        for (DetailsDTO detailsDTO : detailsDTOS) {
            for (DetailsDTO dto : mores) {
                if(detailsDTO.getPcid().equals(dto.getPcid()) &&
                        detailsDTO.getAmmeterid().equals(dto.getAmmeterid())){
                    BeanUtils.copyProperties(dto,detailsDTO,getNullPropertyNames(dto));
                }
            }
        }
        return detailsDTOS;
    }

    private String get5grDayAvg(DetailsDTO detailsDTO,List<AccountQua> accountQuaList){
     // 根据集团算出来的总用电量
        final BigDecimal[] totalQuaPower = {new BigDecimal(0)};

        String startdate = detailsDTO.getStartTime();
        String enddate = detailsDTO.getStopTime();

        String beginYearStr = startdate.substring(0, 4);
        String beginMonthStr = startdate.substring(4, 6);
        String beginDayStr = startdate.substring(6, 8);
        // 拿到年月
        String beginYearMonthStr = beginYearStr+"-" + beginMonthStr;
        DateTime beginMonthDate = DateUtil.parse(beginYearMonthStr, "yyyy-MM");

        String endYearStr = enddate.substring(0, 4);
        String endMonthStr = enddate.substring(4, 6);
        String endDayStr = enddate.substring(6, 8);
        String endYearMonthStr = endYearStr+"-" + endMonthStr;
        DateTime endMonthDate = DateUtil.parse(endYearMonthStr, "yyyy-MM");


            Integer beginYearMonthInt = Integer.valueOf(startdate.substring(0, 6));
            Integer endYearMonthInt = Integer.valueOf(enddate.substring(0, 6));

            // 若是不同的年月份
            if(beginYearMonthInt.compareTo(endYearMonthInt) != 0) {
                // 不同月份，需要拿到每月的天数*集团对应月份的电量平均值
                // 开始和结束设置 开始至月底的天数
                int beginDays =(DateUtil.dayOfMonth(DateUtil.endOfMonth(beginMonthDate))- Integer.parseInt(beginDayStr))+1;
                mutiBetweenPower(beginDays,beginYearMonthStr,accountQuaList,totalQuaPower);

                // 结束开始至当天的天数
                int endDays= Integer.parseInt(endDayStr);
                mutiBetweenPower(endDays,endYearMonthStr,accountQuaList,totalQuaPower);

                // 计算从开始年月到结束年月之间的年月
                List<String> monthsBetween = getMonthsBetween(beginYearMonthStr, endYearMonthStr);
                for (String yearMonth : monthsBetween) {
                    // 拿到年份，若是2024年以前，则使用202401的
                    int daysOfMonth = DateUtil.dayOfMonth(DateUtil.endOfMonth(DateUtil.parse(yearMonth,"yyyy-MM")));
                    mutiBetweenPower(daysOfMonth,yearMonth,accountQuaList,totalQuaPower);
                }
            }else {
                int daysOfMonth = DateUtil.dayOfMonth(DateUtil.endOfMonth(DateUtil.parse(beginYearMonthStr,"yyyy-MM")));
                // 同一月份直接计算
                mutiBetweenPower(daysOfMonth,beginYearMonthStr,accountQuaList,totalQuaPower);
            }

         return  totalQuaPower[0].setScale(2, RoundingMode.HALF_UP).toString();
    }

    public static List<String> getMonthsBetween(String startMonth, String endMonth) {
        DateTime begin = DateUtil.parse(startMonth, "yyyy-MM");
        DateTime end = DateUtil.parse(endMonth, "yyyy-MM");

        List<String> months = new ArrayList<>();

        while (!begin.isAfter(end)) {
            months.add(DateUtil.format(begin,"yyyy-MM"));
            begin = begin.offset(DateField.MONTH,1);
        }
        // 移除开始月份
        months.remove(startMonth);
        // 移除结束月份
        months.remove(endMonth);
        return months;
    }

    private static void mutiBetweenPower(
            int daysOfMonth,
            String beginYearMonthStr,
            List<AccountQua> accountQuaList,
            BigDecimal[] totalQuaPower){
        String year = beginYearMonthStr.split("-")[0];

        if("2024".compareTo(year) > 0){
            // 判断是否小于2024年 小于24年就拿最小的
            AccountQua accountQua = accountQuaList.get(accountQuaList.size() -1);
            totalQuaPower[0] = totalQuaPower[0].add(new BigDecimal(accountQua.getAve()).multiply(new BigDecimal(daysOfMonth)));
        } else {
            beginYearMonthStr = beginYearMonthStr.replace("-","");
            String finalBeginYearMonthStr = beginYearMonthStr;
            List<AccountQua> collect = accountQuaList.stream().filter(q -> finalBeginYearMonthStr.equals(q.getFinanperiod())).collect(Collectors.toList());
            // 若是不为空，则直接取当期的电量
            if (!collect.isEmpty()) {
                totalQuaPower[0] = totalQuaPower[0].add(new BigDecimal(collect.get(0).getAve()).multiply(new BigDecimal(daysOfMonth)));
            } else {
                // 直接取最新一期的，因为查询是倒叙的，直接取第一个就是最新的
                AccountQua accountQua = accountQuaList.get(0);
                totalQuaPower[0] = totalQuaPower[0].add(new BigDecimal(accountQua.getAve()).multiply(new BigDecimal(daysOfMonth)));
            }
        }
    }

    /**
     * @description: 赋值属性时候，忽略 null
     * @author: 芮永恒
     **/
    private static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<>();
        for (java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) emptyNames.add(pd.getName());
        }

        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

    @Override
    public void exportAuditDetails(HttpServletResponse response,PowerAuditVO powerAuditVO) {
        if(StringUtils.isNotEmpty(powerAuditVO.getFileName())) powerAuditVO.setFileName(powerAuditVO.getFileName().replace("/",""));
        powerAuditVO = setPowerAuditVOExceptionType(powerAuditVO);
        List<DetailsDTO> detailsDTOS = getDetailsDTOS(powerAuditVO);
        switch (powerAuditVO.getExportName()) {
            case "一站多表/一表多站":
            case "一站多表/多站多表":
                detailsDTOS = detailsDTOS.stream().filter(detailsDTO -> detailsDTO.getMutiJtlteCodes() != null &&  detailsDTO.getMutiJtlteCodes().equals("是")).collect(Collectors.toList());
                convertList(response,detailsDTOS,MutiJtlteDTO.class,powerAuditVO.getFileName());
                break;
            case "电表站址一致性":
                detailsDTOS = detailsDTOS.stream().filter(detailsDTO -> detailsDTO.getAddressConsistence() != null &&  detailsDTO.getAddressConsistence().equals("否")).collect(Collectors.toList());
                convertList(response,detailsDTOS, AddressDTO.class,powerAuditVO.getFileName());
                break;
            case "台账周期连续性":
                detailsDTOS = detailsDTOS.stream().filter(detailsDTO -> detailsDTO.getPeriodicAnomaly() != null &&  detailsDTO.getPeriodicAnomaly().equals("否")).collect(Collectors.toList());
                convertList(response,detailsDTOS, PeriodicDTO.class,powerAuditVO.getFileName());
                break;
            case "电表度数连续性":
                detailsDTOS = detailsDTOS.stream().filter(detailsDTO -> detailsDTO.getElectricityContinuity() != null &&  detailsDTO.getElectricityContinuity().equals("否")).collect(Collectors.toList());
                convertList(response,detailsDTOS, ContinuityDTO.class,powerAuditVO.getFileName());
                break;
            case "电价合理性":
                detailsDTOS = detailsDTOS.stream().filter(detailsDTO -> detailsDTO.getElectricityPrices().equals("否")).collect(Collectors.toList());
                convertList(response,detailsDTOS, PriceAuditDTO.class,powerAuditVO.getFileName());
                break;
            case "日均电量的波动合理性(集团5gr)":
                detailsDTOS = detailsDTOS.stream().filter(detailsDTO -> detailsDTO.getFluctuateContinuity() != null &&  detailsDTO.getFluctuateContinuity().equals("否")).collect(Collectors.toList());
                convertList(response,detailsDTOS, FluctuateDTO.class,powerAuditVO.getFileName());
                break;
            case "共享站分摊比例准确性":
            case "分摊比例准确性":
                detailsDTOS = detailsDTOS.stream().filter(detailsDTO ->  detailsDTO.getShareAccuracy() != null && detailsDTO.getShareAccuracy().equals("否")).collect(Collectors.toList());
                convertList(response,detailsDTOS, ShareAccuracyDTO.class,powerAuditVO.getFileName());
                break;
            case "台账周期合理性":
                detailsDTOS = detailsDTOS.stream().filter(detailsDTO -> detailsDTO.getReimbursementCycle() != null &&  detailsDTO.getReimbursementCycle().equals("否")).collect(Collectors.toList());
                convertList(response,detailsDTOS, AnomalyDTO.class,powerAuditVO.getFileName());
                break;
            case "分摊比例一致性":
                detailsDTOS = detailsDTOS.stream().filter(detailsDTO ->  detailsDTO.getShareAccuracy() != null && detailsDTO.getShareAccuracy().equals("否")).collect(Collectors.toList());
                convertList(response,detailsDTOS, ShareAccuracyDTO.class,powerAuditVO.getFileName());
                break;
            case "地市和运营分局":
                convertList(response,detailsDTOS, TotalDTO.class,powerAuditVO.getFileName());
                break;
        }
//        // 电价合理性，日均耗电量合理性
//        if(powerAuditVO.getExportName().equals("一站多表/多站多表")) {
//
//        }
//        // 导出 电表站址一致性
//        if(powerAuditVO.getExportName().equals("电表站址一致性")) {
//
//        }
//        // 导出 台账周期连续性 periodicAnomaly
//        if(powerAuditVO.getExportName().equals("台账周期连续性")) {
//
//        }
//        // 导出 电表度数连续性 electricityContinuity
//        if(powerAuditVO.getExportName().equals("电表度数连续性")) {
//
//        }
//        // 电价合理性， electricityPrices
//        if(powerAuditVO.getExportName().equals("电价合理性")) {
//
//        }
//        // 导出 台账电量合理性 electricityRationality
//        if(powerAuditVO.getExportName().equals("电量合理性")) {
//
//        }
//        // 导出 台账日均电量波动 fluctuateContinuity
//        if(powerAuditVO.getExportName().equals("日均电量的波动合理性")) {
//
//        }
//        // 页面无 导出 台账日均耗电量 consumeContinuity ）
//        // 导出 共享站/独享分摊比例  shareAccuracy  exclusiveAccuracy
//        if(powerAuditVO.getExportName().equals("共享站分摊比例准确性")) {
//
//        }
//        if(powerAuditVO.getExportName().equals("独享站分摊比例准确性")) {
//
//        }
//        // 导出 报账周期合理性  台账周期   getReimbursementCycle
//        if(powerAuditVO.getExportName().equals("报账周期合理性")) {
//
//        }
//
//        // 导出 台账周期 periodicAnomaly
//        if(powerAuditVO.getExportName().equals("地市和运营分局")) {
//
//        }
    }

    @Override
    public List<AuditResults> getAuditResultList(String type, List<String> keyList) {
        //删除list中重复元素
        removeDuplicates(keyList);
        List<PowerAuditEntity> powerAuditEntityList = powerAuditMapper.getPowerAudityByPcid(type, keyList);
        List<AuditResults> auditResultsList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(powerAuditEntityList)) {
            int totalSize = powerAuditEntityList.size();
            powerAuditEntityList.forEach(powerAuditEntity -> {
                AuditResults auditResults = new AuditResults();
                auditResults.setTotalSize(totalSize);
                if (powerAuditEntity.getIfQkSuccess().intValue() == 1) {
                    auditResults.setSuccessCount(1);
                    auditResults.setStaute("成功");
                    auditResults.setMsg(powerAuditEntity.getAmmeterid() + "电表、期号" + powerAuditEntity.getLedgerPeriod() + "已稽核、通过");
                } else {
                    auditResults.setFailCount(1);
                    auditResults.setStaute("失败");
                    auditResults.setMsg(powerAuditEntity.getAmmeterid() + "电表、期号" + powerAuditEntity.getLedgerPeriod() + "已稽核、未通过");
                }
                auditResults.setPowerAuditEntity(powerAuditEntity);
                auditResultsList.add(auditResults);
            });
        }
        if (keyList.size() > auditResultsList.size()) {
            //稽核结果不够
            for (AuditResults auditResults : auditResultsList) {
                keyList.remove(auditResults.getPowerAuditEntity().getPcid());
            }
            if (keyList.size() > 0) {
                //重新稽核列表
                List<Long> redoList = keyList.stream().map(Long::parseLong).collect(Collectors.toList());
                //重新稽核Account
                auditAccount(redoList);
                //重新稽核AccountEs
                auditAccountEs(redoList);
                //Account加入结果表
                List<AccountBaseResult> accountBaseResultList = accountMapper.selectByIds(redoList);
                if (CollectionUtil.isNotEmpty(accountBaseResultList)) {
                    for (AccountBaseResult accountBaseResult : accountBaseResultList) {
                        AuditResults auditResults = new AuditResults();
                        PowerAuditEntity powerAuditEntity = new PowerAuditEntity();
                        powerAuditEntity.setAmmeterid(accountBaseResult.getAmmetercode());
                        powerAuditEntity.setPcid(accountBaseResult.getPcid().toString());
                        powerAuditEntity.setLedgerPeriod(accountBaseResult.getAccountno());
                        powerAuditEntity.setMonth(accountBaseResult.getAccountno());
                        auditResults.setMsg(powerAuditEntity.getAmmeterid() + "电表、期号" + powerAuditEntity.getLedgerPeriod() + "未稽核");
                        auditResults.setFailCount(1);
                        auditResults.setStaute("未稽核");
                        auditResults.setPowerAuditEntity(powerAuditEntity);
                        auditResultsList.add(0, auditResults);
                    }
                }
                //AccountEs加入结果表
                List<AccountEsResult> accountEsResultList = powerAccountEsMapper.selectByIds(redoList);
                if (CollectionUtil.isNotEmpty(accountEsResultList)) {
                    for (AccountEsResult accountEsResult : accountEsResultList) {
                        AuditResults auditResults = new AuditResults();
                        PowerAuditEntity powerAuditEntity = new PowerAuditEntity();
                        powerAuditEntity.setAmmeterid(accountEsResult.getAmmetercode());
                        powerAuditEntity.setPcid(accountEsResult.getPcid().toString());
                        powerAuditEntity.setLedgerPeriod(accountEsResult.getAccountno());
                        powerAuditEntity.setMonth(accountEsResult.getAccountno());
                        auditResults.setMsg(powerAuditEntity.getAmmeterid() + "电表、期号" + powerAuditEntity.getLedgerPeriod() + "未稽核");
                        auditResults.setFailCount(1);
                        auditResults.setStaute("未稽核");
                        auditResults.setPowerAuditEntity(powerAuditEntity);
                        auditResultsList.add(0, auditResults);
                    }
                }
            }
        }
        return auditResultsList;
    }

    /****
     *
     * 删除list中重复元素
     */
    private void removeDuplicates(List<String> list) {
        HashSet<String> set = new HashSet<String>(list);
        list.clear();
        list.addAll(set);
    }

    @Override
    public int auditAccount(List<Long> pcidList) {
        if (CollectionUtil.isNotEmpty(pcidList)) {
            StationAuditUtil.doAuditAccountThreadByPcids(pcidList);
        }
        return pcidList.size();
    }

    @Override
    public int auditAccountEs(List<Long> pcidList) {
        if (CollectionUtil.isNotEmpty(pcidList)) {
            StationAuditUtil.doAuditAccountEsThreadByPcids(pcidList);
        }
        return pcidList.size();
    }

    @Override
    public int auditAllAccount() {
        int auditCount = 0;
        List<Long> accountIdList = accountMapper.getAllAccountIds();
        if (CollectionUtil.isNotEmpty(accountIdList)) {
            StationAuditUtil.doAuditAccountThreadByPcids(accountIdList);
            auditCount = accountIdList.size();
        }
        List<Long> accountEsIdList = powerAccountEsMapper.getAllAccountEsIds();
        if (CollectionUtil.isNotEmpty(accountEsIdList)) {
            StationAuditUtil.doAuditAccountEsThreadByPcids(accountEsIdList);
            auditCount += accountEsIdList.size();
        }
        return auditCount;
    }

    private <T> void convertList(HttpServletResponse response,List<DetailsDTO> list, Class<T> aClass,String fileName) {
        List<T> objects = new ArrayList<>();
        for (DetailsDTO powers : list) {
            T object = null;
            try {
                object = (T) aClass.newInstance();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            BeanUtils.copyProperties(powers,object);
            objects.add(object);
        }
        ExcelUtil<T> excelUtil = new ExcelUtil<>(aClass);
        excelUtil.exportExcelToBrowser(response,objects,fileName);
    }

    private <T> List<T> convertListObject(List<DetailsDTO> list, Class<?> aClass) {
        List<T> objects = new ArrayList<>();
        for (DetailsDTO powers : list) {
            T object = null;
            try {
                object = (T) aClass.newInstance();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            BeanUtils.copyProperties(powers,object);

            objects.add(object);
        }
        return objects;
    }

}
