package com.sccl.modules.business.stationreportwhitelist.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/4/22 16:32
 * @describe 供电标志
 */
@Getter
@AllArgsConstructor
public enum Directsupplyflag {

    // 1.直供电 2.转供电
    DIRECT_SUPPLY_FLAG_1(1, "直供电"),
    DIRECT_SUPPLY_FLAG_2(2, "转供电");

    /**
     * 状态码
     */
    private final Integer code;

     /**
     * 状态名称
     */
    private final String name;

    /**
     * 根据code获取name
     */
    public static String getNameByCode(Integer code) {
        for (Directsupplyflag billStatus : Directsupplyflag.values()) {
            if (billStatus.getCode().equals(code)) {
                return billStatus.getName();
            }
        }
        return null;
    }
}
