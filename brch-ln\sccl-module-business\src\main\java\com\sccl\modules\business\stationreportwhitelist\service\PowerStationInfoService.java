package com.sccl.modules.business.stationreportwhitelist.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sccl.modules.business.stationreportwhitelist.domain.PowerStationInfo;
import com.sccl.modules.business.stationreportwhitelist.dto.PowerStationInfoQuery;
import com.sccl.modules.business.stationreportwhitelist.vo.PowerStationInfoVO;

import java.util.List;

public interface PowerStationInfoService extends IService<PowerStationInfo> {
    /**
     * 一站多表 站址列表
     */
    Page<PowerStationInfoVO> oneStopIsMoreThanOneWatch(Page<PowerStationInfoVO> page, PowerStationInfoQuery query);

    /**
     * 一表多站 局站列表
     */
    List<PowerStationInfoVO> oneTableMultiStationList(Page<PowerStationInfoVO> page, PowerStationInfoQuery query);
}
