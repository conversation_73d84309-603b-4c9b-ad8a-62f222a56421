package com.sccl.modules.business.oilcard.service;

import com.sccl.common.support.Convert;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.business.energyaccount.mapper.EnergyAccountMapper;
import com.sccl.modules.business.oilcard.domain.OilCardVo;
import com.sccl.modules.business.oilcard.mapper.OilCardMapper;
import com.sccl.modules.business.oilcardaccount.mapper.OilCardAccountMapper;
import com.sccl.modules.business.oilexpense.domain.OilExpense;
import org.aspectj.weaver.loadtime.Aj;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sccl.modules.business.oilcard.domain.OilCard;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * 油卡 服务层实现
 *
 * <AUTHOR>
 * @date 2021-12-16
 */
@Service
@Transactional
public class OilCardServiceImpl extends BaseServiceImpl<OilCard> implements IOilCardService {

    @Autowired
    private OilCardMapper oilCardMapper;
    @Autowired
    private OilCardAccountMapper oilCardAccountMapper;
    @Autowired
    private EnergyAccountMapper energyAccountMapper;

    /**
     * 删除油卡
     * 1.查询当前油卡是否被使用
     * 2.删除油卡
     * <AUTHOR>
     * @date 2022/3/17
     */
    @Override
    public AjaxResult removeOilCard(String ids) {
        String[] split = ids.split(",");
        for (String id : split) {
        /** 查询购油台账是否使用该油卡 */
            int i = oilCardAccountMapper.selectUsedbyCardid(id);
            if (i != 0) {
                return AjaxResult.error("当前油卡已被使用,无法删除");
            }
            int j = energyAccountMapper.selectAccountByCardid(id);
            if (j != 0) {
                return AjaxResult.error("当前油卡已被应急中心使用,无法删除");
            }
        }
        //删除油卡
        int i = oilCardMapper.deleteByIds(Convert.toStrArray(ids));
        if (i != 0) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.error("删除失败");
    }

    @Override
    public List<OilCard> selectListViewCard(OilCard oilCard) {
        return oilCardMapper.selectListViewCard(oilCard);
    }

    @Override
    public List<OilCardVo> selectListAll(OilCard oilCard) {
         return oilCardMapper.selectListAll(oilCard);
    }
}
