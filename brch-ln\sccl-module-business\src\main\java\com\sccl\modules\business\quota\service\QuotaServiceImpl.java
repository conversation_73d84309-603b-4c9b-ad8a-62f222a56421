package com.sccl.modules.business.quota.service;

import com.sccl.common.support.Convert;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.modules.business.account.mapper.AccountMapper;
import com.sccl.modules.business.ammeterorprotocol.service.IAmmeterorprotocolService;
import com.sccl.modules.business.quota.domain.Quota;
import com.sccl.modules.business.quota.domain.QuotaBaseResult;
import com.sccl.modules.business.quota.domain.QuotaCondition;
import com.sccl.modules.business.quota.domain.QuotaRecord;
import com.sccl.modules.business.quota.mapper.QuotaMapper;
import com.sccl.modules.business.quota.mapper.QuotaRecordMapper;
import com.sccl.modules.system.user.domain.User;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * 定额 服务层实现
 * 
 * <AUTHOR>
 * @date 2019-05-13
 */
@Service
public class QuotaServiceImpl extends BaseServiceImpl<Quota> implements IQuotaService
{
    @Autowired
    public QuotaMapper quotaMapper;
    @Autowired
    public QuotaRecordMapper quotaRecordMapper;
    /**
     * 定额列表
     * 根据条件查询数据
     * @param quotaCondition
     * @return
     */
    @Override
    public List<QuotaBaseResult> selectListBySearch(QuotaCondition quotaCondition){
        User user = ShiroUtils.getUser();
        if(null != user){
            quotaCondition.setUserId(user.getId());
        }
        List<QuotaBaseResult> lists = quotaMapper.selectListBySearch(quotaCondition);
        for (QuotaBaseResult b : lists) {
            if (null != b.getBillStatus() && (b.getBillStatus() == 1 ||b.getBillStatus() == 4)) {//流程中
                b.set_disabled(true);
            } else {
                b.set_disabled(false);
            }
        }
        return lists;
    }
    @Override
    public int deleteQuota(String ids) {
        quotaRecordMapper.deleteByQuotaIdsDB(Convert.toStrArray(ids));//删除定额历史记录
        int result = this.deleteByIdsDB(Convert.toStrArray(ids));//删除电表协议
        return result;
    }
    /**
     * 定额编辑
     * 根据id查询数据
     * @param id 查询条件
     * @return
     */
    @Override
    public QuotaBaseResult getById(Long id){
        return quotaMapper.getById(id);
    }

    @Override
    public int addQuota(Quota quota){
        if (quota.getBillStatus() == null ){//草稿
            quota.setBillStatus(0);
        }
        User user = ShiroUtils.getUser();
        if(null != user){
            quota.setCreatorId(user.getId());
        }
        int result = this.insert(quota);
        saveQuotaRecord(quota);//保存定额记录
        return result;
    }

    @Override
    public int updateQuota(Quota quota){
        Long id = quota.getId();
        if (null == quota.getBillStatus() || quota.getBillStatus() == 2){//修改中
            quota.setBillStatus(3);
        }
        int result = saveQuotaRecord(quota);//保存定额记录
        if(quota.getBillStatus() == 0){//判断是未提交流程前进行编辑
            this.update(quota);
        }else{//修改状态为修改中
            quota = new Quota();
            quota.setId(id);
            quota.setBillStatus(3);
            User user = ShiroUtils.getUser();
            if(null != user){
                quota.setCreatorId(user.getId());
            }
            this.updateForModel(quota);
        }
        return result;

    }

    private int saveQuotaRecord(Quota quota){
        //保存定额记录
        QuotaRecord quotaRecord = new QuotaRecord();
        BeanUtils.copyProperties(quota, quotaRecord);
        quotaRecord.setQuotaId(quota.getId());
        quotaRecord.setId(null);
        User user = ShiroUtils.getUser();
        if(null != user){
            quotaRecord.setCreatorId(user.getId());
        }
        return quotaRecordMapper.insert(quotaRecord);
    }

    @Override
    public List<QuotaBaseResult> checkAmmProByQuota(Quota quota){
        return quotaMapper.checkAmmProByQuota(quota);
    }
}
