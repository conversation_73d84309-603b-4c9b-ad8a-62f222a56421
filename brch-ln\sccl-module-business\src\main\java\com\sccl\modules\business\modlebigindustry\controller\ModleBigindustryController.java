package com.sccl.modules.business.modlebigindustry.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.business.modlebigindustry.domain.ModleBigindustry;
import com.sccl.modules.business.modlebigindustry.service.IModleBigindustryService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 单价大工业 信息操作处理
 * 
 * <AUTHOR>
 * @date 2023-03-03
 */
@RestController
@RequestMapping("/business/modleBigindustry")
public class ModleBigindustryController extends BaseController
{
    private String prefix = "business/modleBigindustry";
	
	@Autowired
	private IModleBigindustryService modleBigindustryService;
	
	@RequiresPermissions("business:modleBigindustry:view")
	@GetMapping()
	public String modleBigindustry()
	{
	    return prefix + "/modleBigindustry";
	}
	
	/**
	 * 查询单价大工业列表
	 */
	@RequiresPermissions("business:modleBigindustry:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(ModleBigindustry modleBigindustry)
	{
		startPage();
        List<ModleBigindustry> list = modleBigindustryService.selectList(modleBigindustry);
		return getDataTable(list);
	}
	
	/**
	 * 新增单价大工业
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存单价大工业
	 */
	@RequiresPermissions("business:modleBigindustry:add")
	@Log(title = "单价大工业", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody ModleBigindustry modleBigindustry)
	{		
		return toAjax(modleBigindustryService.insert(modleBigindustry));
	}

	/**
	 * 修改单价大工业
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		ModleBigindustry modleBigindustry = modleBigindustryService.get(id);

		Object object = JSONObject.toJSON(modleBigindustry);

        return this.success(object);
	}
	
	/**
	 * 修改保存单价大工业
	 */
	@RequiresPermissions("business:modleBigindustry:edit")
	@Log(title = "单价大工业", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody ModleBigindustry modleBigindustry)
	{		
		return toAjax(modleBigindustryService.update(modleBigindustry));
	}
	
	/**
	 * 删除单价大工业
	 */
	@RequiresPermissions("business:modleBigindustry:remove")
	@Log(title = "单价大工业", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(modleBigindustryService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看单价大工业
     */
    @RequiresPermissions("business:modleBigindustry:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		ModleBigindustry modleBigindustry = modleBigindustryService.get(id);

        Object object = JSONObject.toJSON(modleBigindustry);

        return this.success(object);
    }

}
