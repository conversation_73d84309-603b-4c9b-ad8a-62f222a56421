package com.sccl.modules.rental.rentalcarmain.service;

import java.util.List;

import com.sccl.framework.service.IBaseService;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.rental.rentalcarmain.domain.Rentalcarmain;
import com.sccl.modules.uniflow.common.WFModel;

/**
 * 车辆 所属 租赁项目 服务层
 * 
 * <AUTHOR>
 * @date 2019-08-23
 */
public interface IRentalcarmainService extends IBaseService<Rentalcarmain>
{


    List<Rentalcarmain> selectListByIds(String[] toStrArray);

    AjaxResult saveRentalcarmain(Rentalcarmain rentalcarmain);

    int deleteAndItemByIds(String[] toStrArray);

    /**
     * 流程引擎回调（通过MQ）
     *
     * @param wfModel 流程消息对象
     */
    public void uniflowCallBack(WFModel wfModel);
}
