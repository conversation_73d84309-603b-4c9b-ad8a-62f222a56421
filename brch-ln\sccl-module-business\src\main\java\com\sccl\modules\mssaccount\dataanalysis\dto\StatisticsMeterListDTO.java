package com.sccl.modules.mssaccount.dataanalysis.dto;

import com.sccl.framework.web.domain.BasePage;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 局站统计列表
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StatisticsMeterListDTO extends BasePage {
    /**
     * 机构/部门ID
     */
    private Long company;

    /**
     * 结算类型
     * 字典：directSupplyFlag
     * 直供电	1
     * 转供电	2
     */
    private String directSupplyFlag;

    /**
     * 状态
     * 字典：status
     * 在用	1
     * 停用	0
     */
    private String status;

    /**
     * 是否实体电表
     * 是	1
     * 否	0
     */
    private String isentityammeter;

    /**
     * 局站类型
     */
    private Integer stationtype;

    /**
     * 站址产权归属
     * 自有 1
     * 铁塔 2
     */
    private String property;

    /**
     * 电表编码
     */
    private String meterCode;

    /**
     * 局站名称
     */
    private String stationname;

    /**
     * 局站编码
     */
    private String stationcode;

    /**
     * 项目名称
     */
    private String projectname;
}
