package com.sccl.modules.business.stationreportwhitelist.enums;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sccl.modules.business.stationreportwhitelist.domain.PowerCategoryType;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Component
public class MyDict {

    // 需要初始化的字典类型,对应表[power_category_type]字段[type_category]
    public enum TYPE {
        /**站点属性*/
        property,
        /**局站类型(集团电费)*/
        BUR_STAND_TYPE,
        /**对外结算类型*/
        directSupplyFlag,
        /**产权*/
        propertyRight,
        /** 状态 */
        status,
        /** 付费方式 */
        payType,
        /** 电价性质 */
        electrovalenceNature,
        /** 电表类型 */
        ammeterType,
        /** 流程状态 */
        basicBillStatus,

    }

    private static final Map<String, Map<String, String>> DICT_MAP = new HashMap<>(2);

    public void initDict() {
        DICT_MAP.clear();
        // DictType 转list
        List<String> dictTypeList = Arrays.stream(TYPE.values()).map(String::valueOf).collect(Collectors.toList());
        // 查询字典表所有字典
        List<PowerCategoryType> dictList = new PowerCategoryType().selectList(Wrappers.<PowerCategoryType>lambdaQuery()
                .in(PowerCategoryType::getTypeCategory, dictTypeList)
        );
        // 根据typeCategory分组，分别存入到DICT_MAP中
        List<String> categoryList = dictList.stream().map(PowerCategoryType::getTypeCategory).collect(Collectors.toList());
        // 字典组
        categoryList.forEach(category -> {
            Map<String, String> dictMap = new HashMap<>(2);
            dictList.stream().filter(dict -> dict.getTypeCategory().equals(category)).forEach(dict -> {
                dictMap.put(dict.getTypeCode(), dict.getTypeName());
            });
            DICT_MAP.put(category, dictMap);
        });
        System.out.println("初始化字典完成");
//        DICT_MAP.forEach((k, v) -> System.out.println(k + ":" + v));
    }



    /**
     * @param dictCode
     * @param dictValue
     * @return
     */
    public static String findDictLabel(TYPE dictCode, String dictValue) {
        final List<String> moreValue = StrUtil.split(dictValue, StrUtil.COMMA);
        String result = null;
        if (moreValue.size() > 1) {
            result = moreValue.stream().map(v -> DICT_MAP.get(dictCode.name()).get(v)).collect(Collectors.joining("、"));
        } else {
            result = DICT_MAP.get(dictCode.name()).get(dictValue);
        }
        return result;
    }
}
