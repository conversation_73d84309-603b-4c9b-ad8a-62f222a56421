package com.sccl.modules.business.cost.service;

import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.utils.enumClass.CommonConstants;
import com.sccl.modules.business.cost.domain.PowerConsistencyAnomaly;
import com.sccl.modules.business.cost.mapper.ConsistencyDoMapper;
import com.sccl.modules.business.cost.mapper.PowerConsistencyAnomalyMapper;
import com.sccl.modules.business.cost.vo.*;
import com.sccl.modules.monitor.operlog.domain.OperLog;
import com.sccl.modules.monitor.operlog.mapper.OperLogMapper;
import com.sccl.modules.uniflow.common.WFModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 一致率异常处理上报
 *
 * <AUTHOR>
 * @date 2024-10-28
 */
@Service
@Slf4j
public class ConsistencyDoServiceImpl implements IConsistencyDoService {

    @Autowired
    private ConsistencyDoMapper consistencyDoMapper;
    @Autowired
    private PowerConsistencyAnomalyMapper powerConsistencyAnomalyMapper;
    @Autowired
    private OperLogMapper operLogMapper;

    /**
     * 一致率异常处理审批 回调
     * @param wfModel
     * @throws Exception
     */
    public void uniflowCallBack(WFModel wfModel) throws Exception {
        {
            System.out.println("一致率异常处理审批流程信息:" + wfModel.toString());
            if ("PROCESS_STARTED".equals(wfModel.getCallbackType())) {
                this.doStartFlow(wfModel.getBusiId(), wfModel.getProcInstId(),
                        CommonConstants.YCPD_AUDIT_TYPE_CLZ);//修改 审批状态 处理中
            } else if ("PROCESS_COMPLETED".equals(wfModel.getCallbackType())) {
                try {
                    //修改单据状态为已通过并更新数据
                    int state = CommonConstants.YCPD_AUDIT_TYPE_YWC;//已完成
                    this.updateStatus(wfModel.getBusiId(), state);//修改 审批状态 已通过
                } catch (Exception e) {
                    e.printStackTrace();
                    OperLog model = new OperLog();
                    model.setOperName("ammeter");
                    model.setTitle("一致率异常处理审批流程回调 修改审批状态为已通过， 但更新数据异常");
                    model.setMethod("updateStatus");
                    model.setErrorMsg("审批id:" + wfModel.getBusiId() + " 流程：" + wfModel.getBusiAlias() + " 用户：" + wfModel.getApplyUserId());
                    model.setOperTime(new Date());
                    operLogMapper.insert(model);
                    throw e;
                }
            } else if ("PROCESS_CANCELLED".equals(wfModel.getCallbackType()) || "TURNBACK_TO_START".equals(wfModel.getCallbackType())) {
                //修改审批状态为 已驳回
                this.updateStatus(wfModel.getBusiId(), CommonConstants.YCPD_AUDIT_TYPE_YBH);
            }
        }
    }

    //流程启动时，更新单据状态、流程id
    private void doStartFlow(String busiId, Long processinstid, int billStatus) {
        Map<String, Object> parms = new HashMap<>();
        parms.put("id", busiId);
        PowerConsistencyAnomaly consistencyAnomaly = powerConsistencyAnomalyMapper.selectByPrimaryKey(parms);
        if (null != consistencyAnomaly) {
            consistencyAnomaly.setProCessId(processinstid);//流程id
            consistencyAnomaly.setStatus(billStatus);//审核结果
            String userName = ShiroUtils.getUserName();
            consistencyAnomaly.setDoTime(LocalDateTime.now());//上报时间
            consistencyAnomaly.setDoBy(userName);
            consistencyAnomaly.initUpdate();
            powerConsistencyAnomalyMapper.updateForModel(consistencyAnomaly);
        } else {
            log.error("没有找到对应的基础数据，或已被删除");
        }
    }

    //流程结束时，更新单据状态
    private void updateStatus(String busiId, int billStatus) {
        Map<String, Object> parms = new HashMap<>();
        parms.put("id", busiId);
        PowerConsistencyAnomaly consistencyAnomaly = powerConsistencyAnomalyMapper.selectByPrimaryKey(parms);
        if (null != consistencyAnomaly) {
            consistencyAnomaly.setStatus(billStatus);//审核结果
            consistencyAnomaly.initUpdate();
            powerConsistencyAnomalyMapper.updateForModel(consistencyAnomaly);
        } else {
            log.error("没有找到对应的基础数据，或已被删除");
        }
    }

    /**
     * 一览
     * @param searchVo
     * @return
     */
    @Override
    public List<ConsistencyDoResultVo> list(ConsistencyDoSearchVo searchVo) {
        return consistencyDoMapper.list(searchVo);
    }

    /**
     * 一览导出
     * @param searchVo
     * @return
     */
    @Override
    public List<ConsistencyDoExpVo> exportList(ConsistencyDoSearchVo searchVo) {
        List<ConsistencyDoExpVo> list = consistencyDoMapper.exportList(searchVo);
        if (list != null && list.size() > 0) {
            int num = 1;
            for (ConsistencyDoExpVo expVo : list) {
                expVo.setNum(String.valueOf(num));
                num += 1;
            }
        }
        return list;
    }

    /**
     * 异常处理上报-提交
     * @param entity
     * @return
     */
    @Override
    public int report(ConsistencyDoSaveVo entity) {
        PowerConsistencyAnomaly powerConsistencyAnomaly = new PowerConsistencyAnomaly();
        BeanUtils.copyProperties(entity, powerConsistencyAnomaly);
        powerConsistencyAnomaly.initUpdate();
        powerConsistencyAnomalyMapper.updateForModel(powerConsistencyAnomaly);
        return 1;
    }

    /**
     * 待处理统计
     * @param searchVo
     * @return
     */
    @Override
    public ConsistencyToDoStatisticsVo statistics(ConsistencyDoSearchVo searchVo) {
        return consistencyDoMapper.statistics(searchVo);
    }
}
