package com.sccl.modules.oss.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.enrising.dcarbon.manage.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <p>
 * oss存储表
 * </p>
 *
 * <AUTHOR> <PERSON>
 * @since 2023-06-29
 */
@Getter
@Setter
@TableName("oss_msg")
public class OssMsgEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 存储桶名
     */
    @TableField("bucket_name")
    private String bucketName;

    /**
     * 对象存储ID
     */
    @TableField("oss_id")
    private String ossId;

    /**
     * OSS文件名
     */
    @TableField("oss_file_name")
    private String ossFileName;

    /**
     * oss文件路径
     */
    @TableField("oss_path")
    private String ossPath;

    /**
     * oss创建时间
     */
    @TableField("oss_create_time")
    private String ossCreateTime;

    /**
     * oss文件大小(kb)
     */
    @TableField("oss_size")
    private BigDecimal ossSize;
}
