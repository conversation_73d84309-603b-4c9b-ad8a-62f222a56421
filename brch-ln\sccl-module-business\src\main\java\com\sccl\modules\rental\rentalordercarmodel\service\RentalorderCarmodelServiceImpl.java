package com.sccl.modules.rental.rentalordercarmodel.service;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.rental.rentalordercarmodel.mapper.RentalorderCarmodelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sccl.modules.rental.rentalordercarmodel.domain.RentalorderCarmodel;

import java.util.List;
import java.util.Map;


/**
 * 车型数据 服务层实现
 * 
 * <AUTHOR>
 * @date 2019-08-23
 */
@Service
public class RentalorderCarmodelServiceImpl extends BaseServiceImpl<RentalorderCarmodel> implements IRentalorderCarmodelService
{

    @Autowired
    RentalorderCarmodelMapper rentalorderCarmodelMapper;

    @Override
    public List<RentalorderCarmodel> selectAndNameByRcoid(Long id) {
        return rentalorderCarmodelMapper.selectAndNameByRcoid(id);
    }
}
