package com.sccl.modules.business.powermodel.service.impl;

import com.sccl.modules.business.powermodel.entity.PowerModleBase;
import com.sccl.modules.business.powermodel.mapper.PowerModleBaseMapper;
import com.sccl.modules.business.powermodel.service.PowerModleBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import java.util.List;

/**
 * 电量模型基本表(PowerModleBase)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-10-19 17:44:41
 */
@Service
public class PowerModleBaseServiceImpl implements PowerModleBaseService {
    @Autowired(required = false)
    private PowerModleBaseMapper powerModleBaseMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public PowerModleBase queryById(Long id) {
        return this.powerModleBaseMapper.queryById(id);
    }

    /**
     * 分页查询
     *
     * @param powerModleBase 筛选条件
     * @param pageRequest    分页对象
     * @return 查询结果
     */
    @Override
    public Page<PowerModleBase> queryByPage(PowerModleBase powerModleBase, PageRequest pageRequest) {
        long total = this.powerModleBaseMapper.count(powerModleBase);
        return new PageImpl<>(this.powerModleBaseMapper.queryAllByLimit(powerModleBase, pageRequest), pageRequest, total);
    }

    /**
     * 新增数据
     *
     * @param powerModleBase 实例对象
     * @return 实例对象
     */
    @Override
    public PowerModleBase insert(PowerModleBase powerModleBase) {
        this.powerModleBaseMapper.insert(powerModleBase);
        return powerModleBase;
    }

    /**
     * 修改数据
     *
     * @param powerModleBase 实例对象
     * @return 实例对象
     */
    @Override
    public PowerModleBase update(PowerModleBase powerModleBase) {
        this.powerModleBaseMapper.update(powerModleBase);
        return this.queryById(powerModleBase.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(Long id) {
        return this.powerModleBaseMapper.deleteById(id) > 0;
    }

    @Override
    public List<PowerModleBase> checkImportExcel(List<PowerModleBase> content) {
        return null;
    }

    @Override
    public Integer batchAdd(List<PowerModleBase> content) {
        int i = powerModleBaseMapper.insertBatch(content);
        return  i;
    }
}
