package com.sccl.modules.business.lnidc.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.utils.ExcelUtil;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.autojob.util.convert.MessageMaster;
import com.sccl.modules.business.account.domain.Account;
import com.sccl.modules.business.account.domain.AccountBaseResult;
import com.sccl.modules.business.account.domain.AccountCondition;
import com.sccl.modules.business.cache.utils.RedisUtil;
import com.sccl.modules.business.lnidc.domain.IdcMonitorStatistics;
import com.sccl.modules.business.lnidc.domain.IdcMonitorStatisticsBo;
import com.sccl.modules.business.lnidc.domain.IdcMonitorStatisticsVo;
import com.sccl.modules.business.lnidc.service.IdcMontitorStatisticsService;
import com.sccl.modules.business.powermodel.entity.PowerModleInit;
import com.sccl.modules.business.powermodel.entity.PowerModleOrgCode;
import com.sccl.modules.business.statinAudit.aspct.StationAuditAnnotation;
import com.sccl.modules.system.role.domain.Role;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.service.UserServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

/**
 * __IDC服务器能耗数字化监控月度统计__<br/>
 * 2024/4/7
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/business/lnIdc")
@AllArgsConstructor
public class IdcMontitorStatisticsController extends BaseController {

    private final IdcMontitorStatisticsService idcMontitorStatisticsService;

    private final UserServiceImpl userService;

    private static final Map<String, String> IMPORT_COLUMN_MAP = new HashMap<>();

    static {
        IMPORT_COLUMN_MAP.put("地市", "companyName");
        IMPORT_COLUMN_MAP.put("本省IDC名称", "idcName");
        IMPORT_COLUMN_MAP.put("设备名称", "deviceName");
        IMPORT_COLUMN_MAP.put("设备IP地址", "ipAddr");
        IMPORT_COLUMN_MAP.put("设备厂家", "deviceFactory");
        IMPORT_COLUMN_MAP.put("设备型号", "deviceType");
        IMPORT_COLUMN_MAP.put("所属系统或平台", "ownPlatform");
        IMPORT_COLUMN_MAP.put("维护部门", "preserveDept");
        IMPORT_COLUMN_MAP.put("维护人", "preservePerson");
        IMPORT_COLUMN_MAP.put("设备入网时间", "deviceAccessTime");
        IMPORT_COLUMN_MAP.put("额定功率（W)", "ratedPower");
        IMPORT_COLUMN_MAP.put("查询实际功率", "realPower");
    }

    /**
     * 查询IDC服务器能耗数字化监控月度统计
     */
    @GetMapping("/selfIdcEnergyList")
    @ResponseBody
    public TableDataInfo selfIdcEnergyList(IdcMonitorStatisticsBo bo) {
        //填充部门权限，根据token自动填充
        setJurisdiction(bo);
        startPage();
        List<IdcMonitorStatisticsVo> list = idcMontitorStatisticsService.selfIdcEnergyList(bo);
        return getDataTable(list);
    }

    private void setJurisdiction(IdcMonitorStatisticsBo parm) {
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("PROVI_") || role.getCode().startsWith("admin") || role.getCode().equals("SYS_ADMIN")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
            if (role.getCode().startsWith("SUB_")) {//县能耗费管理员
                isSubAdmin = true;
            }
        }
        //  权限设置
        if (isProAdmin) {

        } else if (isCityAdmin || isSubAdmin) {
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0)
                parm.setCompany(Long.valueOf(companies.get(0).getId()));
        } else {
            throw new RuntimeException(
                    String.format("当前用户无查阅权限,可查阅权限角色为系统管理，省，市，县能耗管理")
            );
        }

    }

    /**
     * @Description: 保存 IDC服务器能耗数字化监控月度 录入数据
     * @author: dongk
     * @date: 2019/5/13
     * @param:
     * @return:
     */
    @PostMapping("/editIdcEnergy")
    @ResponseBody
    public AjaxResult editIdcEnergy(@RequestBody IdcMonitorStatisticsBo bo) {
        if(CollectionUtil.isEmpty(bo.getItems()) || StrUtil.isBlank(bo.getYear())){
            return AjaxResult.success();
        }
        idcMontitorStatisticsService.editIdcEnergy(bo);
        return AjaxResult.success();
    }

    @GetMapping("/exportIdcEnergy")
    @ResponseBody
    public void exportIdcEnergy(HttpServletResponse response,IdcMonitorStatisticsBo bo) {
        //填充部门权限，根据token自动填充
        setJurisdiction(bo);
        idcMontitorStatisticsService.exportIdcEnergy(response,bo);
    }

    /**
     * IDC服务器能耗数字化监控月度 基础数据导入
     *
     *  电价基础数据导入
     *
     * @param request
     * @return
     */
    @GetMapping(value = "/importIdcEnergy", produces = "application/json;charset=UTF-8")
    public String importExcel(HttpServletRequest request,@RequestParam(name = "file") MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return MessageMaster.DefaultMessage.EMPTY_PARAMS.toString();
        }
        ExcelUtil<IdcMonitorStatistics> excelUtil = new ExcelUtil<>(IdcMonitorStatistics.class);
        try {
            List<IdcMonitorStatistics> content = excelUtil.importExcel("核定上报表", file, IMPORT_COLUMN_MAP);
            if(CollectionUtil.isNotEmpty(content)){
                idcMontitorStatisticsService.insertList(content);
            }

            return MessageMaster.getMessage(MessageMaster.Code.OK, "导入成功");
        } catch (Exception e) {
            e.printStackTrace();
            return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "导入错误：" + e.getMessage());
        }
    }
}
