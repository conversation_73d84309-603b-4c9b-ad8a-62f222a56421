package com.sccl.modules.mssaccount.mssaccountclearitem.service;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.mssaccount.mssaccountclearitem.mapper.MssAccountclearitemMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sccl.modules.mssaccount.mssaccountclearitem.domain.MssAccountclearitem;

import java.util.List;


/**
 * 挑对报账单 服务层实现
 * 
 * <AUTHOR>
 * @date 2019-05-01
 */
@Service
public class MssAccountclearitemServiceImpl extends BaseServiceImpl<MssAccountclearitem> implements IMssAccountclearitemService
{
    @Autowired
    MssAccountclearitemMapper mssAccountclearitemMapper;
    public List<MssAccountclearitem> selectListAuto(MssAccountclearitem mssAccountclearitem){
        return mssAccountclearitemMapper.selectListAuto(mssAccountclearitem);
    }

    @Override
    public List<MssAccountclearitem> queryClearitem(MssAccountclearitem mssAccountclearitem) {
        return mssAccountclearitemMapper.queryClearitem(mssAccountclearitem);
    }
}
