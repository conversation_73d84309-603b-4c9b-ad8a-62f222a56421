package com.sccl.modules.business.stationinfo.service;

import com.sccl.framework.service.IBaseService;
import com.sccl.modules.business.account.domain.Ltestation;
import com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol;
import com.sccl.modules.business.stationinfo.domain.CheckStationInfoDto;
import com.sccl.modules.business.stationinfo.domain.StationInfo;
import com.sccl.modules.business.stationinfo.dto.ResStationQueryDto;
import com.sccl.modules.business.stationinfo.vo.StationBatchStopVo;

import java.util.List;
import java.util.Map;

/**
 * 局站 服务层
 *
 * <AUTHOR>
 * @date 2019-05-29
 */
public interface IStationInfoService extends IBaseService<StationInfo>
{
    public int addStation(StationInfo stationInfo);
    public StationInfo addStationpro(StationInfo stationInfo);

    public int updateStation(StationInfo stationInfo);

    public List<StationInfo> selectmotherList(StationInfo stationInfo);
    public List<StationInfo> selectListByAmmeter(StationInfo stationInfo,String type);
    public List<Ltestation> getListLtestation(Ltestation stationInfo);
    public List<StationInfo> selectOld(Long id);
    public List<StationInfo> getResStation(StationInfo stationInfo);
    public List<String> getUserRoleAuth(Long id);
    public int IsStationnameExist(StationInfo stationInfo);
    public int IsStationnameExistInRecord(StationInfo stationInfo);
    public int IsStationnameExistEdit(StationInfo stationInfo);
    public int IsStationnameExistInRecordEdit(StationInfo stationInfo);
    public StationInfo getStationByName(String name);
    public String getStationCode();
    public boolean isAuthEditStation(Long id);
    public List<Ammeterorprotocol> getAmmeterListByStation(Ammeterorprotocol ammeterorprotocol);

    public List isInTodoList(Map<String,Object> params);

    public List getAllBelongCountry();
    public int isModifiedByNowUser(Long id,Long userid);
    public List<StationInfo> getStationAddr(StationInfo stationInfo);
    public List<StationInfo> getStationHousing(StationInfo stationInfo);
    public List<StationInfo> selectObjectBy(StationInfo stationInfo);
    public List<StationInfo> selectbaseList(StationInfo stationInfo);

    Map<String, String> checkStationInfo(CheckStationInfoDto stationInfo);

    List<StationInfo> getCheckListByAmmeter(StationInfo stationInfo, String type);

    Map<String, String> checkSheet(CheckStationInfoDto stationInfo);

    List<StationInfo> getResStationAndRoom(ResStationQueryDto queryDto);

    StationInfo select5grforinitid(String stationcode5gr);

    void set5grCodeForLn( StationInfo stationInfo);


    public List<StationInfo> selectListWithArea(StationInfo stationInfo);

    /**
     * 批量停用
     * @param vo
     * @return
     */
    int batchStop(StationBatchStopVo vo);
}
