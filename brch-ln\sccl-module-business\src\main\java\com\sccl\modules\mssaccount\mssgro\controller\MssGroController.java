package com.sccl.modules.mssaccount.mssgro.controller;

import java.util.List;

import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.modules.system.user.domain.User;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.mssaccount.mssgro.domain.MssGro;
import com.sccl.modules.mssaccount.mssgro.service.IMssGroService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 财务辅助提供的组织级次 预算责任中心 信息操作处理
 *
 * <AUTHOR>
 * @date 2019-05-01
 */
@RestController
@RequestMapping("/mssaccount/mssGro")
public class MssGroController extends BaseController {
    private String prefix = "mssaccount/mssGro";
    @Value("${sccl.deployTo}")
    private String deployTo;
    @Autowired
    private IMssGroService mssGroService;


    @RequiresPermissions("mssaccount:mssGro:view")
    @GetMapping()
    public String mssGro() {
        return prefix + "/mssGro";
    }

    /**
     * 查询财务辅助提供的组织级次 预算责任中心列表
     */
    @RequiresPermissions("mssaccount:mssGro:list")
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(MssGro mssGro) {
        //本单位下所有预算责任中心
        User user = this.getCurrentUser();
        List<IdNameVO> companies = user.getCompanies();
        mssGro.setCompany(companies.get(0).getId());
        startPage();
        // 查询 的是 view_org ，power_city_organization 表
        List<MssGro> list = mssGroService.selectByLikeAuto(mssGro);
        return getDataTable(list);
    }

    /**
     * 新增财务辅助提供的组织级次 预算责任中心
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存财务辅助提供的组织级次 预算责任中心
     */
    @RequiresPermissions("mssaccount:mssGro:add")
    //@Log(title = "财务辅助提供的组织级次 预算责任中心", action = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody MssGro mssGro) {
        return toAjax(mssGroService.insert(mssGro));
    }

    /**
     * 修改财务辅助提供的组织级次 预算责任中心
     */
    @GetMapping("/edit/{orgcode}")
    public AjaxResult edit(@PathVariable("orgcode") Long orgcode) {
        MssGro mssGro = mssGroService.get(orgcode);

        Object object = JSONObject.toJSON(mssGro);

        return this.success(object);
    }

    /**
     * 修改保存财务辅助提供的组织级次 预算责任中心
     */
    @RequiresPermissions("mssaccount:mssGro:edit")
    //@Log(title = "财务辅助提供的组织级次 预算责任中心", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody MssGro mssGro) {
        return toAjax(mssGroService.update(mssGro));
    }

    /**
     * 删除财务辅助提供的组织级次 预算责任中心
     */
    @RequiresPermissions("mssaccount:mssGro:remove")
    //@Log(title = "财务辅助提供的组织级次 预算责任中心", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(mssGroService.deleteByIds(Convert.toStrArray(ids)));
    }


    /**
     * 查看财务辅助提供的组织级次 预算责任中心
     */
    @RequiresPermissions("mssaccount:mssGro:view")
    @GetMapping("/view/{orgcode}")
    @ResponseBody
    public AjaxResult view(@PathVariable("orgcode") Long orgcode) {
        MssGro mssGro = mssGroService.get(orgcode);

        Object object = JSONObject.toJSON(mssGro);

        return this.success(object);
    }




    /**
     * 查询财务辅助提供的组织级次 预算责任中心列表,切换新表 dwd_mss_hana_org_d
     */
    @RequiresPermissions("mssaccount:mssGro:list")
    @RequestMapping("/listNew")
    @ResponseBody
    public TableDataInfo listNew(MssGro mssGro) {

        if ("sc".equals(deployTo)) {
            //本单位下所有预算责任中心
            User user = this.getCurrentUser();
            List<IdNameVO> companies = user.getCompanies();
            mssGro.setCompany(companies.get(0).getId());
            startPage();
            // 查询 的是 view_org ，power_city_organization 表
            List<MssGro> list = mssGroService.selectByLikeAutoNew(mssGro);
            return getDataTable(list);
        } else if ("ln".equals(deployTo)) {
            //本单位下所有预算责任中心
            User user = this.getCurrentUser();
            List<IdNameVO> companies = user.getCompanies();
            mssGro.setCompany(companies.get(0).getId());
            startPage();
            // 查询 的是 view_org ，power_city_organization 表
            List<MssGro> list = mssGroService.selectByLikeAuto(mssGro);
            return getDataTable(list);
        } else {
            throw new RuntimeException("错误，当前环境为未知，拒绝执行此操作");
        }
    }
}
