package com.sccl.modules.business.stationauditereply.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.business.stationauditereply.domain.StationauditEReply;
import com.sccl.modules.business.stationauditereply.service.IStationauditEReplyService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 基站一站式稽核结果异常回复 信息操作处理
 * 
 * <AUTHOR>
 * @date 2022-11-21
 */
@RestController
@RequestMapping("/business/stationauditEReply")
public class StationauditEReplyController extends BaseController
{
    private String prefix = "business/stationauditEReply";
	
	@Autowired
	private IStationauditEReplyService stationauditEReplyService;
	
	@RequiresPermissions("business:stationauditEReply:view")
	@GetMapping()
	public String stationauditEReply()
	{
	    return prefix + "/stationauditEReply";
	}
	
	/**
	 * 查询基站一站式稽核结果异常回复列表
	 */
	@RequiresPermissions("business:stationauditEReply:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(StationauditEReply stationauditEReply)
	{
		startPage();
        List<StationauditEReply> list = stationauditEReplyService.selectList(stationauditEReply);
		return getDataTable(list);
	}
	
	/**
	 * 新增基站一站式稽核结果异常回复
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存基站一站式稽核结果异常回复
	 */
	@RequiresPermissions("business:stationauditEReply:add")
	@Log(title = "基站一站式稽核结果异常回复", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody StationauditEReply stationauditEReply)
	{		
		return toAjax(stationauditEReplyService.insert(stationauditEReply));
	}

	/**
	 * 修改基站一站式稽核结果异常回复
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		StationauditEReply stationauditEReply = stationauditEReplyService.get(id);

		Object object = JSONObject.toJSON(stationauditEReply);

        return this.success(object);
	}
	
	/**
	 * 修改保存基站一站式稽核结果异常回复
	 */
	@RequiresPermissions("business:stationauditEReply:edit")
	@Log(title = "基站一站式稽核结果异常回复", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody StationauditEReply stationauditEReply)
	{		
		return toAjax(stationauditEReplyService.update(stationauditEReply));
	}
	
	/**
	 * 删除基站一站式稽核结果异常回复
	 */
	@RequiresPermissions("business:stationauditEReply:remove")
	@Log(title = "基站一站式稽核结果异常回复", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(stationauditEReplyService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看基站一站式稽核结果异常回复
     */
    @RequiresPermissions("business:stationauditEReply:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		StationauditEReply stationauditEReply = stationauditEReplyService.get(id);

        Object object = JSONObject.toJSON(stationauditEReply);

        return this.success(object);
    }

}
