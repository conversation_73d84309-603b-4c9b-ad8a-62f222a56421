package com.sccl.modules.business.noderesultstatistical.mapper;

import com.enrising.dcarbon.audit.AuditResult;
import com.sccl.modules.business.ecceptionprocess.domain.EcceptionProcess;
import com.sccl.modules.business.noderesultstatistical.domain.NodeResultStatistical;
import com.sccl.framework.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 统计指标 数据层
 * 
 * <AUTHOR>
 * @date 2022-11-24
 */
public interface NodeResultStatisticalMapper extends BaseMapper<NodeResultStatistical>
{

    int deleteByBillId(@Param("billId") Long billId);

    List<String> selectListForFillName(@Param("fillName") String fillName);

    List<String> selectListForFillNameAndStationId(EcceptionProcess process);

    String selectIds(@Param("nodeType") String nodeType, @Param("billId") String billId, @Param("level") String level);

    List<AuditResult> selectIdsByAuditResult(@Param("ids") List<String> ids);
}