package com.sccl.modules.rental.rentalcarmain.domain;

import com.sccl.modules.rental.rentalcar.domain.Rentalcar;
import com.sccl.modules.rental.rentalcarmodel.domain.Rentalcarmodel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;

import java.util.Date;
import java.math.BigDecimal;
import java.util.List;


/**
 * 车辆 所属 租赁项目表 rentalcarmain
 *
 * <AUTHOR>
 * @date 2019-08-23
 */
public class Rentalcarmain extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private List<Rentalcar> rentalcarList;

    public List<Rentalcar> getRentalcarList() {
        return rentalcarList;
    }

    public void setRentalcarList(List<Rentalcar> rentalcarList) {
        this.rentalcarList = rentalcarList;
    }

    private Long rcmid;

    public Long getRcmid() {
        return rcmid;
    }

    public void setRcmid(Long rcmid) {
        this.rcmid = rcmid;
    }

    /**
     *
     */
    private String setitle;
    /**
     *
     */
    private Long inputuserid;
    /**
     *
     */
    private Date inputdate;
    /**
     *
     */
    private String inputusername;
    /**
     *
     */
    private Long iprocessinstid;
    /**
     *
     */
    private String status;
    /**
     *
     */
    private String company;
    /**
     *
     */
    private String country;
    /**
     *
     */
    private String memo;

    private String companyName;
    private String countryName;
    private String statusName;
    private Date inputdateStart;
    private Date inputdateEnd;
    private String vin;// 车牌号 用于查询

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public Date getInputdateStart() {
        return inputdateStart;
    }

    public void setInputdateStart(Date inputdateStart) {
        this.inputdateStart = inputdateStart;
    }

    public Date getInputdateEnd() {
        return inputdateEnd;
    }

    public void setInputdateEnd(Date inputdateEnd) {
        this.inputdateEnd = inputdateEnd;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public void setSetitle(String setitle) {
        this.setitle = setitle;
    }

    public String getSetitle() {
        return setitle;
    }

    public void setInputuserid(Long inputuserid) {
        this.inputuserid = inputuserid;
    }

    public Long getInputuserid() {
        return inputuserid;
    }

    public void setInputdate(Date inputdate) {
        this.inputdate = inputdate;
    }

    public Date getInputdate() {
        return inputdate;
    }

    public void setInputusername(String inputusername) {
        this.inputusername = inputusername;
    }

    public String getInputusername() {
        return inputusername;
    }

    public void setIprocessinstid(Long iprocessinstid) {
        this.iprocessinstid = iprocessinstid;
    }

    public Long getIprocessinstid() {
        return iprocessinstid;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getCompany() {
        return company;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getCountry() {
        return country;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getMemo() {
        return memo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("rcmid", getRcmid())
                .append("setitle", getSetitle())
                .append("inputuserid", getInputuserid())
                .append("inputdate", getInputdate())
                .append("inputusername", getInputusername())
                .append("iprocessinstid", getIprocessinstid())
                .append("status", getStatus())
                .append("company", getCompany())
                .append("country", getCountry())
                .append("memo", getMemo())
                .toString();
    }

    private Boolean _disabled;

    public Boolean get_disabled() {
        return _disabled;
    }

    public void set_disabled(Boolean _disabled) {
        this._disabled = _disabled;
    }
}
