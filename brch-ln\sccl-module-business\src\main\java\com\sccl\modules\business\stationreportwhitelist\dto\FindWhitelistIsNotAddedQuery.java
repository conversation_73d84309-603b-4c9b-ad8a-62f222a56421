package com.sccl.modules.business.stationreportwhitelist.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/5/9 15:00
 * @describe 根据局站id查询未加入白名单电表列表
 */
@Getter
@Setter
public class FindWhitelistIsNotAddedQuery {

    /**
     * 局站id
     */
    @NotNull(message = "局站id不能为空")
    private Long stationId;

    /**
     * 白名单类型 1:一表多站 2:一站多表 3:单价
     */
    @NotBlank(message = "白名单类型不能为空")
    private String whitelistType;
}
