package com.sccl.modules.mssaccount.mssaccountclearitemaccount.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 预付冲销 挑对台账表 mss_accountclearitem_account
 * 
 * <AUTHOR>
 * @date 2019-11-25
 */
public class MssAccountclearitemAccount extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
    /** 报账单ID */
    private Long billId;
    /** 台账ID */
    private Long pcid;
    /** 挑对电量 */
    private Double pickingAmount;
    /** 挑对金额 */
    private Double pickingSum;
    /**  */
    private Date inputDate;
    private Integer status;

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	private String electrotypename;
    private String projectname;
	private String ammetercode;
	private String supplybureauammetercode;
	private String stationName;
	private String accountno;
	private String startdate;
	private String enddate;
	private BigDecimal curusedreadings;
	private BigDecimal curtotalreadings;
	private BigDecimal unitpirce;
	private BigDecimal accountmoney;

	public String getSupplybureauammetercode() {
		return supplybureauammetercode;
	}

	public void setSupplybureauammetercode(String supplybureauammetercode) {
		this.supplybureauammetercode = supplybureauammetercode;
	}

	public String getElectrotypename() {
		return electrotypename;
	}

	public void setElectrotypename(String electrotypename) {
		this.electrotypename = electrotypename;
	}

	public String getProjectname() {
		return projectname;
	}

	public void setProjectname(String projectname) {
		this.projectname = projectname;
	}

	public String getAmmetercode() {
		return ammetercode;
	}

	public void setAmmetercode(String ammetercode) {
		this.ammetercode = ammetercode;
	}

	public String getStationName() {
		return stationName;
	}

	public void setStationName(String stationName) {
		this.stationName = stationName;
	}

	public String getAccountno() {
		return accountno;
	}

	public void setAccountno(String accountno) {
		this.accountno = accountno;
	}

	public String getStartdate() {
		return startdate;
	}

	public void setStartdate(String startdate) {
		this.startdate = startdate;
	}

	public String getEnddate() {
		return enddate;
	}

	public void setEnddate(String enddate) {
		this.enddate = enddate;
	}

	public BigDecimal getCurusedreadings() {
		return curusedreadings;
	}

	public void setCurusedreadings(BigDecimal curusedreadings) {
		this.curusedreadings = curusedreadings;
	}

	public BigDecimal getCurtotalreadings() {
		return curtotalreadings;
	}

	public void setCurtotalreadings(BigDecimal curtotalreadings) {
		this.curtotalreadings = curtotalreadings;
	}

	public BigDecimal getUnitpirce() {
		return unitpirce;
	}

	public void setUnitpirce(BigDecimal unitpirce) {
		this.unitpirce = unitpirce;
	}

	public BigDecimal getAccountmoney() {
		return accountmoney;
	}

	public void setAccountmoney(BigDecimal accountmoney) {
		this.accountmoney = accountmoney;
	}

	public void setBillId(Long billId)
	{
		this.billId = billId;
	}

	public Long getBillId() 
	{
		return billId;
	}

	public void setPcid(Long pcid)
	{
		this.pcid = pcid;
	}

	public Long getPcid() 
	{
		return pcid;
	}

	public void setPickingAmount(Double pickingAmount)
	{
		this.pickingAmount = pickingAmount;
	}

	public Double getPickingAmount() 
	{
		return pickingAmount;
	}

	public void setPickingSum(Double pickingSum)
	{
		this.pickingSum = pickingSum;
	}

	public Double getPickingSum() 
	{
		return pickingSum;
	}

	public void setInputDate(Date inputDate)
	{
		this.inputDate = inputDate;
	}

	public Date getInputDate() 
	{
		return inputDate;
	}


	@Override
	public String toString() {
		return "MssAccountclearitemAccount{" +
				"billId=" + billId +
				", pcid=" + pcid +
				", pickingAmount=" + pickingAmount +
				", pickingSum=" + pickingSum +
				", inputDate=" + inputDate +
				", electrotypename='" + electrotypename + '\'' +
				", projectname='" + projectname + '\'' +
				", ammetercode='" + ammetercode + '\'' +
				", stationName='" + stationName + '\'' +
				", accountno='" + accountno + '\'' +
				", startdate='" + startdate + '\'' +
				", enddate='" + enddate + '\'' +
				", curusedreadings=" + curusedreadings +
				", curtotalreadings=" + curtotalreadings +
				", unitpirce=" + unitpirce +
				", accountmoney=" + accountmoney +
				'}';
	}
}
