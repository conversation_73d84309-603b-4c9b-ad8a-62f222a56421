package com.sccl.modules.business.waterexpense.domain;

import com.sccl.framework.web.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2022/1/6 9:44
 **/
@Getter
@Setter
public class WaterExpenseVo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 户号
     */
    private Long waterExpenseId;
    /**
     * 户名
     */
    private String waterExpenseName;
    /**
     * 所属区县
     */
    private Long country;
    /**
     * 分公司
     */
    private Long company;
    /**
     * 分局、支局
     */
    private String substation;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 管理负责人
     */
    private String master;
    /**
     * 单据状态
     */
    private Integer billStatus;
    /**
     * 关联局站编号
     */
    private String stationCode;
    /**
     * 在用1 停用0
     */
    private Integer status;

    private String companyName;

    private String countryName;

    // 翻表度数
    private BigDecimal maxdegree;

    // 倍率
    private BigDecimal magnification;
}
