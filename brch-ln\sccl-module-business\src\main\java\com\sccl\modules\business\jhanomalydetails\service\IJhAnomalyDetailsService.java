package com.sccl.modules.business.jhanomalydetails.service;


import com.sccl.modules.business.jhanomalydetails.domain.JhAnomalyDetails;
import com.sccl.framework.service.IBaseService;
import com.sccl.modules.business.jhanomalydetails.domain.JhPowerErrorVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 稽核结果详情 服务层
 * 
 * <AUTHOR>
 * @date 2024-02-27
 */
public interface IJhAnomalyDetailsService extends IBaseService<JhAnomalyDetails>
{


    List<JhAnomalyDetails> selectByTzIds(List<String> ids, List<String> jhsj);

    int saveOrUpdateList(List<JhAnomalyDetails> jhAnomalyDetailsList);
    //获取异常详情
    List<JhAnomalyDetails> getPowerError(JhPowerErrorVO powerAuditVO);

    //异常导出
    void exportPowerError(HttpServletResponse response, JhPowerErrorVO jhPowerErrorVO);

}
