package com.sccl.modules.business.temporarytower.service;

import com.sccl.framework.service.IBaseService;
import com.sccl.modules.business.temporarytower.domain.TemporaryTower;
import com.sccl.modules.system.role.domain.Role;

import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * __临时铁塔数据__<br/>
 * 2019/10/23
 *
 * <AUTHOR>
 */
public interface ITemporaryTowerService extends IBaseService<TemporaryTower> {

    /**
     * @Description: 删除全部
     * @author: dongk
     * @date: 2019/10/23
     * @param:
     * @return:
     */
    int deleteAll();

    /**
     * @Description: 导入临时铁塔
     * @author: dongk
     * @date: 2019/10/24
     * @param:
     * @return:
     */
    List<TemporaryTower> importExcel(String sheetName, InputStream input) throws Exception;

    List<TemporaryTower> importExcelstop(String sheetName, InputStream input) throws Exception;
    /**
     * @Description: 加入正式铁塔表
     * @author: dongk
     * @date: 2019/10/24
     * @param:
     * @return:
     */
    int initTowerInfo();

    /**
     * @Description: 生成局站
     * @author: dongk
     * @date: 2019/10/31
     * @param:
     * @return:
     */
    Map<String,Object> importTower();

    /**
     * @Description: 删除重复数据
     * @author: dongk
     * @date: 2019/11/6
     * @param:
     * @return:
     */
    int deleteRepeat();
    int deleteTwoAll();
    int deletetaAll();
    /**
     * @Description: 加入正式铁塔表
     * @author: dongk
     * @date: 2019/11/6
     * @param:
     * @return:
     */
    int insertTowerInfo();

    /**
     * @Description: 获取输入用户id的权限
     * @author: dongk
     * @date: 2019/12/2
     * @param:
     * @return:
     */
    List<Role> getUserRole();

    int insertTowerInfoTwo();
    int insertTowerInfoAll();
    Map<String, Object> importTowerTwo();

}
