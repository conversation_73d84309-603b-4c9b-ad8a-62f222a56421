package com.sccl.modules.mssaccount.dataanalysis.service;

import com.sccl.modules.mssaccount.dataanalysis.dto.GetByOrgIdDTO;
import com.sccl.modules.mssaccount.dataanalysis.vo.ElectricityBillResultVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface StatisticalAnalysisService {

    /**
     * 统计某个市州或全省
     */
    List<ElectricityBillResultVO> getByOrgId(GetByOrgIdDTO dto);

    /**
     * 导出
     */
    List<ElectricityBillResultVO> export(HttpServletResponse response, GetByOrgIdDTO dto);

}
