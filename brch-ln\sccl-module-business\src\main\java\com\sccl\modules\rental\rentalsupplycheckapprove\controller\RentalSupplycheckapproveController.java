package com.sccl.modules.rental.rentalsupplycheckapprove.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.rental.rentalsupplycheckapprove.domain.RentalSupplycheckapprove;
import com.sccl.modules.rental.rentalsupplycheckapprove.service.IRentalSupplycheckapproveService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 审批记录 信息操作处理
 * 
 * <AUTHOR>
 * @date 2019-08-23
 */
@RestController
@RequestMapping("/rental/rentalSupplycheckapprove")
public class RentalSupplycheckapproveController extends BaseController
{
    private String prefix = "rental/rentalSupplycheckapprove";
	
	@Autowired
	private IRentalSupplycheckapproveService rentalSupplycheckapproveService;
	
	@RequiresPermissions("rental:rentalSupplycheckapprove:view")
	@GetMapping()
	public String rentalSupplycheckapprove()
	{
	    return prefix + "/rentalSupplycheckapprove";
	}
	
	/**
	 * 查询审批记录列表
	 */
	@RequiresPermissions("rental:rentalSupplycheckapprove:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(RentalSupplycheckapprove rentalSupplycheckapprove)
	{
		startPage();
        List<RentalSupplycheckapprove> list = rentalSupplycheckapproveService.selectList(rentalSupplycheckapprove);
		return getDataTable(list);
	}
	
	/**
	 * 新增审批记录
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存审批记录
	 */
	@RequiresPermissions("rental:rentalSupplycheckapprove:add")
	//@Log(title = "审批记录", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody RentalSupplycheckapprove rentalSupplycheckapprove)
	{		
		return toAjax(rentalSupplycheckapproveService.insert(rentalSupplycheckapprove));
	}

	/**
	 * 修改审批记录
	 */
	@GetMapping("/edit/{pabaid}")
	public AjaxResult edit(@PathVariable("pabaid") Long pabaid)
	{
		RentalSupplycheckapprove rentalSupplycheckapprove = rentalSupplycheckapproveService.get(pabaid);

		Object object = JSONObject.toJSON(rentalSupplycheckapprove);

        return this.success(object);
	}
	
	/**
	 * 修改保存审批记录
	 */
	@RequiresPermissions("rental:rentalSupplycheckapprove:edit")
	//@Log(title = "审批记录", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody RentalSupplycheckapprove rentalSupplycheckapprove)
	{		
		return toAjax(rentalSupplycheckapproveService.update(rentalSupplycheckapprove));
	}
	
	/**
	 * 删除审批记录
	 */
	@RequiresPermissions("rental:rentalSupplycheckapprove:remove")
	//@Log(title = "审批记录", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(rentalSupplycheckapproveService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看审批记录
     */
    @RequiresPermissions("rental:rentalSupplycheckapprove:view")
    @GetMapping("/view/{pabaid}")
    @ResponseBody
    public AjaxResult view(@PathVariable("pabaid") Long pabaid)
    {
		RentalSupplycheckapprove rentalSupplycheckapprove = rentalSupplycheckapproveService.get(pabaid);

        Object object = JSONObject.toJSON(rentalSupplycheckapprove);

        return this.success(object);
    }

}
