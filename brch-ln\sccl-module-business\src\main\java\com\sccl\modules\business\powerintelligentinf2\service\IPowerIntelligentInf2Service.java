package com.sccl.modules.business.powerintelligentinf2.service;

import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.modules.business.powerintelligentinf2.domain.PowerIntelligentInf2;
import com.sccl.framework.service.IBaseService;
import com.sccl.modules.business.powerintelligentinf2.dto.Powerintelligentinf2Dto;

import java.util.List;
import java.util.Map;

/**
 * PUE管控主 服务层
 * 
 * <AUTHOR>
 * @date 2019-07-18
 */
public interface IPowerIntelligentInf2Service extends IBaseService<PowerIntelligentInf2>
{
    List<Map<String,Object>> selectByList(PowerIntelligentInf2 powerIntelligentInf2,String ammeterCode,List<IdNameVO> companies);
    int deletePUECountrols(String ids);

    int updateData(Powerintelligentinf2Dto powerintelligentinf2Dto);
    Map<String,Object> selectAcountByDateAmmeter(Long ammeterid,Integer type);


    /**
     * @Description: 查询送往采集适配系统数据
     * @author: liucong
     * @return:
     */
    List<Map<String,Object>> selectTotalPower();
}
