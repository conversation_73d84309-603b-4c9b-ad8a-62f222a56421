package com.sccl.modules.business.statinAudit.domain;

import com.sccl.modules.business.poweraudit.entity.PowerAuditEntity;
import lombok.Data;

/**
 * @Author: 李佳杰
 * @CreateTime: 2024-03-01  09:21
 * @Description: 稽核结果
 * @Version: 1.0
 */
@Data
public class AuditResults {
    /**稽核总进度，满进度：100*/
    String progress = "100";

    /**稽核完成情况，完成，未完成*/
    String staute = "完成";


    /**台账稽核结果列表*/
    String msg;

    /**通过数*/
    Integer successCount = 0;

    /**失败数*/
    Integer failCount = 0;

    /**稽核总数*/
    Integer totalSize = 0;

    /**StationAudit列表对象*/

    PowerAuditEntity powerAuditEntity;


}
