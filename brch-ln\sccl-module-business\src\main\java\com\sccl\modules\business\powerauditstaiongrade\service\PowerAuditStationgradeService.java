package com.sccl.modules.business.powerauditstaiongrade.service;

import com.sccl.modules.business.msg.domain.Message;
import com.sccl.modules.business.powerauditstaiongrade.entity.*;
import com.baomidou.mybatisplus.extension.service.IService;

import com.sccl.modules.business.stationinfo.domain.PowerStationInfoRJtlte;
import com.sccl.modules.system.attachments.domain.Attachments;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
public interface PowerAuditStationgradeService extends IService<PowerAuditStationgradeEntity> {

    List<PowerAuditStationgradeEntity> selectList(PowerAuditStationgradeEntity stationgradeEntity);

    List<TowerStationGradeaudit> selectListForTowerStaionGrade(TowerStationGradeaudit towerStationGrade);

    Attachments exportExcel(List<TowerStationGradeaudit> towerStationGradeList, Map<String, String> exportColumnMap, Map<String, String> promptColumnMap);

    List<StaionMapBill> selectListForStationMapBill(PowerStationInfoRJtlte rJtlte);

    Attachments exportExcelForStaionMapBill(List<StaionMapBill> rJtltes, Map<String, String> exportColumnMapforstaionbill, Map<String, String> promptColumnMap);

    List<TowerResultSummary> selecTowerResultSummaryList(Message message);
    List<TowerResultSummary> selecTowerResultSummary(Message message);

    List<TowerAuditPro> selecTowerAuditList(Message message);

    List<StaionAnalysisPro> selectStaionAnalysisList(StaionAnalysis message);

    List<StaionAnalysisDetail> getStaionAnalysisDetailForAudit(StaionAnalysis keyOb);

    List<StaionAnalysisDetail> getStaionAnalysisDetailForGrade(StaionAnalysis keyOb);

    String generatPrice(String month);

    List<TowerAudit> selecTowerAuditListPro(Message message);

    List<ExceptionAnalysis> selectExceptionAnalysis(StaionAnalysis message);
}
