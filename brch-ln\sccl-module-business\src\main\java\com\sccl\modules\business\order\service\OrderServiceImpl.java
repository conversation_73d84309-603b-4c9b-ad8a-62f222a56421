package com.sccl.modules.business.order.service;

import org.springframework.stereotype.Service;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.business.order.domain.Order;
import com.sccl.modules.uniflow.common.WFModel;


/**
 * 订单 服务层实现
 * 
 * <AUTHOR>
 * @date 2019-03-05
 */
@Service
public class OrderServiceImpl extends BaseServiceImpl<Order> implements IOrderService
{
    //@Autowired
    //private IWfProcInstService wfProcInstService;
    /**
     * 流程引擎回调（通过MQ）
     * @param wfModel 流程消息对象
     */
    @Override
    public void uniflowCallBack(WFModel wfModel) {
        System.out.println("-----------------lt:"+wfModel.toString());
//		esConsume.callBack(msg.getPayload());
    }

}
