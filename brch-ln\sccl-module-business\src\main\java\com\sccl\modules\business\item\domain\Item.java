package com.sccl.modules.business.item.domain;

import java.math.BigDecimal;
import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.sccl.framework.web.domain.BaseEntity;


/**
 * 项目表 T_ITEM
 * 
 * <AUTHOR>
 * @date 2019-03-04
 */
public class Item extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
    /** 项目名称 */
    private String itemName;
    /** 项目编码 */
    private String itemCode;
    /** 删除标识 */
    private String delFlag;
    /** 金额 */
    private Long itemSum;
    /** 时间 */
    private Date inputDate;
    /** $column.columnComment */
    private BigDecimal amount;


	public void setItemName(String itemName)
	{
		this.itemName = itemName;
	}

	public String getItemName() 
	{
		return itemName;
	}

	public void setItemCode(String itemCode)
	{
		this.itemCode = itemCode;
	}

	public String getItemCode() 
	{
		return itemCode;
	}

	public void setDelFlag(String delFlag)
	{
		this.delFlag = delFlag;
	}

	public String getDelFlag() 
	{
		return delFlag;
	}








	public void setItemSum(Long itemSum)
	{
		this.itemSum = itemSum;
	}

	public Long getItemSum() 
	{
		return itemSum;
	}

	public void setInputDate(Date inputDate)
	{
		this.inputDate = inputDate;
	}

	public Date getInputDate() 
	{
		return inputDate;
	}

	public void setAmount(BigDecimal amount)
	{
		this.amount = amount;
	}

	public BigDecimal getAmount() 
	{
		return amount;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id",getId())
            .append("itemName",getItemName())
            .append("itemCode",getItemCode())
            .append("delFlag",getDelFlag())
            .append("creatorId",getCreatorId())
            .append("creatorName",getCreatorName())
            .append("createTime",getCreateTime())
            .append("updateById",getUpdateById())
            .append("updateByName",getUpdateByName())
            .append("updateTime",getUpdateTime())
            .append("remark",getRemark())
            .append("itemSum",getItemSum())
            .append("inputDate",getInputDate())
            .append("amount",getAmount())
            .toString();
    }
}
