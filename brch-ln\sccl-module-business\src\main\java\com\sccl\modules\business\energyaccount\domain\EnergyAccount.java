package com.sccl.modules.business.energyaccount.domain;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;

import java.util.Date;
import java.math.BigDecimal;


/**
 * 油水气出入台账表 energy_account
 *
 * <AUTHOR>
 * @date 2021-12-15
 */
@Getter
@Setter
public class EnergyAccount extends BaseEntity {
    private static final long serialVersionUID = 1L;

	private Long pcid;

    /**
     * 台账期号：根据台账的录入日期来编号：如用电台账201201；用于为各次台账建立顺序关系，便于查询
     */
    private String accountno;
    /**
     * 基础信息关联ID
     */
    private String relateid;
    /**
     * 开始年
     */
    private String startyear;
    /**
     * 开始月
     */
    private String startmonth;
    /**
     * 截止年
     */
    private String endyear;
    /**
     * 截止月
     */
    private String endmonth;
    /**
     * 上期剩余
     */
    private BigDecimal prevhighreadings;
    /**
     * 上期平值
     */
    private BigDecimal prevflatreadings;
    /**
     * 上期谷值
     */
    private BigDecimal prevlowreadings;
    /**
     * 上期止度
     */
    private BigDecimal prevtotalreadings;
    /**
     * 本期峰值
     */
    private BigDecimal curhighreadings;
    /**
     * 本期平值
     */
    private BigDecimal curflatreadings;
    /**
     * 本期谷值
     */
    private BigDecimal curlowreadings;
    /**
     * 本期止度
     */
    private BigDecimal curtotalreadings;
    /**
     * 倍率在电表的基础信息中有，在台账中保存倍率是防止更换电表导致改变电表倍率的情况
     */
    private BigDecimal multtimes;
    /**
     * 本期用量 加油量 耗油量
     */
    private BigDecimal curusedreadings;
    /**
     * 单价
     */
    private BigDecimal unitpirce;
    /**
     * 计算的专票含税金额
     */
    private BigDecimal taxticketmoney;
    /**
     * 计算的普票含税金额
     */
    private BigDecimal ticketmoney;
    /**
     * 其它：记录发票、新安装、换表等一系列费用
     */
    private BigDecimal ullagemoney;
    /**
     * 总金额
     */
    private BigDecimal accountmoney;
    /**
     * 定额度数
     */
    private BigDecimal quotareadings;
    /**
     * 录入人员ID
     */
    private String inputerid;
    /**
     * 录入日期(购买或出库时间)
     */
    private Date inputdate;
    /**
     * 最后编辑人
     */
    private String lastediterid;
    /**
     * 最后编辑日期
     */
    private Date lasteditdate;
    /**
     * 部门ID
     */
    private Long orgid;
    /**
     * 状态1为正常;2流程中 3报账完成 4已生成归集单 5已退回
     */
    private BigDecimal status;
    /**
     * 0,正常,2手动修改上期止度,3手动修改上期峰谷平4,手工修改上期起始日期
     */
    private BigDecimal opflag;
    /**
     * 损耗
     */
    private BigDecimal transformerullage;
    /**
     * 0,老的电表协议1,新增的电表协议
     */
    private BigDecimal isnew;
    /**
     * 账有效性,填写后的台账<总费用不为0>标识为有效台账 : 有效 - 1 , 无效 - 0
     */
    private BigDecimal effective;
    /**
     * 分公司
     */
    private Long company;
    /**
     * 责任中心
     */
    private Long country;
    /**
     * 定额浮动比(%) , 公式 : (电量+电损-定额)/定额
     */
    private BigDecimal quotereadingsratio;
    /**
     * 总量 : case when  power_ammeterorprotocol.category = 5  then  总电量 =  -( abs(电量)+电损  )  else 总电量 = abs(电量)+电损  end
     */
    private BigDecimal totalusedreadings;
    /**
     * 起始日期-格式为: yyyyMMdd
     * 购油时间
     */
    private String startdate;
    /**
     * 截止日期-格式为: yyyyMMdd
     */
    private String enddate;
    /**
     * 分割比例
     */
    private BigDecimal percent;
    /**
     * 专票税额
     */
    private BigDecimal taxamount;
    /**
     * 专票税率
     */
    private BigDecimal taxrate;
    /**
     * 用户输入的专票含税金额
     */
    private BigDecimal inputtaxticketmoney;
    /**
     * 用户输入的普票含税金额
     */
    private BigDecimal inputticketmoney;
    /**
     * 普票税额（四川）
     */
    private BigDecimal tickettaxamount;
    /**
     * 用户输入的备注
     */
    private String bz;
    /**
     * 移动抄表业务id
     */
    private Integer mobileaccountid;
    /**
     * 1,油,2水,3气
     */
    private String energytype;
    /**
     * 1,入2,出
     */
    private String inout;
    /**
     * 对应卡id
     */
    private Long cardid;
    /**
     * 外部订单号
     */
    private String extracode;
    /**
     * 时长
     */
    private BigDecimal usedtime;
    /**
     * 单位量
     */
    private BigDecimal unit;

    /** 油机编号 */
    private String oilEngineId;
    /** 油机编号 */
    private String oil_engine_id;
    /** 油机编号 */
    private Long expenseid;

    /** 车牌号 */
    private String carplate;

    private String del_flag;

    private String ifout;
    private String executor;
    private String supervisor;
    private String reviewer;
    /**
     * 分公司
     */
    private String companyName;
    /**
     * 责任中心
     */
    private String countryName;
}
