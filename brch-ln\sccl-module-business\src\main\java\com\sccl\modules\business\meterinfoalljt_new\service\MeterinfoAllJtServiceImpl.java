package com.sccl.modules.business.meterinfoalljt_new.service;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sccl.modules.business.meterinfoalljt_new.domain.MeterinfoAllJt;
import com.sccl.modules.business.meterinfoalljt_new.dto.MeterinfoAllJtQueryDTO;
import com.sccl.modules.business.meterinfoalljt_new.mapper.MeterinfoAllJtMapper;
import com.sccl.modules.business.meterinfoalljt_new.vo.MeterinfoAllJtVO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 表计清单查询 服务层实现
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
public class MeterinfoAllJtServiceImpl extends ServiceImpl<MeterinfoAllJtMapper, MeterinfoAllJt> implements IMeterinfoAllJtService {

    @Autowired
    private MeterinfoAllJtMapper meterinfoAllJtMapper;

    /**
     * 查询表计清单列表
     *
     * @param queryDTO 查询条件
     * @return 表计清单列表
     */
    @Override
    public List<MeterinfoAllJtVO> selectMeterinfoAllJtList(MeterinfoAllJtQueryDTO queryDTO) {
        // 参数校验
        if (ObjUtil.isEmpty(queryDTO)) {
            queryDTO = new MeterinfoAllJtQueryDTO();
        }

        return meterinfoAllJtMapper.selectMeterinfoAllJtList(queryDTO);
    }

    /**
     * 根据ID查询表计清单详情
     *
     * @param id 主键ID
     * @return 表计清单详情
     */
    @Override
    public MeterinfoAllJtVO selectMeterinfoAllJtById(Long id) {
        if (ObjUtil.isEmpty(id)) {
            return null;
        }

        return meterinfoAllJtMapper.selectMeterinfoAllJtById(id);
    }

    /**
     * 导入表计清单Excel - 优化内存使用
     *
     * @param sheetName 工作表名称
     * @param input     输入流
     * @return 导入结果
     */
    @Override
    public Map<String, Object> importExcel(String sheetName, InputStream input) throws Exception {
        Map<String, Object> result = new HashMap<>();
        List<String> errorMessages = new ArrayList<>();
        int successCount = 0;
        int totalCount = 0;
        int batchSize = 100; // 大幅减小批量处理大小
        List<MeterinfoAllJt> batchList = new ArrayList<>();

        Workbook workbook = null;
        try {
            // 检查可用内存
            Runtime runtime = Runtime.getRuntime();
            long maxMemory = runtime.maxMemory();
            long usedMemory = runtime.totalMemory() - runtime.freeMemory();
            long availableMemory = maxMemory - usedMemory;

            System.out.println("最大内存: " + (maxMemory / 1024 / 1024) + "MB");
            System.out.println("已用内存: " + (usedMemory / 1024 / 1024) + "MB");
            System.out.println("可用内存: " + (availableMemory / 1024 / 1024) + "MB");

            if (availableMemory < 200 * 1024 * 1024) { // 少于200MB可用内存
                result.put("success", false);
                result.put("message", "当前可用内存不足200MB，请稍后重试或联系管理员");
                return result;
            }

            // 使用最小内存占用的方式创建workbook
            workbook = new XSSFWorkbook(input);
            Sheet sheet = workbook.getSheet(sheetName);
            if (sheet == null) {
                sheet = workbook.getSheetAt(0);
            }

            int rows = sheet.getPhysicalNumberOfRows();
            if (rows <= 1) {
                result.put("success", false);
                result.put("message", "Excel文件中没有数据行");
                return result;
            }

            System.out.println("开始处理Excel文件，总行数: " + rows);

            // 逐行处理，立即释放行对象
            for (int i = 1; i < rows; i++) {
                Row row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }

                totalCount++;
                try {
                    MeterinfoAllJt entity = parseRowToEntityQuickly(row, i + 1);
                    if (entity != null) {
                        batchList.add(entity);

                        // 更频繁的批量处理
                        if (batchList.size() >= batchSize) {
                            successCount += batchInsertQuickly(batchList, errorMessages);
                            batchList.clear();

                            // 每批处理后强制垃圾回收
                            System.gc();
                        }
                    }
                } catch (Exception e) {
                    // 只记录第一个解析错误
                    if (errorMessages.isEmpty()) {
                        errorMessages.add("数据解析失败：" + e.getMessage());
                    }
                }

                // 更频繁的进度输出和内存检查
                if (i % 500 == 0) {
                    long currentUsed = runtime.totalMemory() - runtime.freeMemory();
                    System.out.println("已处理 " + i + " 行，成功 " + successCount + " 条，当前内存使用: " + (currentUsed / 1024 / 1024) + "MB");

                    // 如果内存使用过高，强制垃圾回收
                    if (currentUsed > maxMemory * 0.8) {
                        System.gc();
                        Thread.sleep(100); // 给GC一点时间
                    }
                }

                // 清除行引用，帮助GC
                row = null;
            }

            // 处理剩余数据
            if (!batchList.isEmpty()) {
                successCount += batchInsertQuickly(batchList, errorMessages);
                batchList.clear();
            }

        } catch (OutOfMemoryError e) {
            System.gc(); // 尝试释放内存
            result.put("success", false);
            result.put("message", "文件过大，内存不足，请分批导入或联系管理员增加服务器内存");
            return result;
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "Excel文件处理失败：" + e.getMessage());
            return result;
        } finally {
            // 确保资源释放
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (Exception e) {
                    // 忽略关闭异常
                }
            }
            // 清理引用
            batchList = null;
            // 强制垃圾回收
            System.gc();
        }

        int failCount = totalCount - successCount;

        result.put("success", true);
        result.put("totalCount", totalCount);
        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("message", "导入完成，成功导入" + successCount + "条数据，失败" + failCount + "条");

        // 只返回1条错误日志（如果有错误的话）
        if (!errorMessages.isEmpty()) {
            result.put("errorMessages", errorMessages.subList(0, 1)); // 只取第一条错误信息
        }

        return result;
    }

    /**
     * 快速批量插入
     */
    private int batchInsertQuickly(List<MeterinfoAllJt> entityList, List<String> errorMessages) {
        int successCount = 0;

        for (MeterinfoAllJt entity : entityList) {
            try {
                meterinfoAllJtMapper.insert(entity);
                successCount++;
            } catch (Exception e) {
                // 只记录第一个错误，避免大量重复错误信息
                if (errorMessages.isEmpty()) {
                    errorMessages.add("数据保存失败：" + e.getMessage());
                }
            }
        }

        return successCount;
    }

    /**
     * 快速解析行数据（减少对象创建）
     */
    private MeterinfoAllJt parseRowToEntityQuickly(Row row, int rowNum) throws Exception {
        MeterinfoAllJt entity = new MeterinfoAllJt();

        try {
            entity.setMsgId(getCellStringValueQuickly(row.getCell(0)));
            entity.setProvinceCode(getCellStringValueQuickly(row.getCell(1)));
            entity.setCityCode(getCellStringValueQuickly(row.getCell(2)));
            entity.setCityName(getCellStringValueQuickly(row.getCell(3)));
            entity.setCountyCode(getCellStringValueQuickly(row.getCell(4)));
            entity.setCountyName(getCellStringValueQuickly(row.getCell(5)));
            entity.setEnergyMeterCode(getCellStringValueQuickly(row.getCell(6)));
            entity.setEnergyMeterName(getCellStringValueQuickly(row.getCell(7)));
            entity.setEnergyType(getCellStringValueQuickly(row.getCell(8)));
            entity.setTypeStationCode(getCellStringValueQuickly(row.getCell(9)));
            entity.setContractPrice(getCellStringValueQuickly(row.getCell(10)));
            entity.setStatus(getCellStringValueQuickly(row.getCell(11)));
            entity.setUsageCopy(getCellStringValueQuickly(row.getCell(12)));
            entity.setStationCode(getCellStringValueQuickly(row.getCell(13)));
            entity.setStationName(getCellStringValueQuickly(row.getCell(14)));
            entity.setStationLocation(getCellStringValueQuickly(row.getCell(15)));
            entity.setStationStatus(getCellStringValueQuickly(row.getCell(16)));
            entity.setStationType(getCellStringValueQuickly(row.getCell(17)));
            entity.setLargeIndustrialElectricityFlag(getCellStringValueQuickly(row.getCell(18)));
            entity.setEnergySupplyWay(getCellStringValueQuickly(row.getCell(19)));
            entity.setPowerGridEnergyMeterCode(getCellStringValueQuickly(row.getCell(20)));
            entity.setSiteCode(getCellStringValueQuickly(row.getCell(21)));

            // 简化时间处理
            entity.setCreateTime(new Date());

            // 设置默认值
            entity.setDelFlag(0);
            entity.setSyncFlag(0);

        } catch (Exception e) {
            throw new Exception("第" + rowNum + "行数据格式错误：" + e.getMessage());
        }

        return entity;
    }

    /**
     * 快速获取单元格字符串值
     */
    private String getCellStringValueQuickly(Cell cell) {
        if (cell == null) {
            return null;
        }

        try {
            switch (cell.getCellType()) {
                case Cell.CELL_TYPE_STRING:
                    return cell.getStringCellValue();
                case Cell.CELL_TYPE_NUMERIC:
                    return String.valueOf((long) cell.getNumericCellValue());
                default:
                    return null;
            }
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 批量插入数据
     */
    private int batchInsert(List<MeterinfoAllJt> entityList, List<String> errorMessages, int startRowNum) {
        int successCount = 0;
        for (int i = 0; i < entityList.size(); i++) {
            try {
                meterinfoAllJtMapper.insert(entityList.get(i));
                successCount++;
            } catch (Exception e) {
                errorMessages.add("第" + (startRowNum + i) + "行数据保存失败：" + e.getMessage());
            }
        }
        return successCount;
    }

    /**
     * 解析行数据为实体对象（用于流式处理）
     */
    private MeterinfoAllJt parseRowDataToEntity(List<String> rowData, int rowNum) throws Exception {
        if (rowData.size() < 23) {
            throw new Exception("数据列数不足，至少需要23列");
        }

        MeterinfoAllJt entity = new MeterinfoAllJt();

        try {
            entity.setMsgId(getStringValue(rowData, 0));
            entity.setProvinceCode(getStringValue(rowData, 1));
            entity.setCityCode(getStringValue(rowData, 2));
            entity.setCityName(getStringValue(rowData, 3));
            entity.setCountyCode(getStringValue(rowData, 4));
            entity.setCountyName(getStringValue(rowData, 5));
            entity.setEnergyMeterCode(getStringValue(rowData, 6));
            entity.setEnergyMeterName(getStringValue(rowData, 7));
            entity.setEnergyType(getStringValue(rowData, 8));
            entity.setTypeStationCode(getStringValue(rowData, 9));
            entity.setContractPrice(getStringValue(rowData, 10));
            entity.setStatus(getStringValue(rowData, 11));
            entity.setUsageCopy(getStringValue(rowData, 12));
            entity.setStationCode(getStringValue(rowData, 13));
            entity.setStationName(getStringValue(rowData, 14));
            entity.setStationLocation(getStringValue(rowData, 15));
            entity.setStationStatus(getStringValue(rowData, 16));
            entity.setStationType(getStringValue(rowData, 17));
            entity.setLargeIndustrialElectricityFlag(getStringValue(rowData, 18));
            entity.setEnergySupplyWay(getStringValue(rowData, 19));
            entity.setPowerGridEnergyMeterCode(getStringValue(rowData, 20));
            entity.setSiteCode(getStringValue(rowData, 21));

            // 处理创建时间
            String timeStr = getStringValue(rowData, 22);
            if (StrUtil.isNotBlank(timeStr)) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    entity.setCreateTime(sdf.parse(timeStr));
                } catch (Exception e) {
                    entity.setCreateTime(new Date());
                }
            } else {
                entity.setCreateTime(new Date());
            }

            // 设置默认值
            entity.setDelFlag(0);
            entity.setSyncFlag(0);

        } catch (Exception e) {
            throw new Exception("第" + rowNum + "行数据格式错误：" + e.getMessage());
        }

        return entity;
    }

    /**
     * 安全获取字符串值
     */
    private String getStringValue(List<String> rowData, int index) {
        if (index >= rowData.size()) {
            return null;
        }
        String value = rowData.get(index);
        return StrUtil.isBlank(value) ? null : value.trim();
    }

    /**
     * 解析Excel行数据为实体对象（原方法，保留兼容性）
     */
    private MeterinfoAllJt parseRowToEntity(Row row, int rowNum) throws Exception {
        MeterinfoAllJt entity = new MeterinfoAllJt();

        try {
            // 根据Excel列顺序解析数据，映射到新的表结构字段
            entity.setMsgId(getCellStringValue(row.getCell(0))); // msg_id
            entity.setProvinceCode(getCellStringValue(row.getCell(1))); // provinceCode
            entity.setCityCode(getCellStringValue(row.getCell(2))); // cityCode
            entity.setCityName(getCellStringValue(row.getCell(3))); // cityName
            entity.setCountyCode(getCellStringValue(row.getCell(4))); // countyCode
            entity.setCountyName(getCellStringValue(row.getCell(5))); // countyName
            entity.setEnergyMeterCode(getCellStringValue(row.getCell(6))); // energyMeterCode
            entity.setEnergyMeterName(getCellStringValue(row.getCell(7))); // energyMeterName
            entity.setEnergyType(getCellStringValue(row.getCell(8))); // energyType
            entity.setTypeStationCode(getCellStringValue(row.getCell(9))); // typeStationCode
            entity.setContractPrice(getCellStringValue(row.getCell(10))); // contractPrice
            entity.setStatus(getCellStringValue(row.getCell(11))); // status
            entity.setUsageCopy(getCellStringValue(row.getCell(12))); // usageCopy
            entity.setStationCode(getCellStringValue(row.getCell(13))); // stationCode
            entity.setStationName(getCellStringValue(row.getCell(14))); // stationName
            entity.setStationLocation(getCellStringValue(row.getCell(15))); // stationLocation
            entity.setStationStatus(getCellStringValue(row.getCell(16))); // stationStatus
            entity.setStationType(getCellStringValue(row.getCell(17))); // stationType
            entity.setLargeIndustrialElectricityFlag(getCellStringValue(row.getCell(18))); // largeIndustrialElectricityFlag
            entity.setEnergySupplyWay(getCellStringValue(row.getCell(19))); // energySupplyWay
            entity.setPowerGridEnergyMeterCode(getCellStringValue(row.getCell(20))); // powerGridEnergyMeterCode
            entity.setSiteCode(getCellStringValue(row.getCell(21))); // siteCode

            // 处理创建时间
            Cell createTimeCell = row.getCell(22);
            if (createTimeCell != null) {
                if (createTimeCell.getCellType() == Cell.CELL_TYPE_NUMERIC && DateUtil.isCellDateFormatted(createTimeCell)) {
                    entity.setCreateTime(createTimeCell.getDateCellValue());
                } else {
                    String timeStr = getCellStringValue(createTimeCell);
                    if (StrUtil.isNotBlank(timeStr)) {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        entity.setCreateTime(sdf.parse(timeStr));
                    }
                }
            }

            // 设置默认值
            if (entity.getCreateTime() == null) {
                entity.setCreateTime(new Date());
            }
            entity.setDelFlag(0);
            entity.setSyncFlag(0);

        } catch (Exception e) {
            throw new Exception("第" + rowNum + "行数据格式错误：" + e.getMessage());
        }

        return entity;
    }

    /**
     * 获取单元格字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case Cell.CELL_TYPE_STRING:
                return cell.getStringCellValue();
            case Cell.CELL_TYPE_NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    return sdf.format(cell.getDateCellValue());
                } else {
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            case Cell.CELL_TYPE_BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case Cell.CELL_TYPE_FORMULA:
                return cell.getCellFormula();
            default:
                return null;
        }
    }

    /**
     * 获取单元格整数值
     */
    private Integer getCellIntValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case Cell.CELL_TYPE_NUMERIC:
                return (int) cell.getNumericCellValue();
            case Cell.CELL_TYPE_STRING:
                String strValue = cell.getStringCellValue();
                if (StrUtil.isBlank(strValue)) {
                    return null;
                }
                return Integer.parseInt(strValue);
            default:
                return null;
        }
    }

    /**
     * 获取单元格双精度值
     */
    private Double getCellDoubleValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case Cell.CELL_TYPE_NUMERIC:
                return cell.getNumericCellValue();
            case Cell.CELL_TYPE_STRING:
                String strValue = cell.getStringCellValue();
                if (StrUtil.isBlank(strValue)) {
                    return null;
                }
                return Double.parseDouble(strValue);
            default:
                return null;
        }
    }
}
