package com.sccl.modules.rental.rentalcarmodelmain.mapper;

import com.sccl.modules.rental.rentalcarmodelmain.domain.Rentalcarmodelmain;
import com.sccl.framework.mapper.BaseMapper;

import java.util.List;

/**
 * 车辆 （车型主单） 数据层
 * 
 * <AUTHOR>
 * @date 2019-08-23
 */
public interface RentalcarmodelmainMapper extends BaseMapper<Rentalcarmodelmain>
{


    List<Rentalcarmodelmain> selectListByIds(String[] toStrArray);
}