package com.sccl.modules.business.stationquotaconfig.controller;

import com.alibaba.fastjson.JSONObject;
import com.sccl.common.support.Convert;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.stationquotaconfig.domain.StationQuotaConfig;
import com.sccl.modules.business.stationquotaconfig.service.IStationQuotaConfigService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 基站定额分公司设置 信息操作处理
 * 
 * <AUTHOR>
 * @date 2021-08-12
 */
@RestController
@RequestMapping("/business/stationQuotaConfig")
public class StationQuotaConfigController extends BaseController
{
    private String prefix = "business/stationQuotaConfig";
	
	@Autowired
	private IStationQuotaConfigService stationQuotaConfigService;
	
	@RequiresPermissions("business:stationQuotaConfig:view")
	@GetMapping()
	public String stationQuotaConfig()
	{
	    return prefix + "/stationQuotaConfig";
	}
	
	/**
	 * 查询基站定额分公司设置列表
	 */
	@RequiresPermissions("business:stationQuotaConfig:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(StationQuotaConfig stationQuotaConfig)
	{
		startPage();
        List<StationQuotaConfig> list = stationQuotaConfigService.selectList(stationQuotaConfig);
		return getDataTable(list);
	}
	
	/**
	 * 新增基站定额分公司设置
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存基站定额分公司设置
	 */
	@RequiresPermissions("business:stationQuotaConfig:add")
	@Log(title = "基站定额分公司设置", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody StationQuotaConfig stationQuotaConfig)
	{		
		return toAjax(stationQuotaConfigService.insert(stationQuotaConfig));
	}

	/**
	 * 修改基站定额分公司设置
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		StationQuotaConfig stationQuotaConfig = stationQuotaConfigService.get(id);

		Object object = JSONObject.toJSON(stationQuotaConfig);

        return this.success(object);
	}
	
	/**
	 * 修改保存基站定额分公司设置
	 */
	@RequiresPermissions("business:stationQuotaConfig:edit")
	@Log(title = "基站定额分公司设置", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody StationQuotaConfig stationQuotaConfig)
	{		
		return toAjax(stationQuotaConfigService.update(stationQuotaConfig));
	}
	
	/**
	 * 删除基站定额分公司设置
	 */
	@RequiresPermissions("business:stationQuotaConfig:remove")
	@Log(title = "基站定额分公司设置", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(stationQuotaConfigService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看基站定额分公司设置
     */
    @RequiresPermissions("business:stationQuotaConfig:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		StationQuotaConfig stationQuotaConfig = stationQuotaConfigService.get(id);

        Object object = JSONObject.toJSON(stationQuotaConfig);

        return this.success(object);
    }

}
