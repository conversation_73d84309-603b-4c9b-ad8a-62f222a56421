package com.sccl.modules.business.meterinfoalljt_new.vo;

import com.sccl.framework.aspectj.lang.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

/**
 * 在网表计数据清单 导入对象
 */
@Data
public class MeterinfoAllJtImportExlVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @Excel(name = "msg_id")
    private String msgId;

    /**
     * 省局组织编码 财辅组织
     */
    @Excel(name = "province_code")
    private String provinceCode;

    /**
     * 市局组织编码 财辅组织
     */
    @Excel(name = "city_code")
    private String cityCode;

    /**
     * 市局组织名称 财辅组织
     */
    @Excel(name = "city_name")
    private String cityName;

    /**
     * 区县组织编码 财辅组织
     */
    @Excel(name = "county_code")
    private String countyCode;

    /**
     * 区县组织名称 财辅组织
     */
    @Excel(name = "county_name")
    private String countyName;

    /**
     * 电表编码
     */
    @Excel(name = "energy_meter_code")
    private String energyMeterCode;

    /**
     * 电表名称
     */
    @Excel(name = "energy_meter_name")
    private String energyMeterName;

    /**
     * 类型
     */
    @Excel(name = "energy_type")
    private String energyType;

    @Excel(name = "type_station_code")
    private String typeStationCode;

    /**
     * 单价
     */
    @Excel(name = "contract_price")
    private String contractPrice;

    /**
     * 电表状态	必填	状态：1 在用  2 停用  3 闲置
     */
    @Excel(name = "status_")
    private String status;

    /**
     * 电表用途
     */
    @Excel(name = "usage_")
    private String usageCopy;

    /**
     * 局站编码
     */
    @Excel(name = "station_code")
    private String stationCode;

    /**
     * 局站名称
     */
    @Excel(name = "station_name")
    private String stationName;

    /**
     * 局站地址
     */
    @Excel(name = "station_location")
    private String stationLocation;

    /**
     * 局站状态
     */
    @Excel(name = "station_status")
    private String stationStatus;

    /**
     * 局站类型
     */
    @Excel(name = "station_type")
    private String stationType;

    /**
     * 大工业标识 1 大工业用电，0 非大工业用电
     */
    @Excel(name = "large_lndustrial_electricity_flag")
    private String largeIndustrialElectricityFlag;

    /**
     * 对外结算类型(供电方式)	必填	供电方式 1：直供 2：转供 3：外租
     */
    @Excel(name = "energy_supply_way")
    private String energySupplyWay;

    /**
     * 电网公司电表编码
     */
    @Excel(name = "power_grid_energy_meter_code")
    private String powerGridEnergyMeterCode;

    @Excel(name = "site_code")
    private String siteCode;

    /**
     * 创建时间
     */
    @Excel(name = "create_time")
    private String createTime;
}
