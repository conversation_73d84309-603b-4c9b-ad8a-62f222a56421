package com.sccl.modules.business.noderesult.service;

import com.alibaba.fastjson.JSON;
import com.enrising.dcarbon.audit.AbstractRefereeContent;
import com.enrising.dcarbon.audit.AuditResult;
import com.enrising.dcarbon.audit.RefereeDatasource;
import com.enrising.dcarbon.audit.RefereeResult;
import com.sccl.common.lang.StringUtils;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.utils.RedisUtil;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.business.auditresult.mapper.AuditResultMapper;
import com.sccl.modules.business.noderesult.domain.*;
import com.sccl.modules.business.noderesult.mapper.NodeResultMapper;
import com.sccl.modules.business.noderesultstatistical.domain.NodeResultStatistical;
import com.sccl.modules.business.noderesultstatistical.mapper.NodeResultStatisticalMapper;
import com.sccl.modules.business.stationaudit.AuditVo;
import com.sccl.modules.business.stationaudit.msshistory.HistoryResult;
import com.sccl.modules.business.stationaudit.pavgpowertoolow.AvgPowerTooLowContent;
import com.sccl.modules.business.stationaudit.pcomparequtoa.quotaCompareContent;
import com.sccl.modules.business.stationaudit.pcontractprice.ContractExCreatorContent;
import com.sccl.modules.business.stationaudit.pstationaccountchange.StationAccountChangeRefereeContent;
import com.sccl.modules.business.stationaudit.pstationchangesamemeter.StationChangeSameMeterRefereeContent;
import com.sccl.modules.business.stationaudit.pstationgrade.StationGradeExRefereeContent;
import com.sccl.modules.business.stationaudit.pstationmeterchangesamecode.StationMeterChangeSameCodeRefereeContent;
import com.sccl.modules.business.stationaudit.pstationpowerchange.StationPowerChangeRefereeContent;
import com.sccl.modules.business.stationaudit.pstationprotocolexpired.StationProtocolExpiredRefereeContent;
import com.sccl.modules.business.stationaudit.pstationstop.StaionStopRefreeContent;
import com.sccl.modules.monitor.operlog.domain.OperLog;
import com.sccl.modules.monitor.operlog.mapper.OperLogMapper;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;
import com.sccl.modules.uniflow.common.WFModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * 基站一站式稽核结果 服务层实现
 *
 * <AUTHOR>
 * @date 2022-11-22
 */
@Service
@Slf4j
public class NodeResultServiceImpl extends BaseServiceImpl<NodeResult> implements INodeResultService {
    @Autowired
    private NodeResultMapper nodeResultMapper;

    @Autowired
    private AuditResultMapper auditResultMapper;
    @Autowired
    private OperLogMapper operLogMapper;
    @Autowired
    private NodeResultStatisticalMapper statisticalMapper;


    private BigDecimal wideMiddle;
    private BigDecimal wideAccountMiddle;

    public static void main(String[] args) {


    }

    @Override
    public AjaxResult getNodeResult(NodeResult nodeResult) {
        List<NodeResult> nodeResults =
          this.selectListLatestTime(nodeResult).stream().limit(50).collect(Collectors.toList());
        Map<Boolean, List<NodeResult>> map = nodeResults.stream().collect(
          Collectors.partitioningBy(
            nodeResult1 -> nodeResult1.getWide() != null
          )
        );

        Map<String, List<NodeResult>> map1 = map.get(true).stream().collect(
          Collectors.groupingBy(NodeResult::getEvaluate)
        );

        List<NodeResult> nodeEx = map.get(false);
        map1.put("异常站址", nodeEx);

        return AjaxResult.success(map1);

    }

    @Override
    public List<NodeResult> selectListLatestTime(NodeResult nodeResult) {
        return nodeResultMapper.selectListLatestTime(nodeResult);
    }

    @Override
    public int insertListResults(List<RefereeResult> refereeResults) {
        log.info("准备将责任链稽核结果插入数据库");
        if (refereeResults == null || refereeResults.size() == 0) {
            log.info("该责任链没有评判结果");
            return 0;
        }
        List<HistoryResult> historyResults = refereeResults.stream().map(
          refereeResult -> {
              return ((HistoryResult) refereeResult);
          }
        ).collect(Collectors.toList());
        //合并流
        List<RefereeDatasource> collect = historyResults.stream().flatMap(
          historyResult -> {
              return historyResult.getList().stream();
          }
        ).collect(Collectors.toList());

        //构造默认数据
        Date auditTime = new Date();
        List<AuditResult> auditResults = collect.stream().filter(s -> s != null).map(
          refereeDatasource -> {
              AuditResult auditResult = (AuditResult) refereeDatasource;
              auditResult.setCreateTime(auditTime);
              return auditResult;
          }
        ).collect(Collectors.toList());
        //插入
        //将统计表中 同一报账单 的 明细数据逻辑删除
        List<String> auditKeys =
          auditResults.stream().map(auditResult -> auditResult.getAuditKey()).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(auditKeys)) {
            return 0;
        }
        int n0 = auditResultMapper.deleteByAuditkeys(auditKeys);
        log.info("逻辑删除 稽核明细表数据{}条,audit_key为{}", n0, auditKeys);

        //插入
        auditResults = auditResults.stream().distinct().collect(Collectors.toList());
        log.info("准备向power_audit_result插入{}条数据", auditResults.size());
        int n = 0;
        try {
            n = auditResultMapper.insertList(auditResults);
        } catch (Exception e) {
            log.info("power_audit_result插入失败，异常为:{}", e.getMessage());
        }
        log.info("插入power_audit_result 成功");
        auditResultMapper.updateOther();
        log.info(
          n == auditResults.size() ? "插入 power_audit_result数据库成功，条数为" + n
            : "插入数据库 power_audit_result成功，条数为" + n + "应该为" + auditResults.size()
        );
        return n;
    }

    @Override
    public AjaxResult collectPowerChange(NodeResult nodeResult) {
        //去node result表 找 台账数据 如果有多条，找最新的
        NodeResult powerNode = this.selectupToDateNode(nodeResult);

        //去找 本次台账 同一站址的 上次台账
        NodeResult powerNodeLast = this.selectNodeLast(powerNode);

        //汇总
        CollectPowerChange change = new CollectPowerChange();
        change.setPowerNow(nodeResult);

        //判断 powerNodeLasst==null
        if (powerNodeLast == null) {
            change.setMsg("当前台账无 上次台账信息");
            return AjaxResult.success(change);
        }
        change.setPowerLast(powerNodeLast);

        BigDecimal powerNow = powerNode.getPower();
        BigDecimal powerLast = powerNodeLast.getPower();

        if (powerLast.compareTo(BigDecimal.ZERO) != 0) {
            BigDecimal divide = powerNow.subtract(powerLast).divide(powerLast, 2).multiply(BigDecimal.valueOf(100));
            change.setWidePower(divide);
            String msg = divide.compareTo(BigDecimal.ZERO) > 0 ?
              "本次台账 电量 相比上次上涨了" + divide
              : "本次台账 电量 相比上次下跌了了" + divide;

            change.setMsg(msg);
        }
        return AjaxResult.success(change);

    }

    @Override
    public NodeResult selectupToDateNode(NodeResult nodeResult) {
        return nodeResultMapper.selectupToDateNode(nodeResult);
    }

    @Override
    public NodeResult selectNodeLast(NodeResult powerNode) {
        return nodeResultMapper.selectNodeLast(powerNode);
    }

    @Override
    public AjaxResult collectNodeResult(MssAccountbill mssAccountbill) {
//        HashMap<String, Map<String, Integer>> resultMap = new HashMap<>();
        HashMap<String, Object> resultMap = new HashMap<>();

        NodeResultStatisticalShow statistical = new NodeResultStatisticalShow();
        Long billId = mssAccountbill.getId();
        //billId->list<nodeResult>
        NodeResult nodeResult = new NodeResult();
        nodeResult.setBillid(billId);
        List<NodeResult> nodeResults =
          this.selectListLatestTime(nodeResult).stream().collect(Collectors.toList());

        //1.站址分级评价
        Map<Boolean, List<NodeResult>> gradePo = nodeResults.stream().collect(
          Collectors.partitioningBy(
            nodeResult1 -> nodeResult1.getWide() != null
          )
        );


        Map<String, List<NodeResult>> gradePo2 = gradePo.get(true).stream().collect(
          Collectors.groupingBy(NodeResult::getEvaluate)
        );

        List<NodeResult> nodeEx = gradePo.get(false);
        gradePo2.put("异常站址", nodeEx);

        //将gradePo2 插入到 统计表中
        ArrayList<NodeResultStatistical> nodeResultStatisticals = new ArrayList<>();
        gradePo2.forEach(
          (k, v) -> {
              NodeResultStatistical nodeResultStatistical = new NodeResultStatistical();
              nodeResultStatistical.setBillid(billId);
              nodeResultStatistical.setNodeType(1);
              nodeResultStatistical.setLevel(k);
              //todo: content 只存明细id
//                  String content = JSON.toJSONString(v);
              List<Long> list = v.stream().map(
                nodeResult1 -> {
                    return nodeResult1.getId();
                }
              ).collect(Collectors.toList());
              String content = JSON.toJSONString(list);
              nodeResultStatistical.setContent(content);
              nodeResultStatisticals.add(nodeResultStatistical);
          }
        );


        HashMap<String, Integer> gradeVo = new HashMap<>();
        //组装gradeVo
        for (String s : gradePo2.keySet()) {
            gradeVo.put(s, gradePo2.get(s).size());
        }
        //1.1将站址分级评价 加入 resultmap
        statistical.setTitle(String.format("当前报账单的一站式稽核结果已出"));

        resultMap.put("站址分级异常", gradeVo);

        //2 电量波动
        //2.1按波动比例分组
        //2.1.1 计算分组 数量的最大值  中位数

        //最大值
        Map<BigDecimal, Long> changePoMap = nodeResults.stream().collect(
          Collectors.groupingBy(
            nodeResult1 -> {
                BigDecimal widepower = nodeResult1.getWidepower();
                if (widepower == null) {
                    return ExceptionWide.equNUll.getEx1();
                }
                return widepower;
            }
            ,
            Collectors.counting()
          )
        );
        ArrayList<Map.Entry<BigDecimal, Long>> changePos = new ArrayList<>(changePoMap.entrySet());
        changePos.sort(
          (o1, o2) -> {
              return Math.toIntExact(o2.getValue() - o1.getValue());
          }
        );
        BigDecimal wideMax = changePos.get(0).getKey();
        Long valueMax = changePos.get(0).getValue();
        log.info("当前 台账列表电量偏离比例的数量最大值及对应的偏离百分比为{}:{} ", valueMax, wideMax);
        changePos.remove(0);

        //中位数
        long valueMiddle;
        List<Long> count = changePos.stream().map(Map.Entry::getValue).collect(Collectors.toList());
        if (count.size() % 2 == 0) {
            valueMiddle = (count.get(count.size() / 2 - 1) + count.get(count.size() / 2)) / 2;
            log.info("中位数为 {}", valueMiddle);
        } else {
            valueMiddle = count.get(count.size() / 2);
            log.info("中位数为 {}", valueMiddle);
        }

        for (Map.Entry<BigDecimal, Long> entry : changePos) {
            if (entry.getValue() == valueMiddle) {
                wideMiddle = entry.getKey();
                break;
            }
        }
        log.info("当前 台账列表电量偏离比例的数量中位数及对应的偏离百分比为{}:{} ", valueMiddle, wideMiddle);

        Map<String, List<NodeResult>> powerChangePo = nodeResults.stream().collect(
          Collectors.groupingBy(nodeResult1 -> {
                                    BigDecimal power = nodeResult1.getPower();
                                    BigDecimal power_compare = nodeResult1.getPower_compare();

                                    if (power_compare == null || power_compare.compareTo(BigDecimal.ZERO) == 0) {
                                        return "当前台账 无上次台账信息";
                                    }
                                    BigDecimal widepower = nodeResult1.getWidepower();

                                    BigDecimal widepower1;
                                    BigDecimal widepower2;
                                    if (wideMiddle.compareTo(BigDecimal.ZERO) <= 0) {
                                        widepower2 = wideMiddle;
                                        widepower1 = wideMiddle.multiply(BigDecimal.valueOf(-1));
                                    } else {
                                        widepower1 = wideMiddle;
                                        widepower2 = wideMiddle.multiply(BigDecimal.valueOf(-1));
                                    }
                                    // -1 1
                                    return widepower.compareTo(wideMax) == 0 ?
                                      String.format("电量波动单个比例占比最多的是%d%%", wideMax.intValue()) :
                                      widepower.compareTo(widepower2) <= 0 ?
                                        String.format("电量波动<=%d%%", widepower2.intValue()) :
                                        widepower.compareTo(widepower1) <= 0 ?
                                          String.format(
                                            "电量波动在%d%%%d%%之间",
                                            widepower2.intValue(),
                                            widepower1.intValue()
                                          )
                                          :
                                          String.format("电量波动>=%d%%", widepower1.intValue());


                                }
          ));
        //2.2组装powerChangeVo
        HashMap<String, Integer> powerChangeVo = new HashMap<>();
        powerChangePo.forEach(
          (k, v) -> {
              powerChangeVo.put(k, v.size());
          }
        );
        //2.3 电量波动异常放入resultMap
        resultMap.put("电量波动异常", powerChangeVo);
        //2.4将结果放入到 统计表
        powerChangePo.forEach(
          (k, v) -> {
              NodeResultStatistical nodeResultStatistical = new NodeResultStatistical();
              nodeResultStatistical.setBillid(billId);
              nodeResultStatistical.setNodeType(2);
              nodeResultStatistical.setLevel(k);
//                    String content = JSON.toJSONString(v);
              List<Long> list = v.stream().map(
                nodeResult1 -> {
                    return nodeResult1.getId();
                }
              ).collect(Collectors.toList());
              String content = JSON.toJSONString(list);
              nodeResultStatistical.setContent(content);
              nodeResultStatisticals.add(nodeResultStatistical);
          }
        );


        //3 电费波动
        //3.1按波动比例分组
        //3.1.1 计算分组 数量的最大值  中位数

        //最大值
        Map<BigDecimal, Long> moneyPoMap = nodeResults.stream().collect(
          Collectors.groupingBy(
            nodeResult1 -> {
                BigDecimal wideaccount = nodeResult1.getWideaccount();
                if (wideaccount == null) {
                    return ExceptionWide.equNUll.getEx1();
                }
                return wideaccount;
            }
            ,
            Collectors.counting()
          )
        );
        ArrayList<Map.Entry<BigDecimal, Long>> moneyPos = new ArrayList<>(moneyPoMap.entrySet());
        moneyPos.sort(
          (o1, o2) -> {
              return Math.toIntExact(o2.getValue() - o1.getValue());
          }
        );
        BigDecimal wideAccountMax = moneyPos.get(0).getKey();
        Long valueAccountMax = moneyPos.get(0).getValue();
        log.info("当前 台账列表电费偏离比例的数量最大值及对应的偏离百分比为{}:{} ", valueAccountMax, wideAccountMax);
        moneyPos.remove(0);

        //中位数
        long valueAccountMiddle;
        List<Long> countAccount = changePos.stream().map(Map.Entry::getValue).collect(Collectors.toList());
        if (countAccount.size() % 2 == 0) {
            valueAccountMiddle =
              (countAccount.get(countAccount.size() / 2 - 1) + countAccount.get(countAccount.size() / 2)) / 2;
            log.info("中位数为 {}", valueAccountMiddle);
        } else {
            valueAccountMiddle = countAccount.get(countAccount.size() / 2);
            log.info("中位数为 {}", valueAccountMiddle);
        }

        for (Map.Entry<BigDecimal, Long> entry : moneyPos) {
            if (entry.getValue() == valueAccountMiddle) {
                wideAccountMiddle = entry.getKey();
                break;
            }
        }
        log.info("当前 台账列表电费偏离比例的数量中位数及对应的偏离百分比为{}:{} ", valueAccountMiddle, wideAccountMiddle);

        Map<String, List<NodeResult>> moneyChangePo = nodeResults.stream().collect(
          Collectors.groupingBy(nodeResult1 -> {
                                    BigDecimal accountmoney = nodeResult1.getAccountmoney();
                                    BigDecimal accountmoney_compare = nodeResult1.getAccountmoney_compare();

                                    if (accountmoney_compare == null || accountmoney_compare.compareTo(BigDecimal.ZERO) == 0) {
                                        return "当前台账 无上次台账信息";
                                    }
                                    BigDecimal wideaccount = nodeResult1.getWideaccount();

                                    BigDecimal wideaccount1;
                                    BigDecimal wideaccount2;
                                    if (wideAccountMiddle.compareTo(BigDecimal.ZERO) <= 0) {
                                        wideaccount2 = wideAccountMiddle;
                                        wideaccount1 = wideAccountMiddle.multiply(BigDecimal.valueOf(-1));
                                    } else {
                                        wideaccount1 = wideAccountMiddle;
                                        wideaccount2 = wideAccountMiddle.multiply(BigDecimal.valueOf(-1));
                                    }
                                    // -1 1
                                    return wideaccount.compareTo(wideAccountMax) == 0 ?
                                      String.format("电费波动单个比例占比最多的是%d%%", wideAccountMax.intValue()) :
                                      wideaccount.compareTo(wideaccount2) <= 0 ?
                                        String.format("电费波动<=%d%%", wideaccount2.intValue()) :
                                        wideaccount.compareTo(wideaccount1) <= 0 ?
                                          String.format(
                                            "电费波动在%d%%%d%%之间",
                                            wideaccount2.intValue(),
                                            wideaccount1.intValue()
                                          )
                                          :
                                          String.format("电费波动>=%d%%", wideaccount1.intValue());


                                }
          ));
        //3.2组装accountChangeVo
        HashMap<String, Integer> moneyChangeVo = new HashMap<>();
        moneyChangePo.forEach(
          (k, v) -> {
              moneyChangeVo.put(k, v.size());
          }
        );
        //3.3 电费波动异常放入resultMap
        resultMap.put("电费波动异常", moneyChangeVo);
        //3.4将结果放入到 统计表
        moneyChangePo.forEach(
          (k, v) -> {
              NodeResultStatistical nodeResultStatistical = new NodeResultStatistical();
              nodeResultStatistical.setBillid(billId);
              nodeResultStatistical.setNodeType(3);
              nodeResultStatistical.setLevel(k);
//                    String content = JSON.toJSONString(v);
              List<Long> list = v.stream().map(
                nodeResult1 -> {
                    return nodeResult1.getId();
                }
              ).collect(Collectors.toList());
              String content = JSON.toJSONString(list);
              nodeResultStatistical.setContent(content);
              nodeResultStatisticals.add(nodeResultStatistical);
          }
        );
        //4 站址编码缺失
        //4.1 组建stationCodePoMap
        Map<String, List<NodeResult>> stationCodePoMap = nodeResults.stream().collect(
          Collectors.groupingBy(
            nodeResult1 -> {
                if (nodeResult1.getStationcode() == null) {
                    return "站址编码缺失";
                }
                return "站址编码正常";
            }
          )
        );
        stationCodePoMap.remove("站址编码正常");
        //4.2 组健stationMapVo
        HashMap<String, Integer> stationCodeVoMap = new HashMap<>();
        stationCodePoMap.forEach(
          (k, v) -> {
              stationCodeVoMap.put(k, v.size());
          }
        );
        //4.3 站址编码异常放入 resultMap
        if (stationCodeVoMap.size() == 0) {
            resultMap.put("站址编码缺失", "无");
        } else {
            resultMap.put("站址编码缺失", stationCodeVoMap);
        }
        //4.4将结果放入到 统计表
        stationCodePoMap.forEach(
          (k, v) -> {
              NodeResultStatistical nodeResultStatistical = new NodeResultStatistical();
              nodeResultStatistical.setBillid(billId);
              nodeResultStatistical.setNodeType(4);
              nodeResultStatistical.setLevel(k);
//                    String content = JSON.toJSONString(v);
              List<Long> list = v.stream().map(
                nodeResult1 -> {
                    return nodeResult1.getId();
                }
              ).collect(Collectors.toList());
              String content = JSON.toJSONString(list);
              nodeResultStatistical.setContent(content);
              nodeResultStatisticals.add(nodeResultStatistical);
          }
        );

        //5 站址合同过期
        //5.1 组建 protocolExpiredPoMap
        Map<String, List<NodeResult>> protocolExpiredPoMap = nodeResults.stream().collect(
          Collectors.groupingBy(
            nodeResult1 -> {
                if (nodeResult1.getProtocolsigneddate() == null) {
                    return "协议签订时间缺失";
                } else if (nodeResult1.getProtocolterminatedate() == null) {
                    return "协议终止时间缺失";
                } else if (LocalDateTime.now().compareTo(nodeResult1.getProtocolsigneddate()) < 0) {
                    return "协议还未开始";
                } else if (LocalDateTime.now().compareTo(nodeResult1.getProtocolterminatedate()) > 0) {
                    return "协议已过期";
                } else {
                    return "协议正常期限";
                }
            }
          )
        );
        //5.2 组建 protocolExpiredVoMap
        HashMap<String, Integer> protocolExpiredVoMap = new HashMap<>();
        protocolExpiredPoMap.forEach(
          (k, v) -> {
              protocolExpiredVoMap.put(k, v.size());
          }
        );
        //5.3 协议异常放入 resultMap
        resultMap.put("电表协议异常", protocolExpiredVoMap);
        //5.4将结果放入到 统计表
        protocolExpiredPoMap.forEach(
          (k, v) -> {
              NodeResultStatistical nodeResultStatistical = new NodeResultStatistical();
              nodeResultStatistical.setBillid(billId);
              nodeResultStatistical.setNodeType(5);
              nodeResultStatistical.setLevel(k);
//                    String content = JSON.toJSONString(v);
              List<Long> list = v.stream().map(
                nodeResult1 -> {
                    return nodeResult1.getId();
                }
              ).collect(Collectors.toList());
              String content = JSON.toJSONString(list);
              nodeResultStatistical.setContent(content);
              nodeResultStatisticals.add(nodeResultStatistical);
          }
        );

        //6 电表 对应的 站址变动
        //6.1 组建 codeChangeSameMeterPoMap
        Map<String, List<NodeResult>> codeChangeSameMeterPoMap = nodeResults.stream().collect(
          Collectors.groupingBy(
            nodeResult1 -> {
                String stationcode = nodeResult1.getStationcode();
                String stationcodelast_samemeter = nodeResult1.getStationcodelast_samemeter();
                if (stationcodelast_samemeter != null) {
                    return "电表站址变动";
                } else {
                    return "电表站址无变动";
                }
            }
          )
        );
        codeChangeSameMeterPoMap.remove("电表站址无变动");
        //6.2 组建 codeChangeSameMeterVoMap
        HashMap<String, Integer> codeChangeSameMeterVoMap = new HashMap<>();
        codeChangeSameMeterPoMap.forEach(
          (k, v) -> {
              codeChangeSameMeterVoMap.put(k, v.size());
          }
        );
        //6.3 协议异常放入 resultMap
        resultMap.put("电表站址异常", codeChangeSameMeterVoMap);
        //6.4将结果放入到 统计表
        codeChangeSameMeterPoMap.forEach(
          (k, v) -> {
              NodeResultStatistical nodeResultStatistical = new NodeResultStatistical();
              nodeResultStatistical.setBillid(billId);
              nodeResultStatistical.setNodeType(6);
              nodeResultStatistical.setLevel(k);
//                    String content = JSON.toJSONString(v);
              List<Long> list = v.stream().map(
                nodeResult1 -> {
                    return nodeResult1.getId();
                }
              ).collect(Collectors.toList());
              String content = JSON.toJSONString(list);
              nodeResultStatistical.setContent(content);
              nodeResultStatisticals.add(nodeResultStatistical);
          }
        );

        //7 站址对应 电表变动
        //7.1 组建 meterChangeSameCodePoMap
        Map<String, List<NodeResult>> meterChangeSameCodePoMap = nodeResults.stream().collect(
          Collectors.groupingBy(
            nodeResult1 -> {
                String stationcode = nodeResult1.getStationcode();
                String meterlast_samestationcode = nodeResult1.getMeterlast_samestationcode();
                if (meterlast_samestationcode != null) {
                    return "站址电表变动";
                } else {
                    return "站址电表无变动";
                }
            }
          )
        );
        //7.2 组建 meterChangeSameCodeVoMap
        meterChangeSameCodePoMap.remove("站址电表无变动");
        HashMap<String, Integer> meterChangeSameCodeVoMap = new HashMap<>();
        meterChangeSameCodePoMap.forEach(
          (k, v) -> {
              meterChangeSameCodeVoMap.put(k, v.size());
          }
        );
        //7.3 协议异常放入 resultMap
        resultMap.put("站址电表异常", meterChangeSameCodeVoMap);
        //7.4将结果放入到 统计表
        meterChangeSameCodePoMap.forEach(
          (k, v) -> {
              NodeResultStatistical nodeResultStatistical = new NodeResultStatistical();
              nodeResultStatistical.setBillid(billId);
              nodeResultStatistical.setNodeType(7);
              nodeResultStatistical.setLevel(k);
//                    String content = JSON.toJSONString(v);
              List<Long> list = v.stream().map(
                nodeResult1 -> {
                    return nodeResult1.getId();
                }
              ).collect(Collectors.toList());
              String content = JSON.toJSONString(list);
              nodeResultStatistical.setContent(content);
              nodeResultStatisticals.add(nodeResultStatistical);
          }
        );


        //插入统计表
        //将统计表 同一报账单的统计 逻辑删除
        int n0 = statisticalMapper.deleteByBillId(billId);
        log.info("逻辑删除 统计表{}条", n0);

        int n = statisticalMapper.insertList(nodeResultStatisticals);
        log.info("插入 统计结果数据{}条", n);
        statistical.setResultMap(resultMap);
        return AjaxResult.success(statistical);
    }

    @Override
    public List<NodeResult> get(List<Long> ids) {
        return nodeResultMapper.getIds(ids);
    }

    @Override
    public AjaxResult collectNodeResultNew(MssAccountbill mssAccountbill) {
        log.info("进入billid={}的稽核汇总", mssAccountbill.getId());
        //结果 map
        HashMap<String, Object> resultMap = new HashMap<>();
        //结果List
        ArrayList<AuditVo> results = new ArrayList<>();
        //统计表
        ArrayList<NodeResultStatistical> nodeResultStatisticals = new ArrayList<>();
        //获取 稽核结果表 属于同一报账单 所有明细
        Long billId = mssAccountbill.getId();
        List<AuditResult> auditResults = auditResultMapper.selectListByBillId(billId);

        auditResults = auditResults.stream().filter(Objects::nonNull).collect(Collectors.toList());


        //1 站址编码缺失分组
        stationEmptyGroup(resultMap, nodeResultStatisticals, billId, auditResults);
        //2 站址分级分组
        stationGradeGroup(resultMap, nodeResultStatisticals, billId, auditResults);
        //3 电量波动分组
        ArrayList<Map.Entry<BigDecimal, Long>> changePos = stationPowerGroup(resultMap, nodeResultStatisticals,
                                                                             billId, auditResults
        );
        //4 电费波动分组
        stationAccountGroup(resultMap, nodeResultStatisticals, billId, auditResults);
        //5 电表协议分组
        stationProtocolExpiredGroup(resultMap, nodeResultStatisticals, billId, auditResults);
        //6 电表 站址变化分组
        stationCodeChangeGroup(resultMap, nodeResultStatisticals, billId, auditResults);
        //7 站址 电表变化分组
        stationMeterChangeGroup(resultMap, nodeResultStatisticals, billId, auditResults);
        //8 起租单 停租判定
        stationStopGrop(resultMap, nodeResultStatisticals, billId, auditResults);
        //9 网管 日均电量比对判定
        quotaCompareGrop(resultMap, nodeResultStatisticals, billId, auditResults);
        //10 日均耗电量过低 判定
        avgPowerToolLowGrop(resultMap, nodeResultStatisticals, billId, auditResults);
        //11转供电电价 判定
        ContractPriceGrop(resultMap, nodeResultStatisticals, billId, auditResults);
        log.info("billid={}的稽核汇总完毕", mssAccountbill.getId());

        //插入统计表
        //将统计表 同一报账单的统计 逻辑删除
        int n0 = statisticalMapper.deleteByBillId(billId);
        log.info("逻辑删除 统计表{}条", n0);

        int size = nodeResultStatisticals.size();

        int n = 0;
        if (size != 0) {
            n = statisticalMapper.insertList(nodeResultStatisticals);
        }
        log.info("插入 统计结果数据{}条", n);

        //返回结果
        return AjaxResult.success(resultMap);
    }

    @Override
    public AjaxResult collectNodeResultNew2(MssAccountbill mssAccountbill) {
        log.info("进入billid={}的稽核汇总", mssAccountbill.getId());
        //结果 map
        HashMap<String, Object> resultMap = new HashMap<>();
        //结果List
        ArrayList<AuditVo> results = new ArrayList<>();
        //统计表
        ArrayList<NodeResultStatistical> nodeResultStatisticals = new ArrayList<>();
        //获取 稽核结果表 属于同一报账单 所有明细
        Long billId = mssAccountbill.getId();
        List<AuditResult> auditResults = auditResultMapper.selectListByBillId(billId);

        auditResults = auditResults.stream().filter(Objects::nonNull).collect(Collectors.toList());


        //1 站址编码缺失分组
        stationEmptyGroup(resultMap, nodeResultStatisticals, billId, auditResults);
        //2 站址分级分组
        stationGradeGroup(resultMap, nodeResultStatisticals, billId, auditResults);
        //3 电量波动分组
        ArrayList<Map.Entry<BigDecimal, Long>> changePos = stationPowerGroup(resultMap, nodeResultStatisticals,
                                                                             billId, auditResults
        );
        //4 电费波动分组
        stationAccountGroup(resultMap, nodeResultStatisticals, billId, auditResults);
        //5 电表协议分组
        stationProtocolExpiredGroup(resultMap, nodeResultStatisticals, billId, auditResults);
        //6 电表 站址变化分组
        stationCodeChangeGroup(resultMap, nodeResultStatisticals, billId, auditResults);
        //7 站址 电表变化分组
        stationMeterChangeGroup(resultMap, nodeResultStatisticals, billId, auditResults);
        //8 起租单 停租判定
        stationStopGrop(resultMap, nodeResultStatisticals, billId, auditResults);
        //9 网管 日均电量比对判定
        quotaCompareGrop(resultMap, nodeResultStatisticals, billId, auditResults);
        log.info("billid={}的稽核汇总完毕", mssAccountbill.getId());

        //插入统计表
        //将统计表 同一报账单的统计 逻辑删除
        int n0 = statisticalMapper.deleteByBillId(billId);
        log.info("逻辑删除 统计表{}条", n0);

        int size = nodeResultStatisticals.size();

        int n = 0;
        if (size != 0) {
            n = statisticalMapper.insertList(nodeResultStatisticals);
        }
        log.info("插入 统计结果数据{}条", n);

        //返回结果
        return AjaxResult.success(resultMap);
    }


    private void stationStopGrop(HashMap<String, Object> resultMap,
                                 ArrayList<NodeResultStatistical> nodeResultStatisticals, Long billId,
                                 List<AuditResult> auditResults) {
        //8 起租单失效判定
        //8.1 组建stationStopGropPoMap
        log.info("billid={}的起租单失效判定汇总", billId);

        Map<String, List<AuditResult>> stationStopGropPoMap = auditResults.stream().
          filter(
            auditResult -> {
                return auditResult.getStep() == 8;
            }
          ).collect(
          Collectors.groupingBy(
            auditResult -> {
                StaionStopRefreeContent content =
                  (StaionStopRefreeContent) AbstractRefereeContent.forAuditResultWithJson(auditResult);
                return content.getExmsg();
            }
          )
        );

        // null 截断
        if (stationStopGropPoMap.size() == 0) {
            resultMap.put("起租单失效判定", "无数据");
            return;
        }
        //8.2 stationStopGropVoMap
        stationStopGropPoMap.remove("起租单正常");
        HashMap<String, Object> stationStopGropVoMap = new HashMap<>();
        stationStopGropPoMap.forEach(
          (k, v) -> {
              stationStopGropVoMap.put(k, v.size());
              //设置redis
              StringJoiner joiner = new StringJoiner("_");
              joiner.add("node8").add(billId + "").add(k);
              String rediskey = joiner.toString();
              stationStopGropVoMap.put("node8_key_" + k, rediskey);
              RedisUtil.setObj(rediskey, v);
              RedisUtil.getRedisTemplate().expire(rediskey, 62L, TimeUnit.DAYS);
          }
        );
        //2.3 站址编码异常放入 resultMap
        if (stationStopGropVoMap.size() == 0) {
            resultMap.put("起租单失效判定", "无");
        } else {
            resultMap.put("起租单失效判定", stationStopGropVoMap);
        }
        //2.4将结果放入到 统计表
        stationStopGropPoMap.forEach(
          (k, v) -> {
              NodeResultStatistical nodeResultStatistical = new NodeResultStatistical();
              nodeResultStatistical.setBillid(billId);
              nodeResultStatistical.setNodeType(NodeType.StationStop.getCode());
              nodeResultStatistical.setLevel(k);
//                    String content = JSON.toJSONString(v);
              HashMap<String, Object> contentMap = new HashMap<>();
              List<Long> list = v.stream().map(
                auditResult -> {
                    return auditResult.getId();
                }
              ).collect(Collectors.toList());
              int size = list.size();
              contentMap.put("ids", list);
              contentMap.put("size", size);
              String content = JSON.toJSONString(contentMap);
              nodeResultStatistical.setContent(content);
              nodeResultStatisticals.add(nodeResultStatistical);
          }
        );
        log.info("billid={}的起租单失效判定汇总完毕", billId);
    }

    private void quotaCompareGrop(HashMap<String, Object> resultMap,
                                  ArrayList<NodeResultStatistical> nodeResultStatisticals, Long billId,
                                  List<AuditResult> auditResults) {
        //9 网管日均电量比对汇总
        //9.1 组建stationStopGropPoMap
        log.info("billid={}的网管日均电量比对汇总", billId);

        Map<String, List<AuditResult>> quotaCompareGrop = auditResults.stream().
          filter(
            auditResult -> {
                return auditResult.getStep() == 9;
            }
          ).filter(
          auditResult -> {
              quotaCompareContent content =
                (quotaCompareContent) AbstractRefereeContent.forAuditResultWithJson(auditResult);
              return content.getExmsg() != null;
          }
        )
          .collect(
            Collectors.groupingBy(
              auditResult -> {
                  quotaCompareContent content =
                    (quotaCompareContent) AbstractRefereeContent.forAuditResultWithJson(auditResult);
                  return content.getExmsg();
              }
            )
          );
        log.info("网管日均电量quotaCompareGrop构建正常");
        // null 截断
        if (quotaCompareGrop.size() == 0) {
            log.info("网管日均电量比对汇总完毕");
            resultMap.put("网管日均电量比对", "无数据");
            return;
        }
        //8.2 stationStopGropVoMap
        HashMap<String, Object> quotaCompareGropVoMap = new HashMap<>();
        quotaCompareGrop.forEach(
          (k, v) -> {
              quotaCompareGropVoMap.put(k, v.size());
              //设置redis
              StringJoiner joiner = new StringJoiner("_");
              joiner.add("node9").add(billId + "").add(k);
              String rediskey = joiner.toString();
              quotaCompareGropVoMap.put("node9_key_" + k, rediskey);
              RedisUtil.setObj(rediskey, v);
              RedisUtil.getRedisTemplate().expire(rediskey, 62L, TimeUnit.DAYS);
          }
        );
        //2.3 站址编码异常放入 resultMap
        if (quotaCompareGropVoMap.size() == 0) {
            resultMap.put("网管日均电量比对", "无");
        } else {
            resultMap.put("网管日均电量比对", quotaCompareGropVoMap);
        }
        //2.4将结果放入到 统计表
        quotaCompareGrop.forEach(
          (k, v) -> {
              NodeResultStatistical nodeResultStatistical = new NodeResultStatistical();
              nodeResultStatistical.setBillid(billId);
              nodeResultStatistical.setNodeType(NodeType.QuotaCompare.getCode());
              nodeResultStatistical.setLevel(k);
//                    String content = JSON.toJSONString(v);
              HashMap<String, Object> contentMap = new HashMap<>();
              List<Long> list = v.stream().map(
                auditResult -> {
                    return auditResult.getId();
                }
              ).collect(Collectors.toList());
              int size = list.size();
              contentMap.put("ids", list);
              contentMap.put("size", size);
              String content = JSON.toJSONString(contentMap);
              nodeResultStatistical.setContent(content);
              nodeResultStatisticals.add(nodeResultStatistical);
          }
        );
        log.info("billid={}的网管日均电量比对汇总完毕", billId);
    }

    private void avgPowerToolLowGrop(HashMap<String, Object> resultMap,
                                     ArrayList<NodeResultStatistical> nodeResultStatisticals, Long billId,
                                     List<AuditResult> auditResults) {
        //9 网管日均电量比对汇总
        //9.1 组建stationStopGropPoMap
        log.info("billid={}的日均耗电量过低汇总", billId);

        Map<String, List<AuditResult>> avgPowerGrop = auditResults.stream().
          filter(
            auditResult -> {
                return auditResult.getStep() == 10;
            }
          ).filter(
          auditResult -> {
              AvgPowerTooLowContent content =
                (AvgPowerTooLowContent) AbstractRefereeContent.forAuditResultWithJson(auditResult);
              return content.getExmsg() != null;
          }
        )
          .collect(
            Collectors.groupingBy(
              auditResult -> {
                  AvgPowerTooLowContent content =
                    (AvgPowerTooLowContent) AbstractRefereeContent.forAuditResultWithJson(auditResult);
                  return content.getExmsg();
              }
            )
          );
        log.info("日均耗电量过低avgPowerGrop构建正常");
        // null 截断
        if (avgPowerGrop.size() == 0) {
            log.info("日均耗电量过低汇总完毕");
            resultMap.put("日均耗电量过低", "无数据");
            return;
        }
        //8.2 stationStopGropVoMap
        HashMap<String, Object> avgPowerGropVoMap = new HashMap<>();
        avgPowerGrop.forEach(
          (k, v) -> {
              avgPowerGropVoMap.put(k, v.size());
              //设置redis
              StringJoiner joiner = new StringJoiner("_");
              joiner.add("node10").add(billId + "").add(k);
              String rediskey = joiner.toString();
              avgPowerGropVoMap.put("node10_key_" + k, rediskey);
              RedisUtil.setObj(rediskey, v);
              RedisUtil.getRedisTemplate().expire(rediskey, 62L, TimeUnit.DAYS);
          }
        );
        //2.3 站址编码异常放入 resultMap
        if (avgPowerGropVoMap.size() == 0) {
            resultMap.put("日均耗电量过低", "无");
        } else {
            resultMap.put("日均耗电量过低", avgPowerGropVoMap);
        }
        //2.4将结果放入到 统计表
        avgPowerGrop.forEach(
          (k, v) -> {
              NodeResultStatistical nodeResultStatistical = new NodeResultStatistical();
              nodeResultStatistical.setBillid(billId);
              nodeResultStatistical.setNodeType(NodeType.QuotaCompare.getCode());
              nodeResultStatistical.setLevel(k);
//                    String content = JSON.toJSONString(v);
              HashMap<String, Object> contentMap = new HashMap<>();
              List<Long> list = v.stream().map(
                auditResult -> {
                    return auditResult.getId();
                }
              ).collect(Collectors.toList());
              int size = list.size();
              contentMap.put("ids", list);
              contentMap.put("size", size);
              String content = JSON.toJSONString(contentMap);
              nodeResultStatistical.setContent(content);
              nodeResultStatisticals.add(nodeResultStatistical);
          }
        );
        log.info("billid={}的日均耗电量过低汇总完毕", billId);
    }

    private void ContractPriceGrop(HashMap<String, Object> resultMap,
                                   ArrayList<NodeResultStatistical> nodeResultStatisticals, Long billId,
                                   List<AuditResult> auditResults) {
        //9 网管日均电量比对汇总
        //9.1 组建stationStopGropPoMap
        log.info("billid={}的转供电签约单价汇总", billId);

        Map<String, List<AuditResult>> avgPowerGrop = auditResults.stream().
          filter(
            auditResult -> {
                return auditResult.getStep() == 11;
            }
          ).filter(
          auditResult -> {
              ContractExCreatorContent content =
                (ContractExCreatorContent) AbstractRefereeContent.forAuditResultWithJson(auditResult);
              return (content.getExmg() != null) && (!content.getExmg().equals("对应电表单价,签约单价正常"));
          }
        )
          .collect(
            Collectors.groupingBy(
              auditResult -> {
                  ContractExCreatorContent content =
                    (ContractExCreatorContent) AbstractRefereeContent.forAuditResultWithJson(auditResult);
                  return content.getExmg();
              }
            )
          );
        log.info("转供电签约单价avgPowerGrop构建正常");
        // null 截断
        if (avgPowerGrop.size() == 0) {
            log.info("billid={}转供电签约单价汇总无数据", billId);
            log.info("转供电签约单价汇总完毕");
            resultMap.put("转供电签约单价", "无数据");
            return;
        }
        //8.2 stationStopGropVoMap
        HashMap<String, Object> avgPowerGropVoMap = new HashMap<>();
        avgPowerGrop.forEach(
          (k, v) -> {
              avgPowerGropVoMap.put(k, v.size());
              //设置redis
              StringJoiner joiner = new StringJoiner("_");
              joiner.add("node11").add(billId + "").add(k);
              String rediskey = joiner.toString();
              avgPowerGropVoMap.put("node11_key_" + k, rediskey);
              RedisUtil.setObj(rediskey, v);
              RedisUtil.getRedisTemplate().expire(rediskey, 62L, TimeUnit.DAYS);
          }
        );
        //2.3 站址编码异常放入 resultMap
        if (avgPowerGropVoMap.size() == 0) {
            resultMap.put("转供电签约单价", "无");
        } else {
            resultMap.put("转供电签约单价", avgPowerGropVoMap);
        }
        //2.4将结果放入到 统计表
        avgPowerGrop.forEach(
          (k, v) -> {
              NodeResultStatistical nodeResultStatistical = new NodeResultStatistical();
              nodeResultStatistical.setBillid(billId);
              nodeResultStatistical.setNodeType(NodeType.QuotaCompare.getCode());
              nodeResultStatistical.setLevel(k);
//                    String content = JSON.toJSONString(v);
              HashMap<String, Object> contentMap = new HashMap<>();
              List<Long> list = v.stream().map(
                auditResult -> {
                    return auditResult.getId();
                }
              ).collect(Collectors.toList());
              int size = list.size();
              contentMap.put("ids", list);
              contentMap.put("size", size);
              String content = JSON.toJSONString(contentMap);
              nodeResultStatistical.setContent(content);
              nodeResultStatisticals.add(nodeResultStatistical);
          }
        );
        log.info("billid={}的转供电签约单价汇总完毕", billId);
    }

    private void stationMeterChangeGroup(HashMap<String, Object> resultMap,
                                         ArrayList<NodeResultStatistical> nodeResultStatisticals, Long billId,
                                         List<AuditResult> auditResults) {
        //7 站址对应 电表变动
        //7.1 组建 meterChangeSameCodePoMap
        log.info("billid={}的站址 电表变化分组汇总", billId);
        Map<String, List<AuditResult>> meterChangeSameCodePoMap =
          auditResults.stream().filter(auditResult -> auditResult.getStep() == 7).collect(
            Collectors.groupingBy(
              auditResult -> {
                  StationMeterChangeSameCodeRefereeContent content =
                    (StationMeterChangeSameCodeRefereeContent) AbstractRefereeContent.forAuditResultWithJson(auditResult);
                  String meterlast_samestationcode = content.getMeterlast_samestationcode();
                  if (meterlast_samestationcode != null) {
                      return "站址电表变动";
                  } else {
                      return "站址电表无变动";
                  }
              }
            )
          );
        // null 截断
        if (meterChangeSameCodePoMap.size() == 0) {
            resultMap.put("站址电表异常", "无数据");
            return;
        }
        //7.2 组建 meterChangeSameCodeVoMap
        meterChangeSameCodePoMap.remove("站址电表无变动");
        HashMap<String, Object> meterChangeSameCodeVoMap = new HashMap<>();
        meterChangeSameCodePoMap.forEach(
          (k, v) -> {
              meterChangeSameCodeVoMap.put(k, v.size());
              //设置redis
              StringJoiner joiner = new StringJoiner("_");
              joiner.add("node7").add(billId + "").add(k);
              String rediskey = joiner.toString();
              meterChangeSameCodeVoMap.put("node7_key_" + k, rediskey);
              RedisUtil.setObj(rediskey, v);
              RedisUtil.getRedisTemplate().expire(rediskey, 62L, TimeUnit.DAYS);
          }
        );
        //7.3 协议异常放入 resultMap
        if (meterChangeSameCodeVoMap.size() == 0) {
            resultMap.put("站址电表异常", "无");
        } else {
            resultMap.put("站址电表异常", meterChangeSameCodeVoMap);
        }
        //7.4将结果放入到 统计表
        meterChangeSameCodePoMap.forEach(
          (k, v) -> {
              NodeResultStatistical nodeResultStatistical = new NodeResultStatistical();
              nodeResultStatistical.setBillid(billId);
              nodeResultStatistical.setNodeType(NodeType.StationMeterChangeSameCode.getCode());
              nodeResultStatistical.setLevel(k);
//                    String content = JSON.toJSONString(v);
              HashMap<String, Object> contentMap = new HashMap<>();
              List<Long> list = v.stream().map(
                auditResult -> {
                    return auditResult.getId();
                }
              ).collect(Collectors.toList());
              int size = list.size();
              contentMap.put("ids", list);
              contentMap.put("size", size);
              String content = JSON.toJSONString(contentMap);
              nodeResultStatistical.setContent(content);
              nodeResultStatisticals.add(nodeResultStatistical);
          }
        );
        log.info("billid={}的站址 电表变化分组汇总完毕", billId);
    }

    /**
     * 电表对应站址分组
     *
     * @param resultMap
     * @param nodeResultStatisticals
     * @param billId
     * @param auditResults
     */
    private void stationCodeChangeGroup(HashMap<String, Object> resultMap,
                                        ArrayList<NodeResultStatistical> nodeResultStatisticals, Long billId,
                                        List<AuditResult> auditResults) {
        //6 电表 对应的 站址变动
        //6.1 组建 codeChangeSameMeterPoMap
        log.info("billid={}的电表对应的站址变动汇总", billId);

        Map<String, List<AuditResult>> codeChangeSameMeterPoMap =
          auditResults.stream().filter(auditResult -> auditResult.getStep() == 6).collect(
            Collectors.groupingBy(
              auditResult -> {
                  StationChangeSameMeterRefereeContent content =
                    (StationChangeSameMeterRefereeContent) AbstractRefereeContent.forAuditResultWithJson(auditResult);
                  String stationcodelast_samemeter = content.getStationcodelast_samemeter();
                  if (stationcodelast_samemeter != null) {
                      return "电表站址变动";
                  } else {
                      return "电表站址无变动";
                  }
              }
            )
          );
        // null 截断
        if (codeChangeSameMeterPoMap.size() == 0) {
            resultMap.put("电表站址异常", "无数据");
            return;
        }
        codeChangeSameMeterPoMap.remove("电表站址无变动");
        //6.2 组建 codeChangeSameMeterVoMap
        HashMap<String, Object> codeChangeSameMeterVoMap = new HashMap<>();
        codeChangeSameMeterPoMap.forEach(
          (k, v) -> {
              codeChangeSameMeterVoMap.put(k, v.size());
              //设置redis
              StringJoiner joiner = new StringJoiner("_");
              joiner.add("node6").add(billId + "").add(k);
              String rediskey = joiner.toString();
              codeChangeSameMeterVoMap.put("node6_key_" + k, rediskey);
              RedisUtil.setObj(rediskey, v);
              RedisUtil.getRedisTemplate().expire(rediskey, 62L, TimeUnit.DAYS);
          }
        );
        //6.3 协议异常放入 resultMap
        if (codeChangeSameMeterVoMap.size() == 0) {
            resultMap.put("电表站址异常", "无");
        } else {
            resultMap.put("电表站址异常", codeChangeSameMeterVoMap);
        }
        //6.4将结果放入到 统计表
        codeChangeSameMeterPoMap.forEach(
          (k, v) -> {
              NodeResultStatistical nodeResultStatistical = new NodeResultStatistical();
              nodeResultStatistical.setBillid(billId);
              nodeResultStatistical.setNodeType(NodeType.StationChangeSameMeter.getCode());
              nodeResultStatistical.setLevel(k);
//                    String content = JSON.toJSONString(v);
              HashMap<String, Object> contentMap = new HashMap<>();
              List<Long> list = v.stream().map(
                auditResult -> {
                    return auditResult.getId();
                }
              ).collect(Collectors.toList());
              int size = list.size();
              contentMap.put("ids", list);
              contentMap.put("size", size);
              String content = JSON.toJSONString(contentMap);
              nodeResultStatistical.setContent(content);
              nodeResultStatisticals.add(nodeResultStatistical);
          }
        );
        log.info("billid={}的电表对应的站址变动汇总完毕", billId);
    }

    /**
     * 电表协议分组
     *
     * @param resultMap
     * @param nodeResultStatisticals
     * @param billId
     * @param auditResults
     */
    private void stationProtocolExpiredGroup(HashMap<String, Object> resultMap,
                                             ArrayList<NodeResultStatistical> nodeResultStatisticals, Long billId,
                                             List<AuditResult> auditResults) {
        //5 站址合同过期
        //5.1 组建 protocolExpiredPoMap
        log.info("billid={}的电表协议汇总", billId);

        Map<String, List<AuditResult>> protocolExpiredPoMap =
          auditResults.stream().filter(auditResult -> auditResult.getStep() == 5).collect(
            Collectors.groupingBy(
              auditResult -> {
                  StationProtocolExpiredRefereeContent content =
                    (StationProtocolExpiredRefereeContent) AbstractRefereeContent.forAuditResultWithJson(auditResult);
                  if (content.getProtocolsigneddate() == null) {
                      return "协议签订时间缺失";
                  } else if (content.getProtocolterminatedate() == null) {
                      return "协议终止时间缺失";
                  } else if (LocalDateTime.now().compareTo(content.getProtocolsigneddate()) < 0) {
                      return "协议还未开始";
                  } else if (LocalDateTime.now().compareTo(content.getProtocolterminatedate()) > 0) {
                      return "协议已过期";
                  } else {
                      return "协议正常期限";
                  }
              }
            )
          );
        // null 截断
        if (protocolExpiredPoMap.size() == 0) {
            resultMap.put("电表协议异常", "无数据");
            return;
        }

        log.info("去除正常数据");
        protocolExpiredPoMap.remove("协议正常期限");

        //5.2 组建 protocolExpiredVoMap
        HashMap<String, Object> protocolExpiredVoMap = new HashMap<>();
        protocolExpiredPoMap.forEach(
          (k, v) -> {
              protocolExpiredVoMap.put(k, v.size());
              //设置redis
              StringJoiner joiner = new StringJoiner("_");
              joiner.add("node5").add(billId + "").add(k);
              String rediskey = joiner.toString();
              protocolExpiredVoMap.put("node5_key_" + k, rediskey);
              RedisUtil.setObj(rediskey, v);
              RedisUtil.getRedisTemplate().expire(rediskey, 62L, TimeUnit.DAYS);
          }
        );
        //5.3 协议异常放入 resultMap
        if (protocolExpiredVoMap.size() == 0) {
            resultMap.put("电表协议异常", "无");
        } else {
            resultMap.put("电表协议异常", protocolExpiredVoMap);
        }
        //5.4将结果放入到 统计表
        protocolExpiredPoMap.forEach(
          (k, v) -> {
              NodeResultStatistical nodeResultStatistical = new NodeResultStatistical();
              nodeResultStatistical.setBillid(billId);
              nodeResultStatistical.setNodeType(NodeType.StationProtocolExpired.getCode());
              nodeResultStatistical.setLevel(k);
//                    String content = JSON.toJSONString(v);
              HashMap<String, Object> contentMap = new HashMap<>();
              List<Long> list = v.stream().map(
                auditResult -> {
                    return auditResult.getId();
                }
              ).collect(Collectors.toList());
              int size = list.size();
              contentMap.put("ids", list);
              contentMap.put("size", size);
              String content = JSON.toJSONString(contentMap);
              nodeResultStatistical.setContent(content);
              nodeResultStatisticals.add(nodeResultStatistical);
          }
        );
        log.info("billid={}的电表协议汇总完毕", billId);

    }

    /**
     * 电费波动分组
     *
     * @param resultMap
     * @param nodeResultStatisticals
     * @param billId
     * @param auditResults
     */
    private void stationAccountGroup(HashMap<String, Object> resultMap,
                                     ArrayList<NodeResultStatistical> nodeResultStatisticals, Long billId,
                                     List<AuditResult> auditResults) {
        //3 电费波动
        //3.1按波动比例分组
        //3.1.1 计算分组 数量的最大值  中位数

        //最大值
        //最大值
        log.info("billid={}的电费波动汇总", billId);
        Map<BigDecimal, Long> moneyPoMap = auditResults.stream()
          .filter(
            auditResult -> auditResult.getStep() == 4
          )
          .collect(
            Collectors.groupingBy(
              auditResult -> {
                  StationAccountChangeRefereeContent content =
                    (StationAccountChangeRefereeContent) AbstractRefereeContent.forAuditResultWithJson(auditResult);
                  BigDecimal wideaccount =
                    content.getWideaccount();
                  if (wideaccount == null) {
                      return ExceptionWide.equNUll.getEx1();
                  }
                  return wideaccount;
              }
              ,
              Collectors.counting()
            )
          );
        // null 截断
        if (moneyPoMap.size() == 0) {
            resultMap.put("电费波动异常", "无数据");
            return;
        }

        List<Map.Entry<BigDecimal, Long>> moneyPos = new ArrayList<>(moneyPoMap.entrySet());
        moneyPos = moneyPos.stream()
          .sorted(
            (o1, o2) -> {
                return Math.toIntExact(o2.getValue() - o1.getValue());
            }
          ).collect(Collectors.toList());
        BigDecimal wideAccountMax = moneyPos.get(0).getKey();
        Long valueAccountMax = moneyPos.get(0).getValue();
        log.info("当前 台账列表电费偏离比例的数量最大值及对应的偏离百分比为{}:{} ", valueAccountMax, wideAccountMax);
        moneyPos.remove(0);

        //中位数moneyPos
        long valueAccountMiddle;

        if (moneyPos.size() != 0) {
            List<Long> countAccount = moneyPos.stream().map(Map.Entry::getValue).collect(Collectors.toList());
            if (countAccount.size() % 2 == 0) {
                valueAccountMiddle =
                  (countAccount.get(countAccount.size() / 2 - 1) + countAccount.get(countAccount.size() / 2)) / 2;
                log.info("中位数为 {}", valueAccountMiddle);
            } else {
                valueAccountMiddle = countAccount.get(countAccount.size() / 2);
                log.info("中位数为 {}", valueAccountMiddle);
            }

            for (Map.Entry<BigDecimal, Long> entry : moneyPos) {
                if (entry.getValue() == valueAccountMiddle) {
                    wideAccountMiddle = entry.getKey();
                    break;
                }
            }
            log.info("当前 台账列表电费偏离比例的数量中位数及对应的偏离百分比为{}:{} ", valueAccountMiddle, wideAccountMiddle);
        } else {
            //只有一条记录，中位数和最大比例的重合
            wideAccountMiddle = wideAccountMax;
            valueAccountMiddle = valueAccountMax;
        }


        Map<String, List<AuditResult>> moneyChangePo =
          auditResults.stream().filter(auditResult -> auditResult.getStep() == 4)
            .collect(
              Collectors.groupingBy(auditResult -> {
                                        StationAccountChangeRefereeContent content =
                                          (StationAccountChangeRefereeContent) AbstractRefereeContent.forAuditResultWithJson(auditResult);
                                        BigDecimal accountmoney = content.getAccountmoney();
                                        BigDecimal accountmoney_compare =
                                          content.getAccountmoney_compare();
                                        if (accountmoney == null) {
                                            return "当前台账本次电费为null";
                                        }
                                        if (accountmoney_compare == null || accountmoney_compare.compareTo(BigDecimal.ZERO) == 0) {
                                            return "当前台账 " +
                                              "无上次台账信息";
                                        }
                                        BigDecimal wideaccount = content.getWideaccount();

                                        BigDecimal wideaccount1;
                                        BigDecimal wideaccount2;
                                        if (wideAccountMiddle.compareTo(BigDecimal.ZERO) <= 0) {
                                            wideaccount2 =
                                              wideAccountMiddle;
                                            wideaccount1 =
                                              wideAccountMiddle.multiply(BigDecimal.valueOf(-1));
                                        } else {
                                            wideaccount1 =
                                              wideAccountMiddle;
                                            wideaccount2 =
                                              wideAccountMiddle.multiply(BigDecimal.valueOf(-1));
                                        }
                                        // -1 1
                                        return wideaccount.compareTo(wideAccountMax) == 0 ?
                                          String.format(
                                            "电费波动单个比例占比最多的是%d%%",
                                            wideAccountMax.intValue()
                                          ) :
                                          wideaccount.compareTo(wideaccount2) <= 0 ?
                                            String.format(
                                              "电费波动<=%d%%",
                                              wideaccount2.intValue()
                                            ) :
                                            wideaccount.compareTo(wideaccount1) <= 0 ?
                                              String.format(
                                                "电费波动在%d%%%d%%之间",
                                                wideaccount2.intValue(),
                                                wideaccount1.intValue()
                                              )
                                              :
                                              String.format(
                                                "电费波动>=%d%%",
                                                wideaccount1.intValue()
                                              );


                                    }
              ));
        //3.2组装accountChangeVo
        HashMap<String, Object> moneyChangeVo = new HashMap<>();
        moneyChangePo.forEach(
          (k, v) -> {
              moneyChangeVo.put(k, v.size());
              //设置redis
              StringJoiner joiner = new StringJoiner("_");
              joiner.add("node4").add(billId + "").add(k);
              String rediskey = joiner.toString();
              moneyChangeVo.put("node4_key_" + k, rediskey);
              RedisUtil.setObj(rediskey, v);
              RedisUtil.getRedisTemplate().expire(rediskey, 62L, TimeUnit.DAYS);
          }
        );
        //3.3 电费波动异常放入resultMap
        if (moneyChangeVo.size() == 0) {
            resultMap.put("电费波动异常", "无");
        } else {
            resultMap.put("电费波动异常", moneyChangeVo);
        }
        //3.4将结果放入到 统计表
        moneyChangePo.forEach(
          (k, v) -> {
              NodeResultStatistical nodeResultStatistical = new NodeResultStatistical();
              nodeResultStatistical.setBillid(billId);
              nodeResultStatistical.setNodeType(NodeType.StationAccountChange.getCode());
              nodeResultStatistical.setLevel(k);
//                    String content = JSON.toJSONString(v);
              HashMap<String, Object> contentMap = new HashMap<>();
              List<Long> list = v.stream().map(
                auditResult -> {
                    return auditResult.getId();
                }
              ).collect(Collectors.toList());
              int size = list.size();
              contentMap.put("ids", list);
              contentMap.put("size", size);
              String content = JSON.toJSONString(contentMap);
              nodeResultStatistical.setContent(content);
              nodeResultStatisticals.add(nodeResultStatistical);
          }
        );
        log.info("billid={}的电量波动汇总完毕", billId);
    }

    /**
     * 站址分级 分组
     *
     * @param resultMap
     * @param nodeResultStatisticals
     * @param billId
     * @param auditResults
     */
    private void stationGradeGroup(HashMap<String, Object> resultMap,
                                   ArrayList<NodeResultStatistical> nodeResultStatisticals, Long billId,
                                   List<AuditResult> auditResults) {
        //2 站址分级
        //2.1 组建stationGradePoMap
        log.info("billid={}的站址分级汇总", billId);
        Map<String, List<AuditResult>> stationGradePoMap = auditResults.stream().
          filter(
            auditResult -> {
                return auditResult.getStep() == 2;
            }
          ).collect(
          Collectors.groupingBy(
            auditResult -> {
                StationGradeExRefereeContent content =
                  (StationGradeExRefereeContent) AbstractRefereeContent.forAuditResultWithJson(auditResult);
                if (content.getStationCode() == null || content.getWide() == null) {
                    return "异常站址";
                }
                return content.getEvaluate();
            }
          )
        );

        // null 截断
        if (stationGradePoMap.size() == 0) {
            resultMap.put("站址评级", "无数据");
            return;
        }
        //2.2 组建stationGradeVoMap
        HashMap<String, Object> stationGradeVoMap = new HashMap<>();
        stationGradePoMap.forEach(
          (k, v) -> {
              stationGradeVoMap.put(k, v.size());
              //设置redis
              StringJoiner joiner = new StringJoiner("_");
              joiner.add("node2").add(billId + "").add(k);
              String rediskey = joiner.toString();
              stationGradeVoMap.put("node2_key_" + k, rediskey);
              RedisUtil.setObj(rediskey, v);
              RedisUtil.getRedisTemplate().expire(rediskey, 62L, TimeUnit.DAYS);

          }
        );
        //2.3 站址编码异常放入 resultMap
        if (stationGradeVoMap.size() == 0) {
            resultMap.put("站址评级", "无");
        } else {
            resultMap.put("站址评级", stationGradeVoMap);
        }
        //2.4将结果放入到 统计表
        stationGradePoMap.forEach(
          (k, v) -> {
              NodeResultStatistical nodeResultStatistical = new NodeResultStatistical();
              nodeResultStatistical.setBillid(billId);
              nodeResultStatistical.setNodeType(NodeType.StaionGrade.getCode());
              nodeResultStatistical.setLevel(k);
//                    String content = JSON.toJSONString(v);
              HashMap<String, Object> contentMap = new HashMap<>();
              List<Long> list = v.stream().map(
                auditResult -> {
                    return auditResult.getId();
                }
              ).collect(Collectors.toList());
              int size = list.size();
              contentMap.put("ids", list);
              contentMap.put("size", size);
              String content = JSON.toJSONString(contentMap);
              nodeResultStatistical.setContent(content);
              nodeResultStatisticals.add(nodeResultStatistical);
          }
        );
        log.info("billid={}的站址编码分级汇总完毕", billId);
    }

    private void stationEmptyGroup(HashMap<String, Object> resultMap,
                                   ArrayList<NodeResultStatistical> nodeResultStatisticals, Long billId,
                                   List<AuditResult> auditResults) {
        //1 站址编码缺失
        //1.1 组建stationCodePoMap
        log.info("billid={}的站址编码缺失汇总", billId);
        Map<String, List<AuditResult>> stationCodePoMap = auditResults.stream().
          filter(
            auditResult -> {
                return auditResult.getStep() == 1;
            }
          ).collect(
          Collectors.groupingBy(
            auditResult -> {
                if (auditResult.getRefereeMessage() != null) {
                    return "站址编码缺失";
                } else {
                    return "站址编码存在";
                }
            }
          )
        );
        // null 截断
        if (stationCodePoMap.size() == 0) {
            resultMap.put("站址编码缺失", "无数据");
            return;
        }
        stationCodePoMap.remove("站址编码存在");
        //4.2 组健stationMapVo
        HashMap<String, Object> stationCodeVoMap = new HashMap<>();
        stationCodePoMap.forEach(
          (k, v) -> {
              stationCodeVoMap.put(k, v.size());
              //设置redis
              StringJoiner joiner = new StringJoiner("_");
              joiner.add("node1").add(billId + "").add(k);
              String rediskey = joiner.toString();
              stationCodeVoMap.put("node1_key_" + k, rediskey);
              RedisUtil.setObj(rediskey, v);
              RedisUtil.getRedisTemplate().expire(rediskey, 62L, TimeUnit.DAYS);
          }
        );
        //4.3 站址编码异常放入 resultMap
        if (stationCodeVoMap.size() == 0) {
            resultMap.put("站址编码缺失", "无");
        } else {
            resultMap.put("站址编码缺失", stationCodeVoMap);

        }
        //4.4将结果放入到 统计表
        stationCodePoMap.forEach(
          (k, v) -> {
              NodeResultStatistical nodeResultStatistical = new NodeResultStatistical();
              nodeResultStatistical.setBillid(billId);
              nodeResultStatistical.setNodeType(NodeType.StationEmpty.getCode());
              nodeResultStatistical.setLevel(k);
//                    String content = JSON.toJSONString(v);
              HashMap<String, Object> contentMap = new HashMap<>();
              List<Long> list = v.stream().map(
                auditResult -> {
                    return auditResult.getId();
                }
              ).collect(Collectors.toList());
              int size = list.size();
              contentMap.put("ids", list);
              contentMap.put("size", size);
              String content = JSON.toJSONString(contentMap);
              nodeResultStatistical.setContent(content);
              nodeResultStatisticals.add(nodeResultStatistical);
          }

        );
        log.info("billid={}的站址编码缺失汇总完毕", billId);
    }

    private ArrayList<Map.Entry<BigDecimal, Long>> stationPowerGroup(HashMap<String, Object> resultMap,
                                                                     ArrayList<NodeResultStatistical> nodeResultStatisticals, Long billId, List<AuditResult> auditResults) {
        log.info("billid={}的电量波动汇总", billId);
        //2 电量波动
        //2.1按波动比例分组
        //2.1.1 计算分组 数量的最大值  中位数

        //最大值
        Map<BigDecimal, Long> changePoMap = auditResults.stream()
          .filter(
            auditResult -> auditResult.getStep() == 3
          )
          .collect(
            Collectors.groupingBy(
              auditResult -> {
                  StationPowerChangeRefereeContent content =
                    (StationPowerChangeRefereeContent) AbstractRefereeContent.forAuditResultWithJson(auditResult);
                  BigDecimal widepower =
                    content.getWidepower();
                  if (widepower == null) {
                      return ExceptionWide.equNUll.getEx1();
                  }
                  return widepower;
              }
              ,
              Collectors.counting()
            )
          );
        //判空
        if (changePoMap.size() == 0) {
            resultMap.put("电量波动异常", "无数据");
            return null;
        }
        ArrayList<Map.Entry<BigDecimal, Long>> changePos = new ArrayList<>(changePoMap.entrySet());
        changePos.sort(
          (o1, o2) -> {
              return Math.toIntExact(o2.getValue() - o1.getValue());
          }
        );
        BigDecimal wideMax = changePos.get(0).getKey();
        Long valueMax = changePos.get(0).getValue();
        log.info("当前 台账列表电量偏离比例的数量最大值及对应的偏离百分比为{}:{} ", valueMax, wideMax);
        changePos.remove(0);

        //中位数
        long valueMiddle;


        if (changePos.size() != 0) {
            List<Long> count = changePos.stream().map(Map.Entry::getValue).collect(Collectors.toList());
            if (count.size() % 2 == 0) {
                valueMiddle = (count.get(count.size() / 2 - 1) + count.get(count.size() / 2)) / 2;
                log.info("中位数为 {}", valueMiddle);
            } else {
                valueMiddle = count.get(count.size() / 2);
                log.info("中位数为 {}", valueMiddle);
            }

            for (Map.Entry<BigDecimal, Long> entry : changePos) {
                if (entry.getValue() == valueMiddle) {
                    wideMiddle = entry.getKey();
                    break;
                }
            }
            log.info("当前 台账列表电量偏离比例的数量中位数及对应的偏离百分比为{}:{} ", valueMiddle, wideMiddle);
        } else {
            //只有一条记录，中位数和最大值重合
            valueMiddle = valueMax;
            wideMiddle = wideMax;

        }


        Map<String, List<AuditResult>> powerChangePo = auditResults.stream()
          .filter(auditResult -> auditResult.getStep() == 3)
          .collect(
            Collectors.groupingBy(
              auditResult -> {
                  StationPowerChangeRefereeContent content =
                    (StationPowerChangeRefereeContent) AbstractRefereeContent.forAuditResultWithJson(auditResult);
                  BigDecimal power =
                    content.getPower();
                  BigDecimal power_compare =
                    content.getPower_compare();
                  if (power == null) {
                      return "当前台账本次台账电量为null";
                  }
                  if (power_compare == null || power_compare.compareTo(BigDecimal.ZERO) == 0) {
                      return "当前台账 无上次台账信息";
                  }
                  BigDecimal widepower =
                    content.getWidepower();

                  BigDecimal widepower1;
                  BigDecimal widepower2;
                  if (wideMiddle.compareTo(BigDecimal.ZERO) <= 0) {
                      widepower2 = wideMiddle;
                      widepower1 =
                        wideMiddle.multiply(BigDecimal.valueOf(-1));
                  } else {
                      widepower1 = wideMiddle;
                      widepower2 =
                        wideMiddle.multiply(BigDecimal.valueOf(-1));
                  }
                  // -1 1
                  return widepower.compareTo(wideMax) == 0 ?
                    String.format(
                      "电量波动单个比例占比最多的是%d%%"
                      ,
                      wideMax.intValue()) :
                    widepower.compareTo(widepower2) <= 0 ?
                      String.format(
                        "电量波动<=%d%%", widepower2.intValue()) :
                      widepower.compareTo(widepower1) <= 0 ?
                        String.format(
                          "电量波动在%d%%%d%%之间",
                          widepower2.intValue(),
                          widepower1.intValue()
                        )
                        :
                        String.format(
                          "电量波动>=%d%%",
                          widepower1.intValue()
                        );


              }
            ));
        //2.2组装powerChangeVo
        HashMap<String, Object> powerChangeVo = new HashMap<>();
        powerChangePo.forEach(
          (k, v) -> {
              powerChangeVo.put(k, v.size());
              //设置redis
              StringJoiner joiner = new StringJoiner("_");
              joiner.add("node3").add(billId + "").add(k);
              String rediskey = joiner.toString();
              powerChangeVo.put("node3_key_" + k, rediskey);
              RedisUtil.setObj(rediskey, v);
              RedisUtil.getRedisTemplate().expire(rediskey, 62L, TimeUnit.DAYS);
          }
        );
        //2.3 电量波动异常放入resultMap
        resultMap.put("电量波动异常", powerChangeVo);

        //2.4将结果放入到 统计表
        powerChangePo.forEach(
          (k, v) -> {
              NodeResultStatistical nodeResultStatistical = new NodeResultStatistical();
              nodeResultStatistical.setBillid(billId);
              nodeResultStatistical.setNodeType(NodeType.StationPowerChange.getCode());
              nodeResultStatistical.setLevel(k);
//                    String content = JSON.toJSONString(v);
              HashMap<String, Object> contentMap = new HashMap<>();
              List<Long> list = v.stream().map(
                auditResult -> {
                    return auditResult.getId();
                }
              ).collect(Collectors.toList());
              int size = list.size();
              contentMap.put("ids", list);
              contentMap.put("size", size);
              String content = JSON.toJSONString(contentMap);
              nodeResultStatistical.setContent(content);
              nodeResultStatisticals.add(nodeResultStatistical);
          }
        );
        log.info("billid={}的电量波动汇总完毕", billId);
        return changePos;
    }


    public void uniflowCallBack(WFModel wfModel) throws Exception {
        if (!"MSS_ACCOUNT".equals(wfModel.getBusiAlias())) {
            return;
        }
        log.info("流程信息:{}", wfModel.toString());
        if (StringUtils.isNotEmpty(wfModel.getCallbackType()) && "PROCESS_STARTED".equals(wfModel.getCallbackType()) && wfModel.getVariables().containsKey("firstNode") && wfModel.getVariables().get("firstNode").equals(true)) {
            this.doStartFlow(wfModel.getBusiId(), wfModel.getProcInstId());
            this.updateStatus(wfModel.getBusiId(), 1);//修改 审批状态 为审批中
        } else if (StringUtils.isNotEmpty(wfModel.getCallbackType()) && "PROCESS_COMPLETED".equals(wfModel.getCallbackType())) {
            try {
                //修改单据状态为已完成并更新数据
                this.updateStatus(wfModel.getBusiId(), 2);//修改 审批状态 为审批通过
            } catch (Exception e) {
                e.printStackTrace();
                OperLog model = new OperLog();
                model.setOperName("ammeter");
                model.setTitle("报账批量稽核流程回调 修改审批状态为已通过， 但更新数据异常");
                model.setMethod("updateDateByChange");
                model.setErrorMsg("流程id:" + wfModel.getBusiId() + " 流程：" + wfModel.getBusiAlias() + " 用户：" + wfModel.getApplyUserId());
                model.setOperTime(new Date());
                operLogMapper.insert(model);
                throw e;
            }
        } else if (StringUtils.isNotEmpty(wfModel.getCallbackType()) && "PROCESS_CANCELLED".equals(wfModel.getCallbackType())) {
            //修改审批状态为 不通过
            this.updateStatus(wfModel.getBusiId(), 3);
        }
    }

    public void doStartFlow(String billId, Long processinstid) {
        Map<String, Object> parms = new HashMap<>();


        NodeResultStatistical nodeResultStatistical = null;
        if (null != nodeResultStatistical) {
            nodeResultStatistical.setProcessinstId(processinstid);
            statisticalMapper.updateForModel(nodeResultStatistical);
        } else {
            log.warn("没有找到对应的基础数据，或已被删除");
        }
    }

    private void updateStatus(String id, int billStatus) {
        Map<String, Object> parms = new HashMap<>();
        parms.put("id", id);
        NodeResultStatistical statistical = statisticalMapper.selectByPrimaryKey(parms);
        if (null != statistical) {
            statistical.setBillStatus(billStatus);
            statisticalMapper.updateForModel(statistical);
        } else {
            log.warn("没有找到对应的基础数据，或已被删除");
        }
    }


}

