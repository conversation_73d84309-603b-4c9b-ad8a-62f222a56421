package com.sccl.modules.business.pylonlnbg.service;

import com.sccl.common.lang.StringUtils;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.modules.business.account.domain.Account;
import com.sccl.modules.business.account.domain.AccountBaseResult;
import com.sccl.modules.business.account.domain.AccountCondition;
import com.sccl.modules.business.account.mapper.AccountMapper;
import com.sccl.modules.business.account.service.IAccountService;
import com.sccl.modules.business.account.unit.DataVerification;
import com.sccl.modules.business.accountbillitempre.domain.Accountbillitempre;
import com.sccl.modules.business.ammeterorprotocol.service.IAmmeterorprotocolService;
import com.sccl.modules.business.powerlumpprice.mapper.PowerLumppriceMapper;
import com.sccl.modules.business.pylonBG.mapper.PylonBGMapper;
import com.sccl.modules.business.pylonBG.service.IPylonBGService;
import com.sccl.modules.business.pylonlnbg.mapper.PylonlnbgMapper;
import com.sccl.modules.system.user.domain.User;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.sccl.modules.business.account.service.AccountServiceImpl.subMonth;
import static com.sccl.modules.business.account.unit.DataVerification.ifnull;

/**
 * __辽宁铁塔包干__<br/>
 * 2019/12/24
 *
 * <AUTHOR>
 */
@Service
public class PylonlnbgServiceImpl extends BaseServiceImpl<Account> implements IPylonlnbgService {

    @Autowired
    PylonlnbgMapper pylonlnbgMapper;
    @Autowired
    public AccountMapper accountMapper;
    @Autowired
    public PylonBGMapper pylonBGMapper;
    @Autowired
    IPylonBGService pylonBGService;
    @Autowired
    IAccountService accountService;
    @Autowired
    IAmmeterorprotocolService ammeterorprotocolService;
    @Value("${sccl.deployTo}")
    private String deployTo;

    @Override
    public List<AccountBaseResult> selectByParams(AccountCondition condition) {
        Long companyId = condition.getCompany();
        Long countryId = condition.getCountry();
        User user = ShiroUtils.getUser();
        if (user != null) {
            //部门组
            List<IdNameVO> departments = user.getDepartments();
            //分公司组
            List<IdNameVO> companies = user.getCompanies();
            //如果没有传入分公司和责任中心，则默认第一个
            if (companyId == null) {
                companyId = companies != null && companies.size() > 0 ? Long.parseLong(companies.get(0).getId()) : null;
            }
            if (countryId == null) {
                countryId = departments != null && departments.size() > 0 ? Long.parseLong(departments.get(0).getId()) : null;
            }
        }
        if (!"-1".equals(condition.getAccountno())) {
            initPoweerAccount(condition.getAccountno(), companyId, countryId, user.getId());
        }
        condition.setCompany(companyId);
        condition.setCountry(countryId);
        List<AccountBaseResult> resultList = pylonlnbgMapper.selectByParams(condition);
        if (resultList != null && resultList.size() > 0) {
            for (AccountBaseResult result : resultList) {
                AccountCondition ifNextCondition = new AccountCondition();
                ifNextCondition.setAmmeterid(result.getAmmeterid());
                try {
                    ifNextCondition.setAccountno(result.getAccountno());
                } catch (Exception e) {

                }

                Integer ifNext = pylonBGMapper.selectifNext(ifNextCondition);
                if (ifNext != null && ifNext > 0) {
                    result.setIfNext(true);
                } else {
                    result.setIfNext(false);
                }
            }
        }
        return resultList;
    }

    @Override
    public Map<String, Object> updateData(List<Account> accountList) {
        return null;
    }

    @Override
    public int deleteAccountByIds(String[] pcids) {
        return 0;
    }

    @Override
    public AccountBaseResult accountTotal(AccountCondition condition) {
        Long countryId = condition.getCountry();
        Long companyId = condition.getCompany();
        User user = ShiroUtils.getUser();
        if (user != null) {
            //分公司组
            List<IdNameVO> companies = user.getCompanies();
            //部门组
            List<IdNameVO> departments = user.getDepartments();
            //如果没有传入分公司和责任中心，则默认第一个
            if (companyId == null) {
                companyId = companies != null && companies.size() > 0 ? Long.parseLong(companies.get(0).getId()) : null;
            }
            if (countryId == null) {
                countryId = departments != null && departments.size() > 0 ? Long.parseLong(departments.get(0).getId()) : null;
            }
        }
        //搜索条件中加上当前登录人所属分公司
        //查询
        condition.setCompany(companyId);
        condition.setCountry(countryId);
        return pylonlnbgMapper.accountTotal(condition);
    }

    @Override
    public List<AccountBaseResult> selectListByPre(Accountbillitempre accountbillitempre) {
        return null;
    }

    @Override
    public List<AccountBaseResult> selectQuery(AccountCondition condition) {
        return pylonlnbgMapper.selectQuery(condition);
    }

    @Override
    public AccountBaseResult queryTotal(AccountCondition condition) {
        return pylonlnbgMapper.queryTotal(condition);
    }

    @Override
    public Map<String, Object> selectIdsByParams(AccountCondition condition) {
        Map<String, Object> map = new HashMap<>();
        List<Long> ids = pylonlnbgMapper.selectIdsByParams(condition);
        if (ids == null) {
            ids = new ArrayList<>();
        }
        map.put("ids", ids);
        return map;
    }

    /**
     * @Description: 初始化台账
     * @author: dongk
     * @date: 2019/7/9
     * @param:
     * @return:
     */
    private void initPoweerAccount(String accountno, Long companyId, Long countryId, Long userid) {
        Account account = new Account();
        account.setAccountno(accountno);
        account.setCompany(companyId);
        account.setCountry(countryId);
        account.setInputerid(userid);
        pylonlnbgMapper.initBGAccount(account);
    }

    @Override
    public XSSFWorkbook exportbg(List<AccountBaseResult> list, String version) {
        BigDecimal z = new BigDecimal(0);
        String[] header = null;
        String[] headerSC = new String[]{"电表/协议id", "项目名称", "电表户号/协议编码", "    期号    ", "起始日期(必填)", "截止日期(必填)", "本期峰段起度(选填)", "本期平段起度(选填)", "本期谷段起度(选填)", "本期起度(有表类普通电表必填)", "本期峰段止度(选填)", "本期平段止度(选填)", "本期谷段止度(选填)", "本期止度(有表类普通电表必填)", "电损(度)(选填)", "专票含税金额(元)(专票普票必填其中一项)", "专票税率(%)", "专票税额", "普票含税金额(元)(专票普票必填其中一项)", "其他(元)(选填)", "实缴费用(元)含税", "    倍率    ", "用电量(度)", "总电量(度)", "电价(元)", "    备注(选填)    ", "分割比例(%)"};
        String[] headerLN = new String[]{"电表/协议id", "项目名称", "供电局电表编号", "局站", "用电类型", "站址编码", "    期号    ", "起始日期(必填)", "截止日期(必填)", "用电量【必填】", "电价（元）", "包干电费(元)【必填】", "其他(元)【可选】", "税率(%)【必填】", "税额", "实缴费用(元)", "类型描述", "备注【可选】"};
        if ("sc".equals(version)) {
            header = headerSC;
        } else if ("ln".equals(version)) {
            header = headerLN;
        }

        // 声明一个工作簿
        XSSFWorkbook wb = new XSSFWorkbook();
        // 生成一个表格
        XSSFSheet sheet = wb.createSheet("sheet1");

        // 生成一种样式
        XSSFCellStyle style = wb.createCellStyle();
        // 设置样式
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);

        // 生成一种字体
        XSSFFont font = wb.createFont();
        // 设置字体
        font.setFontName("微软雅黑");
        // 设置字体大小
        font.setFontHeightInPoints((short) 12);
        // 字体加粗
        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        // 在样式中引用这种字体
        style.setFont(font);
        // 生成并设置另一个样式
        XSSFCellStyle style2 = wb.createCellStyle();
        style2.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style2.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style2.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style2.setBorderTop(HSSFCellStyle.BORDER_THIN);
        style2.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style2.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);

        // 生成另一种字体2
        XSSFFont font2 = wb.createFont();
        // 设置字体
        font2.setFontName("微软雅黑");
        // 设置字体大小
        font2.setFontHeightInPoints((short) 12);
        // 字体加粗
        style2.setFont(font2);

        // 生成表格的第一行
        // 第一行表头
        XSSFRow row = sheet.createRow(0);
        for (int i = 0; i < header.length; i++) {
            XSSFCell cell = row.createCell(i);
            cell.setCellValue(header[i]);
            cell.setCellStyle(style);
            sheet.autoSizeColumn(i, true);
        }


        sheet.setColumnWidth(0, 0);
        if (list != null && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                AccountBaseResult result = list.get(i);
                row = sheet.createRow(i + 1);
                XSSFCell cell = row.createCell(0);
                cell.setCellValue(result.getAmmeterid().toString());
                cell.setCellStyle(style2);
                cell = row.createCell(1);
                cell.setCellValue(result.getProjectname());
                cell.setCellStyle(style2);
                if ("sc".equals(version)) {
                    cell = row.createCell(2);
                    cell.setCellValue(result.getAmmetercode());
                    cell.setCellStyle(style2);
                } else if ("ln".equals(version)) {
                    cell = row.createCell(2);
                    cell.setCellValue(result.getSupplybureauammetercode());
                    cell.setCellStyle(style2);
                }
                cell = row.createCell(3);
                cell.setCellValue(result.getStationName());
                cell.setCellStyle(style2);
                cell = row.createCell(4);
                cell.setCellValue(result.getElectrotypename());
                cell.setCellStyle(style2);
                cell = row.createCell(5);
                cell.setCellValue(result.getStationaddresscode());
                cell.setCellStyle(style2);
                cell = row.createCell(6);
                cell.setCellValue(result.getAccountno());
                cell.setCellStyle(style2);
                cell = row.createCell(7);
                cell.setCellValue(result.getStartdate());
                cell.setCellStyle(style2);
                cell = row.createCell(8);
                cell.setCellValue(result.getEnddate());
                cell.setCellStyle(style2);
                cell = row.createCell(9);
                cell.setCellValue(ifnull(result.getCurusedreadings(), z).setScale(2, BigDecimal.ROUND_HALF_DOWN).doubleValue());
                cell.setCellStyle(style2);
                cell = row.createCell(10);
                cell.setCellValue(ifnull(result.getUnitpirce(), z).setScale(2, BigDecimal.ROUND_HALF_DOWN).doubleValue());
                cell.setCellStyle(style2);
                cell = row.createCell(11);
                cell.setCellValue(ifnull(result.getTaxticketmoney(), z).setScale(2, BigDecimal.ROUND_HALF_DOWN).doubleValue());
                cell.setCellStyle(style2);
                cell = row.createCell(12);
                cell.setCellValue(ifnull(result.getUllagemoney(), z).setScale(2, BigDecimal.ROUND_HALF_DOWN).doubleValue());
                cell.setCellStyle(style2);
                cell = row.createCell(13);
                cell.setCellValue((result.getTaxrate() == null || result.getTaxrate().compareTo(z) == 0 ? BigDecimal.valueOf(13) : result.getTaxrate()).doubleValue());
                cell.setCellStyle(style2);
                cell = row.createCell(14);
                cell.setCellValue(ifnull(result.getTaxamount(), z).setScale(2, BigDecimal.ROUND_HALF_DOWN).doubleValue());
                cell.setCellStyle(style2);
                cell = row.createCell(15);
                cell.setCellValue(ifnull(result.getAccountmoney(), z).setScale(2, BigDecimal.ROUND_HALF_DOWN).doubleValue());
                cell.setCellStyle(style2);
                cell = row.createCell(16);
                cell.setCellValue(result.getCategoryname());
                cell.setCellStyle(style2);
                cell = row.createCell(17);
                cell.setCellValue(result.getRemark());
                cell.setCellStyle(style2);

            }
        }
        return wb;
    }

    @Override
    public Map<String, Object> dataVerification(List<AccountBaseResult> list, String version) throws Exception {
        BigDecimal z = new BigDecimal(0);
        List<AccountBaseResult> preserve = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        //获取当前时间的期号和上个月期号
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMM");
        String no = formatter.format(new Date());
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.MONTH, -1);
        Date m = c.getTime();
        String mon = formatter.format(m);

        if (list != null && list.size() > 0) {
            for (AccountBaseResult obj : list) {
                if ("小计".equals(obj.getProjectname()) || "合计".equals(obj.getProjectname())) {
                    continue;
                }
                if (!StringUtils.isEmpty(obj.getAccountno()) && !obj.getAccountno().equals(no) && !obj.getAccountno().equals(mon)) {
                    obj.setError("当前时间只能导入期号为【" + mon + "、" + no + "】的台账；");
                    continue;
                }

                AccountCondition condition = new AccountCondition();
                condition.setAccountno(obj.getAccountno());
                condition.setAmmeterid(obj.getAmmeterid());
                List<AccountBaseResult> data = pylonlnbgMapper.selectByParams(condition);
                condition.setAccountno(no);
                Boolean ifNext = accountMapper.selectcurrent(condition);

                if (ifNext != null && ifNext && obj.getAccountno().equals(mon)) {
                    obj.setError("本期已录入台账，上期台账不能导入；");
                    continue;
                }
                if (data == null || data.size() == 0) {
                    obj.setError("找不到对应的台账信息；");
                    continue;
                } else {
                    obj.setPrevhighreadings(ifnull(obj.getPrevhighreadings(), z));
                    obj.setPrevflatreadings(ifnull(obj.getPrevflatreadings(), z));
                    obj.setPrevlowreadings(ifnull(obj.getPrevlowreadings(), z));
                    obj.setCurhighreadings(ifnull(obj.getCurhighreadings(), z));
                    obj.setCurflatreadings(ifnull(obj.getCurflatreadings(), z));
                    obj.setCurlowreadings(ifnull(obj.getCurlowreadings(), z));
                    obj.setPrevtotalreadings(ifnull(obj.getPrevtotalreadings(), z));
                    obj.setStartdate(obj.getStartdate() == null ? "" : obj.getStartdate());
                    AccountBaseResult query = data.get(0);
                    String remark = "";
//                    if (query.getPrevhighreadings().compareTo(obj.getPrevhighreadings()) != 0) {
//                        remark += "本期起始峰值 从" + query.getPrevhighreadings() + " 修改为 " + obj.getPrevhighreadings() + " ；";
//                    }
//                    if (query.getPrevflatreadings().compareTo(obj.getPrevflatreadings()) != 0) {
//                        remark += "本期起始平值 从" + query.getPrevflatreadings() + " 修改为 " + obj.getPrevflatreadings() + " ；";
//                    }
//                    if (query.getPrevlowreadings().compareTo(obj.getPrevlowreadings()) != 0) {
//                        remark += "本期起始谷值 从" + query.getPrevlowreadings() + " 修改为 " + obj.getPrevlowreadings() + " ；";
//                    }
                    if (!query.getStartdate().equals(obj.getStartdate())) {
                        remark += "本期起始日期 从" + query.getStartdate() + " 修改为 " + obj.getStartdate() + "；";
                    }
//                    if (query.getPrevtotalreadings().compareTo(obj.getPrevtotalreadings()) != 0) {
//                        remark += "本期起度 从" + query.getPrevtotalreadings() + " 修改为 " + obj.getPrevtotalreadings() + " ；";
//                    }

                    if (StringUtils.isEmpty(query.getRemark())) {
                        query.setRemark(remark);
                    } else {
                        String s = query.getRemark();
                        query.setRemark(s += remark);
                    }
                    query.setPrevhighreadings(ifnull(obj.getPrevhighreadings(), z));
                    query.setPrevflatreadings(ifnull(obj.getPrevflatreadings(), z));
                    query.setPrevlowreadings(ifnull(obj.getPrevlowreadings(), z));
                    query.setCurhighreadings(ifnull(obj.getCurhighreadings(), z));
                    query.setCurflatreadings(ifnull(obj.getCurflatreadings(), z));
                    query.setCurlowreadings(ifnull(obj.getCurlowreadings(), z));
                    query.setTransformerullage(ifnull(obj.getTransformerullage(), z));
                    query.setInputtaxticketmoney(ifnull(obj.getInputtaxticketmoney(), z));
                    query.setInputticketmoney(ifnull(obj.getInputticketmoney(), z));
                    query.setTicketmoney(ifnull(obj.getTicketmoney(), z));
                    query.setTaxticketmoney(ifnull(obj.getTaxticketmoney(), z));
                    query.setUllagemoney(ifnull(obj.getUllagemoney(), z));
                    query.setCurusedreadings(ifnull(obj.getCurusedreadings(), z));
//                    query.setBz(obj.getBz());
//                    query.setMulttimes(query.getMagnification());
//
//                    if ("ln".equals(version)) {
//                        query.setSupplybureauammetercode(obj.getAmmetercode());
//                    }

                    StringBuffer rStr = new StringBuffer();
                    String srt = "";
                    BigDecimal taxrate = ifnull(obj.getTaxrate(), z);
                    query.setTaxrate(taxrate);
                    if (taxrate.compareTo(new BigDecimal(13)) != 0 && taxrate.compareTo(new BigDecimal(16)) != 0
                            && taxrate.compareTo(new BigDecimal(1)) != 0 && taxrate.compareTo(new BigDecimal(3)) != 0 && taxrate.compareTo(new BigDecimal(6)) != 0&& taxrate.compareTo(new BigDecimal(17)) != 0) {
                        rStr.append("税率填写不规范，请填写(1,3,6,13,16,17)中的一种");
                    }
                    //category 为1,2,3的电表/协议，站址产权归属与用电类型是否一致
                    if (query.getCategory() != null && (query.getCategory() == 1 || query.getCategory() == 2 || query.getCategory() == 3)) {
                        // 站址产权归属为“铁塔”时，用电类型只能是1411或1412；
                        if (query.getProperty() != null && query.getElectrotype() != null
                                && query.getProperty() == 2 && query.getElectrotype() != 1411 && query.getElectrotype() != 1412) {
                            rStr.append("电表/协议[" + query.getAmmetercode() + "]站址产权归属[铁塔]与用电类型不一致，需到基础数据维护信息;");
                        }
                        // 站址产权归属是非铁塔时，用电类型不能是1411或1412。
                        if (query.getProperty() != null && query.getElectrotype() != null
                                && query.getProperty() != 2 && (query.getElectrotype() == 1411 || query.getElectrotype() == 1412)) {
                            rStr.append("电表/协议[" + query.getAmmetercode() + "]站址产权归属[自有]与用电类型[铁塔公司基站]不一致，需到基础数据维护信息;");
                        }
                    }

                    if (obj.getCurusedreadings().compareTo(BigDecimal.ZERO) == 0) {
                        rStr.append("用电量错误；");
                    }
                    if (obj.getInputtaxticketmoney() == null || obj.getInputtaxticketmoney().compareTo(BigDecimal.ZERO) == 0) {
                        rStr.append("包干电费错误；");
                    }
                    if (query.getLumpenddate() != null && obj.getEnddate() != null && Long.valueOf(obj.getEnddate()) > Long.valueOf(query.getLumpenddate())) {
                        rStr.append("【错误】您录入的截止日期: " + obj.getEnddate() + "不能超过铁塔包干截止日期：" + query.getLumpenddate());
                    }
                    //验证起始时间
                    srt = DataVerification.verifyStartDate(query, obj.getStartdate());
                    if (!StringUtils.isEmpty(srt)) {
                        rStr.append(srt);
                    } else {
                        query.setStartdate(obj.getStartdate());
                    }
                    //验证截止时间
                    srt = DataVerification.verifyEndDate(query, obj.getEnddate());
                    if (!StringUtils.isEmpty(srt)) {
                        rStr.append(srt);
                    } else {
                        query.setEnddate(obj.getEnddate());
                    }
//                    //验证起度
//                    srt = DataVerification.verifyPrevTotalReadings(query, obj.getPrevtotalreadings());
//                    if (!StringUtils.isEmpty(srt)) {
//                        rStr.append(srt);
//                    } else {
//                        query.setPrevtotalreadings(ifnull(obj.getPrevtotalreadings(), z));
//                    }
//                    //验证止度
//                    srt = DataVerification.verifyCurTotalReadings(query, obj.getCurtotalreadings());
//                    if (!StringUtils.isEmpty(srt)) {
//                        rStr.append(srt);
//                    } else {
//                        query.setCurtotalreadings(ifnull(obj.getCurtotalreadings(), z));
//                    }
                    //金额
                    query.setInputtaxticketmoney(ifnull(obj.getInputtaxticketmoney(), z));
                    query.setInputticketmoney(ifnull(obj.getInputticketmoney(), z));
                    query.setTaxticketmoney(ifnull(obj.getTaxticketmoney(), z));
//                    query.setTicketmoney(DataVerification.calculateActualMoney(query.getPercent(), query.getInputticketmoney()));

                    //计算用电量
//                    query.setCurusedreadings(DataVerification.calculateUsedReadings(query));
                    //计算总电量
                    query.setPercent(new BigDecimal(100));
                    query.setTotalusedreadings(DataVerification.calculateTotalReadings(query));
                    //计算税额
                    query.setTaxamount(DataVerification.countTaxamount(query));
                    //计算总价
                    query.setAccountmoney(DataVerification.calculateAccountMoney(query));
                    //计算单价
                    query.setUnitpirce(DataVerification.calculateUnitPriceByUsedMoney(query));
                    //计算浮动比
//                    query.setQuotereadingsratio(DataVerification.calculateQuotereadingsratio(query));

                    //最后的验证
//                    rStr.append(DataVerification.requiredFieldValidator(query, version));
                    if (StringUtils.isEmpty(rStr.toString())) {
                        preserve.add(query);
                    }
//                    rStr.append(DataVerification.unitpirceTips(query, version));
                    obj.setError(rStr.toString());
//                    String str1 = "";
//                    if (query.getMagnificationerr().equals(2)) {
//                        str1 += "台账倍率【" + query.getMagnification() + "】与电表倍率【" + query.getAmmmulttimes() + "】不一致；";
//                    }
//
//                    if (query.getPercenterr().equals(2)) {
//                        str1 += "台账分割比例【" + query.getPercent() + "】与电表分割比例【" + query.getAmmpercent() + "】不一致；";
//                    }
//                    obj.setCareful(str1);

//                    if (query.getIschangeammeter() == 1 && query.getIsnew() == 1) {
//                        if (query.getOldbillpower() != null && query.getOldbillpower().compareTo(BigDecimal.valueOf(0)) > 0) {
//                            query.setTotalusedreadings(query.getTotalusedreadings().add(query.getOldbillpower()));
//                            //从新计算单价
//                            query.setUnitpirce(DataVerification.calculateUnitPriceByUsedMoney(query));
//                        }
//                        if (remark.indexOf("换表") == -1) {
//                            remark += "换表，结清原电表读数【" + query.getOldbillpower().toString() + "】；";
//                            query.setRemark(remark);
//                        }
//                    }
                }

            }
            this.preserve(preserve);
        }
        map.put("list", list);
        map.put("number", preserve.size());
        return map;
    }

    public void preserve(List<AccountBaseResult> list) {
        if (list != null && list.size() > 0) {
            User user = ShiroUtils.getUser();
            for (AccountBaseResult result : list) {

                if (user != null) {
                    //设置最后修改人
                    result.setLastediterid(user.getId());
                    result.setLasteditdate(new Date());
                    if (result.getAccountmoney() != null && result.getAccountmoney().compareTo(new BigDecimal(0)) != 0) {
                        result.setEffective(new BigDecimal(1));
                    }
                }
                result.setIsnew(0);
                accountMapper.updateExcel(result);
                accountMapper.deleteByAmmeterid(result.getAmmeterid());
            }
        }
    }
}
