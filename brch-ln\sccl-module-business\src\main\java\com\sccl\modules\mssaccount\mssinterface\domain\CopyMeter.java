package com.sccl.modules.mssaccount.mssinterface.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 抄表数据
 */
public class CopyMeter implements Serializable {
    public String getOtherSystemMainId() {
        return otherSystemMainId;
    }

    public void setOtherSystemMainId(String otherSystemMainId) {
        this.otherSystemMainId = otherSystemMainId;
    }

    public String getWriteoffInstanceCode() {
        return writeoffInstanceCode;
    }

    public void setWriteoffInstanceCode(String writeoffInstanceCode) {
        this.writeoffInstanceCode = writeoffInstanceCode;
    }

    public String getPickingMode() {
        return pickingMode;
    }

    public void setPickingMode(String pickingMode) {
        this.pickingMode = pickingMode;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<CopyMeterInner> getWriteoffDetailInfos() {
        return writeoffDetailInfos;
    }

    public void setWriteoffDetailInfos(List<CopyMeterInner> writeoffDetailInfos) {
        this.writeoffDetailInfos = writeoffDetailInfos;
    }

    public CopyMeter() {
    }

    public CopyMeter(String otherSystemMainId, String writeoffInstanceCode, String pickingMode, String type,
                     List<CopyMeterInner> writeoffDetailInfos) {
        this.otherSystemMainId = otherSystemMainId;
        this.writeoffInstanceCode = writeoffInstanceCode;
        this.pickingMode = pickingMode;
        this.type = type;
        this.writeoffDetailInfos = writeoffDetailInfos;
    }

    /**
     * 能耗系统主单Id
     */
    private String otherSystemMainId;
    /**
     * 财辅系统报账单号
     */
    private String writeoffInstanceCode;
    /**
     * 业务场景
     */
    private String pickingMode;
    /**
     * 同步类型
     */
    private String type;
    /**
     * 报账子单集合
     */
    private List<CopyMeterInner> writeoffDetailInfos;


}
