package com.sccl.modules.business.ecceptionprocess.service;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.service.IdGenerator;
import com.sccl.framework.utils.RedisUtil;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.domain.BaseEntity;
import com.sccl.framework.web.page.PageDomain;
import com.sccl.framework.web.page.TableSupport;
import com.sccl.modules.autojob.util.convert.DateUtils;
import com.sccl.modules.autojob.util.convert.StringUtils;
import com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol;
import com.sccl.modules.business.ammeterorprotocolcheck.domain.AmmeterorprotocolCheck;
import com.sccl.modules.business.ammeterorprotocolcheck.mapper.AmmeterorprotocolCheckMapper;
import com.sccl.modules.business.auditresult.mapper.AuditResultMapper;
import com.sccl.modules.business.ecceptiondetail.domain.EcceptionDetail;
import com.sccl.modules.business.ecceptiondetail.domain.ExceptionSource;
import com.sccl.modules.business.ecceptiondetail.mapper.EcceptionDetailMapper;
import com.sccl.modules.business.ecceptionprocess.domain.EcceptionProcess;
import com.sccl.modules.business.ecceptionprocess.domain.ExceptionTop;
import com.sccl.modules.business.ecceptionprocess.framework.ViewNode;
import com.sccl.modules.business.ecceptionprocess.framework.YzViewNode;
import com.sccl.modules.business.ecceptionprocess.mapper.EcceptionProcessMapper;
import com.sccl.modules.business.ecceptionreply.domain.EcceptionReply;
import com.sccl.modules.business.noderesultstatistical.mapper.NodeResultStatisticalMapper;
import com.sccl.modules.business.stationequipment.domain.AuditOverdue;
import com.sccl.modules.business.stationequipment.domain.PowerAPPDbyc;
import com.sccl.modules.business.stationequipment.domain.TowerStationEquipment2;
import com.sccl.modules.business.stationequipment.mapper.TowerStationEquipment2Mapper;
import com.sccl.modules.uniflow.common.WFModel;
import com.sccl.timing.finder.util.QueryParamsBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static java.util.stream.Collectors.*;


/**
 * 公共异常流程 服务层实现
 *
 * <AUTHOR>
 * @date 2023-03-23
 */
@Service
@Slf4j
public class EcceptionProcessServiceImpl extends BaseServiceImpl<EcceptionProcess> implements IEcceptionProcessService {
    @Autowired
    private EcceptionProcessMapper ecceptionProcessMapper;
    @Autowired
    private EcceptionDetailMapper detailMapper;
    @Autowired
    private NodeResultStatisticalMapper nodeResultStatisticalMapper;
    @Autowired
    private AuditResultMapper auditResultMapper;
    @Autowired
    private TowerStationEquipment2Mapper equipment2Mapper;
    @Autowired
    private AmmeterorprotocolCheckMapper checkMapper;

    private static String fetchGroupBy(TowerStationEquipment2 t) {
        return t.getNameFull() + "-" + t.getStationcode();
    }

    public static void main(String[] args) {
        List<Integer> list = IntStream.range(0, 1503).boxed().collect(toList());
        IntStream.range(0, 1503).boxed()
                .collect(
                        Collectors.groupingBy(
                                i -> i / 500,
                                Collectors.mapping(
                                        i -> list.get(i), toList()
                                )
                        )
                ).values().forEach(
                        batch -> {
                            System.out.println(batch.size());
                            System.out.println("一批");
                        }
                );
    }

    private static String fetchGroupBy2(EcceptionDetail t) {
        return t.getExceptionSource() + "-" + t.getReplyFlag();
    }
    public void uniflowCallBack(WFModel wfModel) {
        if (wfModel == null || wfModel.getVariables() == null || wfModel.getBusiId() == null) {
            throw new RuntimeException("异常流程处理发生异常，回调参数为空");
        }
        Map<String, Object> params = wfModel.getVariables();
        //流程开始（发起流程）
        if (com.sccl.common.lang.StringUtils.isNotEmpty(wfModel.getCallbackType()) && "PROCESS_STARTED".equals(wfModel.getCallbackType())) {
            Long detailID = Long.parseLong(wfModel.getBusiId());
            EcceptionProcess param = new EcceptionProcess();
            param.setId(detailID);
            param.setProcessinstid(wfModel.getProcInstId() + "");
            param.setBillStatus("1");
            //param.set(wfModel.getBusiAlias());
            ecceptionProcessMapper.updateForModel(param);
            log.info("异常处理流程发起");
        }
        //流程中
        else if ("MID_NODE".equals(wfModel.getCallbackType())) {
    /*        EcceptionReply reply = buildNewOne(wfModel);
            mapper.insert(reply);
            log.info("{}回复异常流程：{}", wfModel.getApplyUserId(), wfModel
                    .getWfTask()
                    .getNodeId());*/
        }
        //流程完成（流程走完了）
        else if ("PROCESS_COMPLETED".equals(wfModel.getCallbackType())) {
/*            if (params.containsKey(ADVICE)) {
                EcceptionReply reply = buildNewOne(wfModel);
                mapper.insert(reply);
            }*/
 /*           EcceptionDetail param = new EcceptionDetail();
            param.setId(Long.parseLong(wfModel.getBusiId()));
            param.setBillstatus("2");*/
            EcceptionProcess param = new EcceptionProcess();
            param.setId(Long.parseLong(wfModel.getBusiId()));

            param.setBillStatus("2");
            ecceptionProcessMapper.updateByPrimaryKey(param);
            log.info("异常流程已完成");
        }
        //流程终止（删除该流程）
        else if ("PROCESS_CANCELLED".equals(wfModel.getCallbackType())) {
            //删除流程主表信息
/*            processMapper.deleteByPrimaryKey(QueryParamsBuilder
                    .newInstance()
                    .addParams("id", params.get(EXCEPTION_ID))
                    .getParams());
            //删除流程回复
            replyMapper.deleteByExceptionCommonID(Collections.singletonList((String) params.get(EXCEPTION_ID)));
            //删除异常明细
            detailMapper.stopProcess(Collections.singletonList(Long.parseLong(wfModel.getBusiAlias())));
            log.info("异常流程已终止");*/
        }
    }
    @Override
    @Transactional
    public AjaxResult generateCommonException() {
//        拿到不在common_ecception_process的全量集团下发异常
        List<TowerStationEquipment2> towerStationEquipment2s = ecceptionProcessMapper.selectAllForEquipment2();
//        拿到不在common_ecception_process的全量基础异常
        List<AmmeterorprotocolCheck> checks = ecceptionProcessMapper.selectAllForCheck();
//        对取出数据进行判断
        if (towerStationEquipment2s.size() == 0 & checks.size() == 0) {
            return AjaxResult.success("无异常代办要生成");
        }

        //组装 commonEccptionprocess
        Map<String, List<TowerStationEquipment2>> towerMap =
                towerStationEquipment2s.stream().filter(towerStationEquipment2 -> towerStationEquipment2.getMssStationCode() != null)
                        .collect(
                                groupingBy(EcceptionProcessServiceImpl::fetchGroupBy)
                        );

        Map<String, List<AmmeterorprotocolCheck>> checkMap =
                checks.stream().filter(check -> check.getStationcode() != null)
                        .collect(groupingBy(AmmeterorprotocolCheck::getStationcode));

        List<EcceptionProcess> processList = new ArrayList<>();

        List<EcceptionDetail> details = new ArrayList<>();
        towerMap.forEach(
                (k, v) -> {
                    EcceptionProcess process = new EcceptionProcess();
                    String[] split = k.split("-");
                    process.setFullName(split[0]);
                    process.setStationid(split[1]);

                    //组装listids
                    HashMap<String, List<Long>> idMap = new HashMap<>();
                    //集团下发异常
                    List<Long> jtids = v.stream().map(BaseEntity::getId).collect(Collectors.toList());
                    idMap.put(ExceptionSource.JT.getName(), jtids);
                    //基础异常
                    List<String> jtAmmeterids =
                            v.stream().map(TowerStationEquipment2::getAmmeterid).collect(Collectors.toList());
                    List<AmmeterorprotocolCheck> checks1 = checkMap.get(split[1]);
                    if (CollectionUtils.isNotEmpty(checks1)) {
                        List<AmmeterorprotocolCheck> checkList =
                                checks1.stream().collect(Collectors.toList());

                        List<Long> checkids = checkList.stream().filter(
                                check -> containAmmterid(check, jtAmmeterids)
                        ).map(BaseEntity::getId).collect(Collectors.toList());

                        if (CollectionUtils.isNotEmpty(checkids)) {
                            idMap.put(ExceptionSource.JC.getName(), checkids);
                        }

                    }

                    //设置组织编码
                    TowerStationEquipment2 jtOrg =
                            v.stream().filter(towerStationEquipment2 -> towerStationEquipment2.getOrgCode() != null && towerStationEquipment2.getCountry() != null
                            ).findFirst().get();
                    String orgcode = jtOrg.getOrgCode();
                    String country = StringUtils.splitData(jtOrg.getCountry(), "-1", ".");

                    process.setOrgCode(orgcode);
                    process.setCountry(country);
                    process.setListIds(JSON.toJSONString(idMap));
                    process.setCreateTime(new Date());
                    process.setUpdateTime(new Date());
                    process.setDelFlag("0");
                    long sourceid = IdGenerator.getNextId();
                    process.setId(sourceid);
                    processList.add(process);
                    //异常明细生成
                    List<EcceptionDetail> detailALLs = new ArrayList<>();
                    idMap.forEach(
                            (k1, v1) -> {
                                List<EcceptionDetail> detailList = v1.stream().map(
                                        id -> {
                                            EcceptionDetail detail = new EcceptionDetail();
                                            detail.setSourceid(sourceid);
                                            detail.setExceptionSource(k1);
                                            detail.setBusitabid(id);
                                            return detail;
                                        }
                                ).collect(Collectors.toList());

                                detailALLs.addAll(detailList);
                            }
                    );
                    details.addAll(detailALLs);

                }

        );
        int n = CollectionUtils.isNotEmpty(processList) ?
                ecceptionProcessMapper.insertList(processList) : 0;
        //维护异常明细表
        int n1 = CollectionUtils.isNotEmpty(details) ?
                detailMapper.insertList(details) : 0;

        log.info("调用定时任务生成{}条公共异常", n);
        log.info("调用定时任务生成{}条公共异常明细", n1);

        //更新异常标识
        List<Long> jtidList = towerStationEquipment2s.stream().map(BaseEntity::getId).collect(toList());
        List<Long> ckidList = checks.stream().map(BaseEntity::getId).collect(toList());
        int d1 = 0;
        int d2 = 0;
        if (CollectionUtils.isNotEmpty(jtidList)) {
            d1 = ecceptionProcessMapper.updateJtIds(jtidList);
        }
        if (CollectionUtils.isNotEmpty(ckidList)) {
            d2 = ecceptionProcessMapper.updateCkIds(ckidList);
        }
        log.info("更新了集团下发异常{}条异常标识，基础异常{}条异常标识", d1, d2);

        return AjaxResult.success(
                String.format("公共异常定时任务调用成功,生成%d条公共异常，%d条公共异常明细", n, n1)
        );

    }


    @Transactional
    public AjaxResult generateCommonExceptionPro() {
        log.info("第一步，数据准备");
        int bitchsize = 500;
        log.info("拿到不在common_ecception_process的全量集团下发异常");
        log.info("处理集团下发异常code_meter一对一的情况");
        List<TowerStationEquipment2> towerStationEquipment2s = new ArrayList<>();
        int temp1sum = 0;
        boolean temp1Flag = true;
        int temp1Limit = 500;
        int temp1offset = 0;
        while (temp1Flag) {
            List<TowerStationEquipment2> temp1 = ecceptionProcessMapper.selectAllForEquipment2Pro(temp1Limit, temp1offset);
            towerStationEquipment2s.addAll(temp1);
            if (temp1.size() < 500) {
                temp1Flag = false;
            }
            temp1offset += temp1Limit;
            temp1sum += 1;
            log.info("temp1第{}批数据初始化完毕", temp1sum);
        }

        log.info("处理集团下发异常code_meter存在多个的情况");
        List<TowerStationEquipment2> moreCodeMeters = new ArrayList<>();
        boolean temp2Flag = true;
        int temp2Limit = 500;
        int temp2offset = 0;
        while (temp2Flag) {
            List<TowerStationEquipment2> temp2 = ecceptionProcessMapper.moreCodeMeters(temp2Limit, temp2offset);
            moreCodeMeters.addAll(temp2);
            if (temp2.size() < 500) {
                temp2Flag = false;
            }
            temp2offset += temp2Limit;
        }
        List<TowerStationEquipment2> towerStationEquipment2sMoreCodeMeters = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(moreCodeMeters)) {
            moreCodeMeters = moreCodeMeters.stream().flatMap(
                    item -> {
                        Long id = item.getId();
                        return Arrays.stream(item.getCodeMeter().split(","))
                                .map(
                                        k -> {
                                            TowerStationEquipment2 temp = new TowerStationEquipment2();
                                            temp.setId(id);
                                            temp.setCodeMeter(k);
                                            return temp;
                                        }
                                );
                    }
            ).collect(toList());

            List<TowerStationEquipment2> finalMoreCodeMeters = moreCodeMeters;
            Map<Integer, List<TowerStationEquipment2>> temp2map = IntStream.range(0, moreCodeMeters.size()).boxed()
                    .collect(groupingBy(
                                    i -> i / bitchsize,
                                    mapping(i -> finalMoreCodeMeters.get(i), toList())
                            )
                    );
            List<TowerStationEquipment2> finalTowerStationEquipment2sMoreCodeMeters = towerStationEquipment2sMoreCodeMeters;
            AtomicInteger temp2sum = new AtomicInteger();
            temp2map.forEach(
                    (k, v) -> {
                        List<TowerStationEquipment2> temp2s = ecceptionProcessMapper.moreCodeMetersPro(v);
                        finalTowerStationEquipment2sMoreCodeMeters.addAll(temp2s);
                        temp2sum.addAndGet(1);
                        log.info("temp2第{}批数据初始化完毕", temp2sum.get());
                    }
            );
        }

        towerStationEquipment2s.addAll(towerStationEquipment2sMoreCodeMeters);
        log.info("拿到不在common_ecception_process的全量基础异常");
        List<AmmeterorprotocolCheck> checks = new ArrayList<>();
        boolean temp3Flag = true;
        int temp3sum = 0;
        int temp3Limit = 500;
        int temp3offset = 0;
        while (temp3Flag) {
            List<AmmeterorprotocolCheck> temp3 = ecceptionProcessMapper.selectAllForCheckPro(temp3Limit, temp3offset);
            checks.addAll(temp3);
            if (temp3.size() < bitchsize) {
                temp3Flag = false;
            }
            temp3sum += 1;
            temp3offset += temp3Limit;
            log.info("temp3sum第{}批数据初始化完毕", temp3sum);
        }

        log.info("拿到不在common_ecception_process的全量台账截至报账超期异常");
        ArrayList<AuditOverdue> auditOverdues = new ArrayList<>();
        int temp4sum = 0;
        boolean temp4Flag = true;
        int temp4Limit = 500;
        int temp4offset = 0;
        String lastEndDate = DateUtils.getFirstDayOfLastYear("yyyyMMdd");
        while (temp4Flag) {
            List<AuditOverdue> temp4 = ecceptionProcessMapper.selectAllForAuditOverdue(lastEndDate, temp4Limit, temp4offset);
            auditOverdues.addAll(temp4);
            if (temp4.size() < 500) {
                temp4Flag = false;
            }
            temp4offset += temp4Limit;
            temp4sum += 1;
            log.info("temp4第{}批数据初始化完毕", temp4sum);
        }
        log.info("处理超期400天 enddata->pcid映射");
        AuditOverdue.processPcid(auditOverdues);

        log.info("拿到不在common_ecception_process的全量大数据基站异常");
        ArrayList<PowerAPPDbyc> appDbycs = new ArrayList<>();
        int temp5sum = 0;
        boolean temp5Flag = true;
        int temp5Limit = 500;
        int temp5offset = 0;
        while (temp5Flag) {
            List<PowerAPPDbyc> temp5 = ecceptionProcessMapper.selectAllForPowerAppDbyc(temp5Limit, temp5offset);
            appDbycs.addAll(temp5);
            if (temp5.size() < 500) {
                temp5Flag = false;
            }
            temp5offset += temp5Limit;
            temp5sum += 1;
            log.info("temp5第{}批数据初始化完毕", temp5sum);
        }

//        对取出数据进行判断
        if (towerStationEquipment2s.size() == 0 && checks.size() == 0 && auditOverdues.size() == 0 && appDbycs.size() == 0) {
            log.info("无异常代办要生成");
            return AjaxResult.success("无异常代办要生成");
        }


        log.info("第二步，处理数据");
        //组装 commonEccptionprocess
        Map<String, List<TowerStationEquipment2>> towerMap =
                towerStationEquipment2s.stream().filter(towerStationEquipment2 -> towerStationEquipment2.getStationcode() != null)
                        .collect(
                                groupingBy(TowerStationEquipment2::getStationcode)
                        );

        Map<String, List<AmmeterorprotocolCheck>> checkMap =
                checks.stream().filter(check -> check.getStationcode() != null)
                        .collect(groupingBy(AmmeterorprotocolCheck::getStationcode));

        Map<String, List<AuditOverdue>> auditOverdueMap = auditOverdues.stream().filter(auditOverdue -> auditOverdue.getStationcode() != null).collect(
                groupingBy(AuditOverdue::getStationcode)
        );

        Map<String, List<PowerAPPDbyc>> appDbsyMap = appDbycs.stream().filter(appDbyc -> appDbyc.getStationcode() != null).collect(
                groupingBy(PowerAPPDbyc::getStationcode)
        );


        List<EcceptionProcess> towerprocessList = new ArrayList<>();
        towerMap.forEach(
                (stationcode, towers) -> {
                    EcceptionProcess process = new EcceptionProcess();
                    process.setStationid(stationcode);
                    TowerStationEquipment2 temp = towers.stream().filter(item -> item.getOrgCode() != null && item.getCountry() != null).findFirst().get();
                    String orgCode = temp.getOrgCode();
                    String country = temp.getCountry();
                    process.setOrgCode(orgCode);
                    process.setCountry(country);
                    List<Long> ids = towers.stream().map(BaseEntity::getId).collect(toList());
                    HashMap<String, List<Long>> idMap = new HashMap<>();
                    idMap.put(ExceptionSource.JT.getName(), ids);
                    process.setListIds(JSON.toJSONString(idMap));
                    towerprocessList.add(process);
                }
        );
        List<EcceptionProcess> checkprocessList = new ArrayList<>();
        checkMap.forEach(
                (stationcode, checkList) -> {
                    EcceptionProcess process = new EcceptionProcess();
                    process.setStationid(stationcode);
                    AmmeterorprotocolCheck temp = checkList.stream().filter(item -> item.getOrgCode() != null && item.getCountry() != null).findFirst().get();
                    String orgCode = temp.getOrgCode();
                    String country = temp.getCountry();
                    process.setOrgCode(orgCode);
                    process.setCountry(country);
                    List<Long> ids = checkList.stream().map(BaseEntity::getId).collect(toList());
                    HashMap<String, List<Long>> idMap = new HashMap<>();
                    idMap.put(ExceptionSource.JC.getName(), ids);
                    process.setListIds(JSON.toJSONString(idMap));
                    checkprocessList.add(process);
                }
        );
        ArrayList<EcceptionProcess> auditprocessList = new ArrayList<>();
        auditOverdueMap.forEach(
                (stationcode, auditOverdueList) -> {
                    EcceptionProcess process = new EcceptionProcess();
                    process.setStationid(stationcode);
                    AuditOverdue temp = auditOverdueList.stream().filter(item -> item.getOrgCode() != null && item.getCountry() != null).findFirst().get();
                    String orgCode = temp.getOrgCode();
                    String country = temp.getCountry();
                    process.setOrgCode(orgCode);
                    process.setCountry(country);
                    List<Long> ids = auditOverdueList.stream().map(AuditOverdue::getPcid).collect(toList());
                    HashMap<String, List<Long>> idMap = new HashMap<>();
                    idMap.put(ExceptionSource.PC.getName(), ids);
                    process.setListIds(JSON.toJSONString(idMap));
                    auditprocessList.add(process);
                }
        );

        ArrayList<EcceptionProcess> appprocessList = new ArrayList<>();
        appDbsyMap.forEach(
                (stationcode,apps)->{
                    EcceptionProcess process = new EcceptionProcess();
                    process.setStationid(stationcode);
                    PowerAPPDbyc temp = apps.stream().filter(item -> item.getCompany() != null && item.getCountry() != null).findFirst().get();
                    String company = temp.getCompany();
                    String country = temp.getCountry();
                    process.setOrgCode(company);
                    process.setCountry(country);
                    List<Long> ids = apps.stream().map(PowerAPPDbyc::getId).collect(toList());
                    HashMap<String, List<Long>> idMap = new HashMap<>();
                    idMap.put(ExceptionSource.APP.getName(), ids);
                    process.setListIds(JSON.toJSONString(idMap));
                    appprocessList.add(process);
                }
        );


        List<EcceptionProcess> processListtemp = EcceptionProcess.merge(towerprocessList, checkprocessList);
        List<EcceptionProcess> processListtemp2 = EcceptionProcess.merge(processListtemp, auditprocessList);
        List<EcceptionProcess> processList = EcceptionProcess.merge(processListtemp2, appprocessList);

        List<EcceptionDetail> details = new ArrayList<>();
        processList.forEach(
                process -> {
                    long nextId = IdGenerator.getNextId();
                    process.setId(nextId);
                    String ids = process.getListIds();
                    ObjectMapper objectMapper = new ObjectMapper();
                    try {
                        HashMap<String, List<Long>> idMap = objectMapper.readValue(ids, new TypeReference<HashMap<String, List<Long>>>() {
                        });
                        idMap.forEach(
                                (k1, v1) -> {
                                    List<EcceptionDetail> detailList = v1.stream().map(
                                            id -> {
                                                EcceptionDetail detail = new EcceptionDetail();
                                                detail.setSourceid(nextId);
                                                detail.setExceptionSource(k1);
                                                detail.setBusitabid(id);
                                                return detail;
                                            }
                                    ).collect(Collectors.toList());
                                    details.addAll(detailList);
                                }
                        );
                    } catch (IOException e) {
                        log.info("json序列化失败");
                    }

                }
        );
        log.info("第三步，数据库处理数据");
        int bitchsize2 = 1000;
        AtomicInteger n1 = new AtomicInteger();
        IntStream.range(0, processList.size()).boxed().collect(Collectors.groupingBy(
                i -> i / bitchsize2,
                mapping(i -> processList.get(i), toList())
        )).values().forEach(
                bitch -> {
                    int n = CollectionUtils.isNotEmpty(bitch) ?
                            ecceptionProcessMapper.insertList(bitch)
                            : 0;
                    n1.addAndGet(n);
                }
        );

        AtomicInteger n2 = new AtomicInteger();
        IntStream.range(0, details.size()).boxed().collect(Collectors.groupingBy(
                i -> i / bitchsize2,
                mapping(i -> details.get(i), toList())
        )).values().forEach(
                bitch -> {
                    int n = CollectionUtils.isNotEmpty(bitch) ?
                            detailMapper.insertList(bitch) : 0;
                    n2.addAndGet(n);
                }
        );

        log.info("调用定时任务生成{}条公共异常,等待事务提交", n1);
        log.info("调用定时任务生成{}条公共异常明细,等待事务提交", n2);

        //更新异常标识
        List<Long> jtidList = towerStationEquipment2s.stream().map(BaseEntity::getId).collect(toList());
        List<Long> ckidList = checks.stream().map(BaseEntity::getId).collect(toList());
        List<Long> pcidList = auditOverdues.stream().map(AuditOverdue::getPcid).collect(toList());
        AtomicInteger d1 = new AtomicInteger();
        AtomicInteger d2 = new AtomicInteger();
        AtomicInteger d3 = new AtomicInteger();
        int bitchsize3 = 5000;
        if (CollectionUtils.isNotEmpty(jtidList)) {
            IntStream.range(0, jtidList.size()).boxed().collect(Collectors.groupingBy(
                    i -> i / bitchsize3,
                    mapping(
                            i -> jtidList.get(i),
                            toList()
                    )
            )).values().forEach(
                    bitch -> {
                        int d1temp = ecceptionProcessMapper.updateJtIds(bitch);
                        d1.addAndGet(d1temp);
                    }
            );
        }
        if (CollectionUtils.isNotEmpty(ckidList)) {
            IntStream.range(0, ckidList.size()).boxed().collect(Collectors.groupingBy(
                    i -> i / bitchsize3,
                    mapping(
                            i -> ckidList.get(i),
                            toList()
                    )
            )).values().forEach(
                    bitch -> {
                        int d2temp = ecceptionProcessMapper.updateCkIds(bitch);
                        d2.addAndGet(d2temp);
                    }
            );
        }
        log.info("更新了集团下发异常{}条异常标识，基础异常{}条异常标识,审计超期台账{}条异常标识", d1.get(), d2.get(), d3.get());
        log.info("公共异常定时任务调用成功,生成{}条公共异常，{}条公共异常明细", n1.get(), n2.get());
        return AjaxResult.success(
                String.format("公共异常定时任务调用成功,生成%d条公共异常，%d条公共异常明细", n1.get(), n2.get())
        );

    }

    @Override
    public AjaxResult generateCommonExceptionPlus() {
        StringJoiner tall = new StringJoiner("||", "异常代办生成情况汇总(每批500条", "汇总结束");

        return generateCommonExceptionPro();
    }

    public AjaxResult viewTop(EcceptionProcess process) {
        //0最终返回结果
        HashMap<Object, Object> voMap = new HashMap<>();
        //1. 取出公共异常数据
        List<EcceptionDetail> detailList = ecceptionProcessMapper.selDetailList(process);
        //1.1判空
        if (CollectionUtils.isEmpty(detailList)) {
            return AjaxResult.success("当前用户对应局站无异常数据");
        }

        //2.开始构造集团异常和基础异常
        Map<String, Map<String, List<EcceptionDetail>>> mapOne = detailList.stream().collect(groupingBy(
                        EcceptionDetail::getExceptionSource,
                        groupingBy(EcceptionDetail::getReplyFlag)
                )
        );
        EcceptionProcess finalProcess = process;
        mapOne.forEach(
                (k, v) -> {
                    HashMap<String, Object> replyMap = new HashMap<>();
                    //构建总数
                    AtomicInteger sum = new AtomicInteger();
                    v.forEach(
                            (k1, v1) -> {
                                String k2 = "";
                                if ("1".equals(k1)) {
                                    k1 = "response";
                                    k2 = "responseKey";
                                } else {
                                    k1 = "responseNot";
                                    k2 = "responseNotKey";
                                }

                                //放入redis
                                String exceptionKey = finalProcess.getId() + "-" + k + "-" + k1;
                                List<Long> exceptionValue =
                                        v1.stream().map(EcceptionDetail::getBusitabid).collect(toList());
                                RedisUtil.setJson(exceptionKey, exceptionValue);
                                RedisUtil.setValidTimeForString(exceptionKey, 10 * 60 * 1000);

                                replyMap.put(k1, v1.size());
                                replyMap.put(k2, exceptionKey);
                                sum.addAndGet(v1.size());
                            }
                    );

                    //补充replyMap
                    replyMap.put("sum", sum.get());
                    if (replyMap.get("response") == null) {
                        replyMap.put("response", 0);
                    }
                    if (replyMap.get("responseNot") == null) {
                        replyMap.put("responseNot", 0);
                    }

                    voMap.put(k, replyMap);
                }

        );


//        //3.构建一站式稽核异常
//        //3.0获取对应异常工单
//        HashMap<String, Object> contionMap = new HashMap<>();
//        contionMap.put("id", process.getId());
//        process = ecceptionProcessMapper.selectByPrimaryKey(contionMap);
//
//        HashMap<String, Object> auditMap = new HashMap<>();
//        List<String> conents = nodeResultStatisticalMapper.selectListForFillName(process.getFullName());
//        //3.1 判空
//        if (CollectionUtils.isEmpty(conents)) {
//            return AjaxResult.success(voMap);
//        }
//        ArrayList<String> auditIds = new ArrayList<>();
//
//        ArrayList<String> finalAuditIds = auditIds;
//        conents.stream().forEach(
//                conent -> {
//                    List<String> strings = new Gson().fromJson(
//                            conent,
//                            TypeToken.getParameterized(List.class, String.class).getType()
//                    );
//                    finalAuditIds.addAll(strings);
//
//                }
//        );
//        //过滤掉局站id!=当前局站id的
//        List<String> auditIds1 = auditResultMapper.filterByStationId(auditIds, process.getStationid());
//        List<String> auditIds2 = auditResultMapper.filterByStationId2(auditIds, process.getStationid());
//        auditIds1 = auditIds1.stream().distinct().collect(toList());
//        auditIds2 = auditIds2.stream().distinct().collect(toList());
//        //判空
//        if (CollectionUtils.isEmpty(auditIds1) && CollectionUtils.isEmpty(auditIds2)) {
//            return AjaxResult.success(voMap);
//
//        }
//        //构建 key
//        String exceptionKey = process.getId() + "-" + ExceptionSource.YZ.getName() + "-" + "response";
//        List<String> exceptionValue = auditIds1;
//        String exceptionKey2 = process.getId() + "-" + ExceptionSource.YZ.getName() + "-" + "responseNot";
//        List<String> exceptionValue2 = auditIds2;
//        //放入redis
//        RedisUtil.setJson(exceptionKey, exceptionValue);
//        RedisUtil.setValidTimeForString(exceptionKey, 10 * 60 * 1000);
//        RedisUtil.setJson(exceptionKey2, exceptionValue2);
//        RedisUtil.setValidTimeForString(exceptionKey2, 10 * 60 * 1000);
//        //放入要返回的map中
//        if (CollectionUtils.isNotEmpty(auditIds1)) {
//            auditMap.put("response", auditIds1.size());
//            auditMap.put("responseKey", exceptionKey);
//        }
//        if (CollectionUtils.isNotEmpty(auditIds2)) {
//            auditMap.put("responseNot", auditIds2.size());
//            auditMap.put("responseNotKey", exceptionKey2);
//        }
//        //补充auditMap
//        int sum = 0;
//        sum = auditIds1.size() + auditIds2.size();
//        auditMap.put("sum", sum);
//        if (auditMap.get("response") == null) {
//            auditMap.put("response", 0);
//        }
//        if (auditMap.get("responseNot") == null) {
//            auditMap.put("responseNot", 0);
//        }
//
//        //返回给前端的map
//        voMap.put("audit", auditMap);

        //vomap转换
        ArrayList<ExceptionTop> list = new ArrayList<>();
        voMap.forEach(
                (k, v) -> {
                    ExceptionTop top = new ExceptionTop();
                    String k1 = (String) k;
                    HashMap v1 = (HashMap) v;
                    if ("jt".equals(k1)) {
                        top.setType("2");
                        top.setResponseKey(String.valueOf(v1.get("responseKey")));
                        top.setResponse(String.valueOf(v1.get("response")));
                        top.setResponseNotKey(String.valueOf(v1.get("responseNotKey")));
                        top.setResponseNot(String.valueOf(v1.get("responseNot")));
                        top.setSum(String.valueOf(v1.get("sum")));
                    } else if ("check".equals(k1)) {
                        top.setType("3");
                        top.setResponseKey(String.valueOf(v1.get("responseKey")));
                        top.setResponse(String.valueOf(v1.get("response")));
                        top.setResponseNotKey(String.valueOf(v1.get("responseNotKey")));
                        top.setResponseNot(String.valueOf(v1.get("responseNot")));
                        top.setSum(String.valueOf(v1.get("sum")));
                    } else if ("pc".equals(k1)) {
                        top.setType("4");
                        top.setResponseKey(String.valueOf(v1.get("responseKey")));
                        top.setResponse(String.valueOf(v1.get("response")));
                        top.setResponseNotKey(String.valueOf(v1.get("responseNotKey")));
                        top.setResponseNot(String.valueOf(v1.get("responseNot")));
                        top.setSum(String.valueOf(v1.get("sum")));
                    }else if ("app".equals(k1)) {
                        top.setType("5");
                        top.setResponseKey(String.valueOf(v1.get("responseKey")));
                        top.setResponse(String.valueOf(v1.get("response")));
                        top.setResponseNotKey(String.valueOf(v1.get("responseNotKey")));
                        top.setResponseNot(String.valueOf(v1.get("responseNot")));
                        top.setSum(String.valueOf(v1.get("sum")));
                    }
                    list.add(top);
                }

        );
        return AjaxResult.success(list);

    }

    public AjaxResult viewTopUpGrade(EcceptionProcess process) {
        //0最终返回结果
        HashMap<Object, Object> voMap = new HashMap<>();
        //1节点容器
        List<ViewNode> nodes = new ArrayList<>();
        //2加入节点
        YzViewNode yzViewNode = new YzViewNode();
        yzViewNode.setProcess(process);
        yzViewNode.setEcceptionProcessMapper(ecceptionProcessMapper);
        yzViewNode.setVoMap(voMap);
        yzViewNode.setAuditResultMapper(auditResultMapper);
        yzViewNode.setNodeResultStatisticalMapper(nodeResultStatisticalMapper);

        nodes.add(yzViewNode);
        //3调用节点处方法
        nodes.forEach(
                node -> node.todo(2)
        );


        return AjaxResult.success(voMap);

    }


    @Override
    public List<? extends Object> viewTwo(String exceptionKey) {
        //返回给前端的volist
        List<? extends Object> voList = new ArrayList();
        //取redis数据
        List<String> ids = new ArrayList<>();
        ids = RedisUtil.getJson(exceptionKey, List.class);
        //拆分exceptionKey
        String sourid = exceptionKey.split("-")[0];
        String businessFlag = exceptionKey.split("-")[1];
        String resflag = exceptionKey.split("-")[2];
        //判空
        if (CollectionUtils.isEmpty(ids)) {
            ids = ecceptionProcessMapper.getids(sourid, businessFlag, resflag);

        }
        if (CollectionUtils.isEmpty(ids)) {
            return voList;
        }

        startPage();
        if (ExceptionSource.JT.getName().equals(businessFlag)) {
            voList = equipment2Mapper.selectByIds(ids);
        } else if (ExceptionSource.JC.getName().equals(businessFlag)) {
            voList = checkMapper.selectByIds(ids);
            voList.stream().forEach(
                    init -> {
                        Ammeterorprotocol a = (Ammeterorprotocol) init;
                        if ("2".equals(a.getDownStatus())) {
                            a.set_disabled(true);
                        }
                    }
            );
        } else if (ExceptionSource.YZ.getName().equals(businessFlag)) {
            voList = auditResultMapper.selectByIds(ids);
        } else if (ExceptionSource.PC.getName().equals(businessFlag)) {
            voList = auditResultMapper.selectByPowerIds(ids);
        }else if (ExceptionSource.APP.getName().equals(businessFlag)) {
            voList = auditResultMapper.selectByAppIds(ids);
        }
        return voList;
    }

    private boolean containAmmterid(AmmeterorprotocolCheck check, List<String> v) {
        return v.contains(String.valueOf(check.getAmmeterid()));
    }


    public static class Ids {
        private String key;
        private List<Long> ids;

        public Ids() {
        }

        public Ids(String key, List<Long> ids) {
            this.key = key;
            this.ids = ids;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public List<Long> getIds() {
            return ids;
        }

        public void setIds(List<Long> ids) {
            this.ids = ids;
        }
    }

    protected void startPage() {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        if (com.sccl.common.lang.StringUtils.isNotNull(pageNum) && com.sccl.common.lang.StringUtils.isNotNull(pageSize)) {
            String orderBy = pageDomain.getOrderBy();
            PageHelper.startPage(pageNum, pageSize, orderBy);
        }
    }


}
