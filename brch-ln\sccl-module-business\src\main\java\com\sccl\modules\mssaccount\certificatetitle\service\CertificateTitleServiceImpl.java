package com.sccl.modules.mssaccount.certificatetitle.service;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.mssaccount.certificatetitle.mapper.CertificateTitleMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.sccl.modules.mssaccount.certificatetitle.domain.CertificateTitle;

import java.util.List;


/**
 * view_certificate_title 服务层实现
 * 
 * <AUTHOR>
 * @date 2019-09-20
 */
@Service
public class CertificateTitleServiceImpl extends BaseServiceImpl<CertificateTitle> implements ICertificateTitleService
{

    @Autowired
    CertificateTitleMapper certificateTitleMapper;

    @Value("${sccl.deployTo}")
    private String deployTo;

    @Override
    public List<CertificateTitle> selectCertificateTitleList(CertificateTitle certificateTitle) {
        if ("sc".equalsIgnoreCase(deployTo)) {
            return certificateTitleMapper.selectCertificateTitleList(certificateTitle);
        } else {
            return certificateTitleMapper.selectCertificateTitleListold(certificateTitle);
        }
    }
}
