package com.sccl.modules.business.statisticalanalysis.service;

import com.sccl.framework.service.IBaseService;
import com.sccl.modules.business.statisticalanalysis.domain.AmmeterProtocolNumber;

import java.util.List;

public interface IAmmeterProtocolNumberService extends IBaseService<AmmeterProtocolNumber> {
    // 分页查询
    List<AmmeterProtocolNumber> findGroupByCompany(AmmeterProtocolNumber ammeterProtocolNumber);

    List<AmmeterProtocolNumber> findGroupByCountry(AmmeterProtocolNumber ammeterProtocolNumber);
}
