package com.sccl.modules.business.stationequipment.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sccl.framework.aspectj.lang.annotation.Excel;
import com.sccl.framework.web.domain.BaseEntity;
import com.sccl.modules.autojob.util.convert.StringUtils;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;



/**
 * 铁塔站址设备表 tower_station_equipment_2
 *
 * <AUTHOR>
 * @date 2022-10-31
 */
@Data
public class TowerStationEquipment2 extends BaseEntity  {
    private static final long serialVersionUID = 1L;
    /**
     * 组织编码
     */
    private String orgCode;
    /**
     * 部门编码
     */
    private String country;
    /**
     * 电表局站id
     */
    private String stationcode;

    /**
     * 组Id，同一组属于同一个设备
     */
    @Excel(name = "设备组Id（请勿修改）", isAllowEdit = false)
    private Long groupId;
    /**
     * 省份标识
     */
    @cn.afterturn.easypoi.excel.annotation.Excel(name = "省份标识")
    private String province;
    /**
     * 省份名称
     */
    @cn.afterturn.easypoi.excel.annotation.Excel(name = "省份名称")
    private String provincename;
    /**
     * 月账期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @cn.afterturn.easypoi.excel.annotation.Excel(name = "月账期")
    private LocalDate monthaccount;
    /**
     * Mss省份编码
     */
    @cn.afterturn.easypoi.excel.annotation.Excel(name = "MSS省份编码")
    private Long mssProvince;
    /**
     * 本地网标识
     */
    @cn.afterturn.easypoi.excel.annotation.Excel(name = "本地网标识")
    private Integer networkLocal;
    /**
     * 本地网名称
     */
    @cn.afterturn.easypoi.excel.annotation.Excel(name = "本地网名称")
    private String networkLocalName;
    /**
     * 市局组织编码
     */
    @cn.afterturn.easypoi.excel.annotation.Excel(name = "市局组织编码")
    private String city;
    /**
     * 站址编码_报账
     */
    @cn.afterturn.easypoi.excel.annotation.Excel(name = "站址编码-报账")
    private String mssStationCode;
    @cn.afterturn.easypoi.excel.annotation.Excel(name = "站址编码-旧")
    /** 站址编码_旧 */
    private String oldStationCode;
    /**
     * 铁塔站址编码
     */
    @cn.afterturn.easypoi.excel.annotation.Excel(name = "铁塔站址编码")
    private String towerStationCode;
    /**
     * 局站类型
     */
    @cn.afterturn.easypoi.excel.annotation.Excel(name = "局站类型")
    private String typeStation;
    /**
     * 机房类型
     */
    @cn.afterturn.easypoi.excel.annotation.Excel(name = "机房类型")
    private String typeComputer;
    /**
     * 报账单号
     */
    private String mssCode;
    /**
     * 电表编码
     */
    private String codeMeter;
    /**
     * 填报人名称
     */
    private String nameFull;
    /**
     * 开始日期
     */
    private LocalDate timeStart;
    /**
     * 结束日期
     */
    private LocalDate timeEnd;
    /**
     * 报账金额
     */
    private BigDecimal mssaccount;
    /**
     * 报账电量
     */
    private BigDecimal powerMss;
    /**
     * 价款
     */
    private BigDecimal pricePre;
    /**
     * 税款
     */
    private BigDecimal priceAfter;
    /**
     * 供电方式
     */
    private Integer typePowersupply;
    /**
     * 站址稽核电量_全部
     */
    private BigDecimal powerStationAudit1;
    /**
     * 报账与能耗差异-全部%
     */
    private String diffMssFornh1;
    /**
     * 站址稽核电量-不含CDMA
     */
    private BigDecimal powerStationAudit2;
    /**
     * 报账与能耗差异-不含CDMA%
     */
    private String diffMssFornh2;
    /**
     * 差异区间
     */
    private String diffRange;
    /**
     * AAU/RRU数量（5G)
     */
    private Integer quantityAauRru1;
    /**
     * BBU数量(5G)
     */
    private Integer quantityBbu1;
    /**
     * AAU/RRU数量（4G)
     */
    private Integer quantityAauRru2;
    /**
     * BBU数量(4G)
     */
    private Integer quantityBbu2;
    /**
     * PRRU数量(5G)
     */
    private Integer quantityPrru1;
    /**
     * PRRU数量(4G)
     */
    private Integer quantityPrru2;
    /**
     * C网设备数量
     */
    private Double quantityC;
    /**
     * 远端直放站数量
     */
    private Double quantityRemote;
    /**
     * 近端直放站数量
     */
    private Double quantityNear;
    /**
     * 微波数量
     */
    private Double quantityWei;
    /**
     * IPRAN数量
     */
    private Double quantityIppan;
    /**
     * 皮站数量
     */
    private Double quantityP;
    /**
     * 光接入数量
     */
    private Double quantityG;
    /**
     * 传输设备数量
     */
    private Double quantityTrans;
    /**
     * 交换机数量
     */
    private Double quantitySwitch;
    /**
     * 域网数量
     */
    private Double quantityDomain;
    /**
     * 其它设备数量
     */
    private Double quantityOther;
    /**
     * AAU/RRU能耗（5G)
     */
    private BigDecimal powerAauRru1;
    /**
     * BBU能耗(5G)
     */
    private BigDecimal powerBbu1;
    /**
     * AAU/RRU能耗（4G)
     */
    private BigDecimal powerAauRru2;
    /**
     * BBU能耗(4G)
     */
    private BigDecimal powerBbu2;
    /**
     * PRRU能耗(5G)
     */
    private BigDecimal powerPrru1;
    /**
     * PRRU能耗(4G)
     */
    private BigDecimal powerPrru2;
    /**
     * C网设备能耗
     */
    private BigDecimal powerC;
    /**
     * 远端直放站能耗
     */
    private BigDecimal powerRemote;
    /**
     * 近端直放站能耗
     */
    private BigDecimal powerNear;
    /**
     * 微波能耗
     */
    private BigDecimal powerWei;
    /**
     * IPRAN能耗
     */
    private BigDecimal powerIpran;
    /**
     * 光接入能耗
     */
    private BigDecimal powerG;
    /**
     * 传输设备能耗
     */
    private BigDecimal powerTrans;
    /**
     * 交换机能耗
     */
    private BigDecimal powerSwitch;
    /**
     * 域网能耗
     */
    private BigDecimal powerDomain;
    /**
     * 其它能耗
     */
    private BigDecimal powerOther;
    /**
     * PUE系数(主要)
     */
    private BigDecimal pueMain;
    /**
     * PUE系数(机房)
     */
    private BigDecimal pueComputer;
    /**
     * PUE系数(季节)
     */
    private BigDecimal pueSeason;
    /**
     * PUE系数(默认)
     */
    private BigDecimal pueDefault;
    /**
     * 是否打包报账站址 0否/1是
     */
    private Integer packFlag;
    /**
     * 是否包干站址 0否/1是
     */
    private Integer baoganFlag;
    /**
     * 是否补缴预缴站址 0否/1是
     */
    private Integer bujiaoFlag;
    /**
     * 是否有换充电业务 0否/1是
     */
    private Integer chargingFlag;
    /**
     * 是否有智联业务 0否/1是
     */
    private Integer zlFlag;
    /**
     * 是否报账周期异常 0否/1是
     */
    private Integer exceptionMsFlag;
    /**
     * 是否需要核查 0否/1是
     */
    private Integer validateFlag;
    /**
     * 版本号
     */
    @Excel(name = "设备组Id（请勿修改）", isAllowEdit = false)
    private Long version;

    /**
     * 定额（依据设备标准功率计算）
     *
     * @param groupId
     */
    private BigDecimal standard;

    /**
     * 实际功率
     *
     * @param groupId
     */
    private BigDecimal calc;

    /**
     * 实际功率与 定额 偏差百分比
     *
     * @param groupId
     */
    private Double deviate;

    private Integer page;
    private Integer size;
    private String ammeterid;

    public static boolean orgCodeKey(TowerStationEquipment2 towerStationEquipment2) {
        String mssStationCode = towerStationEquipment2.getMssStationCode();
        String orgCode = towerStationEquipment2.getOrgCode();
        if (StringUtils.isNotBlank(mssStationCode) && StringUtils.isNotBlank(orgCode)) {
            return true;
        }
        return false;
    }

    //评级
    // 按月 标准 ，
    // 按 不同月 对应 不同 评级


}
