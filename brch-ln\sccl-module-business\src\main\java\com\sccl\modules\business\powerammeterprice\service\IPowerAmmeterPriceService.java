package com.sccl.modules.business.powerammeterprice.service;

import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.business.account.domain.AccountBaseResult;
import com.sccl.modules.business.account.domain.AccountCondition;
import com.sccl.modules.business.alertcontrol.domain.AlertControl;
import com.sccl.modules.business.powerammeterprice.domain.PowerAmmeterPrice;
import com.sccl.framework.service.IBaseService;
import com.sccl.modules.business.toweraccount.domain.TowerData;
import com.sccl.modules.mssaccount.mssinterface.domain.PowerElePriceItem;

import java.util.List;

/**
 * 电价上传 服务层
 * 
 * <AUTHOR>
 * @date 2022-09-18
 */
public interface IPowerAmmeterPriceService extends IBaseService<PowerAmmeterPrice>
{

    Object update(List<PowerAmmeterPrice> powerarlist);
    public List<PowerAmmeterPrice> selectbyList(PowerAmmeterPrice powerAmmeterPrice);
    public List<PowerAmmeterPrice> selecttabyList(PowerAmmeterPrice powerAmmeterPrice);
    public List<PowerAmmeterPrice> selectListapByIds(String[] ids);
    public List<PowerElePriceItem> selectListByIds(String[] ids);
    public List<PowerElePriceItem> selectListall(String ids);
    public String sendPricedetailInfo(List<PowerElePriceItem> items) throws Exception;
    public AjaxResult searchPricedetailInfo(List<PowerAmmeterPrice> items) throws Exception;
    public void sendpricebatch(List<PowerElePriceItem> items) throws Exception;
    public int countPricestatus(PowerAmmeterPrice powerAmmeterPrice);
    public int countPricetastatus(PowerAmmeterPrice powerAmmeterPrice);
    public void priceinit(PowerAmmeterPrice powerAmmeterPrice);
    public void priceinitta(PowerAmmeterPrice powerAmmeterPrice);

    AjaxResult createPrice(int year, int month);
}
