package com.sccl.modules.business.jhanomalydetails.mapper;


import com.sccl.modules.business.jhanomalydetails.domain.*;
import com.sccl.framework.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 稽核结果详情 数据层
 * 
 * <AUTHOR>
 * @date 2024-02-27
 */
public interface JhAnomalyDetailsMapper extends BaseMapper<JhAnomalyDetails>
{


    List<JhAnomalyDetails> selectByTzIds(@Param("ids") List<String> ids, @Param("jhsjs") List<String> jhsjs);



}