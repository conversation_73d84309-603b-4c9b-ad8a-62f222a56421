package com.sccl.modules.business.ecceptiondetail.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.business.ecceptiondetail.domain.EcceptionDetail;
import com.sccl.modules.business.ecceptiondetail.service.IEcceptionDetailService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 公共异常明细 信息操作处理
 * 
 * <AUTHOR>
 * @date 2023-03-23
 */
@RestController
@RequestMapping("/business/ecceptionDetail")
public class EcceptionDetailController extends BaseController
{
    private String prefix = "business/ecceptionDetail";
	
	@Autowired
	private IEcceptionDetailService ecceptionDetailService;
	
	@RequiresPermissions("business:ecceptionDetail:view")
	@GetMapping()
	public String ecceptionDetail()
	{
	    return prefix + "/ecceptionDetail";
	}
	
	/**
	 * 查询公共异常明细列表
	 */
	@RequiresPermissions("business:ecceptionDetail:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(EcceptionDetail ecceptionDetail)
	{
		startPage();
        List<EcceptionDetail> list = ecceptionDetailService.selectList(ecceptionDetail);
		return getDataTable(list);
	}
	
	/**
	 * 新增公共异常明细
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存公共异常明细
	 */
	@RequiresPermissions("business:ecceptionDetail:add")
	@Log(title = "公共异常明细", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody EcceptionDetail ecceptionDetail)
	{		
		return toAjax(ecceptionDetailService.insert(ecceptionDetail));
	}

	/**
	 * 修改公共异常明细
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		EcceptionDetail ecceptionDetail = ecceptionDetailService.get(id);

		Object object = JSONObject.toJSON(ecceptionDetail);

        return this.success(object);
	}
	
	/**
	 * 修改保存公共异常明细
	 */
	@RequiresPermissions("business:ecceptionDetail:edit")
	@Log(title = "公共异常明细", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody EcceptionDetail ecceptionDetail)
	{		
		return toAjax(ecceptionDetailService.update(ecceptionDetail));
	}
	
	/**
	 * 删除公共异常明细
	 */
	@RequiresPermissions("business:ecceptionDetail:remove")
	@Log(title = "公共异常明细", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(ecceptionDetailService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看公共异常明细
     */
    @RequiresPermissions("business:ecceptionDetail:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		EcceptionDetail ecceptionDetail = ecceptionDetailService.get(id);

        Object object = JSONObject.toJSON(ecceptionDetail);

        return this.success(object);
    }



}
