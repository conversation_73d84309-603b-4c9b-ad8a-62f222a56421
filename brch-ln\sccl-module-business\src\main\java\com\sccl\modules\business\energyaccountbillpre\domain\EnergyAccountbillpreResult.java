package com.sccl.modules.business.energyaccountbillpre.domain;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2022/1/10 14:20
 **/
@Getter
@Setter
public class EnergyAccountbillpreResult {
    /** id */
    private String pabrid;
    /** 归集单名称 */
    private String note;
    /** 类型 1自有 2预估 3铁塔 4合并 */
    private String billtype;
    /** 部门名字*/
    private String inputorgName;
    /** 分公司名字*/
    private String companyName;
    /** 录入时间 */
    private Date inputdate;
    /** 汇总金额 */
    private BigDecimal money;
    /** 代垫金额 */
    private BigDecimal incomeMoney;
    /** 汇总报账人 */
    private Long summaruser;
    /** 录入人名字*/
    private String inputName;
    /** 汇总报账人名字*/
    private String summaruserName;
    /** 关联报账单 */
    private String pabid;
    /** 状态 状态 1正常 2已生成报账单 3 已完成 */
    private Integer status;
}
