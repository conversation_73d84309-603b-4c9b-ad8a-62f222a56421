package com.sccl.modules.business.twoc.domain;

import com.sccl.framework.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;


/**
 * 计量设备表 meterinfo_twoc
 *
 * <AUTHOR>
 * @date 2023-04-13
 */
public class Twoc extends BaseEntity implements TwoCFlag {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private String provincecode;
    /**
     *
     */
    private String citycode;
    /**
     *
     */
    private String cityname;
    /**
     *
     */
    private String countycode;
    /**
     *
     */
    private String countyname;
    /**
     *
     */
    private String energymetercode;
    /**
     *
     */
    private String energymetername;
    /**
     *
     */
    private String status;
    /**
     *
     */
    private String usagecopy;
    /**
     *
     */
    private String type;
    /**
     *
     */
    private String stationcode;
    /**
     *
     */
    private String stationname;
    /**
     *
     */
    private String stationlocation;
    /**
     *
     */
    private String stationstatus;
    /**
     *
     */
    private String stationtype;
    /**
     *
     */
    private String energysupplyway;
    /**
     *
     */
    private String powergridenergymetercode;
    /**
     *
     */
    private String sitecode;
    /**
     * 创建时间
     */
    private Date createtime;
    /**
     * 同步标志 0未同步，1同步成功 2同步失败
     */
    private Integer syncflag;
    /**
     *
     */
    private String failmag;

    public String getProvincecode() {
        return provincecode;
    }

    public void setProvincecode(String provincecode) {
        this.provincecode = provincecode;
    }

    public String getCitycode() {
        return citycode;
    }

    public void setCitycode(String citycode) {
        this.citycode = citycode;
    }

    public String getCityname() {
        return cityname;
    }

    public void setCityname(String cityname) {
        this.cityname = cityname;
    }

    public String getCountycode() {
        return countycode;
    }

    public void setCountycode(String countycode) {
        this.countycode = countycode;
    }

    public String getCountyname() {
        return countyname;
    }

    public void setCountyname(String countyname) {
        this.countyname = countyname;
    }

    public String getEnergymetercode() {
        return energymetercode;
    }

    public void setEnergymetercode(String energymetercode) {
        this.energymetercode = energymetercode;
    }

    public String getEnergymetername() {
        return energymetername;
    }

    public void setEnergymetername(String energymetername) {
        this.energymetername = energymetername;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getUsagecopy() {
        return usagecopy;
    }

    public void setUsagecopy(String usagecopy) {
        this.usagecopy = usagecopy;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStationcode() {
        return stationcode;
    }

    public void setStationcode(String stationcode) {
        this.stationcode = stationcode;
    }

    public String getStationname() {
        return stationname;
    }

    public void setStationname(String stationname) {
        this.stationname = stationname;
    }

    public String getStationlocation() {
        return stationlocation;
    }

    public void setStationlocation(String stationlocation) {
        this.stationlocation = stationlocation;
    }

    public String getStationstatus() {
        return stationstatus;
    }

    public void setStationstatus(String stationstatus) {
        this.stationstatus = stationstatus;
    }

    public String getStationtype() {
        return stationtype;
    }

    public void setStationtype(String stationtype) {
        this.stationtype = stationtype;
    }

    public String getEnergysupplyway() {
        return energysupplyway;
    }

    public void setEnergysupplyway(String energysupplyway) {
        this.energysupplyway = energysupplyway;
    }

    public String getPowergridenergymetercode() {
        return powergridenergymetercode;
    }

    public void setPowergridenergymetercode(String powergridenergymetercode) {
        this.powergridenergymetercode = powergridenergymetercode;
    }

    public String getSitecode() {
        return sitecode;
    }

    public void setSitecode(String sitecode) {
        this.sitecode = sitecode;
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public Integer getSyncflag() {
        return syncflag;
    }

    public void setSyncflag(Integer syncflag) {
        this.syncflag = syncflag;
    }

    public String getFailmag() {
        return failmag;
    }

    public void setFailmag(String failmag) {
        this.failmag = failmag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("provincecode", getProvincecode())
                .append("citycode", getCitycode())
                .append("cityname", getCityname())
                .append("countycode", getCountycode())
                .append("countyname", getCountyname())
                .append("energymetercode", getEnergymetercode())
                .append("energymetername", getEnergymetername())
                .append("status", getStatus())
                .append("usagecopy", getUsagecopy())
                .append("type", getType())
                .append("stationcode", getStationcode())
                .append("stationname", getStationname())
                .append("stationlocation", getStationlocation())
                .append("stationstatus", getStationstatus())
                .append("stationtype", getStationtype())
                .append("energysupplyway", getEnergysupplyway())
                .append("powergridenergymetercode", getPowergridenergymetercode())
                .append("sitecode", getSitecode())
                .append("delFlag", getDelFlag())
                .append("createtime", getCreatetime())
                .append("syncflag", getSyncflag())
                .append("failmag", getFailmag())
                .toString();
    }
}
