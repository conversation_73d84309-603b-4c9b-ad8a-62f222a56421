package com.sccl.modules.business.powermodel.controller;

import com.sccl.common.utils.ShiroUtils;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.utils.ExcelUtil;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.autojob.util.convert.MessageMaster;
import com.sccl.modules.autojob.util.convert.StringUtils;
import com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol;
import com.sccl.modules.business.modelegetprice.domain.ModeleGetprice;
import com.sccl.modules.business.modelegetprice.service.IModeleGetpriceService;
import com.sccl.modules.business.modlebigandwork.domain.ModleBigandwork;
import com.sccl.modules.business.modlebigandwork.mapper.ModleBigandworkMapper;
import com.sccl.modules.business.modlebigandwork.service.IModleBigandworkService;
import com.sccl.modules.business.modlebigindustry.domain.ModleBigindustry;
import com.sccl.modules.business.modlebigindustry.mapper.ModleBigindustryMapper;
import com.sccl.modules.business.modlebigindustry.service.IModleBigindustryService;
import com.sccl.modules.business.modlepricesp.domain.ModlePriceSp2;
import com.sccl.modules.business.modlepricesp.mapper.ModlePricespMapper;
import com.sccl.modules.business.modleshupei.domain.ModleShupei;
import com.sccl.modules.business.modleshupei.service.IModleShupeiService;
import com.sccl.modules.business.powermodel.entity.*;
import com.sccl.modules.business.powermodel.mapper.PowerModleInitMapper;
import com.sccl.modules.business.powermodel.service.PowerModleInitService;
import com.sccl.modules.system.attachments.domain.UpLoadFile;
import com.sccl.modules.system.role.domain.Role;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.service.IUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static java.util.stream.Collectors.*;

/**
 * 电量模型原始数据(PowerModleInit)表控制层
 *
 * <AUTHOR>
 * @since 2022-10-20 10:44:40
 */
@RestController
@RequestMapping("powerModleInit")
@Slf4j
public class PowerModleInitController extends BaseController {

    private static final Map<String, String> IMPORT_COLUMN_MAP = new HashMap<>();

    static {
        IMPORT_COLUMN_MAP.put("用户名称", "username");
        IMPORT_COLUMN_MAP.put("户号", "accountno");
        IMPORT_COLUMN_MAP.put("售电公司", "elesellcompany");
        IMPORT_COLUMN_MAP.put("地市", "citycompany");
        IMPORT_COLUMN_MAP.put("县级", "countylevelcompany");
        IMPORT_COLUMN_MAP.put("电压", "voltagelevel");
        IMPORT_COLUMN_MAP.put("结算品种", "sttype");
        IMPORT_COLUMN_MAP.put("用户类别", "ueertype");
        IMPORT_COLUMN_MAP.put("年", "year");
        IMPORT_COLUMN_MAP.put("月", "month");
        IMPORT_COLUMN_MAP.put("用电量", "powercSize");
        IMPORT_COLUMN_MAP.put("水电消纳计量点量", "sizeHydropower");
        IMPORT_COLUMN_MAP.put("水电消纳用电量", "powerHydropower");
        IMPORT_COLUMN_MAP.put("电能替代用电量", "powerDntd");
        IMPORT_COLUMN_MAP.put("工业电量", "powercIndustrySize");
        IMPORT_COLUMN_MAP.put("非工业电量", "powercIndustrySizeNot");
        IMPORT_COLUMN_MAP.put("精准长协电量", "powerAccurateLong");
        IMPORT_COLUMN_MAP.put("常规长协电量", "powerRoutineLong");
        IMPORT_COLUMN_MAP.put("留存电量", "powerRetained");
        IMPORT_COLUMN_MAP.put("富余基数", "surplusBase");
        IMPORT_COLUMN_MAP.put("(常规直购)总结算电量", "powerTotalSize");
        IMPORT_COLUMN_MAP.put("(常规直购)总结算均价", "averagepriceTotal");
        IMPORT_COLUMN_MAP.put("(常规直购)直接交易结算电量", "powerTotalSize1");
        IMPORT_COLUMN_MAP.put("(常规直购)直接交易结算均价", "averagepriceTotal1");
        IMPORT_COLUMN_MAP.put("用户实际承担考核费用", "priceUserActual");
        IMPORT_COLUMN_MAP.put("(精准长协)总结算均价", "averagepriceTota2");
        IMPORT_COLUMN_MAP.put("(精准长协)总结算电量", "powerTotalSize2");
    }

    @Autowired
    private ModleBigindustryMapper bigindustryMapper;
    /**
     * 服务对象
     */
    @Autowired
    private PowerModleInitService powerModleInitService;
    @Autowired
    private IUserService userService;

    @Autowired
    private PowerModleInitMapper powerModleInitMapper;

    @Autowired
    private IModeleGetpriceService modeleGetpriceService;
    @Autowired
    private IModleShupeiService modleShupeiService;
    @Autowired
    private IModleBigindustryService modleBigindustryService;
    @Autowired
    private ModleBigandworkMapper bigandworkMapper;
    @Autowired
    private IModleBigandworkService bigandworkService;
    @Autowired
    private ModlePricespMapper pricespMapper;

    public static String splitData(String str, String strStart, String strEnd) {
        String tempStr;
        tempStr = str.substring(str.indexOf(strStart) + 1, str.lastIndexOf(strEnd));
        return tempStr;
    }

    /**
     * 新增数据
     *
     * @param powerModleInit 实体
     * @return 新增结果
     */
/*    @PostMapping
    public ResponseEntity<PowerModleInit> add(PowerModleInit powerModleInit) {
        return ResponseEntity.ok(this.powerModleInitService.insert(powerModleInit));
    }*/
    public static void main(String[] args) {
        HashMap<String, String> map = new HashMap<>();
        map.put("1", null);
    }


    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody(required = false) List<PowerModleInit> powerModleInits) {

        return toAjax(powerModleInitService.updateForModelList(powerModleInits));
    }


    /**
     * 户号未找到 -> 查询地市电表
     *
     * @param powerModleInit
     * @param pageRequest
     * @return
     */
    @PostMapping("/queryMeter")
    public AjaxResult queryMeter(Ammeterorprotocol msg) {
        //权限判定 组装
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role
                    .getCode()
                    .startsWith("PROVI_")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role
                    .getCode()
                    .startsWith("admin")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role
                    .getCode()
                    .startsWith("CITY_") || role
                    .getCode()
                    .startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
        }
        if (isProAdmin) {
            msg.setCompany(null);
            //  查询权限设置 分公司
        } else if (isCityAdmin) {
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0) msg.setCompany(Long.valueOf(companies
                    .get(0)
                    .getId()));
        }
        startPage();
        msg.setDirectFlag(0);
        return powerModleInitService.queryMeter(msg);
    }

    /**
     * 户号未找到 -> 新增电表供电局编号
     *
     * @param powerModleInit
     * @param pageRequest
     * @return
     */
    @PostMapping("/updateMeter")
    public AjaxResult updateMeter(@RequestBody List<Ammeterorprotocol> ammeterorprotocols) {
        return powerModleInitService.updateMeter(ammeterorprotocols);
    }

    /**
     * 维护供电局编号 -> 查询地市需要维护的电表信息
     *
     * @param ammeterorprotocols
     * @return
     */
    @PostMapping("/quetyMeter")
    public AjaxResult quetyMeter(Ammeterorprotocol msg) {
        //权限判定 组装
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role
                    .getCode()
                    .startsWith("PROVI_")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role
                    .getCode()
                    .startsWith("admin")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role
                    .getCode()
                    .startsWith("CITY_") || role
                    .getCode()
                    .startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
        }
        if (isProAdmin) {
            msg.setCompany(null);
            //  查询权限设置 分公司
        } else if (isCityAdmin) {
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0) msg.setCompany(Long.valueOf(companies
                    .get(0)
                    .getId()));
        }
        startPage();
        return powerModleInitService.quetyMeter(msg);
    }

    /**
     * 维护供电局编号 -> 更新电表信息
     *
     * @param ammeterorprotocols
     * @return
     */
    @PostMapping("/updateMeterend")
    public AjaxResult updateMeterend(@RequestBody List<Ammeterorprotocol> msg) {
        return powerModleInitService.updateMeterend(msg);
    }

    /**
     * 分页查询
     *
     * @param powerModleInit 筛选条件
     * @param pageRequest    分页对象
     * @return 查询结果
     */
    @GetMapping("/page")
    public ResponseEntity<Page<PowerModleInit>> queryByPage(PowerModleInit powerModleInit, PageRequest pageRequest) {
        return ResponseEntity.ok(this.powerModleInitService.queryByPage(powerModleInit, pageRequest));
    }


    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("{id}")
    public ResponseEntity<PowerModleInit> queryById(@PathVariable("id") Long id) {
        return ResponseEntity.ok(this.powerModleInitService.queryById(id));
    }

    /**
     * @param powerModleInits 批量添加数据
     * @return
     */
    @PostMapping("/bitch")
    public AjaxResult bitchAdd(@RequestBody ArrayList<PowerModleInit> powerModleInits) {
        System.out.println(1);
        return AjaxResult.success("保存成功-条数：" + this.powerModleInitService.batchAdd(powerModleInits));
    }

    /**
     * 删除数据
     *
     * @param id 主键
     * @return 删除是否成功
     */
    @DeleteMapping
    public ResponseEntity<Boolean> deleteById(Long id) {
        return ResponseEntity.ok(this.powerModleInitService.deleteById(id));
    }

    /**
     * 电量模型初始数据导入
     *
     * @param request
     * @param response
     * @param uploadFile
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/uploadExcel")
    public AjaxResult uploadExcel(HttpServletRequest request, HttpServletResponse response, UpLoadFile uploadFile) throws Exception {
        response.setContentType("text/html;charset=utf-8");
        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        Iterator<String> iterator = multiRequest.getFileNames();

        while (iterator.hasNext()) {
            String name = iterator.next();
            List<MultipartFile> files = new LinkedList<>();
            files = multiRequest.getFiles(name);
            // 一次遍历所有文件
            for (MultipartFile file : files) {
                if (file == null) {
                    continue;
                }

                //如果文件大小为0则不上传
                long fileSize = file.getSize();
                if (fileSize <= 0L) {
                    throw new Exception("文件名称：【" + file
                            .getOriginalFilename()
                            .substring(
                                    file.getOriginalFilename().lastIndexOf("\\") + 1,
                                    file.getOriginalFilename().length()) + "】的文件大小为0,请核对!");
                }

                ExcelUtil<PowerModleInit> excelUtil = new ExcelUtil<PowerModleInit>(PowerModleInit.class);
//                List<PowerModleBase> list=excelUtil.importExcel("结算数据", file.getInputStream());
                MultipartFile multipartFile = getMultipartFile(file.getInputStream(), "结算数据");

                try {
                    log.info("结算数据excel转化为内存content");
                    List<PowerModleInit> content = getPowerModleInits(excelUtil, multipartFile);
                    log.info("转化完毕,共{}条结算数据", content.size());

                    log.info("content数据校验");
                    if (CollectionUtils.isEmpty(content)) {
                        return AjaxResult.success("空数据");
                    }
                    log.info("1.户号空校验");
                    boolean b = content
                            .stream()
                            .anyMatch(init -> init.getAccountno() == null);
                    if (b) {
                        log.info("户号校验失败");
                        return AjaxResult.error("当前导入数据存在户号为空的数据，请检查后导入");
                    }
                    //2.户号有效性校验
                    //2.1户号存在并有效
                    //2.1.1 数据准备 account::list<ammeters>::list<account>

                    String year = content.get(0).getYear();
                    String month = content.get(0).getMonth();
                    int month1 = Integer.parseInt(month);
                    month = month1 < 10 ? "0" + month : month;
                    String budget = year + "-" + month;

                    List<String> accounts = content
                            .stream()
                            .map(PowerModleInit::getAccountno)
                            .distinct()
                            .collect(toList());
                    log.info("获取结算数据判断户号是否存在的数据");
                    List<Ammeterorprotocol> ammeterorprotocols = getAmmeterorprotocols(accounts);
                    log.info("获取结算数据判断是否存在报账记录的数据");
                    List<SupplyCodeMapBill> supplyCodeMapBills = getSupplyCodeMapBills(accounts, budget);

                    Map<String, List<Ammeterorprotocol>> ammeterorprotocolMap = ammeterorprotocols
                            .stream()
                            .distinct()
                            .collect(groupingBy(Ammeterorprotocol::getSupplybureauammetercode));

                    Map<String, List<SupplyCodeMapBill>> supplyCodeBillMap = supplyCodeMapBills.stream().collect(
                            groupingBy(SupplyCodeMapBill::getSupplybureauammetercode)
                    );
                    log.info("开始判定户号存在，账期记录");
                    content.stream().forEach(init -> powerModleInitService.checkAndInput(init, budget, ammeterorprotocolMap, supplyCodeBillMap));
                    log.info("判定结束");
                    ////2.2相同户号收敛
                    //Map<String, List<PowerModleInit>> map = content
                    //  .stream()
                    //  .collect(groupingBy(init -> init.getAccountno()));
                    //
                    //content.clear();
                    //List<PowerModleInit> finalContent = content;
                    //map.forEach((k, v) -> {
                    //    PowerModleInit reduce = v
                    //      .stream()
                    //      .reduce(new PowerModleInit(), PowerModleInitService::reduce);
                    //    finalContent.add(reduce);
                    //});
                    //orgCodeMap accountNo orgCode
                    log.info("设置结算数据大工业标识，如果为大工业，默认为容量法");
                    powerModleInitService.SetBigWord(content);
                    log.info("设置结算数据部门编码");
                    content = setOrgCode(content);

                    if (org.springframework.util.CollectionUtils.isEmpty(content)) {
                        return AjaxResult.error("文件内容为空，请检查格式或内容");
                    }


                    //2.3 依据 username+accno+year+month 将原表中重复的逻辑删除
                    //2.3.1 拿到当前数据对应的原表数据
                    List<String> userNames = content
                            .stream()
                            .map(PowerModleInit::getUsername)
                            .distinct()
                            .collect(toList());
                    if (CollectionUtils.isEmpty(userNames)) {
                        return AjaxResult.error("\"错误，检查你的用户名称是否有遗漏\"");
                    }
                    List<PowerModleInit> existContent = powerModleInitMapper.selectExistContent(year, month, userNames);
                    List<PowerModleInit> finalContent1 = content;
                    List<PowerModleInit> updateContent = existContent
                            .stream()
                            .filter(init -> init.existKey(finalContent1))
                            .map(init -> {
                                init.setDelflag(1);
                                return init;
                            })
                            .collect(toList());
                    //2.4.1 更新powerinit 原来表数据
                    int updateRepeatNum = 0;
                    if (CollectionUtils.isNotEmpty(updateContent)) {
                        updateRepeatNum = powerModleInitMapper.updateForRepeat(updateContent);
                    }

                    //导入基础表数据
                    Integer count = powerModleInitService.batchAdd(content);


//            3基础表 power_mode_biaAndWork的更新
//            3.1导入户号不存在原表的
//                    List<String> accountNos = bigandworkMapper.selectAll();
//                    List<PowerModleInit> list = content
//                      .stream()
//                      .filter(powerModleInit -> !accountNos.contains(powerModleInit.getAccountno()))
//                      .collect(toList());
//                    //orgCode
//                    String orgCode = null;
//                    List<IdNameVO> companies = ShiroUtils
//                      .getUser()
//                      .getCompanies();
//                    if (CollectionUtils.isEmpty(companies)) {
//                        orgCode = "-1";
//                    } else {
//                        orgCode = companies
//                          .get(0)
//                          .getId();
//                    }
//                    String finalOrgCode = orgCode;
//                    List<ModleBigandwork> bigandworks = list
//                      .stream()
//                      .map(powerModleInit -> {
//                          ModleBigandwork bigandwork = new ModleBigandwork();
//                          bigandwork.setDelFlag("0");
//                          bigandwork.setCreateTime(new Date());
//                          bigandwork.setUpdatetime(new Date());
//                          bigandwork.setAccountno(powerModleInit.getAccountno());
//                          bigandwork.setOrgcode(finalOrgCode);
//                          return bigandwork;
//                      })
//                      .collect(toList());
//
//                    int i = 0;
//                    if (CollectionUtils.isNotEmpty(bigandworks)) {
//                        i = bigandworkMapper.insertList(bigandworks);
//                    }
                    log.info("导入单价基础表条数:{}\n去除原表重复数据: {}", count, updateRepeatNum);
                    return AjaxResult.success("导入成功");

                } catch (Exception e) {
                    e.printStackTrace();
                    return AjaxResult.error("导入失败" + e.getMessage());
                }
            }

        }
        return AjaxResult.success("导入成功");
    }

    @NotNull
    private static List<PowerModleInit> getPowerModleInits(ExcelUtil<PowerModleInit> excelUtil, MultipartFile multipartFile) throws Exception {
        List<PowerModleInit> content = excelUtil.importExcel("结算数据", multipartFile, IMPORT_COLUMN_MAP);
        User user = ShiroUtils.getUser();
        content = content
                .stream()
                .map(powerModleInit -> {
                    powerModleInit.setLoginId(user.getLoginId());
                    powerModleInit.setCreatetime(LocalDateTime.now());
                    powerModleInit.setUpdatetime(LocalDateTime.now());
                    powerModleInit.setDelflag(0);
                    return powerModleInit;
                })
                .collect(toList());
        return content;
    }

    @NotNull
    private List<PowerModleInit> setOrgCode(List<PowerModleInit> content) {
        List<PowerModleInit> finalContent2 = content;

        int batchSize = 200;
        List<List<PowerModleInit>> batches = IntStream.range(0, (content.size() + batchSize - 1) / batchSize)
                .mapToObj(i -> finalContent2.subList(i * batchSize, Math.min((i + 1) * batchSize, finalContent2.size())))
                .collect(Collectors.toList());

        List<PowerModleOrgCode> orgCodes = new ArrayList<>();
        batches.forEach(
                item -> {
                    List<PowerModleOrgCode> temp = powerModleInitMapper.selectOrgCode(item);
                    orgCodes.addAll(temp);
                }
        );

        List<PowerModleOrgCode> orgcodefilter = orgCodes.stream().filter(
                        item -> StringUtils.isNotBlank(item.getAccountNo()) && StringUtils.isNotBlank(item.getOrgCode()))
                .collect(toList());

        Map<String, String> orgCodeMap = orgcodefilter
                .stream()
                .collect(toMap(PowerModleOrgCode::getAccountNo,
                        PowerModleOrgCode::getOrgCode,
                        (item1, item2) -> item1));
        content = content
                .stream()
                .map(powerModleInit -> {
                    powerModleInit.setOrgCode(orgCodeMap.get(powerModleInit.getAccountno()));
                    return powerModleInit;
                })
                .collect(toList());
        return content;
    }

    private List<Ammeterorprotocol> getAmmeterorprotocols(List<String> accounts) {
        List<Ammeterorprotocol> ammeterorprotocols = new ArrayList<>();
        int bitchsize = 500;
        for (int i = 0; i < accounts.size(); i += bitchsize) {
            int endindex = Math.min(i + bitchsize, accounts.size());
            List<String> accountTemp = accounts.subList(i, endindex);
            List<Ammeterorprotocol> ammeterorprotocolsTemp = powerModleInitMapper.selectAmmeterorprotocolsForContion2(accounts);
            ammeterorprotocols.addAll(ammeterorprotocolsTemp);
        }
        return ammeterorprotocols;
    }

    private List<SupplyCodeMapBill> getSupplyCodeMapBills(List<String> accounos, String budget) {
        List<SupplyCodeMapBill> supplyCodeMapBills = new ArrayList<>();
        int bitchsize = 1000;
        for (int i = 0; i < accounos.size(); i += bitchsize) {
            int endindex = Math.min(i + bitchsize, accounos.size());
            List<String> accounoTempList = accounos.subList(i, endindex);
            List<SupplyCodeMapBill> tempList = powerModleInitMapper.selectSupplyCodeMapBill(accounoTempList, budget);
            supplyCodeMapBills.addAll(tempList);
        }
        return supplyCodeMapBills;
    }

    /**
     * 基础数据户号重新稽核
     */
    @PostMapping("/reloadAuditinit/{auditType}")
    public AjaxResult reloadAuditinit(@RequestBody(required = false) PowerModleInit powerModleInit, @PathVariable(required = false) String auditType) {
        if (auditType == null) {
            auditType = "all";
        }
        if ("accnos".equals(auditType)) {
            if (CollectionUtils.isEmpty(powerModleInit.getAccnos())) {
                return AjaxResult.success("无户号要重新稽核");
            }
        }

        return powerModleInitService.reloadAuditinit(powerModleInit, auditType);

    }


    /**
     * 电价基础数据导入
     *
     * @param multipartFile
     * @param sheetName
     * @return
     */
    @GetMapping(value = "/import_excel", produces = "application/json;charset=UTF-8")
    public String importExcel(HttpServletRequest request, @RequestParam(required = false, value = "sheet_name") String sheetName) {
        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        MultipartFile multipartFile = multiRequest.getFile("file");
        if (multipartFile == null || multipartFile.isEmpty()) {
            return MessageMaster.DefaultMessage.EMPTY_PARAMS.toString();
        }
        ExcelUtil<PowerModleInit> excelUtil = new ExcelUtil<>(PowerModleInit.class);
        try {

            User user = ShiroUtils.getUser();
            List<PowerModleInit> content = excelUtil.importExcel("Sheet1", multipartFile, IMPORT_COLUMN_MAP);
            content = content
                    .stream()
                    .map(powerModleInit -> {
                        powerModleInit.setLoginId(user.getLoginId());
                        powerModleInit.setCreatetime(LocalDateTime.now());
                        powerModleInit.setUpdatetime(LocalDateTime.now());
                        powerModleInit.setDelflag(0);
                        return powerModleInit;
                    })
                    .collect(toList());
            //数据校验
            //1. 对不存在的户号给出提示,并强制返回
            boolean b = content
                    .stream()
                    .anyMatch(init -> init.getAccountno() == null);
            if (b) {
                return "当前导入数据存在户号为空的数据，请检查后导入";
            }
            //2.户号有效性校验
            //2.1户号存在并有效
//            content.stream().forEach(
//                    init ->
//                            powerModleInitService.checkAndInput(init, ammeterorprotocolMap, accountMap)
//            );
//            //2.2相同户号收敛
//            Map<String, List<PowerModleInit>> map =
//                    content.stream().collect(groupingBy(init -> init.getAccountno()));
//
//            content.clear();
//            List<PowerModleInit> finalContent = content;
//            map.forEach(
//                    (k, v) -> {
//                        PowerModleInit reduce = v.stream().reduce(new PowerModleInit(),
//                        PowerModleInitService::reduce);
//                        finalContent.add(reduce);
//                    }
//            );
            //orgCodeMap accountNo orgCode
            List<PowerModleOrgCode> orgCodes = powerModleInitMapper.selectOrgCode(content);
            Map<String, String> orgCodeMap = orgCodes
                    .stream()
                    .collect(toMap(PowerModleOrgCode::getAccountNo, PowerModleOrgCode::getOrgCode));
            content = content
                    .stream()
                    .map(powerModleInit -> {
                        powerModleInit.setOrgCode(orgCodeMap.get(powerModleInit.getAccountno()));
                        return powerModleInit;
                    })
                    .collect(toList());

            if (org.springframework.util.CollectionUtils.isEmpty(content)) {
                return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "文件内容为空，请检查格式或内容");
            }


            Integer count = powerModleInitService.batchAdd(content);

//            3基础表 power_mode_biaAndWork的更新
//            3.1导入户号不存在原表的
//            List<String> accountNos = bigandworkMapper.selectAll();
//            List<PowerModleInit> list =
//                    content.stream().filter(powerModleInit -> !accountNos.contains(powerModleInit.getAccountno()))
//                    .collect(toList());
//            List<ModleBigandwork> bigandworks = list.stream().map(
//                    powerModleInit -> {
//                        ModleBigandwork bigandwork = new ModleBigandwork();
//                        bigandwork.setDelFlag("0");
//                        bigandwork.setCreateTime(new Date());
//                        bigandwork.setUpdatetime(new Date());
//                        bigandwork.setAccountno(powerModleInit.getAccountno());
////                        bigandworkService.setInfoForAmmeter(bigandwork);
//                        return bigandwork;
//                    }
//            ).collect(toList());
//
//            int i = 0;
//            if (CollectionUtils.isNotEmpty(bigandworks)) {
//                i = bigandworkMapper.insertList(bigandworks);
//            }
//            log.info("导入单价-大工业-办公表条数：{}\n导入单价基础表条数:{}", i, count);
            return MessageMaster.getMessage(MessageMaster.Code.OK, "导入成功");
        } catch (Exception e) {
            e.printStackTrace();
            return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "导入错误：" + e.getMessage());
        }
    }

    /**
     * 计算 电量电价 ，并且保存到数据库
     *
     * @param content
     * @return
     */
    @PostMapping("calcandsave")
    public AjaxResult calcAndSave(@RequestBody List<PowerModleInit> content) {

        //计算各单价
        ExecutorService pool = Executors.newFixedThreadPool(Runtime
                .getRuntime()
                .availableProcessors() - 1);
        List<CompletableFuture<PowerModleInit>> futures = content
                .stream()
                .map(init -> {
                    CompletableFuture<PowerModleInit> future = CompletableFuture.supplyAsync(() -> {
                        return getPowerModleInit(init);
                    }, pool);
                    return future;
                })
                .collect(toList());
        content = futures
                .stream()
                .map(CompletableFuture::join)
                .collect(toList());
        try {
            pool.awaitTermination(600, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            pool.shutdownNow();
        } finally {
            pool.shutdownNow();
        }
        int n = powerModleInitService.replaceList(content);
        log.info("计算了{}条电价，保存了{}条计算后的电价", content.size(), n);
        return AjaxResult.success("计算了" + content.size() + "条单价");
    }


    public AjaxResult calcAndSave2(@RequestBody List<PowerModleInit> content, @RequestParam(value = "dire") String direFlag, @RequestParam(value = "work") String workFlag, @RequestParam(value = "voltageLevel") Integer voltageLevel, @RequestParam(value = "industrialUse") String industrialUse) {
        //1.先拿到单价计算从官网拿到的数据
        PowerModleInit modleInit = content
                .stream()
                .filter(powerModleInit -> powerModleInit.check1())
                .findFirst()
                .get();
        String province = modleInit
                .getElesellcompany()
                .substring(0, 2);
        String year = modleInit.getYear();
        String month = modleInit.getMonth();

        //国网代购 损益
        ModeleGetprice modeleGetprice = new ModeleGetprice();
        modeleGetprice.setProvince(province);
        modeleGetprice.setYear(year);
        modeleGetprice.setMonth(month);
        ModeleGetprice modeleGetprice1 = this.modeleGetpriceService.selectByLatest(modeleGetprice);

        BigDecimal indirectcountry = modeleGetprice1.getIndirectcountry();
        BigDecimal profitloss = modeleGetprice1.getProfitloss();
        BigDecimal appendFund = modeleGetprice1.getAppendFund();

        //2. 先计算
        //2.1 多线程1


        content = content
                .stream()
                .map(init -> {

                    //输配
                    ModleShupei modleShupei = new ModleShupei();
                    modleShupei.setVoltagelevel(voltageLevel);
                    modleShupei.setIndustrialuse(industrialUse);
                    ModleShupei modleShupei1 = modleShupeiService.selectByLatest(modleShupei);
                    BigDecimal pricesp = modleShupei1.getPricesp();
                    //设置默认值
                    init.setAppendFund(appendFund);
                    init.setGetDire(1);
                    init.setWork(Integer.valueOf(workFlag));
                    init.setVoltagelevel(Double.valueOf(voltageLevel));
                    init.setIndirectCountry(indirectcountry);
                    init.setProfitLoss(profitloss);
                    init.setPriceSp(pricesp);

                    String province1 = init
                            .getElesellcompany()
                            .substring(0, 2);
                    String city1 = StringUtils.splitData(init.getCitycompany(), "网", "供");
                    String year1 = init.getYear();
                    String month1 = init.getMonth();
                    ModleBigindustry bigindustry = new ModleBigindustry();
                    bigindustry.setProvince(province1);
                    bigindustry.setCity(city1);
                    bigindustry = bigindustryMapper.selectTheLatest(bigindustry);
                    BigDecimal capacitydemandbig1 = bigindustry.getCapacitydemandbig1();
                    BigDecimal capacitydemandbig2 = bigindustry.getCapacitydemandbig2();
                    init.setCapacityDemandBig(capacitydemandbig1 == null && capacitydemandbig2 == null ? BigDecimal.ZERO : capacitydemandbig1 == null ? capacitydemandbig2 : capacitydemandbig1);
                    init.setCapacityDemandBigFlag(capacitydemandbig1 == null && capacitydemandbig2 == null ? -1 : capacitydemandbig1 == null ? 1 : 2);


                    //国网代购（无办公）
                    //国网代购价+基金附加费+大工业基本电费折算单价（市州填写）+ 输配电价+损益
                    //大工业基本电费折算单价（市州填写）=大工业容量/需量（需填） * 22 /  用电量
                    init.setPriceCountryProxy(powerModleInitService.calcPriceContryProxy(init));

                    //直购电实际（无办公）
                    //输配电价+损益+基金附加费+大工业基本电费折算单价（市州填写）+常规直购)总结算均价
                    init.setPriceDirectpurele(powerModleInitService.calcPriceDirectpurele(init));

                    //国网代购分峰平谷单价（有办公）
                    //(((国网代购价+输配电价)*1.6+基金附加费+大工业基本电费折算单价（市州填写）)*峰电量1.6)+((大工业基本电费折算单价（市州填写）+基金附加费+输配电价+国网代购价)
                    // *平电量1)+(((国网代购价+输配电价)\*0.4+基金附加费+大工业基本电费折算单价（市州填写）)*谷电量0.4))/用电量
                    //大工业基本电费折算单价（市州填写）=大工业容量/需量（需填） * 22 /  用电量
                    init.setPriceCountryProxyTimeshar(powerModleInitService.calcPriceCountryProxyTimeshar(init));

                    //直购电理论电价测算（不分合同内外、不含富余、分峰平谷）
                    //(((((输配电价+直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余）+损益)\*1.6)+大工业基本电费折算单价（市州填写）+基金附加费)\*峰电量1.6)+(
                    // (输配电价+损益+基金附加费+大工业基本电费折算单价（市州填写）+直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余）)\*平电量1)+(
                    // (输配电价+直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余）+损益)\*0.4+大工业基本电费折算单价（市州填写）+基金附加费)\*谷电量0.4)/(峰电量1
                    // .6+平电量1+谷电量0.4)
                    init.setPriceDirectpureleEstimate(powerModleInitService.calcPriceDirectpureleEstimate(init));

                    //直购电收益
                    //是否 参加直购电
                    // 参加 办公
                    // (国网代购分峰平谷单价-直购电理论电价测算（不分合同内外、不含富余、分峰平谷）)*用电量
                    //无 办公
                    //   (国网代购测算值单价-直购电（实际）无办公)*(常规直购)总结算电量
                    init.setProfitDire(powerModleInitService.caclProfitDire(init));

                    return init;
                })
                .collect(toList());

        int n = powerModleInitService.replaceList(content);
        log.info("计算了{}条电价，保存了{}条计算后的电价", content.size(), n);
        return AjaxResult.success("计算了" + content.size() + "条单价");
    }

//    @PostMapping("calcandsave2")
//    public AjaxResult calcAndSave3(@RequestBody PowerModleInit powerModleInitTemp) {
//        //1.先拿到单价计算从官网拿到的数据
//        PowerModleInit powerModleInit1 = new PowerModleInit();
//        powerModleInit1.setDelflag(0);
//        powerModleInit1.setOrgCode(powerModleInitTemp.getOrgCode());
//        powerModleInit1.setYear(powerModleInitTemp.getYear());
//        powerModleInit1.setMonth(powerModleInitTemp.getMonth());
//        List<PowerModleInit> content =
//                powerModleInitMapper.selectList(powerModleInit1);
//        //为空返回
//        if (CollectionUtils.isEmpty(content)) {
//            return AjaxResult.success("此地市当前账期无单价基础数据，请导入后在计算");
//        }
//        boolean b = content.stream().allMatch(powerModleInit -> powerModleInit.getCapacityDemandBigFlag() == null);
//        if (b) {
//            return AjaxResult.success("此地市所有数据均未选择变压器计算方式");
//        }
//        PowerModleInit modleInit =
//                content.stream().filter(powerModleInit -> powerModleInit.check1()).findFirst().get();
//        String province = modleInit.getElesellcompany().substring(0, 2);
//        String year = modleInit.getYear();
//        String month = modleInit.getMonth();
//
//        //国网代购 损益 基金附加费
//        ModeleGetprice modeleGetprice = new ModeleGetprice();
//        modeleGetprice.setProvince(province);
//        modeleGetprice.setYear(year);
//        modeleGetprice.setMonth(month);
//        ModeleGetprice modeleGetprice1 = this.modeleGetpriceService.selectByLatest(modeleGetprice);
//        if (modeleGetprice1 == null) {
//            return AjaxResult.success(String.format("%s省%s年%s月的国网代购数据不存在，请先维护", province, year, month));
//        }
//        BigDecimal indirectcountry = modeleGetprice1.getIndirectcountry();
//        BigDecimal profitloss = modeleGetprice1.getProfitloss();
//        BigDecimal appendFund = modeleGetprice1.getAppendFund();
//
//        //输配,直购电，办公
//        List<String> accountNos = content.stream().map(PowerModleInit::getAccountno).collect(toList());
//        List<ModlePriceSp2> priceSp2s = pricespMapper.selctPrisp(accountNos);
//        if (CollectionUtils.isEmpty(priceSp2s)) {
//            return AjaxResult.success(String.format("当前计算的所有户号无对应的输配信息，请维护电表基础信息或者输配信息"));
//        }
//        Map<String, ModlePriceSp2> priceSp2Map = priceSp2s.stream().distinct().collect(
//                toMap(
//                        ModlePriceSp2::getAccountNo,
//                        modlePriceSp2 -> modlePriceSp2
//                )
//        );
//        //大工业
//        List<ModleBigandwork> bigandworks = bigandworkMapper.selectListByAccountNo(accountNos);
//        bigandworks = bigandworks.stream()
//                                 .filter(ModleBigandwork::checkBig).collect(toList());
//        if (CollectionUtils.isEmpty(bigandworks)) {
//            return AjaxResult.success(String.format("当前计算所有户号无对应大工业信息，请维护"));
//        }
//        Map<String, ModleBigandwork> bigandworkMap = bigandworks.stream().collect(toMap(
//                ModleBigandwork::getAccountno,
//                modleBigandwork -> modleBigandwork
//        ));
//
//        //峰平谷
//        //2. 先计算
//        //2.1开线程执行
//        //2.1.1可使用cpu核数-1
//        ExecutorService pool = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors() - 1);
//        List<Future> futures = new ArrayList<>();
//        content.stream().forEach(
//                init -> {
//                    Future<?> future = pool.submit(
//                            () -> {
//                                long s = System.currentTimeMillis();
//                                //设置默认值
//                                //国网代购价 损益 基金附加费
//                                init.setAppendFund(appendFund);
//                                init.setProfitLoss(profitloss);
//                                init.setIndirectCountry(indirectcountry);
//                                //输配 是否直购电 是否办公
//                                ModlePriceSp2 sp2 = priceSp2Map.get(init.getAccountno());
//                                if (sp2 != null) {
//                                    init.setPriceSp(sp2.getPriceSp());
//                                }
//                                //缺省 办公/直购电
//                                init.setWork(sp2 != null ? sp2.getWork() : 1);
//                                init.setGetDire(sp2 != null ? sp2.getDire() : 1);
//                                //大工业
//                                ModleBigandwork bigandwork = bigandworkMap.get(init.getAccountno());
//                                if (bigandwork != null) {
//                                    Integer bigFlag = init.getCapacityDemandBigFlag();
//                                    init.setCapacityDemandBig(bigFlag == 0 ? bigandwork.getCapacitydemandbig1() :
//                                                                      bigandwork.getCapacitydemandbig2());
//                                }
////                    String province1 = init.getElesellcompany().substring(0, 2);
////                    String city1 = StringUtils.splitData(init.getCitycompany(), "网", "供");
////                    String year1 = init.getYear();
////                    String month1 = init.getMonth();
////                    ModleBigindustry bigindustry = new ModleBigindustry();
////                    bigindustry.setProvince(province1);
////                    bigindustry.setCity(city1);
////                    bigindustry = bigindustryMapper.selectTheLatest(bigindustry);
////                    BigDecimal capacitydemandbig1 = bigindustry.getCapacitydemandbig1();
////                    BigDecimal capacitydemandbig2 = bigindustry.getCapacitydemandbig2();
////                    init.setCapacityDemandBig(
////                            capacitydemandbig1 == null && capacitydemandbig2 == null ? BigDecimal.ZERO :
////                                    capacitydemandbig1 == null ? capacitydemandbig2 : capacitydemandbig1
////                    );
////                    init.setCapacityDemandBigFlag(
////                            capacitydemandbig1 == null && capacitydemandbig2 == null ? -1 :
////                                    capacitydemandbig1 == null ? 1 : 0
////                    );
//
//
//                                //国网代购（无办公）
//                                //国网代购价+基金附加费+大工业基本电费折算单价（市州填写）+ 输配电价+损益
//                                //大工业基本电费折算单价（市州填写）=大工业容量/需量（需填） * 22 /  用电量
//                                init.setPriceCountryProxy(powerModleInitService.calcPriceContryProxy(init));
//
//                                //直购电实际（无办公）
//                                //输配电价+损益+基金附加费+大工业基本电费折算单价（市州填写）+常规直购)总结算均价
//                                init.setPriceDirectpurele(powerModleInitService.calcPriceDirectpurele(init));
//
//                                //国网代购分峰平谷单价（有办公）
//                                //(((国网代购价+输配电价)*1.6+基金附加费+大工业基本电费折算单价（市州填写）)*峰电量1.6)+(
//                                // (大工业基本电费折算单价（市州填写）+基金附加费+输配电价+国网代购价)
//                                // *平电量1)+(((国网代购价+输配电价)\*0.4+基金附加费+大工业基本电费折算单价（市州填写）)*谷电量0.4))/用电量
//                                //大工业基本电费折算单价（市州填写）=大工业容量/需量（需填） * 22 /  用电量
//                                init.setPriceCountryProxyTimeshar(powerModleInitService
//                                .calcPriceCountryProxyTimeshar(init));
//
//                                //直购电理论电价测算（不分合同内外、不含富余、分峰平谷）
//                                //(((((输配电价+直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余）+损益)\*1.6)+大工业基本电费折算单价（市州填写）+基金附加费)\*峰电量1
//                                .6)+(
//                                // (输配电价+损益+基金附加费+大工业基本电费折算单价（市州填写）+直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余）)\*平电量1)+(
//                                // (输配电价+直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余）+损益)\*0.4+大工业基本电费折算单价（市州填写）+基金附加费)\*谷电量0.4)/
//                                (峰电量1
//                                // .6+平电量1+谷电量0.4)
//                                init.setPriceDirectpureleEstimate(powerModleInitService
//                                .calcPriceDirectpureleEstimate(init));
//
//                                //直购电收益
//                                //是否 参加直购电
//                                // 参加 办公
//                                // (国网代购分峰平谷单价-直购电理论电价测算（不分合同内外、不含富余、分峰平谷）)*用电量
//                                //无 办公
//                                //   (国网代购测算值单价-直购电（实际）无办公)*(常规直购)总结算电量
//                                init.setProfitDire(powerModleInitService.caclProfitDire(init));
//                                long e = System.currentTimeMillis();
//                                log.info("耗时：{}", (e - s));
//                            }
//                    );
//                    futures.add(future);
//                }
//        );
//
//        while (!futures.stream().allMatch(Future::isDone)) {
//            try {
//                Thread.sleep(10000L);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
//            log.info("效益分析中");
//        }
////        content.stream().map(
////                init -> {
////                    return getPowerModleInit(indirectcountry, profitloss, appendFund, priceSp2Map, bigandworkMap,
////                    init);
////                }
////
////        ).collect(toList());
//
//        int n = powerModleInitService.replaceList(content);
//        log.info("计算了{}条电价，保存了{}条计算后的电价", content.size(), n);
//        return AjaxResult.success("计算了" + content.size() + "条单价");
//    }

    @PostMapping("calcandsave2")
    public AjaxResult calcAndSave3(@RequestBody PowerModleInit powerModleInitTemp) {
        //
        log.info("权限判定 组装");
        setorgCode(powerModleInitTemp);
        log.info("效益分析company={}", powerModleInitTemp.getOrgCode());
        return powerModleInitService.calcByinit(powerModleInitTemp);
    }

    private void setorgCode(PowerModleInit powerModleInitTemp) {
        if (StringUtils.isBlank(powerModleInitTemp.getOrgCode()) && StringUtils.isBlank(powerModleInitTemp.getCompany())) {
            User user = ShiroUtils.getUser();
            List<Role> roles = userService.selectUserRole(user.getId());
            boolean isProAdmin = false;
            boolean isCityAdmin = false;
            boolean isSubAdmin = false;
            for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
                if (role
                        .getCode()
                        .startsWith("admin")) {//省能耗费管理员
                    isProAdmin = true;
                }
                if (role
                        .getCode()
                        .startsWith("PROVI_")) {//省能耗费管理员
                    isProAdmin = true;
                }
                if (role
                        .getCode()
                        .startsWith("CITY_") || role
                        .getCode()
                        .startsWith("LOCALNET_")) {//市能耗费管理员
                    isCityAdmin = true;
                }
            }
            if (isProAdmin) {
                powerModleInitTemp.setOrgCode(null);
                //  查询权限设置 分公司
            } else {
                List<IdNameVO> companies = user.getCompanies();
                if (companies != null && companies.size() > 0) {
                    powerModleInitTemp.setOrgCode(companies.get(0).getId());
                }
            }
        } else {
            powerModleInitTemp.setOrgCode(
                    StringUtils.isNotBlank(powerModleInitTemp.getCompany()) ?
                            powerModleInitTemp.getCompany()
                            :
                            powerModleInitTemp.getOrgCode()
            );
        }
    }


    /**
     * 根据地市查询计算结果
     *
     * @param powerModleInit
     * @return
     */
    @GetMapping("/allCalcResult")
    public TableDataInfo allCalcResult(PowerModleInit powerModleInit) {
        //权限判定 组装
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role
                    .getCode()
                    .startsWith("admin")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role
                    .getCode()
                    .startsWith("PROVI_")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role
                    .getCode()
                    .startsWith("CITY_") || role
                    .getCode()
                    .startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
        }
        if (isProAdmin) {
            powerModleInit.setOrgCode(null);
            //  查询权限设置 分公司
        } else if (isCityAdmin) {
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0) powerModleInit.setOrgCode(companies
                    .get(0)
                    .getId());
        }
        powerModleInit.setDelflag(0);
        startPage();

        List<PowerModleInit> list = powerModleInitMapper.selectListForCalc(powerModleInit);
        //有无办公数据过滤
        list.forEach(init -> {
            //无办公 将直购电、代购电有办公置空
            if (init.getWork() == 0) {
                init.setPriceCountryProxyTimeshar(null);
                init.setPriceDirectpureleEstimate(null);
            }
            //有办公 将直购电、代购电 无办公置空
            if (init.getWork() == 1) {
                init.setPriceCountryProxy(null);
                init.setPriceDirectpurele(null);
            }
        });
        return getDataTable(list);
    }

    /**
     * 获取封装得MultipartFile
     *
     * @param inputStream inputStream
     * @param fileName    fileName
     * @return MultipartFile
     */
    public MultipartFile getMultipartFile(InputStream inputStream, String fileName) {
        FileItem fileItem = createFileItem(inputStream, fileName);
        //CommonsMultipartFile是feign对multipartFile的封装，但是要FileItem类对象
        return new CommonsMultipartFile(fileItem);
    }


    /**
     * FileItem类对象创建
     *
     * @param inputStream inputStream
     * @param fileName    fileName
     * @return FileItem
     */
    public FileItem createFileItem(InputStream inputStream, String fileName) {
        FileItemFactory factory = new DiskFileItemFactory(16, null);
        String textFieldName = "file";
        FileItem item = factory.createItem(textFieldName, MediaType.MULTIPART_FORM_DATA_VALUE, true, fileName);
        int bytesRead = 0;
        byte[] buffer = new byte[10 * 1024 * 1024];
        OutputStream os = null;
        //使用输出流输出输入流的字节
        try {
            os = item.getOutputStream();
            while ((bytesRead = inputStream.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            inputStream.close();
        } catch (IOException e) {
            log.error("Stream copy exception", e);
            throw new IllegalArgumentException("文件上传失败");
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    log.error("Stream close exception", e);

                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("Stream close exception", e);
                }
            }
        }

        return item;
    }

    /**
     * 电量模型 电价差异导出 excel
     */
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult exportExcel(PowerModleInit powerModleInit, PageRequest pageRequest) throws Exception {

        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role
                    .getCode()
                    .startsWith("PROVI_")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role
                    .getCode()
                    .startsWith("CITY_") || role
                    .getCode()
                    .startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
            if (role
                    .getCode()
                    .startsWith("SUB_")) {//县能耗费管理员
                isSubAdmin = true;
            }
        }
        if (isProAdmin) {
            powerModleInit = null;
            //  查询权限设置 分公司
        } else if (isCityAdmin) {
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0) powerModleInit.setCity(Long.valueOf(companies
                    .get(0)
                    .getId()));
        }
        try {

//            budget.setStatus(1);
            //todo:挪位置 到 导入
            Page<PowerModleInit> powerModleInits = powerModleInitService.queryByPage(powerModleInit, pageRequest);
            List<PowerModleInit> list = powerModleInits.getContent();


            //保存生成的数据到init 表
            Integer n = powerModleInitService.batchAddToOhter(list);
            if (n != list.size()) {
                log.info("保存成功，但是条数不对，应该是 {},实际是{}", list.size(), n);
            }
            //导出数据
            ExcelUtil<PowerModleInit> util = new ExcelUtil<PowerModleInit>(PowerModleInit.class);
            return util.exportExcel(list, "结算数据稽核");
        } catch (Exception e) {
            return error(e.getMessage() + "导出Excel失败，请联系网站管理员！");
        }
    }

    @GetMapping("/demo")
    public Integer demo() {
        return powerModleInitMapper.demo();
    }

    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(PowerModleInit msg) {
        log.info("权限判定 组装");
        setOrgCode(msg);
        boolean calcFlag = powerModleInitMapper.calcFlag(msg);
        msg.setCalcFlag(calcFlag);
        int sum = powerModleInitMapper.selectListForCalcCount(msg);
        startPage();
        List<PowerModleInit> list = powerModleInitMapper.selectListForCalc(msg);
        //有无办公数据过滤
        list.stream()
                .filter(init -> init.getWork() != null)
                .forEach(init -> {
                    //无办公 将直购电、代购电有办公置空
                    if (init.getWork() == 0) {
                        init.setPriceCountryProxyTimeshar(null);
                        init.setPriceDirectpureleEstimate(null);
                    }
                    //有办公 将直购电、代购电 无办公置空
                    if (init.getWork() == 1) {
                        init.setPriceCountryProxy(null);
                        init.setPriceDirectpurele(null);
                    }
                });
        List<PowerModleInitVo> voList = list.stream().map(
                item -> {
                    PowerModleInitVo vo = new PowerModleInitVo();
                    BeanUtils.copyProperties(item, vo, "capacityDemandBigFlag");

                    Integer flag = item.getCapacityDemandBigFlag();
                    vo.setCapacityDemandBigFlag(
                            flag == null ? "非工业" :
                                    flag == -1 ? "非工业" :
                                            flag == 1 ? "容量"
                                                    : "需量"
                    );
                    String accountno = vo.getAccountno();
                    vo.setAccountno(
                            StringUtils.isBlank(accountno) ? "户号缺失" : accountno
                    );
                    return vo;
                }
        ).collect(toList());
        TableDataInfo dataTable = getDataTable(voList);
        dataTable.setTotal(sum);
        return dataTable;
    }

    private void setOrgCode(PowerModleInit msg) {
        if (StringUtils.isBlank(msg.getOrgCode()) && StringUtils.isBlank(msg.getCompany())) {
            User user = ShiroUtils.getUser();
            List<Role> roles = userService.selectUserRole(user.getId());
            boolean isProAdmin = false;
            boolean isCityAdmin = false;
            for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
                if (role.getCode().startsWith("PROVI_")) {//省能耗费管理员
                    isProAdmin = true;
                }
                if (role.getCode().startsWith("admin")) {//省能耗费管理员
                    isProAdmin = true;
                }
                if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                    isCityAdmin = true;
                }
            }
            if (isProAdmin) {
                msg.setOrgCode(null);
            } else {
                List<IdNameVO> companies = user.getCompanies();
                if (companies != null && companies.size() > 0)
                    msg.setOrgCode(companies.get(0).getId());
            }
        } else {
            msg.setOrgCode(msg.getCompany());
        }

    }

    @RequestMapping("/list1")
    @ResponseBody
    public TableDataInfo list1(PowerModleInit msg) {
        startPage();
        List<PowerModleInit> list = powerModleInitService.selectList1(msg);
        return getDataTable(list);
    }

    /**
     * 根据台账户号，起止日期对应结算数据电量
     */
    @PostMapping("/getPower")
    public AjaxResult getPower(@RequestBody Accountqur account) {
        return powerModleInitService.
                getPower(account.getAmmeterid(), account.getStartdate(), account.getEnddate());
    }


    //封装方法
    private PowerModleInit getPowerModleInit(PowerModleInit init) {
        //国网代购测算值单价
        //国网代购价+基金附加费+大工业基本电费折算单价（市州填写）+ 输配电价
        //大工业基本电费折算单价（市州填写）=大工业容量/需量（需填） * 22 /  用电量
        init.setPriceCountryProxy(powerModleInitService.calcPriceContryProxy(init));

        //国网代购分峰平谷单价
        //(((国网代购价+输配电价)*1.6+基金附加费+大工业基本电费折算单价（市州填写）)*峰电量1.6)+((大工业基本电费折算单价（市州填写）+基金附加费+输配电价+国网代购价)
        // *平电量1)+(((国网代购价+输配电价)\*0.4+基金附加费+大工业基本电费折算单价（市州填写）)*谷电量0.4))/用电量
        //大工业基本电费折算单价（市州填写）=大工业容量/需量（需填） * 22 /  用电量
        init.setPriceCountryProxyTimeshar(powerModleInitService.calcPriceCountryProxyTimeshar(init));

        //代购分时 峰电价
        //((国网代购价+输配电价)*1.6+基金附加费+大工业基本电费折算单价（市州填写）)\*峰电量1.6
        //大工业基本电费折算单价（市州填写）=大工业容量/需量（需填） * 22 /  用电量
        init.setPriceCountryProxyHigh(powerModleInitService.calcPriceCountryProxyHigh(init));

        //代购分时 平电价
        //(大工业基本电费折算单价（市州填写）+基金附加费+输配电价+国网代购价)*平电量1
        //大工业基本电费折算单价（市州填写）=大工业容量/需量（需填） * 22 /  用电量
        init.setPriceCountryProxyMid(powerModleInitService.calcPriceCountryProxyMid(init));

        //代购分时 谷电价
        //((国网代购价+输配电价)*0.4+基金附加费+大工业基本电费折算单价（市州填写）)\*谷电量0.4
        //大工业基本电费折算单价（市州填写）=大工业容量/需量（需填） * 22 /  用电量
        init.setPriceCountryProxyLow(powerModleInitService.calcPriceCountryProxyLow(init));

        //直购电（实际）
        //输配电价+损益+基金附加费+大工业基本电费折算单价（市州填写）+((常规直购)总结算均价\*(常规直购)总结算电量+(富余电量)总结算均价\*(富余电量)总结算电量)/((常规直购)
        // 总结算电量+(富余电量)总结算电量)
        init.setPriceDirectpurele(powerModleInitService.calcPriceDirectpurele(init));

        //直购电理论值电价测算（分合同、浮动）
        //(水电合同电量（度）\*水电合同价+浮动水电电量*水电浮动价+实际火电电量\*火电价格)/(实际水电电量+实际火电电量)
        //水电合同电量（度）=合同电量*0.8
        //实际火电电量 = 用电量\*0.2
        //实际水电电量 = 用电量*0.8
        init.setPriceDirectpureleTheoryEstimate(powerModleInitService.calcPriceDirectpureleTheoryEstimate(init));

        //直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余）
        //(实际水电电量\*水电合同价+实际火电电量\*火电价格)/(实际水电电量+实际火电电量)
        //实际水电电量 = 用电量*0.8
        //实际火电电量 = 用电量\*0.2
        init.setPriceDirectpureleTheoryEstimate2(powerModleInitService.calcPriceDirectpureleTheoryEstimate2(init));

        //直购电理论电价测算（不分合同内外、不含富余、分峰平谷）
        //(((((输配电价+直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余）+损益)\*1.6)+大工业基本电费折算单价（市州填写）+基金附加费)\*峰电量1.6)+(
        // (输配电价+损益+基金附加费+大工业基本电费折算单价（市州填写）+直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余）)\*平电量1)+(
        // (输配电价+直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余）+损益)\*0.4+大工业基本电费折算单价（市州填写）+基金附加费)\*谷电量0.4)/(峰电量1
        // .6+平电量1+谷电量0.4)
        init.setPriceDirectpureleEstimate(powerModleInitService.calcPriceDirectpureleEstimate(init));

        //直购电实际（含富余、并分峰平谷）
        //((((((常规直购)总结算均价\*(常规直购)总结算电量+(富余电量)总结算均价\*(富余电量)总结算电量)/((富余电量)总结算电量+(常规直购)总结算电量)+损益+输配电价)
        // \*1.6+基金附加费+大工业基本电费折算单价（市州填写）)\*峰电量1.6)+((((常规直购)总结算电量\*(常规直购)总结算均价+(富余电量)总结算电量\*(富余电量)
        // 总结算均价)/((常规直购)总结算电量+(富余电量)总结算电量)+损益+输配电价+基金附加费+大工业基本电费折算单价（市州填写）)\*平电量1)+((((常规直购)总结算均价\*
        // (常规直购)总结算电量+(富余电量)总结算均价\*(富余电量)总结算电量)/((常规直购)总结算电量+(富余电量)总结算电量)+损益+输配电价)\*0
        // .4+基金附加费+大工业基本电费折算单价（市州填写）)\*谷电量0.4)/(峰电量1.6+平电量1+谷电量0.4)
        init.setPriceDirectpurele2(powerModleInitService.calcPriceDirectpurele2(init));

        //直购电收益
        //是否 参加直购电
        // 参加 办公
        // (国网代购分峰平谷单价-直购电理论电价测算（不分合同内外、不含富余、分峰平谷）)*用电量
        //无 办公
        //   (国网代购测算值单价-直购电（实际）无办公)*(常规直购)总结算电量
        init.setProfitDire(powerModleInitService.caclProfitDire(init));

        return init;
    }

    private void calcinit(BigDecimal indirectcountry, BigDecimal profitloss, BigDecimal appendFund, Map<String, ModlePriceSp2> priceSp2Map, Map<String, ModleBigandwork> bigandworkMap, PowerModleInit init) {
        long s = System.currentTimeMillis();
        //设置默认值
        //国网代购价 损益 基金附加费
        init.setAppendFund(appendFund);
        init.setProfitLoss(profitloss);
        init.setIndirectCountry(indirectcountry);
        //输配 是否直购电 是否办公
        ModlePriceSp2 sp2 = priceSp2Map.get(init.getAccountno());
        if (sp2 != null) {
            init.setPriceSp(sp2.getPriceSp());
        }
        //缺省 办公/直购电
        //默认无办公
        init.setWork(sp2 != null && sp2.getWork() != null ? sp2.getWork() : 0);
        //默认参加直购电
        init.setGetDire(sp2 != null && sp2.getDire() != null ? sp2.getDire() : 1);
        //大工业
        ModleBigandwork bigandwork = bigandworkMap.get(init.getAccountno());
        //大工业标识 先从结算数据拿
        int bigFlag = -1;
        if (init.getCapacityDemandBigFlag() != null && init.getCapacityDemandBigFlag() != -1) {
            bigFlag = init.getCapacityDemandBigFlag();
        } else {
            bigFlag = bigandwork.getBigflag();
        }
        //大工业需量 先从结算数据拿
        BigDecimal Capacitydemandbig2 = BigDecimal.ZERO;
        if (init.getCapacityDemandBig2() != null) {
            Capacitydemandbig2 = init.getCapacityDemandBig2();
        } else {
            Capacitydemandbig2 = bigandwork.getCapacitydemandbig2();
        }

        init.setCapacityDemandBig(bigFlag == 1 ? bigandwork.getCapacitydemandbig1() : bigFlag == 2 ? Capacitydemandbig2 : BigDecimal.ZERO);


        //国网代购（无办公）
        //国网代购价+基金附加费+大工业基本电费折算单价（市州填写）+ 输配电价+损益
        //大工业基本电费折算单价（市州填写）=大工业容量/需量（需填） * 22 /  用电量
        init.setPriceCountryProxy(powerModleInitService.calcPriceContryProxy(init));

        //直购电实际（无办公）
        //输配电价+损益+基金附加费+大工业基本电费折算单价（市州填写）+常规直购)总结算均价
        init.setPriceDirectpurele(powerModleInitService.calcPriceDirectpurele(init));

        //国网代购分峰平谷单价（有办公）
        //(((国网代购价+输配电价)*1.6+基金附加费+大工业基本电费折算单价（市州填写）)*峰电量1.6)+(
        // (大工业基本电费折算单价（市州填写）+基金附加费+输配电价+国网代购价)
        // *平电量1)+(((国网代购价+输配电价)\*0.4+基金附加费+大工业基本电费折算单价（市州填写）)*谷电量0.4))/用电量
        //大工业基本电费折算单价（市州填写）=大工业容量/需量（需填） * 22 /  用电量
        init.setPriceCountryProxyTimeshar(powerModleInitService.calcPriceCountryProxyTimeshar(init));

        //直购电理论电价测算（不分合同内外、不含富余、分峰平谷）
        //(((((输配电价+直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余）+损益)\*1.6)+大工业基本电费折算单价（市州填写）+基金附加费)\*峰电量1.6)+(
        // (输配电价+损益+基金附加费+大工业基本电费折算单价（市州填写）+直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余）)\*平电量1)+(
        // (输配电价+直购电理论值电价测算（不分合同内外、不分峰平谷、不含富余）+损益)\*0.4+大工业基本电费折算单价（市州填写）+基金附加费)\*谷电量0.4)/(峰电量1
        // .6+平电量1+谷电量0.4)
        init.setPriceDirectpureleEstimate(powerModleInitService.calcPriceDirectpureleEstimate(init));

        //直购电收益
        //是否 参加直购电
        // 参加 办公
        // (国网代购分峰平谷单价-直购电理论电价测算（不分合同内外、不含富余、分峰平谷）)*用电量
        //无 办公
        //   (国网代购测算值单价-直购电（实际）无办公)*(常规直购)总结算电量
        init.setProfitDire(powerModleInitService.caclProfitDire(init));
        long e = System.currentTimeMillis();
        log.info("耗时：{}", (e - s));
    }

}

