package com.sccl.modules.business.meterinfo.service;

import com.sccl.modules.business.meterinfo.domain.Meterinfo;
import com.sccl.framework.service.IBaseService;

import java.io.InputStream;
import java.util.List;

/**
 * 计量设备 服务层
 * 
 * <AUTHOR>
 * @date 2023-03-15
 */
public interface IMeterinfoService extends IBaseService<Meterinfo>
{


    List<Meterinfo> importExcel(String sheet1, InputStream inputStream);

    int insertListForTemporary(List newList);

    int insertInfo();

    void deleteRepeat();
}
