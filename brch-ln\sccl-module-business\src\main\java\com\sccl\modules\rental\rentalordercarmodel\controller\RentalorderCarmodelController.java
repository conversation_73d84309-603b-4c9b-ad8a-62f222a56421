package com.sccl.modules.rental.rentalordercarmodel.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.rental.rentalordercarmodel.domain.RentalorderCarmodel;
import com.sccl.modules.rental.rentalordercarmodel.service.IRentalorderCarmodelService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 车型数据 信息操作处理
 * 
 * <AUTHOR>
 * @date 2019-08-23
 */
@RestController
@RequestMapping("/rental/rentalorderCarmodel")
public class RentalorderCarmodelController extends BaseController
{
    private String prefix = "rental/rentalorderCarmodel";
	
	@Autowired
	private IRentalorderCarmodelService rentalorderCarmodelService;

//	@RequiresPermissions("rental:rentalorderCarmodel:view")
	@RequiresPermissions("rental:rentalcarorder:view")
	@GetMapping()
	public String rentalorderCarmodel()
	{
	    return prefix + "/rentalorderCarmodel";
	}
	
	/**
	 * 查询车型数据列表
	 */
	@RequiresPermissions("rental:rentalcarorder:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(RentalorderCarmodel rentalorderCarmodel)
	{
		startPage();
        List<RentalorderCarmodel> list = rentalorderCarmodelService.selectList(rentalorderCarmodel);
		return getDataTable(list);
	}
	
	/**
	 * 新增车型数据
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存车型数据
	 */
	@RequiresPermissions("rental:rentalcarorder:add")
	//@Log(title = "车型数据", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody RentalorderCarmodel rentalorderCarmodel)
	{		
		return toAjax(rentalorderCarmodelService.insert(rentalorderCarmodel));
	}

	/**
	 * 修改车型数据
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		RentalorderCarmodel rentalorderCarmodel = rentalorderCarmodelService.get(id);

		Object object = JSONObject.toJSON(rentalorderCarmodel);

        return this.success(object);
	}
	
	/**
	 * 修改保存车型数据
	 */
	@RequiresPermissions("rental:rentalcarorder:edit")
	//@Log(title = "车型数据", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody RentalorderCarmodel rentalorderCarmodel)
	{		
		return toAjax(rentalorderCarmodelService.update(rentalorderCarmodel));
	}
	
	/**
	 * 删除车型数据
	 */
	@RequiresPermissions("rental:rentalcarorder:remove")
	//@Log(title = "车型数据", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(rentalorderCarmodelService.deleteByIdsDB(Convert.toStrArray(ids)));
	}


    /**
     * 查看车型数据
     */
    @RequiresPermissions("rental:rentalcarorder:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		RentalorderCarmodel rentalorderCarmodel = rentalorderCarmodelService.get(id);

        Object object = JSONObject.toJSON(rentalorderCarmodel);

        return this.success(object);
    }

}
