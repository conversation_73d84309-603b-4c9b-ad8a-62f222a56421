package com.sccl.modules.business.statistical.tower.domain;

import com.sccl.common.utils.DateUtils;
import com.sccl.modules.business.statistical.framework.StatisticalIndex;
import com.sccl.modules.business.statistical.framework.StatisticalIndexObject;
import com.sccl.modules.business.statistical.tower.vo.ContributionPercentVO;
import lombok.Data;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/10/31 16:14
 * @Email <EMAIL>
 */
@Data
public class TowerBillAuditResult implements StatisticalIndexObject {
    private final static Map<Integer, String> errTypeMap = new HashMap<>();

    static {
        errTypeMap.put(0, "格式异常站址");
        errTypeMap.put(1, "新增站址");
        errTypeMap.put(2, "基础数据异常站址");
        errTypeMap.put(3, "连续性异常站址-无上期台账");
        errTypeMap.put(4, "连续性异常站址-日期跳跃");
        errTypeMap.put(5, "连续性异常站址-度数跳跃");
        errTypeMap.put(6, "定额异常站址-无定额");
        errTypeMap.put(7, "定额异常站址-无线大数据和异常");
        errTypeMap.put(8, "分摊比例异常");
        errTypeMap.put(9, "历史波动异常");
    }

    /**
     * 地市名
     */
    private String city;
    /**
     * 区县组织名
     */
    private String district;
    /**
     * company编码
     */
    private Long company;
    /**
     * country编码
     */
    private Long country;
    /**
     * 统计日期
     */
    private Date statisticalDate;
    /**
     * 总稽核数目
     */
    private int totalCount;
    /**
     * 通过数目
     */
    private int passCount;
    /**
     * 未通过数目
     */
    private int failCount;
    /**
     * 已报账数目
     */
    private int accountedCount;
    /**
     * 更新时间
     */
    private String updateTime;

    private final Map<String, Integer> collectClassifiedMap = new HashMap<>();
    /**
     * 分摊比例异常的站址数目
     */
    private Integer errContributionPercentCount;

    private Map<String, ContributionPercentVO> contributionPercentVOMap;

    public void putCollectClassified(int errStep, int totalCount) {
        if (getErrType(errStep) != null) {
            Integer in = collectClassifiedMap.get(getErrType(errStep));
            in = in == null ? 0 : in;
            collectClassifiedMap.put(getErrType(errStep), in + totalCount);
        }
    }

    private String getErrType(int errStep) {
        return errTypeMap.get(errStep);
    }

    @Override
    public String getTitle() {
        return city + district + DateUtils.formatDate(statisticalDate) + "稽核统计";
    }

    @Override
    public Date getStatisticalDate() {
        return statisticalDate;
    }

    @Override
    public StatisticalIndex getIndexObject() {
        StatisticalIndex index = new StatisticalIndex();
        index.setTitle(getTitle());
        index.setContent(getContent());
        index.setContentType(this.getClass());
        index.setStatisticalTime(getStatisticalDate());
        return index;
    }
}
