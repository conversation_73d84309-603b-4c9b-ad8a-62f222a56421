package com.sccl.modules.business.stationaudit.pcontractprice;

import com.enrising.dcarbon.audit.*;
import com.sccl.common.utils.SpringUtil;
import com.sccl.modules.business.account.domain.Account;
import com.sccl.modules.business.account.mapper.AccountMapper;
import com.sccl.modules.business.noderesultstatistical.domain.AuditFlag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;

@Slf4j
public class ContractExCreator extends AbstractRefereeDatasourceCreator implements AuditFlag {
    @Override
    public Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> collect(Auditable auditable) {
        //数据源
        Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> datasource = new HashMap<>();

        //从Spring上下文取得mapper
        AccountMapper mapper = SpringUtil.getBean(AccountMapper.class);
        //可能上下文没有这个mapper
        if (mapper == null) {
            return datasource;
        }
        Account account = (Account) auditable;
        //根据 台账主键 获取对应的平均电量比对信息
        Long pcid = account.getPcid();
        List<ContractExCreatorContent> contents = mapper.selectContract2(Arrays.asList(pcid));
        
        if (CollectionUtils.isEmpty(contents)) {
            return datasource;
        }
        ContractExCreatorContent content = contents.get(0);

        log.info("判空");

        if (content == null) {
            return datasource;
        }

        log.info("开始评判");
        String exmsg = ContractExCreatorContent.getStrContract(content);
        content.setExmg(exmsg);
        //构造auditResult
        RefereeResult refereeResult = new RefereeResult("转供电单价稽核", true, "成功");
        content.setAuditKey(pcid + "");
        content.setRefereeResult(refereeResult);
        content.setStep(11);

        AuditResult auditResult = content.getAuditResult();


        //可以添加多种不同类型的评判数据
        //2添加到数据源
        datasource.put(ContractExCreatorContent.class, new ArrayList<>(Arrays.asList(auditResult)));
        return datasource;
    }

    @Override
    public AbstractReferee getReferee() {
        return new ContracatReferee("转供电单价");
    }
}
