package com.sccl.modules.rental.rentalcarorder.service;

import java.util.List;

import com.sccl.framework.service.IBaseService;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.rental.rentalcarorder.domain.Rentalcarorder;
import com.sccl.modules.uniflow.common.WFModel;

/**
 * 车辆租赁申请 服务层
 * 
 * <AUTHOR>
 * @date 2019-08-23
 */
public interface IRentalcarorderService extends IBaseService<Rentalcarorder>
{


    List<Rentalcarorder> selectListByIds(String[] toStrArray);

    AjaxResult saveRentalcarorder(Rentalcarorder rentalcarorder);

    /**
     * 流程引擎回调（通过MQ）
     *
     * @param wfModel 流程消息对象
     */
    public void uniflowCallBack(WFModel wfModel);

    int deleteAndItemByIds(String[] toStrArray);
}
