package com.sccl.modules.business.quota.controller;

import com.alibaba.fastjson.JSONObject;
import com.sccl.common.lang.StringUtils;
import com.sccl.common.support.Convert;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol;
import com.sccl.modules.business.ammeterorprotocol.service.IAmmeterorprotocolService;
import com.sccl.modules.business.quota.domain.Quota;
import com.sccl.modules.business.quota.domain.QuotaBaseResult;
import com.sccl.modules.business.quota.domain.QuotaCondition;
import com.sccl.modules.business.quota.domain.QuotaRecord;
import com.sccl.modules.business.quota.service.IQuotaRecordService;
import com.sccl.modules.business.quota.service.IQuotaService;
import com.sccl.modules.system.organization.service.IOrganizationService;
import com.sccl.modules.system.role.domain.Role;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.service.IUserService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 定额管理
 * 
 * <AUTHOR>
 * @date 2019-05-13
 */
@RestController
@RequestMapping("/business/quota")
public class QuotaController extends BaseController
{
	private static final Logger logger = LoggerFactory.getLogger(QuotaController.class);
    private String prefix = "business/quota";
	@Autowired
	private IOrganizationService organizationService;
	@Autowired
	private IQuotaService quotaService;
	@Autowired
	private IQuotaRecordService quotaRecordService;
	@Autowired
	private IAmmeterorprotocolService ammeterorprotocolService;
	@Autowired
	private IUserService userService;
	
	@RequiresPermissions("business:quota:view")
	@GetMapping()
	public String quota()
	{
	    return prefix + "/quota";
	}
	
	/**
	 * 查询定额列表
	 */
//	@RequiresPermissions("business:quota:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(QuotaCondition quotaCondition)
	{
		List<Map<String,Object>> countrys = new ArrayList<>();
		//业务逻辑，不能放在service中，会影响前端分页
		//根据用户责任中心查询分公司和责任中心
		//默认查询第一个公司和用户的第一个责任中心
		if(null != quotaCondition.getCompany() && "-1".equals(quotaCondition.getCompany().toString())){
			quotaCondition.setCompany(null);
			quotaCondition.setCountry(null);
		}
		else if(null != quotaCondition.getCompany() && (null == quotaCondition.getCountry() || "-1".equals(quotaCondition.getCountry().toString()) )){
			countrys = organizationService.selectSubordinateOrgByRole(quotaCondition.getCompany().toString(),"1");
			if(null == countrys || countrys.size() == 0){
				countrys = new ArrayList<>();
			}
			quotaCondition.setCountry(null);
		}
		quotaCondition.setCountrys(countrys);
		startPage();
        List<QuotaBaseResult> list = quotaService.selectListBySearch(quotaCondition);
		return getDataTable(list);
	}
	
	/**
	 * 新增定额
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存定额
	 */
//	@RequiresPermissions("business:quota:add")
	//@Log(title = "定额", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody Quota quota)
	{
		quotaService.addQuota(quota);
		return this.success(quota);
	}

	/**
	 * 修改定额
	 */
	@GetMapping("/edit")
	public AjaxResult edit( Long id)
	{
		QuotaBaseResult quota = quotaService.getById(id);

		Object object = JSONObject.toJSON(quota);

        return this.success(object);
	}

	/**
	 * 修改保存定额
	 */
//	@RequiresPermissions("business:quota:edit")
	//@Log(title = "定额", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody Quota quota)
	{
		int result = quotaService.updateQuota(quota);
		return toAjax(result);
	}
	
	/**
	 * 删除定额
	 */
//	@RequiresPermissions("business:quota:remove")
	//@Log(title = "定额", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{
		return toAjax(quotaService.deleteQuota(ids));
	}


    /**
     * 查看定额
     */
//    @RequiresPermissions("business:quota:view")
    @GetMapping("/view/{pcid}")
    @ResponseBody
    public AjaxResult view(@PathVariable("pcid") Long pcid)
    {
		QuotaBaseResult quota = quotaService.getById(pcid);

        Object object = JSONObject.toJSON(quota);

        return this.success(object);
    }

	/**
	 * 获取 session data
	 */
	@GetMapping("/getUserdata")
	@ResponseBody
	public AjaxResult getUserdata() {
		AjaxResult json = new AjaxResult();
		Map<String, Object> map = new HashMap<>();
		User user = ShiroUtils.getUser();
		if (user == null) {
			return AjaxResult.error(1, "登录信息已过期，请重新登录");
		}
		List<Role> roles = userService.selectUserRole(user.getId());
		boolean flag = false;
		for (Role role:roles) {
			if("CITY_ADMIN".equals(role.getCode())){//能耗费市管理员
				flag = true;break;
			}
		}
		map.put("isCityAdmin", flag);
		map.put("roles", roles);
		map.put("userName", user.getUserName());
		map.put("loginId", user.getLoginId());
		map.put("companies", user.getCompanies());
		map.put("departments", user.getDepartments());
		json.put("data", map);
		return json;
	}
	/**
	 * 提交流程验证用户是否有数据需要提交
	 * @param id
	 * @return
	 */
	@RequestMapping("/checkStartFlow")
	@ResponseBody
	public AjaxResult checkStartFlow(@RequestParam(required = false) Long id) {
		QuotaRecord searchValue = new QuotaRecord();
		searchValue.setQuotaId(id);
		User user = ShiroUtils.getUser();
		//最新数据不是有效数据并且不是自己的数据，根据用户查询上一有效数据时间后面的
		searchValue.setBillStatus(2);
		if (null != user) {
			searchValue.setCreatorId(user.getId());
		}
		//最新数据不是有效数据并且不是自己的数据，根据用户查询上一有效数据时间后面的
		Map<String,Object> params = new HashMap<>();
		params.put("billStatus",2);
		params.put("quotaId",id);
		String createTime = quotaRecordService.getByCreateTime(params);
		if(StringUtils.isNotEmpty(createTime)){
			try{
				SimpleDateFormat format1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				searchValue.setCreateTime(format1.parse(createTime));
			}catch (ParseException e){
				e.printStackTrace();
			}
		}
		List<QuotaRecord> quota = quotaRecordService.getByQuotaDateId(searchValue);
		Object object = null;
		if(quota != null && quota.size()!= 0){
			object = JSONObject.toJSON(quota.get(0));
		}

		return this.success(object);
	}/**
	 * 通过电表协议id查询是否有多个定额
	 * @param id
	 * @return
	 */
	@RequestMapping("/checkAmmProByQuota")
	@ResponseBody
	public AjaxResult checkAmmProByQuota(Long id,Long deviceId) {
		//验证电表协议是否唯一
		Quota quota1 = new Quota();
		quota1.setDeviceId(deviceId);
//		quota1.setId(id);
		//查询正式表是否关联了电表协议，如果没有关联，则查询定额记录表
		Quota quota = quotaService.selectUniqueBy(quota1);
//		if(null == quota){
//			QuotaRecord quotaRecord1 = new QuotaRecord();
//			quotaRecord1.setDeviceId(deviceId);
//			List<QuotaRecord> records = quotaRecordService.selectObjectByAmmPro(quotaRecord1);
//			if(records.size() > 0){
//				return this.error("电表或协议已经存在");
//			}
//		}else if(null == id || (null != id && !id.toString().equals(quota.getId().toString()))){
//			return this.error("电表或协议已经存在");
//		}
		if((null != quota && null == id) || (null != quota && null != id && !id.toString().equals(quota.getId().toString()))){
			return this.error("电表或协议已经存在");
		}
		return this.success();
	}
}
