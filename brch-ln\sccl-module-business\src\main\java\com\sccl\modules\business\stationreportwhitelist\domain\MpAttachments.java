package com.sccl.modules.business.stationreportwhitelist.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@TableName("attachments")
public class MpAttachments extends Model<MpAttachments> {

    /**
     * 附件id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 业务主表id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long busiId;

    /**
     * 附件名称
     */
    private String fileName;

    /**
     * 上传人
     */
    private String creatorName;

    /**
     * 上传日期
     */
    private Date createTime;

    /**
     * 附件大小
     */
    private String fileSize;
}
