package com.sccl.modules.business.powerpriceconf.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.math.BigDecimal;


/**
 * 单价配置表 power_price_conf
 * 
 * <AUTHOR>
 * @date 2022-09-18
 */
public class PowerPriceConf extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
    /** 单位 */
    private Long company;
    /** 部门 */
    private Long country;
    /** 年 */
    private String year;
    /** 月 */
    private String month;
    /** 用电类型 */
    private Long electrotype;
    /** 对外结算 */
    private Integer directsupplyflag;
    /** 电压等级 */
    private Integer voltageclass;
    /** 输配电价 */
    private BigDecimal tdprice;
    /** 基金附加费 */
    private BigDecimal fundprice;
    /** 损益电价 */
    private BigDecimal plprice;
    /** 直购单价 */
    private BigDecimal directprice;
    /** 代购单价 */
    private BigDecimal agentprice;
    /** 地网代购单价 */
    private BigDecimal areaprice;
    /** 测算单价 */
    private BigDecimal estprice;
    /** 状态 */
    private String status;


	public void setCompany(Long company)
	{
		this.company = company;
	}

	public Long getCompany() 
	{
		return company;
	}

	public void setCountry(Long country)
	{
		this.country = country;
	}

	public Long getCountry() 
	{
		return country;
	}

	public void setYear(String year)
	{
		this.year = year;
	}

	public String getYear() 
	{
		return year;
	}

	public void setMonth(String month)
	{
		this.month = month;
	}

	public String getMonth() 
	{
		return month;
	}

	public void setElectrotype(Long electrotype)
	{
		this.electrotype = electrotype;
	}

	public Long getElectrotype() 
	{
		return electrotype;
	}

	public void setDirectsupplyflag(Integer directsupplyflag)
	{
		this.directsupplyflag = directsupplyflag;
	}

	public Integer getDirectsupplyflag() 
	{
		return directsupplyflag;
	}

	public void setVoltageclass(Integer voltageclass)
	{
		this.voltageclass = voltageclass;
	}

	public Integer getVoltageclass() 
	{
		return voltageclass;
	}

	public void setTdprice(BigDecimal tdprice)
	{
		this.tdprice = tdprice;
	}

	public BigDecimal getTdprice() 
	{
		return tdprice;
	}

	public void setFundprice(BigDecimal fundprice)
	{
		this.fundprice = fundprice;
	}

	public BigDecimal getFundprice() 
	{
		return fundprice;
	}

	public void setPlprice(BigDecimal plprice)
	{
		this.plprice = plprice;
	}

	public BigDecimal getPlprice() 
	{
		return plprice;
	}

	public void setDirectprice(BigDecimal directprice)
	{
		this.directprice = directprice;
	}

	public BigDecimal getDirectprice() 
	{
		return directprice;
	}

	public void setAgentprice(BigDecimal agentprice)
	{
		this.agentprice = agentprice;
	}

	public BigDecimal getAgentprice() 
	{
		return agentprice;
	}

	public void setAreaprice(BigDecimal areaprice)
	{
		this.areaprice = areaprice;
	}

	public BigDecimal getAreaprice() 
	{
		return areaprice;
	}

	public void setEstprice(BigDecimal estprice)
	{
		this.estprice = estprice;
	}

	public BigDecimal getEstprice() 
	{
		return estprice;
	}

	public void setStatus(String status)
	{
		this.status = status;
	}

	public String getStatus() 
	{
		return status;
	}


	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("company", getCompany())
            .append("country", getCountry())
            .append("year", getYear())
            .append("month", getMonth())
            .append("electrotype", getElectrotype())
            .append("directsupplyflag", getDirectsupplyflag())
            .append("voltageclass", getVoltageclass())
            .append("tdprice", getTdprice())
            .append("fundprice", getFundprice())
            .append("plprice", getPlprice())
            .append("directprice", getDirectprice())
            .append("agentprice", getAgentprice())
            .append("areaprice", getAreaprice())
            .append("estprice", getEstprice())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
