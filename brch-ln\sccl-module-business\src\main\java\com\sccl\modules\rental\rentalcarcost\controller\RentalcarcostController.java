package com.sccl.modules.rental.rentalcarcost.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.rental.rentalcarcost.domain.Rentalcarcost;
import com.sccl.modules.rental.rentalcarcost.service.IRentalcarcostService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 租赁费用
 * 
 * <AUTHOR>
 * @date 2019-08-28
 */
@RestController
@RequestMapping("/rental/rentalcarcost")
public class RentalcarcostController extends BaseController
{
    private String prefix = "rental/rentalcarcost";
	
	@Autowired
	private IRentalcarcostService rentalcarcostService;
	
	@RequiresPermissions("rental:rentalcarcost:view")
	@GetMapping()
	public String rentalcarcost()
	{
	    return prefix + "/rentalcarcost";
	}
	
	/**
	 * 查询租赁费用列表
	 */
	@RequiresPermissions("rental:rentalcarcost:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(Rentalcarcost rentalcarcost)
	{
		startPage();
        List<Rentalcarcost> list = rentalcarcostService.selectList(rentalcarcost);
		return getDataTable(list);
	}
	
	/**
	 * 新增租赁费用
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存租赁费用
	 */
	@RequiresPermissions("rental:rentalcarcost:add")
	//@Log(title = "租赁费用", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody Rentalcarcost rentalcarcost)
	{		
		return toAjax(rentalcarcostService.insert(rentalcarcost));
	}

	/**
	 * 修改租赁费用
	 */
	@GetMapping("/edit/{rccid}")
	public AjaxResult edit(@PathVariable("rccid") Long rccid)
	{
		Rentalcarcost rentalcarcost = rentalcarcostService.get(rccid);

		Object object = JSONObject.toJSON(rentalcarcost);

        return this.success(object);
	}
	
	/**
	 * 修改保存租赁费用
	 */
	@RequiresPermissions("rental:rentalcarcost:edit")
	//@Log(title = "租赁费用", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody Rentalcarcost rentalcarcost)
	{		
		return toAjax(rentalcarcostService.update(rentalcarcost));
	}
	
	/**
	 * 删除租赁费用
	 */
//	@RequiresPermissions("rental:rentalcarcost:remove")
	//@Log(title = "租赁费用", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(rentalcarcostService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看租赁费用
     */
    @RequiresPermissions("rental:rentalcarcost:view")
    @GetMapping("/view/{rccid}")
    @ResponseBody
    public AjaxResult view(@PathVariable("rccid") Long rccid)
    {
		Rentalcarcost rentalcarcost = rentalcarcostService.get(rccid);

        Object object = JSONObject.toJSON(rentalcarcost);

        return this.success(object);
    }

}
