package com.sccl.modules.business.ecceptiondetail.domain;

public enum ExceptionSource {
    YZ("yz", "1"), JT("jt", "2"), JC("check", "3"), PC("pc", "3"),APP("app", "4");
    private String name;
    private String code;

    ExceptionSource(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
