package com.sccl.modules.business.cost.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 局站业务电量查询 定时任务  查询参数列表
 */
@Data
public class StationElectricParamVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  1 缺省 2 回写 3 无线大数据
     */
    private String type;

    /**
     * 统计时间 yyyyMM
     */
    private String tjsj;

    /**
     * 统计时间 yyyyMM%
     */
    private String tjsjLike;

    /**
     * 统计年份 yyyy
     */
    private String year;

    /**
     * 统计月份 MM
     */
    private String month;

    /**
     * 统计月份天数
     */
    private int days;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标志 0/1 正常/删除
     */
    private String delFlag;

    /**
     * 站址编码
     */
    private String stationCode;

    /**
     * 新增
     *
     * @param userName 当前登录用户
     */
    public void initInsert(String userName) {
        delFlag = "0";
        createdBy = userName;
        createTime = new Date();
        updateTime = new Date();
    }
}
