package com.sccl.modules.business.ecceptionprocess.controller;

import com.alibaba.fastjson.JSONObject;
import com.sccl.common.support.Convert;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.autojob.util.convert.StringUtils;
import com.sccl.modules.business.ecceptionprocess.domain.EcceptionProcess;
import com.sccl.modules.business.ecceptionprocess.service.IEcceptionProcessService;
import com.sccl.modules.system.organization.service.IOrganizationService;
import com.sccl.modules.system.role.domain.Role;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.service.IUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 公共异常流程 信息操作处理
 *
 * <AUTHOR>
 * @date 2023-03-23
 */
@RestController
@RequestMapping("/business/ecceptionProcess")
public class EcceptionProcessController extends BaseController {
    private String prefix = "business/ecceptionProcess";

    @Autowired
    private IEcceptionProcessService ecceptionProcessService;
    @Autowired
    private IUserService userService;
    @Autowired
    private IOrganizationService organizationService;

    @GetMapping()
    public String ecceptionProcess() {
        return prefix + "/ecceptionProcess";
    }

    /**
     * 查询公共异常流程列表
     */
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(EcceptionProcess ecceptionProcess) {

        startPagePro();
        setcompanyAndCountry(ecceptionProcess);
        ecceptionProcess.setOrgCode(ecceptionProcess.getCompany());
        List<EcceptionProcess> list = ecceptionProcessService.selectList(ecceptionProcess);
        return getDataTable(list);
    }

    private void setcompanyAndCountry(EcceptionProcess ecceptionProcess) {
        if (StringUtils.isBlank(ecceptionProcess.getCompany())) {
            User user = ShiroUtils.getUser();
            List<Role> roles = userService.selectUserRole(user.getId());
            boolean isProAdmin = false;
            boolean isCityAdmin = false;
            boolean isSubAdmin = false;
            for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
                if (role.getCode().startsWith("PROVI_") || role.getCode().startsWith("admin")) {//省能耗费管理员
                    isProAdmin = true;
                }
                if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                    isCityAdmin = true;
                }
                if (role.getCode().startsWith("SUB_")) {//县能耗费管理员
                    isSubAdmin = true;
                }
            }
            //  权限设置
            if (isProAdmin) {

            } else if (isCityAdmin) {
                List<IdNameVO> companies = user.getCompanies();
                if (companies != null && companies.size() > 0)
                    ecceptionProcess.setOrgCode(companies.get(0).getId());
            } else if (isSubAdmin) {
                List<IdNameVO> departments = user.getDepartments();
                if (departments != null && departments.size() > 0)
                    ecceptionProcess.setCountry(departments.get(0).getId());
            }
        }
    }

    /**
     * 新增公共异常流程
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存公共异常流程
     */
    @Log(title = "公共异常流程", action = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody EcceptionProcess ecceptionProcess) {
        return toAjax(ecceptionProcessService.insert(ecceptionProcess));
    }

    /**
     * 修改公共异常流程
     */
    @GetMapping("/edit/{id}")
    public AjaxResult edit(@PathVariable("id") Long id) {
        EcceptionProcess ecceptionProcess = ecceptionProcessService.get(id);

        Object object = JSONObject.toJSON(ecceptionProcess);

        return this.success(object);
    }

    /**
     * 修改保存公共异常流程
     */
    @Log(title = "公共异常流程", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody EcceptionProcess ecceptionProcess) {
        return toAjax(ecceptionProcessService.update(ecceptionProcess));
    }

    /**
     * 删除公共异常流程
     */
    @Log(title = "公共异常流程", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(ecceptionProcessService.deleteByIds(Convert.toStrArray(ids)));
    }


    /**
     * 查看公共异常流程
     */
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id) {
        EcceptionProcess ecceptionProcess = ecceptionProcessService.get(id);

        Object object = JSONObject.toJSON(ecceptionProcess);

        return this.success(object);
    }

    @GetMapping("/generateCommonException")
    @ResponseBody
    public AjaxResult generateCommonException() {
        return ecceptionProcessService.generateCommonExceptionPro();
    }

    /**
     * 异常查看顶层视角
     *
     * @param process
     * @return
     */
    @GetMapping("/viewTop")
    @ResponseBody
    public AjaxResult viewTop(EcceptionProcess process) {
        return ecceptionProcessService.viewTop(process);
    }

    /**
     * 异常查看二级视角 根据exceptionKey去相应业务表取数据
     *
     * @param fillName
     * @return
     */
    @GetMapping("/viewTwo")
    @ResponseBody
    public TableDataInfo viewTwo(@RequestParam(required = false, value = "fullName") String exceptionKey) {
        if (StringUtils.isBlank(exceptionKey)) {
            throw new RuntimeException("无效的查询参数");
        }
        return getDataTable(
                ecceptionProcessService.viewTwo(exceptionKey));
    }

}
