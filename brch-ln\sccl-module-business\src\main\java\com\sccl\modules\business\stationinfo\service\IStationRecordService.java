package com.sccl.modules.business.stationinfo.service;

import com.sccl.modules.business.stationinfo.domain.StationRecord;
import com.sccl.framework.service.IBaseService;

import java.util.List;
import java.util.Map;

/**
 * 局站历史 服务层
 * 
 * <AUTHOR>
 * @date 2019-05-31
 */
public interface IStationRecordService extends IBaseService<StationRecord>
{
	public StationRecord getStationNewInfo(String id);
	public StationRecord getStationNewInfoView(String id,String userid);
	public StationRecord getStationNewInfoById(String id);

	public int deleteByStationId(String[] ids);

	public List<StationRecord> selectObjectBy(Map<String,Object> params);

}
