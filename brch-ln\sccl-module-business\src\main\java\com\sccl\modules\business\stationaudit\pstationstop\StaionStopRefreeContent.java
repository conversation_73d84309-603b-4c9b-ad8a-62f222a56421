package com.sccl.modules.business.stationaudit.pstationstop;

import com.enrising.dcarbon.audit.AbstractRefereeContent;
import com.enrising.dcarbon.audit.RefereeDatasource;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class StaionStopRefreeContent extends AbstractRefereeContent implements RefereeDatasource {
    private Long billId;
    private Long pcid;
    private String stationCode;
    private String ammetername;
    /**
     * 项目名称
     */
    private String projectname;
    /**
     * 局站名称
     */
    private String stationname;
    /**
     * 局站地址
     */
    private String stationaddress;
    /**
     * 台账起始日期
     */
    private LocalDateTime startdate;
    /**
     * 台账结束日期
     */
    private LocalDateTime enddate;
    /**
     * 服务起始日期
     */
    private LocalDateTime servestartdate;
    /**
     * 服务结束日期
     */
    private LocalDateTime serveenddate;

    /**
     * 评判信息
     */
    private String exmsg;

}
