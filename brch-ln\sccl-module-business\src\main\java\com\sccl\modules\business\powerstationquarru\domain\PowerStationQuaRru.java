package com.sccl.modules.business.powerstationquarru.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;


/**
 * 站址rru能耗指标表 power_station_qua_rru
 * 
 * <AUTHOR>
 * @date 2021-09-14
 */
public class PowerStationQuaRru extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
    /** 地市 */
    private String citycode;
    /** 区县 */
    private String subcode;
    /** 站址 */
    private String resstationcode;
    /**  */
    private String resstationname;
    /** 站址 */
    private String stationcode;
    /** 日期 */
    private String currentdate;
    /** 电量 */
    private String stationquantity;
    /**  */
    private String rrusnr;
    /** 类型 */
    private String networktype;
    /**  */
    private String rruquantity;
    /** 0,1 */
    private Integer status;
    /** 接口标识ID */
    private String msgId;
    /**  */
    private String country;
    /**  */
    private String company;
    /** 反馈人 */
    private String remarker;


	public void setCitycode(String citycode)
	{
		this.citycode = citycode;
	}

	public String getCitycode() 
	{
		return citycode;
	}

	public void setSubcode(String subcode)
	{
		this.subcode = subcode;
	}

	public String getSubcode() 
	{
		return subcode;
	}

	public void setResstationcode(String resstationcode)
	{
		this.resstationcode = resstationcode;
	}

	public String getResstationcode() 
	{
		return resstationcode;
	}

	public void setResstationname(String resstationname)
	{
		this.resstationname = resstationname;
	}

	public String getResstationname() 
	{
		return resstationname;
	}

	public void setStationcode(String stationcode)
	{
		this.stationcode = stationcode;
	}

	public String getStationcode() 
	{
		return stationcode;
	}

	public void setCurrentdate(String currentdate)
	{
		this.currentdate = currentdate;
	}

	public String getCurrentdate() 
	{
		return currentdate;
	}

	public void setStationquantity(String stationquantity)
	{
		this.stationquantity = stationquantity;
	}

	public String getStationquantity() 
	{
		return stationquantity;
	}

	public void setRrusnr(String rrusnr)
	{
		this.rrusnr = rrusnr;
	}

	public String getRrusnr() 
	{
		return rrusnr;
	}

	public void setNetworktype(String networktype)
	{
		this.networktype = networktype;
	}

	public String getNetworktype() 
	{
		return networktype;
	}

	public void setRruquantity(String rruquantity)
	{
		this.rruquantity = rruquantity;
	}

	public String getRruquantity() 
	{
		return rruquantity;
	}


	public void setStatus(Integer status)
	{
		this.status = status;
	}

	public Integer getStatus() 
	{
		return status;
	}


	public void setMsgId(String msgId)
	{
		this.msgId = msgId;
	}

	public String getMsgId() 
	{
		return msgId;
	}

	public void setCountry(String country)
	{
		this.country = country;
	}

	public String getCountry() 
	{
		return country;
	}

	public void setCompany(String company)
	{
		this.company = company;
	}

	public String getCompany() 
	{
		return company;
	}


	public void setRemarker(String remarker)
	{
		this.remarker = remarker;
	}

	public String getRemarker() 
	{
		return remarker;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("citycode", getCitycode())
            .append("subcode", getSubcode())
            .append("resstationcode", getResstationcode())
            .append("resstationname", getResstationname())
            .append("stationcode", getStationcode())
            .append("currentdate", getCurrentdate())
            .append("stationquantity", getStationquantity())
            .append("rrusnr", getRrusnr())
            .append("networktype", getNetworktype())
            .append("rruquantity", getRruquantity())
            .append("delFlag", getDelFlag())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .append("msgId", getMsgId())
            .append("country", getCountry())
            .append("company", getCompany())
            .append("remark", getRemark())
            .append("remarker", getRemarker())
            .toString();
    }
}
