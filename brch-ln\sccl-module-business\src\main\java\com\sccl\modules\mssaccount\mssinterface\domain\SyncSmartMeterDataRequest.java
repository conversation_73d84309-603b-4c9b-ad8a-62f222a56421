package com.sccl.modules.mssaccount.mssinterface.domain;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 同步智能电表数据请求DTO
 *
 * <AUTHOR>
 * @date 2025-01-25
 */
@Data
public class SyncSmartMeterDataRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 采集时间
     * 单个日期格式：YYYY-MM-DD (如：2024-05-26)
     * 批量日期格式：YYYYMMDD-YYYYMMDD-YYYYMMDD (如：********-********-********)
     */
    @NotBlank(message = "采集时间不能为空")
    private String collectTime;

    /**
     * 是否更新或插入collectmeter表
     * true: 执行数据修正（更新/插入collectmeter表）
     * false: 只查询和推送，不修正数据
     * 默认值：true
     */
    private Boolean updateCollectMeter = true;
}
