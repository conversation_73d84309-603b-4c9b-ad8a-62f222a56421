package com.sccl.modules.business.modlepricesp.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;
import java.math.BigDecimal;


/**
 * 单价输配电价表 power_modle_pricesp
 * 
 * <AUTHOR>
 * @date 2023-03-08
 */
public class ModlePricesp extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
    /** 电压等级 10/10/-1(小于10） */
    private Integer voltagelevel;
    /** 工业用途 大工业/一般商业  */
    private String industrialuse;
    /** 输配电价 */
    private BigDecimal pricesp;
    /** 新增时间 */
    private Date updatetime;
    /** 录入人id */
    private String inputuserid;


	public void setVoltagelevel(Integer voltagelevel)
	{
		this.voltagelevel = voltagelevel;
	}

	public Integer getVoltagelevel() 
	{
		return voltagelevel;
	}

	public void setIndustrialuse(String industrialuse)
	{
		this.industrialuse = industrialuse;
	}

	public String getIndustrialuse() 
	{
		return industrialuse;
	}

	public void setPricesp(BigDecimal pricesp)
	{
		this.pricesp = pricesp;
	}

	public BigDecimal getPricesp() 
	{
		return pricesp;
	}


	public void setUpdatetime(Date updatetime)
	{
		this.updatetime = updatetime;
	}

	public Date getUpdatetime() 
	{
		return updatetime;
	}

	public void setInputuserid(String inputuserid)
	{
		this.inputuserid = inputuserid;
	}

	public String getInputuserid() 
	{
		return inputuserid;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("voltagelevel", getVoltagelevel())
            .append("industrialuse", getIndustrialuse())
            .append("pricesp", getPricesp())
            .append("delFlag", getDelFlag())
            .append("updatetime", getUpdatetime())
            .append("inputuserid", getInputuserid())
            .toString();
    }
}
