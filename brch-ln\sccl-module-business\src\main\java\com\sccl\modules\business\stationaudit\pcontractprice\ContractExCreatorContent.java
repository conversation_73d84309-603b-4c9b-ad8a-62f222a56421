package com.sccl.modules.business.stationaudit.pcontractprice;

import com.enrising.dcarbon.audit.AbstractRefereeContent;
import com.enrising.dcarbon.audit.RefereeDatasource;
import com.enrising.dcarbon.audit.RefereeResult;
import com.sccl.modules.autojob.util.convert.StringUtils;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class ContractExCreatorContent extends AbstractRefereeContent implements RefereeDatasource, Serializable {
   private String ammetername;
   private String price;
   private String TransferPowerSupplyContractPrice;
    private Long billId;
    private Long pcid;
    private String exmg;


    public ContractExCreatorContent(RefereeResult refereeResult, int step, String auditKey) {
        super(refereeResult, step, auditKey);
    }

    public static String getStrContract(ContractExCreatorContent pa) {
        String price = pa.getPrice();
        String contractPrice = pa.getTransferPowerSupplyContractPrice();
        String ammetername = pa.getAmmetername();
        if (StringUtils.isBlank(price) && StringUtils.isBlank(contractPrice)) {
            return String.format("对应电表单价及签约单价为空");
        }
        if (StringUtils.isBlank(price)) {
            return String.format("对应电表单价为空");
        }
        if (StringUtils.isBlank(contractPrice)) {
            return String.format("对应签约单价为空");
        }
        return String.format("对应电表单价,签约单价正常");
    }


    public void setBillId(Long billId) {
        this.billId = billId;
    }

    public Long getBillId() {
        return billId;
    }

    public void setPcid(Long pcid) {
        this.pcid = pcid;
    }

    public Long getPcid() {
        return pcid;
    }

    public void setExmg(String exmg) {
        this.exmg = exmg;
    }

    public String getExmg() {
        return exmg;
    }
}
