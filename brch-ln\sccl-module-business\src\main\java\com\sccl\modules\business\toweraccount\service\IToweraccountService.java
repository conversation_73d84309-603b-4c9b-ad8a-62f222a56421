package com.sccl.modules.business.toweraccount.service;

import com.sccl.modules.business.account.domain.Account;
import com.sccl.modules.business.msg.domain.Message;
import com.sccl.modules.business.toweraccount.domain.TowerData;
import com.sccl.framework.service.IBaseService;
import com.sccl.modules.business.toweraccount.domain.mssbusi;
import com.sccl.modules.system.attachments.domain.Attachments;

import java.util.List;
import java.util.Map;

/**
 * 铁塔接口 服务层
 * 
 * <AUTHOR>
 * @date 2021-08-12
 */
public interface IToweraccountService extends IBaseService<TowerData>
{
    public List<TowerData> selectByList(TowerData towerdata);
    public List<TowerData> selectListByIds(String[] ids);
    public List<TowerData> selectListByOrgid(String orgid);
    public List<String> selectListtowidsByIds (String[] ids);
    public Map<String, Object> selectlst(Account account);
    public void deloldaccount(Account account);
    public int inserttaaccount(Account account);
    public void upannix(Attachments attachments,Long pcid);
    public int addaccount(String ids);
    public int batchaccount(String orgid);
    public Message selectLstMsg(Long id);
    public List<mssbusi> seletmssbusi(TowerData towerdata);
}
