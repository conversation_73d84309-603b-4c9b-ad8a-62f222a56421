package com.sccl.modules.business.meterinfoalljt_new.mapper;

import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.meterinfoalljt_new.domain.MeterinfoAllJtApply;
import com.sccl.modules.business.meterinfoalljt_new.vo.MeterinfoAllJtApplyResultVo;
import com.sccl.modules.business.meterinfoalljt_new.vo.MeterinfoAllJtApplySaveVo;
import com.sccl.modules.business.meterinfoalljt_new.vo.MeterinfoAllJtApplySearchVo;
import com.sccl.modules.mssaccount.mssinterface.domain.MeterInfo3;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 在网表计数据清单-新增申请
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@Mapper
public interface MeterinfoAllJtApplyMapper extends BaseMapper<MeterinfoAllJtApply>
{
    /**
     * 根据主键id数组批量更新
     * @param entity
     * @return
     */
    int updateForModelBatch(MeterinfoAllJtApply entity);

    /**
     * 一览查询
     * @param searchVo
     * @return
     */
    List<MeterinfoAllJtApplyResultVo> list(MeterinfoAllJtApplySearchVo searchVo);

    /**
     * 待添加电表一览查询
     * @param searchVo
     * @return
     */
    List<MeterinfoAllJtApplyResultVo> ammeterList(MeterinfoAllJtApplySearchVo searchVo);

    /**
     * 添加-保存
     * @param entity
     * @return
     */
    int save(MeterinfoAllJtApplySaveVo entity);

    /**
     * 校验数据状态是否存在不为‘草稿’的数据
     * @param ids
     */
    int checkByIds(@Param("ids") String[] ids);

    /**
     * 查询需要推送的数据
     * @param provinceCode 省局组织编码 财辅组织
     * @return
     */
    List<MeterInfo3> meterInfoList(@Param("provinceCode") String provinceCode);

    /**
     * 根据网表计清单，更新数据状态为已处理
     * @return
     */
    int completed();
}
