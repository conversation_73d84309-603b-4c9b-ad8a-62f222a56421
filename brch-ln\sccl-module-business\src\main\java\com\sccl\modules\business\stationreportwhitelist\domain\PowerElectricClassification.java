package com.sccl.modules.business.stationreportwhitelist.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

/**
 * <AUTHOR>
 * @date 2024/4/23 13:34
 * @describe 电表用电类型
 */
@Getter
@Setter
@TableName(value = "power_electric_classification")
public class PowerElectricClassification {

    /**
     * ID，唯一标识
     */
    private Long id;

    /**
     * 用电类型名称
     */
    private String typeName;

    /**
     * 上级ID，指向父级电力分类
     */
    private Long parentId;

    /**
     * 删除标记，0表示已删除，1表示在用
     */
    private Integer delFlag;

    /**
     * 是否包含子节点标记，0表示否，1表示是
     */
    @TableField(value = "isChild")
    private Integer isChild;

    @Override
    public String toString() {
    	return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }
}
