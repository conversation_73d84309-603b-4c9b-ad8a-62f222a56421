package com.sccl.modules.business.powerappdbyc.service;

import com.sccl.modules.business.powerappdbyc.domain.PowerAppDbyc;
import com.sccl.framework.service.IBaseService;
import com.sccl.modules.business.toweraccount.domain.TowerData;

import java.util.List;

/**
 * 大数据基站异常 服务层
 * 
 * <AUTHOR>
 * @date 2022-03-20
 */
public interface IPowerAppDbycService extends IBaseService<PowerAppDbyc>
{
    public List<PowerAppDbyc> selectByList(PowerAppDbyc powerAppDbyc);
}
