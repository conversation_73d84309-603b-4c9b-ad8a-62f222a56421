package com.sccl.modules.business.modelegetprice.mapper;

import com.sccl.modules.business.modelegetprice.domain.ModeleGetprice;
import com.sccl.framework.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 来自官网的单价数据 数据层
 * 
 * <AUTHOR>
 * @date 2023-03-03
 */
public interface ModeleGetpriceMapper extends BaseMapper<ModeleGetprice>
{


    ModeleGetprice selectByLatest( ModeleGetprice modeleGetprice);

    int updatebitch(@Param("list") List<ModeleGetprice> modeleGetprices);
}