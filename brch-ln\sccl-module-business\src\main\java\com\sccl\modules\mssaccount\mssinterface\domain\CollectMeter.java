package com.sccl.modules.mssaccount.mssinterface.domain;

import cn.hutool.core.util.StrUtil;
import com.sccl.modules.autojob.util.convert.StringUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.jsoup.helper.StringUtil;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 同步智能采集的电量数据
 */
@Data
public class CollectMeter implements Serializable {
    /**
     * 数据采集时间
     */
    private String collectTime;
    /**
     * 市局组织编码
     */
    protected String cityCode;
    /**
     * 市局组织名称
     */
    private String cityName;
    /**
     * 区县组织编码
     */
    protected String countyCode;
    /**
     * 区县组织名称
     */
    private String countyName;
    /**
     * 局站编码
     */
    protected String stationCode;
    /**
     * 局站表名称
     */
    private String stationName;
    /**
     * 局站编码
     */
    private String parentStationCode = "";
    /**
     * 折标系数
     */
    private String ccoer = "";
    /**
     * 碳排放因子
     */
    private String cdcf = "";
//    /**
//     * 能耗编码
//     */
//    private String energyType = "45";
    /**
     * 总能耗
     */
    protected String energyData;
    /**
     * 总能耗数据来源
     */
    private String energyDataSource = "223";
    /**
     * 市电电量(小数点2位)
     */
    private String acData;
    /**
     * 市电数据来源
     */
    private String acDataSource = "223";
    /**
     * 油机发电电量
     */
    private String oepgData;
    /**
     * 油机发电数据来源
     */
    private String oepgDataSource = "2";
    /**
     * 光伏发电电量
     */
    private String pvpgData;
    /**
     * 光伏发电数据来源
     */
    private String pvpgDataSource = "2";
    /**
     * 主设备或IT设备能耗
     */
    private String deviceData;
    /**
     * 设备能耗数据来源
     */
    private String deviceDataSource = "2";
    /**
     * 生产分摊能耗
     */
    private String productionData;
    /**
     * 设备能耗数据来源
     */
    private String productionDataSource = "2";
    /**
     * 管理分摊能耗
     */
    private String managementData = "0";
    /**
     * 设备能耗数据来源
     */
    private String managementDataSource = "2";
    /**
     * 营业分摊能耗
     */
    private String businessData = "0";
    /**
     * 设备能耗数据来源
     */
    private String businessDataSource = "2";
    /**
     * 其他分摊能耗
     */
    private String otherData = "0";
    /**
     * 设备能耗数据来源
     */
    private String otherDataSource = "2";

    public CollectMeter() {
    }

    public CollectMeter(String collectTime, String stationCode, String stationName, String parentStationCode,
                        String ccoer, String cdcf, String energyData, String energyDataSource, String deviceData,
                        String deviceDataSource, String productionData, String productionDataSource,
                        String managementData, String managementDataSource, String businessData,
                        String businessDataSource, String otherData, String otherDataSource) {
        this.collectTime = collectTime;
        this.stationCode = stationCode;
        this.stationName = stationName;
        this.parentStationCode = parentStationCode;
        this.ccoer = ccoer;
        this.cdcf = cdcf;
        this.energyData = energyData;
        this.energyDataSource = energyDataSource;
        this.deviceData = deviceData;
        this.deviceDataSource = deviceDataSource;
        this.productionData = productionData;
        this.productionDataSource = productionDataSource;
        this.managementData = managementData;
        this.managementDataSource = managementDataSource;
        this.businessData = businessData;
        this.businessDataSource = businessDataSource;
        this.otherData = otherData;
        this.otherDataSource = otherDataSource;
    }

    public static void check(CollectMeter collectMeter) {
        if (collectMeter.getCountyName() == null) {
            collectMeter.setCountyName("未知");
        }
        if (collectMeter.getEnergyData() == null) {
            collectMeter.setEnergyData("0");
        }
        if (collectMeter.getAcData() == null) {
            collectMeter.setAcData("0");
        }
        if (collectMeter.getOepgData() == null) {
            collectMeter.setOepgData("0");
        }
        if (collectMeter.getPvpgData() == null) {
            collectMeter.setPvpgData("0");
        }
        if (collectMeter.getDeviceData() == null) {
            collectMeter.setDeviceData("0");
        }
        if (collectMeter.getProductionData() == null) {
            collectMeter.setProductionData("0");
        }
        if (collectMeter.getManagementData() == null) {
            collectMeter.setManagementData("0");
        }
        if (collectMeter.getBusinessData() == null) {
            collectMeter.setBusinessData("0");
        }
        if (collectMeter.getOtherData() == null) {
            collectMeter.setOtherData("0");
        }
    }

    public static void processDataFields(List<CollectMeter> collectMeterList) {
        collectMeterList.stream()
                .filter(Objects::nonNull) // Filter out any null objects from the list
                .forEach(collectMeter -> {
                    collectMeter.setEnergyData(processData(collectMeter.getEnergyData()));
                    collectMeter.setAcData(processData(collectMeter.getAcData()));
                    collectMeter.setOepgData(processData(collectMeter.getOepgData()));
                    collectMeter.setPvpgData(processData(collectMeter.getPvpgData()));
                    collectMeter.setDeviceData(processData(collectMeter.getDeviceData()));
                    collectMeter.setProductionData(processData(collectMeter.getProductionData()));
                    collectMeter.setManagementData(processData(collectMeter.getManagementData()));
                    collectMeter.setBusinessData(processData(collectMeter.getBusinessData()));
                    collectMeter.setOtherData(processData(collectMeter.getOtherData()));
                });
    }

    private static String processData(String dataField) {
        return dataField != null && !dataField.isEmpty() ? dataField : "0";
    }

    public static List<CollectMeter> ConvertToFail(List<CollectMeter> infos, String syncFlag, String failMag, String sync_time, String budget) {
        return infos.stream().map(
                item -> {
                    CollectMeterFail fail = new CollectMeterFail();
                    BeanUtils.copyProperties(item, fail);
                    fail.setBudget(budget);
                    fail.setSyncFlag(syncFlag);
                    fail.setFailMag(failMag);
                    fail.setSync_time(sync_time);
                    return fail;
                }
        ).collect(Collectors.toList());
    }

    public static void main(String[] args) {
        List<String> inputList = new ArrayList<>();
        inputList.add("1/60/30/2");
        inputList.add("1/90/30/6");
        inputList.add("2/30/15/1");
        Double aDouble = CalcPower(inputList);
        System.out.println();
    }

    public static List<CollectMeter> failConvertToSync(List<CollectMeter> collectMeterInfors) {
        return collectMeterInfors.stream().map(
                item -> {
                    CollectMeter sync = new CollectMeter();
                    BeanUtils.copyProperties(item, sync);
                    return sync;
                }
        ).collect(Collectors.toList());
    }

    public static void initData(List<CollectMeter> collectMeterInfors) {
        collectMeterInfors.stream().filter(Objects::nonNull)
                .forEach(
                        item -> ProcessAve(item)
                );
    }

    public static void ProcessAve(CollectMeter item) {
        String energyData = item.getEnergyData();
        List<String> list = Arrays.stream(energyData.split(",")).collect(Collectors.toList());
        Double energyDate = CalcPower(list);
        item.setEnergyData(energyData);
        item.setAcData(energyData);
    }

    public static Double CalcPower(List<String> inputList) {
        if (CollectionUtils.isEmpty(inputList)) {
            return 0.0;
        }
        // 创建一个Map，用于存储每个电表编号对应的电量、天数和个数
        Map<String, Double[]> idToValues = new HashMap<>();

        // 遍历输入列表，将数据分解并存储在Map中
        for (String input : inputList) {
            String[] parts = input.split("/");
            if (parts.length == 4) {
                String id = parts[0];
                double power = Double.parseDouble(parts[1]);
                double days = Double.parseDouble(parts[2]);
                double count = Double.parseDouble(parts[3]);

                // 更新Map中的数据
                if (idToValues.containsKey(id)) {
                    Double[] values = idToValues.get(id);
                    values[0] += power / days / count;
                    values[1] += 1.0;
                } else {
                    Double[] values = {power / days / count, 1.0};
                    idToValues.put(id, values);
                }
            }
        }
        // 计算最终电量
        double finalPower = 0.0;
        for (Double[] values : idToValues.values()) {
            finalPower += values[0] / values[1];
        }
        return finalPower;
    }

    public static List<CollectMeter> processEnergyData(List<CollectMeter> collectMeterInfors) {
        List<CollectMeter> collect = collectMeterInfors.stream().filter(
                item -> {
                    String energyData = item.getEnergyData();
                    if (StringUtil.isBlank(energyData)) {
                        energyData = "0";
                    }
                    BigDecimal b1 = new BigDecimal(energyData);

                    return b1.compareTo(BigDecimal.ZERO) > 0
                            &&
                            StringUtils.isNotBlank(item.getCountyCode())
                            &&
                            StringUtils.isNotBlank(item.getCityCode());
                }
        ).collect(Collectors.toList());
        return collect;
    }


    public static List<CollectMeter> addCollects(List<CollectMeter> collectMeterInfors, List<CollectMeter> synccollectMeters) {
        List<CollectMeter> updateMeters = getaddCollects(collectMeterInfors, synccollectMeters);
        updateMeters.forEach(
                syncmeter -> {
                    Optional<CollectMeter> matchMeter = collectMeterInfors.stream().filter(
                                    infometer -> CollectMeter.StaEquOtherNotEqu(infometer, syncmeter))
                            .findFirst();
                    matchMeter.ifPresent(
                            infometer -> {
                                BeanUtils.copyProperties(
                                        infometer,
                                        syncmeter,
                                        "cityCode", "countyCode", "stationCode");
                            }
                    );
                }
        );

        return updateMeters;
    }

    public static List<CollectMeter> getaddCollects(List<CollectMeter> collectMeterInfors, List<CollectMeter> synccollectMeters) {
        return synccollectMeters.stream().filter(
                syncmeter ->
                        collectMeterInfors.stream().anyMatch(
                                infometer -> CollectMeter.StaEquOtherNotEqu(infometer, syncmeter)
                        )

        ).collect(Collectors.toList());
    }

    private static boolean StaEquOtherNotEqu(CollectMeter infometer, CollectMeter syncmeter) {
        return syncmeter.getStationCode().equals(infometer.getStationCode())
                &&
                (!syncmeter.getcityAndCountry().equals(infometer.getcityAndCountry()));
    }

    public static List<CollectMeter> tempList(List<CollectMeter> tempList, List<CollectMeter> synccollectMeters) {
        List<CollectMeter> resultList = tempList.stream()
                .flatMap(
                        temp -> {
                            String tempStationCode = temp.getStationCode();
                            return synccollectMeters.stream()
                                    .filter(item -> item.getStationCode().equals(tempStationCode))
                                    .map(
                                            item -> {
                                                BeanUtils.copyProperties(temp, item,
                                                        "cityCode", "cityName",
                                                        "countyCode", "countyName",
                                                        "stationCode", "stationName");
                                                return item;
                                            });

                        }
                )
                .filter(item -> item != null &&
                        StringUtils.isNotBlank(item.getCityCode()) &&
                        StringUtils.isNotBlank(item.getCountyCode())
                )
                .collect(Collectors.toList());
        return resultList;

    }

    public static List<CollectMeter> tempList2(List<CollectMeter> tempList, List<CollectMeter> otherList) {
        List<CollectMeter> collect = tempList.stream().filter(
                item -> {
                    String stationCode1 = item.getStationCode();
                    return !otherList.stream().filter(
                            item1 -> item1.getStationCode().equals(stationCode1)
                    ).findAny().isPresent();
                }
        ).collect(Collectors.toList());
        return null;
    }

    private String getcityAndCountry() {
        return this.getCityCode() + "_" + this.getCountyCode();
    }


    public String getCollectTime() {
        return collectTime;
    }

    public void setCollectTime(String collectTime) {
        this.collectTime = collectTime;
    }

    public String getStationCode() {
        return stationCode;
    }

    public void setStationCode(String stationCode) {
        this.stationCode = stationCode;
    }

    public String getStationName() {
        return stationName;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }

    public String getParentStationCode() {
        return parentStationCode;
    }

    public void setParentStationCode(String parentStationCode) {
        this.parentStationCode = parentStationCode;
    }

    public String getCcoer() {
        return ccoer;
    }

    public void setCcoer(String ccoer) {
        this.ccoer = ccoer;
    }

    public String getCdcf() {
        return cdcf;
    }

    public void setCdcf(String cdcf) {
        this.cdcf = cdcf;
    }

    public String getEnergyData() {
        return energyData;
    }

    public void setEnergyData(String energyData) {
        if (StrUtil.isBlank(energyData)) {
            this.energyData = "0";
            this.deviceData = "0";
            this.productionData = "0";
            this.acData = "0";
        } else {
            this.energyData = energyData;
            this.productionData = this.energyData;
            this.acData = this.energyData;
        }
    }

    public String getEnergyDataSource() {
        return energyDataSource;
    }

    public void setEnergyDataSource(String energyDataSource) {
        this.energyDataSource = energyDataSource;
    }

    public String getDeviceData() {
        return deviceData;
    }

    public void setDeviceData(String deviceData) {
        this.deviceData = deviceData;
    }

    public String getDeviceDataSource() {
        return deviceDataSource;
    }

    public void setDeviceDataSource(String deviceDataSource) {
        this.deviceDataSource = deviceDataSource;
    }

    public String getProductionData() {
        return productionData;
    }

    public void setProductionData(String productionData) {
        this.productionData = productionData;
    }

    public String getProductionDataSource() {
        return productionDataSource;
    }

    public void setProductionDataSource(String productionDataSource) {
        this.productionDataSource = productionDataSource;
    }

    public String getManagementData() {
        return managementData;
    }

    public void setManagementData(String managementData) {
        this.managementData = managementData;
    }

    public String getManagementDataSource() {
        return managementDataSource;
    }

    public void setManagementDataSource(String managementDataSource) {
        this.managementDataSource = managementDataSource;
    }

    public String getBusinessData() {
        return businessData;
    }

    public void setBusinessData(String businessData) {
        this.businessData = businessData;
    }

    public String getBusinessDataSource() {
        return businessDataSource;
    }

    public void setBusinessDataSource(String businessDataSource) {
        this.businessDataSource = businessDataSource;
    }

    public String getOtherData() {
        return otherData;
    }

    public void setOtherData(String otherData) {
        this.otherData = otherData;
    }

    public String getOtherDataSource() {
        return otherDataSource;
    }

    public void setOtherDataSource(String otherDataSource) {
        this.otherDataSource = otherDataSource;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getCountyCode() {
        return countyCode;
    }

    public void setCountyCode(String countyCode) {
        this.countyCode = countyCode;
    }

    public String getCountyName() {
        return countyName;
    }

    public void setCountyName(String countyName) {
        this.countyName = countyName;
    }

    public String getAcData() {
        return acData;
    }

    public void setAcData(String acData) {
        this.acData = acData;
    }

    public String getAcDataSource() {
        return acDataSource;
    }

    public void setAcDataSource(String acDataSource) {
        this.acDataSource = acDataSource;
    }

    public String getOepgData() {
        return oepgData;
    }

    public void setOepgData(String oepgData) {
        this.oepgData = oepgData;
    }

    public String getOepgDataSource() {
        return oepgDataSource;
    }

    public void setOepgDataSource(String oepgDataSource) {
        this.oepgDataSource = oepgDataSource;
    }

    public String getPvpgData() {
        return pvpgData;
    }

    public void setPvpgData(String pvpgData) {
        this.pvpgData = pvpgData;
    }

    public String getPvpgDataSource() {
        return pvpgDataSource;
    }

    public void setPvpgDataSource(String pvpgDataSource) {
        this.pvpgDataSource = pvpgDataSource;
    }

    public boolean filterCityAndCountry(CollectMeter item) {
        String cityCode1 = item.getCityCode();
        String countyCode1 = item.getCountyCode();
        String stationCode1 = item.getStationCode();

        return StringUtils.isNotBlank(cityCode1)
                &&
                StringUtils.isNotBlank(countyCode1)
                &&
                StringUtils.isNotBlank(stationCode1);
    }

    public boolean filterCityAndCountryOpposite(CollectMeter item) {
        String cityCode1 = item.getCityCode();
        String countyCode1 = item.getCountyCode();
        String stationCode1 = item.getStationCode();

        return StringUtils.isNotBlank(stationCode1)
                &&
                (StringUtils.isBlank(cityCode1)
                        ||
                        StringUtils.isBlank(countyCode1));
    }

    public Stream<CollectMeter> flatMap() {
        List<CollectMeter> list = new ArrayList<>();
        String key = this.getCityCode();
        //1301||130113,1302||130213
        if (StringUtils.isNotBlank(key)) {
            for (String s : key.split(",")) {
                CollectMeter temp = new CollectMeter();
                temp.setCityCode(s.split("#")[0]);
                temp.setCountyCode(s.split("#")[1]);
                BeanUtils.copyProperties(this, temp, "cityCode", "countyCode");
                list.add(temp);
            }
        } else {
            list.add(this);
        }
        return list.stream();
    }
}
