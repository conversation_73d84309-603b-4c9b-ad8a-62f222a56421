package com.sccl.modules.inspection.inspectioninfo.service;

import com.sccl.modules.inspection.inspectioninfo.domain.InspectionInfo;
import com.sccl.framework.service.IBaseService;

import java.util.List;

/**
 * 巡检记录 服务层
 * 
 * <AUTHOR>
 * @date 2019-05-27
 */
public interface IInspectionInfoService extends IBaseService<InspectionInfo>
{

    public List<InspectionInfo> selectListByCondition(InspectionInfo inspectionInfo);

    Object update(List<InspectionInfo> inspectionInfoObj);

    public List getAllAccountno(String company);
}
