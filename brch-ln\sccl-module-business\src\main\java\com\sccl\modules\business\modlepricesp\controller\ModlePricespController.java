package com.sccl.modules.business.modlepricesp.controller;

import com.alibaba.fastjson.JSONObject;
import com.sccl.common.support.Convert;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.modlepricesp.domain.ModlePricesp;
import com.sccl.modules.business.modlepricesp.service.IModlePricespService;
import com.sccl.modules.system.role.domain.Role;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.service.IUserService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 单价输配电价 信息操作处理
 *
 * <AUTHOR>
 * @date 2023-03-08
 */
@RestController
@RequestMapping("/business/modlePricesp")
public class ModlePricespController extends BaseController {
    private String prefix = "business/modlePricesp";
    @Autowired
    private IUserService userService;
    @Autowired
    private IModlePricespService modlePricespService;

    @RequiresPermissions("business:modlePricesp:view")
    @GetMapping()
    public String modlePricesp() {
        return prefix + "/modlePricesp";
    }

    /**
     * 查询单价输配电价列表
     */
    @RequiresPermissions("business:modlePricesp:list")
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(ModlePricesp modlePricesp) {
        //权限判定 组装
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("admin")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("PROVI_")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
        }
        startPage();
        List<ModlePricesp> list = modlePricespService.selectList(modlePricesp);
        return getDataTable(list);
    }

    /**
     * 新增单价输配电价
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存单价输配电价
     */
    @RequiresPermissions("business:modlePricesp:add")
    @Log(title = "单价输配电价", action = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody List<ModlePricesp> modlePricesps) {
        //权限判定 组装
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("PROVI_")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
        }
        if (isProAdmin == false && isCityAdmin == false) {
            return AjaxResult.success("只有省管理员或市管理员有权限操作");
        }
        return toAjax(modlePricespService.insertList(modlePricesps));
    }

    /**
     * 批量更新
     * @param modlePricesps
     * @return
     */
    @PostMapping("updatebitch")
    @ResponseBody
    public AjaxResult updatebitch(@RequestBody List<ModlePricesp> modlePricesps) {
        //权限判定 组装
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("PROVI_")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
        }
        if (isProAdmin == false && isCityAdmin == false) {
            return AjaxResult.success("只有省管理员或市管理员有权限操作");
        }
        return AjaxResult.success(modlePricespService.updatebitch(modlePricesps));
    }

    /**
     * 修改单价输配电价
     */
    @GetMapping("/edit/{id}")
    public AjaxResult edit(@PathVariable("id") Long id) {
        ModlePricesp modlePricesp = modlePricespService.get(id);

        Object object = JSONObject.toJSON(modlePricesp);

        return this.success(object);
    }

    /**
     * 修改保存单价输配电价
     */
    @RequiresPermissions("business:modlePricesp:edit")
    @Log(title = "单价输配电价", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody ModlePricesp modlePricesp) {
        //权限判定 组装
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("admin")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("PROVI_")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
        }
        if (isProAdmin == false && isCityAdmin == false) {
            return AjaxResult.success("只有省管理员或市管理员有权限操作");
        }
        return toAjax(modlePricespService.update(modlePricesp));
    }

    /**
     * 删除单价输配电价
     */
    @RequiresPermissions("business:modlePricesp:remove")
    @Log(title = "单价输配电价", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(modlePricespService.deleteByIds(Convert.toStrArray(ids)));
    }


    /**
     * 查看单价输配电价
     */
    @RequiresPermissions("business:modlePricesp:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id) {
        ModlePricesp modlePricesp = modlePricespService.get(id);
        Object object = JSONObject.toJSON(modlePricesp);

        return this.success(object);
    }

}
