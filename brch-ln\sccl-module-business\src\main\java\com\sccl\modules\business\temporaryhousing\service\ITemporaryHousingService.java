package com.sccl.modules.business.temporaryhousing.service;

import com.sccl.framework.service.IBaseService;
import com.sccl.modules.business.temporaryhousing.domain.TemporaryHousing;

import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * __房屋临时表__<br/>
 * 2019/10/31
 *
 * <AUTHOR>
 */
public interface ITemporaryHousingService extends IBaseService<TemporaryHousing> {

    /**
     * @Description: 删除全部
     * @author: dongk
     * @date: 2019/10/23
     * @param:
     * @return:
     */
    int deleteAll();

    /**
     * @Description: 导入临时铁塔
     * @author: dongk
     * @date: 2019/10/24
     * @param:
     * @return:
     */
    List<TemporaryHousing> importExcel(String sheetName, InputStream input,Integer type) throws Exception;

    /**
     * @Description: 加入正式铁塔表
     * @author: dongk
     * @date: 2019/10/24
     * @param:
     * @return:
     */
    int initHousingInfo();

    /**
     * @Description: 生成局站
     * @author: dongk
     * @date: 2019/10/31
     * @param:
     * @return:
     */
    Map<String,Object> importHousing();

    /**
     * @Description: 删除重复数据
     * @author: dongk
     * @date: 2019/11/6
     * @param:
     * @return:
     */
    int deleteRepeat();

    /**
     * @Description: 生成局站
     * @author: dongk
     * @date: 2019/11/6
     * @param:
     * @return:
     */
    int insertHousingInfo();
}
