package com.sccl.modules.business.mssaccountprepaid.service;

import com.sccl.modules.business.mssaccountprepaid.domain.MssAccountPrepaid;
import com.sccl.framework.service.IBaseService;

import java.math.BigDecimal;

/**
 * 关联交易客户代垫及收款 服务层
 * 
 * <AUTHOR>
 * @date 2021-10-24
 */
public interface IMssAccountPrepaidService extends IBaseService<MssAccountPrepaid>
{
public BigDecimal computeBalance(MssAccountPrepaid mssAccountPrepaid);
	
}
