package com.sccl.modules.business.examine.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 转供电考核-详情 查询条件对象
 */
@Data
public class TransferExamineXqSearchVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 关键字 项目名称/局站名称关键字模糊查询
     */
    private String key;

    /**
     * 数据部门
     */
    private String company;

    /**
     * 电表编号/户号精确查询
     */
    private String ammeterCode;

    /**
     * 站址编码精确查询
     */
    private String resstationCode;

    /**
     * 账期 为空则查基础数据表 transfer_examine_base
     */
    private String accountPeriod;

    /**
     * 单价开始
     * 1 1≤单价<1.2
     * 2 1.2≤单价<1.5
     * 3 1.5≤单价<2
     * 4 2≤单价
     */
    private String flag;
    private Double priceStart;//单价开始
    private Double priceEnd;  //单价结束
}
