package com.sccl.modules.business.powerlumpprice.controller;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.sccl.common.utils.ShiroUtils;
import com.sccl.modules.system.organization.service.IOrganizationService;
import com.sccl.modules.system.user.domain.User;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.business.powerlumpprice.domain.PowerLumpprice;
import com.sccl.modules.business.powerlumpprice.service.IPowerLumppriceService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 铁塔包干单价维护 信息操作处理
 * 
 * <AUTHOR>
 * @date 2019-06-19
 */
@RestController
@RequestMapping("/business/lumpprice")
public class PowerLumppriceController extends BaseController
{
    private String prefix = "business/lumpprice";
	
	@Autowired
	private IPowerLumppriceService powerLumppriceService;

	@Autowired
	private IOrganizationService organizationService;

//	@RequiresPermissions("business:lumpprice:view")
	@GetMapping()
	public String lumpprice()
	{
	    return prefix + "/lumpprice";
	}
	
	/**
	 * 查询铁塔包干单价维护列表
	 */
//	@RequiresPermissions("business:powerLumpprice:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(PowerLumpprice powerLumpprice)
	{
		List<Map<String,Object>> countrys = new ArrayList<>();
		if(null != powerLumpprice.getCompany() && (null == powerLumpprice.getOrgid() || "-1".equals(powerLumpprice.getOrgid().toString()) )){
			countrys = organizationService.selectSubordinateOrgByRole(powerLumpprice.getCompany().toString(),"1");
			if(null == countrys || countrys.size() == 0){
				countrys = new ArrayList<>();
			}
			powerLumpprice.setOrgid(null);
		}
		powerLumpprice.setCountrys(countrys);
		startPage();
        List<Map<String,Object>> list = powerLumppriceService.selectByList(powerLumpprice);
		return getDataTable(list);
	}
	
	/**
	 * 新增铁塔包干单价维护
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存铁塔包干单价维护
	 */
//	@RequiresPermissions("business:powerLumpprice:add")
	//@Log(title = "铁塔包干单价维护", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody PowerLumpprice powerLumpprice)
	{
		User user = ShiroUtils.getUser();
		powerLumpprice.setInputuserid(user.getId());
		powerLumpprice.setInputusername(user.getUserName());
		return toAjax(powerLumppriceService.insert(powerLumpprice));
	}

	/**
	 * 修改铁塔包干单价维护
	 */
	@GetMapping("/edit")
	public AjaxResult edit(Long plpId)
	{
		Map<String,Object> powerLumpprice = powerLumppriceService.selectById(plpId);

		Object object = JSONObject.toJSON(powerLumpprice);

        return this.success(object);
	}
	
	/**
	 * 同一部门下设置的RRU单价的启用时间和停用时间不能有重合
	 */
//	@RequiresPermissions("business:powerLumpprice:edit")
	@PostMapping("/checkDate")
	@ResponseBody
	public AjaxResult checkDate(@RequestBody PowerLumpprice powerLumpprice)
	{
		List<Map<String,Object>> maps = powerLumppriceService.checkDate(powerLumpprice);
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		SimpleDateFormat format1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		try {
			Date start = format.parse(format1.format(powerLumpprice.getStartdate()));
			Date end = format.parse(format1.format(powerLumpprice.getEnddate()));
			for (Map<String,Object> map:maps) {
				Date startdate = format.parse(map.get("startdate").toString());
				Date enddate = format.parse(map.get("enddate").toString());
				//判断开始时间或者结束时间是否在区间内
				//判断查询出的时间是否在开始时间或者结束时间之间
				if((start.after(startdate) && !start.after(enddate) || !end.before(startdate) && end.before(enddate))||(startdate.after(start) && !startdate.after(end) || !enddate.before(start) && enddate.before(end))){
					if (null == powerLumpprice.getPlpId() || (null != powerLumpprice.getPlpId() && !powerLumpprice.getPlpId().toString().equals(map.get("plp_id").toString()))) {
						return this.success(-1);
					}
				}
			}
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return this.success(1);
	}
	/**
	 * 修改保存铁塔包干单价维护
	 */
//	@RequiresPermissions("business:powerLumpprice:edit")
	//@Log(title = "铁塔包干单价维护", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody PowerLumpprice powerLumpprice)
	{
		return toAjax(powerLumppriceService.update(powerLumpprice));
	}
	
	/**
	 * 删除铁塔包干单价维护
	 */
//	@RequiresPermissions("business:powerLumpprice:remove")
	//@Log(title = "铁塔包干单价维护", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(powerLumppriceService.deleteByIdsDB(Convert.toStrArray(ids)));
	}


    /**
     * 查看铁塔包干单价维护
     */
//    @RequiresPermissions("business:powerLumpprice:view")
    @GetMapping("/view/{plpId}")
    @ResponseBody
    public AjaxResult view(@PathVariable("plpId") Long plpId)
    {
		PowerLumpprice powerLumpprice = powerLumppriceService.get(plpId);

        Object object = JSONObject.toJSON(powerLumpprice);

        return this.success(object);
    }

}
