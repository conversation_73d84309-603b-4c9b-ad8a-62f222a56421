package com.sccl.modules.business.oilcard.domain;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 油卡表 oil_card
 *
 * <AUTHOR>
 * @date 2021-12-16
 */
@Getter
@Setter
public class OilCard extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 油卡编号
     */
    private String oilCardId;
    /**
     * 所属区县
     */
    private Long country;
    /**
     * 余额
     */
    private BigDecimal residue;
    /**
     * 充值金额
     */
    private BigDecimal recharge;
    /**
     * 分公司
     */
    private Long company;
    /**
     * 分局、支局
     */
    private String substation;
    /**
     * 油卡使用者
     */
    private String cardMaster;
    /**
     * 单据状态
     */
    private Integer billStatus;

    private Integer status;

    private Long procInstId;
    private Date createTime;

}
