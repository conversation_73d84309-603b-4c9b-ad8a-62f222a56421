package com.sccl.modules.oss.collect;

import com.enrising.dcarbon.audit.AuditContext;
import com.enrising.dcarbon.collector.*;
import com.enrising.dcarbon.redis.RedisUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-06-29 13:55
 * @email <EMAIL>
 */
@Slf4j
public class OssMsgCollectorTemplate extends AbstractCollectorTemplate {
    private final OssMsgCollector collector = new OssMsgCollector();

    @Override
    protected Collector collector() {
        return collector;
    }

    @Override
    protected CollectedDatasourceHandlerChain handlerChain() {
        return CollectedDatasourceHandlerChain
                .builder()
                .addHandler(new RepeatHandler())
                .build();
    }

    @Override
    protected AuditContext auditContext() {
        return null;
    }

    @Override
    protected void afterRoute(List<CollectedDatasource> datasourceList) {
        super.afterRoute(datasourceList);
        Map<String, Integer> pages = collector.getCurrentPages();
        for (Map.Entry<String, Integer> entry : pages.entrySet()) {
            RedisUtil.set(OssMsgCollector.COLLECTED_PAGE_KEY_PREFIX + entry.getKey(), entry.getValue());
        }
        log.info("{}个桶的加载状态数据已存入Redis", pages.size());
    }

    @Override
    protected CollectedDatasourceRouteStrategyContext strategyContext() {
        RouteEventManagerInstance
                .getInstance()
                .addListenerIfAbsent(OssEntityRouteEvent.class, new OssEntityRouteEventListener());
        return new CollectedDatasourceRouteStrategyContext(new ObserverRouteStrategy(OssEntityRouteEvent::new));
    }
}
