package com.sccl.modules.mssaccount.msssupplier.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;
import java.math.BigDecimal;


/**
 * 供应商表 MSS_SUPPLIER
 * 
 * <AUTHOR>
 * @date 2019-05-01
 */
public class MssSupplier extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
	private Long msId;
    public Long getMsId() {
		return msId;
	}

	public void setMsId(Long msId) {
		this.msId = msId;
	}


	/** ‘C’ = 创建，‘M‘ = 修改 */
    private String status;
    /** 申请单ID */
    private BigDecimal zsqdId;
    /** 供应商或债权人的帐号 */
    private String lifnr;
    /** 供应商帐户组 */
    private String ktokk;
    /** 公司全称 */
    private String name1;
    /** 公司英文名称 */
    private String name2;
    /** 公司简称 */
    private String sort1;
    /** 街道 */
    private String street;
    /** 邮政编码 */
    private String pstlz;
    /** 城市 */
    private String city1;
    /** 国家代码 */
    private String land1;
    /** 根据 ISO 639 的语言 固定为“ZH” */
    private String langu;
    /** 供应商主要联系人电话 */
    private String telf1;
    /** 企业固定电话 */
    private String telf2;
    /** 传真号 */
    private String telfx;
    /** 如无值，默认为’<EMAIL>’   E-mail */
    private String smtpAddr;
    /** 主营业务所属行业  枚举项参见附件Excel中的“主营业务所属行业”列表（根据行业对照表转换成行业代码） */
    private String brsch;
    /** 贸易伙伴的公司标识 */
    private String vbund;
    /** 注册编号 */
    private String zregno;
    /** 公司性质  参见Excel附件：“供应商主数据枚举项信息参考” */
    private String zprop;
    /** 供应商法定身份识别类型   单选，可选项包括：组织机构代码、身份证、军官证、DUNS码、特殊单位编码。与前面性质绑定 */
    private String zlglid;
    /** 组织机构代码   根据公司性质和识别类型的不同代表不同类型的号码 */
    private String zorgco;
    /** 是否为独立法人企业 */
    private String zcop;
    /** 法人代表 */
    private String zleglRep;
    /** 营业执照注册号 */
    private String zregco;
    /** 工商注册省份 */
    private String zregio;
    /** 供应商主要联系人 */
    private String zcont;
    /** 注册资本(万元) */
    private String zregcap;
    /** 注册资本币种 */
    private String zregcy;
    /** 纳税人类型  选项为“一般纳税人”“小规模纳税人”“营业税纳税人” */
    private String ztxptp;
    /** 纳税人识别号 */
    private String ztxpid;
    /** 关联类型 自定义表ZTMM_GLLX,值为
01-  合并范围外关连人士
02-合并范围内关连人士
03-内部单位
99-非关联方
采辅传编码的值，SAP根据自定义表里的数据转换为描述
 */
    private String zsfgl;
    /** 是否由税务局代开增值税专用发票 */
    private String zvatProxy;
    /** 增值税专用发票的金额限制 */
    private String zvatAmountLimit;
    /** 增值税专用发票的数量限制 */
    private String zvatQtyLimit;
    /** 是否需要代扣代缴增值税 */
    private String zvatPayProxy;
    /** 经营范围 */
    private String zbusiness;
    /** 纳税人类型编码 */
    private String ztaxerNum;
    /** 与电信合作的业务类型范围  可选项为：
“业务合作类（如CP/SP、业务渠道代理商等）”
“物资类”
“服务类-工程建设类服务”
“服务类-营销服务”
“服务类-劳务派遣”
“服务类-人力外包”
“服务类-审计类服务”
“服务类-其它”
“快速报账供应商”
“基站租用供应商”
 */
    private String zdxhzywlxfw;
    /** 人力外包商类型  人力资源系统使用，选项包括：1:电信实业上市2:电信实业存续3:社会法人4:个体工商户 */
    private String zrlwbslx;
    /**  */
    private Date infDate;
    /**  */
    private String infStatus;
    /** 接口 */
    private BigDecimal infId;

	public void setStatus(String status)
	{
		this.status = status;
	}

	public String getStatus() 
	{
		return status;
	}

	public void setZsqdId(BigDecimal zsqdId)
	{
		this.zsqdId = zsqdId;
	}

	public BigDecimal getZsqdId() 
	{
		return zsqdId;
	}

	public void setLifnr(String lifnr)
	{
		this.lifnr = lifnr;
	}

	public String getLifnr() 
	{
		return lifnr;
	}

	public void setKtokk(String ktokk)
	{
		this.ktokk = ktokk;
	}

	public String getKtokk() 
	{
		return ktokk;
	}

	public void setName1(String name1)
	{
		this.name1 = name1;
	}

	public String getName1() 
	{
		return name1;
	}

	public void setName2(String name2)
	{
		this.name2 = name2;
	}

	public String getName2() 
	{
		return name2;
	}

	public void setSort1(String sort1)
	{
		this.sort1 = sort1;
	}

	public String getSort1() 
	{
		return sort1;
	}

	public void setStreet(String street)
	{
		this.street = street;
	}

	public String getStreet() 
	{
		return street;
	}

	public void setPstlz(String pstlz)
	{
		this.pstlz = pstlz;
	}

	public String getPstlz() 
	{
		return pstlz;
	}

	public void setCity1(String city1)
	{
		this.city1 = city1;
	}

	public String getCity1() 
	{
		return city1;
	}

	public void setLand1(String land1)
	{
		this.land1 = land1;
	}

	public String getLand1() 
	{
		return land1;
	}

	public void setLangu(String langu)
	{
		this.langu = langu;
	}

	public String getLangu() 
	{
		return langu;
	}

	public void setTelf1(String telf1)
	{
		this.telf1 = telf1;
	}

	public String getTelf1() 
	{
		return telf1;
	}

	public void setTelf2(String telf2)
	{
		this.telf2 = telf2;
	}

	public String getTelf2() 
	{
		return telf2;
	}

	public void setTelfx(String telfx)
	{
		this.telfx = telfx;
	}

	public String getTelfx() 
	{
		return telfx;
	}

	public void setSmtpAddr(String smtpAddr)
	{
		this.smtpAddr = smtpAddr;
	}

	public String getSmtpAddr() 
	{
		return smtpAddr;
	}

	public void setBrsch(String brsch)
	{
		this.brsch = brsch;
	}

	public String getBrsch() 
	{
		return brsch;
	}

	public void setVbund(String vbund)
	{
		this.vbund = vbund;
	}

	public String getVbund() 
	{
		return vbund;
	}

	public void setZregno(String zregno)
	{
		this.zregno = zregno;
	}

	public String getZregno() 
	{
		return zregno;
	}

	public void setZprop(String zprop)
	{
		this.zprop = zprop;
	}

	public String getZprop() 
	{
		return zprop;
	}

	public void setZlglid(String zlglid)
	{
		this.zlglid = zlglid;
	}

	public String getZlglid() 
	{
		return zlglid;
	}

	public void setZorgco(String zorgco)
	{
		this.zorgco = zorgco;
	}

	public String getZorgco() 
	{
		return zorgco;
	}

	public void setZcop(String zcop)
	{
		this.zcop = zcop;
	}

	public String getZcop() 
	{
		return zcop;
	}

	public void setZleglRep(String zleglRep)
	{
		this.zleglRep = zleglRep;
	}

	public String getZleglRep() 
	{
		return zleglRep;
	}

	public void setZregco(String zregco)
	{
		this.zregco = zregco;
	}

	public String getZregco() 
	{
		return zregco;
	}

	public void setZregio(String zregio)
	{
		this.zregio = zregio;
	}

	public String getZregio() 
	{
		return zregio;
	}

	public void setZcont(String zcont)
	{
		this.zcont = zcont;
	}

	public String getZcont() 
	{
		return zcont;
	}

	public void setZregcap(String zregcap)
	{
		this.zregcap = zregcap;
	}

	public String getZregcap() 
	{
		return zregcap;
	}

	public void setZregcy(String zregcy)
	{
		this.zregcy = zregcy;
	}

	public String getZregcy() 
	{
		return zregcy;
	}

	public void setZtxptp(String ztxptp)
	{
		this.ztxptp = ztxptp;
	}

	public String getZtxptp() 
	{
		return ztxptp;
	}

	public void setZtxpid(String ztxpid)
	{
		this.ztxpid = ztxpid;
	}

	public String getZtxpid() 
	{
		return ztxpid;
	}

	public void setZsfgl(String zsfgl)
	{
		this.zsfgl = zsfgl;
	}

	public String getZsfgl() 
	{
		return zsfgl;
	}

	public void setZvatProxy(String zvatProxy)
	{
		this.zvatProxy = zvatProxy;
	}

	public String getZvatProxy() 
	{
		return zvatProxy;
	}

	public void setZvatAmountLimit(String zvatAmountLimit)
	{
		this.zvatAmountLimit = zvatAmountLimit;
	}

	public String getZvatAmountLimit() 
	{
		return zvatAmountLimit;
	}

	public void setZvatQtyLimit(String zvatQtyLimit)
	{
		this.zvatQtyLimit = zvatQtyLimit;
	}

	public String getZvatQtyLimit() 
	{
		return zvatQtyLimit;
	}

	public void setZvatPayProxy(String zvatPayProxy)
	{
		this.zvatPayProxy = zvatPayProxy;
	}

	public String getZvatPayProxy() 
	{
		return zvatPayProxy;
	}

	public void setZbusiness(String zbusiness)
	{
		this.zbusiness = zbusiness;
	}

	public String getZbusiness() 
	{
		return zbusiness;
	}

	public void setZtaxerNum(String ztaxerNum)
	{
		this.ztaxerNum = ztaxerNum;
	}

	public String getZtaxerNum() 
	{
		return ztaxerNum;
	}

	public void setZdxhzywlxfw(String zdxhzywlxfw)
	{
		this.zdxhzywlxfw = zdxhzywlxfw;
	}

	public String getZdxhzywlxfw() 
	{
		return zdxhzywlxfw;
	}

	public void setZrlwbslx(String zrlwbslx)
	{
		this.zrlwbslx = zrlwbslx;
	}

	public String getZrlwbslx() 
	{
		return zrlwbslx;
	}

	public void setInfDate(Date infDate)
	{
		this.infDate = infDate;
	}

	public Date getInfDate() 
	{
		return infDate;
	}

	public void setInfStatus(String infStatus)
	{
		this.infStatus = infStatus;
	}

	public String getInfStatus() 
	{
		return infStatus;
	}

	public void setInfId(BigDecimal infId)
	{
		this.infId = infId;
	}

	public BigDecimal getInfId() 
	{
		return infId;
	}


	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("status", getStatus())
            .append("zsqdId", getZsqdId())
            .append("lifnr", getLifnr())
            .append("ktokk", getKtokk())
            .append("name1", getName1())
            .append("name2", getName2())
            .append("sort1", getSort1())
            .append("street", getStreet())
            .append("pstlz", getPstlz())
            .append("city1", getCity1())
            .append("land1", getLand1())
            .append("langu", getLangu())
            .append("telf1", getTelf1())
            .append("telf2", getTelf2())
            .append("telfx", getTelfx())
            .append("smtpAddr", getSmtpAddr())
            .append("brsch", getBrsch())
            .append("vbund", getVbund())
            .append("zregno", getZregno())
            .append("zprop", getZprop())
            .append("zlglid", getZlglid())
            .append("zorgco", getZorgco())
            .append("zcop", getZcop())
            .append("zleglRep", getZleglRep())
            .append("zregco", getZregco())
            .append("zregio", getZregio())
            .append("zcont", getZcont())
            .append("zregcap", getZregcap())
            .append("zregcy", getZregcy())
            .append("ztxptp", getZtxptp())
            .append("ztxpid", getZtxpid())
            .append("zsfgl", getZsfgl())
            .append("zvatProxy", getZvatProxy())
            .append("zvatAmountLimit", getZvatAmountLimit())
            .append("zvatQtyLimit", getZvatQtyLimit())
            .append("zvatPayProxy", getZvatPayProxy())
            .append("zbusiness", getZbusiness())
            .append("ztaxerNum", getZtaxerNum())
            .append("zdxhzywlxfw", getZdxhzywlxfw())
            .append("zrlwbslx", getZrlwbslx())
            .append("infDate", getInfDate())
            .append("infStatus", getInfStatus())
            .append("infId", getInfId())
            .append("msId", getMsId())
            .toString();
    }
}
