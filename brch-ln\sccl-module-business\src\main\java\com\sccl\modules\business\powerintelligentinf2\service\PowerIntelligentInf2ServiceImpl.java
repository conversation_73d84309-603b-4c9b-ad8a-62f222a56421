package com.sccl.modules.business.powerintelligentinf2.service;

import com.sccl.common.support.Convert;
import com.sccl.common.utils.DateUtils;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.service.IdGenerator;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.modules.business.account.mapper.AccountMapper;
import com.sccl.modules.business.powerintelligentinf2.domain.PowerIntelligentRelate;
import com.sccl.modules.business.powerintelligentinf2.dto.Powerintelligentinf2Dto;
import com.sccl.modules.business.powerintelligentinf2.mapper.PowerIntelligentInf2Mapper;
import com.sccl.modules.business.powerintelligentinf2.mapper.PowerIntelligentRelateMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sccl.modules.business.powerintelligentinf2.domain.PowerIntelligentInf2;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * PUE管控主 服务层实现
 * 
 * <AUTHOR>
 * @date 2019-07-18
 */
@Service
public class PowerIntelligentInf2ServiceImpl extends BaseServiceImpl<PowerIntelligentInf2> implements IPowerIntelligentInf2Service
{
    @Autowired
    private PowerIntelligentInf2Mapper powerIntelligentInf2Mapper;
    @Autowired
    private PowerIntelligentRelateMapper powerIntelligentRelateMapper;
    @Autowired
    private AccountMapper accountMapper;

    @Override
    public List<Map<String,Object>> selectByList(PowerIntelligentInf2 powerIntelligentInf2,String ammeterCode,List<IdNameVO> companies){
        Map<String,Object> params = new HashMap<>();
        params.put("stationName",powerIntelligentInf2.getStationname());
        params.put("ammeterCode",ammeterCode);
        params.put("companies", companies);
        return powerIntelligentInf2Mapper.selectByList(params);
    }
    @Override
    public int deletePUECountrols(String ids){
        return powerIntelligentRelateMapper.deleteDetailsByMain(Convert.toStrArray(ids));//删除详情
//        return powerIntelligentInf2Mapper.deleteByIdsDB(ConvertFormat.toStrArray(ids));//删除主表
    }
    @Override
    public int updateData(Powerintelligentinf2Dto powerintelligentinf2){
        //保存关联的用电类型
        if(null != powerintelligentinf2.getDetails()&& powerintelligentinf2.getDetails().size() != 0){
            List<PowerIntelligentRelate> details = powerintelligentinf2.getDetails()
                    .stream()
                    .map(e -> new PowerIntelligentRelate(IdGenerator.getNextId(), powerintelligentinf2.getPinId(),e.getAmmeterid(),e.getPercent(),e.getLastmonthelectricity(),e.getMonthelectricity()))
                    .collect(Collectors.toList());
            if(details.size() != 0){
                powerIntelligentRelateMapper.deleteDetailsByMain(Convert.toStrArray(powerintelligentinf2.getPinId().toString()));
                powerIntelligentRelateMapper.insertList(details);
            }
        }else{
            powerIntelligentRelateMapper.deleteDetailsByMain(Convert.toStrArray(powerintelligentinf2.getPinId().toString()));
        }
        return this.update(powerintelligentinf2);//删除主表
    }

    @Override
    public Map<String,Object> selectAcountByDateAmmeter(Long ammeterid,Integer type){
        Map<String,Object> params = new HashMap<>();
        params.put("ammeterid",ammeterid);
        //获取当前时间的期号
        SimpleDateFormat formatter  = new SimpleDateFormat("yyyyMM");
        if(null == type || type == 1){
            params.put("accountno",formatter.format(new Date()));
        }else{
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            calendar.add(Calendar.MONTH, -1);
            params.put("accountno",formatter.format(calendar.getTime()));
        }
        return accountMapper.selectAcountByDateAmmeter(params);
    }

    /**
     * @Description: 查询送往采集适配系统数据
     * @author: liucong
     * @return:
     */
    @Override
    public List<Map<String,Object>> selectTotalPower(){
        List<String> accountnos = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();//日历对象
        calendar.setTime(new Date());//设置当前日期
        calendar.add(Calendar.MONTH, -1);//月份减一
        accountnos.add(DateUtils.formatDate(calendar.getTime(),"yyyyMM"));
        calendar.add(Calendar.MONTH, -1);//月份减二
        accountnos.add(DateUtils.formatDate(calendar.getTime(),"yyyyMM"));
        calendar.add(Calendar.MONTH, -1);//月份减三
        accountnos.add(DateUtils.formatDate(calendar.getTime(),"yyyyMM"));
        Map<String,Object> params = new HashMap<>();
        params.put("accountnos",accountnos);
        return powerIntelligentInf2Mapper.selectTotalPower(params);
    }
}
