package com.sccl.modules.business.poweraudit.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.sccl.modules.business.poweraudit.entity.PowerAuditDTO;
import com.sccl.modules.business.poweraudit.entity.PowerAuditEntity;
import com.sccl.modules.business.poweraudit.entity.PowerAuditVO;
import com.sccl.modules.business.statinAudit.domain.AuditResults;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-22
 */
public interface PowerAuditService extends IService<PowerAuditEntity> {

    /**
     * @description: 通过地市获取稽核列表
     * @author: 芮永恒
     * @date: 2024-2-23 9:47
     * @param: * @param powerAuditVO
     * @return: * @return java.util.List<com.sccl.modules.business.poweraudit.entity.PowerAuditDTO>
     **/
    List<PowerAuditDTO> getPowerAuditByCity(PowerAuditVO powerAuditVO);

    /**
     * @description: 通过区县获取稽核结果
     * @author: 芮永恒
     * @date:  2024-2-23 9:48
     * @param: * @param powerAuditVO
     * @return: * @return java.util.List<com.sccl.modules.business.poweraudit.entity.PowerAuditDTO>
     **/
    List<PowerAuditDTO> getPowerAuditCompanies(PowerAuditVO powerAuditVO);

    /**
     * @description: 到处台账稽核
     * @author: 芮永恒
     * @date: 2024-2-23 10:54
     * @param: * @param powerAuditVO
     * @return: * @return java.lang.String
     **/
    void exportPowerAudit(HttpServletResponse response, PowerAuditVO powerAuditVO, String fileName);

    /**
     * @description: 获取合计
     * @author: 芮永恒
     * @date: 2024-2-23 16:18
     * @param: * @param powerAuditVO
     * @return: * @return com.sccl.modules.business.poweraudit.entity.PowerAuditDTO
     **/
    PowerAuditDTO auditTotal(PowerAuditVO powerAuditVO);

    /**
     * @description: 嵌入报账稽核结果查看功能
     * @author: 芮永恒
     * @date: 2024-2-26 14:55
     * @param: * @param ammeterids
     * @return: * @return java.util.List<com.sccl.modules.business.poweraudit.entity.PowerAuditEntity>
     **/
    List<PowerAuditEntity> getAuditResult(List<String> ammeterids);

    /**
     * @description:  通过报账单ID查询最新的报账单稽核结果
     * @author: 芮永恒
     * @date: 2024-2-27 9:30
     * @param: * @param mssAccountId
     * @return: * @return com.sccl.modules.business.poweraudit.entity.PowerAuditEntity
     **/
    PowerAuditEntity getAuditByMssAccountId(Long mssAccountId);

    PowerAuditEntity getAuditByPcid(String pcid, String type);

    <T> PageInfo<T> getAuditDetails(PowerAuditVO powerAuditVO);

    List<PowerAuditDTO> getAuditOperationsBranch(PowerAuditVO powerAuditVO);

    /**
     * @description: 台账稽核详情（数值超链接） 导出
     * @author: 芮永恒
     * @date: 2024-3-4 9:09
     * @param: * @param powerAuditVO
     * @return: * @return void
     **/
    void exportAuditDetails(HttpServletResponse response, PowerAuditVO powerAuditVO);

    List<AuditResults> getAuditResultList(String type, List<String> keyList);

    int auditAccount(List<Long> pcidList);
    int auditAccountEs(List<Long> pcidList);

    int auditAllAccount();
}
