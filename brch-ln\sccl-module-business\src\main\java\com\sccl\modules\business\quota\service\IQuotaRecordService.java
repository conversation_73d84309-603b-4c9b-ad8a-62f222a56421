package com.sccl.modules.business.quota.service;

import com.sccl.framework.service.IBaseService;
import com.sccl.modules.business.quota.domain.Quota;
import com.sccl.modules.business.quota.domain.QuotaBaseResult;
import com.sccl.modules.business.quota.domain.QuotaCondition;
import com.sccl.modules.business.quota.domain.QuotaRecord;

import java.util.List;
import java.util.Map;

/**
 * 定额管理 服务层
 * 
 * <AUTHOR>
 * @date 2019-05-13
 */
public interface IQuotaRecordService extends IBaseService<QuotaRecord>
{
    public List<QuotaRecord> getByQuotaId(QuotaRecord quotaRecord);
    public List<QuotaRecord> getByQuotaDateId(QuotaRecord quotaRecord);

    public List<QuotaRecord> selectObjectByAmmPro(QuotaRecord quotaRecord);

    String getByCreateTime(Map<String,Object> params);

}
