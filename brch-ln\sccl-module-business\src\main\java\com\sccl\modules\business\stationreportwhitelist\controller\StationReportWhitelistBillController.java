package com.sccl.modules.business.stationreportwhitelist.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sccl.common.io.FileUtils;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.utils.ExcelUtil;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.stationreportwhitelist.domain.MpAttachments;
import com.sccl.modules.business.stationreportwhitelist.dto.StationReportWhitelistBillDTO;
import com.sccl.modules.business.stationreportwhitelist.dto.StationReportWhitelistBillQuery;
import com.sccl.modules.business.stationreportwhitelist.service.StationReportWhitelistBillService;
import com.sccl.modules.business.stationreportwhitelist.vo.OneStopIsMoreThanOneWatchExport;
import com.sccl.modules.business.stationreportwhitelist.vo.StationReportWhitelistBillVO;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/11 15:14
 * @describe 白名单单据
 */
@RestController
@AllArgsConstructor
@RequestMapping("/stationReportWhitelistBill")
public class StationReportWhitelistBillController extends BaseController {

    private final StationReportWhitelistBillService stationReportWhitelistBillService;


    /**
     * 新增白名单
     */
    @PostMapping(value = "/add")
    public AjaxResult add(@RequestBody @Valid StationReportWhitelistBillDTO dto) {
        String billId = stationReportWhitelistBillService.add(dto);
        return AjaxResult.success("新增成功", billId);
    }

    /**
     * 修改白名单单据
     */
    @PostMapping(value = "/edit")
    public AjaxResult edit(@RequestBody @Valid StationReportWhitelistBillDTO dto) {
        String billId = stationReportWhitelistBillService.edit(dto);
        return AjaxResult.success("修改成功", billId);
    }

    /**
     * 详情
     */
    @PostMapping(value = "/findById/{id}")
    public AjaxResult findById(@PathVariable Long id) {
        StationReportWhitelistBillVO vo = stationReportWhitelistBillService.findById(id);
        return AjaxResult.success("查询成功", vo);
    }

    /**
     * 单据列表
     */
    @GetMapping(value = "/list")
    public TableDataInfo list(Page<StationReportWhitelistBillVO> page, StationReportWhitelistBillQuery query) {
        IPage<StationReportWhitelistBillVO> voiPage = stationReportWhitelistBillService.selectList(page, query);
        return getDataTable(voiPage.getRecords(), voiPage.getTotal());
    }

    /**
     * 导出 一站多表
     */
    @GetMapping(value = "/export")
    public void export(Page<StationReportWhitelistBillVO> page, StationReportWhitelistBillQuery query, HttpServletResponse response) {
        List<OneStopIsMoreThanOneWatchExport> exportList = stationReportWhitelistBillService.export(page, query);
        ExcelUtil<OneStopIsMoreThanOneWatchExport> excelUtil = new ExcelUtil<>(OneStopIsMoreThanOneWatchExport.class);
        excelUtil.exportExcelToBrowser(response, exportList, "一站多表");
    }

    /**
     * 移除白名单
     */
    @PostMapping(value = "/notAvailable/{id}")
    public AjaxResult notAvailable(@PathVariable Long id) {
        stationReportWhitelistBillService.notAvailable(id);
        return AjaxResult.success("移除成功");
    }

    /**
     * 删除白名单
     */
    @PostMapping(value = "/del/{id}")
    public AjaxResult del(@PathVariable Long id) {
        stationReportWhitelistBillService.del(id);
        return AjaxResult.success("删除成功");
    }

    /**
     * 获取附件busiId
     */
    @GetMapping(value = "/getFileBusiId")
    public AjaxResult getFileBusiId() {
        return AjaxResult.success("fileBusiId", String.valueOf(IdWorker.getId()));
    }

    /**
     * 根据fileBusiId查询附件列表
     */
    @GetMapping(value = "/getFileListByFileBusiId/{fileBusiId}")
    public AjaxResult getFileListByFileBusiId(@PathVariable String fileBusiId) {
        List<MpAttachments> mpAttachments = new MpAttachments().selectList(Wrappers.<MpAttachments>lambdaQuery()
                .eq(MpAttachments::getBusiId, fileBusiId)
        );
        for (MpAttachments mpAttachment : mpAttachments) {
            mpAttachment.setFileSize(FileUtils.formatFileSize(Long.parseLong(mpAttachment.getFileSize())));
        }
        return AjaxResult.success("附件列表", mpAttachments);
    }
}