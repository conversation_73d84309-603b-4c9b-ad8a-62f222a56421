package com.sccl.modules.business.meterinfoalljt_new.controller;

import cn.hutool.core.util.ObjUtil;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.meterinfoalljt_new.dto.MeterinfoAllJtQueryDTO;
import com.sccl.modules.business.meterinfoalljt_new.service.IMeterinfoAllJtService;
import com.sccl.modules.business.meterinfoalljt_new.vo.MeterinfoAllJtVO;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * 表计清单查询 信息操作处理
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@RestController("meterinfoAllJtListController")
@RequestMapping("/business/meterinfoAllJt")
public class MeterinfoAllJtListController extends BaseController {

    @Autowired
    private IMeterinfoAllJtService meterinfoAllJtService;

    /**
     * 查询表计清单列表
     */
    @Log(title = "表计清单查询", action = BusinessType.OTHER)
    @PostMapping("/list")
    @ResponseBody
    @RequiresPermissions("basedata:meterinfo:list")
    public TableDataInfo list(@RequestBody MeterinfoAllJtQueryDTO queryDTO) {
        // 参数校验
        if (ObjUtil.isEmpty(queryDTO)) {
            queryDTO = new MeterinfoAllJtQueryDTO();
        }

        // 分页处理
        startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<MeterinfoAllJtVO> list = meterinfoAllJtService.selectMeterinfoAllJtList(queryDTO);
        return getDataTable(list);
    }

    /**
     * 根据ID查询表计清单详情
     */
    @Log(title = "表计清单查询", action = BusinessType.OTHER)
    @GetMapping("/detail/{id}")
    @ResponseBody
    @RequiresPermissions("basedata:meterinfo:detail")
    public AjaxResult getDetail(@PathVariable("id") Long id) {
        if (ObjUtil.isEmpty(id)) {
            return error("参数不能为空");
        }

        MeterinfoAllJtVO vo = meterinfoAllJtService.selectMeterinfoAllJtById(id);
        if (ObjUtil.isEmpty(vo)) {
            return error("数据不存在");
        }

        return success(vo);
    }

    /**
     * 导入表计清单Excel
     */
    @Log(title = "表计清单导入", action = BusinessType.IMPORT)
    @PostMapping("/uploadExcel")
    @ResponseBody
    @RequiresPermissions("basedata:meterinfo:import")
    public Map<String, Object> uploadExcel(HttpServletRequest request) throws Exception {
        Map<String, Object> map = new HashMap<>(8);

        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        Iterator<String> iterator = multiRequest.getFileNames();

        while (iterator.hasNext()) {
            String name = iterator.next();
            List<MultipartFile> files = new LinkedList<>();
            files = multiRequest.getFiles(name);

            // 一次遍历所有文件
            for (MultipartFile file : files) {
                if (file == null) {
                    continue;
                }

                // 如果文件大小为0则不上传
                long fileSize = file.getSize();
                if (fileSize <= 0L) {
                    throw new Exception("文件名称：【" + file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("\\") + 1, file.getOriginalFilename().length()) + "】的文件大小为0,请核对!");
                }

                // 调用服务层处理Excel导入
                map = meterinfoAllJtService.importExcel("sheet1", file.getInputStream());
            }
        }

        return map;
    }
}
