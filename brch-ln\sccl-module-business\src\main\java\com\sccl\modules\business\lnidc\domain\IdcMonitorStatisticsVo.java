package com.sccl.modules.business.lnidc.domain;


import com.baomidou.mybatisplus.annotation.TableName;
import com.sccl.framework.aspectj.lang.annotation.Excel;
import com.sccl.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
public class IdcMonitorStatisticsVo extends BaseEntity  {

    private static final long serialVersionUID = 1L;


    private Long id;

    /**
     * 地市id
     */
    private String company;

    /**
     * 地市名称
     */
    @Excel(name = "地市")
    private String companyName;

    /**
     * 本省idc名称
     */
    @Excel(name = "本省IDC名称")
    private String idcName;

    /**
     * 设备名称
     */
    @Excel(name = "设备名称")
    private String deviceName;

    /**
     * 设备ip地址
     */
    @Excel(name = "设备IP地址")
    private String ipAddr;

    /**
     * 设备厂家
     */
    @Excel(name = "设备厂家")
    private String deviceFactory;

    /**
     * 设备型号
     */
    @Excel(name = "设备型号")
    private String deviceType;

    /**
     * 所属系统或平台
     */
    @Excel(name = "所属系统或平台")
    private String ownPlatform;

    /**
     * 维护部门
     */
    @Excel(name = "维护部门")
    private String preserveDept;

    /**
     * 维护人
     */
    @Excel(name = "维护人")
    private String preservePerson;

    /**
     * 设备入网时间
     */
    @Excel(name = "设备入网时间")
    private String deviceAccessTime;

    /**
     * 额定功率（w）
     */
    @Excel(name = "额定功率（W）")
    private String ratedPower;

    /**
     * 查询实际功率
     */
    @Excel(name = "查询实际功率")
    private String realPower;

    /**
     * 统计id
     */
    private Long statisticsId;

    /**
     * 年份
     */
    private String year;

    /**
     * 一月
     */
    @Excel(name = "1月实际功率（W）")
    private String january;

    /**
     * 二月
     */
    @Excel(name = "2月实际功率（W）")
    private String february;

    /**
     * 三月
     */
    @Excel(name = "3月实际功率（W）")
    private String march;

    /**
     * 四月
     */
    @Excel(name = "4月实际功率（W）")
    private String april;

    /**
     * 五月
     */
    @Excel(name = "5月实际功率（W）")
    private String may;

    /**
     * 六月
     */
    @Excel(name = "6月实际功率（W）")
    private String june;

    /**
     * 七月
     */
    @Excel(name = "7月实际功率（W）")
    private String july;

    /**
     * 八月
     */
    @Excel(name = "8月实际功率（W）")
    private String august;

    /**
     * 九月
     */
    @Excel(name = "9月实际功率（W）")
    private String september;

    /**
     * 十月
     */
    @Excel(name = "10月实际功率（W）")
    private String october;

    /**
     * 十一月
     */
    @Excel(name = "11月实际功率（W）")
    private String november;

    /**
     * 十二月
     */
    @Excel(name = "12月实际功率（W）")
    private String december;


}
