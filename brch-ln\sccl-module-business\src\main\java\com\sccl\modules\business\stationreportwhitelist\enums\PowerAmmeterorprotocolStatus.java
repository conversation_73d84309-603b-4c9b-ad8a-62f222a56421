package com.sccl.modules.business.stationreportwhitelist.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/4/22 16:32
 * @describe 电表状态
 */
@Getter
@AllArgsConstructor
public enum PowerAmmeterorprotocolStatus {
    // 0:停用 1:在用
    STOP(0, "停用"),
    IN_USE(1, "在用")
    ;

    /**
     * 状态码
     */
    private final Integer code;

     /**
     * 状态名称
     */
    private final String name;

    /**
     * 根据code获取name
     */
    public static String getNameByCode(Integer code) {
        for (PowerAmmeterorprotocolStatus billStatus : PowerAmmeterorprotocolStatus.values()) {
            if (billStatus.getCode().equals(code)) {
                return billStatus.getName();
            }
        }
        return null;
    }
}
