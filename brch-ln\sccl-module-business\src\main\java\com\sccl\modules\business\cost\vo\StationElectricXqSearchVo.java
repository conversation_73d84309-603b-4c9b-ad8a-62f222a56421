package com.sccl.modules.business.cost.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 局站业务电量查询详情 一览 查询条件对象
 */
@Data
public class StationElectricXqSearchVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 当前记录起始索引
     */
    private Integer pageNum;
    /**
     * 每页显示记录数
     */
    private Integer pageSize;

    /**
     * 数据来源 1 无线大数据 2 智慧机房 暂时不用了
     */
    private String source;

    /**
     * 市局编码
     */
    private String cityCode;

    /**
     * 区县编码
     */
    private String countyCode;

    /**
     * 局站编码
     */
    private String stationCode;

    /**
     * 统计月份【yyyyMM】
     */
    private String tjyf;

    /**
     * 统计月份【yyyyMM%】
     */
    private String tjsjLike;

    /**
     * 统计日期开始【yyyyMMdd】
     * @ignore
     */
    private String tjrqks;

    /**
     * 统计日期结束【yyyyMMdd】
     * * @ignore
     */
    private String tjrqjs;
}
