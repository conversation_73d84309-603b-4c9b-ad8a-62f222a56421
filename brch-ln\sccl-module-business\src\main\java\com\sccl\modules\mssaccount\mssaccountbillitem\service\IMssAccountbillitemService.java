package com.sccl.modules.mssaccount.mssaccountbillitem.service;

import com.sccl.framework.service.IBaseService;
import com.sccl.modules.mssaccount.mssaccountbillitem.domain.MssAccountbillitem;

/**
 * 报账明细 服务层
 *
 * <AUTHOR>
 * @date 2019-04-24
 */
public interface IMssAccountbillitemService extends IBaseService<MssAccountbillitem> {


    // 删除 明细 并删除 与台账的关联关系
    int deleteDBAndRAccount(Long id);
}
