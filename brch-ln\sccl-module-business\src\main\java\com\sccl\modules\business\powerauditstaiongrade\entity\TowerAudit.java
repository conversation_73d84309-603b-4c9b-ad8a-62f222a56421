package com.sccl.modules.business.powerauditstaiongrade.entity;

import lombok.Data;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static java.util.stream.Collectors.toMap;

@Data
public class TowerAudit {
    private String company;
    private String companyName;
    private String country;
    private String countryName;
    /**
     * 时间
     */
    private String time;
    /**
     * 稽核通过数
     */
    private String auditNum;
    /**
     * 一次性稽核通过数
     */
    private String auditNumOne;
    /**
     * 一次性稽核通过key
     */
    private String auditNumOneKey;
    /**
     * 稽核总数
     */
    private String totalNum;
    /**
     * 稽核通过比例
     */
    private String auditScale;
    /**
     * 一次性稽核通过比例
     */
    private String auditOneScale;

    public static List<TowerAudit> mergeAuditList(List<TowerAudit> auditList, List<TowerAudit> auditOneList) {
        Map<String, TowerAudit> map = auditList.stream().collect(
                toMap(
                        audit -> audit.getCompany() + "-" + audit.getCountry() + "-" + audit.getTime(),
                        audit -> audit,
                        (item1, item2) -> item1
                )
        );
        auditOneList.forEach(
                auditOne -> {
                    String key = auditOne.getCompany() + "-" + auditOne.getCountry() + "-" + auditOne.getTime();
                    if (map.containsKey(key)) {
                        TowerAudit towerAudit = map.get(key);
                        towerAudit.setAuditNumOne(auditOne.getAuditNumOne());
                    } else {
                        map.put(key, auditOne);
                    }
                }
        );
        return new ArrayList<>(map.values());
    }

    public static String calculatePercentage(Integer i1, Integer i2) {
        if (i2 == 0) {
            return "存在0";
        }
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
        decimalFormat.setRoundingMode(java.math.RoundingMode.DOWN);
        double percentage = i1 * 1.0 / i2 * 100;
        return decimalFormat.format(percentage) + "%";
    }

    public static void addAuditNumOneKey(List<TowerAudit> auditList) {
        auditList.forEach(
                item -> {
                    item.setAuditNumOneKey(
                            String.format("msgaudit-%s-%s-%s", item.getCompany(), item.getCountry(), item.getTime())
                    );
                }
        );
    }
}
