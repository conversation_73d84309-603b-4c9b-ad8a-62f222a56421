package com.sccl.modules.business.pylonBG.mapper;


import com.sccl.modules.business.account.domain.Account;
import com.sccl.modules.business.account.domain.AccountBaseResult;
import com.sccl.modules.business.account.domain.AccountCondition;

import java.util.List;
import java.util.Map;

/**
 * __铁塔包干__<br/>
 * 2019/7/18
 *
 * <AUTHOR>
 */
public interface PylonBGMapper{
   /**
     * @Description: 铁塔包干录入界面列表
     * @author: dongk
     * @date: 2019/7/18
     * @param:
     * @return:
     */
    List<AccountBaseResult> selectByParams(AccountCondition condition);

 /**
  * @Description: 查询台账是否有下期正式台账
  * @author: dongk
  * @date: 2019/8/7
  * @param:
  * @return:
  */
 Integer selectifNext(AccountCondition condition);

    /**
     * @Description: 初始化铁塔包干台账数据
     * @author: dongk
     * @date: 2019/7/22
     * @param:
     * @return:
     */
    void initBGAccount(Account account);

    /**
     * @Description: 更新
     * @author: dongk
     * @date: 2019/7/22
     * @param:
     * @return:
     */
    int update(Account account);

    /**
     * @Description: 删除
     * @author: dongk
     * @date: 2019/7/22
     * @param:
     * @return:
     */
    int deleteByIds(String[] pcids);

    /**
     * @Description: 铁塔包干台账合计
     * @author: dongk
     * @date: 2019/6/6
     * @param:
     * @return:
     */
    AccountBaseResult accountTotal(AccountCondition condition);

    /**
     * @Description: 查询归集单下面所有台账
     * @author: dongk
     * @date: 2019/7/23
     * @param:
     * @return:
     */
    List<AccountBaseResult> selectListByPre(Map<String,Object> map);

    /**
     * @Description: 包干查询功能列表
     * @author: dongk
     * @date: 2019/7/24
     * @param:
     * @return:
     */
    List<AccountBaseResult> selectQuery(AccountCondition condition);

    /**
     * @Description: 铁塔包干台账合计
     * @author: dongk
     * @date: 2019/7/25
     * @param:
     * @return:
     */
    AccountBaseResult queryTotal(AccountCondition condition);

}
