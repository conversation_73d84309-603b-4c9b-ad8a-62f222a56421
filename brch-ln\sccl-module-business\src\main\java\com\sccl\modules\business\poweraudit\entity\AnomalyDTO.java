package com.sccl.modules.business.poweraudit.entity;

import com.sccl.framework.aspectj.lang.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Author: 芮永恒
 * @CreateTime: 2024-03-01  16:47
 * @Description: 台账周期
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AnomalyDTO {

    /**
     * 地市
     */
    @Excel(name = "所属部门")
    private String city;

    /**
     * 区县
     */
//    @Excel(name = "区县公司")
    private String countyCompanies;

    /**
     * 运营分局
     */
    @Excel(name = "运营分局")
    private String operationsBranch;

    /**
     * 台账期号
     */
    @Excel(name = "台账期号")
    private String accountNo;

    /**
     * 台账周期合理性
     */
    @Excel(name = "类型")
    private String errorType = "台账周期合理性";

    /**
     * 电表协议编号
     */
    @Excel(name = "电表户名/协议号码")
    private String ammeterid;


    /**
     * 局站编码
     */
    @Excel(name = "集团站址编码")
    private String stationcode;

    /**
     * 铁塔站址编码
     */
    @Excel(name = "铁塔站址编码")
    private String towerSiteCode;


    /**
     * 本次台账录入时间
     */
    @Excel(name = "本次台账录入时间")
    private Date auditTime;
    /**
     * 上次台账录入时间
     */
    @Excel(name = "上次台账录入时间")
    private String auditTimeLast;

    /**
     * 报账周期差异（天）
     */
    @Excel(name = "报账周期差异（天）")
    private String differencesDay;

}
