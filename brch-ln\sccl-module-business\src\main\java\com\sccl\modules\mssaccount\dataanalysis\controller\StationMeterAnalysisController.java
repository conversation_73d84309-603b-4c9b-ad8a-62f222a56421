package com.sccl.modules.mssaccount.dataanalysis.controller;

import cn.hutool.core.lang.Dict;
import com.github.pagehelper.PageHelper;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.mssaccount.dataanalysis.dto.StatisticsListDTO;
import com.sccl.modules.mssaccount.dataanalysis.dto.StatisticsMeterListDTO;
import com.sccl.modules.mssaccount.dataanalysis.service.StationMeterAnalysisService;
import com.sccl.modules.mssaccount.dataanalysis.vo.PowerStationInfoDetailListVO;
import com.sccl.modules.mssaccount.dataanalysis.vo.PowerStationInfoMeterDetailListVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 电表 局站 统计分析
 */
@Slf4j
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RequestMapping(value = "/stationStatisticsAnalysis")
public class StationMeterAnalysisController extends BaseController {

    private final StationMeterAnalysisService analysisService;

    /**
     * 局站统计列表
     */
    @GetMapping(value = "/stationStatisticsList")
    public TableDataInfo statisticsList(StatisticsListDTO dto) {
        return analysisService.statisticsList(dto);
    }

    /**
     * 局站明细列表
     */
    @GetMapping(value = "/stationStatisticsDetailList")
    public TableDataInfo statisticsDetailList(StatisticsListDTO dto) {
        PageHelper.startPage(dto.getPageNumber(), dto.getPageSize());
        List<PowerStationInfoDetailListVO> list = analysisService.stationStatisticsDetailList(dto);
        return getDataTable(list);
    }



    /**
     * 电表统计列表
     */
    @GetMapping(value = "/statisticsMeterList")
    public Dict statisticsMeterList(StatisticsMeterListDTO dto) {
        return analysisService.statisticsMeterList(dto);
    }

    /**
     * 电表明细列表
     */
    @GetMapping(value = "/statisticsMeterDetailList")
    public TableDataInfo statisticsMeterDetailList(StatisticsMeterListDTO dto) {
        PageHelper.startPage(dto.getPageNumber(), dto.getPageSize());
        List<PowerStationInfoMeterDetailListVO> list = analysisService.stationStatisticsMeterDetailList(dto);
        return getDataTable(list);
    }
}
