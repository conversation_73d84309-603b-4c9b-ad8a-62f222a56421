package com.sccl.modules.business.powerintelligentinf2.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.math.BigDecimal;


/**
 * PUE管控主表 power_intelligent_inf2
 * 
 * <AUTHOR>
 * @date 2019-07-18
 */
public class PowerIntelligentInf2
{
	private static final long serialVersionUID = 1L;
	
    /**  */
    private Long pinId;
    /**  */
    private String rotatingringname;
    /** 局站名称 */
    private String stationname;
    /** 分公司 */
    private Long company;
    /** 部门 */
    private Long country;
    /**  */
    private String ammetercode10;
    /**  */
    private String ammetercode11;
    /**  */
    private String ammetercode12;
    /**  */
    private String ammerercode13;
    /**  */
    private String ammetercode14;
    /**  */
    private String ammetercode15;
    /**  */
    private String ammetercode16;
    /**  */
    private String ammetercode17;
    /**  */
    private String ammetercode18;
    /**  */
    private String ammetercode19;
    /**  */
    private String ammetercode20;
    /**  */
    private String ammetercode21;
    /**  */
    private String ammetercode22;
    /**  */
    private String ammetercode23;
    /**  */
    private String ammetercode24;
    /**  */
    private String ammetercode25;
    /**  */
    private String ammetercode26;
    /**  */
    private String ammetercode27;
    /**  */
    private String ammetercode28;
    /**  */
    private String ammetercode29;
    /**  */
    private String ammetercode30;
    /**  */
    private String ammetercode31;
    /**  */
    private String ammetercode32;
    /**  */
    private String ammetercode33;
    /**  */
    private String tempsql;
    /**  */
    private String companyname;
    /**  */
    private String zystationname;
    /** 百分比 */
    private BigDecimal percent;

	public Long getPinId() {
		return pinId;
	}

	public void setPinId(Long pinId) {
		this.pinId = pinId;
	}

	public void setRotatingringname(String rotatingringname)
	{
		this.rotatingringname = rotatingringname;
	}

	public String getRotatingringname() 
	{
		return rotatingringname;
	}

	public void setStationname(String stationname)
	{
		this.stationname = stationname;
	}

	public String getStationname() 
	{
		return stationname;
	}

	public void setCompany(Long company)
	{
		this.company = company;
	}

	public Long getCompany() 
	{
		return company;
	}

	public void setCountry(Long country)
	{
		this.country = country;
	}

	public Long getCountry() 
	{
		return country;
	}

	public void setAmmetercode10(String ammetercode10)
	{
		this.ammetercode10 = ammetercode10;
	}

	public String getAmmetercode10() 
	{
		return ammetercode10;
	}

	public void setAmmetercode11(String ammetercode11)
	{
		this.ammetercode11 = ammetercode11;
	}

	public String getAmmetercode11() 
	{
		return ammetercode11;
	}

	public void setAmmetercode12(String ammetercode12)
	{
		this.ammetercode12 = ammetercode12;
	}

	public String getAmmetercode12() 
	{
		return ammetercode12;
	}

	public void setAmmerercode13(String ammerercode13)
	{
		this.ammerercode13 = ammerercode13;
	}

	public String getAmmerercode13() 
	{
		return ammerercode13;
	}

	public void setAmmetercode14(String ammetercode14)
	{
		this.ammetercode14 = ammetercode14;
	}

	public String getAmmetercode14() 
	{
		return ammetercode14;
	}

	public void setAmmetercode15(String ammetercode15)
	{
		this.ammetercode15 = ammetercode15;
	}

	public String getAmmetercode15() 
	{
		return ammetercode15;
	}

	public void setAmmetercode16(String ammetercode16)
	{
		this.ammetercode16 = ammetercode16;
	}

	public String getAmmetercode16() 
	{
		return ammetercode16;
	}

	public void setAmmetercode17(String ammetercode17)
	{
		this.ammetercode17 = ammetercode17;
	}

	public String getAmmetercode17() 
	{
		return ammetercode17;
	}

	public void setAmmetercode18(String ammetercode18)
	{
		this.ammetercode18 = ammetercode18;
	}

	public String getAmmetercode18() 
	{
		return ammetercode18;
	}

	public void setAmmetercode19(String ammetercode19)
	{
		this.ammetercode19 = ammetercode19;
	}

	public String getAmmetercode19() 
	{
		return ammetercode19;
	}

	public void setAmmetercode20(String ammetercode20)
	{
		this.ammetercode20 = ammetercode20;
	}

	public String getAmmetercode20() 
	{
		return ammetercode20;
	}

	public void setAmmetercode21(String ammetercode21)
	{
		this.ammetercode21 = ammetercode21;
	}

	public String getAmmetercode21() 
	{
		return ammetercode21;
	}

	public void setAmmetercode22(String ammetercode22)
	{
		this.ammetercode22 = ammetercode22;
	}

	public String getAmmetercode22() 
	{
		return ammetercode22;
	}

	public void setAmmetercode23(String ammetercode23)
	{
		this.ammetercode23 = ammetercode23;
	}

	public String getAmmetercode23() 
	{
		return ammetercode23;
	}

	public void setAmmetercode24(String ammetercode24)
	{
		this.ammetercode24 = ammetercode24;
	}

	public String getAmmetercode24() 
	{
		return ammetercode24;
	}

	public void setAmmetercode25(String ammetercode25)
	{
		this.ammetercode25 = ammetercode25;
	}

	public String getAmmetercode25() 
	{
		return ammetercode25;
	}

	public void setAmmetercode26(String ammetercode26)
	{
		this.ammetercode26 = ammetercode26;
	}

	public String getAmmetercode26() 
	{
		return ammetercode26;
	}

	public void setAmmetercode27(String ammetercode27)
	{
		this.ammetercode27 = ammetercode27;
	}

	public String getAmmetercode27() 
	{
		return ammetercode27;
	}

	public void setAmmetercode28(String ammetercode28)
	{
		this.ammetercode28 = ammetercode28;
	}

	public String getAmmetercode28() 
	{
		return ammetercode28;
	}

	public void setAmmetercode29(String ammetercode29)
	{
		this.ammetercode29 = ammetercode29;
	}

	public String getAmmetercode29() 
	{
		return ammetercode29;
	}

	public void setAmmetercode30(String ammetercode30)
	{
		this.ammetercode30 = ammetercode30;
	}

	public String getAmmetercode30() 
	{
		return ammetercode30;
	}

	public void setAmmetercode31(String ammetercode31)
	{
		this.ammetercode31 = ammetercode31;
	}

	public String getAmmetercode31() 
	{
		return ammetercode31;
	}

	public void setAmmetercode32(String ammetercode32)
	{
		this.ammetercode32 = ammetercode32;
	}

	public String getAmmetercode32() 
	{
		return ammetercode32;
	}

	public void setAmmetercode33(String ammetercode33)
	{
		this.ammetercode33 = ammetercode33;
	}

	public String getAmmetercode33() 
	{
		return ammetercode33;
	}

	public void setTempsql(String tempsql)
	{
		this.tempsql = tempsql;
	}

	public String getTempsql() 
	{
		return tempsql;
	}

	public void setCompanyname(String companyname)
	{
		this.companyname = companyname;
	}

	public String getCompanyname() 
	{
		return companyname;
	}

	public void setZystationname(String zystationname)
	{
		this.zystationname = zystationname;
	}

	public String getZystationname() 
	{
		return zystationname;
	}

	public void setPercent(BigDecimal percent)
	{
		this.percent = percent;
	}

	public BigDecimal getPercent() 
	{
		return percent;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("pinId", getPinId())
            .append("rotatingringname", getRotatingringname())
            .append("stationname", getStationname())
            .append("company", getCompany())
            .append("country", getCountry())
            .append("ammetercode10", getAmmetercode10())
            .append("ammetercode11", getAmmetercode11())
            .append("ammetercode12", getAmmetercode12())
            .append("ammerercode13", getAmmerercode13())
            .append("ammetercode14", getAmmetercode14())
            .append("ammetercode15", getAmmetercode15())
            .append("ammetercode16", getAmmetercode16())
            .append("ammetercode17", getAmmetercode17())
            .append("ammetercode18", getAmmetercode18())
            .append("ammetercode19", getAmmetercode19())
            .append("ammetercode20", getAmmetercode20())
            .append("ammetercode21", getAmmetercode21())
            .append("ammetercode22", getAmmetercode22())
            .append("ammetercode23", getAmmetercode23())
            .append("ammetercode24", getAmmetercode24())
            .append("ammetercode25", getAmmetercode25())
            .append("ammetercode26", getAmmetercode26())
            .append("ammetercode27", getAmmetercode27())
            .append("ammetercode28", getAmmetercode28())
            .append("ammetercode29", getAmmetercode29())
            .append("ammetercode30", getAmmetercode30())
            .append("ammetercode31", getAmmetercode31())
            .append("ammetercode32", getAmmetercode32())
            .append("ammetercode33", getAmmetercode33())
            .append("tempsql", getTempsql())
            .append("companyname", getCompanyname())
            .append("zystationname", getZystationname())
            .append("percent", getPercent())
            .toString();
    }
}
