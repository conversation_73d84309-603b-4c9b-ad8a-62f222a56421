package com.sccl.modules.business.statistical.framework.codec;

import com.sccl.common.utils.DateUtils;
import com.sccl.modules.autojob.util.convert.JsonUtil;
import com.sccl.modules.business.statistical.domain.StatisticalRedisValue;
import com.sccl.modules.business.statistical.framework.StatisticalIndex;

/**
 * 统计指标Redis序列化器
 *
 * @<PERSON> <PERSON>
 * @Date 2022/10/25 17:31
 */
public class StatisticalRedisSerializerAndDeserializer implements StatisticalSerializer<StatisticalRedisValue>, StatisticalDeserializer<StatisticalRedisValue> {

    @Override
    public StatisticalRedisValue serialize(StatisticalIndex statisticalIndex) {
        StatisticalRedisValue redisValue = new StatisticalRedisValue();
        redisValue.setContent(JsonUtil.pojoToJsonString(statisticalIndex.getContent()));
        redisValue.setContentType(statisticalIndex
                .getContentType()
                .getName());
        redisValue.setGroupAlias(statisticalIndex.getGroupAlias());
        redisValue.setOwnerAs(statisticalIndex.getOwnerAs());
        redisValue.setStatisticalTime(DateUtils.formatDate(statisticalIndex.getStatisticalTime()));
        redisValue.setTitle(statisticalIndex.getTitle());
        return redisValue;
    }

    @Override
    public StatisticalIndex deserialize(StatisticalRedisValue redisValue) {
        StatisticalIndex index = new StatisticalIndex();
        try {
            index.setContentType(Class.forName(redisValue.getContentType()));
            index.setStatisticalTime(DateUtils.parseDate(redisValue.getStatisticalTime(), "yyyy-MM-dd"));
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        index.setContent(JsonUtil.jsonStringToPojo(redisValue.getContent(), index.getContentType()));
        index.setGroupAlias(redisValue.getGroupAlias());
        index.setOwnerAs(redisValue.getOwnerAs());
        index.setTitle(redisValue.getTitle());
        return index;
    }
}
