//package com.sccl.modules.business.meterInfoalljt.controller;
//
//import com.sccl.common.lang.StringUtils;
//import com.sccl.common.utils.ShiroUtils;
//import com.sccl.common.web.controller.BaseController;
//import com.sccl.framework.utils.ExcelUtil;
//import com.sccl.framework.web.domain.AjaxResult;
//import com.sccl.framework.web.domain.IdNameVO;
//import com.sccl.framework.web.page.TableDataInfo;
//import com.sccl.modules.autojob.util.convert.MessageMaster;
//import com.sccl.modules.business.cost.service.IConsistencyGkService;
//import com.sccl.modules.business.meterInfoalljt.service.IMeterinfoAllJtService;
//import com.sccl.modules.business.meterInfoalljt.vo.MeterinfoAllJtImportExlVo;
//import com.sccl.modules.business.meterInfoalljt.vo.MeterinfoAllJtResultVo;
//import com.sccl.modules.business.meterInfoalljt.vo.MeterinfoAllJtSearchVo;
//import com.sccl.modules.business.statinAudit.mapper.PowerStationInfoMapper;
//import com.sccl.modules.system.attachments.domain.UpLoadFile;
//import com.sccl.modules.system.user.domain.User;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.fileupload.FileItem;
//import org.apache.commons.fileupload.FileItemFactory;
//import org.apache.commons.fileupload.disk.DiskFileItemFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.http.MediaType;
//import org.springframework.web.bind.annotation.*;
//import org.springframework.web.multipart.MultipartFile;
//import org.springframework.web.multipart.MultipartHttpServletRequest;
//import org.springframework.web.multipart.commons.CommonsMultipartFile;
//
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import java.io.IOException;
//import java.io.InputStream;
//import java.io.OutputStream;
//import java.util.*;
//
/// **
// * 在网表计数据清单
// *
// * <AUTHOR>
// * @date 2025-04-29
// */
//@RestController
//@RequestMapping("/business/meterinfoAllJt")
//@Slf4j
//public class MeterinfoAllJtController extends BaseController {
//
//    @Autowired
//    private IMeterinfoAllJtService meterinfoAllJtService;
//    @Autowired
//    private IConsistencyGkService consistencyGkService;
//    @Autowired
//    private PowerStationInfoMapper stationInfoMapper;
//
//    @Value("${sccl.deployTo}")
//    private String deployTo;
//
//    private static final Map<String, String> IMPORT_COLUMN_MAP = new HashMap<>();
//
//    static {
//        IMPORT_COLUMN_MAP.put("msg_id", "msgId");
//        IMPORT_COLUMN_MAP.put("province_code", "provinceCode");
//        IMPORT_COLUMN_MAP.put("city_code", "cityCode");
//        IMPORT_COLUMN_MAP.put("city_name", "cityName");
//        IMPORT_COLUMN_MAP.put("county_code", "countyCode");
//        IMPORT_COLUMN_MAP.put("county_name", "countyName");
//        IMPORT_COLUMN_MAP.put("energy_meter_code", "energyMeterCode");
//        IMPORT_COLUMN_MAP.put("energy_meter_name", "energyMeterName");
//        IMPORT_COLUMN_MAP.put("energy_type", "energyType");
//        IMPORT_COLUMN_MAP.put("type_station_code", "typeStationCode");
//        IMPORT_COLUMN_MAP.put("contract_price", "contractPrice");
//        IMPORT_COLUMN_MAP.put("status_", "status");
//        IMPORT_COLUMN_MAP.put("usage_", "usageCopy");
//        IMPORT_COLUMN_MAP.put("station_code", "stationCode");
//        IMPORT_COLUMN_MAP.put("station_name", "stationName");
//        IMPORT_COLUMN_MAP.put("station_location", "stationLocation");
//        IMPORT_COLUMN_MAP.put("station_status", "stationStatus");
//        IMPORT_COLUMN_MAP.put("station_type", "stationType");
//        IMPORT_COLUMN_MAP.put("large_lndustrial_electricity_flag", "largeIndustrialElectricityFlag");
//        IMPORT_COLUMN_MAP.put("energy_supply_way", "energySupplyWay");
//        IMPORT_COLUMN_MAP.put("power_grid_energy_meter_code", "powerGridEnergyMeterCode");
//        IMPORT_COLUMN_MAP.put("site_code", "siteCode");
//        IMPORT_COLUMN_MAP.put("create_time", "createTime");
//    }
//
//    /**
//     * 一览查询
//     */
//    @PostMapping("/list")
//    @ResponseBody
//    public TableDataInfo list(@RequestBody MeterinfoAllJtSearchVo searchVo) {
//        setParam(searchVo);//参数处理
//        startPage(searchVo.getPageNum(), searchVo.getPageSize());
//        List<MeterinfoAllJtResultVo> result = meterinfoAllJtService.list(searchVo);
//        return getDataTable(result);
//    }
//
//    /**
//     * 一览 导出
//     */
//    @PostMapping("/export")
//    @ResponseBody
//    public AjaxResult export(@RequestBody MeterinfoAllJtSearchVo searchVo, HttpServletRequest request, HttpServletResponse response) throws Exception {
//        setParam(searchVo);//参数处理
//        startPage(searchVo.getPageNum(), searchVo.getPageSize());
//        List<MeterinfoAllJtResultVo> list = meterinfoAllJtService.list(searchVo);
//        ExcelUtil<MeterinfoAllJtResultVo> util = new ExcelUtil<>(MeterinfoAllJtResultVo.class);
//        return util.exportExcelToBrowser(response, list, "在网表计数据");
//    }
//
//    /**
//     * 查询参数处理
//     * @param searchVo
//     */
//    private void setParam(MeterinfoAllJtSearchVo searchVo) {
//
//        //所属公司和所属部门处理
//        if (StringUtils.isBlank(searchVo.getCompany()) || StringUtils.isBlank(searchVo.getCountry())) {
//            User user = ShiroUtils.getUser();
//            String admin = consistencyGkService.getAdmin(user);//判断登录用是省/市/区级
//            if ("2".equals(admin)) {
//                //市级
//                if (StringUtils.isBlank(searchVo.getCompany())) {
//                    List<IdNameVO> companies = user.getCompanies();
//                    if (companies != null && companies.size() > 0) {
//                        List<String> ids = new ArrayList<>();
//                        for (IdNameVO company : companies) {
//                            ids.add(company.getId());
//                        }
//                        searchVo.setCompanys(ids.toArray(new String[companies.size()]));
//                    }
//                }
//            } else if ("3".equals(admin)) {
//                //区县级
//                if (StringUtils.isBlank(searchVo.getCompany())) {
//                    List<IdNameVO> companies = user.getCompanies();
//                    if (companies != null && companies.size() > 0) {
//                        List<String> ids = new ArrayList<>();
//                        for (IdNameVO company : companies) {
//                            ids.add(company.getId());
//                        }
//                        searchVo.setCompanys(ids.toArray(new String[companies.size()]));
//                    }
//                }
//                if (StringUtils.isBlank(searchVo.getCountry())) {
//                    List<IdNameVO> countrys = user.getDepartments();
//                    if (countrys != null && countrys.size() > 0) {
//                        List<String> ids = new ArrayList<>();
//                        for (IdNameVO country : countrys) {
//                            ids.add(country.getId());
//                        }
//                        searchVo.setCountrys(ids.toArray(new String[countrys.size()]));
//                    }
//                }
//            }
//        }
//
//        //数据部门处理
//        String prov = "9999999991";              //四川登录用户所属省份id
//        if ("ln".equals(deployTo)) {
//            prov = "2600000000";                 //辽宁登录用户所属省份id
//        }
//        if ("-1".equals(searchVo.getCompany()) || prov.equals(searchVo.getCompany())) {
//            searchVo.setCompany("");
//        }
//        if ("-1".equals(searchVo.getCountry())) {
//            searchVo.setCountry("");
//        }
//        //电表编号
//        if (StringUtils.isNotBlank(searchVo.getAmmeterCode())) {
//            searchVo.setAmmeterCodes(StringUtils.split(searchVo.getAmmeterCode(), ","));
//        }
//        //站址编码
//        if (StringUtils.isNotBlank(searchVo.getResstationcode())) {
//            searchVo.setResstationcodes(StringUtils.split(searchVo.getResstationcode(), ","));
//        }
//    }
//
//    /**
//     * 导入在网表计数据
//     */
//    @PostMapping(value = "/uploadExcel")
//    public String uploadExcel(HttpServletRequest request, HttpServletResponse response, UpLoadFile uploadFile)
//            throws Exception {
//        response.setContentType("text/html;charset=utf-8");
//        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
//        Iterator<String> iterator = multiRequest.getFileNames();
//        List<MeterinfoAllJtImportExlVo> list = new ArrayList<>();
//        while (iterator.hasNext()) {
//            String name = iterator.next();
//            List<MultipartFile> files = multiRequest.getFiles(name);
//            // 一次遍历所有文件
//            for (MultipartFile file : files) {
//                if (file == null) {
//                    continue;
//                }
//
//                //如果文件大小为0则不上传
//                long fileSize = file.getSize();
//                if (fileSize <= 0L) {
//                    return MessageMaster.getMessage(MessageMaster.Code.ERROR, "文件名称：【" + file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("\\") + 1, file.getOriginalFilename().length()) + "】的文件大小为0,请核对!");
//                }
//
//                ExcelUtil<MeterinfoAllJtImportExlVo> excelUtil = new ExcelUtil<MeterinfoAllJtImportExlVo>(MeterinfoAllJtImportExlVo.class);
//                MultipartFile multipartFile = getMultipartFile(file.getInputStream(), "在网表计数据");
//                List<MeterinfoAllJtImportExlVo> content = excelUtil.importExcel("Sheet1", multipartFile, IMPORT_COLUMN_MAP);
//                if (CollectionUtils.isEmpty(content)) {
//                    return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "文件内容为空，请检查格式或内容");
//                }
//
//                list.addAll(content);
//            }
//        }
//
//        //导入在网表计数据
//        String result = meterinfoAllJtService.importExcel(list);
//        list = null;
//        return result;
//    }
//
//    /**
//     * 获取封装得MultipartFile
//     *
//     * @param inputStream inputStream
//     * @param fileName    fileName
//     * @return MultipartFile
//     */
//    public MultipartFile getMultipartFile(InputStream inputStream, String fileName) {
//        FileItem fileItem = createFileItem(inputStream, fileName);
//        //CommonsMultipartFile是feign对multipartFile的封装，但是要FileItem类对象
//        return new CommonsMultipartFile(fileItem);
//    }
//
//    /**
//     * FileItem类对象创建
//     *
//     * @param inputStream inputStream
//     * @param fileName    fileName
//     * @return FileItem
//     */
//    public FileItem createFileItem(InputStream inputStream, String fileName) {
//        FileItemFactory factory = new DiskFileItemFactory(16, null);
//        String textFieldName = "file";
//        FileItem item = factory.createItem(textFieldName, MediaType.MULTIPART_FORM_DATA_VALUE, true, fileName);
//        int bytesRead = 0;
//        byte[] buffer = new byte[10 * 1024 * 1024];
//        OutputStream os = null;
//        //使用输出流输出输入流的字节
//        try {
//            os = item.getOutputStream();
//            while ((bytesRead = inputStream.read(buffer, 0, 8192)) != -1) {
//                os.write(buffer, 0, bytesRead);
//            }
//            inputStream.close();
//        } catch (IOException e) {
//            log.error("Stream copy exception", e);
//            throw new IllegalArgumentException("文件上传失败");
//        } finally {
//            if (os != null) {
//                try {
//                    os.close();
//                } catch (IOException e) {
//                    log.error("Stream close exception", e);
//
//                }
//            }
//            if (inputStream != null) {
//                try {
//                    inputStream.close();
//                } catch (IOException e) {
//                    log.error("Stream close exception", e);
//                }
//            }
//        }
//
//        return item;
//    }
//}
