package com.sccl.modules.business.ecceptiondetail.mapper;

import com.sccl.modules.business.ecceptiondetail.domain.EcceptionDetail;
import com.sccl.framework.mapper.BaseMapper;

import java.util.List;

/**
 * 公共异常明细 数据层
 *
 * <AUTHOR>
 * @date 2023-03-23
 */
public interface EcceptionDetailMapper extends BaseMapper<EcceptionDetail> {
    int deleteByExceptionSourceID(List<Long> exceptionSourceIds);

    int stopProcess(List<Long> ids);

}