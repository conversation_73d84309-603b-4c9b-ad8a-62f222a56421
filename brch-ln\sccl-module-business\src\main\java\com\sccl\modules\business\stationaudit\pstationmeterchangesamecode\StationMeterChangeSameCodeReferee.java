package com.sccl.modules.business.stationaudit.pstationmeterchangesamecode;


import com.enrising.dcarbon.audit.AbstractReferee;
import com.enrising.dcarbon.audit.Auditable;
import com.enrising.dcarbon.audit.RefereeDatasource;
import com.enrising.dcarbon.audit.RefereeResult;
import com.sccl.modules.business.stationaudit.msshistory.HistoryResult;
import com.sccl.modules.business.stationaudit.pstationaccountchange.StationAccountChangeRefereeContent;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class StationMeterChangeSameCodeReferee extends AbstractReferee {
    @Override
    public RefereeResult doReferee(RefereeResult lastRefereeResult, Auditable auditable, Map<Class<?
                extends RefereeDatasource>, List<RefereeDatasource>> refereeDatasourceList) {
        //取出数据
        List<RefereeDatasource> list = refereeDatasourceList.get(StationMeterChangeSameCodeRefereeContent.class)
                                                            .stream().filter(Objects::nonNull).collect(Collectors.toList());

        //放数据
        HistoryResult result = new HistoryResult();
        result.setTopic("电表变动");
        result.setList(list);
        return result;
    }


    public StationMeterChangeSameCodeReferee(String refereeName) {
        super(refereeName);
    }

}
