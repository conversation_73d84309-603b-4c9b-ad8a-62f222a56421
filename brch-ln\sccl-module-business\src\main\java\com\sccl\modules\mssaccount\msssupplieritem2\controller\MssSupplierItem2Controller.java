package com.sccl.modules.mssaccount.msssupplieritem2.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.mssaccount.msssupplieritem2.domain.MssSupplierItem2;
import com.sccl.modules.mssaccount.msssupplieritem2.service.IMssSupplierItem2Service;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 供应商item 信息操作处理
 * 
 * <AUTHOR>
 * @date 2019-05-01
 */
@RestController
@RequestMapping("/mssaccount/mssSupplierItem2")
public class MssSupplierItem2Controller extends BaseController
{
    private String prefix = "mssaccount/mssSupplierItem2";
	
	@Autowired
	private IMssSupplierItem2Service mssSupplierItem2Service;
	
	@RequiresPermissions("mssaccount:mssSupplierItem2:view")
	@GetMapping()
	public String mssSupplierItem2()
	{
	    return prefix + "/mssSupplierItem2";
	}
	
	/**
	 * 查询供应商item列表
	 */
	@RequiresPermissions("mssaccount:mssSupplierItem2:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(MssSupplierItem2 mssSupplierItem2)
	{
		mssSupplierItem2.setInfStatus("1");
		startPage();
        List<MssSupplierItem2> list = mssSupplierItem2Service.selectList(mssSupplierItem2);
		return getDataTable(list);
	}
	
	/**
	 * 新增供应商item
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存供应商item
	 */
	@RequiresPermissions("mssaccount:mssSupplierItem2:add")
	//@Log(title = "供应商item", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody MssSupplierItem2 mssSupplierItem2)
	{		
		return toAjax(mssSupplierItem2Service.insert(mssSupplierItem2));
	}

	/**
	 * 修改供应商item
	 */
	@GetMapping("/edit/{iId}")
	public AjaxResult edit(@PathVariable("iId") Long iId)
	{
		MssSupplierItem2 mssSupplierItem2 = mssSupplierItem2Service.get(iId);

		Object object = JSONObject.toJSON(mssSupplierItem2);

        return this.success(object);
	}
	
	/**
	 * 修改保存供应商item
	 */
	@RequiresPermissions("mssaccount:mssSupplierItem2:edit")
	//@Log(title = "供应商item", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody MssSupplierItem2 mssSupplierItem2)
	{		
		return toAjax(mssSupplierItem2Service.update(mssSupplierItem2));
	}
	
	/**
	 * 删除供应商item
	 */
	@RequiresPermissions("mssaccount:mssSupplierItem2:remove")
	//@Log(title = "供应商item", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(mssSupplierItem2Service.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看供应商item
     */
    @RequiresPermissions("mssaccount:mssSupplierItem2:view")
    @GetMapping("/view/{iId}")
    @ResponseBody
    public AjaxResult view(@PathVariable("iId") Long iId)
    {
		MssSupplierItem2 mssSupplierItem2 = mssSupplierItem2Service.get(iId);

        Object object = JSONObject.toJSON(mssSupplierItem2);

        return this.success(object);
    }

	/**
	 * 查看供应商item
	 */
	@RequiresPermissions("mssaccount:mssSupplierItem2:list")
	@PostMapping("/query")
	@ResponseBody
	public AjaxResult query(@RequestBody MssSupplierItem2 item)
	{
		Object object = JSONObject.toJSON(mssSupplierItem2Service.selectList(item));
		return this.success(object);
	}
}
