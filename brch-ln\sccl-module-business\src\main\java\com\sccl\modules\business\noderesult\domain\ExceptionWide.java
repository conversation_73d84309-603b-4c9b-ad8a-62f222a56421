package com.sccl.modules.business.noderesult.domain;

import lombok.Data;

import java.math.BigDecimal;
public enum ExceptionWide {
    equNUll(BigDecimal.valueOf(-999999));

    ExceptionWide(BigDecimal ex1) {
        this.ex1 = ex1;
    }

    ExceptionWide() {
    }

    private BigDecimal ex1;

    public BigDecimal getEx1() {
        return ex1;
    }

    public void setEx1(BigDecimal ex1) {
        this.ex1 = ex1;
    }
}
