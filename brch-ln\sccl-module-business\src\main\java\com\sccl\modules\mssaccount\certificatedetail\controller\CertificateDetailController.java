package com.sccl.modules.mssaccount.certificatedetail.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.mssaccount.certificatedetail.domain.CertificateDetail;
import com.sccl.modules.mssaccount.certificatedetail.service.ICertificateDetailService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * view_certificate_detail 信息操作处理
 * 
 * <AUTHOR>
 * @date 2019-09-20
 */
@RestController
@RequestMapping("/mssaccount/certificateDetail")
public class CertificateDetailController extends BaseController
{
    private String prefix = "mssaccount/certificateDetail";
	
	@Autowired
	private ICertificateDetailService certificateDetailService;
	
	@RequiresPermissions("mssaccount:mssaccountbill:view")
	@GetMapping()
	public String certificateDetail()
	{
	    return prefix + "/certificateDetail";
	}
	
	/**
	 * 查询view_certificate_detail列表
	 */
	@RequiresPermissions("mssaccount:mssaccountbill:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(CertificateDetail certificateDetail)
	{
		startPage();
        List<CertificateDetail> list = certificateDetailService.selectList(certificateDetail);
		return getDataTable(list);
	}
	
	/**
	 * 新增view_certificate_detail
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存view_certificate_detail
	 */
	@RequiresPermissions("mssaccount:mssaccountbill:add")
	//@Log(title = "view_certificate_detail", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody CertificateDetail certificateDetail)
	{		
		return toAjax(certificateDetailService.insert(certificateDetail));
	}

	/**
	 * 修改view_certificate_detail
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		CertificateDetail certificateDetail = certificateDetailService.get(id);

		Object object = JSONObject.toJSON(certificateDetail);

        return this.success(object);
	}
	
	/**
	 * 修改保存view_certificate_detail
	 */
	@RequiresPermissions("mssaccount:mssaccountbill:edit")
	//@Log(title = "view_certificate_detail", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody CertificateDetail certificateDetail)
	{		
		return toAjax(certificateDetailService.update(certificateDetail));
	}
	
	/**
	 * 删除view_certificate_detail
	 */
	@RequiresPermissions("mssaccount:mssaccountbill:remove")
	//@Log(title = "view_certificate_detail", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(certificateDetailService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看view_certificate_detail
     */
    @RequiresPermissions("mssaccount:mssaccountbill:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		CertificateDetail certificateDetail = certificateDetailService.get(id);

        Object object = JSONObject.toJSON(certificateDetail);

        return this.success(object);
    }

}
