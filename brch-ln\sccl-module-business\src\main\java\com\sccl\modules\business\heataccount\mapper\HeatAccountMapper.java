package com.sccl.modules.business.heataccount.mapper;

import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.heataccount.domain.HeatAccount;
import com.sccl.modules.business.heataccount.domain.HeatAccountRequest;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


public interface HeatAccountMapper extends BaseMapper<HeatAccount> {

    List<HeatAccount> listHeatAccount(HeatAccountRequest request);

    List<HeatAccount> listHeatAccountByCondition(HeatAccountRequest request);

    List<HeatAccount> selectByIds(@Param("ids") List<Long> ids);

    /**
     * 已报账金额
     */
    BigDecimal selectAccountMoney(@Param("id") Long id);


    int updateStatus(Map<String, Object> map);

    int updateNewAccountStatusByType(@Param("id") Long id);
    int batchUpdateHeatAccount(@Param("accounts") List<HeatAccount> accounts);

    int updateStatuByParid(Map<String, Object> map);
}
