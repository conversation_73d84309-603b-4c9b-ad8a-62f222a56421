package com.sccl.modules.business.demo.service;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.business.demo.mapper.DemoMapper;
import com.sccl.modules.domain.demo.Demo;


/**
 * 测试 服务层实现
 * 
 * <AUTHOR>
 * @date 2019-04-02
 */
@Service
public class DemoServiceImpl extends BaseServiceImpl<Demo>
{

	@Autowired
	private DemoMapper demoMapper;
	
	public List<Map<String, String>> visitOracle() {
		return demoMapper.visitOracle();
	}

}
