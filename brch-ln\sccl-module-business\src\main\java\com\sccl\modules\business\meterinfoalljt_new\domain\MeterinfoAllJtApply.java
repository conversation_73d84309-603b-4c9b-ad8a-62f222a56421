package com.sccl.modules.business.meterinfoalljt_new.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 在网表计数据清单新增申请表
 */
@Data
@EqualsAndHashCode
@TableName(value = "meterinfo_all_jt_apply")
public class MeterinfoAllJtApply implements Serializable {


	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	private Long id;

	/**
	 * 所属分公司
	 */
	private Long company;

	/**
	 * 所属分公司名称
	 */
	private String companyName;

	/**
	 * 所属部门
	 */
	private Long country;

	/**
	 * 所属部门名称
	 */
	private String countryName;

	/**
	 * 市局组织编码 财辅组织
	 */
	private String cityCode;

	/**
	 * 区县组织编码 财辅组织
	 */
	private String countyCode;

	/**
	 * 电表id
	 */
	private Long ammeterId;

	/**
	 * 电表编号
	 */
	private String ammeterCode;

	/**
	 * 电表状态
	 */
	private Integer ammeterStatus;

	/**
	 * 项目名称
	 */
	private String projectname;

	/**
	 * 是否实体电表
	 */
	private Integer isentityammeter;

	/**
	 * 对外结算类型
	 */
	private Integer directsupplyflag;

	/**
	 * 单价
	 */
	private BigDecimal price;

	/**
	 * 供电局电表编号
	 */
	private String supplybureauammetercode;

	/**
	 * 电表用途
	 */
	private Integer ammeteruse;

	/**
	 * 局(站)id
	 */
	private Long stationId;

	/**
	 * 局站编码
	 */
	private String stationcode;

	/**
	 * 局站名称
	 */
	private String stationname;

	/**
	 * 站址编码
	 */
	private String resstationcode;

	/**
	 * 站址名称
	 */
	private String resstationname;

	/**
	 * 5gr站址编码
	 */
	private String stationcodeintid;

	/**
	 * 局站状态(0停用、1在用)
	 */
	private String stationStatus;

	/**
	 * 局站地址
	 */
	private String stationAddress;

	/**
	 * 是否是大工业用电
	 */
	private String isbigfactories;

	/**
	 * 用电类型
	 */
	private Long electrotype;

	/**
	 * 单据状态
	 */
	private Integer status;

	/**
	 * 提交人
	 */
	private String submitBy;

	/**
	 * 提交时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date submitTime;

	/**
	 * 创建人
	 */
	private String createdBy;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;

	/**
	 * 删除标志 0/1 正常/删除
	 */
	private String delFlag;

	/**
	 * 新增
	 *
	 * @param userName 当前登录用户
	 */
	public void initInsert(String userName) {
		delFlag = "0";
		createdBy = userName;
		createTime = new Date();
		updateTime = new Date();
	}

	/**
	 * 修改
	 */
	public void initUpdate() {
		updateTime = new Date();
	}

	/**
	 * 主键id数组
	 */
	private String[] ids;
}
