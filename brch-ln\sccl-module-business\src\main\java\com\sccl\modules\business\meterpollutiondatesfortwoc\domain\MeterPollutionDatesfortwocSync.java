package com.sccl.modules.business.meterpollutiondatesfortwoc.domain;

import com.sccl.modules.business.twoc.domain.TwoCFlag;
import lombok.Data;

@Data
public class MeterPollutionDatesfortwocSync implements TwoCFlag {
    /**
     * 用能月份 YYYYMM
     */
    private String statisPeriod;
    /**
     * 省份编码
     */
    private String provinCecode;
    /**
     * 市局组织编码
     */
    private String cityCode;
    /**
     * 市局组织名称
     */
    private String cityName;
    /**
     * 区县组织名称
     */
    private String countyCode;
    /**
     * 区县组织名称
     */
    private String countyName;
    /**
     * 归属类型 1:集团存续 2:股份上市
     */
    private String groupType;
    /**
     * 局站类型
     */
    private String stationtype;
    /**
     * 废水排放量（吨）
     */
    private String wastewaterDischarge;
    /**
     * 一般固体废物产生量（吨）
     */
    private String solidTrashProduce;
    /**
     * 一般固体废物综合利用量（吨）
     */
    private String solidTrashHandle;
    /**
     * 综合利用往年贮存量（吨）
     */
    private String solidTrashStorage;
    /**
     * 危险废物产生量（吨）
     */
    private String hazardousWasteProduce;
    /**
     * 危险废物处置量（吨）
     */
    private String hazardousWasteHandle;
    /**
     * 处置往年贮存量（吨）
     */
    private String hazardousWasteStorage;
    /**
     * 土壤污染治理面积
     */
    private String soilPollutionGovern;
    /**
     * 土壤污染需要治理面积
     */
    private String soilPollutionNeedgovern;
    /**
     * 矿山（或生态）修复治理面积
     */
    private String ecologyGovern;
    /**
     * 矿山（或生态）需要修复治理面积
     */
    private String ecologyNeedGovern;
    /**
     * 废气治理设施数
     */
    private String wasteGastreaTmentTotal;
    /**
     * 废气治理设施处理能力
     */
    private String wasteGastreaTmentAble;
    /**
     * 废水治理设施数
     */
    private String wasteWaterTreatmenttoTal;
    /**
     * 废水治理设施处理能力
     */
    private String wasteWaterTreatmentAble;
    /**
     * 生态环境污染源
     */
    private String ecologyPollutionSource;
    /**
     * 生态环境风险点
     */
    private String ecologyRiskAmount;
    /**
     * 节能投入
     */
    private String energyInvestment;
    /**
     * 环保投入
     */
    private String environmentInvestment;
}
