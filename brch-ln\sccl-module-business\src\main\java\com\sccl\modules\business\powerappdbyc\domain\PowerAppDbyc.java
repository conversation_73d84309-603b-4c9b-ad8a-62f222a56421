package com.sccl.modules.business.powerappdbyc.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;


/**
 * 大数据基站异常表 power_app_dbyc
 * 
 * <AUTHOR>
 * @date 2022-03-20
 */
public class PowerAppDbyc extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
    /** 接收时间 */
    private Date intputdate;
    /** 报账单编号 */
    private String writeoffInstanceCode;
    /** 用电类型 */
    private String electrotype;
    /** 电表ID */
    private String ammeterid;
    /** 分公司 */
    private String company;
    /** 关联局站id */
    private String stationcode;
    /** 电表项目名称 */
    private String projectname;
    /** 电表类型 */
    private String category;
    /** 电表协议编号 */
    private String protocolname;
    /** 分支局 */
    private String substation;
    /** 本地网标识 */
    private String latnId;
    /** 电表倍率 */
    private String multtimes;
    /** 定额度数 */
    private String quotareadings;
    /** 定额浮动比 */
    private String quotereadingsratio;
    /** 报账月份 */
    private String accountno;
    /** 业务时间 */
    private String busiDate;
    /** 上期止度 */
    private String prevtotalreadings;
    /** 本期止度 */
    private String curtotalreadings;
    /** 上期峰止度 */
    private String prevhighreadings;
    /** 上期平止度 */
    private String prevflatreadings;
    /** 上期谷止度 */
    private String prevlowreadings;
    /** 本期峰止度 */
    private String curhighreadings;
    /** 本期平止度 */
    private String curflatreadings;
    /** 本期谷止度 */
    private String curlowreadings;
    /** 本次耗电量 */
    private String curusedreadings;
    /** 变压器损耗 */
    private String transformerullage;
    /** 单价 */
    private String unitpirce;
    /** 本次耗电金额 */
    private String curuserdmoney;
    /** 损耗 */
    private String ullagemoney;
    /** 合计 */
    private String accountmoney;
    /** 状态 */
    private String status;
    /** 分割比例 */
    private String percentHive;
    /** 中继性质 */
    private String property;
    /** 机构名称 */
    private String orgname;
    /** 站址编码 */
    private String resstationcode;
    /** 月分区 */
    private String monthNo;
    /** 集团表id */
    private String enbid;
    /** 集团表站址 */
    private String eneaddrCode;
    /** 上行业务信息实际平均占用prb资源个数 */
    private String sumSxPrb;
    /** 上行业务信息实际平均占用prb资源个数(qci1) */
    private String sumSxPrbQci1;
    /** 下行业务信息实际平均占用prb资源个数 */
    private String sumXxPrb;

	/** 下行业务信息实际平均占用prb资源个数(qci1) */
	private String sumXxPrbQci1;
    /** 最大rrc连接用户数 */
    private String sumMaxRrc;
    /** 平均rrc连接用户数 */
    private String sumAvgRrc;
    /** 上行平均激活用户数 */
    private String sumSxAvgJh;
    /** 上行平均激活用户数(qci1) */
    private String sumSxAvgJhQci1;
    /** 下行平均激活用户数 */
    private String sumXxAvgJh;
    /** 下行平均激活用户数(qci1) */
    private String sumXxAvgJhQci1;
    /** pdcp层上行用户面流量字节数 */
    private String sumSxPdcp;
    /** pdcp层下行用户面流量字节数 */
    private String sumXxPdcp;
    /** volte语音峰值用户数 */
    private String sumMaxVlote;
    /** volte语音时长 */
    private String sumVolteTime;
    /** 市州名称 */
    private String latnName;

	public String getCountryName() {
		return countryName;
	}

	public void setCountryName(String countryName) {
		this.countryName = countryName;
	}

	/** 部门名称 */
	private String countryName;
	public String getSumXxPrbQci1() {
		return sumXxPrbQci1;
	}

	public void setSumXxPrbQci1(String sumXxPrbQci1) {
		this.sumXxPrbQci1 = sumXxPrbQci1;
	}


	public void setIntputdate(Date intputdate)
	{
		this.intputdate = intputdate;
	}

	public Date getIntputdate() 
	{
		return intputdate;
	}

	public void setWriteoffInstanceCode(String writeoffInstanceCode)
	{
		this.writeoffInstanceCode = writeoffInstanceCode;
	}

	public String getWriteoffInstanceCode() 
	{
		return writeoffInstanceCode;
	}

	public void setElectrotype(String electrotype)
	{
		this.electrotype = electrotype;
	}

	public String getElectrotype() 
	{
		return electrotype;
	}

	public void setAmmeterid(String ammeterid)
	{
		this.ammeterid = ammeterid;
	}

	public String getAmmeterid() 
	{
		return ammeterid;
	}

	public void setCompany(String company)
	{
		this.company = company;
	}

	public String getCompany() 
	{
		return company;
	}

	public void setStationcode(String stationcode)
	{
		this.stationcode = stationcode;
	}

	public String getStationcode() 
	{
		return stationcode;
	}

	public void setProjectname(String projectname)
	{
		this.projectname = projectname;
	}

	public String getProjectname() 
	{
		return projectname;
	}

	public void setCategory(String category)
	{
		this.category = category;
	}

	public String getCategory() 
	{
		return category;
	}

	public void setProtocolname(String protocolname)
	{
		this.protocolname = protocolname;
	}

	public String getProtocolname() 
	{
		return protocolname;
	}

	public void setSubstation(String substation)
	{
		this.substation = substation;
	}

	public String getSubstation() 
	{
		return substation;
	}

	public void setLatnId(String latnId)
	{
		this.latnId = latnId;
	}

	public String getLatnId() 
	{
		return latnId;
	}

	public void setMulttimes(String multtimes)
	{
		this.multtimes = multtimes;
	}

	public String getMulttimes() 
	{
		return multtimes;
	}

	public void setQuotareadings(String quotareadings)
	{
		this.quotareadings = quotareadings;
	}

	public String getQuotareadings() 
	{
		return quotareadings;
	}

	public void setQuotereadingsratio(String quotereadingsratio)
	{
		this.quotereadingsratio = quotereadingsratio;
	}

	public String getQuotereadingsratio() 
	{
		return quotereadingsratio;
	}

	public void setAccountno(String accountno)
	{
		this.accountno = accountno;
	}

	public String getAccountno() 
	{
		return accountno;
	}

	public void setBusiDate(String busiDate)
	{
		this.busiDate = busiDate;
	}

	public String getBusiDate() 
	{
		return busiDate;
	}

	public void setPrevtotalreadings(String prevtotalreadings)
	{
		this.prevtotalreadings = prevtotalreadings;
	}

	public String getPrevtotalreadings() 
	{
		return prevtotalreadings;
	}

	public void setCurtotalreadings(String curtotalreadings)
	{
		this.curtotalreadings = curtotalreadings;
	}

	public String getCurtotalreadings() 
	{
		return curtotalreadings;
	}

	public void setPrevhighreadings(String prevhighreadings)
	{
		this.prevhighreadings = prevhighreadings;
	}

	public String getPrevhighreadings() 
	{
		return prevhighreadings;
	}

	public void setPrevflatreadings(String prevflatreadings)
	{
		this.prevflatreadings = prevflatreadings;
	}

	public String getPrevflatreadings() 
	{
		return prevflatreadings;
	}

	public void setPrevlowreadings(String prevlowreadings)
	{
		this.prevlowreadings = prevlowreadings;
	}

	public String getPrevlowreadings() 
	{
		return prevlowreadings;
	}

	public void setCurhighreadings(String curhighreadings)
	{
		this.curhighreadings = curhighreadings;
	}

	public String getCurhighreadings() 
	{
		return curhighreadings;
	}

	public void setCurflatreadings(String curflatreadings)
	{
		this.curflatreadings = curflatreadings;
	}

	public String getCurflatreadings() 
	{
		return curflatreadings;
	}

	public void setCurlowreadings(String curlowreadings)
	{
		this.curlowreadings = curlowreadings;
	}

	public String getCurlowreadings() 
	{
		return curlowreadings;
	}

	public void setCurusedreadings(String curusedreadings)
	{
		this.curusedreadings = curusedreadings;
	}

	public String getCurusedreadings() 
	{
		return curusedreadings;
	}

	public void setTransformerullage(String transformerullage)
	{
		this.transformerullage = transformerullage;
	}

	public String getTransformerullage() 
	{
		return transformerullage;
	}

	public void setUnitpirce(String unitpirce)
	{
		this.unitpirce = unitpirce;
	}

	public String getUnitpirce() 
	{
		return unitpirce;
	}

	public void setCuruserdmoney(String curuserdmoney)
	{
		this.curuserdmoney = curuserdmoney;
	}

	public String getCuruserdmoney() 
	{
		return curuserdmoney;
	}

	public void setUllagemoney(String ullagemoney)
	{
		this.ullagemoney = ullagemoney;
	}

	public String getUllagemoney() 
	{
		return ullagemoney;
	}

	public void setAccountmoney(String accountmoney)
	{
		this.accountmoney = accountmoney;
	}

	public String getAccountmoney() 
	{
		return accountmoney;
	}

	public void setStatus(String status)
	{
		this.status = status;
	}

	public String getStatus() 
	{
		return status;
	}

	public void setPercentHive(String percentHive)
	{
		this.percentHive = percentHive;
	}

	public String getPercentHive() 
	{
		return percentHive;
	}

	public void setProperty(String property)
	{
		this.property = property;
	}

	public String getProperty() 
	{
		return property;
	}


	public void setOrgname(String orgname)
	{
		this.orgname = orgname;
	}

	public String getOrgname() 
	{
		return orgname;
	}

	public void setResstationcode(String resstationcode)
	{
		this.resstationcode = resstationcode;
	}

	public String getResstationcode() 
	{
		return resstationcode;
	}

	public void setMonthNo(String monthNo)
	{
		this.monthNo = monthNo;
	}

	public String getMonthNo() 
	{
		return monthNo;
	}

	public void setEnbid(String enbid)
	{
		this.enbid = enbid;
	}

	public String getEnbid() 
	{
		return enbid;
	}

	public void setEneaddrCode(String eneaddrCode)
	{
		this.eneaddrCode = eneaddrCode;
	}

	public String getEneaddrCode() 
	{
		return eneaddrCode;
	}

	public void setSumSxPrb(String sumSxPrb)
	{
		this.sumSxPrb = sumSxPrb;
	}

	public String getSumSxPrb() 
	{
		return sumSxPrb;
	}

	public void setSumSxPrbQci1(String sumSxPrbQci1)
	{
		this.sumSxPrbQci1 = sumSxPrbQci1;
	}

	public String getSumSxPrbQci1() 
	{
		return sumSxPrbQci1;
	}

	public void setSumXxPrb(String sumXxPrb)
	{
		this.sumXxPrb = sumXxPrb;
	}

	public String getSumXxPrb() 
	{
		return sumXxPrb;
	}

	public void setSumMaxRrc(String sumMaxRrc)
	{
		this.sumMaxRrc = sumMaxRrc;
	}

	public String getSumMaxRrc() 
	{
		return sumMaxRrc;
	}

	public void setSumAvgRrc(String sumAvgRrc)
	{
		this.sumAvgRrc = sumAvgRrc;
	}

	public String getSumAvgRrc() 
	{
		return sumAvgRrc;
	}

	public void setSumSxAvgJh(String sumSxAvgJh)
	{
		this.sumSxAvgJh = sumSxAvgJh;
	}

	public String getSumSxAvgJh() 
	{
		return sumSxAvgJh;
	}

	public void setSumSxAvgJhQci1(String sumSxAvgJhQci1)
	{
		this.sumSxAvgJhQci1 = sumSxAvgJhQci1;
	}

	public String getSumSxAvgJhQci1() 
	{
		return sumSxAvgJhQci1;
	}

	public void setSumXxAvgJh(String sumXxAvgJh)
	{
		this.sumXxAvgJh = sumXxAvgJh;
	}

	public String getSumXxAvgJh() 
	{
		return sumXxAvgJh;
	}

	public void setSumXxAvgJhQci1(String sumXxAvgJhQci1)
	{
		this.sumXxAvgJhQci1 = sumXxAvgJhQci1;
	}

	public String getSumXxAvgJhQci1() 
	{
		return sumXxAvgJhQci1;
	}

	public void setSumSxPdcp(String sumSxPdcp)
	{
		this.sumSxPdcp = sumSxPdcp;
	}

	public String getSumSxPdcp() 
	{
		return sumSxPdcp;
	}

	public void setSumXxPdcp(String sumXxPdcp)
	{
		this.sumXxPdcp = sumXxPdcp;
	}

	public String getSumXxPdcp() 
	{
		return sumXxPdcp;
	}

	public void setSumMaxVlote(String sumMaxVlote)
	{
		this.sumMaxVlote = sumMaxVlote;
	}

	public String getSumMaxVlote() 
	{
		return sumMaxVlote;
	}

	public void setSumVolteTime(String sumVolteTime)
	{
		this.sumVolteTime = sumVolteTime;
	}

	public String getSumVolteTime() 
	{
		return sumVolteTime;
	}

	public void setLatnName(String latnName)
	{
		this.latnName = latnName;
	}

	public String getLatnName() 
	{
		return latnName;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("intputdate", getIntputdate())
            .append("writeoffInstanceCode", getWriteoffInstanceCode())
            .append("electrotype", getElectrotype())
            .append("ammeterid", getAmmeterid())
            .append("company", getCompany())
            .append("stationcode", getStationcode())
            .append("projectname", getProjectname())
            .append("category", getCategory())
            .append("protocolname", getProtocolname())
            .append("substation", getSubstation())
            .append("latnId", getLatnId())
            .append("multtimes", getMulttimes())
            .append("quotareadings", getQuotareadings())
            .append("quotereadingsratio", getQuotereadingsratio())
            .append("accountno", getAccountno())
            .append("busiDate", getBusiDate())
            .append("prevtotalreadings", getPrevtotalreadings())
            .append("curtotalreadings", getCurtotalreadings())
            .append("prevhighreadings", getPrevhighreadings())
            .append("prevflatreadings", getPrevflatreadings())
            .append("prevlowreadings", getPrevlowreadings())
            .append("curhighreadings", getCurhighreadings())
            .append("curflatreadings", getCurflatreadings())
            .append("curlowreadings", getCurlowreadings())
            .append("curusedreadings", getCurusedreadings())
            .append("transformerullage", getTransformerullage())
            .append("unitpirce", getUnitpirce())
            .append("curuserdmoney", getCuruserdmoney())
            .append("ullagemoney", getUllagemoney())
            .append("accountmoney", getAccountmoney())
            .append("status", getStatus())
            .append("percentHive", getPercentHive())
            .append("property", getProperty())
            .append("remark", getRemark())
            .append("orgname", getOrgname())
            .append("resstationcode", getResstationcode())
            .append("monthNo", getMonthNo())
            .append("enbid", getEnbid())
            .append("eneaddrCode", getEneaddrCode())
            .append("sumSxPrb", getSumSxPrb())
            .append("sumSxPrbQci1", getSumSxPrbQci1())
            .append("sumXxPrb", getSumXxPrb())
            .append("sumMaxRrc", getSumMaxRrc())
            .append("sumAvgRrc", getSumAvgRrc())
            .append("sumSxAvgJh", getSumSxAvgJh())
            .append("sumSxAvgJhQci1", getSumSxAvgJhQci1())
            .append("sumXxAvgJh", getSumXxAvgJh())
            .append("sumXxAvgJhQci1", getSumXxAvgJhQci1())
            .append("sumSxPdcp", getSumSxPdcp())
            .append("sumXxPdcp", getSumXxPdcp())
            .append("sumMaxVlote", getSumMaxVlote())
            .append("sumVolteTime", getSumVolteTime())
            .append("latnName", getLatnName())
            .toString();
    }
}
