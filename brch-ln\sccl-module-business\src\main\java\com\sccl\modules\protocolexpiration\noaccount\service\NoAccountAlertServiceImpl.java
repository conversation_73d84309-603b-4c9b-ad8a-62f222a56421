package com.sccl.modules.protocolexpiration.noaccount.service;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.business.account.domain.Account;
import com.sccl.modules.business.account.mapper.AccountMapper;
import com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol;
import com.sccl.modules.business.ammeterorprotocol.mapper.AmmeterorprotocolMapper;
import com.sccl.modules.system.organization.domain.Organization;
import com.sccl.modules.system.organization.mapper.OrganizationMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 台账 服务层实现
 * 
 * <AUTHOR>
 * @date 2019-05-20
 */
@Service
public class NoAccountAlertServiceImpl extends BaseServiceImpl<Ammeterorprotocol> implements INoAccountAlertService
{
    @Autowired
    AmmeterorprotocolMapper ammeterorprotocolMapper;

    @Autowired
    private OrganizationMapper organizationMapper;

    public List<Ammeterorprotocol> selectNoAccountList(Ammeterorprotocol ammeterorprotocol){
       return ammeterorprotocolMapper.selectNoAccountList(ammeterorprotocol);
    }

    public List<Organization> organizationList (String parentcompanyno){
        return organizationMapper.selectSubordinateOrganization(parentcompanyno);
    }
}
