package com.sccl.modules.business.quota.mapper;

import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.quota.domain.QuotaBaseResult;
import com.sccl.modules.business.quota.domain.QuotaRecord;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 定额 数据层
 * 
 * <AUTHOR>
 * @date 2019-05-13
 */
public interface QuotaRecordMapper extends BaseMapper<QuotaRecord>
{
    public List<QuotaRecord> getByQuotaId(QuotaRecord quotaRecord);
    public List<QuotaRecord> getByQuotaDateId(QuotaRecord quotaRecord);
    int deleteByQuotaIdsDB(String[] ids);
    /**
     * 删除大于当前时间并且不是已完成的数据
     * @param params
     * @return
     */
    int deleteByDateDB(Map<String,Object> params);

    /**
     * 通过条件查询记录表最新一条数据
     * @param quotaRecord
     * @return
     */
    public List<QuotaRecord> selectObjectByAmmPro(QuotaRecord quotaRecord);
    String getByCreateTime(Map<String,Object> params);
}

