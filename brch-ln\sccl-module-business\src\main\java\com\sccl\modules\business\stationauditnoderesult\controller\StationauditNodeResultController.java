package com.sccl.modules.business.stationauditnoderesult.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.business.stationauditnoderesult.domain.StationauditNodeResult;
import com.sccl.modules.business.stationauditnoderesult.service.IStationauditNodeResultService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 基站一站式稽核结果 信息操作处理
 * 
 * <AUTHOR>
 * @date 2022-11-16
 */
@RestController
@RequestMapping("/business/stationauditNodeResult")
public class StationauditNodeResultController extends BaseController
{
    private String prefix = "business/stationauditNodeResult";
	
	@Autowired
	private IStationauditNodeResultService stationauditNodeResultService;
	
	@RequiresPermissions("business:stationauditNodeResult:view")
	@GetMapping()
	public String stationauditNodeResult()
	{
	    return prefix + "/stationauditNodeResult";
	}
	
	/**
	 * 查询基站一站式稽核结果列表
	 */
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(StationauditNodeResult stationauditNodeResult)
	{
		startPage();
        List<StationauditNodeResult> list = stationauditNodeResultService.selectList(stationauditNodeResult);
		return getDataTable(list);
	}
	
	/**
	 * 新增基站一站式稽核结果
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存基站一站式稽核结果
	 */
	@RequiresPermissions("business:stationauditNodeResult:add")
	@Log(title = "基站一站式稽核结果", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody StationauditNodeResult stationauditNodeResult)
	{		
		return toAjax(stationauditNodeResultService.insert(stationauditNodeResult));
	}

	/**
	 * 修改基站一站式稽核结果
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		StationauditNodeResult stationauditNodeResult = stationauditNodeResultService.get(id);

		Object object = JSONObject.toJSON(stationauditNodeResult);

        return this.success(object);
	}
	
	/**
	 * 修改保存基站一站式稽核结果
	 */
	@RequiresPermissions("business:stationauditNodeResult:edit")
	@Log(title = "基站一站式稽核结果", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody StationauditNodeResult stationauditNodeResult)
	{		
		return toAjax(stationauditNodeResultService.update(stationauditNodeResult));
	}
	
	/**
	 * 删除基站一站式稽核结果
	 */
	@RequiresPermissions("business:stationauditNodeResult:remove")
	@Log(title = "基站一站式稽核结果", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(stationauditNodeResultService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看基站一站式稽核结果
     */
    @RequiresPermissions("business:stationauditNodeResult:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		StationauditNodeResult stationauditNodeResult = stationauditNodeResultService.get(id);

        Object object = JSONObject.toJSON(stationauditNodeResult);

        return this.success(object);
    }

}
