package com.sccl.modules.business.powerauditstaiongrade.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sccl.modules.business.powerauditstaiongrade.util.StringJoinerSerializer;
import lombok.Data;
import org.nustaq.serialization.annotations.Serialize;
import org.springframework.beans.BeanUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.StringJoiner;

@Data
@Serialize
public class StaionAnalysisPro {
    private String company;
    private String companyName;
    private String country;
    private String countryName;
    /**
     * 时间
     */
    private String evaluationDate;

    @JsonSerialize(using = StringJoinerSerializer.class)
    private StringJoiner evaluationContent = new StringJoiner("\n");


    /**
     * 铁塔稽核相关字段
     */
    private String auditNum;
    private String auditNumOne;
    private String auditNumOneKey;
    private String totalNum;
    private String auditScale;
    private String auditOneScale;

    public static List<StaionAnalysisPro> mergeStaionAnalysisProList(List<StaionAnalysis> staionAnalysisList, List<TowerAudit> auditOneList) {
        HashMap<String, StaionAnalysisPro> map = new HashMap<>();

        for (StaionAnalysis staionAnalysis : staionAnalysisList) {
            String company = staionAnalysis.getCompany();
            String companyname = staionAnalysis.getCompanyname();
            String country = staionAnalysis.getCountry();
            String countryname = staionAnalysis.getCountryname();
            String evaluationDate = staionAnalysis.getEvaluationDate();
            String evaluationContent = staionAnalysis.getEvaluationContent().replaceAll("\"", "");
            String key = String.format("%s-%s-%s", staionAnalysis.getCompany(), staionAnalysis.getCountry(), staionAnalysis.getEvaluationDate());
            if (!map.containsKey(key)) {
                StaionAnalysisPro pro = new StaionAnalysisPro();
                BeanUtils.copyProperties(staionAnalysis, pro);
                pro.setCompanyName(staionAnalysis.getCompanyname());
                pro.setCountryName(staionAnalysis.getCountryname());
                map.put(key, pro);
            }
            StaionAnalysisPro pro = map.get(key);
            setEvaluationContentData(staionAnalysis, pro, evaluationContent);
        }

        for (TowerAudit audit : auditOneList) {
            String company = audit.getCompany();
            String companyName = audit.getCompanyName();
            String country = audit.getCountry();
            String countryName = audit.getCountryName();
            String time = audit.getTime();
            String key = String.format("%s-%s-%s", company, country, time);
            if (!map.containsKey(key)) {
                StaionAnalysisPro pro = new StaionAnalysisPro();
                BeanUtils.copyProperties(audit, pro);
                pro.setEvaluationDate(audit.getTime());
                map.put(key, pro);
            }
            StaionAnalysisPro pro = map.get(key);
            BeanUtils.copyProperties(audit, pro);
            pro.setEvaluationDate(audit.getTime());
        }

        return new ArrayList<>(map.values());
    }


    private static void setEvaluationContentData(StaionAnalysis staionAnalysis, StaionAnalysisPro pro, String evaluationContent) {
        String evaluationContent1 = staionAnalysis.getEvaluationContent();
        String sum = staionAnalysis.getSum();
        StringJoiner joiner = pro.getEvaluationContent();
        String join = String.format("%s——>数量:%s条", evaluationContent1, sum);
        joiner.add(join);
//        switch (evaluationContent) {
//            case "实际功耗或标准功耗为0，无法评级":
//                pro.setEvaluationContent0(evaluationContent);
//                pro.setSum0(staionAnalysis.getSum());
//                pro.setKey0(staionAnalysis.getKey());
//                break;
//            case "L1":
//                pro.setEvaluationContent1(evaluationContent);
//                pro.setSum1(staionAnalysis.getSum());
//                pro.setKey1(staionAnalysis.getKey());
//                break;
//            case "优秀":
//                pro.setEvaluationContent1(evaluationContent);
//                pro.setSum1(staionAnalysis.getSum());
//                pro.setKey1(staionAnalysis.getKey());
//                break;
//            case "L2":
//                pro.setEvaluationContent2(evaluationContent);
//                pro.setSum2(staionAnalysis.getSum());
//                pro.setKey2(staionAnalysis.getKey());
//                break;
//            case "良好":
//                pro.setEvaluationContent2(evaluationContent);
//                pro.setSum2(staionAnalysis.getSum());
//                pro.setKey2(staionAnalysis.getKey());
//                break;
//            case "L3":
//                pro.setEvaluationContent3(evaluationContent);
//                pro.setSum3(staionAnalysis.getSum());
//                pro.setKey3(staionAnalysis.getKey());
//                break;
//            case "一般":
//                pro.setEvaluationContent3(evaluationContent);
//                pro.setSum3(staionAnalysis.getSum());
//                pro.setKey3(staionAnalysis.getKey());
//                break;
//            case "黑名单":
//                pro.setEvaluationContent4(evaluationContent);
//                pro.setSum4(staionAnalysis.getSum());
//                pro.setKey4(staionAnalysis.getKey());
//                break;
//            case "不合格":
//                pro.setEvaluationContent4(evaluationContent);
//                pro.setSum4(staionAnalysis.getSum());
//                pro.setKey4(staionAnalysis.getKey());
//                break;
//        }
    }

    public static void process(StaionAnalysisPro item) {
        setNullFieldsToDefault(item, "不存在");
    }


    public static void setNullFieldsToDefault(Object obj, String defaultValue) {
        Field[] fields = obj.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                if (field.get(obj) == null) {
                    field.set(obj, defaultValue);
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
    }
}
