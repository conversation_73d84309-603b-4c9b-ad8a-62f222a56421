package com.sccl.modules.rental.rentalordercarmodel.mapper;

import com.sccl.modules.rental.rentalordercarmodel.domain.RentalorderCarmodel;
import com.sccl.framework.mapper.BaseMapper;

import java.util.List;

/**
 * 车型数据 数据层
 * 
 * <AUTHOR>
 * @date 2019-08-23
 */
public interface RentalorderCarmodelMapper extends BaseMapper<RentalorderCarmodel>
{


    void deleteByRcoids(String[] toStrArray);

    List<RentalorderCarmodel> selectAndNameByRcoid(Long id);
}