package com.sccl.modules.mssaccount.mssinterface.domain;

import lombok.Data;

@Data
public class WriteoffInfoDb {
    // 报账单主键
    private String otherSystemMainId;

    // 报账单号
    private String writeoffInstanceCode;

    // 同步类型 1/3 新增/作废
    private String type;

    // 挑对模式
    private String pickingMode;

    // 区县名称
    private String countyName;

    // 电表编码（必填）
    private String energyMeterCode;

    // 电表名称（必填）
    private String energyMeterName;

    // 用电开始时间（格式：YYYYMMDD，当type为1和2时必填）
    private String electricityStartDate;

    // 总电量(度)（当type为1和2时必填）
    private String totalQuantityOfElectricity;

    // 合同电价（格式：数值，单价（元），当type为1和2时必填）
    private String contractPrice;

    // 设备耗电量（格式：数值，当type为1和2时必填）
    private String powerConsumption;

    // 本次电费（格式：数值，当type为1和2时必填）
    private String thisElectricityCharge;

    // 本次电量（格式：数值，当type为1和2时必填）
    private String thisQuantityOfElectricity;

    // 用电截止时间（格式：YYYYMMDD，当type为1和2时必填）
    private String electricityEndDate;

    // 回收电量标识
    private String recoveryElectricityFlag;

    // 本次电价（格式：数值，单价（元），当type为1和2时必填）
    private String thisElectricityPrice;

    // 本次电费税金（格式：数值，当type为1和2时必填）
    private String thisElectricityTax;

    // 消息ID
    private String msgId;

    // 同步结果
    private String syncResult;
    private Long billid;

    // 构造方法、Getter和Setter等其他类成员方法省略
}

