package com.sccl.modules.rental.rentalsupplycheckdetal.service;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.rental.rentalsupplycheckdetal.mapper.RentalsupplycheckdetalMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sccl.modules.rental.rentalsupplycheckdetal.domain.Rentalsupplycheckdetal;

import java.util.List;


/**
 * 供应商 检验 服务层实现
 * 
 * <AUTHOR>
 * @date 2019-09-06
 */
@Service
public class RentalsupplycheckdetalServiceImpl extends BaseServiceImpl<Rentalsupplycheckdetal> implements IRentalsupplycheckdetalService
{
    @Autowired
    RentalsupplycheckdetalMapper detalMapper;

    @Override
    public List<Rentalsupplycheckdetal> selectByParams(Rentalsupplycheckdetal rentalsupplycheckdetal) {
        return detalMapper.selectList(rentalsupplycheckdetal);
    }
}
