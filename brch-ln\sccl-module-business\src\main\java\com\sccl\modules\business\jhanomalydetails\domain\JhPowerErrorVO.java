package com.sccl.modules.business.jhanomalydetails.domain;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
public class JhPowerErrorVO {


    /**
     * 异常类型
     * A.	电表与站址关系异常查看,
     * B.   电价合理性,
     * C.   电表站址一致性异常查看
     * D.	台账周期连续性异常查看
     * E.	电表度数连续性异常查看
     * F.	日均电量及台账波动性异常查看
     * G.	共享站分摊比例异常日均电量异常查看
     * H.	独享站分摊比例异常日均电量异常查看
     * I.	台账周期异常查看
     * */
    @NotBlank(message = "异常类型不能为空")
    private String  menu;


    /**
     *台账：tz
     * 报账：bz
     * 通过后缀判断导出列表
     * */
    @NotBlank(message = "稽核类型不能为空")
    private String task;

    /**
    **台账id
     * */
    private String tzId;


    /**
     **报账id
     * */
    private String bzId;

    /**
     **稽核时间
     * */
    @NotBlank(message = "稽核时间不能为空")
    private String auditTime;



}
