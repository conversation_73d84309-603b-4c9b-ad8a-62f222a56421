package com.sccl.modules.business.statinAudit.mapper;

import cn.hutool.core.date.DateTime;
import com.sccl.modules.business.account.domain.AccountQua;
import com.sccl.modules.business.statinAudit.domain.SysOrganizations;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * @Author: 李佳杰
 * @CreateTime: 2024-02-21  16:12
 * @Description: TODO
 * @Version: 1.0
 */
@Mapper
public interface PowerStationInfoMapper {
    @Select("select stationcode as '局站编码',isshare as '是否共享'," +
            "sheredepartname as '共享单位名称',sharenum as '共享单位数量' from power_station_info where id=#{id}")
    /**查询站址信息*/
    Map selectStationCodeById(Long id);


    @Select("SELECT\n"
        + "\t( CASE WHEN pa.category = 1 THEN pa.ammetername ELSE pa.protocolname END ) '电表编号',\n"
        + "\tpa.stationaddresscode '局站编码',\n"
        + "\t(\n"
        + "\tSELECT\n"
        + "\t\tcount( DISTINCT stationcode ) \n"
        + "\tFROM\n"
        + "\t\tpower_ammeterorprotocol \n"
        + "\tWHERE\n"
        + "\t\t( CASE WHEN category = 1 THEN ammetername ELSE protocolname END ) = ( CASE WHEN pa.category = 1 THEN pa.ammetername ELSE pa.protocolname END ) \n"
        + "\t\tAND stationcode IS NOT NULL \n"
        + "\t\tAND stationcode != ''" +
            "AND STATUS = 1 \n"
        + "\t) '一表多站',\n"
        + "\t( SELECT count( DISTINCT ( CASE WHEN category = 1 THEN ammetername ELSE protocolname END )) " +
            "FROM power_ammeterorprotocol WHERE stationcode = pa.stationcode " +
            "AND STATUS = 1) '一站多表' ,\n"
        + "pa.category\n"
        + "FROM\n"
        + "\tpower_ammeterorprotocol pa \n"
        + "WHERE \n"
        + "pa.id = #{ammeterid} AND pa.STATUS = 1;"
    )
    Map selectAccountState(Long ammeterid);



    @Select("SELECT\n" +
            "\t1 \n" +
            "FROM\n" +
            "\tstation_report_whitelist a\n" +
            "WHERE\n" +
            "\ta.type  = '3'\n" +
            "\tAND a.meter_id = #{meterId}\n" +
            "\tAND a.bill_status = '2'")
    /**检查是否在白名单中*/
    List<String> selectPriceAccountWhiteList(@Param("meterId") Long  meterId);

    @Select("SELECT\n" +
            "\t1\n" +
            "FROM\n" +
            "\tstation_report_whitelist a\n" +
            "WHERE\n" +
            "\ta.type IN ('1', '2') \n" +
            "\tAND a.meter_id = #{meterId}\n" +
            "\tAND a.bill_status = '2'")
    /**一站一表/检查是否在白名单中*/
    List<String> selectAmmeterAccountWhiteList(@Param("meterId") Long meterId);

    @Select("select pc.exceptionflag from power_ammeterorprotocol_check pc inner join power_ammeterorprotocol pa on pc.ammeterid = pa.id where pa.ammetername =#{ammetername}")
    /**查询大数据建立同类型站址用电模型库*/
    List<String> selectAllCheckByName(String  ammetername);

    @Select("SELECT case when round( avg(( ps.station4gquantity + ps.station5gquantity )),2 ) is null then 0 else round( avg(( ps.station4gquantity + ps.station5gquantity )),2 ) end  staPower \n"
        + "\tFROM\n"
        + "\t\tpower_station_qua_sta ps \n"
        + "WHERE\n"
        + "\tps.id5gr = #{stationcode} \n"
        + "  and ps.currentdate between #{startdate} and #{enddate}")
    /**查询台账日均电量的波动合理性数据*/
    String selectPowerStationQuaSta(@Param("stationcode") String stationcode,@Param("startdate")String startdate,@Param("enddate")String enddate);


    @Select("select case when sum( ps.station4gquantity + ps.station5gquantity ) is null then 0 else sum( ps.station4gquantity + ps.station5gquantity ) end sumStaPower\n"
        + "FROM power_station_qua_sta ps\n"
        + "WHERE ps.resstationcode = #{stationcode}"
        + "  and ps.currentdate between #{startdate} and #{enddate}")
    /**查询台账合计电量的波动合理性数据*/
    String selectSumPowerStationQuaSta(@Param("stationcode") String stationcode,@Param("startdate")String startdate,@Param("enddate")String enddate);



    @Select("SELECT\n" +
            "  distinct\n" +
            "\tpsiv.stationcodeintid as '集团LTE站址编码',\n" +
            "\tpsiv.stationcodetowercode as '集团LTE铁塔站址编码' \n" +
            "FROM\n" +
            "\tpower_station_info psi\n" +
            "LEFT JOIN\tpower_station_info_validity psiv on \tpsi.stationcodeintid = psiv.stationcodeintid\n" +
            "WHERE\n" +
            "\tpsi.id = #{id}\n" +
            "\tand psiv.stationcodetowercode is not null \n" +
            "\tand psiv.stationcodeintid is not null\n" +
            "\tand psiv.stationcodetowercode != ''\n" +
            "\tand psiv.stationcodeintid != ''\n" +
            "limit 1\t")
    Map selectStationRJtlteBystationId(Long id);

    @Select("select * from rmp.sys_organizations org where id = #{id}")
    /**根据id查询公司*/
    String selectsysOrganizationsById(Long id);

    @Select("select id, org_code, org_name, parent_company_no  from rmp.sys_organizations org where id = #{id}")
    /**根据id查询公司名称*/
    SysOrganizations selectsysOrgNameById(Long id);

    @Select("select pc.exceptionflag from power_ammeterorprotocol_check pc where pc.ammeterid =#{ammeterId}")
    List<String> selectAllCheckById(@Param("ammeterId") Long ammeterId);

    @Select("select stationcodeintid\n" +
            "from power_station_info\n" +
            "where id=#{stationcodeId}")
    String getResstationcode(@Param("stationcodeId") String stationcodeId);
    @Select("select id5gr\n" +
            "from power_station_qua_sta\n" +
            "where resstationcode = (select resstationcode\n" +
            "                        from power_station_info\n" +
            "                        where id = #{stationcodeId})\n" +
            "order by id desc limit 1")
    String getId5gr(@Param("stationcodeId") String stationcodeId);

    @Select("SELECT\n" +
            "\tt.finanperiod,\n" +
            "\tt.quantity,\n" +
            "\tt.startdate,\n" +
            "\tt.enddate,\n" +
            "CASE\n" +
            "\t\t\n" +
            "\t\tWHEN SUBSTRING( t.finanperiod, 5, 1 ) = '0' THEN\n" +
            "\t\tSUBSTRING( t.finanperiod, 6, 1 ) ELSE SUBSTRING( t.finanperiod, 5, 2 ) \n" +
            "\tEND AS dateMonth,\n" +
            "\tavg(\n" +
            "\tt.quantity / ( datediff( t.enddate, t.startdate ) + 1 )) ave \n" +
            "FROM\n" +
            "\t(\n" +
            "\tSELECT\n" +
            "\t\tfinanperiod,\n" +
            "\t\tstationcodeintid,\n" +
            "\t\tquantity,\n" +
            "\t\tstartdate,\n" +
            "\t\tenddate \n" +
            "\tFROM\n" +
            "\t\t`power_station_qua_jt5gr` \n" +
            "WHERE\n" +
            "\tstationcodeintid = #{stationcodeintid}\n" +
            ") t \n" +
            "\tGROUP BY t.enddate desc")
    List<AccountQua> getQuaJt5gr(@Param("beginMonthDate") DateTime beginMonthDate, @Param("endMonthDate") DateTime endMonthDate,
                                 @Param("stationcodeintid") String stationcodeintid);
}
