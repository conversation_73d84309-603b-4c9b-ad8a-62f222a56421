package com.sccl.modules.business.stationinfo.mapper;


import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.account.domain.NhSite;
import com.sccl.modules.business.stationinfo.domain.StationJt5gjz;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 集团LTE网管基站 数据层
 * 
 * <AUTHOR>
 * @date 2021-05-16
 */
public interface StationJt5gjzMapper extends BaseMapper<StationJt5gjz>
{


    NhSite getgetNhsite(@Param("nhsiteid") Long nhsiteid);

    List<StationJt5gjz> selectListLike(StationJt5gjz stationJt5gjz);
}