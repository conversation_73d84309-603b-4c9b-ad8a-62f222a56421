package com.sccl.modules.business.stationaudit.pstationprotocolexpired;

import com.enrising.dcarbon.audit.*;
import com.sccl.common.utils.SpringUtil;
import com.sccl.modules.business.account.domain.Account;
import com.sccl.modules.business.account.mapper.AccountMapper;
import com.sccl.modules.business.auditresult.mapper.AuditResultMapper;
import com.sccl.modules.business.basestation.service.IStationGradeService;

import com.sccl.modules.business.stationaudit.pstationempty.StationEmptyReferee;
import com.sccl.modules.business.stationaudit.pstationempty.StationEmptyRefereeContent;
import com.sccl.modules.business.stationaudit.pstationgrade.StationGradeExReferee;
import com.sccl.modules.business.stationaudit.pstationgrade.StationGradeExRefereeContent;
import com.sccl.modules.business.stationequipment.domain.StationEquipment;
import com.sccl.modules.business.stationequipment.mapper.StationEquipmentMapper;
import com.sccl.modules.business.stationequipment.service.IStationEquipmentService;
import com.sccl.modules.mssaccount.mssaccountbill.mapper.MssAccountbillMapper;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class StationProtocolExpiredCreator extends AbstractRefereeDatasourceCreator {
    @Override
    public Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> collect(Auditable auditable) {
        //数据源
        Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> datasource = new HashMap<>();

        //从Spring上下文取得mapper
        MssAccountbillMapper mapper = SpringUtil.getBean(MssAccountbillMapper.class);
        //可能上下文没有这个mapper
        if (mapper == null) {
            return null;
        }
        Account account = (Account) auditable;
        //根据 台账主键 获取对应的台账 信息
        Long pcid = account.getPcid();
        List<StationProtocolExpiredRefereeContent> nodeResults = mapper.getStationProtocol(pcid);

        //去除null数据
        nodeResults = nodeResults.stream().filter(Objects::nonNull).collect(Collectors.toList());


        //转为auditResult 稽核对象
        List<AuditResult> auditResults = nodeResults.stream().map(
                n -> {
                    //创建 评审内容对象
                    RefereeResult refereeResult = new RefereeResult("电表合同是否过期", true, "成功");
                    n.setRefereeResult(refereeResult);
                    AuditResult auditResult = n.getAuditResult();
                    auditResult.setAuditKey(String.valueOf(n.getPcid()));
                    auditResult.setStep(5);
                    auditResult.setNodeTopic("电表合同");
                    auditResult.setRefereeMessage("电表合同");
                    return auditResult;
                }
        ).collect(Collectors.toList());
        //可以添加多种不同类型的评判数据
        //2添加到数据源
        datasource.put(StationProtocolExpiredRefereeContent.class, new ArrayList<>(auditResults));
        return datasource;
    }

    @Override
    public AbstractReferee getReferee() {
        return new StationProtocolExpiredReferee("电表合同");
    }

}
