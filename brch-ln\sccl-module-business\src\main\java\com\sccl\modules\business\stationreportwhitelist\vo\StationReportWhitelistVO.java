package com.sccl.modules.business.stationreportwhitelist.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sccl.modules.business.stationreportwhitelist.domain.StationReportWhitelist;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @date 2024/4/9 16:23
 * @describe 一站多表白名单
 */
@Getter
@Setter
public class StationReportWhitelistVO extends StationReportWhitelist implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 项目名称
     */
    private String projectname;

    /**
     * 局(站)id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long stationId;

    /**
     * 关联局站数量
     */
    private Integer stationTotal;

    /**
     * 局(站)编码
     */
    private String stationcode;

    /**
     * 关联的局站编码列表
     */
    private String stationcodes;

    /**
     * 局站名称
     */
    private String stationName;

    /**
     * 分公司
     */
    private Long company;

    /**
     * 分公司名称
     */
    private String companyName;

    /**
     * 部门
     */
    private Long country;

    /**
     * 部门名称
     */
    private String countryName;

    /**
     * 用电类型
     * 在power_electric_classification表中
     */
    private Long electrotype;

    /**
     * 用电类型名称
     */
    private String electrotypeName;

    /**
     * 站址产权归属(1自留2铁塔)
     */
    private Integer property;

    /**
     * 流程单据状态名称
     */
    private String billStatusName;

    /**
     * 电表状态
     */
    private Integer status;

    /**
     * 电表状态名称
     */
    private String statusName;

    /**
     * 业务模块编码
     */
    private String busiAlias;

    /**
     * 流程实例id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long procInstId;

    /**
     * 流程单据状态
     */
    private Integer billStatus;

    /**
     * 局(站)类型
     */
    private Integer stationtype;

    /**
     * 局站类型名称
     */
    private String stationtypeName;

    /**
     * 局站关联电表数量
     */
    private Long stationMetersCount;

    /** 是否供电局直供，0：否，1：是  改成对外结算类型 直供电、转供电
     * 在power_category_type表中type_category="directSupplyFlag" */
    private String directsupplyflag;

    /**
     * 对外结算类型名称
     */
    private String directsupplyflagName;

    /**
     * 单价
     */
    private BigDecimal price;

    @Override
    public String toString() {
        return super.toString();
    }

}
