package com.sccl.modules.business.powerauditstaiongrade.controller;

import com.sccl.common.utils.ShiroUtils;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.autojob.util.convert.MessageMaster;
import com.sccl.modules.autojob.util.convert.StringUtils;
import com.sccl.modules.business.equipmentdict.service.IEquipmentDictService;
import com.sccl.modules.business.msg.domain.Message;
import com.sccl.modules.business.powerauditstaiongrade.entity.*;
import com.sccl.modules.business.powerauditstaiongrade.service.PowerAuditStationgradeService;
import com.sccl.modules.business.stationinfo.domain.PowerStationInfoRJtlte;
import com.sccl.modules.system.attachments.domain.Attachments;
import com.sccl.modules.system.role.domain.Role;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.service.IUserService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Slf4j
@RestController
@RequestMapping("/power-audit-stationgrade-entity")
public class PowerAuditStationgradeController extends BaseController {
    private static final Map<String, String> PROMPT_COLUMN_MAP = null;
    private static final Map<String, String> EXPORT_COLUMN_MAP = new HashMap<>();
    private static final Map<String, String> EXPORT_COLUMN_MAPFORSTAIONBILL = new HashMap<>();

    static {
        EXPORT_COLUMN_MAP.put("city", "地市");
        EXPORT_COLUMN_MAP.put("district", "区县");
        EXPORT_COLUMN_MAP.put("towersitecode", "站址编码");
        EXPORT_COLUMN_MAP.put("towersitename", "站址名称");
        EXPORT_COLUMN_MAP.put("nh_persent", "能耗电信分割比例");
        EXPORT_COLUMN_MAP.put("tower_persent", "铁塔电信分割比例");
        EXPORT_COLUMN_MAP.put("diff", "差异百分比%（相对于能耗）");
    }

    static {
        EXPORT_COLUMN_MAPFORSTAIONBILL.put("stationid", "能耗局站id");
        EXPORT_COLUMN_MAPFORSTAIONBILL.put("jtlte_code", "集团LTE站址编码");
        EXPORT_COLUMN_MAPFORSTAIONBILL.put("jtlte_name", "集团LTE站址名称");
        EXPORT_COLUMN_MAPFORSTAIONBILL.put("jtlte_tacode", "集团LTE铁塔站址");
        EXPORT_COLUMN_MAPFORSTAIONBILL.put("jtlte_taname", "集团LTE铁塔站址名称");
        EXPORT_COLUMN_MAPFORSTAIONBILL.put("updatetime", "关联时间");
        EXPORT_COLUMN_MAPFORSTAIONBILL.put("billid", "报账单id");
        EXPORT_COLUMN_MAPFORSTAIONBILL.put("writeoff_instance_code", "财辅单号");
        EXPORT_COLUMN_MAPFORSTAIONBILL.put("budgetsetname", "账期");
        EXPORT_COLUMN_MAPFORSTAIONBILL.put("mark", "备注");
    }


    @Autowired
    private PowerAuditStationgradeService auditStationgradeService;
    @Autowired
    private IUserService userService;
    @Autowired
    private IEquipmentDictService equipmentDictService;

    /**
     * 根据一站式稽核结果获取 站址评级分组
     *
     * @param ecceptionProcess
     * @return
     */
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(PowerAuditStationgradeEntity stationgradeEntity) {

        if (stationgradeEntity == null) {
            setEntityForCompanyAndCountry(stationgradeEntity);
        }
        if (StringUtils.isBlank(stationgradeEntity.getBudget())) {
            String budget = LocalDate.now().minusMonths(1L).format(DateTimeFormatter.ofPattern("yyyyMM"));
            stationgradeEntity.setBudget(budget);
        }
        stationgradeEntity.setBudget(
                stationgradeEntity.getBudget().replace("-", "")
        );
        startPage();
        List<PowerAuditStationgradeEntity> list = auditStationgradeService.selectList(stationgradeEntity);
        return getDataTable(list);
    }

    private void setEntityForCompanyAndCountry(PowerAuditStationgradeEntity stationgradeEntity) {
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("PROVI_") || role.getCode().equals("admin")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
            if (role.getCode().startsWith("SUB_")) {//县能耗费管理员
                isSubAdmin = true;
            }
        }
        //  权限设置
        if (isProAdmin) {

        } else if (isCityAdmin) {
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0)
                stationgradeEntity.setCompany(companies.get(0).getId());
        } else if (isSubAdmin) {
            List<IdNameVO> departments = user.getDepartments();
            if (departments != null && departments.size() > 0)
                stationgradeEntity.setRegion(departments.get(0).getId());
        }
    }

    @RequestMapping("/towerStaionList")
    @ResponseBody
    public TableDataInfo towerStaionList(TowerStationGradeaudit towerStationGrade) {
        List<TowerStationGradeaudit> list = getTowerGradeList(towerStationGrade);
        return getDataTable(list);
    }


    private List<TowerStationGradeaudit> getTowerGradeList(TowerStationGradeaudit towerStationGrade) {
        if (towerStationGrade == null) {
            towerStationGrade = setTowerStationGradeauditForCompanyAncCountry(towerStationGrade);
        }
        startPage();
        List<TowerStationGradeaudit> list = auditStationgradeService.selectListForTowerStaionGrade(towerStationGrade);
        return list;
    }

    private TowerStationGradeaudit setTowerStationGradeauditForCompanyAncCountry(TowerStationGradeaudit towerStationGrade) {
        if (towerStationGrade == null) {
            towerStationGrade = new TowerStationGradeaudit();
        }
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("PROVI_") || role.getCode().equals("admin")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
            if (role.getCode().startsWith("SUB_")) {//县能耗费管理员
                isSubAdmin = true;
            }
        }
        //  权限设置
        if (isProAdmin) {

        } else if (isCityAdmin) {
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0)
                towerStationGrade.setCompany(companies.get(0).getId());
        } else if (isSubAdmin) {
            List<IdNameVO> departments = user.getDepartments();
            if (departments != null && departments.size() > 0)
                towerStationGrade.setCountry(departments.get(0).getId());
        }
        return towerStationGrade;
    }

    /**
     * 站址分摊比例导出
     *
     * @param towerStationGrade
     * @param latest
     * @return
     */
    @RequestMapping(value = "export_excel")
    public String exportExcel(TowerStationGradeaudit towerStationGrade) {
        if (towerStationGrade == null) {
            towerStationGrade = new TowerStationGradeaudit();
        }
        List<TowerStationGradeaudit> towerStationGradeList = getTowerGradeList(towerStationGrade);
        if (CollectionUtils.isEmpty(towerStationGradeList)) {
            return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "指定条件下无匹配数据");
        }

        //Attachments attachments = equipmentDictService.exportExcel(towerStationGradeList, EXPORT_COLUMN_MAP,
        //                                                           PROMPT_COLUMN_MAP);

        Attachments attachments = auditStationgradeService.exportExcel(towerStationGradeList, EXPORT_COLUMN_MAP,
                PROMPT_COLUMN_MAP);
        if (attachments == null) {
            return MessageMaster.DefaultMessage.SYSTEM_ERROR.toString();
        }
        return MessageMaster.getMessage(MessageMaster.Code.OK, "上传成功", attachments, true, false);
    }

    /**
     * 站址对应报账单查询
     *
     * @param rJtlte
     * @return
     */
    @RequestMapping("/stationMappingBill")
    @ResponseBody
    public TableDataInfo stationMappingBill(PowerStationInfoRJtlte rJtlte) {
        List<StaionMapBill> list = getStationMappingBillList(rJtlte);
        return getDataTable(list);
    }

    public static void main(String[] args) {
        LocalDate localDate = LocalDate.now().minusMonths(1L);
        String lastMonth = localDate.format(DateTimeFormatter.ofPattern("yyyyMM"));
        log.info("{}", lastMonth);
    }

    private List<StaionMapBill> getStationMappingBillList(PowerStationInfoRJtlte rJtlte) {
        String budget = rJtlte.getBudget();
        if (StringUtils.isBlank(budget)) {
            LocalDate localDate = LocalDate.now().minusMonths(1L);
            String lastMonth = localDate.format(DateTimeFormatter.ofPattern("yyyyMM"));
            rJtlte.setBudget(lastMonth);
        }
        rJtlte.setBudget(rJtlte.getBudget().replace("-", ""));
        startPage();
        List<StaionMapBill> list = auditStationgradeService.selectListForStationMapBill(rJtlte);
        return list;
    }

    private PowerStationInfoRJtlte setRjtlteCompanyAndCountry(PowerStationInfoRJtlte rJtlte) {
        if (rJtlte == null) {
            rJtlte = new PowerStationInfoRJtlte();
        }
        if (rJtlte.getPageNum() == 0 || rJtlte.getPageSize() == 0) {
            rJtlte.setPageNum(1);
            rJtlte.setPageSize(10);
        }
        rJtlte.setOffset((rJtlte.getPageNum() - 1) * rJtlte.getPageSize());
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("PROVI_") || role.getCode().equals("admin")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
            if (role.getCode().startsWith("SUB_")) {//县能耗费管理员
                isSubAdmin = true;
            }
        }
        //  权限设置
        if (isProAdmin) {

        } else if (isCityAdmin) {
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0) {
                rJtlte.setCompany(companies.get(0).getId());
            }
        } else if (isSubAdmin) {
            List<IdNameVO> departments = user.getDepartments();
            if (departments != null && departments.size() > 0)
                rJtlte.setCountry(departments.get(0).getId());
        }
        return rJtlte;
    }

    private List<TowerResultSummaryPro> getTowerResultSummaryList(Message message) {

        if (message == null) {
            setMessageCompanyAndCountry(message);
        }
        if (StringUtils.isBlank(message.getTime())) {
            String currdata = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
            message.setTime(currdata);
        }
        List<TowerResultSummary> list = auditStationgradeService.selecTowerResultSummaryList(message);
        //list.forEach(
        //  item -> {
        //      item.setScale(item.getScale() + "%");
        //  }
        //);
        List<TowerResultSummaryPro> pros = TowerResultSummaryPro.mergeTowerResultSummaries(list);
        return pros;
    }

    private List<TowerResultSummary> getTowerResultSummary(Message message) {

        if (message == null) {
            setMessageCompanyAndCountry(message);
        }
        if (StringUtils.isBlank(message.getTime())) {
            String currdata = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
            message.setTime(currdata);
        }
        List<TowerResultSummary> list = auditStationgradeService.selecTowerResultSummary(message);
        list.forEach(
                item -> {
                    item.setTowerelectronScale(
                            Optional.ofNullable(item.getTowerelectronScale()).orElseGet(() -> "0")
                                    +
                                    "%"
                    );
                    item.setTotalbillScale(
                            Optional.ofNullable(item.getTotalbillScale()).orElseGet(() -> "0")
                                    +
                                    "%");
                }
        );
        return list;
    }

    private void setMessageCompanyAndCountry(Message message) {

        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("PROVI_") || role.getCode().equals("admin")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
            if (role.getCode().startsWith("SUB_")) {//县能耗费管理员
                isSubAdmin = true;
            }
        }
        //  权限设置
        if (isProAdmin) {

        } else if (isCityAdmin) {
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0) {
                message.setCompany(Long.valueOf(companies.get(0).getId()));
            }
        } else if (isSubAdmin) {
            List<IdNameVO> departments = user.getDepartments();
            if (departments != null && departments.size() > 0)
                message.setCountry(Long.valueOf(departments.get(0).getId()));
        }
    }

    private List<TowerAuditPro> getTowerAuditList(Message message) {
        if (message == null) {
            message = setMessageForcompanyAndCountryw(message);
        }
        if (StringUtils.isBlank(message.getTime())) {
            String currdata = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
            message.setTime(currdata);
        }
        List<TowerAuditPro> list = auditStationgradeService.selecTowerAuditList(message);

        return list;
    }

    private List<TowerAudit> getTowerAuditListPro(Message message) {
        if (message == null) {
            message = setMessageForcompanyAndCountryw(message);
        }
        if (StringUtils.isBlank(message.getTime())) {
            String currdata = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
            message.setTime(currdata);
        }
        List<TowerAudit> list = auditStationgradeService.selecTowerAuditListPro(message);

        return list;
    }

    @NotNull
    private Message setMessageForcompanyAndCountryw(Message message) {
        if (message == null) {
            message = new Message();
        }

        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("PROVI_") || role.getCode().equals("admin")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
            if (role.getCode().startsWith("SUB_")) {//县能耗费管理员
                isSubAdmin = true;
            }
        }
        //  权限设置
        if (isProAdmin) {

        } else if (isCityAdmin) {
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0) {
                message.setCompany(Long.valueOf(companies.get(0).getId()));
            }
        } else if (isSubAdmin) {
            List<IdNameVO> departments = user.getDepartments();
            if (departments != null && departments.size() > 0)
                message.setCountry(Long.valueOf(departments.get(0).getId()));
        }
        return message;
    }

    private List<StaionAnalysisPro> getStaionAnalysis(StaionAnalysis message) {
        if (message == null) {
            message = setMessageCompanyAndCountry3(message);
        }
        if (StringUtils.isBlank(message.getEvaluationDate())) {
            String currdata = LocalDate.now().minusMonths(1L).format(DateTimeFormatter.ofPattern("yyyy-MM"));
            message.setEvaluationDate(currdata);
        }
        List<StaionAnalysisPro> list = auditStationgradeService.selectStaionAnalysisList(message);

        return list;
    }

    private List<ExceptionAnalysis> getExceptionAnalysis(StaionAnalysis message) {
        if (message == null) {
            message = setMessageCompanyAndCountry3(message);
        }
        List<ExceptionAnalysis> list = auditStationgradeService.selectExceptionAnalysis(message);
        return list;
    }

    @NotNull
    private StaionAnalysis setMessageCompanyAndCountry3(StaionAnalysis message) {
        if (message == null) {
            message = new StaionAnalysis();
        }
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("PROVI_") || role.getCode().equals("admin")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("LOCALNET_")) {//市能耗费管理员
                isCityAdmin = true;
            }
            if (role.getCode().startsWith("SUB_")) {//县能耗费管理员
                isSubAdmin = true;
            }
        }
        //  权限设置
        if (isProAdmin) {

        } else if (isCityAdmin) {
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0) {
                message.setCompany(companies.get(0).getId());
            }
        } else if (isSubAdmin) {
            List<IdNameVO> departments = user.getDepartments();
            if (departments != null && departments.size() > 0)
                message.setCountry(departments.get(0).getId());
        }
        return message;
    }

    @RequestMapping(value = "exportExcelForStaionMapBill")
    public String exportExcelForStaionMapBill(PowerStationInfoRJtlte rJtlte) {
        if (rJtlte == null) {
            new PowerStationInfoRJtlte();
        }
        List<StaionMapBill> rJtltes = getStationMappingBillList(rJtlte);
        if (CollectionUtils.isEmpty(rJtltes)) {
            return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "指定条件下无匹配数据");
        }


        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        Attachments attachments = auditStationgradeService.exportExcelForStaionMapBill(rJtltes, EXPORT_COLUMN_MAPFORSTAIONBILL,
                PROMPT_COLUMN_MAP);
        if (attachments == null) {
            return MessageMaster.DefaultMessage.SYSTEM_ERROR.toString();
        }
        return MessageMaster.getMessage(MessageMaster.Code.OK, "上传成功", attachments, true, false);
    }


    /**
     * 铁塔电子化对账比例
     *
     * @param id
     * @return
     * @throws Exception
     */
    @GetMapping("/syncIncrementalMeter/towerResultTop1")
    @ResponseBody
    public TableDataInfo towerResultTop1(Message message) throws Exception {
        List<TowerResultSummary> list = getTowerResultSummary(message);
        return getDataTable(list);
    }

    /**
     * 铁塔电子化对账 一次性稽核通过比例
     *
     * @param
     * @return
     * @throws Exception
     */
    @GetMapping("/syncIncrementalMeter/towerResultTop2Pro")
    @ResponseBody
    public TableDataInfo towerResultTop2(Message message) throws Exception {
        List<TowerAuditPro> list = getTowerAuditList(message);
        return getDataTable(list);
    }

    /**
     * 铁塔电子化对账 一次性稽核通过比例pro
     *
     * @param
     * @return
     * @throws Exception
     */
    @GetMapping("/syncIncrementalMeter/towerResultTop2")
    @ResponseBody
    public TableDataInfo towerResultTop2Pro(Message message) throws Exception {
        List<TowerAudit> list = getTowerAuditListPro(message);
        return getDataTable(list);
    }


    @GetMapping("/syncIncrementalMeter/staionAnalysis")
    @ResponseBody
    public TableDataInfo staionAnalysis(StaionAnalysis message) throws Exception {
        List<StaionAnalysisPro> list = getStaionAnalysis(message);
        return getDataTable(list);
    }

    /**
     * 异常待办统计 （回复角度)
     *
     * @param message
     * @return
     * @throws Exception
     */
    @GetMapping("/syncIncrementalMeter/ExceptionAnalysis")
    @ResponseBody
    public TableDataInfo ExceptionAnalysis(StaionAnalysis message) throws Exception {
        List<ExceptionAnalysis> list = getExceptionAnalysis(message);
        return getDataTable(list);
    }

    /**
     * 基站综合评估明细
     *
     * @param detailKey
     * @return
     * @throws Exception
     */
    @GetMapping("/syncIncrementalMeter/staionAnalysis/detail")
    @ResponseBody
    public TableDataInfo staionAnalysisDetail(@RequestParam("key") String detailKey) throws Exception {
        List<StaionAnalysisDetail> list = getStaionAnalysisDetail(detailKey);
        return getDataTable(list);
    }


    private List<StaionAnalysisDetail> getStaionAnalysisDetail(String detailKey) {
        String[] keyArray = detailKey.split("-");
        String keyFlag = keyArray[0];
        String keyCompany = keyArray[1];
        String keyCountry = keyArray[2];
        String keyTimeYear = keyArray[3];
        String keyTimeMonth = keyArray[4];
        String keyEvaluationContent = null;
        if (keyArray.length == 6) {
            keyEvaluationContent = keyArray[5];
        }
        String keyTime = keyTimeYear + "-" + keyTimeMonth;
        StaionAnalysis keyOb = StaionAnalysis.createKeyOb(keyCompany, keyCountry, keyTime, keyEvaluationContent);
        if (keyFlag.equals("msgaudit")) {
            startPage();
            return auditStationgradeService.getStaionAnalysisDetailForAudit(keyOb);
        } else if (keyFlag.equals("stationgrade")) {
            startPage();
            return auditStationgradeService.getStaionAnalysisDetailForGrade(keyOb);
        }
        return null;
    }


    /**
     * 按月生成 power_ammeter_price；
     * 默认清空数据，重新生成当月数据
     *
     * @param detailKey
     * @return
     * @throws Exception
     */
    @GetMapping("/syncIncrementalMeter/generatPrice")
    @ResponseBody
    public AjaxResult generatPrice(@RequestParam("month") String month) throws Exception {
        String result = auditStationgradeService.generatPrice(month);
        return AjaxResult.success(result);
    }

}
