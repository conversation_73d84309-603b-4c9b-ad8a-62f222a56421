package com.sccl.modules.business.stationauditnoderesult.service;

import com.enrising.dcarbon.audit.RefereeDatasource;
import com.enrising.dcarbon.audit.RefereeResult;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.business.stationaudit.msshistory.HistoryResult;
import com.sccl.modules.business.stationauditnoderesult.domain.StationauditNodeResult;
import com.sccl.modules.business.stationauditnoderesult.mapper.StationauditNodeResultMapper;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 基站一站式稽核结果 服务层实现
 *
 * <AUTHOR>
 * @date 2022-11-16
 */
@Service
@Slf4j
public class StationauditNodeResultServiceImpl extends BaseServiceImpl<StationauditNodeResult> implements IStationauditNodeResultService {
    @Autowired(required = false)
    StationauditNodeResultMapper mapper;

    @Override
    public int insertListResults(List<RefereeResult> refereeResults) {
        if (refereeResults == null || refereeResults.size() == 0) {
            log.info("该责任链没有评判结果");
            return 0;
        }
        List<HistoryResult> historyResults = refereeResults.stream().map(
                refereeResult -> {
                    return ((HistoryResult) refereeResult);
                }
        ).collect(Collectors.toList());
        //合并流
        List<RefereeDatasource> collect = historyResults.stream().flatMap(
                historyResult -> {
                    return historyResult.getList().stream();
                }
        ).collect(Collectors.toList());

        //构造默认数据
        LocalDateTime auditTime = LocalDateTime.now();
        collect.stream().filter(s->s!=null).forEach(
                refereeDatasource -> {
                    StationauditNodeResult nodeResult = (StationauditNodeResult) refereeDatasource;
                    nodeResult.setBillId(nodeResult.getBillId());

                    nodeResult.setAuditTime(auditTime);
                }
        );
        //插入
        int n = mapper.insertListResults(collect);
        return n;
    }
}
