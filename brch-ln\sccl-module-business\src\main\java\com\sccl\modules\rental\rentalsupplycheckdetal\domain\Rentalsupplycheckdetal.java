package com.sccl.modules.rental.rentalsupplycheckdetal.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;

import java.util.Date;


/**
 * 供应商 检验表 rentalsupplycheckdetal
 *
 * <AUTHOR>
 * @date 2019-09-06
 */
public class Rentalsupplycheckdetal extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long rsdid;

    public Long getRsdid() {
        return rsdid;
    }

    public void setRsdid(Long rsdid) {
        this.rsdid = rsdid;
    }

    /**
     * mainid
     */
    private Long remid;
    /**
     * checkid
     */
    private Long checkid;
    /**
     * 1市州/2省公司 考评单位
     */
    private Long targettype;
    /**
     * 考核指标
     */
    private String target;
    /**
     * 考核标准
     */
    private String targetdetail;
    /**
     * 计分规则
     */
    private String targetrule;
    /**
     * 权重
     */
    private Long targetweight;
    /**
     * 项目评分
     */
    private Long targetvalue;
    /**
     * 扣分说明
     */
    private String targetmemo;
    /**
     * 考核模版id
     */
    private Long targetid;
    /**
     *
     */
    private String status;
    /**
     *
     */
    private Date inputdate;
    /**
     *
     */
    private String supplyid;


    public void setRemid(Long remid) {
        this.remid = remid;
    }

    public Long getRemid() {
        return remid;
    }

    public void setCheckid(Long checkid) {
        this.checkid = checkid;
    }

    public Long getCheckid() {
        return checkid;
    }

    public void setTargettype(Long targettype) {
        this.targettype = targettype;
    }

    public Long getTargettype() {
        return targettype;
    }

    public void setTarget(String target) {
        this.target = target;
    }

    public String getTarget() {
        return target;
    }

    public void setTargetdetail(String targetdetail) {
        this.targetdetail = targetdetail;
    }

    public String getTargetdetail() {
        return targetdetail;
    }

    public void setTargetrule(String targetrule) {
        this.targetrule = targetrule;
    }

    public String getTargetrule() {
        return targetrule;
    }

    public void setTargetweight(Long targetweight) {
        this.targetweight = targetweight;
    }

    public Long getTargetweight() {
        return targetweight;
    }

    public void setTargetvalue(Long targetvalue) {
        this.targetvalue = targetvalue;
    }

    public Long getTargetvalue() {
        return targetvalue;
    }

    public void setTargetmemo(String targetmemo) {
        this.targetmemo = targetmemo;
    }

    public String getTargetmemo() {
        return targetmemo;
    }

    public void setTargetid(Long targetid) {
        this.targetid = targetid;
    }

    public Long getTargetid() {
        return targetid;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setInputdate(Date inputdate) {
        this.inputdate = inputdate;
    }

    public Date getInputdate() {
        return inputdate;
    }

    public void setSupplyid(String supplyid) {
        this.supplyid = supplyid;
    }

    public String getSupplyid() {
        return supplyid;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("rsdid", getRsdid())
                .append("remid", getRemid())
                .append("checkid", getCheckid())
                .append("targettype", getTargettype())
                .append("target", getTarget())
                .append("targetdetail", getTargetdetail())
                .append("targetrule", getTargetrule())
                .append("targetweight", getTargetweight())
                .append("targetvalue", getTargetvalue())
                .append("targetmemo", getTargetmemo())
                .append("targetid", getTargetid())
                .append("status", getStatus())
                .append("inputdate", getInputdate())
                .append("supplyid", getSupplyid())
                .toString();
    }
}
