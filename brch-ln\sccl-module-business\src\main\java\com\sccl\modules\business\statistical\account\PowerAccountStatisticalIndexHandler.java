package com.sccl.modules.business.statistical.account;

import com.sccl.modules.business.statistical.framework.AbstractStatisticalIndexGroupHandler;

/**
 * 台账相关指标处理器
 *
 * <AUTHOR>
 * @date 2022-12-05 10:41
 * @email <EMAIL>
 */
public class PowerAccountStatisticalIndexHandler extends AbstractStatisticalIndexGroupHandler {
    private final Long company;

    public PowerAccountStatisticalIndexHandler(Long company) {
        this.company = company;
    }

    @Override
    public String getGroupName() {
        return "台账指标统计";
    }

    @Override
    public String getOwnerAs() {
        return company == null ? "全省" : company + "";
    }
}
