package com.sccl.modules.business.statistical.framework.mapper;

import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.statistical.domain.StatisticalEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 统计指标 数据层
 *
 * <AUTHOR>
 * @date 2022-10-25
 */
@Mapper
public interface StatisticalMapper extends BaseMapper<StatisticalEntity> {
    Long selectGroupId(@Param("groupName") String groupName);

    int updateAll(@Param("entity") StatisticalEntity model, @Param("ids") List<Long> ids);
}