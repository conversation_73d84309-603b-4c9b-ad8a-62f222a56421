package com.sccl.modules.business.timing.controller;

import com.sccl.modules.autojob.util.convert.MessageMaster;
import com.sccl.modules.autojob.util.convert.StringUtils;
import com.sccl.modules.business.timing.dto.EnergyQuantity;
import com.sccl.modules.business.timing.dto.STAEnergyConsumptionIndex;
import com.sccl.timing.finder.framework.*;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-03-23 21:14
 * @email <EMAIL>
 */
@RestController
@RequestMapping("/timing_db")
public class TimingController {
    private final TimeSeriesDataFinder<STAEnergyConsumptionIndex> finder = new TimeSeriesDataFinder<>("sta");

    @GetMapping(value = "/index", produces = "application/json;charset=UTF-8")
    public String index(@RequestParam(value = "KEY") String key, @RequestParam("DATE") String dateString, @RequestParam(value = "APPLICATION_NAME", required = false) String applicationName, @RequestParam(value = "USE_PATTERN", required = false) Boolean usePattern) {
        if (StringUtils.isEmpty(key) || StringUtils.isEmpty(dateString)) {
            return MessageMaster.DefaultMessage.EMPTY_PARAMS.toString();
        }
        if (usePattern != null && usePattern) {
            return MessageMaster.getMessage(MessageMaster.Code.OK, "查询成功", finder.indexPattern(key, dateString, EnergyQuantity.class));
        }
        return MessageMaster.getMessage(MessageMaster.Code.OK, "查询成功", finder.index(key, dateString, EnergyQuantity.class, true));
    }

    @GetMapping(value = "/range", produces = "application/json;charset=UTF-8")
    public String range(@RequestParam("KEY") String key, @RequestParam("START") String start, @RequestParam("END") String end, @RequestParam(value = "APPLICATION_NAME", required = false) String applicationName, @RequestParam(value = "USE_PATTERN", required = false) Boolean usePattern) {
        if (StringUtils.isEmpty(key) || StringUtils.isEmpty(start) || StringUtils.isEmpty(end)) {
            return MessageMaster.DefaultMessage.EMPTY_PARAMS.toString();
        }
        if (usePattern != null && usePattern) {
            return MessageMaster.getMessage(MessageMaster.Code.OK, "查询成功", finder.rangePattern(key, start, end, EnergyQuantity.class));
        }
        List<? extends CacheData> res = finder.range(key, start, end, true, EnergyQuantity.class);
        MessageMaster messageMaster = new MessageMaster();
        messageMaster.setCode(MessageMaster.Code.OK);
        messageMaster.setMessage("查询成功");
        messageMaster.insertNewMessage("totalNum", res.size());
        messageMaster.setData(res);
        return messageMaster.toString();
    }

}
