package com.sccl.modules.business.pylonlnbg.controller;

import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.common.exception.base.BaseException;
import com.sccl.framework.utils.ExcelUtil;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.account.domain.AccountAmountStationInfo;
import com.sccl.modules.business.account.domain.AccountBaseResult;
import com.sccl.modules.business.account.domain.AccountCondition;
import com.sccl.modules.business.pylonlnbg.service.IPylonlnbgService;
import com.sccl.modules.system.attachments.domain.UpLoadFile;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

/**
 * __辽宁铁塔包干__<br/>
 * 2019/12/25
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/business/pylonlnbg")
public class PylonlnbgController extends BaseController {

    private String prefix = "business/pylonlnbg";

    @RequiresPermissions("business:pylonlnbg:view")
    @GetMapping()
    public String account() {
        return prefix + "/pylonlnbg";
    }

    @Autowired
    IPylonlnbgService pylonlnbgService;

    /**
     * @Description: 铁塔包干列表
     * @author: dongk
     * @date: 2019/7/18
     * @param:
     * @return:
     */
    @RequiresPermissions("business:pylonlnbg:list")
    @RequestMapping("/selectByParams")
    @ResponseBody
    public TableDataInfo selectByParams(AccountCondition condition) {
        startPage();
        List<AccountBaseResult> list = pylonlnbgService.selectByParams(condition);
        return getDataTable(list);

    }

    /**
     * @Description: 铁塔包干台账总合计
     * @author: dongk
     * @date: 2019/7/23
     * @param:
     * @return:
     */
    @RequestMapping("/accountTotal")
    @ResponseBody
    public AjaxResult accountTotal(@RequestBody AccountCondition condition) {
        AccountBaseResult result = pylonlnbgService.accountTotal(condition);
        if (result != null) {
            return this.success(result);
        } else {
            return this.success(new AccountBaseResult());
        }
    }

    /**
     * @Description: 包干查询功能列表
     * @author: dongk
     * @date: 2019/7/24
     * @param:
     * @return:
     */
    @RequiresPermissions("business:pylonlnbgquery:list")
    @RequestMapping("/selectQuery")
    @ResponseBody
    public TableDataInfo selectQuery(AccountCondition condition) {
        startPage();
        List<AccountBaseResult> list = pylonlnbgService.selectQuery(condition);
        return getDataTable(list);

    }

    /**
     * @Description: 铁塔包干台账查询合计
     * @author: dongk
     * @date: 2019/7/25
     * @param:
     * @return:
     */
    @RequestMapping("/queryTotal")
    @ResponseBody
    public AjaxResult queryTotal(@RequestBody AccountCondition condition) {
        AccountBaseResult result = pylonlnbgService.queryTotal(condition);
        if (result != null) {
            return this.success(result);
        } else {
            return this.success(new AccountBaseResult());
        }
    }

    @RequestMapping("/selectIdsByParams")
    @ResponseBody
    public Map<String, Object> selectIdsByParams(@RequestBody AccountCondition condition) {
        return pylonlnbgService.selectIdsByParams(condition);

    }

    @RequestMapping(value = "/uploadExcel")
    @ResponseBody
    public Map<String, Object> uploadExcel(HttpServletRequest request, HttpServletResponse response, UpLoadFile uploadFile)
            throws Exception {
        response.setContentType("text/html;charset=utf-8");
        Map<String, Object> map = new HashMap<>();
        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        Iterator<String> iterator = multiRequest.getFileNames();

        String version = ServletRequestUtils.getStringParameter(request, "version", "");

        while (iterator.hasNext()) {
            String name = iterator.next();
            List<MultipartFile> files = new LinkedList<>();
            files = multiRequest.getFiles(name);
            // 一次遍历所有文件
            for (MultipartFile file : files) {
                if (file == null) {
                    continue;
                }

                //如果文件大小为0则不上传
                long fileSize = file.getSize();
                if (fileSize <= 0L) {
                    throw new Exception("文件名称：【" + file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("\\") + 1, file.getOriginalFilename().length()) + "】的文件大小为0,请核对!");
                }
                Workbook workbook = WorkbookFactory.create(file.getInputStream());
                Sheet sheet = workbook.getSheet("sheet1");
                if (sheet == null) {
                    sheet = workbook.getSheetAt(0);
                }
                int rows = sheet.getPhysicalNumberOfRows();
                if (rows > 1) {
                    StringBuilder errors = new StringBuilder();
                    List<AccountBaseResult> list = new ArrayList<>();
                    for (int i = 1; i < rows; i++) {
                        try {
                            AccountBaseResult a = new AccountBaseResult();
                            // 从第2行开始取数据,默认第一行是表头.
                            Row row = sheet.getRow(i);
                            if (row.getCell(0) != null) {
                                a.setAmmeterid(Long.valueOf(row.getCell(0).getStringCellValue()));
                                a.setProjectname(row.getCell(1).getStringCellValue());
                                a.setSupplybureauammetercode(row.getCell(2).getStringCellValue());
                                a.setStationName(row.getCell(3).getStringCellValue());
                                a.setElectrotypename(row.getCell(4).getStringCellValue());
                                a.setStationaddresscode(row.getCell(5).getStringCellValue());
                                row.getCell(6).setCellType(HSSFCell.CELL_TYPE_STRING);
                                row.getCell(7).setCellType(HSSFCell.CELL_TYPE_STRING);
                                row.getCell(8).setCellType(HSSFCell.CELL_TYPE_STRING);
                                a.setAccountno(row.getCell(6).getStringCellValue());
                                a.setStartdate(row.getCell(7).getStringCellValue());
                                a.setEnddate(row.getCell(8).getStringCellValue());
                                a.setCurusedreadings(BigDecimal.valueOf(row.getCell(9).getNumericCellValue()));
                                a.setUnitpirce(BigDecimal.valueOf(row.getCell(10).getNumericCellValue()));
                                a.setInputtaxticketmoney(BigDecimal.valueOf(row.getCell(11).getNumericCellValue()));
                                a.setTaxticketmoney(BigDecimal.valueOf(row.getCell(11).getNumericCellValue()));
                                a.setUllagemoney(BigDecimal.valueOf(row.getCell(12).getNumericCellValue()));
                                a.setTaxrate(BigDecimal.valueOf(row.getCell(13).getNumericCellValue()));
                                a.setTaxamount(BigDecimal.valueOf(row.getCell(14).getNumericCellValue()));
                                a.setAccountmoney(BigDecimal.valueOf(row.getCell(15).getNumericCellValue()));
                                list.add(a);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
//                            throw new BaseException("第" + (i + 1) + "行导入数据异常!!!");
                        }
                    }
                    map = pylonlnbgService.dataVerification(list, version);
                }
            }
        }

        return map;
    }

    @RequestMapping("/exportbg")
    @ResponseBody
    public void exportbg(HttpServletResponse response, AccountCondition condition) throws IOException {
        startPage();
        List<AccountBaseResult> list = pylonlnbgService.selectByParams(condition);
        XSSFWorkbook wb = pylonlnbgService.exportbg(list, condition.getVersion());
        String filename = "自有台账导出数据.xlsx";
//		OutputStream os = new FileOutputStream("E:\\导出测试.xlsx");
//		wb.write(os);
        ExcelUtil.export(response, wb, filename);
    }
}
