package com.sccl.modules.business.powerammeterprice.mapper;

import com.sccl.modules.business.powerammeterprice.domain.PowerAmmeterPrice;
import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.mssaccount.mssinterface.domain.PowerElePriceItem;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 电价上传 数据层
 * 
 * <AUTHOR>
 * @date 2022-09-18
 */
public interface PowerAmmeterPriceMapper extends BaseMapper<PowerAmmeterPrice>
{
    public int countPrice(PowerAmmeterPrice powerAmmeterPrice);
    public List<PowerElePriceItem> selectListByIdsSc(String[] ids);
    public List<PowerElePriceItem> selectListByIdsLn(String[] ids);
    public List<PowerElePriceItem> selectListallLn(String ids);
    public List<PowerElePriceItem> selectListallSc(String ids);
    public List<PowerAmmeterPrice> selectListapByIds(String[] ids);
    public List<PowerAmmeterPrice> selectListta(PowerAmmeterPrice powerAmmeterPrice);
    public int countPricestatus(PowerAmmeterPrice powerAmmeterPrice);
    public int countPricetastatus(PowerAmmeterPrice powerAmmeterPrice);
    public  void initPrice(PowerAmmeterPrice powerAmmeterPrice);
    public  void initPriceta(PowerAmmeterPrice powerAmmeterPrice);

    void createPrice(@Param("year") int year, @Param("month") int month);
    void createPrice2(@Param("year") int year, @Param("month") String month);


}