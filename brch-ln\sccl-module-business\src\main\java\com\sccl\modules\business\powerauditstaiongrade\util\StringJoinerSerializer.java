package com.sccl.modules.business.powerauditstaiongrade.util;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.util.StringJoiner;

public class StringJoinerSerializer extends JsonSerializer<StringJoiner> {

    @Override
    public void serialize(StringJoiner stringJoiner, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        jsonGenerator.writeString(stringJoiner.toString());
    }
}

