package com.sccl.modules.business.modelegetprice.service;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.business.modelegetprice.domain.ModeleGetprice;
import com.sccl.modules.business.modelegetprice.mapper.ModeleGetpriceMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 来自官网的单价数据 服务层实现
 *
 * <AUTHOR>
 * @date 2023-03-03
 */
@Service
public class ModeleGetpriceServiceImpl extends BaseServiceImpl<ModeleGetprice> implements IModeleGetpriceService {
    @Autowired
    ModeleGetpriceMapper modeleGetpriceMapper;

    @Override
    public ModeleGetprice selectByLatest(ModeleGetprice modeleGetprice) {
        return modeleGetpriceMapper.selectByLatest(modeleGetprice);

    }

    @Override
    public int updatebitch(List<ModeleGetprice> modeleGetprices) {

        int n = modeleGetpriceMapper.updatebitch(modeleGetprices);
        return n;
    }
}
