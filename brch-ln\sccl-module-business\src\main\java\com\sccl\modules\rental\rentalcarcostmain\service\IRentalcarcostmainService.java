package com.sccl.modules.rental.rentalcarcostmain.service;

import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.rental.rentalcarcostmain.domain.Rentalcarcostmain;
import com.sccl.framework.service.IBaseService;
import com.sccl.modules.uniflow.common.WFModel;

import java.util.List;
import java.util.Map;

/**
 * 租赁费用主 服务层
 * 
 * <AUTHOR>
 * @date 2019-08-29
 */
public interface IRentalcarcostmainService extends IBaseService<Rentalcarcostmain>
{

    /**
     * @Description: 更新租赁费用信息
     * @author: dongk
     * @date: 2019/9/5
     * @param:
     * @return:
     */
    AjaxResult savecostmain(Rentalcarcostmain rentalcarcostmain);

    /**
     * @Description: 通过id查询对象
     * @author: dongk
     * @date: 2019/9/5
     * @param:
     * @return:
     */
    Rentalcarcostmain selectById(Long id);

    @Override
    int deleteByIds(String[] ids);


    /**
     * @Description: 通过id数组批量查询数据
     * @author: dongk
     * @date: 2019/9/5
     * @param:
     * @return:
     */
    List<Rentalcarcostmain> selectByIds(String[] ids);

    /**
     * 流程引擎回调（通过MQ）
     *
     * @param wfModel 流程消息对象
     */
    public void uniflowCallBack(WFModel wfModel);

    //租赁费用统计分析表
    List<Map<String,Object>> statistical(Integer startYear, Integer endYear, Integer startQuarter, Integer endQuarter,
                                         String company, String modelname, String suppliername);
}
