package com.sccl.modules.business.powerstationquasta.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.business.powerstationquasta.domain.PowerStationQuaSta;
import com.sccl.modules.business.powerstationquasta.service.IPowerStationQuaStaService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 站址能耗指标 信息操作处理
 * 
 * <AUTHOR>
 * @date 2021-09-14
 */
@RestController
@RequestMapping("/business/powerStationQuaSta")
public class PowerStationQuaStaController extends BaseController
{
    private String prefix = "business/powerStationQuaSta";
	
	@Autowired
	private IPowerStationQuaStaService powerStationQuaStaService;
	
	@RequiresPermissions("business:powerStationQuaSta:view")
	@GetMapping()
	public String powerStationQuaSta()
	{
	    return prefix + "/powerStationQuaSta";
	}
	
	/**
	 * 查询站址能耗指标列表
	 */
	@RequiresPermissions("business:powerStationQuaSta:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(PowerStationQuaSta powerStationQuaSta)
	{
		startPage();
        List<PowerStationQuaSta> list = powerStationQuaStaService.selectList(powerStationQuaSta);
		return getDataTable(list);
	}
	
	/**
	 * 新增站址能耗指标
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存站址能耗指标
	 */
	@RequiresPermissions("business:powerStationQuaSta:add")
	@Log(title = "站址能耗指标", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody PowerStationQuaSta powerStationQuaSta)
	{		
		return toAjax(powerStationQuaStaService.insert(powerStationQuaSta));
	}

	/**
	 * 修改站址能耗指标
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		PowerStationQuaSta powerStationQuaSta = powerStationQuaStaService.get(id);

		Object object = JSONObject.toJSON(powerStationQuaSta);

        return this.success(object);
	}
	
	/**
	 * 修改保存站址能耗指标
	 */
	@RequiresPermissions("business:powerStationQuaSta:edit")
	@Log(title = "站址能耗指标", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody PowerStationQuaSta powerStationQuaSta)
	{		
		return toAjax(powerStationQuaStaService.update(powerStationQuaSta));
	}
	
	/**
	 * 删除站址能耗指标
	 */
	@RequiresPermissions("business:powerStationQuaSta:remove")
	@Log(title = "站址能耗指标", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(powerStationQuaStaService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看站址能耗指标
     */
    @RequiresPermissions("business:powerStationQuaSta:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		PowerStationQuaSta powerStationQuaSta = powerStationQuaStaService.get(id);

        Object object = JSONObject.toJSON(powerStationQuaSta);

        return this.success(object);
    }

}
