package com.sccl.modules.business.oilreimbursement.controller;

import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;

import java.util.List;
import java.util.Map;

/**
 * @Description
 * @Auther Huang <PERSON>
 * @Date 2021/12/21 10:43
 */
public interface OilReimbursementController {
    /**
     * 初始化报账单，包括生成报账单基础信息、关联基础信息到归集单、设置明细信息基础信息
     *
     * @param pabrid 归集单ID
     * @param type   归集单类型：1：水 2：气 3：油
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/12/21 17:13
     */
    String createNewReimbursementBill(String pabrid, String type);

    /**
     * 新增报账单明细，包含明细的关联台账
     *
     * @param mssAccountItemParams
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/12/22 10:39
     */
    String createNewItemReimbursementBill(List<Map<String, Object>> mssAccountItemParams);

    /**
     * 保存报账单基础信息
     *
     * @param mssAccountbill 包含报账单基础信息ID和其他信息的对象
     * @return java.lang.String
     * <AUTHOR> Yongxiang
     * @date 2021/12/22 16:53
     */
    String saveMssAccount(MssAccountbill mssAccountbill, String pabrid);

    /**
     * 查询归集单关联的台账信息、报账单基础信息和明细信息
     *
     * @param pabrid 归集单ID
     * @return java.lang.String
     * <AUTHOR> Yongxiang
     * @date 2021/12/23 13:48
     */
    String getEnerguAccountBillDetailed(String pabrid, String type);

    /**
     * 模糊查询报账单
     *
     * @param mssAccountbill 必须包含模糊查询的字段
     * @return java.lang.String
     * <AUTHOR> Yongxiang
     * @date 2021/12/23 15:33
     */
    String getMssAccountBillLike(MssAccountbill mssAccountbill);
}
