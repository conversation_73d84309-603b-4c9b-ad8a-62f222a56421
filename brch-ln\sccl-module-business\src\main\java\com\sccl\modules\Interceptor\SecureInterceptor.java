package com.sccl.modules.Interceptor;

import cn.hutool.core.io.LineHandler;
import cn.hutool.core.lang.hash.Hash32;
import cn.hutool.core.lang.tree.parser.NodeParser;
import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.Uninterruptibles;
import com.sccl.common.lang.StringUtils;
import com.sccl.framework.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.aopalliance.intercept.MethodInterceptor;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.function.Supplier;
import java.util.stream.Stream;

@Slf4j
@Component
public class SecureInterceptor implements HandlerInterceptor {

    private String key="ifiwa";



    @Override
    public boolean preHandle(HttpServletRequest request,
                             HttpServletResponse response, Object handler) throws Exception {
        // 获取时间戳  
        String timestamp = request.getHeader("timestamp");
        // 获取随机字符串  
        String nonceStr = request.getHeader("nonceStr");
        // 获取签名  
        String signature = request.getHeader("signature");

        // 判断时间是否大于xx秒(防止重放攻击)  
        long NONCE_STR_TIMEOUT_SECONDS = 60L;
        if (StringUtils.isEmpty(timestamp) || !DateBetten(timestamp, NONCE_STR_TIMEOUT_SECONDS)) {
            throw new Exception("invalid  timestamp");
        }

        // 判断该用户的nonceStr参数是否已经在redis中（防止短时间内的重放攻击）  
/*        Boolean haveNonceStr = RedisUtil.hasStringKey(nonceStr);
        if (StringUtils.isEmpty(nonceStr) || Objects.isNull(haveNonceStr) || haveNonceStr) {
            throw new Exception("invalid nonceStr");
        }*/

//         对请求头参数进行签名
        if (StringUtils.isEmpty(signature) || !Objects.equals(signature,
                                                              this.signature(timestamp, nonceStr, request))) {
            throw new Exception("invalid signature");
        }

        // 将本次用户请求的nonceStr参数存到redis中设置xx秒后自动删除  
        RedisUtil.setJson(nonceStr, nonceStr);
        RedisUtil.setValidTimeForString(nonceStr, NONCE_STR_TIMEOUT_SECONDS*1000);

        return true;
    }

    /**
     * 判定 请求和服务器 时间差值 是否小于nonce_str_timeout_seconds
     * @param timestamp
     * @param nonce_str_timeout_seconds
     * @return
     */
    private boolean DateBetten(String timestamp, long nonce_str_timeout_seconds) {
        long other = 0;
        try {
            other = Long.parseLong(timestamp);
        } catch (NumberFormatException e) {
            throw  new RuntimeException("错误，请求时间戳有误，请重新发送");
        }
        long now = System.currentTimeMillis();
        return Math.abs(now - other) / 1000 <= 6000;
    }

    private String signature(String timestamp, String nonceStr, HttpServletRequest request) throws UnsupportedEncodingException {
        Map<String, Object> params = new HashMap<>(16);
        Enumeration<String> enumeration = request.getParameterNames();
        if (enumeration.hasMoreElements()) {
            String name = enumeration.nextElement();
            String value = request.getParameter(name);
            params.put(name, URLEncoder.encode(value, "UTF-8"));
        }
        String qs = String.format("%s×tamp=%s&nonceStr=%s&key=%s", this.sortQueryParamString(params), timestamp,
                                  nonceStr, key);
        log.info("qs:{}", qs);
        String sign = MD5Utils.string2MD5(qs);
        log.info("sign:{}", sign);
        return sign;
    }

    /**
     * 按照字母顺序进行升序排序
     *
     * @param params 请求参数 。注意请求参数中不能包含key
     * @return 排序后结果
     */
    private String sortQueryParamString(Map<String, Object> params) {
        List<String> listKeys = Lists.newArrayList(params.keySet());
        Collections.sort(listKeys);
        StringBuilder content = new StringBuilder();
        for (String param : listKeys) {
            content.append(param).append("=").append(params.get(param).toString()).append("&");
        }
        return content.toString();
    }


}  
