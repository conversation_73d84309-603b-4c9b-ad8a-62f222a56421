package com.sccl.modules.business.stationreportwhitelist.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.business.stationreportwhitelist.domain.PowerAmmeterorprotocol;
import com.sccl.modules.business.stationreportwhitelist.dto.FindWhitelistIsNotAddedQuery;
import com.sccl.modules.business.stationreportwhitelist.dto.PowerAmmeterorprotocolQuery;
import com.sccl.modules.business.stationreportwhitelist.vo.PowerAmmeterorprotocolVO;

public interface PowerAmmeterorprotocolService extends IService<PowerAmmeterorprotocol> {
    /**
     * 详情
     */
    PowerAmmeterorprotocolVO findById(Long id);

    /**
     * 根据局站id查询电表列表
     */
    AjaxResult findWhitelistIsNotAddedByStation(FindWhitelistIsNotAddedQuery query);

    /**
     * 一表多站列表
     */
    IPage<PowerAmmeterorprotocolVO> oneTableMultiStationList(Page<PowerAmmeterorprotocolVO> page, PowerAmmeterorprotocolQuery query);

    /**
     * 排除白名单的列表
     */
    IPage<PowerAmmeterorprotocolVO> excludeWhitelistList(Page<PowerAmmeterorprotocolVO> page, PowerAmmeterorprotocolQuery query);
}
