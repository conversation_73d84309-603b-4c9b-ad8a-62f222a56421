package com.sccl.modules.mssaccount.mssinterface.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import sun.misc.BASE64Encoder;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;

@Component
public class MssClient {
    // 类被new新建了实例，而没有使用@Autowired @Value取值为NULL
    private static final Logger logger = LoggerFactory.getLogger(MssClient.class);
    @Value("${MssInterface.MssClient.PASSWORD}")
    private String PASSWORD;
    @Value("${MssInterface.MssClient.PASSWORDTESTLN:}")
    private String PASSWORDTESTLN;
//    = "ZPIAPPL_LN:Zpiappl!1234";

    public static String encode(String source) {
        BASE64Encoder enc = new sun.misc.BASE64Encoder();
        return (enc.encode(source.getBytes()));
    }

    public static String convertStreamToString(InputStream is) {

        BufferedReader reader = null;
        try {
            reader = new BufferedReader(new InputStreamReader(is, "utf8"));
        } catch (UnsupportedEncodingException e1) {
            // TODO Auto-generated catch block
            e1.printStackTrace();
        }


        StringBuilder sb = new StringBuilder();
        String line = null;
        try {
            while ((line = reader.readLine()) != null) {
                sb.append(line + "\n");
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                is.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return sb.toString();
    }

    public static void main(String[] args) {
        // TODO Auto-generated method stub

        String soapRequestData = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" " +
                "xmlns:ser=\"http://services.allcommonwriteoff.ws.dakar.eshore.com/\">"
                + "<soapenv:Header/>"
                + "<soapenv:Body>"
                + "<ser:OP_ClearSAP>"

                //+"<ser:OP_GainWriteoffInstStatus>"
                //+"<ser:OP_GetBudgetItems>"
                //+"<ser:OP_GetSubOrgs>"
                //+"<ser:OP_GetWriteoffBaseData>"
                //+"<ser:OP_AutoCreateWriteoff>"
                + "<I_REQUEST>"
                + "<BASEINFO>"
                + "<MSGID>SCNH_VENDOR_20150602101010_ABCDE</MSGID>"

                + "<PMSGID></PMSGID>"

                + "<RETRY>1</RETRY>"

                + "<SENDTIME>20150805000001</SENDTIME>"
                //+"<SERVICENAME>SI_CW_CFYS_BUDGETITEM_OUT_Syn</SERVICENAME>"
                //+"<SERVICENAME>SI_CF_ESB_INTERGRATED_OUT_Syn_OP_GainWriteoffInstStatus</SERVICENAME>"
                + "<SERVICENAME>SI_CF_ESB_INTERGRATED_OUT_Syn_OP_ClearSAP</SERVICENAME>"
                //+"<SERVICENAME>SI_CF_ESB_INTERGRATED_OUT_Syn_OP_GetWriteoffBaseData</SERVICENAME>"
                //+"<SERVICENAME>SI_CF_ESB_INTERGRATED_OUT_Syn_OP_AutoCreateWriteoff</SERVICENAME>"
                + "<S_PROVINCE>13</S_PROVINCE>"

                + "<S_SYSTEM>NH</S_SYSTEM>"

                + "<T_PROVINCE>00</T_PROVINCE>"

                + "<T_SYSTEM>CW-CFBZ-CSQN</T_SYSTEM>"
                + "</BASEINFO>"

                + "<MESSAGE>"
                //+"<items><item><parentOrgCode></parentOrgCode><item><items>"
                //+"<items><item><orgCode>A113</orgCode><sapCompanyCode>13</sapCompanyCode><type>1</type><year>2015
                // </year></item></items>"
                //+"<bizMessage><![CDATA[<requestMessage><items><item><otherSystemMainId>107543_93</otherSystemMainId
                // ></item></items></requestMessage>]]></bizMessage>"
                //+"<bizMessage><![CDATA[<requestMessage><items><item><account>********@SC</account></item></items
                // ></requestMessage>]]></bizMessage>"

                + "<bizMessage><![CDATA[<requestMessage><dtoList><item><clearSapId>8a92e7e44a087785014a093db09e011c" +
                "</clearSapId><sapCompanyCode>A019</sapCompanyCode><creator>********@GD</creator><accountCode" +
                ">**********</accountCode><items><detailItem><sapCertificateCode>**********</sapCertificateCode" +
                "><sapCompanyCode>A019</sapCompanyCode><year>2014</year></detailItem><detailItem><sapCertificateCode" +
                ">**********</sapCertificateCode><sapCompanyCode>A019</sapCompanyCode><year>2014</year></detailItem" +
                "></items></item></dtoList></requestMessage>]]></bizMessage>"

                // +  "<bizMessage><![CDATA[<requestMessage><processCode>4400-CBSSJFK</processCode><processSuffixName
                // >- </processSuffixName><writeoffItems><item><otherSystemMainId>107543_93</otherSystemMainId
                // ><account>wanghw1</account><fillInName>MBOSS</fillInName><fillInOrgCode>**********</fillInOrgCode
                // ><sapCompayCode>1000</sapCompayCode><economicItemCode>102701</economicItemCode><economicItemName
                // >ww</economicItemName><paymentType>4</paymentType><happenDate>2015-09-01</happenDate><budgetSet
                // >2013-11</budgetSet><bizType>0</bizType><isStaffPayment>0</isStaffPayment><contractNo
                // >GDPB01300347CN110</contractNo><contractName>www</contractName><sum>6971
                // .0</sum><invoiceType></invoiceType><isNeedImage>0</isNeedImage><lineItems><lineItem
                // ><otherSystemDetailId>2890</otherSystemDetailId><usageCode>032</usageCode><usageName>ddd
                // </usageName><budgetType>2</budgetType><sum>6971
                // .00</sum><budgetOrgCode>**********</budgetOrgCode><sapCostCenterCode>**********</sapCostCenterCode
                // ><sapCostCenterName>ddd</sapCostCenterName><chargecode>0</chargecode></lineItem></lineItems></item
                // ></writeoffItems></requestMessage>]]></bizMessage>"
                + "</MESSAGE>"
                + "</I_REQUEST>"
                //+"</ser:OP_GetSubOrgs>"
                //+"</ser:OP_GetBudgetItems>"
                + "</ser:OP_ClearSAP>"
                // +"</ser:OP_GetWriteoffBaseData>"
                //+"</ser:OP_GainWriteoffInstStatus>"
                //+"</ser:OP_AutoCreateWriteoff>"
                + "</soapenv:Body>"
                + "</soapenv:Envelope>";

        System.out.println(soapRequestData);
        try {
            MssClient client = new MssClient();
            client.callCF("http://************:8060/PIproxy", soapRequestData);
        } catch (Exception e1) {
            // TODO Auto-generated catch block
            e1.printStackTrace();
        }
    }

    public String callCF(String soapUrl, String xml) throws Exception {
        logger.info("opiUrl:{}", soapUrl);
        String soapXml = xml;
        URL url;
        HttpURLConnection httpConn = null;
        OutputStreamWriter osw = null;
        try {
            url = new URL(soapUrl);
            httpConn = (HttpURLConnection) url.openConnection();
            String author = "Basic " + encode(PASSWORD);
            logger.info("password:{}", PASSWORD);
            logger.info("author:{}", author);
            httpConn.setRequestProperty("Authorization", author);
            httpConn.setRequestProperty("Content-Length", Integer.toString(soapXml.length()));
            httpConn.setRequestProperty("Content-Type", "text/xml; charset=utf-8");
            httpConn.setReadTimeout(60000);
            httpConn.setRequestProperty("SOAPAction", "");
            httpConn.setRequestMethod("POST");
            httpConn.setDoOutput(true);
            httpConn.setDoInput(true);

            OutputStream out = httpConn.getOutputStream();
            osw = new OutputStreamWriter(out, "utf-8");
            osw.write(soapXml);
            logger.info("soapxml{}", soapXml);
        } catch (MalformedURLException e) {
            e.printStackTrace();
            throw e;
        } catch (IOException e) {
            e.printStackTrace();
            throw e;
        } finally {
            if (osw != null) {
                try {
                    osw.flush();
                    osw.close();
                } catch (IOException e) {
                    e.printStackTrace();
                    throw e;
                }
            }
        }
        String returnXml = null;
        InputStream is = null;
        try {
            is = httpConn.getInputStream();

            String backXml = convertStreamToString(is);
            logger.debug("MSS接口调用回调:" + backXml);
            returnXml = backXml;

        } catch (Exception e) {
            e.printStackTrace();
            logger.error("MSS接口调用报错:" + e.getMessage());
            returnXml = "-1";
            throw e;
        }
        return returnXml;
    }

    public String callCF2(String soapUrl, String xml) throws Exception {
        logger.info("opiUrl:{}", soapUrl);
        logger.info("password:{}", PASSWORDTESTLN);
        String soapXml = xml;
        URL url;
        HttpURLConnection httpConn = null;
        OutputStreamWriter osw = null;
        try {
            url = new URL(soapUrl);
            httpConn = (HttpURLConnection) url.openConnection();
            String author = "Basic " + encode(PASSWORDTESTLN);
            logger.info("author:{}", author);
            httpConn.setRequestProperty("Authorization", author);
            httpConn.setRequestProperty("Content-Length", Integer.toString(soapXml.length()));
            httpConn.setRequestProperty("Content-Type", "text/xml; charset=utf-8");
            httpConn.setReadTimeout(60000);
            httpConn.setRequestProperty("SOAPAction", "");
            httpConn.setRequestMethod("POST");
            httpConn.setDoOutput(true);
            httpConn.setDoInput(true);

            OutputStream out = httpConn.getOutputStream();
            osw = new OutputStreamWriter(out, "utf-8");
            osw.write(soapXml);
            logger.info("soapxml{}", soapXml);
        } catch (MalformedURLException e) {
            e.printStackTrace();
            throw e;
        } catch (IOException e) {
            e.printStackTrace();
            throw e;
        } finally {
            if (osw != null) {
                try {
                    osw.flush();
                    osw.close();
                } catch (IOException e) {
                    e.printStackTrace();
                    throw e;
                }
            }
        }
        String returnXml = null;
        InputStream is = null;
        try {
            is = httpConn.getInputStream();

            String backXml = convertStreamToString(is);
            logger.debug("MSS接口调用回调:" + backXml);
            returnXml = backXml;

        } catch (Exception e) {
            e.printStackTrace();
            logger.error("MSS接口调用报错:" + e.getMessage());
            returnXml = "-1";
            throw e;
        }
        return returnXml;
    }

}
