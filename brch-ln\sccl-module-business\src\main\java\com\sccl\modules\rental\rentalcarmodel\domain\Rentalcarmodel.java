package com.sccl.modules.rental.rentalcarmodel.domain;

import com.sccl.modules.rental.rentalordercarmodel.domain.RentalorderCarmodel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;

import java.util.Date;
import java.math.BigDecimal;
import java.util.List;


/**
 * 车辆 （model）表 rentalcarmodel
 *
 * <AUTHOR>
 * @date 2019-08-23
 */
public class Rentalcarmodel extends BaseEntity {

    private List<RentalorderCarmodel> rentalorderCarmodels;

    public List<RentalorderCarmodel> getRentalorderCarmodels() {
        return rentalorderCarmodels;
    }

    public void setRentalorderCarmodels(List<RentalorderCarmodel> rentalorderCarmodels) {
        this.rentalorderCarmodels = rentalorderCarmodels;
    }

    private static final long serialVersionUID = 1L;
    private Long modelid;

    public Long getModelid() {
        return modelid;
    }

    public void setModelid(Long modelid) {
        this.modelid = modelid;
    }

    /**
     *
     */
    private String groupid;
    /**
     *
     */
    private String modelname;
    /**
     * 车型说明
     */
    private String modelmemo;
    /**
     * 颜色
     */
    private String color;
    /**
     * 预估值
     */
    private String futureprice;
    /**
     * 租期
     */
    private String rentalterm;
    /**
     *
     */
    private String memo;
    /**
     * 可见范围
     */
    private String visibelorgs;
    /**
     * 可见时间从
     */
    private Date visibelfrom;
    /**
     * 可见时间到
     */
    private Date visibelto;
    /**
     * id
     */
    private Long rmmid;
    /**
     *
     */
    private Date inputdate;
    /**
     *
     */
    private Long inputuserid;
    /**
     *
     */
    private String inputusername;
    /**
     * 1 草稿 2 流程中 3 完成
     */
    private String status;
    /**
     *
     */
    private BigDecimal iprocessinstid;
    /**
     *
     */
    private String company;
    /**
     *
     */
    private String country;
    private String companyName;
    private String countryName;

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }

    private Long year;// 查询 条件

    public Long getYear() {
        return year;
    }

    public void setYear(Long year) {
        this.year = year;
    }

    public void setGroupid(String groupid) {
        this.groupid = groupid;
    }

    public String getGroupid() {
        return groupid;
    }

    public void setModelname(String modelname) {
        this.modelname = modelname;
    }

    public String getModelname() {
        return modelname;
    }

    public void setModelmemo(String modelmemo) {
        this.modelmemo = modelmemo;
    }

    public String getModelmemo() {
        return modelmemo;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getColor() {
        return color;
    }

    public void setFutureprice(String futureprice) {
        this.futureprice = futureprice;
    }

    public String getFutureprice() {
        return futureprice;
    }

    public void setRentalterm(String rentalterm) {
        this.rentalterm = rentalterm;
    }

    public String getRentalterm() {
        return rentalterm;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getMemo() {
        return memo;
    }

    public void setVisibelorgs(String visibelorgs) {
        this.visibelorgs = visibelorgs;
    }

    public String getVisibelorgs() {
        return visibelorgs;
    }

    public void setVisibelfrom(Date visibelfrom) {
        this.visibelfrom = visibelfrom;
    }

    public Date getVisibelfrom() {
        return visibelfrom;
    }

    public void setVisibelto(Date visibelto) {
        this.visibelto = visibelto;
    }

    public Date getVisibelto() {
        return visibelto;
    }

    public void setRmmid(Long rmmid) {
        this.rmmid = rmmid;
    }

    public Long getRmmid() {
        return rmmid;
    }

    public void setInputdate(Date inputdate) {
        this.inputdate = inputdate;
    }

    public Date getInputdate() {
        return inputdate;
    }

    public void setInputuserid(Long inputuserid) {
        this.inputuserid = inputuserid;
    }

    public Long getInputuserid() {
        return inputuserid;
    }

    public void setInputusername(String inputusername) {
        this.inputusername = inputusername;
    }

    public String getInputusername() {
        return inputusername;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setIprocessinstid(BigDecimal iprocessinstid) {
        this.iprocessinstid = iprocessinstid;
    }

    public BigDecimal getIprocessinstid() {
        return iprocessinstid;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getCompany() {
        return company;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getCountry() {
        return country;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("modelid", getModelid())
                .append("groupid", getGroupid())
                .append("modelname", getModelname())
                .append("modelmemo", getModelmemo())
                .append("color", getColor())
                .append("futureprice", getFutureprice())
                .append("rentalterm", getRentalterm())
                .append("memo", getMemo())
                .append("visibelorgs", getVisibelorgs())
                .append("visibelfrom", getVisibelfrom())
                .append("visibelto", getVisibelto())
                .append("rmmid", getRmmid())
                .append("inputdate", getInputdate())
                .append("inputuserid", getInputuserid())
                .append("inputusername", getInputusername())
                .append("status", getStatus())
                .append("iprocessinstid", getIprocessinstid())
                .append("company", getCompany())
                .append("country", getCountry())
                .toString();
    }

    private Boolean _disabled;

    public Boolean get_disabled() {
        return _disabled;
    }

    public void set_disabled(Boolean _disabled) {
        this._disabled = _disabled;
    }

    private String disabled;

    public String getDisabled() {
        return disabled;
    }

    public void setDisabled(String disabled) {
        this.disabled = disabled;
    }
}
