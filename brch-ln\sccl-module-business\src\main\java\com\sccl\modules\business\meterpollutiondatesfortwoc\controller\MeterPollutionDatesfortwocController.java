package com.sccl.modules.business.meterpollutiondatesfortwoc.controller;

import com.alibaba.fastjson.JSONObject;
import com.sccl.common.support.Convert;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.meterpollutiondatesfortwoc.domain.MeterPollutionDatesfortwoc;
import com.sccl.modules.business.meterpollutiondatesfortwoc.service.IMeterPollutionDatesfortwocService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 双碳接口废水废气数据 信息操作处理
 *
 * <AUTHOR>
 * @date 2023-04-17
 */
@RestController
@RequestMapping("/business/meterPollutionDatesfortwoc")
public class MeterPollutionDatesfortwocController extends BaseController {
    private String prefix = "business/meterPollutionDatesfortwoc";

    @Autowired
    private IMeterPollutionDatesfortwocService meterPollutionDatesfortwocService;

    @RequiresPermissions("business:meterPollutionDatesfortwoc:view")
    @GetMapping()
    public String meterPollutionDatesfortwoc() {
        return prefix + "/meterPollutionDatesfortwoc";
    }

    /**
     * 查询双碳接口废水废气数据列表
     */
    @RequiresPermissions("business:meterPollutionDatesfortwoc:list")
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(MeterPollutionDatesfortwoc meterPollutionDatesfortwoc) {
        startPage();
        List<MeterPollutionDatesfortwoc> list =
                meterPollutionDatesfortwocService.selectList(meterPollutionDatesfortwoc);
        return getDataTable(list);
    }

    /**
     * 新增双碳接口废水废气数据
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存双碳接口废水废气数据
     */
    @Log(title = "双碳接口废水废气数据", action = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody MeterPollutionDatesfortwoc meterPollutionDatesfortwoc) {
        return toAjax(meterPollutionDatesfortwocService.insert(meterPollutionDatesfortwoc));
    }

    /**
     * 批量保存双碳接口废水废气数据
     *
     * @param meterPollutionDatesfortwoc
     * @return
     */
    @PostMapping("/addbitch")
    @ResponseBody
    public AjaxResult addbitchSave(@RequestBody List<MeterPollutionDatesfortwoc> meterPollutionDatesfortwocs) {
        return toAjax(meterPollutionDatesfortwocService.insertList(meterPollutionDatesfortwocs));
    }

    /**
     * 修改双碳接口废水废气数据
     */
    @GetMapping("/edit/{id}")
    public AjaxResult edit(@PathVariable("id") Long id) {
        MeterPollutionDatesfortwoc meterPollutionDatesfortwoc = meterPollutionDatesfortwocService.get(id);

        Object object = JSONObject.toJSON(meterPollutionDatesfortwoc);

        return this.success(object);
    }

    /**
     * 修改保存双碳接口废水废气数据
     */
    @RequiresPermissions("business:meterPollutionDatesfortwoc:edit")
    @Log(title = "双碳接口废水废气数据", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody MeterPollutionDatesfortwoc meterPollutionDatesfortwoc) {
        return toAjax(meterPollutionDatesfortwocService.update(meterPollutionDatesfortwoc));
    }

    /**
     * 删除双碳接口废水废气数据
     */
    @RequiresPermissions("business:meterPollutionDatesfortwoc:remove")
    @Log(title = "双碳接口废水废气数据", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(meterPollutionDatesfortwocService.deleteByIds(Convert.toStrArray(ids)));
    }


    /**
     * 查看双碳接口废水废气数据
     */
    @RequiresPermissions("business:meterPollutionDatesfortwoc:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id) {
        MeterPollutionDatesfortwoc meterPollutionDatesfortwoc = meterPollutionDatesfortwocService.get(id);

        Object object = JSONObject.toJSON(meterPollutionDatesfortwoc);

        return this.success(object);
    }

}
