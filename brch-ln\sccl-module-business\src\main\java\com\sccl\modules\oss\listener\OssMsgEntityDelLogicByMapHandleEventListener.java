package com.sccl.modules.oss.listener;

import com.enrising.dcarbon.manage.ResponsiveEventListener;
import com.enrising.dcarbon.manage.event.DelLogicByMapHandleEvent;
import com.sccl.modules.oss.service.impl.OssMsgServiceImp;
import com.sccl.modules.oss.mapper.OssMsgMapper;


/**
 * 操作事件DelLogicByMapHandleEvent的监听器
 *
 * <AUTHOR> <PERSON>
 * @date 2023-06-29
 */
public class OssMsgEntityDelLogicByMapHandleEventListener extends ResponsiveEventListener<DelLogicByMapHandleEvent> {
    @Override
    public void onEvent(DelLogicByMapHandleEvent event) {
        //处理事件的相关逻辑写在这里
        OssMsgMapper mapper = OssMsgServiceImp.getMapper();


    }

    /**
    * 返回监听器名称
    *
    * @return java.lang.String
    * <AUTHOR> <PERSON>
    * @date 2023-06-29 13:38:24
    */
    @Override
    public String name() {
        return null;
    }

    /**
     * 监听器级别，高级别的监听器会优先执行
     *
     * @return int
     * <AUTHOR> <PERSON>
     * @date 2023-06-29 13:38:24
     */
    @Override
    public int level() {
        return 0;
    }

}