package com.sccl.modules.mssaccount.rbillitemaccount.mapper;

import com.sccl.modules.business.account.domain.*;
import com.sccl.modules.business.accountEs.domain.AccountEsResult;
import com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol;
import com.sccl.modules.business.stationinfo.domain.PowerStationInfoRJtlte;
import com.sccl.modules.mssaccount.rbillitemaccount.domain.OrgName;
import com.sccl.modules.mssaccount.rbillitemaccount.domain.RBillitemAccount;
import com.sccl.framework.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 报账明细 台账 关联 数据层
 *
 * <AUTHOR>
 * @date 2019-06-01
 */
@Mapper
public interface RBillitemAccountMapper extends BaseMapper<RBillitemAccount> {
    void deleteRbillitemAccountByItemId(Long id);

    List<RBillitemAccount> selectListByPcids(String[] toArray);

    void deleteRbillitemAccountByBillId(Long id);

    List<RBillitemAccount> countUseMoneyedit(Map<String, Object> reqMap);

    List<RBillitemAccount> selectListByBillId(@Param("billIds") String[] billIds);
    List<RBillitemAccount> selectRListByBillId(Long id);
    List<AccountBaseResult> accountlistBybillId(RBillitemAccount rBillitemAccount);
    List<AccountBaseResult> accountlistLnBybillId(RBillitemAccount rBillitemAccount);
    void callinsertpowerjtlte(Long id);
    List<StationbybillResult> stationidListBybillId(Long id);
    void insertpowerjtlteTA(Long id);
    void insertpowerjtltenew(Long id);
    void insertpowerjtltedif(Long id);
    List<PowerStationInfoRJtlte> stationrjtlistBybillId(Long id);
    List<PowerStationInfoRJtlte> stationTarjtlistBybillId(Long id);
    List<StationbybillResult> stationlistBybillId(RBillitemAccount rBillitemAccount);
    List<StationbybillResult> stationlistBycompany(Ammeterorprotocol ammeterorprotocol);
    List<StationbybillResult> stationlistBylncompany(Ammeterorprotocol ammeterorprotocol);
    List<AccountEsResult> accountEslistBybillId(RBillitemAccount rBillitemAccount);

    void updateRbillitemAccountByBillId(Map<String, Long> pmap);

    // 根据台账ids 查询是否关联代垫的台账
    List<AccountBaseResult> selectAccountAmmeterParent(String[] toStrArray);

    Double validateMoney(Map<String, Object> param);

    //判断 报账单关联的电表/协议用电类型含“生产用电-移动基站”
    int countAmmeterTypeBybillId(Long id);
    int countLteStationBybillId(Long id);
    // 验证一个报账单里面是否包含一个电表的多个台账
    List<Ammeterorprotocol> ifhaveTwoAmmeteridByBillId(Long id);

    List<AccountEsResult> accountEslistBybillIds(String[] ids);

    AccountAmount getAccountAmount(AccountAmount accountAmount);

    int insertAccountAmount(AccountAmount accountAmount);

    int updateAccountAmount(AccountAmount accountAmount);

    List<AccountAmount> ifhaveNoAccountAmountByBillId(Long id);

    List<AccountAmount> ifhaveNoAccountAmountByPcId(Long pcid);

    //验证辽宁收款 是否有不是回收电费的协议报账
    List<Ammeterorprotocol> ifhaveNoRecycleByBillId(Long id);

	void deleteAccountAmountStationInfoByBillid(Long billid);

	void insertAccountAmountStationInfo(List<AccountAmountStationInfo> list);

	List<AccountAmountStationInfo> ifhaveNoAccountAmountStationInfoByBillId(Long id);

	List<AccountAmountStationInfo> getAccountAmountStationInfo(Long billid);

	void updateAccountAmountStationInfo(AccountAmountStationInfo item);

	// 查询报账单关联台账最小开始时间和最大结束时间
    String selectAccountsedate(Long id);

    List<Ammeterorprotocol> ifhaveIDCStationByBillId(Long id);

    List<Ammeterorprotocol> ifhaveLTEStationByBillId(Long id);
    List<Ammeterorprotocol> ifhaveLTEStationvalidityByBillId(Long id);
    int countqutoaBybillId(Long id);
    int countlteBybillId(Long id);
    int updatepowerjtl(Long id);
    void cleartemp();
    int updatepowerjtlta(Long id);
    int insertjt4glta(Long id);

    StationListTop stationlistTop(Ammeterorprotocol ammeterorprotocol);

    List<StationListTopRedis> selectRelevanceRedis(@Param("company") String company);

    List<StationListTopRedis> selectRelevanceNotRedis(@Param("company") String company);

    List<OrgName> selectOrgName(@Param("company") String company);

    List<StationbybillResult> stationlistTwoRelevance(@Param("company") String company, @Param("country") String country);
    List<StationbybillResult> stationlistTwoRelevanceNot(@Param("company") String company, @Param("country") String country);

    List<NhSite> getStationFor5G(NhSite nhSite);

    int selectByAccount(@Param("pcid") Long pcid);

    List<Long> selectAccountIdByBillId(@Param("billId") Long billId);

    /**
     * 根据报账单id判断是否是实体电表
     * @param billId
     * @return true是 false否
     */
    String getAccountIdByBillId(@Param("billId") Long billId);

    /**
     * 实体电表
     * 根据报账单id查询组织编码
     */
    String selectOrgCodeByBillId(@Param("billId") Long billId);

    /**
     * 虚拟电表
     * 根据报账单id查询组织编码
     */
    String selectOrgCodeByBillIdVirtual(@Param("billId") Long billId);
}
