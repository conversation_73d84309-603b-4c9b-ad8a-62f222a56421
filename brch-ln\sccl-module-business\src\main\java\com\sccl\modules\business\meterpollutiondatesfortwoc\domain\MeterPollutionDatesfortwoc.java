package com.sccl.modules.business.meterpollutiondatesfortwoc.domain;

import com.sccl.modules.business.twoc.domain.TwoCFlag;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.util.Date;


/**
 * 双碳接口废水废气数据表 meter_pollution_datesfortwoc
 * 
 * <AUTHOR>
 * @date 2023-04-17
 */
public class MeterPollutionDatesfortwoc extends BaseEntity implements TwoCFlag
{
	private static final long serialVersionUID = 1L;
	
    /** 地市编码 */
    private String subjectcode;
    /** 用能月份 YYYYMM */
    private String statisperiod;
    /** 省份编码 */
    private String provincecode;
    /** 市局组织编码 */
    private String citycode;
    /** 市局组织名称 */
    private String cityname;
    /** 区县组织名称 */
    private String countycode;
    /** 区县组织名称 */
    private String countyname;
    /** 归属类型 1:集团存续 2:股份上市 */
    private String grouptype;
    /** 局站类型 */
    private String stationtype;
    /** 废水排放量（吨） */
    private String wastewaterdischarge;
    /** 一般固体废物产生量（吨） */
    private String solidtrashproduce;
    /** 一般固体废物综合利用量（吨） */
    private String solidtrashhandle;
    /** 综合利用往年贮存量（吨） */
    private String solidtrashstorage;
    /** 危险废物产生量（吨） */
    private String hazardouswasteproduce;
    /** 危险废物处置量（吨） */
    private String hazardouswastehandle;
    /** 处置往年贮存量（吨） */
    private String hazardouswastestorage;
    /** 土壤污染治理面积 */
    private String soilpollutiongovern;
    /** 土壤污染需要治理面积 */
    private String soilpollutionneedgovern;
    /** 矿山（或生态）修复治理面积 */
    private String ecologygovern;
    /** 矿山（或生态）需要修复治理面积 */
    private String ecologyneedgovern;
    /** 废气治理设施数 */
    private String wastegastreatmenttotal;
    /** 废气治理设施处理能力 */
    private String wastegastreatmentable;
    /** 废水治理设施数 */
    private String wastewatertreatmenttotal;
    /** 废水治理设施处理能力 */
    private String wastewatertreatmentable;
    /** 生态环境污染源 */
    private String ecologypollutionsource;
    /** 生态环境风险点 */
    private String ecologyriskamount;
    /** 节能投入 */
    private String energyinvestment;
    /** 环保投入 */
    private String environmentinvestment;
    /** 创建时间 */
    private Date createtime;
    /** 同步标志 0未同步，1同步成功 2同步失败  */
    private Integer syncflag;
    /**  */
    private String failmag;


	public void setSubjectcode(String subjectcode)
	{
		this.subjectcode = subjectcode;
	}

	public String getSubjectcode() 
	{
		return subjectcode;
	}

	public void setStatisperiod(String statisperiod)
	{
		this.statisperiod = statisperiod;
	}

	public String getStatisperiod() 
	{
		return statisperiod;
	}

	public void setProvincecode(String provincecode)
	{
		this.provincecode = provincecode;
	}

	public String getProvincecode() 
	{
		return provincecode;
	}

	public void setCitycode(String citycode)
	{
		this.citycode = citycode;
	}

	public String getCitycode() 
	{
		return citycode;
	}

	public void setCityname(String cityname)
	{
		this.cityname = cityname;
	}

	public String getCityname() 
	{
		return cityname;
	}

	public void setCountycode(String countycode)
	{
		this.countycode = countycode;
	}

	public String getCountycode() 
	{
		return countycode;
	}

	public void setCountyname(String countyname)
	{
		this.countyname = countyname;
	}

	public String getCountyname() 
	{
		return countyname;
	}

	public void setGrouptype(String grouptype)
	{
		this.grouptype = grouptype;
	}

	public String getGrouptype() 
	{
		return grouptype;
	}

	public void setStationtype(String stationtype)
	{
		this.stationtype = stationtype;
	}

	public String getStationtype() 
	{
		return stationtype;
	}

	public void setWastewaterdischarge(String wastewaterdischarge)
	{
		this.wastewaterdischarge = wastewaterdischarge;
	}

	public String getWastewaterdischarge() 
	{
		return wastewaterdischarge;
	}

	public void setSolidtrashproduce(String solidtrashproduce)
	{
		this.solidtrashproduce = solidtrashproduce;
	}

	public String getSolidtrashproduce() 
	{
		return solidtrashproduce;
	}

	public void setSolidtrashhandle(String solidtrashhandle)
	{
		this.solidtrashhandle = solidtrashhandle;
	}

	public String getSolidtrashhandle() 
	{
		return solidtrashhandle;
	}

	public void setSolidtrashstorage(String solidtrashstorage)
	{
		this.solidtrashstorage = solidtrashstorage;
	}

	public String getSolidtrashstorage() 
	{
		return solidtrashstorage;
	}

	public void setHazardouswasteproduce(String hazardouswasteproduce)
	{
		this.hazardouswasteproduce = hazardouswasteproduce;
	}

	public String getHazardouswasteproduce() 
	{
		return hazardouswasteproduce;
	}

	public void setHazardouswastehandle(String hazardouswastehandle)
	{
		this.hazardouswastehandle = hazardouswastehandle;
	}

	public String getHazardouswastehandle() 
	{
		return hazardouswastehandle;
	}

	public void setHazardouswastestorage(String hazardouswastestorage)
	{
		this.hazardouswastestorage = hazardouswastestorage;
	}

	public String getHazardouswastestorage() 
	{
		return hazardouswastestorage;
	}

	public void setSoilpollutiongovern(String soilpollutiongovern)
	{
		this.soilpollutiongovern = soilpollutiongovern;
	}

	public String getSoilpollutiongovern() 
	{
		return soilpollutiongovern;
	}

	public void setSoilpollutionneedgovern(String soilpollutionneedgovern)
	{
		this.soilpollutionneedgovern = soilpollutionneedgovern;
	}

	public String getSoilpollutionneedgovern() 
	{
		return soilpollutionneedgovern;
	}

	public void setEcologygovern(String ecologygovern)
	{
		this.ecologygovern = ecologygovern;
	}

	public String getEcologygovern() 
	{
		return ecologygovern;
	}

	public void setEcologyneedgovern(String ecologyneedgovern)
	{
		this.ecologyneedgovern = ecologyneedgovern;
	}

	public String getEcologyneedgovern() 
	{
		return ecologyneedgovern;
	}

	public void setWastegastreatmenttotal(String wastegastreatmenttotal)
	{
		this.wastegastreatmenttotal = wastegastreatmenttotal;
	}

	public String getWastegastreatmenttotal() 
	{
		return wastegastreatmenttotal;
	}

	public void setWastegastreatmentable(String wastegastreatmentable)
	{
		this.wastegastreatmentable = wastegastreatmentable;
	}

	public String getWastegastreatmentable() 
	{
		return wastegastreatmentable;
	}

	public void setWastewatertreatmenttotal(String wastewatertreatmenttotal)
	{
		this.wastewatertreatmenttotal = wastewatertreatmenttotal;
	}

	public String getWastewatertreatmenttotal() 
	{
		return wastewatertreatmenttotal;
	}

	public void setWastewatertreatmentable(String wastewatertreatmentable)
	{
		this.wastewatertreatmentable = wastewatertreatmentable;
	}

	public String getWastewatertreatmentable() 
	{
		return wastewatertreatmentable;
	}

	public void setEcologypollutionsource(String ecologypollutionsource)
	{
		this.ecologypollutionsource = ecologypollutionsource;
	}

	public String getEcologypollutionsource() 
	{
		return ecologypollutionsource;
	}

	public void setEcologyriskamount(String ecologyriskamount)
	{
		this.ecologyriskamount = ecologyriskamount;
	}

	public String getEcologyriskamount() 
	{
		return ecologyriskamount;
	}

	public void setEnergyinvestment(String energyinvestment)
	{
		this.energyinvestment = energyinvestment;
	}

	public String getEnergyinvestment() 
	{
		return energyinvestment;
	}

	public void setEnvironmentinvestment(String environmentinvestment)
	{
		this.environmentinvestment = environmentinvestment;
	}

	public String getEnvironmentinvestment() 
	{
		return environmentinvestment;
	}


	public void setCreatetime(Date createtime)
	{
		this.createtime = createtime;
	}

	public Date getCreatetime() 
	{
		return createtime;
	}

	public void setSyncflag(Integer syncflag)
	{
		this.syncflag = syncflag;
	}

	public Integer getSyncflag() 
	{
		return syncflag;
	}

	public void setFailmag(String failmag)
	{
		this.failmag = failmag;
	}

	public String getFailmag() 
	{
		return failmag;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("subjectcode", getSubjectcode())
            .append("statisperiod", getStatisperiod())
            .append("provincecode", getProvincecode())
            .append("citycode", getCitycode())
            .append("cityname", getCityname())
            .append("countycode", getCountycode())
            .append("countyname", getCountyname())
            .append("grouptype", getGrouptype())
            .append("stationtype", getStationtype())
            .append("wastewaterdischarge", getWastewaterdischarge())
            .append("solidtrashproduce", getSolidtrashproduce())
            .append("solidtrashhandle", getSolidtrashhandle())
            .append("solidtrashstorage", getSolidtrashstorage())
            .append("hazardouswasteproduce", getHazardouswasteproduce())
            .append("hazardouswastehandle", getHazardouswastehandle())
            .append("hazardouswastestorage", getHazardouswastestorage())
            .append("soilpollutiongovern", getSoilpollutiongovern())
            .append("soilpollutionneedgovern", getSoilpollutionneedgovern())
            .append("ecologygovern", getEcologygovern())
            .append("ecologyneedgovern", getEcologyneedgovern())
            .append("wastegastreatmenttotal", getWastegastreatmenttotal())
            .append("wastegastreatmentable", getWastegastreatmentable())
            .append("wastewatertreatmenttotal", getWastewatertreatmenttotal())
            .append("wastewatertreatmentable", getWastewatertreatmentable())
            .append("ecologypollutionsource", getEcologypollutionsource())
            .append("ecologyriskamount", getEcologyriskamount())
            .append("energyinvestment", getEnergyinvestment())
            .append("environmentinvestment", getEnvironmentinvestment())
            .append("delFlag", getDelFlag())
            .append("createtime", getCreatetime())
            .append("syncflag", getSyncflag())
            .append("failmag", getFailmag())
            .toString();
    }
}
