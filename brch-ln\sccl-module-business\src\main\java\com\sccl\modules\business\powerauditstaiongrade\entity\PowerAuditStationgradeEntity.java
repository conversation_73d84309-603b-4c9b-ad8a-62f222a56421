package com.sccl.modules.business.powerauditstaiongrade.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Getter
@Setter
@TableName("power_audit_stationgrade")
public class PowerAuditStationgradeEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 能耗站址的编码
     */
    @TableField("stationCode")
    private String stationCode;

    /**
     * 公司名称
     */
    @TableField("company")
    private String company;

    /**
     * 所在国家/地区
     */
    @TableField("region")
    private String region;

    /**
     * 站址的名称
     */
    @TableField("stationName")
    private String stationName;
    /**
     * 报账账期
     */
    private String budget;



    /**
     * 站址的详细地址
     */
    @TableField("stationAddress")
    private String stationAddress;

    /**
     * 能耗的功耗数值
     */
    @TableField("energyConsumption")
    private BigDecimal energyConsumption;

    /**
     * 标准功耗数值
     */
    @TableField("standardPower")
    private BigDecimal standardPower;

    /**
     * 实际功耗与标准功耗的偏离比例
     */
    @TableField("deviationRatio")
    private BigDecimal deviationRatio;

    /**
     * 能耗评级的具体内容
     */
    @TableField("evaluationContent")
    private String evaluationContent;

    /**
     * 评级内容
     */
    private String content;

    /**
     * 能耗评级的时间
     */
    @TableField("evaluationDate")
    private Date evaluationDate;
    /**
     * 有效标识 0/1 有效/无效
     */
    private Integer del_flag;
}
