package com.sccl.modules.business.powerappdbyc.mapper;

import com.sccl.modules.business.powerappdbyc.domain.PowerAppDbyc;
import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.toweraccount.domain.TowerData;

import java.util.List;

/**
 * 大数据基站异常 数据层
 * 
 * <AUTHOR>
 * @date 2022-03-20
 */
public interface PowerAppDbycMapper extends BaseMapper<PowerAppDbyc>
{
    public List<PowerAppDbyc> selectByList(PowerAppDbyc powerAppDbyc);
	
}