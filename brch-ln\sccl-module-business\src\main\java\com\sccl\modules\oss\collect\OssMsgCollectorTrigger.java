package com.sccl.modules.oss.collect;

import com.enrising.dcarbon.collector.AbstractCollectorTemplate;
import com.enrising.dcarbon.collector.CyclicallyCollectorTrigger;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-06-29 16:31
 * @email <EMAIL>
 */
public class OssMsgCollectorTrigger extends CyclicallyCollectorTrigger {
    public OssMsgCollectorTrigger(AbstractCollectorTemplate template, long cycle, TimeUnit unit) {
        super(template, cycle, unit);
    }

    @Override
    public void execute() {
        executeActiveTemplate();
    }
}
