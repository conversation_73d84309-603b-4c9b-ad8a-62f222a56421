package com.sccl.modules.mssaccount.mssinterface.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.enrising.dcarbon.codec.JsonUtil;
import com.sccl.common.lang.StringUtils;
import com.sccl.common.utils.TimeUtils;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.autojob.util.id.IdGenerator;
import com.sccl.modules.business.meterdatesfortwoc.domain.MeterDateForTowcSyncEnd;
import com.sccl.modules.business.meterdatesfortwoc.domain.Meterdatesfortwoc;
import com.sccl.modules.business.meterdatesfortwoc.mapper.MeterdatesfortwocMapper;
import com.sccl.modules.business.meterotherdatesfortwoc.domain.MeterOtherDatesfortwoc;
import com.sccl.modules.business.meterotherdatesfortwoc.domain.MeterOtherDatesfortwocSync;
import com.sccl.modules.business.meterotherdatesfortwoc.mapper.MeterOtherDatesfortwocMapper;
import com.sccl.modules.business.meterpollutiondatesfortwoc.domain.MeterPollutionDatesfortwoc;
import com.sccl.modules.business.meterpollutiondatesfortwoc.domain.MeterPollutionDatesfortwocSync;
import com.sccl.modules.business.meterpollutiondatesfortwoc.mapper.MeterPollutionDatesfortwocMapper;
import com.sccl.modules.business.powermodel.mapper.PowerModleInitMapper;
import com.sccl.modules.business.syncresult.domain.Syncresult;
import com.sccl.modules.business.syncresult.mapper.SyncresultMapper;
import com.sccl.modules.business.twoc.domain.MeterInofTwoC;
import com.sccl.modules.business.twoc.domain.TwoCFlag;
import com.sccl.modules.business.twoc.domain.Twoc;
import com.sccl.modules.business.twoc.mapper.TwocMapper;
import com.sccl.modules.monitor.operlog.domain.OperLog;
import com.sccl.modules.monitor.operlog.mapper.OperLogMapper;
import com.sccl.modules.mssaccount.mssaccountbill.mapper.MssAccountbillMapper;
import com.sccl.modules.mssaccount.mssinterface.domain.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.Charset;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

@Component
@Slf4j
public class MssJsonClient {
    private static String CHARSET = "UTF-8";
    @Autowired
    private SqlSessionFactory sqlSessionFactory;
    @Autowired
    private SyncresultMapper syncresultMapper;
    @Value("${sccl.deployTo}")
    private String deployTo;
    // 类被new新建了实例，而没有使用@Autowired @Value取值为NULL
    @Value("${MssInterface.MssJsonClient.URL}")
    private String URL;// = "http://136.96.61.114:8888/api/openapi";
    //集团能耗同步上传地址
    @Value("${MssInterface.MssJsonClient.NHURL}")
    private String NHURL;
    // 辽宁上传电价地址
    /*    @Value("${MssInterface.MssJsonClient.LNSP}")*/
    private String LNSP = "http://136.96.61.114:8888/api/openapi/sjpt.dj.sync/syncEnergyMeterPriceInfos";
    // 辽宁查询电价地址
    /*    @Value("${MssInterface.MssJsonClient.LNSSP}")*/
    private String LNSSP = "http://136.96.61.114:8888/api/openapi/sjpt.dj.inquire/getEnergyMeterPriceInfos";
    // 测试秘钥
    //    private static String APPKey = "32e9c4952904219c6d532086b3905b50";
    //    private static String AppSecret = "6d403ace70e313ccd10f18663d9e14f7";
    //正式秘钥
    @Value("${MssInterface.MssJsonClient.APPKey}")
    private String APPKey;// = "c0e02c1ea7d741da987d08aa3ce57db6";
    @Value("${MssInterface.MssJsonClient.AppSecret}")
    private String AppSecret;// = "f11c4ece1a3352f321fc527388d5fe94";

    @Value("${Twoc.roomorstation.X-APP-ID:514d972deab8d4d217621a1a12c4f040}")
    private String roomorstationAPPID;
    @Value("${Twoc.roomorstation.X-APP-KEY:3f66ec2a8c90f183a970171834680946}")
    private String roomorstationAPPKEY;


    @Value("${Twoc.prod.X-APP-ID:Properties not load}")
    private String TwocAPPID;
    @Value("${Twoc.prod.X-APP-KEY:Properties not load}")
    private String TwocAppKEY;
    @Value("${Twoc.prod.MeterURL:Properties not load}")
    private String TwocMeterUrl;
    @Value("${Twoc.prod.PowerURL:Properties not load}")
    private String TwocPowerUrl;
    @Value("${Twoc.prod.OtherURL:Properties not load}")
    private String TwocOtherUrl;
    @Value("${Twoc.prod.PollutionURL:Properties not load}")
    private String TwocPollutionUrl;
    @Value("${Twoc.prod.test1:Properties not load}")
    private String LnTest1Url;
    @Value("${Twoc.prod.test2:Properties not load}")
    private String LnTest2Url;

    @Value("${MssInterface.MssJsonClient.PROVINCECODE}")
    private String PROVINCECODE;
    @Value("${MssInterface.MssJsonClient.msgF}")
    private String msgF;//SCNH_
    @Autowired
    private OperLogMapper operLogMapper;
    @Autowired
    private TwocMapper twocMapper;
    @Autowired
    private MssAccountbillMapper billMapper;
    @Autowired
    private MeterdatesfortwocMapper meterdatesfortwocMapper;
    @Autowired
    private MeterOtherDatesfortwocMapper meterOtherDatesfortwocMapper;
    @Autowired
    private MeterPollutionDatesfortwocMapper meterPollutionDatesfortwocMapper;
    @Lazy
    @Autowired
    private IMssInterfaceService mssInterfaceService;
    public static boolean collectFlag = true;
    private ReentrantReadWriteLock writeMeterInfoDbLock = new ReentrantReadWriteLock();
    private ReentrantReadWriteLock writeWriteoffInfoDbLock = new ReentrantReadWriteLock();

    public static void main(String args[]) {

        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String format = now.format(formatter);
        System.out.println(format);
        System.out.println(new Date().toString());

        String nowTime = TimeUtils.getNowTime();
        System.out.println(nowTime);

    }

    public static List<MeterInfo2> notExist(List<MeterInfo2> deduplicateMeterInfoList, List<MeterInfo2> Exist) {
        Set<String> existKeys = Exist.stream().map(existElement -> existElement.getProvinceCode() + existElement.getCityCode() + existElement.getCountyCode() + existElement.getEnergyMeterCode()).collect(Collectors.toSet());

        List<MeterInfo2> unmatchedElements = deduplicateMeterInfoList.stream().filter(deduplicateElement -> {
            String key = deduplicateElement.getProvinceCode() + deduplicateElement.getCityCode() + deduplicateElement.getCountyCode() + deduplicateElement.getEnergyMeterCode();
            return !existKeys.contains(key);
        }).collect(Collectors.toList());

        return unmatchedElements;
    }

    public static List<MeterInfo2> ExistStacodeNot(List<MeterInfo2> deduplicateMeterInfoList, List<MeterInfo2> Exist) {
        return deduplicateMeterInfoList.stream().filter(deduplicateElement -> Exist.stream().anyMatch(existElement -> deduplicateElement.getProvinceCode().equals(existElement.getProvinceCode()) && deduplicateElement.getCityCode().equals(existElement.getCityCode()) && deduplicateElement.getCountyCode().equals(existElement.getCountyCode()) && deduplicateElement.getEnergyMeterCode().equals(existElement.getEnergyMeterCode()) && !deduplicateElement.getStationCode().equals(existElement.getStationCode()))).collect(Collectors.toList());
    }

    /**
     * 同步电表基础数据 post
     * /rest/energyMeter/syncEnergyMeterInfos
     */
    public String doHttp(Map<String, Object> param, String url) throws Exception {
//        SimpleClientHttpRequestFactory httpRequestFactory = new SimpleClientHttpRequestFactory();
//        httpRequestFactory.setConnectTimeout(3000);
//        httpRequestFactory.setReadTimeout(3000);
        RestTemplate rest = new RestTemplate();
        rest.getMessageConverters().add(new StringHttpMessageConverter(Charset.forName(CHARSET)));
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("X-APP-ID", APPKey);
        headers.add("X-APP-KEY", AppSecret);
        param.put("provinceCode", PROVINCECODE);
        String msgId = msgF + System.currentTimeMillis();
        param.put("msgId", msgId);
        String time = TimeUtils.getNowTime();
        log.info("param去掉 key=billid");

        Long billid = (Long) param.remove("billid");

        HttpEntity<Object> requestEntity = new HttpEntity<>(param, headers);
        String resJson = null;

        try {
/*            String jsStr=JsonUtil.pojoToJsonString(requestEntity);
            JsonElement jsonElement  = new JsonParser().parse(jsStr);
             jsonElement.getAsJsonObject();
            for (JsonElement item : ja){
                WriteoffInfo woi = new Gson().fromJson(item, WriteoffInfo.class);
                List<WriteoffDetailInfo> wollist=woi.getWriteoffDetailInfos();
                for (WriteoffDetailInfo wdi:wollist)
                {sum=sum.add(new BigDecimal(wdi.getThisElectricityCharge())).setScale(2, RoundingMode.HALF_UP);
                }
            }
            System.out.println("sum:"+sum);*/
            resJson = rest.postForObject(url, requestEntity, String.class);
            if (resJson != null && resJson.contains("\"code\":\"0\"")) {
                // 若是推送成功，则将报账单的状态置为1
                billMapper.updateMssAccountbillStatusById(billid, "1");
                if (param.containsKey("meterInfos")) {
                    insertDbForEnergyMeterInfos(param, msgId, resJson, billid);
                } else if (param.containsKey("writeoffInfos")) {
                    insertDbForWriteoffDetailInfos(param, msgId, resJson,billid);
                }
            } else {
                billMapper.updateMssAccountbillStatusById(billid, "2");
            }
        } catch (RestClientException e) {
            log.info("同步数据存档");
            insertResult("数据平台", 1, "", time, "失败", JsonUtil.pojoToJsonString(requestEntity), "未能连接到集团接口", 1);
            if (param.containsKey("meterInfos")) {
                log.info("同步电表数据出错：{}", e.getMessage());
                insertDbForEnergyMeterInfos(param, msgId, resJson, billid);

            } else if (param.containsKey("writeoffInfos")) {
                log.info("同步电表用电数据出错:{}", e.getMessage());
                insertDbForWriteoffDetailInfos(param, msgId, resJson,billid);
            }
            billMapper.updateMssAccountbillStatusById(billid, "2");
            e.getMessage();
            throw e;
        }
        log.info("同步数据存档");
/*        if (param.containsKey("meterInfos")) {
            log.info("同步电表数据resJson:{}", resJson);
            insertDbForEnergyMeterInfos(param, msgId, resJson, billid);
            //insertResult("数据平台", 1, "", time, "成功", JsonUtil.pojoToJsonString(requestEntity), "未能连接到集团接口", 1);
        } else if (param.containsKey("writeoffInfos")) {
            log.info("同步电表用电数据resJson:{}", resJson);
            insertDbForWriteoffDetailInfos(param, msgId, resJson, billid);
        }*/
//        if (param.containsKey("meterInfos")) {
//            log.info("同步计量设备增量数据");
//            syncIncrementalMeter2(param, msgId, resJson);
//        }

        return resJson + msgId;
    }

    private void syncIncrementalMeter(Map<String, Object> param, String msgId, String resJson) {
        log.info("接收到财辅返回信息:{}", resJson);
        if (resJson != null && resJson.contains("\"code\":\"0\"")) {
            log.info("判定成功");
            List<MeterInfo> meterInfos = (List<MeterInfo>) param.get("meterInfos");
            List<MeterInfo> meterInfoList = new ArrayList<>();
            List<MeterInfo> distinctMeterInfoList = meterInfos.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(meterInfo -> meterInfo.getStationCode() + meterInfo.getEnergyMeterCode()))), ArrayList::new));
            log.info("判定同步电表数据是否存在meterinfo表中,判断标准 stationcode+energyMeterCode相等");
            log.info("进行计量设备删除 contion->stationcode相等，energyMeterCode不相等");
            List<? extends MeterInfo2> meterInfosForDel = syncresultMapper.selectDelete(distinctMeterInfoList);
            List<String> listadd = meterInfosForDel.stream().map(MeterInfo2::getStationCode).collect(toList());
            meterInfosForDel.forEach(meterInfo2 -> meterInfo2.setType(MssInterfaceServiceImpl.SYNCTYPEDELETE));
            try {
                mssInterfaceService.syncIncrementalMeter(meterInfosForDel);
            } catch (Exception e) {
                log.info("计算设备同步删除出错");
                e.printStackTrace();
            }

            log.info("进行计量设备新增 contion->stationcode相等，energyMeterCode不相等");
            List<? extends MeterInfo2> meterinfosadd1 = syncresultMapper.selectForMeterinfoAll(listadd);

            log.info("进行计量设备新增 contion->stationcode从未在meterinfo中出现");
            List<String> stationcodes = distinctMeterInfoList.stream().map(MeterInfo::getStationCode).collect(toList());
            List<String> newStationcodes = syncresultMapper.selectNewStationcdoes(stationcodes);
            List<? extends MeterInfo2> meterinfosadd2 = syncresultMapper.selectForMeterinfoAll(newStationcodes);

            ArrayList<MeterInfo2> meterInfo2s = new ArrayList<>();
            meterInfo2s.addAll(meterinfosadd1);
            meterInfo2s.addAll(meterinfosadd2);
            List<? extends MeterInfo2> meterinAddAll = new ArrayList<>(meterInfo2s);
            meterinAddAll.forEach(item -> item.setType(MssInterfaceServiceImpl.SYNCTYPECREATE));
            try {
                mssInterfaceService.syncIncrementalMeter(meterinAddAll);
            } catch (Exception e) {
                e.printStackTrace();
                log.info("计量设备同步新增异常");
            }

            log.info("数据库层面操作");
            log.info("逻辑删除");
            syncresultMapper.delMeterinfoForStatoncode(listadd);
            log.info("新增");
            List<String> insertStationcodes = meterInfo2s.stream().map(MeterInfo2::getStationCode).collect(toList());
            syncresultMapper.insertMeterinfoAll(insertStationcodes);
            syncresultMapper.insertMeterinfoAll2(meterinAddAll);

        } else {
            log.info("判定失败");
            log.info("不进行任何操作");
        }
    }

    private void syncIncrementalMeter2(Map<String, Object> param, String msgId, String resJson) {
        log.info("接收到财辅返回信息:{}", resJson);
        if (resJson != null && resJson.contains("\"code\":\"0\"")) {
            log.info("判定成功");

            log.info("取出同步数据");
            List<MeterInfo> meterInfos = (List<MeterInfo>) param.get("meterInfos");

            log.info("数据转化为计量设备协议格式");
            List<MeterInfo2> meterInfo2s = MeterInfo.convertMeterInfos(meterInfos);
            List<String> energyMeterCodes = meterInfo2s.stream().filter(item -> item.getEnergyMeterCode() != null).map(MeterInfo2::getEnergyMeterCode).collect(toList());
            if (CollectionUtils.isEmpty(energyMeterCodes)) {
                log.info("同步电表信息不存在有效电表编码，计量设备更正结束");
                return;
            }
            List<String> contractPrices = syncresultMapper.selectContractPrice(energyMeterCodes);
            Map<String, String> contractPriceMap = contractPrices.stream().map(item -> item.split("/")).collect(toMap(item -> item[0].trim(), item -> item[1].trim(), (item1, item2) -> item1));
            meterInfo2s.forEach(meterInfo2 -> meterInfo2.setContractPrice(contractPriceMap.get(meterInfo2.getEnergyMeterCode())));
            log.info("转化完毕");
            log.info("计量设备数据重复标准：provinceCode+cityCode+countyCode+energyMeterCode");
            log.info("去重");
            List<MeterInfo2> deduplicateMeterInfoList = MeterInfo2.deduplicateMeterInfoList(meterInfo2s);
            List<MeterInfo2> Exist = syncresultMapper.selectExists(deduplicateMeterInfoList);
            log.info("获取不在局站全量表中存在的");
            List<MeterInfo2> notExists = notExist(deduplicateMeterInfoList, Exist);
            log.info("获取在局站全量表存在但是stationcode不相等的，即换表数据");
            List<MeterInfo2> existStacodeNot = ExistStacodeNot(deduplicateMeterInfoList, Exist);

            log.info("判定有无要改变的计量设备数据");
            if (CollectionUtils.isEmpty(notExists) && CollectionUtils.isEmpty(existStacodeNot)) {
                log.info("同步到财的计量设备已存在，计量设备增量同步不执行");
                return;
            }

            log.info("数据转化");
            notExists = notExists.stream().map(meterInfo2 -> {
                MeterInfo3 meterInfo3 = new MeterInfo3();
                BeanUtils.copyProperties(meterInfo2, meterInfo3);
                return meterInfo3;
            }).collect(toList());

            existStacodeNot = existStacodeNot.stream().map(meterInfo2 -> {
                MeterInfo3 meterInfo3 = new MeterInfo3();
                BeanUtils.copyProperties(meterInfo2, meterInfo3);
                return meterInfo3;
            }).collect(toList());

            log.info("数据转化为计量设备协议要求的格式");
            notExists.forEach(item -> {
                MeterInfo2.ProcessData(item);
            });
            existStacodeNot.forEach(item -> {
                MeterInfo2.ProcessData(item);
            });

            log.info("DB层面");
            if (CollectionUtils.isNotEmpty(notExists)) {
                log.info("存在未存在的计量设备数据");
                int n = syncresultMapper.insertMeterinfoBitch(notExists);
                log.info("新增 未存在的计量设备{}条", n);
            }
            if (CollectionUtils.isNotEmpty(existStacodeNot)) {
                log.info("存在换表数据{}条", existStacodeNot.size());
                int n = syncresultMapper.deleteMeterinoBitch(existStacodeNot);
                log.info("删除原有数据{}条", n);
                int n1 = syncresultMapper.insertMeterinfoBitch(existStacodeNot);
                log.info("新增换标后的数据{}条", n1);
            }


            log.info("sync层面");
            notExists.addAll(existStacodeNot);
            try {
                mssInterfaceService.syncIncrementalMeter(notExists);
            } catch (Exception e) {
                e.printStackTrace();
                log.info("集团同步失败");
            }
        } else {
            log.info("判定失败");
            log.info("不进行任何操作");
        }
    }


    private static ConcurrentHashMap<Long, List<MeterInfoDb>> DbForEnergyMeterInfosMap = new ConcurrentHashMap<>();
    private static ConcurrentHashMap<Long, List<WriteoffInfoDb>> DbForWriteoffDetailInfosMap = new ConcurrentHashMap<>();

    private void insertDbForEnergyMeterInfos(Map<String, Object> param, String msgId, String resJson, Long billid) {
        log.info("将报账财辅同步电表数据插入数据库中");
        log.info("接收到财辅返回信息:{}", resJson);
        if (resJson != null && resJson.contains("\"code\":\"0\"")) {
            log.info("判定成功");
            List<MeterInfo> meterInfos = (List<MeterInfo>) param.get("meterInfos");
            List<MeterInfoDb> dbs = meterInfos.stream().map(meterInfo -> {
                MeterInfoDb db = new MeterInfoDb();
                db.setMsgId(msgId);
                db.setBillid(String.valueOf(billid));
                db.setSyncResult("成功");
                BeanUtils.copyProperties(meterInfo, db);
                return db;
            }).collect(toList());

            try {
                writeMeterInfoDbLock.readLock().lock();
                DbForEnergyMeterInfosMap.put(billid, dbs);
            } finally {
                writeMeterInfoDbLock.readLock().unlock();
            }
            bitchInsertEnergyMeterInfos();
        } else {
            log.info("判定失败");
            List<MeterInfo> meterInfos = (List<MeterInfo>) param.get("meterInfos");
            List<MeterInfoDb> dbs = meterInfos.stream().map(meterInfo -> {
                MeterInfoDb db = new MeterInfoDb();
                db.setMsgId(msgId);
                db.setBillid(String.valueOf(billid));
                db.setSyncResult("失败");
                BeanUtils.copyProperties(meterInfo, db);
                return db;
            }).collect(toList());
            int n = syncresultMapper.insertMeterInfos(dbs);
            log.info("插入电表基础同步数据：{}条", 0);
        }
    }

    private void bitchInsertEnergyMeterInfos() {
            writeMeterInfoDbLock.writeLock().lock();
            try {
                ArrayList<MeterInfoDb> temps = new ArrayList<>();
                DbForEnergyMeterInfosMap.forEach(
                        (billID, meterInfoDbs) -> {
                            temps.addAll(meterInfoDbs);
                        }
                );
                int n = syncresultMapper.insertMeterInfos(temps);
                log.info("插入电表基础同步数据：{}条", n);
                DbForEnergyMeterInfosMap.clear();
            } finally {
                writeMeterInfoDbLock.writeLock().unlock();
            }
    }

    private void bitchInsertWriteoffDetailInfos() {
            writeWriteoffInfoDbLock.writeLock().lock();
            try {
                ArrayList<WriteoffInfoDb> temps = new ArrayList<>();
                DbForWriteoffDetailInfosMap.forEach(
                        (billID, writeoffInfoDb) -> {
                            temps.addAll(writeoffInfoDb);
                        }
                );
                int n = syncresultMapper.insertWriteoffInfos(temps);
                log.info("插入电表用电同步数据：{}条", n);
                DbForWriteoffDetailInfosMap.clear();
            } finally {
                writeWriteoffInfoDbLock.writeLock().unlock();
            }
    }

    private void insertDbForWriteoffDetailInfos(Map<String, Object> param, String msgId, String resJson, Long billid) {
        log.info("将报账财辅同步电表用电数据 插入数据库中");
        log.info("接收到财辅返回信息:{}", resJson);
        if (resJson != null && resJson.contains("\"code\":\"0\"")) {
            log.info("判定成功");
            List<WriteoffInfo> writeoffInfos = (List<WriteoffInfo>) param.get("writeoffInfos");
            List<WriteoffInfoDb> dbs = writeoffInfos.stream().flatMap(writeoffInfo -> {
                ArrayList<WriteoffInfoDb> tempDbs = new ArrayList<>();
                List<WriteoffDetailInfo> detailInfos = writeoffInfo.getWriteoffDetailInfos();
                detailInfos.stream().forEach(item -> {
                    WriteoffInfoDb db = new WriteoffInfoDb();
                    BeanUtils.copyProperties(writeoffInfo, db);
                    BeanUtils.copyProperties(item, db);
                    db.setSyncResult("成功");
                    db.setMsgId(msgId);
                    db.setBillid(billid);
                    tempDbs.add(db);
                });
                return tempDbs.stream();
            }).collect(toList());
            try {
                writeWriteoffInfoDbLock.readLock().lock();
                DbForWriteoffDetailInfosMap.put(billid, dbs);
            } finally {
                writeWriteoffInfoDbLock.readLock().unlock();
            }
            bitchInsertWriteoffDetailInfos();
        } else {
            log.info("判定失败");
            List<WriteoffInfo> writeoffInfos = (List<WriteoffInfo>) param.get("writeoffInfos");
            List<WriteoffInfoDb> dbs = writeoffInfos.stream().flatMap(writeoffInfo -> {
                ArrayList<WriteoffInfoDb> tempDbs = new ArrayList<>();
                List<WriteoffDetailInfo> detailInfos = writeoffInfo.getWriteoffDetailInfos();
                detailInfos.stream().forEach(item -> {
                    WriteoffInfoDb db = new WriteoffInfoDb();
                    BeanUtils.copyProperties(writeoffInfo, db);
                    BeanUtils.copyProperties(item, db);
                    db.setSyncResult("失败");
                    db.setMsgId(msgId);
                    db.setBillid(billid);
                    tempDbs.add(db);
                });
                return tempDbs.stream();
            }).collect(toList());
            int n = syncresultMapper.insertWriteoffInfos(dbs);
            log.info("插入电表用电同步数据：{}条", n);
        }
    }

    /**
     * 同步电表基础数据 post
     * /rest/energyMeter/syncEnergyMeterInfos
     */
    public String syncWriteoffInfos(List<WriteoffInfo> writeoffInfos, Long billId) throws Exception {
//        String url = URL + "/syncWriteoffInfos/rest/energyMeter/syncWriteoffInfos";
        String url = URL;
        if ("sc".equals(deployTo)) {
            url = URL + "/syncWriteoffInfos/rest/energyMeter/syncWriteoffInfos";
        }
        if ("ln".equals(deployTo)) {
//            url += "/mss.sync.bill/syncWriteoffInfos";
            url += "/syncWriteoffInfos";
        }
        BigDecimal Chargesum = new BigDecimal(0);
        BigDecimal pricesum = new BigDecimal(0);
        BigDecimal taxsum = new BigDecimal(0);
        for (WriteoffInfo woi : writeoffInfos) {
            List<WriteoffDetailInfo> wdi = woi.getWriteoffDetailInfos();
            for (WriteoffDetailInfo wdd : wdi) {
                Chargesum = Chargesum.add(new BigDecimal(wdd.getThisElectricityCharge())).setScale(2, RoundingMode.HALF_UP);
                taxsum = taxsum.add(new BigDecimal(wdd.getThisElectricityTax())).setScale(2, RoundingMode.HALF_UP);
                pricesum = pricesum.add(new BigDecimal(wdd.getThisElectricityPrice())).setScale(2, RoundingMode.HALF_UP);
            }
        }
        System.out.println("Chargesum：" + Chargesum);
        System.out.println("taxsum：" + taxsum);
        System.out.println("pricesum：" + pricesum);
        Map<String, Object> param = new HashMap<>();
/*        param.put("Charge_sum",Chargesum);
        param.put("price_sum",pricesum);
        param.put("tax_sum",taxsum);*/
        param.put("writeoffInfos", writeoffInfos);
        param.put("billid", billId);
        log.info("url:[}", url);
        String res = doHttp(param, url);
        return "Chargesum:" + Chargesum + ";pricesum:" + pricesum + ";taxsum:" + taxsum + res;
    }

    public String syncWriteoffInfosAddPcids(List<WriteoffInfo> writeoffInfos, Long billId, List<String> pcids) throws Exception {
//        String url = URL + "/syncWriteoffInfos/rest/energyMeter/syncWriteoffInfos";
        String url = URL;
        if ("sc".equals(deployTo)) {
            url = URL + "/syncWriteoffInfos/rest/energyMeter/syncWriteoffInfos";
        }
        if ("ln".equals(deployTo)) {
//            url += "/mss.sync.bill/syncWriteoffInfos";
            url += "/syncWriteoffInfos";
        }
        BigDecimal Chargesum = new BigDecimal(0);
        BigDecimal pricesum = new BigDecimal(0);
        BigDecimal taxsum = new BigDecimal(0);
        for (WriteoffInfo woi : writeoffInfos) {
            List<WriteoffDetailInfo> wdi = woi.getWriteoffDetailInfos();
            for (WriteoffDetailInfo wdd : wdi) {
                Chargesum = Chargesum.add(new BigDecimal(wdd.getThisElectricityCharge())).setScale(2, RoundingMode.HALF_UP);
                taxsum = taxsum.add(new BigDecimal(wdd.getThisElectricityTax())).setScale(2, RoundingMode.HALF_UP);
                pricesum = pricesum.add(new BigDecimal(wdd.getThisElectricityPrice())).setScale(2, RoundingMode.HALF_UP);
            }
        }
        System.out.println("Chargesum：" + Chargesum);
        System.out.println("taxsum：" + taxsum);
        System.out.println("pricesum：" + pricesum);
        Map<String, Object> param = new HashMap<>();

        log.info("执行新增台账逻辑");
        WriteoffInfo writeoffInfo = writeoffInfos.get(0);
        List<WriteoffDetailInfo> infoDbs = writeoffInfo.getWriteoffDetailInfos();
        List<WriteoffDetailInfo> MatchingElements =
                infoDbs.stream().filter(
                        info -> {
                            WriteoffDetailInfoAddPcids db = (WriteoffDetailInfoAddPcids) info;
                            return pcids.contains(db.getAccountId());
                        }
                ).collect(toList());
        infoDbs.addAll(MatchingElements);

        log.info("按照协议格式转化");
        List<WriteoffDetailInfo> syncs = infoDbs.stream().map(
                db -> {
                    WriteoffDetailInfo sync = new WriteoffDetailInfo();
                    BeanUtils.copyProperties(db, sync);
                    return sync;
                }
        ).collect(toList());
        writeoffInfo.setWriteoffDetailInfos(syncs);

        param.put("writeoffInfos", writeoffInfos);
        param.put("billid", billId);
        log.info("url:[}", url);
        String res = doHttp(param, url);
        return "Chargesum:" + Chargesum + ";pricesum:" + pricesum + ";taxsum:" + taxsum + res;
    }

    public String syncWriteoffInfosPro(List<WriteoffInfo> writeoffInfos, Long billId) throws Exception {
//        String url = URL + "/syncWriteoffInfos/rest/energyMeter/syncWriteoffInfos";
        String url = URL;
        if ("sc".equals(deployTo)) {
            url = URL + "/syncWriteoffInfos/rest/energyMeter/syncWriteoffInfos";
        }
        if ("ln".equals(deployTo)) {
//            url += "/mss.sync.bill/syncWriteoffInfos";
            url += "/syncWriteoffInfos";
        }
        BigDecimal Chargesum = new BigDecimal(0);
        BigDecimal pricesum = new BigDecimal(0);
        BigDecimal taxsum = new BigDecimal(0);
        for (WriteoffInfo woi : writeoffInfos) {
            List<WriteoffDetailInfo> wdi = woi.getWriteoffDetailInfos();
            for (WriteoffDetailInfo wdd : wdi) {
                Chargesum = Chargesum.add(new BigDecimal(wdd.getThisElectricityCharge())).setScale(2, RoundingMode.HALF_UP);
                taxsum = taxsum.add(new BigDecimal(wdd.getThisElectricityTax())).setScale(2, RoundingMode.HALF_UP);
                pricesum = pricesum.add(new BigDecimal(wdd.getThisElectricityPrice())).setScale(2, RoundingMode.HALF_UP);
            }
        }
        System.out.println("Chargesum：" + Chargesum);
        System.out.println("taxsum：" + taxsum);
        System.out.println("pricesum：" + pricesum);
        Map<String, Object> param = new HashMap<>();
/*        param.put("Charge_sum",Chargesum);
        param.put("price_sum",pricesum);
        param.put("tax_sum",taxsum);*/
        param.put("writeoffInfos", writeoffInfos);
        param.put("billid", billId);
        log.info("url:[}", url);
        String res = doHttp(param, url);
        return res;
    }

    /**
     * 同步 抄表
     *
     * @param writeoffInfos
     * @return
     * @throws Exception
     */
    public String syncWriteoffInfos2(List<WriteoffInfo2> writeoffInfos) throws Exception {
        String url = null;
        if ("sc".equals(deployTo)) {
            url = NHURL + "/syncWriteoffInfos/rest/energyMeter/syncWriteoffInfos";
        }
        if ("ln".equals(deployTo)) {
            url = NHURL + "/nh.cb.xx/syncWriteoffInfos";
        }
        Map<String, Object> param = new HashMap<>();
        param.put("writeoffInfos", writeoffInfos);
        String res = doHttpForCopyMeter(param, url);
        return res;
    }

    /**
     * 双碳电表数据生成
     *
     * @param writeoffInfos
     * @return
     * @throws Exception
     */
    public String syncWriteoffInfosTwoc(List<WriteoffInfo2> writeoffInfos) throws Exception {
        String url = null;
        if ("sc".equals(deployTo)) {
            url = NHURL + "/syncWriteoffInfos/rest/energyMeter/syncWriteoffInfos";
        }
        if ("ln".equals(deployTo)) {
            url = NHURL + "/nh.cb.xx/syncWriteoffInfos";
        }
        Map<String, Object> param = new HashMap<>();
        param.put("writeoffInfos", writeoffInfos);
        String res = doHttpForCopyMeter(param, url);
        return res;
    }

    /**
     * 用于能耗系统向数据平台推送电表基础数据，
     * 数据平台接收数据后，用于电表的展示及计算。
     */
    public String syncEnergyMeterInfos(List<MeterInfo> meterInfos) throws Exception {

//        String url = URL + "/syncEnergyMeterInfos/rest/energyMeter/syncEnergyMeterInfos";
        String url = URL;
        if ("sc".equals(deployTo)) {
            url = URL + "/syncWriteoffInfos/rest/energyMeter/syncEnergyMeterInfos";
        }
        if ("ln".equals(deployTo)) {
//            url += "/mss.sync.meter/syncEnergyMeterInfos";
            url += "/syncEnergyMeterInfos";
        }
        log.info("syncEnergyMeterInfos url{}:", url);
        Map<String, Object> param = new HashMap<>();
        param.put("meterInfos", meterInfos);
        String res = doHttp(param, url);
        return res;
    }

    public String syncEnergyMeterInfosAddBillid(List<MeterInfo> meterInfos, Long billid) throws Exception {

//        String url = URL + "/syncEnergyMeterInfos/rest/energyMeter/syncEnergyMeterInfos";
        String url = URL;
        if ("sc".equals(deployTo)) {
            url = URL + "/syncWriteoffInfos/rest/energyMeter/syncEnergyMeterInfos";
        }
        if ("ln".equals(deployTo)) {
//            url += "/mss.sync.meter/syncEnergyMeterInfos";
            url += "/syncEnergyMeterInfos";
        }
        log.info("syncEnergyMeterInfos url{}:", url);
        Map<String, Object> param = new HashMap<>();
        param.put("meterInfos", meterInfos);

        log.info("param add billid:{}", billid);
        param.put("billid", billid);
        String res = doHttp(param, url);
        return res;
    }

    /**
     * 同步计量设备
     *
     * @param meterInfos
     * @return
     * @throws Exception
     */
    public String syncEnergyMeterInfos2(List<? extends MeterInfo2> meterInfos) throws Exception {

        String url = null;
        if ("sc".equals(deployTo)) {
            url = NHURL + "/syncMeterInfos/rest/energyMeter/syncEnergyMeterInfos";
        }
        if ("ln".equals(deployTo)) {
            url = NHURL + "/jl.sb.xx/syncEnergyMeterInfos";
        }
        Map<String, Object> param = new HashMap<>();
        param.put("meterInfos", meterInfos);
        String res = doHttpForMeterEqu(param, url);
        return res;
    }

    /**
     * 同步计量设备
     *
     * @param meterInfos
     * @return
     * @throws Exception
     */
    public String syncEnergyMeterInfos2New(Long billId, List<? extends MeterInfo2> meterInfos) throws Exception {

        String url = null;
        if ("sc".equals(deployTo)) {
            url = NHURL + "/syncMeterInfos/rest/energyMeter/syncEnergyMeterInfos";
        }
        if ("ln".equals(deployTo)) {
            url = NHURL + "/jl.sb.xx/syncEnergyMeterInfos";
        }
        Map<String, Object> param = new HashMap<>();
        param.put("meterInfos", meterInfos);
        if (ObjectUtil.isNotEmpty(billId)) {
            param.put("billId", billId.toString());
        } else {
            param.put("billId", "0000000000");
        }
        String res = doHttpForMeterEquNew(param, url);
        return res;
    }
    /**
     * 同步双碳电表全量数据
     *
     * @param subjectcode
     * @param meterInfos
     * @return
     * @throws Exception
     */
    public String syncEnergyMeterInfoTwoc(String subjectcode, List<? extends TwoCFlag> meterInfos) throws Exception {

        String url = null;
        if ("sc".equals(deployTo)) {
            url = TwocMeterUrl;
        }
        if ("ln".equals(deployTo)) {
            url = TwocMeterUrl;
        }
        log.info("url{}", url);
        Map<String, Object> param = new HashMap<>();
        param.put("meterInfos", meterInfos);
        String res = doHttpForMeterEquTowc(subjectcode, param, url);
        return res;
    }

    /**
     * 同步双碳 电表实际用电数据 全量数据
     *
     * @param subjectcode
     * @param meterInfos
     * @return
     * @throws Exception
     */
    public String syncMeterDateInforTwocAll(String subjectcode, List<? extends TwoCFlag> meterInfos) throws Exception {

        String url = null;
        if ("sc".equals(deployTo)) {
            url = TwocPowerUrl;
        }
        if ("ln".equals(deployTo)) {
            url = TwocPowerUrl;
        }
        Map<String, Object> param = new HashMap<>();
        param.put("meterDatas", meterInfos);
        String res = doHttpForMeterEquTowcAll(subjectcode, param, url);
        return res;
    }

    /**
     * 同步双碳 其它能耗数据
     *
     * @param subjectcode
     * @param meterInfos
     * @return
     * @throws Exception
     */
    public String syncMeterOtherDateInforTwocAll(String subjectcode, List<? extends TwoCFlag> meterInfos) throws Exception {

        String url = null;
        if ("sc".equals(deployTo)) {
            url = TwocOtherUrl;
        }
        if ("ln".equals(deployTo)) {
            url = TwocOtherUrl;
        }
        Map<String, Object> param = new HashMap<>();
        param.put("otherEnergyDatas", meterInfos);
        String res = doHttpForMeterOtherEquTowcAll(subjectcode, param, url);
        return res;
    }

    /**
     * 同步双碳 废水废气数据
     *
     * @param subjectcode
     * @param meterInfos
     * @return
     * @throws Exception
     */
    public String syncMeterPollutionDateInforTwocAll(String subjectcode, List<? extends TwoCFlag> meterInfos) throws Exception {

        String url = null;
        if ("sc".equals(deployTo)) {
            url = TwocPollutionUrl;
        }
        if ("ln".equals(deployTo)) {
            url = TwocPollutionUrl;
        }
        Map<String, Object> param = new HashMap<>();
        param.put("polluteGovernDatas", meterInfos);
        String res = doHttpForMeterPollutionEquTowcAll(subjectcode, param, url);
        return res;
    }

    public String syncLnTest(String id) throws Exception {

        String url = null;
        if ("ln".equals(deployTo)) {
            if ("1".equals(id)) {
                url = LnTest1Url;
            } else {
                url = LnTest2Url;
            }
        }

        Map<String, Object> param = new HashMap<>();
        String res = dohttpLnTest("1234", param, url);
        return res;
    }

    /**
     * 用于能耗系统向大数据平台 推送 电价上传信息
     */
    public String syncEnergyMeterPriceInfos(List<PowerElePriceItem> items) throws Exception {
        String url = URL;
        if ("ln".equals(deployTo))
//            url += "/mss.sync.ele/syncEnergyMeterPriceInfos";
            url += "/syncEnergyMeterPriceInfos";
        if ("sc".equals(deployTo)) {
            url = URL + "/syncWriteoffInfos/rest/energyMeter/syncEnergyMeterPriceInfos";
        }
        Map<String, Object> param = new HashMap<>();
        param.put("meterInfos", items);
        String res = doHttpSync(param, url);
        return res;
    }

    private String doHttpSync(Map<String, Object> param, String url) {
        RestTemplate rest = new RestTemplate();
        rest.getMessageConverters().add(new StringHttpMessageConverter(Charset.forName(CHARSET)));
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("X-APP-ID", APPKey);
        headers.add("X-APP-KEY", AppSecret);
        String msgId = msgF + System.currentTimeMillis();

        List<PowerElePriceItem> items = (List<PowerElePriceItem>) param.get("meterInfos");
        HttpEntity<Object> requestEntity = new HttpEntity<>(items, headers);

        //System.out.println(JSON.toJSONString(requestEntity));
        insertLog("提交接口集团数据平台doHttp-price", "doHttpprice", JSON.toJSONString(requestEntity));

        //System.out.println("第三方接口headet:" + JSON.toJSONString(headers));
        //System.out.println("url:" + url);
        //System.out.println("查询发送时间" + System.currentTimeMillis());
        String resJson = rest.postForObject(url, requestEntity, String.class);
        //System.out.println("查询接受时间" + System.currentTimeMillis());

        //System.out.println("resJson:" + resJson);
        insertLog("提交接口集团数据平台doHttp-price-rtl ", "doHttpprice", resJson);
        return resJson + msgId;
    }

    /**
     * 查询电价明细信息
     */
    public AjaxResult queryEnergyMeterInfo(String budgetSet, List<String> energyMeterCodes) throws Exception {
        String url = URL;
        if ("ln".equals(deployTo))
//            url += "/mss.refer.ele/getEnergyMeterPriceInfos";
            url += "/getEnergyMeterPriceInfos";
        if ("sc".equals(deployTo)) {
            url = URL + "/syncWriteoffInfos/rest/energyMeter/getEnergyMeterPriceInfos";
        }
        Map<String, Object> param = new HashMap<>();
        param.put("provinceCode", PROVINCECODE);
        param.put("budgetSet", budgetSet);
        param.put("energyMeterCodes", energyMeterCodes);
        String res = doHttpForQuery(param, url);
        return AjaxResult.success(res);

    }

    //大数据平台查询电价明细信息
    private String doHttpForQuery(Map<String, Object> param, String url) {
        RestTemplate rest = new RestTemplate();
        rest.getMessageConverters().add(new StringHttpMessageConverter(Charset.forName(CHARSET)));
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("X-APP-ID", APPKey);
        headers.add("X-APP-KEY", AppSecret);
        String msgId = msgF + System.currentTimeMillis();
        HttpEntity<Object> requestEntity = new HttpEntity<>(param, headers);
        //System.out.println(JSON.toJSONString(requestEntity));

        //System.out.println("第三方接口headet:" + JSON.toJSONString(headers));
        insertLog("提交接口集团数据平台doHttp-price_q", "doHttp", JSON.toJSONString(requestEntity));
        //System.out.println("url:" + url);

        //System.out.println("查询发送时间" + System.currentTimeMillis());
        String resJson = rest.postForObject(url, requestEntity, String.class);
        //System.out.println("查询接受时间" + System.currentTimeMillis());

        //System.out.println("resJson:" + resJson);
        insertLog("提交接口集团数据平台doHttp-rtl_price_q", "doHttp", resJson);
        return resJson + msgId;
    }

    private void insertLog(String title, String method, String errorMes) {
        OperLog model = new OperLog();
        model.setOperName("mss");
        model.setTitle(title);
        model.setMethod(method);
        model.setErrorMsg(errorMes);
        model.setOperTime(new Date());
        //System.out.println("method："+method);
        //System.out.println("errorMes："+errorMes);
        operLogMapper.insert(model);
    }

    private void insertLog2(String title, String method, String errorMes, String URL, String token) {
        OperLog model = new OperLog();
        model.setOperName("mss");
        model.setTitle(title);
        model.setMethod(method);
        model.setErrorMsg(errorMes);
        model.setOperTime(new Date());
        model.setOperUrl(URL);
        model.setOperParam(token);
        //System.out.println("method："+method);
        //System.out.println("errorMes："+errorMes);
        operLogMapper.insert(model);
    }

    /**
     * 同步计量设备
     *
     * @param meterEquipmentInfos
     * @return
     */
    public String syncMeterEquipmentInfos(List<MeterEquipmentInfo> meterEquipmentInfos) {
        String url = null;
        if ("sc".equals(deployTo)) {
            url = NHURL + "/syncMeterInfos/rest/energyMeter/syncEnergyMeterInfos";
        }
        if ("ln".equals(deployTo)) {
            url = NHURL + "/jl.sb.xx/syncEnergyMeterInfos";
        }
        Map<String, Object> param = new HashMap<>();
        param.put("meterInfos", meterEquipmentInfos);
        String res = null;
        try {
            res = doHttpForMeterEqu(param, url);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return res;

    }

    /**
     * 同步计量设备
     *
     * @param param
     * @param url
     * @return
     */
    private String doHttpForMeterEqu(Map<String, Object> param, String url) {
        //        SimpleClientHttpRequestFactory httpRequestFactory = new SimpleClientHttpRequestFactory();
//        httpRequestFactory.setConnectTimeout(3000);
//        httpRequestFactory.setReadTimeout(3000);
        RestTemplate rest = new RestTemplate();
        rest.getMessageConverters().add(new StringHttpMessageConverter(Charset.forName(CHARSET)));
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("X-APP-ID", APPKey);
        headers.add("X-APP-KEY", AppSecret);
        param.put("provinceCode", PROVINCECODE);
        String msgId = "msgId_" + IdGenerator.getNextIdAsString();
        param.put("msgId", msgId);
        log.info("msgId:{}", msgId);
        // 数据转换

        List<MeterInfo3> infosForDb = (List<MeterInfo3>) param.get("meterInfos");
        List<MeterInfo2> infosForSync = infosForDb.stream().map(meterInfo3 -> {
            MeterInfo2 meterInfo2 = new MeterInfo2();
            BeanUtils.copyProperties(meterInfo3, meterInfo2);
            return meterInfo2;
        }).collect(toList());
        param.put("meterInfos", infosForSync);
        HttpEntity<Object> requestEntity = new HttpEntity<>(param, headers);
        System.out.println("url:" + url);
        String resJson = null;
        String time = TimeUtils.getNowTime();

        try {
            log.info("开始同步");
            resJson = rest.postForObject(url, requestEntity, String.class);
        } catch (RestClientException e) {
            //失败
            //数据转换
            log.info("集团接口连接异常");
            infosForDb.stream().forEach(meterInfo2 -> {
                meterInfo2.setSyncFlag(2);
                meterInfo2.setFailMag("未能连接到集团接口");
            });
            //批量更新
            SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH);
            try {
                PowerModleInitMapper mapper = sqlSession.getMapper(PowerModleInitMapper.class);
                infosForDb.stream().forEach(meterInfo3 -> {
                    meterInfo3.setMsgId(msgId);
                    if (meterInfo3.getType().equals("1")) {
                        mapper.updateForMeterInfo(meterInfo3);
                    }
                    if (meterInfo3.getType().equals("2")) {
                        mapper.updateForMeterInfoAll(meterInfo3);
                    }
                    if (meterInfo3.getType().equals("3")) {
                        mapper.updateForMeterInfoAll(meterInfo3);
                    }
                });
                sqlSession.commit();
            } finally {
                sqlSession.close();
            }
            //日志结果
            insertResult("计量设备接口", 1, "", time, "失败", JsonUtil.pojoToJsonString(infosForSync), "未能连接到集团接口", infosForDb.size());
            return "集团接口异常";
        }
        //操作结果处理
        log.info("接受到同步结果");

        if (resJson.contains("\"code\":\"0\"")) {
            //成功
            //数据转换
            log.info("判定成功");
            infosForDb.stream().forEach(meterInfo3 -> {
                meterInfo3.setSyncFlag(1);
                meterInfo3.setFailMag("同步成功");
                meterInfo3.setMsgId(msgId);
            });
            //批量更新
            SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH);
            try {
                PowerModleInitMapper mapper = sqlSession.getMapper(PowerModleInitMapper.class);
                infosForDb.stream().forEach(meterInfo3 -> {
                    if (meterInfo3.getType().equals("1")) {
                        mapper.updateForMeterInfo(meterInfo3);
                    }
                    if (meterInfo3.getType().equals("2")) {
                        mapper.updateForMeterInfoAll(meterInfo3);
                    }
                    if (meterInfo3.getType().equals("3")) {
                        mapper.updateForMeterInfoAll(meterInfo3);
                    }
                });
                sqlSession.commit();
            } finally {
                sqlSession.close();
            }
            insertResult("计量设备接口", 1, "", time, "成功", JsonUtil.pojoToJsonString(infosForSync), "", infosForSync.size());
        }
        if (resJson.contains("\"code\":\"-1\"")) {
            //失败
            //数据转换
            log.info("判定失败");
            String finalResJson = resJson;
            infosForDb.stream().forEach(meterInfo3 -> {
                meterInfo3.setSyncFlag(2);
                meterInfo3.setFailMag(finalResJson);
            });
            //批量更新
            SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH);
            try {
                PowerModleInitMapper mapper = sqlSession.getMapper(PowerModleInitMapper.class);
                infosForDb.stream().forEach(meterInfo3 -> {
                    if (meterInfo3.getType().equals("1")) {
                        mapper.updateForMeterInfo(meterInfo3);
                    }
                    if (meterInfo3.getType().equals("2")) {
                        mapper.updateForMeterInfoAll(meterInfo3);
                    }
                    if (meterInfo3.getType().equals("3")) {
                        mapper.updateForMeterInfoAll(meterInfo3);
                    }
                });
                sqlSession.commit();
            } finally {
                sqlSession.close();
            }
            insertResult("计量设备接口", 1, "", time, "失败", JsonUtil.pojoToJsonString(infosForSync), resJson, infosForSync.size());
        }
        return msgId;
    }
    /**
     * 同步计量设备
     *
     * @param param
     * @param url
     * @return
     */
    private String doHttpForMeterEquNew(Map<String, Object> param, String url) {
        //        SimpleClientHttpRequestFactory httpRequestFactory = new SimpleClientHttpRequestFactory();
//        httpRequestFactory.setConnectTimeout(3000);
//        httpRequestFactory.setReadTimeout(3000);
        RestTemplate rest = new RestTemplate();
        rest.getMessageConverters().add(new StringHttpMessageConverter(Charset.forName(CHARSET)));
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("X-APP-ID", APPKey);
        headers.add("X-APP-KEY", AppSecret);
        String operName = "MSS_";
        if (ObjectUtil.isNotEmpty(param.get("billId"))) {
            operName = operName + param.get("billId").toString();
            param.remove("billId");
        }
        param.put("provinceCode", PROVINCECODE);
        String msgId = "msgId_" + IdGenerator.getNextIdAsString();
        param.put("msgId", msgId);
        log.info("msgId:{}", msgId);
        // 数据转换

        List<MeterInfo3> infosForDb = (List<MeterInfo3>) param.get("meterInfos");
        List<MeterInfo2> infosForSync = infosForDb.stream().map(meterInfo3 -> {
            MeterInfo2 meterInfo2 = new MeterInfo2();
            BeanUtils.copyProperties(meterInfo3, meterInfo2);
            return meterInfo2;
        }).collect(toList());
        param.put("meterInfos", infosForSync);
        HttpEntity<Object> requestEntity = new HttpEntity<>(param, headers);
        System.out.println("url:" + url);
        String resJson = null;
        String time = TimeUtils.getNowTime();

        try {
            log.info("开始同步");
            resJson = rest.postForObject(url, requestEntity, String.class);
        } catch (RestClientException e) {
            //失败
            //数据转换
            log.info("集团接口连接异常");
            infosForDb.stream().forEach(meterInfo2 -> {
                meterInfo2.setSyncFlag(2);
                meterInfo2.setFailMag("未能连接到集团接口");
            });
            //批量更新
            SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH);
            try {
                PowerModleInitMapper mapper = sqlSession.getMapper(PowerModleInitMapper.class);
                infosForDb.stream().forEach(meterInfo3 -> {
                    if (meterInfo3.getType().equals("1")) {
                        mapper.updateForMeterInfo(meterInfo3);
                    }
                    if (meterInfo3.getType().equals("2")) {
                        mapper.updateForMeterInfo(meterInfo3);
                    }
                    if (meterInfo3.getType().equals("3")) {
                        mapper.updateForMeterInfoAll(meterInfo3);
                    }
                });
                sqlSession.commit();
            } finally {
                sqlSession.close();
            }
            //日志结果
            insertResult("计量设备接口", 1, operName, time, "失败", JsonUtil.pojoToJsonString(infosForSync), "未能连接到集团接口", infosForDb.size());
            return "集团接口异常";
        }
        //操作结果处理
        log.info("接受到同步结果");

        if (resJson.contains("\"code\":\"0\"")) {
            //成功
            //数据转换
            log.info("判定成功");
            infosForDb.stream().forEach(meterInfo3 -> {
                meterInfo3.setSyncFlag(1);
                meterInfo3.setFailMag("同步成功");
            });
            //批量更新
            SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH);
            try {
                PowerModleInitMapper mapper = sqlSession.getMapper(PowerModleInitMapper.class);
                infosForDb.stream().forEach(meterInfo3 -> {
                    if (meterInfo3.getType().equals("1")) {
                        meterInfo3.setCreateTime(new Date());
                        mapper.addMeterInfoAllJt(meterInfo3);
                        mapper.updateForMeterInfo(meterInfo3);
                    }
                    if (meterInfo3.getType().equals("2")) {
                        mapper.updateMeterInfoAllJt(meterInfo3);
                        mapper.updateForMeterInfo(meterInfo3);
                    }
                    if (meterInfo3.getType().equals("3")) {
                        mapper.updateForMeterInfoAll(meterInfo3);
                    }
                });
                sqlSession.commit();
            } finally {
                sqlSession.close();
            }
            insertResult("计量设备接口", 1, operName, time, "成功", JsonUtil.pojoToJsonString(infosForSync), resJson, infosForSync.size());
        }
        if (resJson.contains("\"code\":\"-1\"")) {
            //失败
            //数据转换
            log.info("判定失败");
            String finalResJson = resJson;
            infosForDb.stream().forEach(meterInfo3 -> {
                meterInfo3.setSyncFlag(2);
                meterInfo3.setFailMag(finalResJson);
            });
            //批量更新
            SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH);
            try {
                PowerModleInitMapper mapper = sqlSession.getMapper(PowerModleInitMapper.class);
                infosForDb.stream().forEach(meterInfo3 -> {
                    if (meterInfo3.getType().equals("1")) {
                        mapper.updateForMeterInfo(meterInfo3);
                    }
                    if (meterInfo3.getType().equals("2")) {
                        mapper.updateForMeterInfo(meterInfo3);
                    }
                    if (meterInfo3.getType().equals("3")) {
                        mapper.updateForMeterInfoAll(meterInfo3);
                    }
                });
                sqlSession.commit();
            } finally {
                sqlSession.close();
            }
            insertResult("计量设备接口", 1, operName, time, "失败", JsonUtil.pojoToJsonString(infosForSync), resJson, infosForSync.size());
        }
        return msgId;
    }

    /**
     * 同步双碳接口电表全量数据
     *
     * @param subjectcode
     * @param param
     * @param url
     * @return
     */
    private String doHttpForMeterEquTowc(String subjectcode, Map<String, Object> param, String url) {
        RestTemplate rest = new RestTemplate();
        rest.getMessageConverters().add(new StringHttpMessageConverter(Charset.forName(CHARSET)));
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("X-APP-ID", TwocAPPID);
        headers.add("X-APP-KEY", TwocAppKEY);
        param.put("subjectCode", subjectcode);
        String msgId = subjectcode + System.currentTimeMillis() + IdGenerator.getNextIdAsString().substring(0, 7);
        param.put("msgId", msgId);
        log.info("appId:{}", TwocAPPID);
        log.info("appkey:{}", TwocAppKEY);
        log.info("msgId:{}", msgId);
        // 数据转换

        List<Twoc> infosForDb = (List<Twoc>) param.get("meterInfos");
        List<MeterInofTwoC> infosForSync = infosForDb.stream().map(infoDb -> {
            MeterInofTwoC infoSync = new MeterInofTwoC();
            infoSync.setUsage(infoDb.getUsagecopy());
            BeanUtil.copyProperties(infoDb, infoSync, true);
            return infoSync;
        }).collect(toList());
        param.put("meterInfos", infosForSync);
        HttpEntity<Object> requestEntity = new HttpEntity<>(param, headers);

        log.info("url:{}", url);
        String resJson = null;

        try {
            log.info("开始同步");
            resJson = rest.postForObject(url, requestEntity, String.class);
        } catch (RestClientException e) {
            log.info("未能连接到集团双碳接口");
            String time = TimeUtils.getNowTime();
            //失败
            //数据转换
            infosForDb.stream().forEach(infodb -> {
                infodb.setSyncflag(2);
                infodb.setFailmag("未能连接到集团接口");
            });
            //批量更新
            SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH);
            try {
                PowerModleInitMapper mapper = sqlSession.getMapper(PowerModleInitMapper.class);
                infosForDb.stream().forEach(infoDb -> {
                    if (infoDb.getType().equals("1")) {
                        twocMapper.updateForMeterInfo(infoDb);
                    }
                    if (infoDb.getType().equals("3")) {
                        twocMapper.updateForMeterInfoAll(infoDb);
                    }
                });
                sqlSession.commit();
            } finally {
                sqlSession.close();
            }
            //日志结果
            insertResult("双碳电表全量接口", 1, "", time, "失败", JSON.toJSONString(requestEntity), "未能连接到集团接口", infosForDb.size());
            return "集团接口异常";
        }
        //操作结果处理
        log.info("接受到同步结果{}", resJson);
        String time = TimeUtils.getNowTime();
        if (resJson.contains("操作成功")) {
            //成功
            //数据转换
            log.info("判定成功");
            infosForDb.stream().forEach(infoDb -> {
                infoDb.setSyncflag(1);
                infoDb.setFailmag("同步成功");
            });
            //批量更新
            SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH);
            try {
                PowerModleInitMapper mapper = sqlSession.getMapper(PowerModleInitMapper.class);
                infosForDb.stream().forEach(meterInfo3 -> {
                    if (meterInfo3.getType().equals("1")) {
                        twocMapper.updateForMeterInfo(meterInfo3);
                    }
                    if (meterInfo3.getType().equals("3")) {
                        twocMapper.updateForMeterInfoAll(meterInfo3);
                    }
                });
                sqlSession.commit();
            } finally {
                sqlSession.close();
            }
            insertResult("双碳电表全量接口", 1, "", time, "成功", JSON.toJSONString(requestEntity), "", infosForSync.size());
        } else {
            //失败
            //数据转换
            log.info("判定失败");
            String finalResJson = resJson;
            infosForDb.stream().forEach(infoDb -> {
                infoDb.setSyncflag(2);
                infoDb.setFailmag(finalResJson);
            });
            //批量更新
            SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH);
            try {
                PowerModleInitMapper mapper = sqlSession.getMapper(PowerModleInitMapper.class);
                infosForDb.stream().forEach(infoDb -> {
                    if (infoDb.getType().equals("1")) {
                        twocMapper.updateForMeterInfo(infoDb);
                    }
                    if (infoDb.getType().equals("3")) {
                        twocMapper.updateForMeterInfoAll(infoDb);
                    }
                });
                sqlSession.commit();
            } finally {
                sqlSession.close();
            }
            insertResult("双碳电表全量接口", 1, "", time, "失败", JSON.toJSONString(requestEntity), resJson, infosForSync.size());
        }
        return msgId;
    }

    /**
     * 同步双碳接口电表 实际用电数据 全量数据
     *
     * @param subjectcode
     * @param param
     * @param url
     * @return
     */
    private String doHttpForMeterEquTowcAll(String subjectcode, Map<String, Object> param, String url) {
        RestTemplate rest = new RestTemplate();
        rest.getMessageConverters().add(new StringHttpMessageConverter(Charset.forName(CHARSET)));
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("X-APP-ID", TwocAPPID);
        headers.add("X-APP-KEY", TwocAppKEY);
        log.info("appId:{}", TwocAPPID);
        log.info("appkey:{}", TwocAppKEY);
        param.put("subjectCode", subjectcode);
        String msgId = subjectcode + System.currentTimeMillis() + IdGenerator.getNextIdAsString().substring(0, 7);
        param.put("msgId", msgId);
        log.info("msgId:{}", msgId);
        // 数据转换

        List<Meterdatesfortwoc> infosForDb = (List<Meterdatesfortwoc>) param.get("meterDatas");
        List<MeterDateForTowcSyncEnd> infosForSync = infosForDb.stream().map(infoDb -> {
            MeterDateForTowcSyncEnd infoSync = new MeterDateForTowcSyncEnd();
            BeanUtil.copyProperties(infoDb, infoSync, true);
            return infoSync;
        }).collect(toList());
        param.put("meterDatas", infosForSync);
        HttpEntity<Object> requestEntity = new HttpEntity<>(param, headers);

        log.info("url:{}", url);
        String resJson = null;

        try {
            log.info("开始同步");
            resJson = rest.postForObject(url, requestEntity, String.class);
        } catch (RestClientException e) {
            String time = TimeUtils.getNowTime();
            //失败
            //数据转换
            infosForDb.stream().forEach(infodb -> {
                infodb.setSyncflag(2);
                infodb.setFailmag("未能连接到集团接口");
            });
            //批量更新
            SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH);
            try {
                PowerModleInitMapper mapper = sqlSession.getMapper(PowerModleInitMapper.class);
                infosForDb.stream().forEach(infoDb -> {
                    meterdatesfortwocMapper.updateForMeterInfoAll(infoDb);

                });
                sqlSession.commit();
            } finally {
                sqlSession.close();
            }
            //日志结果
            insertResult("双碳电表实际用电接口", 2, "", time, "失败", JSON.toJSONString(requestEntity), "未能连接到集团接口", infosForDb.size());
            return "集团接口异常";
        }
        //操作结果处理
        log.info("接受到同步结果{}", resJson);
        String time = TimeUtils.getNowTime();
        if (resJson.contains("操作成功")) {
            //成功
            //数据转换
            log.info("判定成功");
            infosForDb.stream().forEach(infoDb -> {
                infoDb.setSyncflag(1);
                infoDb.setFailmag("同步成功");
            });
            //批量更新
            SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH);
            try {
                PowerModleInitMapper mapper = sqlSession.getMapper(PowerModleInitMapper.class);
                infosForDb.stream().forEach(meterInfo3 -> {
                    meterdatesfortwocMapper.updateForMeterInfoAll(meterInfo3);
                });
                sqlSession.commit();
            } finally {
                sqlSession.close();
            }
            insertResult("双碳电表实际用电接口", 2, "", time, "成功", JSON.toJSONString(requestEntity), "", infosForSync.size());
        } else {
            //失败
            //数据转换
            log.info("判定失败");
            String finalResJson = resJson;
            infosForDb.stream().forEach(infoDb -> {
                infoDb.setSyncflag(2);
                infoDb.setFailmag(finalResJson);
            });
            //批量更新
            SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH);
            try {
                PowerModleInitMapper mapper = sqlSession.getMapper(PowerModleInitMapper.class);
                infosForDb.stream().forEach(infoDb -> {
                    meterdatesfortwocMapper.updateForMeterInfoAll(infoDb);

                });
                sqlSession.commit();
            } finally {
                sqlSession.close();
            }
            insertResult("双碳电表实际用电接口", 2, "", time, "失败", JSON.toJSONString(requestEntity), resJson, infosForSync.size());
        }
        return msgId;
    }

    /**
     * 同步双碳接口其它能耗数据
     *
     * @param subjectcode
     * @param param
     * @param url
     * @return
     */
    private String doHttpForMeterOtherEquTowcAll(String subjectcode, Map<String, Object> param, String url) {
        RestTemplate rest = new RestTemplate();
        rest.getMessageConverters().add(new StringHttpMessageConverter(Charset.forName(CHARSET)));
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("X-APP-ID", TwocAPPID);
        headers.add("X-APP-KEY", TwocAppKEY);
        param.put("subjectCode", subjectcode);
        String msgId = subjectcode + System.currentTimeMillis() + IdGenerator.getNextIdAsString().substring(0, 7);
        param.put("msgId", msgId);
        log.info("appId:{}", TwocAPPID);
        log.info("appkey:{}", TwocAppKEY);
        log.info("msgId:{}", msgId);
        // 数据转换

        List<MeterOtherDatesfortwoc> infosForDb = (List<MeterOtherDatesfortwoc>) param.get("otherEnergyDatas");
        List<MeterOtherDatesfortwocSync> infosForSync = infosForDb.stream().map(infoDb -> {
            MeterOtherDatesfortwocSync infoSync = new MeterOtherDatesfortwocSync();
            BeanUtil.copyProperties(infoDb, infoSync, true);
            return infoSync;
        }).collect(toList());
        param.put("otherEnergyDatas", infosForSync);
        HttpEntity<Object> requestEntity = new HttpEntity<>(param, headers);

        log.info("url:{}", url);
        String resJson = null;

        try {
            log.info("开始同步");
            resJson = rest.postForObject(url, requestEntity, String.class);
        } catch (RestClientException e) {
            String time = TimeUtils.getNowTime();
            //失败
            //数据转换
            infosForDb.stream().forEach(infodb -> {
                infodb.setSyncflag(2);
                infodb.setFailmag("未能连接到集团接口");
            });
            //批量更新
            SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH);
            try {
                PowerModleInitMapper mapper = sqlSession.getMapper(PowerModleInitMapper.class);
                infosForDb.stream().forEach(infoDb -> {
                    meterOtherDatesfortwocMapper.updateForMeterInfoAll(infoDb);

                });
                sqlSession.commit();
            } finally {
                sqlSession.close();
            }
            //日志结果
            insertResult("双碳其它能耗数据接口", 2, "", time, "失败", JSON.toJSONString(requestEntity), "未能连接到集团接口", infosForDb.size());
            return "集团接口异常";
        }
        //操作结果处理
        log.info("接受到同步结果{}", resJson);
        String time = TimeUtils.getNowTime();
        if (resJson.contains("操作成功")) {
            //成功
            //数据转换
            log.info("判定成功");
            infosForDb.stream().forEach(infoDb -> {
                infoDb.setSyncflag(1);
                infoDb.setFailmag("同步成功");
            });
            //批量更新
            SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH);
            try {
                PowerModleInitMapper mapper = sqlSession.getMapper(PowerModleInitMapper.class);
                infosForDb.stream().forEach(meterInfo3 -> {
                    meterOtherDatesfortwocMapper.updateForMeterInfoAll(meterInfo3);
                });
                sqlSession.commit();
            } finally {
                sqlSession.close();
            }
            insertResult("双碳其它能耗数据接口", 2, "", time, "成功", JSON.toJSONString(requestEntity), "", infosForSync.size());
        } else {
            //失败
            //数据转换
            log.info("判定失败");
            String finalResJson = resJson;
            infosForDb.stream().forEach(infoDb -> {
                infoDb.setSyncflag(2);
                infoDb.setFailmag(finalResJson);
            });
            //批量更新
            SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH);
            try {
                PowerModleInitMapper mapper = sqlSession.getMapper(PowerModleInitMapper.class);
                infosForDb.stream().forEach(infoDb -> {
                    meterOtherDatesfortwocMapper.updateForMeterInfoAll(infoDb);

                });
                sqlSession.commit();
            } finally {
                sqlSession.close();
            }
            insertResult("双碳其它能耗数据接口", 2, "", time, "失败", JSON.toJSONString(requestEntity), resJson, infosForSync.size());
        }
        return msgId;
    }

    /**
     * 同步双碳接口废水废气数据
     *
     * @param subjectcode
     * @param param
     * @param url
     * @return
     */
    private String doHttpForMeterPollutionEquTowcAll(String subjectcode, Map<String, Object> param, String url) {
        RestTemplate rest = new RestTemplate();
        rest.getMessageConverters().add(new StringHttpMessageConverter(Charset.forName(CHARSET)));
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("X-APP-ID", TwocAPPID);
        headers.add("X-APP-KEY", TwocAppKEY);
        param.put("subjectCode", subjectcode);
        String msgId = subjectcode + System.currentTimeMillis() + IdGenerator.getNextIdAsString().substring(0, 7);
        param.put("msgId", msgId);
        log.info("appId:{}", TwocAPPID);
        log.info("appkey:{}", TwocAppKEY);
        log.info("msgId:{}", msgId);
        // 数据转换

        List<MeterPollutionDatesfortwoc> infosForDb = (List<MeterPollutionDatesfortwoc>) param.get("polluteGovernDatas");
        List<MeterPollutionDatesfortwocSync> infosForSync = infosForDb.stream().map(infoDb -> {
            MeterPollutionDatesfortwocSync infoSync = new MeterPollutionDatesfortwocSync();
            BeanUtil.copyProperties(infoDb, infoSync, true);
            return infoSync;
        }).collect(toList());
        param.put("polluteGovernDatas", infosForSync);
        HttpEntity<Object> requestEntity = new HttpEntity<>(param, headers);

        log.info("url:{}", url);
        String resJson = null;

        try {
            log.info("开始同步");
            resJson = rest.postForObject(url, requestEntity, String.class);
        } catch (RestClientException e) {
            String time = TimeUtils.getNowTime();
            //失败
            //数据转换
            infosForDb.stream().forEach(infodb -> {
                infodb.setSyncflag(2);
                infodb.setFailmag("未能连接到集团接口");
            });
            //批量更新
            SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH);
            try {
                PowerModleInitMapper mapper = sqlSession.getMapper(PowerModleInitMapper.class);
                infosForDb.stream().forEach(infoDb -> {
                    meterPollutionDatesfortwocMapper.updateForMeterInfoAll(infoDb);

                });
                sqlSession.commit();
            } finally {
                sqlSession.close();
            }
            //日志结果
            insertResult("双碳废水废气数据接口", 2, "", time, "失败", JSON.toJSONString(requestEntity), "未能连接到集团接口", infosForDb.size());
            return "集团接口异常";
        }
        //操作结果处理
        log.info("接受到同步结果{}", resJson);
        String time = TimeUtils.getNowTime();
        if (resJson.contains("操作成功")) {
            //成功
            //数据转换
            log.info("判定成功");
            infosForDb.stream().forEach(infoDb -> {
                infoDb.setSyncflag(1);
                infoDb.setFailmag("同步成功");
            });
            //批量更新
            SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH);
            try {
                PowerModleInitMapper mapper = sqlSession.getMapper(PowerModleInitMapper.class);
                infosForDb.stream().forEach(meterInfo3 -> {
                    meterPollutionDatesfortwocMapper.updateForMeterInfoAll(meterInfo3);
                });
                sqlSession.commit();
            } finally {
                sqlSession.close();
            }
            insertResult("双碳废水废气数据接口", 2, "", time, "成功", JSON.toJSONString(requestEntity), "", infosForSync.size());
        } else {
            //失败
            //数据转换
            log.info("判定失败");
            String finalResJson = resJson;
            infosForDb.stream().forEach(infoDb -> {
                infoDb.setSyncflag(2);
                infoDb.setFailmag(finalResJson);
            });
            //批量更新
            SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH);
            try {
                PowerModleInitMapper mapper = sqlSession.getMapper(PowerModleInitMapper.class);
                infosForDb.stream().forEach(infoDb -> {
                    meterPollutionDatesfortwocMapper.updateForMeterInfoAll(infoDb);

                });
                sqlSession.commit();
            } finally {
                sqlSession.close();
            }
            insertResult("双碳废水废气数据接口", 2, "", time, "失败", JSON.toJSONString(requestEntity), resJson, infosForSync.size());
        }
        return msgId;
    }

    private String dohttpLnTest(String subjectcode, Map<String, Object> param, String url) {
        RestTemplate rest = new RestTemplate();
        rest.getMessageConverters().add(new StringHttpMessageConverter(Charset.forName(CHARSET)));
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("X-APP-ID", APPKey);
        headers.add("X-APP-KEY", AppSecret);
        param.put("subjectCode", subjectcode);
        String msgId = subjectcode + System.currentTimeMillis() + IdGenerator.getNextIdAsString().substring(0, 7);
        param.put("msgId", msgId);
        log.info("appId:{}", TwocAPPID);
        log.info("appkey:{}", TwocAppKEY);
        log.info("msgId:{}", msgId);
        // 数据转换
        param.put("polluteGovernDatas", null);
        HttpEntity<Object> requestEntity = new HttpEntity<>(param, headers);
        url = url + "?X-APP-ID=" + APPKey + "&X-APP-KEY=" + AppSecret;
        log.info("url:{}", url);
        String resJson = null;


        log.info("开始同步");
        resJson = rest.getForObject(url, String.class);
        log.info("url{}:reponse：{}", url, resJson);
        return resJson;
    }

    /**
     * 同步省能耗管理平台中抄表数据
     *
     * @param copyMeters
     * @return
     */
    public String syncCopyMeterInfors(List<CopyMeter> copyMeters) {
        String url = null;
        if ("sc".equals(deployTo)) {
            url = NHURL + "/syncWriteoffInfos/rest/energyMeter/syncWriteoffInfos";
        }
        if ("ln".equals(deployTo)) {
            url = NHURL + "/nh.cb.xx/syncWriteoffInfos";
        }
        Map<String, Object> param = new HashMap<>();
        param.put("writeoffInfos", copyMeters);
        String res = null;
        try {
            res = doHttpForCopyMeter(param, url);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return res;
    }

    private String doHttpForCollect(Map<String, Object> param, String url) {
        RestTemplate rest = new RestTemplate();
        rest.getMessageConverters().add(new StringHttpMessageConverter(Charset.forName(CHARSET)));
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("X-APP-ID", APPKey);
        headers.add("X-APP-KEY", AppSecret);
        param.put("provinceCode", PROVINCECODE);
        String msgId = "msgId" + IdGenerator.getNextIdAsString();
        param.put("msgId", msgId);
        List<CollectMeter> listcoll = (List<CollectMeter>) param.get("infos");
        List<String> stationCodes = listcoll.stream().map(CollectMeter::getStationCode).collect(toList());
        String scmsg = stationCodes.stream().collect(Collectors.joining(",", "(", ")"));
        HttpEntity<Object> requestEntity = new HttpEntity<>(param, headers);
        System.out.println("url:" + url);
        String resJson = null;

        HttpRequest post = HttpUtil.createPost(url);
        post.header("X-APP-ID", APPKey);
        post.header("X-APP-KEY", AppSecret);
        post.contentType("application/json");
        Dict map = Dict.create().set("provinceCode", PROVINCECODE).set("msgId", msgId).set("infos", listcoll);
        post.body(map.toString());

        try {
            log.info("开始采集同步");
            resJson = rest.postForObject(url, requestEntity, String.class);
        } catch (Exception e) {
            List<CollectMeter> infos = (List<CollectMeter>) param.get("infos");
            LocalDateTime.now().getDayOfYear();
            String time = TimeUtils.getNowTime();
            log.info("采集同步失败，未能连接到集团接口");

            //insertResult("采集电量接口", 3, "", time, "失败", JSON.toJSONString(infos), "未能连接到集团接口", infos.size());
            //log.info("插入syncresult日志表 采集失败日志{}条", infos.size());

            log.info("插入采集失败记录表");
            String syncFlag = "2";
            String failMag = "未能连接到集团接口";
            String sync_time = LocalDateTime.now().toString();
            List<CollectMeter> infosDb = CollectMeter.ConvertToFail(infos, syncFlag, failMag, sync_time, "");
            int n = syncresultMapper.insertFailCollect(infosDb);
            log.info("插入采集失败{}条", n);
            return "集团接口异常";
        }
        //操作结果处理
        List<CollectMeter> infos = (List<CollectMeter>) param.get("infos");
        String time = TimeUtils.getNowTime();
        log.info("接收到集团返回结果{}", resJson);
        if (resJson.contains("\"code\":\"0\"")) {
            //成功
            String syncFlag = "1";
            String failMag = resJson;
            String sync_time = LocalDateTime.now().toString();
            log.info("判定成功");
            //insertResult("采集电量接口", 3, "", time, "成功", JSON.toJSONString(infos), resJson, infos.size());
            //log.info("插入syncresult日志表 采集成功日志{}条", infos.size());
            List<CollectMeter> infosDb = CollectMeter.ConvertToFail(infos, syncFlag, failMag, sync_time, "");
            int n = syncresultMapper.insertFailCollect(infosDb);
            log.info("插入采集成功{}条", n);
        }
        if (resJson.contains("\"code\":\"-1\"")) {
            //失败
            log.info("判定失败");
            //insertResult("采集电量接口", 3, "", time, "失败", JSON.toJSONString(infos), resJson, infos.size());
            //log.info("插入syncresult日志表 采集失败日志{}条", infos.size());

            log.info("插入采集失败记录表");
            String syncFlag = "2";
            String failMag = resJson;
            String sync_time = LocalDateTime.now().toString();
            List<CollectMeter> infosDb = CollectMeter.ConvertToFail(infos, syncFlag, failMag, sync_time, "");
            int n = syncresultMapper.insertFailCollect(infosDb);
            log.info("插入采集失败{}条", n);
        }

        return resJson + msgId;
    }

    private String doHttpForCollectPlus(Map<String, Object> param, String url, String budget) {
        RestTemplate rest = new RestTemplate();
        rest.getMessageConverters().add(new StringHttpMessageConverter(Charset.forName(CHARSET)));
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("X-APP-ID", APPKey);
        headers.add("X-APP-KEY", AppSecret);
        param.put("provinceCode", PROVINCECODE);
        String msgId = "msgId" + IdGenerator.getNextIdAsString();
        param.put("msgId", msgId);
        List<CollectMeter> listcoll = (List<CollectMeter>) param.get("infos");
        List<String> stationCodes = listcoll.stream().map(CollectMeter::getStationCode).collect(toList());
        String scmsg = stationCodes.stream().collect(Collectors.joining(",", "(", ")"));
        HttpEntity<Object> requestEntity = new HttpEntity<>(param, headers);
        System.out.println("url:" + url);
        String resJson = null;
        try {
            log.info("开始采集同步");
            if (collectFlag) {
                resJson = rest.postForObject(url, requestEntity, String.class);
                log.info("正在采集传送集团");
            } else {
                resJson = "\"code\":\"0\"";
                log.info("正在只写db");
            }
        } catch (Exception e) {
            List<CollectMeter> infos = (List<CollectMeter>) param.get("infos");
            LocalDateTime.now().getDayOfYear();
            String time = TimeUtils.getNowTime();
            log.info("采集同步失败，未能连接到集团接口");

            insertResult("采集电量接口", 3, "", time, "失败", JSON.toJSONString(infos), "未能连接到集团接口", infos.size());
            log.info("插入syncresult日志表 采集失败日志{}条", infos.size());

            log.info("插入采集失败记录表");
            String syncFlag = "2";
            String failMag = "未能连接到集团接口";
            String sync_time = time;
            List<CollectMeter> infosDb = CollectMeter.ConvertToFail(infos, syncFlag, failMag, sync_time, budget);
            int n = syncresultMapper.insertFailCollect(infosDb);
            log.info("插入采集失败{}条", n);
            return "集团接口异常";
        }
        //操作结果处理
        List<CollectMeter> infos = (List<CollectMeter>) param.get("infos");
        String time = TimeUtils.getNowTime();
        log.info("接收到集团返回结果{}", resJson);
        if (resJson.contains("\"code\":\"0\"")) {
            //成功
            String syncFlag = "1";
            String failMag = resJson;
            String sync_time = time;
            log.info("判定成功");

            insertResult("采集电量接口", 3, "", time, "成功", JSON.toJSONString(infos), resJson, infos.size());
            log.info("插入syncresult日志表 采集成功日志{}条", infos.size());


            List<CollectMeter> infosDb = CollectMeter.ConvertToFail(infos, syncFlag, failMag, sync_time, budget);
            int n = syncresultMapper.insertFailCollect(infosDb);
            log.info("插入采集成功{}条", n);
        }
        if (resJson.contains("\"code\":\"-1\"")) {
            //失败
            log.info("判定失败");

            insertResult("采集电量接口", 3, "", time, "失败", JSON.toJSONString(infos), resJson, infos.size());
            log.info("插入syncresult日志表 采集失败日志{}条", infos.size());


            log.info("插入采集失败记录表");
            String syncFlag = "2";
            String failMag = resJson;
            String sync_time = time;
            List<CollectMeter> infosDb = CollectMeter.ConvertToFail(infos, syncFlag, failMag, sync_time, budget);
            int n = syncresultMapper.insertFailCollect(infosDb);
            log.info("插入采集失败{}条", n);
        }

        return resJson + msgId;
    }


    private void insertResult(String title, int method, String operName, String oper_time, String result, String msg, String failMsg, int num) {
        Syncresult syncresult = new Syncresult();
        syncresult.setTitle(title);
        syncresult.setMethod(method);
        if (StringUtils.isNotBlank(operName)) {
            syncresult.setOperName(operName);
        }
        try {
            syncresult.setOperTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(TimeUtils.getNowTime()));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        syncresult.setResult(result);
        syncresult.setMsg(msg);
        syncresult.setFailMsg(failMsg);
        syncresult.setNum(num);
        syncresultMapper.insert(syncresult);
    }


    /**
     * 同步抄表数据
     *
     * @param param
     * @param url
     * @return
     */
    private String doHttpForCopyMeter(Map<String, Object> param, String url) {
        //        SimpleClientHttpRequestFactory httpRequestFactory = new SimpleClientHttpRequestFactory();
//        httpRequestFactory.setConnectTimeout(3000);
//        httpRequestFactory.setReadTimeout(3000);
        RestTemplate rest = new RestTemplate();
        rest.getMessageConverters().add(new StringHttpMessageConverter(Charset.forName(CHARSET)));
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("X-APP-ID", APPKey);
        headers.add("X-APP-KEY", AppSecret);
        param.put("provinceCode", PROVINCECODE);
        String msgId = "msgId" + IdGenerator.getNextIdAsString();
        param.put("msgId", msgId);
        HttpEntity<Object> requestEntity = new HttpEntity<>(param, headers);
        //System.out.println(JSON.toJSONString(requestEntity));
//        insertLog("提交接口集团能源平台抄表doHttp-", "copyMeter", JSON.toJSONString(requestEntity));
        System.out.println("url:" + url);
        String resJson = null;
        try {
            resJson = rest.postForObject(url, requestEntity, String.class);
        } catch (RestClientException e) {
            List<WriteoffInfo2> infos = (List<WriteoffInfo2>) param.get("writeoffInfos");
            String time = TimeUtils.getNowTime();
            //失败
            insertResult("抄表接口", 2, "", time, "失败", JSON.toJSONString(requestEntity), "未能连接到集团接口", infos.size());
            return "集团接口异常";
        }
        //操作结果处理
        List<WriteoffInfo2> infos = (List<WriteoffInfo2>) param.get("writeoffInfos");
        String time = TimeUtils.getNowTime();

        if (resJson.contains("\"code\":\"0\"")) {
            //成功
            insertResult("抄表接口", 2, "", time, "成功", "", "", infos.size());
        }
        if (resJson.contains("\"code\":\"-1\"")) {
            //失败
            insertResult("抄表接口", 2, "", time, "失败", JSON.toJSONString(requestEntity), resJson, infos.size());
        }
        insertLog("提交接口集团数据平台doHttp-rtl", "copyMeterResult", resJson);
        //System.out.println("该信息MsgId:" + msgId);
        return resJson + msgId;
    }

    /**
     * 同步智能采集的电量数据
     *
     * @param collectMeterInfors
     * @return
     */
    public String syncCollectMeterInfors(List<CollectMeter> collectMeterInfors) {

        String url = null;
        if ("sc".equals(deployTo)) {
            url = NHURL + "/syncEnergyCollectionInfos/rest/energyMeter/syncEnergyCollectionInfos";
        }
        if ("ln".equals(deployTo)) {
            url = NHURL + "/nh.cj.xx/syncEnergyCollectionInfos";
        }
        Map<String, Object> param = new HashMap<>();
        param.put("infos", collectMeterInfors);
        String res = null;
        try {
            res = doHttpForCollect(param, url);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return res;
    }

    public String syncCollectMeterInforsPlus(List<CollectMeter> collectMeterInfors, String budget) {

        String url = null;
        if ("sc".equals(deployTo)) {
            url = NHURL + "/syncEnergyCollectionInfos/rest/energyMeter/syncEnergyCollectionInfos";
        }
        if ("ln".equals(deployTo)) {
            url = NHURL + "/nh.cj.xx/syncEnergyCollectionInfos";
        }
        Map<String, Object> param = new HashMap<>();
        param.put("infos", collectMeterInfors);
        String res = null;
        try {
            res = doHttpForCollectPlus(param, url, budget);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return res;

    }

    public String sendSyncCollectMeter(List<CollectMeter> collectMeterInfors) {

        String url = null;
        if ("sc".equals(deployTo)) {
            url = NHURL + "/syncEnergyCollectionInfos/rest/energyMeter/syncEnergyCollectionInfos";
        }
        if ("ln".equals(deployTo)) {
            url = NHURL + "/nh.cj.xx/syncEnergyCollectionInfos";
        }
        Map<String, Object> param = new HashMap<>();
        param.put("infos", collectMeterInfors);
        String res = null;
        try {
            res = doHttpForFailCollect(param, url);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return res;
    }

    private String doHttpForFailCollect(Map<String, Object> param, String url) {
        log.info("获取db数据");
        List<CollectMeter> dbs = (List<CollectMeter>) param.get("infos");
        log.info("获取同步数据");
        List<CollectMeter> sys = CollectMeter.failConvertToSync(dbs);
        log.info("构建Http请求");
        param.put("infos", sys);
        RestTemplate rest = new RestTemplate();
        rest.getMessageConverters().add(new StringHttpMessageConverter(Charset.forName(CHARSET)));
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("X-APP-ID", APPKey);
        headers.add("X-APP-KEY", AppSecret);
        param.put("provinceCode", PROVINCECODE);
        String msgId = "msgId" + IdGenerator.getNextIdAsString();
        param.put("msgId", msgId);
        List<CollectMeter> listcoll = (List<CollectMeter>) param.get("infos");
        List<String> stationCodes = listcoll.stream().map(CollectMeter::getStationCode).collect(toList());
        String scmsg = stationCodes.stream().collect(Collectors.joining(",", "(", ")"));
        HttpEntity<Object> requestEntity = new HttpEntity<>(param, headers);
        System.out.println("url:" + url);
        String resJson = null;
        try {
            log.info("开始采集同步");
            resJson = rest.postForObject(url, requestEntity, String.class);
        } catch (Exception e) {
            log.info("采集同步失败，未能连接到集团接口");
            dbs.forEach(item -> {
                CollectMeterFail db = (CollectMeterFail) item;
                db.setFailMag(db.getFailMag() + "||未能连接到集团接口");
                db.setSyncFlag("2");
            });
            syncresultMapper.updateRetryFailCollecter(dbs);
            return "集团接口异常";
        }
        log.info("接收到集团返回结果{}", resJson);
        if (resJson.contains("\"code\":\"0\"")) {
            log.info("判定成功");
            dbs.forEach(item -> {
                CollectMeterFail db = (CollectMeterFail) item;
                db.setFailMag(db.getFailMag() + "||sucess");
                db.setSyncFlag("1");
            });
            syncresultMapper.updateRetryFailCollecter(dbs);
        }
        if (resJson.contains("\"code\":\"-1\"")) {
            log.info("判定失败");
            String finalResJson = resJson;
            dbs.forEach(item -> {
                CollectMeterFail db = (CollectMeterFail) item;
                db.setFailMag(db.getFailMag() + "||" + finalResJson);
                db.setSyncFlag("2");
            });
            syncresultMapper.updateRetryFailCollecter(dbs);
        }
        return resJson + msgId;
    }

    public String syncRoomEnergyUse(MachineRoomEnergyUseDTO item, String environment) {
        log.info("构建机房用能同步地址");
        String url = null;
        if ("sc".equals(deployTo)) {
            log.info("当前环境为四川");
            url = "prod".equals(environment) ? "http://10.141.134.30:12500/serviceAgent/rest/api/ctcarbon/meter/syncRoomDatas" : "http://10.142.244.170:20501/serviceAgent/rest/api/ctcarbon/meter/syncRoomDatas";
        }
        if ("ln".equals(deployTo)) {
            log.info("当前环境为辽宁");
            url = "prod".equals(environment) ? "http://136.96.61.114:8888/api/openapi/meter.sync.datas/syncRoomDatas" : "http://136.96.61.114:8888/api/openapi/meter.sync.datas/syncRoomDatas";
        }
        log.info("地址为{}", url);

        log.info("构建协议");
        HttpHeaders httpheaders = new HttpHeaders();
        httpheaders.setContentType(MediaType.APPLICATION_JSON);
        httpheaders.add("X-APP-ID", roomorstationAPPID);
        httpheaders.add("X-APP-KEY", roomorstationAPPKEY);
        log.info("APPID:{}\nAPPKEy:{}", roomorstationAPPID, roomorstationAPPKEY);
        Map<String, Object> httpbody = new HashMap<>();
        httpbody.put("subjectCode", item.getSubjectCode());
        httpbody.put("msgId", item.getMsgId());
        List<MachineRoomEnergyUseEntity> dbs = item.getRoomDatas();
        List<MachineRoomEnergyUseEntity> syncs = dbs.stream().map(db -> {
            MachineRoomEnergyUseEntity sync = new MachineRoomEnergyUseEntity();
            BeanUtils.copyProperties(db, sync);
            return sync;
        }).collect(toList());

        httpbody.put("roomDatas", syncs);
        HttpEntity<Object> requestEntity = new HttpEntity<>(httpbody, httpheaders);
        log.info("http协议构建完毕");

        RestTemplate rest = new RestTemplate();
        rest.getMessageConverters().add(new StringHttpMessageConverter(Charset.forName(CHARSET)));
        String resJson = null;
        try {
            log.info("开始机房用能数据同步");
            resJson = rest.postForObject(url, requestEntity, String.class);
        } catch (RestClientException e) {
            log.info("机房用能同步失败，未能连接到集团接口");
            dbs.forEach(db -> {
                MachineRoomEnergyUseEntityDB dbtemp = (MachineRoomEnergyUseEntityDB) db;
                dbtemp.setSubjectCode(item.getSubjectCode());
                dbtemp.setMsgId(item.getMsgId());
                String failMag = dbtemp.getFailMag();
                dbtemp.setFailMag((StringUtils.isEmpty(failMag) ? "" : failMag) + "||未能连接到集团接口");
                dbtemp.setSyncFlag("2");
            });
            syncresultMapper.updateRoomEnergyUse(dbs);
            return String.format("subjectcode:%s同步失败-%d条", item.getSubjectCode(), dbs.size());
        }
        log.info("接收到集团返回结果{}", resJson);
        if (resJson.contains("\"code\":200")) {
            log.info("判定成功");
            dbs.forEach(db -> {
                MachineRoomEnergyUseEntityDB dbtemp = (MachineRoomEnergyUseEntityDB) db;
                dbtemp.setSubjectCode(item.getSubjectCode());
                dbtemp.setMsgId(item.getMsgId());
                String failMag = dbtemp.getFailMag();
                dbtemp.setFailMag((StringUtils.isEmpty(failMag) ? "" : failMag) + "||成功");
                dbtemp.setSyncFlag("1");
            });
            syncresultMapper.updateRoomEnergyUse(dbs);
            return String.format("subjectcode:%s同步成功-%d条", item.getSubjectCode(), dbs.size());
        } else {
            log.info("判定失败");
            String finalResJson = resJson;
            dbs.forEach(db -> {
                MachineRoomEnergyUseEntityDB dbtemp = (MachineRoomEnergyUseEntityDB) db;
                dbtemp.setSubjectCode(item.getSubjectCode());
                dbtemp.setMsgId(item.getMsgId());
                String failMag = dbtemp.getFailMag();
                dbtemp.setFailMag((StringUtils.isEmpty(failMag) ? "" : failMag) + "||" + finalResJson);
                dbtemp.setSyncFlag("2");
            });
            syncresultMapper.updateRoomEnergyUse(dbs);
            return String.format("subjectcode:%s同步失败-%d条", item.getSubjectCode(), dbs.size());
        }
    }

    public String syncStaionEnergyUse(StationEnergyUseDTO item, String environment) {
        log.info("构建局站用能同步地址");
        String url = null;
        if ("sc".equals(deployTo)) {
            log.info("当前环境为四川");
            url = "prod".equals(environment) ? "http://10.141.134.30:12500/serviceAgent/rest/api/ctcarbon/meter/syncStationDatas" : "http://10.142.244.170:20501/serviceAgent/rest/api/ctcarbon/meter/syncStationDatas";
        }
        if ("ln".equals(deployTo)) {
            log.info("当前环境为辽宁");
            url = "prod".equals(environment) ? "http://136.96.61.114:8888/api/openapi/meter.sync.station/syncStationDatas" : "http://136.96.61.114:8888/api/openapi/meter.sync.station/syncStationDatas";
        }
        log.info("地址为{}", url);

        log.info("构建协议");
        HttpHeaders httpheaders = new HttpHeaders();
        httpheaders.setContentType(MediaType.APPLICATION_JSON);
        httpheaders.add("X-APP-ID", roomorstationAPPID);
        httpheaders.add("X-APP-KEY", roomorstationAPPKEY);
        log.info("APPID:{}\nAPPKEy:{}", APPKey, AppSecret);
        Map<String, Object> httpbody = new HashMap<>();
        httpbody.put("subjectCode", item.getSubjectCode());
        httpbody.put("msgId", item.getMsgId());
        List<StationEnergyUseEntity> dbs = item.getRoomDatas();
        List<StationEnergyUseEntity> syncs = dbs.stream().map(db -> {
            StationEnergyUseEntity sync = new StationEnergyUseEntity();
            BeanUtils.copyProperties(db, sync);
            return sync;
        }).collect(toList());

        httpbody.put("stationDatas", syncs);
        HttpEntity<Object> requestEntity = new HttpEntity<>(httpbody, httpheaders);
        log.info("http协议构建完毕");

        RestTemplate rest = new RestTemplate();
        rest.getMessageConverters().add(new StringHttpMessageConverter(Charset.forName(CHARSET)));
        String resJson = null;
        try {
            log.info("开始局站用能数据同步");
            resJson = rest.postForObject(url, requestEntity, String.class);
        } catch (RestClientException e) {
            log.info("局站用能同步失败，未能连接到集团接口");
            dbs.forEach(db -> {
                StationEnergyUseEntityDB dbtemp = (StationEnergyUseEntityDB) db;
                dbtemp.setSubjectCode(item.getSubjectCode());
                dbtemp.setMsgId(item.getMsgId());
                String failMag = dbtemp.getFailMag();
                dbtemp.setFailMag((StringUtils.isEmpty(failMag) ? "" : failMag) + "||未能连接到集团接口");
                dbtemp.setSyncFlag("2");
            });
            syncresultMapper.updateStationEnergyUse(dbs);
            return String.format("subjectcode:%s同步失败-%d条", item.getSubjectCode(), dbs.size());
        }
        log.info("接收到集团返回结果{}", resJson);
        if (resJson.contains("\"code\":200")) {
            log.info("判定成功");
            dbs.forEach(db -> {
                StationEnergyUseEntityDB dbtemp = (StationEnergyUseEntityDB) db;
                dbtemp.setSubjectCode(item.getSubjectCode());
                dbtemp.setMsgId(item.getMsgId());
                String failMag = dbtemp.getFailMag();
                dbtemp.setFailMag((StringUtils.isEmpty(failMag) ? "" : failMag) + "||成功");
                dbtemp.setSyncFlag("1");
            });
            syncresultMapper.updateStationEnergyUse(dbs);
            return String.format("subjectcode:%s同步成功-%d条", item.getSubjectCode(), dbs.size());
        } else {
            log.info("判定失败");
            String finalResJson = resJson;
            dbs.forEach(db -> {
                StationEnergyUseEntityDB dbtemp = (StationEnergyUseEntityDB) db;
                dbtemp.setSubjectCode(item.getSubjectCode());
                dbtemp.setMsgId(item.getMsgId());
                String failMag = dbtemp.getFailMag();
                dbtemp.setFailMag((StringUtils.isEmpty(failMag) ? "" : failMag) + "||" + finalResJson);
                dbtemp.setSyncFlag("2");
            });
            syncresultMapper.updateStationEnergyUse(dbs);
            return String.format("subjectcode:%s同步失败-%d条", item.getSubjectCode(), dbs.size());
        }
    }

    public String syncCollectMeterInforsPlusFix(List<CollectMeter> collectMeterInfors, String budget) {

        String url = null;
        if ("sc".equals(deployTo)) {
            url = NHURL + "/syncEnergyCollectionInfos/rest/energyMeter/syncEnergyCollectionInfos";
        }
        if ("ln".equals(deployTo)) {
            url = NHURL + "/nh.cj.xx/syncEnergyCollectionInfos";
        }
        Map<String, Object> param = new HashMap<>();
        param.put("infos", collectMeterInfors);
        String res = null;
        try {
            res = doHttpForCollectPlusFix(param, url, budget);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return res;

    }

    private String doHttpForCollectPlusFix(Map<String, Object> param, String url, String budget) {
        log.info("固网业财一致率修复测试推送--->开始采集同步");
        RestTemplate rest = new RestTemplate();
        rest.getMessageConverters().add(new StringHttpMessageConverter(Charset.forName(CHARSET)));
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("X-APP-ID", APPKey);
        headers.add("X-APP-KEY", AppSecret);
        param.put("provinceCode", PROVINCECODE);
        String msgId = "msgId" + IdGenerator.getNextIdAsString();
        param.put("msgId", msgId);
//        List<CollectMeter> listcoll = (List<CollectMeter>) param.get("infos");
//        List<String> stationCodes = listcoll.stream().map(CollectMeter::getStationCode).collect(toList());
//        String scmsg = stationCodes.stream().collect(Collectors.joining(",", "(", ")"));
        HttpEntity<Object> requestEntity = new HttpEntity<>(param, headers);
        System.out.println("url:" + url);
        String resJson = null;
        try {
            log.info("开始采集同步");
            if (collectFlag) {
                resJson = rest.postForObject(url, requestEntity, String.class);
                log.info("正在采集传送集团");
            } else {
                resJson = "\"code\":\"0\"";
                log.info("正在只写db");
            }
        } catch (Exception e) {
            List<CollectMeter> infos = (List<CollectMeter>) param.get("infos");
            LocalDateTime.now().getDayOfYear();
            String time = TimeUtils.getNowTime();
            log.info("采集同步失败，未能连接到集团接口");

            insertResult("采集电量接口", 3, "", time, "失败", JSON.toJSONString(infos), "未能连接到集团接口", infos.size());
            log.info("插入syncresult日志表 采集失败日志{}条", infos.size());

            log.info("插入采集失败记录表");
            String syncFlag = "2";
            String failMag = "未能连接到集团接口";
            String sync_time = time;
            List<CollectMeter> infosDb = CollectMeter.ConvertToFail(infos, syncFlag, failMag, sync_time, budget);
            int n = syncresultMapper.insertFailCollect(infosDb);
            log.info("插入采集失败{}条", n);
            return "集团接口异常";
        }
        //操作结果处理
        List<CollectMeter> infos = (List<CollectMeter>) param.get("infos");
        String time = TimeUtils.getNowTime();
        log.info("接收到集团返回结果{}", resJson);
        if (resJson.contains("\"code\":\"0\"")) {
            //成功
            String syncFlag = "1";
            String failMag = resJson;
            String sync_time = time;
            log.info("判定成功");

            insertResult("采集电量接口", 3, "", time, "成功", JSON.toJSONString(infos), resJson, infos.size());
            log.info("插入syncresult日志表 采集成功日志{}条", infos.size());


            List<CollectMeter> infosDb = CollectMeter.ConvertToFail(infos, syncFlag, failMag, sync_time, budget);
            int n = syncresultMapper.insertFailCollect(infosDb);
            log.info("插入采集成功{}条", n);
        }
        if (resJson.contains("\"code\":\"-1\"")) {
            //失败
            log.info("判定失败");

            insertResult("采集电量接口", 3, "", time, "失败", JSON.toJSONString(infos), resJson, infos.size());
            log.info("插入syncresult日志表 采集失败日志{}条", infos.size());


            log.info("插入采集失败记录表");
            String syncFlag = "2";
            String failMag = resJson;
            String sync_time = time;
            List<CollectMeter> infosDb = CollectMeter.ConvertToFail(infos, syncFlag, failMag, sync_time, budget);
            int n = syncresultMapper.insertFailCollect(infosDb);
            log.info("插入采集失败{}条", n);
        }
        log.info("固网业财一致率修复测试推送--->结束采集同步");
        return resJson + msgId;
    }
}
