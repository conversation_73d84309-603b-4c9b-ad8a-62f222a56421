package com.sccl.modules.business.stationinfovalidity.service;

import com.sccl.modules.business.stationinfovalidity.domain.StationInfoValidity;
import com.sccl.framework.service.IBaseService;

import java.util.List;

/**
 * 电站址有效 服务层
 * 
 * <AUTHOR>
 * @date 2023-03-31
 */
public interface IStationInfoValidityService extends IBaseService<StationInfoValidity>
{


    List<StationInfoValidity> getStationInfoValidity(StationInfoValidity parm);
}
