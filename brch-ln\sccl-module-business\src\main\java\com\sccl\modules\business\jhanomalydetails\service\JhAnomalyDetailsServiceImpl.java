package com.sccl.modules.business.jhanomalydetails.service;

import com.sccl.framework.service.BaseServiceImpl;

import com.sccl.modules.business.jhanomalydetails.domain.JhPowerErrorVO;
import com.sccl.modules.business.jhanomalydetails.mapper.JhAnomalyDetailsMapper;
import com.sccl.modules.business.jhanomalydetails.util.ErrorEnum;
import org.springframework.stereotype.Service;
import com.sccl.modules.business.jhanomalydetails.domain.JhAnomalyDetails;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

import javax.annotation.Resource;


/**
 * 稽核结果详情 服务层实现
 *
 * <AUTHOR>
 * @date 2024-02-27
 */
@Service
public class JhAnomalyDetailsServiceImpl extends BaseServiceImpl<JhAnomalyDetails> implements IJhAnomalyDetailsService
{
    @Resource
    private JhAnomalyDetailsMapper jhAnomalyDetailsMapper;
    /**根据台账ids查询对应稽核异常列表**/
    @Override
    public List<JhAnomalyDetails> selectByTzIds(List<String> ids, List<String> jhsj) {
        return jhAnomalyDetailsMapper.selectByTzIds(ids,jhsj);
    }

    @Override
    public int saveOrUpdateList(List<JhAnomalyDetails> jhAnomalyDetailsList) {
        if (jhAnomalyDetailsList.size() == 0){
            return 0;
        }
        for (JhAnomalyDetails jhAnomalyDetails : jhAnomalyDetailsList) {
            jhAnomalyDetailsMapper.updateForModel(jhAnomalyDetails);
        }
        return jhAnomalyDetailsList.size();
    }

    @Override
    public List<JhAnomalyDetails> getPowerError(JhPowerErrorVO powerAuditVO) {

        JhAnomalyDetails jh =  JhAnomalyDetails.compositeAudit(powerAuditVO.getMenu(),powerAuditVO.getTzId(), powerAuditVO.getBzId(), powerAuditVO.getAuditTime());
        return jhAnomalyDetailsMapper.selectList(jh);
    }

    @Override
    public void exportPowerError(HttpServletResponse response, JhPowerErrorVO powerAuditVO) {

        ErrorEnum errorEnum =  ErrorEnum.valueOf(powerAuditVO.getMenu());
        JhAnomalyDetails jh =  JhAnomalyDetails.compositeAudit(powerAuditVO.getMenu(),powerAuditVO.getTzId(), powerAuditVO.getBzId(), powerAuditVO.getAuditTime());
        errorEnum.exportExcel(response,powerAuditVO,jh);

    }
}
