package com.sccl.modules.mssaccount.mssaccountclearitem.controller;

import java.util.HashMap;
import java.util.List;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.mssaccount.mssaccountclearitem.domain.MssAccountclearitem;
import com.sccl.modules.mssaccount.mssaccountclearitem.service.IMssAccountclearitemService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 挑对报账单 信息操作处理
 *
 * <AUTHOR>
 * @date 2019-05-01
 */
@RestController
@RequestMapping("/mssaccount/mssAccountclearitem")
public class MssAccountclearitemController extends BaseController {
    private String prefix = "mssaccount/mssAccountclearitem";

    @Autowired
    private IMssAccountclearitemService mssAccountclearitemService;

    @RequiresPermissions("mssaccount:mssAccountclearitem:view")
    @GetMapping()
    public String mssAccountclearitem() {
        return prefix + "/mssAccountclearitem";
    }

    /**
     * 查询挑对报账单列表
     */
    @RequiresPermissions("mssaccount:mssAccountclearitem:list")
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(MssAccountclearitem mssAccountclearitem) {
        startPage();
        List<MssAccountclearitem> list = mssAccountclearitemService.selectListAuto(mssAccountclearitem);
        return getDataTable(list);
    }

    @RequiresPermissions("mssaccount:mssAccountclearitem:list")
    @PostMapping("/queryClearitem")
    @ResponseBody
    public AjaxResult queryClearitem(@RequestBody MssAccountclearitem mssAccountclearitem) {
        // 验证可挑对金额
        List<MssAccountclearitem> list = mssAccountclearitemService.queryClearitem(mssAccountclearitem);
        Object object = JSONObject.toJSON(list);
        return this.success(object);
    }

    /**
     * 新增挑对报账单
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存挑对报账单
     */
    @RequiresPermissions("mssaccount:mssAccountclearitem:add")
    //@Log(title = "挑对报账单", action = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody MssAccountclearitem mssAccountclearitem) {
        return toAjax(mssAccountclearitemService.insert(mssAccountclearitem));
    }

    /**
     * 修改挑对报账单
     */
    @GetMapping("/edit/{clearid}")
    public AjaxResult edit(@PathVariable("clearid") Long clearid) {
        MssAccountclearitem mssAccountclearitem = mssAccountclearitemService.get(clearid);

        Object object = JSONObject.toJSON(mssAccountclearitem);

        return this.success(object);
    }

    /**
     * 修改保存挑对报账单
     */
    @RequiresPermissions("mssaccount:mssAccountclearitem:edit")
    //@Log(title = "挑对报账单", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody MssAccountclearitem mssAccountclearitem) {
        return toAjax(mssAccountclearitemService.update(mssAccountclearitem));
    }

    /**
     * 删除挑对报账单
     */
    @RequiresPermissions("mssaccount:mssAccountclearitem:remove")
	//@Log(title = "挑对报账单", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(@RequestBody HashMap<String,Object> map) {
        Object ids = map.get("ids");
        return toAjax(mssAccountclearitemService.deleteByIdsDB(Convert.toStrArray(ids.toString())));
    }


    /**
     * 查看挑对报账单
     */
    @RequiresPermissions("mssaccount:mssAccountclearitem:view")
    @GetMapping("/view/{clearid}")
    @ResponseBody
    public AjaxResult view(@PathVariable("clearid") Long clearid) {
        MssAccountclearitem mssAccountclearitem = mssAccountclearitemService.get(clearid);

        Object object = JSONObject.toJSON(mssAccountclearitem);

        return this.success(object);
    }

}
