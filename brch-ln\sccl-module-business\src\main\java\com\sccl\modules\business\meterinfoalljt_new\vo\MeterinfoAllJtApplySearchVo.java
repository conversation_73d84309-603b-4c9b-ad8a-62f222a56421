package com.sccl.modules.business.meterinfoalljt_new.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 在网表计数据清单-新增申请 一览 查询条件对象
 */
@Data
public class MeterinfoAllJtApplySearchVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 当前记录起始索引
     */
    private Integer pageNum;
    /**
     * 每页显示记录数
     */
    private Integer pageSize;

    /**
     * 所属分公司
     */
    private String company;
    private String[] companys;

    /**
     * 所属部门
     */
    private String country;
    private String[] countrys;

    /**
     * 关键字 站址名称模糊查询
     */
    private String key;

    /**
     * 电表编号 精确查询（多个逗号隔开）
     */
    private String ammeterCode;
    private String[] ammeterCodes;

    /**
     * 站址编码 5gr站址编码/站址编码精确查询（多个逗号隔开）
     */
    private String resstationcode;
    private String[] resstationcodes;

    /**
     * 状态
     */
    private String status;

    /**
     * 电表id（多个逗号隔开）
     */
    private String id;
    private String[] ids;

    /**
     * 登录用户是省/市/区级 1省级 2市级 3区级
     */
    private String admin;
}
