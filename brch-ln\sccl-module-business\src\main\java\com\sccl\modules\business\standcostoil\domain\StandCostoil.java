package com.sccl.modules.business.standcostoil.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.math.BigDecimal;


/**
 * 平均油耗表 stand_costoil
 * 
 * <AUTHOR>
 * @date 2022-05-09
 */
public class StandCostoil extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
    /** 发电功率 */
    private BigDecimal power;
    /** 油耗（Kg/h) */
    private BigDecimal costkg;
    /** 油耗（L/h) */
    private BigDecimal costl;
    /** 单位油耗（L/min) */
    private BigDecimal unitOilCost;
    /** 油类型 */
    private String oilType;


	public void setPower(BigDecimal power)
	{
		this.power = power;
	}

	public BigDecimal getPower() 
	{
		return power;
	}

	public void setCostkg(BigDecimal costkg)
	{
		this.costkg = costkg;
	}

	public BigDecimal getCostkg() 
	{
		return costkg;
	}

	public void setCostl(BigDecimal costl)
	{
		this.costl = costl;
	}

	public BigDecimal getCostl() 
	{
		return costl;
	}

	public void setUnitOilCost(BigDecimal unitOilCost)
	{
		this.unitOilCost = unitOilCost;
	}

	public BigDecimal getUnitOilCost() 
	{
		return unitOilCost;
	}

	public void setOilType(String oilType)
	{
		this.oilType = oilType;
	}

	public String getOilType() 
	{
		return oilType;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("power", getPower())
            .append("costkg", getCostkg())
            .append("costl", getCostl())
            .append("unitOilCost", getUnitOilCost())
            .append("oilType", getOilType())
            .toString();
    }
}
