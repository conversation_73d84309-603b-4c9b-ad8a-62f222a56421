package com.sccl.modules.mssaccount.mssinterface.domain;

import lombok.Getter;
import lombok.Setter;

/**
 * 机房用能实体
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-09-14 14:29
 * @email <EMAIL>
 */
@Getter
@Setter
public class MachineRoomEnergyUseEntity {
    /**
     * 用能月份，必填项，实际产生用能的时间。格式：YYYYMM。
     */
    private String statisDay;

    /**
     * 省级编码，必填项，双碳组织提供，见《附件二：双碳组织编码》。
     */
    private String provinceCode;

    /**
     * 市局组织编码，必填项，双碳组织提供，见《附件二：双碳组织编码》。
     */
    private String cityCode;

    /**
     * 市局组织名称，必填项，双碳组织提供，见《附件二：双碳组织编码》。
     */
    private String cityName;

    /**
     * 局站编码，必填项。
     */
    private String stationCode;

    /**
     * 局站名称，必填项，例如：**路**号**楼**机楼。
     */
    private String stationName;

    /**
     * 机房编码，必填项，唯一。
     */
    private String roomCode;

    /**
     * 机房名称，必填项。
     */
    private String roomName;

    /**
     * 投产运行时间，必填项，YYYYMM。
     */
    private String runningDate;

    /**
     * 上架率，必填项，百分比，保留小数点后2位，计算方式：实际使用机架数/总机架数。
     */
    private String idcutilization;

    /**
     * 设计PUE值，必填项，取小数点后2位。
     */
    private String idcdesignPUE;

    /**
     * 局站类型，必填项，按照集团网运部要求的四大级四大类划分，不能超出枚举值范围，
     * 必须为树形结构末端的枚举项（标红的枚举项），具体参考附件三：基础数据规范表。
     */
    private String stationType;

    /**
     * 规模，必填项，局站类型为“数据中心”时必填，枚举值：1=中小型、2=大型、3=超大型。
     * 中小型数据中心：100个≤标准机柜数＜3000个，或250kW≤主设备设计功率＜7500kW；
     * 大型数据中心：3000个≤标准机柜数＜10000个，或7500kw≤主设备设计功率＜25000kW；
     * 超大型数据中心：标准机柜数≥10000个，或主设备设计功率≥25000kW。
     */
    private String scale;

    /**
     * 局站地址，必填项，例如：**路**号**大楼。
     */
    private String stationLocation;

    /**
     * 标准机架数，必填项，单个机房内的标准机架总数。单位：个。
     */
    private String frameTotal;

    /**
     * IT设备数，必填项，单个机房内IT设备总数。单位：个。
     */
    private String produceDeviceTotal;

    /**
     * 空调设备数，非必填项，单个机房内空调设备总数。单位：个。
     */
    private String airDeviceTotal;

    /**
     * 动力设备数，非必填项，单个机房内动力设备总数。单位：个。
     */
    private String motiveDeviceTotal;

    /**
     * 照明及其他设备数，非必填项，单个机房内照明及其他设备总数。单位：个。
     */
    private String otherDeviceTotal;

    /**
     * 总能耗，必填项，单个机房内总能耗累计数值。单位：千瓦（kwh）。如无该数据，则传0。
     */
    private String energyTotal;

    /**
     * IT能耗，必填项，单个机房内IT设备所用能耗的累计数值。单位：千瓦（kwh）。如无该数据，则传0。
     */
    private String itEnergyTotal;

    /**
     * 空调能耗，非必填项，单个机房内空调系统设备所用能耗的累计数值。单位：千瓦（kwh）。
     */
    private String airEnergyTotal;

    /**
     * 动力能耗，非必填项，单个机房内动力设备所用能耗的累计数值。单位：千瓦（kwh）。
     */
    private String motiveEnergyTotal;

    /**
     * PUE值，必填项，若管控颗粒度到机房推送机房实际PUE值。
     */
    private String pue;

    /**
     * WUE值，必填项，WUE=机房用水量/机房IT设备用电量。单位：L/kwh。如无该数据，则传0。
     */
    private String wue;

    /**
     * 煤炭消耗量，非必填项，单位：吨。
     */
    private String coal;

    /**
     * 发电用煤消耗量，非必填项，单位：吨。
     */
    private String electricCoal;

    /**
     * 焦炭消耗量，非必填项，单位：吨。
     */
    private String cokeConsumption;

    /**
     * 原油消耗量，非必填项，单位：吨。
     */
    private String oilConsumption;

    /**
     * 固定源-汽油消耗量，非必填项，单位：升。
     */
    private String gasolineConsumption;

    /**
     * 移动源-汽油消耗量，非必填项，单位：升。
     */
    private String carGasolineConsumption;

    /**
     * 煤油消耗量，非必填项，单位：升。
     */
    private String keroseneConsumption;

    /**
     * 液化石油气消耗量，非必填项，单位：吨。
     */
    private String lngConsumption;

    /**
     * 固定源-柴油消耗量，非必填项，生产用途柴油消耗量。单位：升。
     */
    private String dieselOilConsumption;

    /**
     * 移动源-柴油消耗量，非必填项，移动源-柴油消耗量。单位：升。
     */
    private String carDieselOilConsumption;

    /**
     * 燃料油消耗量，非必填项，单位：升。
     */
    private String fuelOilConsumption;

    /**
     * 天然气消耗量，非必填项，单位：立方米。
     */
    private String naturalGasConsumption;

    /**
     * 热力，非必填项，单位：十亿焦。
     */
    private String heatingPowerConsumption;

    /**
     * 其他能源，非必填项，单位：吨标准煤。
     */
    private String otherEnergyConsumption;

    /**
     * 新水用量，非必填项，单位：吨。
     */
    private String waterConsumption;

    /**
     * 节能量，非必填项，单位：吨标准煤。
     */
    private String conservationEnergyConsumption;

    /**
     * 节能电量，非必填项，单位：千瓦时。
     */
    private String conservationPowerConsumption;

    /**
     * 是否AI节能，必填项，枚举值：
     * 1=是
     * 2=否
     */
    private String aiConservation;

}

