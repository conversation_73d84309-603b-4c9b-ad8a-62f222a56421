package com.sccl.modules.protocolexpiration.ammeterorprotocol.controller;

import com.alibaba.fastjson.JSONObject;
import com.sccl.common.support.Convert;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol;
import com.sccl.modules.protocolexpiration.ammeterorprotocol.service.IProtocolexpirationService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 基础数据-电管理（注意该中所有的引用类型，
都是指的power_category_type里面的type_code而非
ID，关注每个字段的注释） 信息操作处理
 * 
 * <AUTHOR>
 * @date 2019-05-18
 */
@RestController
@RequestMapping("/protocolexpiration/ammeterorprotocol")
public class ProtocolexpirationController extends BaseController
{
    private String prefix = "protocolexpiration/ammeterorprotocol";
	
	@Autowired
	private IProtocolexpirationService protocolexpirationService;
	@RequiresPermissions("protocolexpiration:ammeterorprotocol:view")
	@GetMapping()
	public String ammeterorprotocol()
	{
	    return prefix + "/ammeterorprotocol";
	}
	
	/**
	 * 查询基础数据-电管理（注意该中所有的引用类型，
都是指的power_category_type里面的type_code而非
ID，关注每个字段的注释）列表
	 */
	@RequiresPermissions("protocolexpiration:ammeterorprotocol:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(Ammeterorprotocol ammeterorprotocol)
	{
		startPage();
        List<Ammeterorprotocol> list = protocolexpirationService.selectListBy(ammeterorprotocol);
		return getDataTable(list);
	}
	
	/**
	 * 新增基础数据-电管理（注意该中所有的引用类型，
都是指的power_category_type里面的type_code而非
ID，关注每个字段的注释）
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存基础数据-电管理（注意该中所有的引用类型，
都是指的power_category_type里面的type_code而非
ID，关注每个字段的注释）
	 */
	@RequiresPermissions("protocolexpiration:ammeterorprotocol:add")
	//@Log(title = "基础数据-电管理（注意该中所有的引用类型， 都是指的power_category_type里面的type_code而非 ID，关注每个字段的注释）", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody Ammeterorprotocol ammeterorprotocol)
	{		
		return toAjax(protocolexpirationService.insert(ammeterorprotocol));
	}

	/**
	 * 修改基础数据-电管理（注意该中所有的引用类型，
都是指的power_category_type里面的type_code而非
ID，关注每个字段的注释）
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		Ammeterorprotocol ammeterorprotocol = protocolexpirationService.get(id);

		Object object = JSONObject.toJSON(ammeterorprotocol);

        return this.success(object);
	}
	
	/**
	 * 修改保存基础数据-电管理（注意该中所有的引用类型，
都是指的power_category_type里面的type_code而非
ID，关注每个字段的注释）
	 */
	@RequiresPermissions("protocolexpiration:ammeterorprotocol:edit")
	//@Log(title = "基础数据-电管理（注意该中所有的引用类型， 都是指的power_category_type里面的type_code而非 ID，关注每个字段的注释）", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody Ammeterorprotocol ammeterorprotocol)
	{		
		return toAjax(protocolexpirationService.update(ammeterorprotocol));
	}
	
	/**
	 * 删除基础数据-电管理（注意该中所有的引用类型，
都是指的power_category_type里面的type_code而非
ID，关注每个字段的注释）
	 */
	@RequiresPermissions("protocolexpiration:ammeterorprotocol:remove")
	//@Log(title = "基础数据-电管理（注意该中所有的引用类型， 都是指的power_category_type里面的type_code而非 ID，关注每个字段的注释）", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(protocolexpirationService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看基础数据-电管理（注意该中所有的引用类型，
都是指的power_category_type里面的type_code而非
ID，关注每个字段的注释）
     */
    @RequiresPermissions("protocolexpiration:ammeterorprotocol:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		Ammeterorprotocol ammeterorprotocol = protocolexpirationService.get(id);

        Object object = JSONObject.toJSON(ammeterorprotocol);

        return this.success(object);
    }

}
