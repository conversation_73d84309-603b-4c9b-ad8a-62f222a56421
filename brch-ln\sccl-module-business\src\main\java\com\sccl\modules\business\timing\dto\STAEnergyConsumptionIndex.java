package com.sccl.modules.business.timing.dto;

import com.sccl.timing.finder.framework.CacheData;
import com.sccl.timing.finder.framework.TimeSeriesData;
import com.sccl.timing.finder.util.DateUtils;
import com.sccl.timing.finder.util.StringUtils;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-03-08 13:36
 * @email <EMAIL>
 */
@Getter
@Setter
public class STAEnergyConsumptionIndex implements TimeSeriesData {
    private Long id;

    private String stationCode;

    private String currentDate;

    private String resStationCode;

    private Float _4gQuantity;

    private Float _5gQuantity;

    @Override
    public long id() {
        return id;
    }

    @Override
    public long timestamp() {
        try {
            Date date = DateUtils.parseDate(currentDate, "yyyyMMdd", "yyyy-MM-dd");
            return date.getTime();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    @Override
    public String key() {
        return stationCode + (StringUtils.isEmpty(resStationCode) ? "" : "-" + resStationCode) + "-init" + DateUtils.getTime();
    }

    @Override
    public CacheData storeValue() {
        return new EnergyQuantity(currentDate, _4gQuantity, _5gQuantity);
    }
}
