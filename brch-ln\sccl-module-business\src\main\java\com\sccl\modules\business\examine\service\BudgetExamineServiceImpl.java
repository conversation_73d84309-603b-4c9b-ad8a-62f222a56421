package com.sccl.modules.business.examine.service;

import com.sccl.modules.autojob.util.convert.StringUtils;
import com.sccl.modules.business.examine.mapper.BudgetExamineMapper;
import com.sccl.modules.business.examine.vo.BudgetExamineResultVo;
import com.sccl.modules.business.examine.vo.BudgetExamineSearchVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;


/**
 * 预算考核
 *
 * <AUTHOR>
 * @date 2025-02-12
 */
@Service
@Slf4j
public class BudgetExamineServiceImpl implements IBudgetExamineService {

    @Autowired
    private BudgetExamineMapper budgetExamineMapper;

    /**
     * 一览
     * @param searchVo
     * @return
     */
    @Override
    public List<BudgetExamineResultVo> list(BudgetExamineSearchVo searchVo) {
        List<BudgetExamineResultVo> list = budgetExamineMapper.list(searchVo);
        if (list != null) {
            for (BudgetExamineResultVo vo : list) {
                BigDecimal big100 = new BigDecimal("100");
                BigDecimal big12 = new BigDecimal("12");
                BigDecimal big1 = new BigDecimal("1");
                BigDecimal bigMonthCnt = new BigDecimal(searchVo.getMonthCnt());
                BigDecimal com = null;//同比增幅
                if (!"——".equals(vo.getCom())) {
                    com = new BigDecimal(vo.getCom()).divide(big100);
                    vo.setCom(vo.getCom() + "%");
                }
                int monthScore = 0;
                if ((!"——".equals(vo.getBudgetAmount())) && (!"——".equals(vo.getUsedTotal()))) {
                    BigDecimal usedTotal = new BigDecimal(vo.getUsedTotal());//累计使用预算
                    BigDecimal budgetAmount = new BigDecimal(vo.getBudgetAmount());//年度预算
                    //累计平均月度预算=年度预算/12*对应的月份数量
                    BigDecimal avg = budgetAmount.divide(big12, 10, BigDecimal.ROUND_HALF_UP).multiply(bigMonthCnt);
                    //预算使用比增幅=累计使用预算/累计平均月度预算-1
                    BigDecimal useRate = BigDecimal.ZERO;
                    if (avg.compareTo(BigDecimal.ZERO) != 0) {
                        useRate = usedTotal.divide(avg, 4, BigDecimal.ROUND_HALF_UP);
                    }
                    useRate = useRate.subtract(big1);
                    vo.setUseRate(useRate.multiply(big100).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");

                    //本月预算参考值 查询年份为当前年份时才计算
                    String[] useds = getUseds(vo);
                    if (searchVo.getMonthCnt() != 12) {
                        //本月预算参考值 上月使用预算*（1+同比增幅）计算出的值与上月使用预算*（1+预算使用比增幅）计算出的值相加/2
                        if (com != null) {
                            BigDecimal big2 = new BigDecimal("2");
                            BigDecimal usedPre = new BigDecimal(useds[searchVo.getMonthCnt()]);//上月使用预算
                            BigDecimal ckz = usedPre.multiply(com.add(big1))
                                    .add(usedPre.multiply(useRate.add(big1)))
                                    .divide(big2, 2, BigDecimal.ROUND_HALF_UP);
                            setNowMonth(vo, searchVo.getMonthCnt(), ckz + "");//本月预算参考值
                        } else {
                            setNowMonth(vo, searchVo.getMonthCnt(), "——");
                        }
                    }
                    //月度得分 预算使用比增幅的结果的绝对值在10%以内的得1分
                    if (searchVo.getMonthCnt() > 0) {
                        BigDecimal ut = BigDecimal.ZERO;
                        for (int i = 1; i <= searchVo.getMonthCnt(); i++) {
                            BigDecimal av = budgetAmount.divide(big12, 10, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(i));//累计平均月度预算
                            ut = ut.add(new BigDecimal(useds[i]));                                 //累计使用预算
                            BigDecimal us = BigDecimal.ZERO;
                            if (av.compareTo(BigDecimal.ZERO) != 0) {
                                us = ut.divide(av, 4, BigDecimal.ROUND_HALF_UP);
                            }
                            us = us.subtract(big1).multiply(big100);
                            if (Math.abs(us.doubleValue()) <= 10) {
                                monthScore += 1;
                            }
                        }
                    }
                } else {
                    vo.setUseRate("——");
                    if (searchVo.getMonthCnt() != 12) {
                        setNowMonth(vo, searchVo.getMonthCnt(), "——");
                    }
                }
                vo.setUsed1(calValue(vo.getUsed1()));
                vo.setUsed2(calValue(vo.getUsed2()));
                vo.setUsed3(calValue(vo.getUsed3()));
                vo.setUsed4(calValue(vo.getUsed4()));
                vo.setUsed5(calValue(vo.getUsed5()));
                vo.setUsed6(calValue(vo.getUsed6()));
                vo.setUsed7(calValue(vo.getUsed9()));
                vo.setUsed8(calValue(vo.getUsed8()));
                vo.setUsed9(calValue(vo.getUsed9()));
                vo.setUsed10(calValue(vo.getUsed10()));
                vo.setUsed11(calValue(vo.getUsed11()));
                vo.setUsed12(calValue(vo.getUsed12()));
                vo.setUsedTotal(calValue(vo.getUsedTotal()));
                vo.setBudgetAmount(calValue(vo.getBudgetAmount()));
                vo.setMonthScore(monthScore + "");
                //年度得分
                vo.setYearScore(new BigDecimal(monthScore).divide(big12, 2, BigDecimal.ROUND_HALF_UP) + "");
            }
        }
        return list;
    }

    //结果处理 转换为万元
    private String calValue(String value) {
        if (!"——".equals(value)) {
            BigDecimal big10000 = new BigDecimal("10000");
            BigDecimal newValue = new BigDecimal(value).divide(big10000, 2, BigDecimal.ROUND_HALF_UP);
            return newValue + "";
        } else {
            return value;
        }
    }

    //设置本月预算参考值
    private void setNowMonth(BudgetExamineResultVo vo, int monthCnt, String ckz) {
        if (monthCnt == 0) {
            vo.setUsed1(ckz);
        }
        if (monthCnt == 1) {
            vo.setUsed2(ckz);
        }
        if (monthCnt == 2) {
            vo.setUsed3(ckz);
        }
        if (monthCnt == 3) {
            vo.setUsed4(ckz);
        }
        if (monthCnt == 4) {
            vo.setUsed5(ckz);
        }
        if (monthCnt == 5) {
            vo.setUsed6(ckz);
        }
        if (monthCnt == 6) {
            vo.setUsed7(ckz);
        }
        if (monthCnt == 7) {
            vo.setUsed8(ckz);
        }
        if (monthCnt == 8) {
            vo.setUsed9(ckz);
        }
        if (monthCnt == 9) {
            vo.setUsed10(ckz);
        }
        if (monthCnt == 10) {
            vo.setUsed11(ckz);
        }
        if (monthCnt == 11) {
            vo.setUsed12(ckz);
        }
    }

    //将12个月的使用预算转换为数组
    private String[] getUseds(BudgetExamineResultVo vo){
        String[] useds = {"","0","0","0","0","0","0","0","0","0","0","0","0"};
        if(StringUtils.isBlank(vo.getUsedPre12())){
            useds[0] = vo.getUsedPre12();//去年12月使用预算
        }
        if(!"——".equals(vo.getUsed1())){
            useds[1] = vo.getUsed1();
        }
        if(!"——".equals(vo.getUsed2())){
            useds[2] = vo.getUsed2();
        }
        if(!"——".equals(vo.getUsed3())){
            useds[3] = vo.getUsed3();
        }
        if(!"——".equals(vo.getUsed4())){
            useds[4] = vo.getUsed4();
        }
        if(!"——".equals(vo.getUsed5())){
            useds[5] = vo.getUsed5();
        }
        if(!"——".equals(vo.getUsed6())){
            useds[6] = vo.getUsed6();
        }
        if(!"——".equals(vo.getUsed7())){
            useds[7] = vo.getUsed7();
        }
        if(!"——".equals(vo.getUsed8())){
            useds[8] = vo.getUsed8();
        }
        if(!"——".equals(vo.getUsed9())){
            useds[9] = vo.getUsed9();
        }
        if(!"——".equals(vo.getUsed10())){
            useds[10] = vo.getUsed10();
        }
        if(!"——".equals(vo.getUsed11())){
            useds[11] = vo.getUsed11();
        }
        if(!"——".equals(vo.getUsed12())){
            useds[12] = vo.getUsed12();
        }
        return useds;
    }
}
