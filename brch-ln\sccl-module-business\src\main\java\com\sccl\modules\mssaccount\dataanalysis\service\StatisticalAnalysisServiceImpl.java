package com.sccl.modules.mssaccount.dataanalysis.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.sccl.framework.utils.ExcelUtil;
import com.sccl.modules.mssaccount.dataanalysis.dto.GetByOrgIdDTO;
import com.sccl.modules.mssaccount.dataanalysis.vo.ElectricityBillResultExport;
import com.sccl.modules.mssaccount.dataanalysis.vo.ElectricityBillResultVO;
import com.sccl.modules.mssaccount.dataanalysis.vo.ElectricityBillVO;
import com.sccl.modules.mssaccount.mssaccountbill.mapper.MssAccountbillMapper;
import com.sccl.modules.system.organization.domain.Organization;
import com.sccl.modules.system.organization.mapper.OrganizationMapper;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class StatisticalAnalysisServiceImpl implements StatisticalAnalysisService {

    private final MssAccountbillMapper accountbillMapper;
    private final OrganizationMapper organizationMapper;


    public List<ElectricityBillResultVO> getByOrgId(GetByOrgIdDTO dto) {
        setSelectDate(dto);

        // 查询原始数据
        List<ElectricityBillVO> rawCurrentYearData = accountbillMapper.statisticalAnalysisOfAccounting(dto.getYear(), dto.getOrgId());
        List<ElectricityBillVO> rawLastYearData = accountbillMapper.statisticalAnalysisOfAccounting(dto.getYear() - 1, dto.getOrgId());

        // 补全月份数据
        List<ElectricityBillVO> dataList = completeMissingMonths(rawCurrentYearData, dto.getYear());
        List<ElectricityBillVO> lastYearDataList = completeMissingMonths(rawLastYearData, dto.getYear() - 1);

        // 转换为Map便于查询
        Map<String, ElectricityBillVO> lastYearDataMap = lastYearDataList.stream()
                .collect(Collectors.toMap(ElectricityBillVO::getIndex, vo -> vo));

        // 处理同比
        List<ElectricityBillResultVO> resultList = new ArrayList<>();
        for (ElectricityBillVO nowYearData : dataList) {
            ElectricityBillResultVO result = new ElectricityBillResultVO();
            BeanUtils.copyProperties(nowYearData, result);

            // 构造去年对应的index（如当前是202401，去年应为202301）
            String lastYearIndex = (dto.getYear() - 1) + nowYearData.getMonth();
            ElectricityBillVO lastYearData = lastYearDataMap.getOrDefault(lastYearIndex, createDefaultBillVO(lastYearIndex));

            // 计算同比
            yearOnYear(result, nowYearData, lastYearData);
            resultList.add(result);
        }

        // 合并数据用于环比计算
        Map<String, ElectricityBillVO> allDataMap = new HashMap<>();
        lastYearDataList.forEach(vo -> allDataMap.put(vo.getIndex(), vo));
        dataList.forEach(vo -> allDataMap.put(vo.getIndex(), vo));

        // 处理环比
        resultList.forEach(result -> {
            String currentIndex = result.getIndex();
            DateTime currentDate = DateUtil.parse(currentIndex, "yyyyMM");
            DateTime lastMonthDate = DateUtil.offsetMonth(currentDate, -1);
            String lastMonthIndex = lastMonthDate.toString("yyyyMM");

            ElectricityBillVO lastMonthData = allDataMap.getOrDefault(lastMonthIndex, createDefaultBillVO(lastMonthIndex));

            // 计算环比
            result.setYearOverMonthTotalKWh(calculateRate(result.getKwh(), lastMonthData.getKwh()));
            result.setYearOverMonthUnitPrice(calculateRate(result.getUnitPrice(), lastMonthData.getUnitPrice()));
            result.setYearOverMonthElectricityCost(calculateRate(result.getElectricityCost(), lastMonthData.getElectricityCost()));

            if (dto.getOrgId() == null) {
                result.setOrgName("全省");
            } else {
                Organization org = organizationMapper.selectById(dto.getOrgId());
                result.setOrgName(org.getOrgName());
            }
        });

        // 过滤单月查询
        if (StrUtil.isNotBlank(dto.getMonth())) {
            resultList = resultList.stream()
                    .filter(vo -> vo.getIndex().equals(dto.getDate()))
                    .collect(Collectors.toList());
        }

        return resultList;
    }

    @Override
    @SneakyThrows
    public List<ElectricityBillResultVO> export(HttpServletResponse response, GetByOrgIdDTO dto) {
        List<ElectricityBillResultVO> list = this.getByOrgId(dto);
        List<ElectricityBillResultExport> exportList = list.stream().map(vo -> {
            ElectricityBillResultExport export = new ElectricityBillResultExport();
            BeanUtils.copyProperties(vo, export);
            export.setYearOverYearTotalKWh(vo.getYearOverYearTotalKWh() + "%");
            export.setYearOverMonthTotalKWh(vo.getYearOverMonthTotalKWh() + "%");
            export.setYearOverYearUnitPrice(vo.getYearOverYearUnitPrice() + "%");
            export.setYearOverMonthUnitPrice(vo.getYearOverMonthUnitPrice() + "%");
            export.setYearOverYearElectricityCost(vo.getYearOverYearElectricityCost() + "%");
            export.setYearOverMonthElectricityCost(vo.getYearOverMonthElectricityCost() + "%");
            return export;
        }).collect(Collectors.toList());

        ExcelUtil<ElectricityBillResultExport> excelUtil = new ExcelUtil<>(ElectricityBillResultExport.class);
        excelUtil.exportExcelToBrowser(response, exportList, "用能统计报表");
        return Collections.emptyList();
    }

    private void setSelectDate(GetByOrgIdDTO dto) {
        if (StrUtil.isBlank(dto.getDate())) {
            log.info("年份为空，默认为今年");
            dto.setYear(DateUtil.thisYear());
        }
        // 截取年份
        else if (dto.getDate().length() == 6) {
            Integer year = Integer.parseInt(dto.getDate().substring(0, 4));
            dto.setYear(year);
            log.info("年份截取成功:{}", dto.getYear());

            String month = dto.getDate().substring(4, 6);
            dto.setMonth(month);
            log.info("月份:{}", dto.getMonth());
        } else {
            dto.setYear(Integer.parseInt(dto.getDate()));
        }
    }

    /**
     * 计算同比
     *
     * @param result       结果对象
     * @param nowYearData  今年数据
     * @param lastYearData 上一年数据
     */
    private void yearOnYear(ElectricityBillResultVO result,
                            ElectricityBillVO nowYearData,
                            ElectricityBillVO lastYearData) {
        // 总用电量同比
        result.setYearOverYearTotalKWh(
                calculateRate(nowYearData.getKwh(), lastYearData.getKwh())
        );
        // 单价同比
        result.setYearOverYearUnitPrice(
                calculateRate(nowYearData.getUnitPrice(), lastYearData.getUnitPrice())
        );
        // 金额同比
        result.setYearOverYearElectricityCost(
                calculateRate(nowYearData.getElectricityCost(), lastYearData.getElectricityCost())
        );
    }


    private List<ElectricityBillVO> completeMissingMonths(List<ElectricityBillVO> dataList, int year) {
        Map<String, ElectricityBillVO> dataMap = dataList.stream()
                .collect(Collectors.toMap(ElectricityBillVO::getIndex, vo -> vo)); // 使用index作为键

        List<ElectricityBillVO> completeList = new ArrayList<>();
        for (int month = 1; month <= 12; month++) {
            String monthStr = String.format("%02d", month);
            String index = year + monthStr;
            completeList.add(
                    dataMap.getOrDefault(index, createDefaultBillVO(index)) // 按完整索引查询
            );
        }
        return completeList;
    }

    private ElectricityBillVO createDefaultBillVO(String index) {
        ElectricityBillVO vo = new ElectricityBillVO();
        vo.setIndex(index);
        vo.setYear(index.substring(0, 4));
        vo.setMonth(index.substring(4, 6));
        vo.setKwh(BigDecimal.ZERO);
        vo.setUnitPrice(BigDecimal.ZERO);
        vo.setElectricityCost(BigDecimal.ZERO);
        return vo;
    }

    /**
     * 计算增长率百分比（处理除零和空值）
     *
     * @param current  当前期数值
     * @param previous 上一期数值
     * @return 增长率百分比（保留两位小数，已格式化为 12.34% 形式）
     */
    private BigDecimal calculateRate(BigDecimal current, BigDecimal previous) {
        BigDecimal safeCurrent = (current == null) ? BigDecimal.ZERO : current;
        BigDecimal safePrevious = (previous == null) ? BigDecimal.ZERO : previous;

        if (safePrevious.compareTo(BigDecimal.ZERO) == 0) {
            return safeCurrent.compareTo(BigDecimal.ZERO) == 0
                    ? BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP)
                    : new BigDecimal("100.00");
        }

        return safeCurrent.subtract(safePrevious)
                .divide(safePrevious, 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"))
                .setScale(2, RoundingMode.HALF_UP);
    }
}
