package com.sccl.modules.business.stationaudit.demo;

import com.enrising.dcarbon.audit.Progress;
import com.enrising.dcarbon.audit.ProgressListener;

public abstract class StuauditProgressListener implements ProgressListener {
   private String name;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String name() {
        return name;
    }
}
