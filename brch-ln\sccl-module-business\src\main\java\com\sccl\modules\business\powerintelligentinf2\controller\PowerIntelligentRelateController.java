package com.sccl.modules.business.powerintelligentinf2.controller;

import java.util.List;
import java.util.Map;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.business.powerintelligentinf2.domain.PowerIntelligentRelate;
import com.sccl.modules.business.powerintelligentinf2.service.IPowerIntelligentRelateService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * PUE管控详情 信息操作处理
 * 
 * <AUTHOR>
 * @date 2019-07-18
 */
@RestController
@RequestMapping("/business/PUECountrolsDetail")
public class PowerIntelligentRelateController extends BaseController
{
    private String prefix = "business/PUECountrolsDetail";
	
	@Autowired
	private IPowerIntelligentRelateService powerIntelligentRelateService;
	
//	@RequiresPermissions("business:powerIntelligentRelate:view")
	@GetMapping()
	public String powerIntelligentRelate()
	{
	    return prefix + "/powerIntelligentRelate";
	}
	
	/**
	 * 查询PUE管控详情列表
	 */
//	@RequiresPermissions("business:powerIntelligentRelate:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(PowerIntelligentRelate powerIntelligentRelate)
	{
		startPage();
        List<PowerIntelligentRelate> list = powerIntelligentRelateService.selectList(powerIntelligentRelate);
		return getDataTable(list);
	}

	/**
	 * 查询PUE管控详情列表
	 */
//	@RequiresPermissions("business:powerIntelligentRelate:list")
	@RequestMapping("/listByMainId")
	@ResponseBody
	public TableDataInfo listByMainId(PowerIntelligentRelate powerIntelligentRelate)
	{
		List<Map<String,Object>> list = powerIntelligentRelateService.selectByList(powerIntelligentRelate);
		return getDataTable(list);
	}
	
	/**
	 * 新增PUE管控详情
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存PUE管控详情
	 */
//	@RequiresPermissions("business:powerIntelligentRelate:add")
	//@Log(title = "PUE管控详情", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody PowerIntelligentRelate powerIntelligentRelate)
	{		
		return toAjax(powerIntelligentRelateService.insert(powerIntelligentRelate));
	}

	/**
	 * 修改PUE管控详情
	 */
	@GetMapping("/edit/{detaiid}")
	public AjaxResult edit(@PathVariable("detaiid") Long detaiid)
	{
		PowerIntelligentRelate powerIntelligentRelate = powerIntelligentRelateService.get(detaiid);

		Object object = JSONObject.toJSON(powerIntelligentRelate);

        return this.success(object);
	}
	
	/**
	 * 修改保存PUE管控详情
	 */
//	@RequiresPermissions("business:powerIntelligentRelate:edit")
	//@Log(title = "PUE管控详情", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody PowerIntelligentRelate powerIntelligentRelate)
	{		
		return toAjax(powerIntelligentRelateService.update(powerIntelligentRelate));
	}
	
	/**
	 * 删除PUE管控详情
	 */
//	@RequiresPermissions("business:powerIntelligentRelate:remove")
	//@Log(title = "PUE管控详情", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(powerIntelligentRelateService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看PUE管控详情
     */
//    @RequiresPermissions("business:powerIntelligentRelate:view")
    @GetMapping("/view/{detaiid}")
    @ResponseBody
    public AjaxResult view(@PathVariable("detaiid") Long detaiid)
    {
		PowerIntelligentRelate powerIntelligentRelate = powerIntelligentRelateService.get(detaiid);

        Object object = JSONObject.toJSON(powerIntelligentRelate);

        return this.success(object);
    }

}
