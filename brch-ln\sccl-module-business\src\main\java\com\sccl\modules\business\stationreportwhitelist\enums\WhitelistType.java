package com.sccl.modules.business.stationreportwhitelist.enums;

import cn.hutool.core.lang.Dict;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/22 16:32
 * @describe 白名单类型
 */
@Getter
@AllArgsConstructor
public enum WhitelistType {

    ONE_WATCH_HAS_MANY_STATIONS("1", "一表多站"),
    ONE_STOP_IS_MORE_THAN_ONE_WATCH("2", "一站多表"),
    UNIT_PRICE("3", "单价"),
    ;

    private final String code;
    private final String name;

    // 获取枚举列表
    public static List<Dict> getDictList() {
        List<Dict> dictList = Lists.newArrayList();
        for (WhitelistType whitelistType : WhitelistType.values()) {
            Dict dict = Dict.create();
            dict.put("code", whitelistType.getCode());
            dict.put("name", whitelistType.getName());
            dictList.add(dict);
        }
        return dictList;
    }

}
