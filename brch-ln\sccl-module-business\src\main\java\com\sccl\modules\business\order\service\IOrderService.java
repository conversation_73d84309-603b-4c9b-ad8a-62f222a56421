package com.sccl.modules.business.order.service;

import com.sccl.framework.service.IBaseService;
import com.sccl.modules.business.order.domain.Order;
import com.sccl.modules.uniflow.common.WFModel;


/**
 * 订单 服务层
 * 
 * <AUTHOR>
 * @date 2019-03-05
 */
public interface IOrderService extends IBaseService<Order>
{
    /**
     * 流程引擎回调（通过MQ）
     * @param wfModel 流程消息对象
     */
    public void uniflowCallBack(WFModel wfModel) throws Exception;
}
