package com.sccl.modules.business.stationreportwhitelist.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sccl.modules.business.stationreportwhitelist.domain.StationReportWhitelist;
import com.sccl.modules.business.stationreportwhitelist.dto.StationReportWhitelistDTO;
import com.sccl.modules.business.stationreportwhitelist.dto.StationReportWhitelistQuery;
import com.sccl.modules.business.stationreportwhitelist.vo.OneWatchHasManyStationsExport;
import com.sccl.modules.business.stationreportwhitelist.vo.RationalityOfUnitPriceExport;
import com.sccl.modules.business.stationreportwhitelist.vo.StationReportWhitelistVO;
import com.sccl.modules.uniflow.common.WFModel;

import java.util.List;

public interface StationReportWhitelistService extends IService<StationReportWhitelist> {

    IPage<StationReportWhitelistVO> selectList(Page<StationReportWhitelistVO> page, StationReportWhitelistQuery query);

    StationReportWhitelistVO findById(Long id);

    String add(StationReportWhitelistDTO dto);

    int edit(StationReportWhitelistDTO dto);

    int del(Long id);

    /**
     * 流程回调
     *
     * @param wfModel 流程
     */
    void uniflowCallBack(WFModel wfModel);


    void initMeterId();

    /**
     * 导出 一表多站
     */
    List<OneWatchHasManyStationsExport> exportOneWatchHasManyStationsExport(Page<StationReportWhitelistVO> page, StationReportWhitelistQuery query);

    /**
     * 导出 单价合理性
     */
    List<RationalityOfUnitPriceExport> exportRationalityOfUnitPrice(Page<StationReportWhitelistVO> page, StationReportWhitelistQuery query);
}
