package com.sccl.modules.business.ecceptionreply.mapper;

import com.sccl.modules.business.ecceptionreply.domain.EcceptionReply;
import com.sccl.framework.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 公共异常回复 数据层
 *
 * <AUTHOR>
 * @date 2023-03-27
 */
public interface EcceptionReplyMapper extends BaseMapper<EcceptionReply> {

    int deleteByExceptionCommonID(@Param("list") List<String> exceptionCommonIds);


    int updateHistoryForModel(EcceptionReply delParm);
}