package com.sccl.modules.ocr.service;

import com.sccl.framework.web.domain.AjaxResult;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;

/**
 * RapidOCr进行OCR识别 服务层
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
public interface IRapidOCRService {

    /**
     * 识别发票并获取供应商信息
     * @param file
     * @return
     */
    String getSupplier(File file);

    /**
     * 识别发票并获取供应商信息
     * @param mfiles
     * @return
     */
    AjaxResult getSupplier(List<MultipartFile> mfiles);
}
