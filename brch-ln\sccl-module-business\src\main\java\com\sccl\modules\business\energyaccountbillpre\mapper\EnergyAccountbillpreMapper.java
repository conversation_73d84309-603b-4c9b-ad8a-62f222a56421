package com.sccl.modules.business.energyaccountbillpre.mapper;

import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.accountbillpre.domain.Accountbillpre;
import com.sccl.modules.business.accountbillpre.domain.AccountbillpreCondition;
import com.sccl.modules.business.accountbillpre.domain.AccountbillpreResult;
import com.sccl.modules.business.energyaccountbillpre.domain.EnergyAccountbillitempre;
import com.sccl.modules.business.energyaccountbillpre.domain.EnergyAccountbillpre;
import com.sccl.modules.business.energyaccountbillpre.domain.EnergyAccountbillpreResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 归集单主单 数据层
 *
 * <AUTHOR>
 * @date 2021-12-20
 */
public interface EnergyAccountbillpreMapper extends BaseMapper<EnergyAccountbillpre> {

    void insertBillItemPre(EnergyAccountbillitempre energyAccountbillitempre);

    List<EnergyAccountbillitempre> selectBillItem(@Param("pcid") Long pcid);

    // 查询油水气归集单
    List<EnergyAccountbillpre> selectListByParams(AccountbillpreCondition condition);

    List<EnergyAccountbillpreResult> selectListByName(Map<String,Object> map);

    int setSummaruser(EnergyAccountbillpre pre);

    /**
     * 通过归集单主键查询其关联的报账单编号
     *
     * @param energyAccountbillpre 包含pabid的对象
     * @return java.util.List<com.sccl.modules.business.energyaccountbillpre.domain.EnergyAccountbillpre>
     * <AUTHOR> Yongxiang
     * @date 2021/12/21 11:21
     */
    List<EnergyAccountbillpre> selectPabid(EnergyAccountbillpre energyAccountbillpre);

    /**
     * 关联报账表到归集单
     *
     * @param energyAccountbillpres 包含pabrid和pabid的对象
     * @return int
     * <AUTHOR> Yongxiang
     * @date 2021/12/21 11:26
     */
    int updatePabid(EnergyAccountbillpre energyAccountbillpres);

    /**
     * 通过归集单编号查询关联的台账ID和台账金额
     *
     * @param energyAccountbillitempre 包含归集单编号的对象
     * @return java.util.List<com.sccl.modules.business.oilcardaccount.domain.OilCardAccount>
     * <AUTHOR> Yongxiang
     * @date 2021/12/21 12:45
     */
    List<EnergyAccountbillitempre> selectPcid(EnergyAccountbillitempre energyAccountbillitempre);

    EnergyAccountbillpre selectEnergyAccountBill(@Param("pabrid") Long pabrid);

    // 修改台账状态
    int updateStatusByParid(Map<String, Object> map);
}