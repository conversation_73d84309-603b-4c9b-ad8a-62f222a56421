package com.sccl.modules.business.powerpriceconf.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.business.powerpriceconf.domain.PowerPriceConf;
import com.sccl.modules.business.powerpriceconf.service.IPowerPriceConfService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 单价配置 信息操作处理
 * 
 * <AUTHOR>
 * @date 2022-09-18
 */
@RestController
@RequestMapping("/business/powerPriceConf")
public class PowerPriceConfController extends BaseController
{
    private String prefix = "business/powerPriceConf";
	
	@Autowired
	private IPowerPriceConfService powerPriceConfService;
	
	@RequiresPermissions("business:powerPriceConf:view")
	@GetMapping()
	public String powerPriceConf()
	{
	    return prefix + "/powerPriceConf";
	}
	
	/**
	 * 查询单价配置列表
	 */
	@RequiresPermissions("business:powerPriceConf:list")
	@RequestMapping("/list")
	@ResponseBody
	public TableDataInfo list(PowerPriceConf powerPriceConf)
	{
		startPage();
        List<PowerPriceConf> list = powerPriceConfService.selectList(powerPriceConf);
		return getDataTable(list);
	}
	
	/**
	 * 新增单价配置
	 */
	@GetMapping("/add")
	public String add()
	{
	    return prefix + "/add";
	}
	
	/**
	 * 新增保存单价配置
	 */
	@RequiresPermissions("business:powerPriceConf:add")
	@Log(title = "单价配置", action = BusinessType.INSERT)
	@PostMapping("/add")
	@ResponseBody
	public AjaxResult addSave(@RequestBody PowerPriceConf powerPriceConf)
	{		
		return toAjax(powerPriceConfService.insert(powerPriceConf));
	}

	/**
	 * 修改单价配置
	 */
	@GetMapping("/edit/{id}")
	public AjaxResult edit(@PathVariable("id") Long id)
	{
		PowerPriceConf powerPriceConf = powerPriceConfService.get(id);

		Object object = JSONObject.toJSON(powerPriceConf);

        return this.success(object);
	}
	
	/**
	 * 修改保存单价配置
	 */
	@RequiresPermissions("business:powerPriceConf:edit")
	@Log(title = "单价配置", action = BusinessType.UPDATE)
	@PostMapping("/edit")
	@ResponseBody
	public AjaxResult editSave(@RequestBody PowerPriceConf powerPriceConf)
	{		
		return toAjax(powerPriceConfService.update(powerPriceConf));
	}
	
	/**
	 * 删除单价配置
	 */
	@RequiresPermissions("business:powerPriceConf:remove")
	@Log(title = "单价配置", action = BusinessType.DELETE)
	@PostMapping( "/remove")
	@ResponseBody
	public AjaxResult remove(String ids)
	{		
		return toAjax(powerPriceConfService.deleteByIds(Convert.toStrArray(ids)));
	}


    /**
     * 查看单价配置
     */
    @RequiresPermissions("business:powerPriceConf:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id)
    {
		PowerPriceConf powerPriceConf = powerPriceConfService.get(id);

        Object object = JSONObject.toJSON(powerPriceConf);

        return this.success(object);
    }

}
