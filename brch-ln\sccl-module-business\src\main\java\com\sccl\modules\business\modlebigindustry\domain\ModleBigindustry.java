package com.sccl.modules.business.modlebigindustry.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sccl.framework.web.domain.BaseEntity;
import java.math.BigDecimal;


/**
 * 单价大工业表 power_modle_bigindustry
 * 
 * <AUTHOR>
 * @date 2023-03-03
 */
public class ModleBigindustry extends BaseEntity
{
	private static final long serialVersionUID = 1L;
	
    /** 省份 */
    private String province;
    /** 市 */
    private String city;
    /** 填报年 */
    private String year;
    /** 填报月 */
    private String month;
    /** 大工业基本电费折算单价 */
    private BigDecimal bigindustryprice;
    /** 大工业容量 */
    private BigDecimal capacitydemandbig1;
    /** 大工业需量 */
    private BigDecimal capacitydemandbig2;
    /** 省份编码 */
    private String provincecode;
    /** 地市编码 */
    private String citycode;


	public void setProvince(String province)
	{
		this.province = province;
	}

	public String getProvince() 
	{
		return province;
	}

	public void setCity(String city)
	{
		this.city = city;
	}

	public String getCity() 
	{
		return city;
	}

	public void setYear(String year)
	{
		this.year = year;
	}

	public String getYear() 
	{
		return year;
	}

	public void setMonth(String month)
	{
		this.month = month;
	}

	public String getMonth() 
	{
		return month;
	}

	public void setBigindustryprice(BigDecimal bigindustryprice)
	{
		this.bigindustryprice = bigindustryprice;
	}

	public BigDecimal getBigindustryprice() 
	{
		return bigindustryprice;
	}

	public void setCapacitydemandbig1(BigDecimal capacitydemandbig1)
	{
		this.capacitydemandbig1 = capacitydemandbig1;
	}

	public BigDecimal getCapacitydemandbig1() 
	{
		return capacitydemandbig1;
	}

	public void setCapacitydemandbig2(BigDecimal capacitydemandbig2)
	{
		this.capacitydemandbig2 = capacitydemandbig2;
	}

	public BigDecimal getCapacitydemandbig2() 
	{
		return capacitydemandbig2;
	}


	public void setProvincecode(String provincecode)
	{
		this.provincecode = provincecode;
	}

	public String getProvincecode() 
	{
		return provincecode;
	}

	public void setCitycode(String citycode)
	{
		this.citycode = citycode;
	}

	public String getCitycode() 
	{
		return citycode;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("province", getProvince())
            .append("city", getCity())
            .append("year", getYear())
            .append("month", getMonth())
            .append("bigindustryprice", getBigindustryprice())
            .append("capacitydemandbig1", getCapacitydemandbig1())
            .append("capacitydemandbig2", getCapacitydemandbig2())
            .append("delFlag", getDelFlag())
            .append("provincecode", getProvincecode())
            .append("citycode", getCitycode())
            .toString();
    }
}
