package com.sccl.modules.mssaccount.msssupplier.mapper;

import com.sccl.modules.mssaccount.msssupplier.domain.MssSupplier;
import com.sccl.framework.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * 供应商 数据层
 * 
 * <AUTHOR>
 * @date 2019-05-01
 */
public interface MssSupplierMapper extends BaseMapper<MssSupplier>
{

    /**
     * 根据供货商名称获取供货商信息
     * @param name 供货商名称
     */
    MssSupplier selectOneByName(@Param("name") String name);
}