package com.sccl.modules.business.equipmentdict.controller;

import com.alibaba.fastjson.JSONObject;
import com.sccl.common.support.Convert;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.utils.ExcelUtil;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.autojob.util.bean.ObjectUtil;
import com.sccl.modules.autojob.util.convert.MessageMaster;
import com.sccl.modules.autojob.util.convert.StringUtils;
import com.sccl.modules.autojob.util.id.IdGenerator;
import com.sccl.modules.business.equipmentdict.domain.EquipmentDict;
import com.sccl.modules.business.equipmentdict.service.IEquipmentDictService;
import com.sccl.modules.system.attachments.domain.Attachments;
import com.sccl.modules.system.attachments.service.AttachmentsServiceImpl;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 铁塔站址设备字典 信息操作处理
 *
 * <AUTHOR> Yongxiang
 * @date 2022-08-09
 */
@RestController
@RequestMapping("/business/equipment_dict")
public class EquipmentDictController extends BaseController {
    private final String prefix = "business/equipmentDict";

    @Autowired
    private IEquipmentDictService equipmentDictService;

    @Autowired
    private AttachmentsServiceImpl service;

    private static final Map<String, String> IMPORT_COLUMN_MAP = new HashMap<>();

    private static final Map<String, String> EXPORT_COLUMN_MAP = new HashMap<>();

    private static final Map<String, String> PROMPT_COLUMN_MAP = new HashMap<>();

    static {
        IMPORT_COLUMN_MAP.put("设备类型", "type");
        IMPORT_COLUMN_MAP.put("设备厂家", "factory");
        IMPORT_COLUMN_MAP.put("设备型号", "model");
        IMPORT_COLUMN_MAP.put("设备参考能耗(kwh/日)", "energyCostPerDay");
        IMPORT_COLUMN_MAP.put("设备组Id（请勿修改）", "groupId");
        IMPORT_COLUMN_MAP.put("版本号（请勿修改）", "version");
    }

    static {
        EXPORT_COLUMN_MAP.put("type", "设备类型");
        EXPORT_COLUMN_MAP.put("factory", "设备厂家");
        EXPORT_COLUMN_MAP.put("model", "设备型号");
        EXPORT_COLUMN_MAP.put("energyCostPerDay", "设备参考能耗(kwh/日)");
        EXPORT_COLUMN_MAP.put("groupId", "设备组Id（请勿修改）");
        EXPORT_COLUMN_MAP.put("version", "版本号（请勿修改）");
    }

    static {
        PROMPT_COLUMN_MAP.put("groupId","系统导出的表格拥有该字段，用户可以对导出的表格进行任意修改（除标注'请勿修改'的字段除外），修改后可以直接导入到系统，系统可以通过设备组ID找到匹配的设备进行更新");
        PROMPT_COLUMN_MAP.put("version", "版本号为系统生成字段，数值代表此版本前还有n个版本，最大的版本号代表最新的记录");
    }


    /*=================新增接口=================>*/
    @PostMapping(value = "/insert", produces = "application/json;charset=UTF-8")
    public String insert(@RequestBody(required = false) EquipmentDict equipmentDict) {
        if (equipmentDict == null || equipmentDict.getEnergyCostPerDay() == null || StringUtils.isEmpty(equipmentDict.getModel()) || StringUtils.isEmpty(equipmentDict.getType()) || StringUtils.isEmpty(equipmentDict.getFactory())) {
            return MessageMaster.DefaultMessage.EMPTY_PARAMS.toString();
        }
        equipmentDict.setVersion(0L);
        equipmentDict.setId(IdGenerator.getNextIdAsLong());
        equipmentDict.setGroupId(IdGenerator.getNextIdAsLong());
        equipmentDict.setCreateTime(new Date());
        equipmentDict.setUpdateTime(new Date());
        int count = equipmentDictService.insert(equipmentDict);
        return MessageMaster.getMessage(count == 1 ? MessageMaster.Code.OK : MessageMaster.Code.ERROR, count == 1 ? "新增成功" : "新增失败", equipmentDict, false, false);
    }

    @PostMapping(value = "/update", produces = "application/json;charset=UTF-8")
    public String update(@RequestBody(required = false) EquipmentDict equipmentDict) {
        if (equipmentDict == null || equipmentDict.getGroupId() == null) {
            return MessageMaster.DefaultMessage.EMPTY_PARAMS.toString();
        }
        EquipmentDict history = equipmentDictService.getLatestByGroupId(equipmentDict.getGroupId());
        if (history == null) {
            return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "没有Id：" + equipmentDict.getId() + "的设备");
        }
        ObjectUtil.mergeObjectFields(equipmentDict, history, "id", "version");
        history.setId(IdGenerator.getNextIdAsLong());
        //更新版本号
        history.setVersion(history.getVersion() + 1);
        equipmentDict.setGroupId(history.getGroupId());
        history.setCreateTime(new Date());
        history.setUpdateTime(new Date());
        int count = equipmentDictService.insert(history);
        return MessageMaster.getMessage(count == 1 ? MessageMaster.Code.OK : MessageMaster.Code.ERROR, count == 1 ? "更新成功" : "更新失败", history, false, false);
    }

    @GetMapping(value = "/get/{groupId}", produces = "application/json;charset=UTF-8")
    public String getByGroupId(@PathVariable("groupId") Long groupId) {
        if (groupId == null) {
            return MessageMaster.DefaultMessage.EMPTY_PARAMS.toString();
        }
        EquipmentDict history = equipmentDictService.getLatestByGroupId(groupId);
        if (history == null) {
            return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "没有指定设备");
        }
        return MessageMaster.getMessage(MessageMaster.Code.OK, "查找成功", history, false, false);
    }

    @GetMapping(value = "/get_all", produces = "application/json;charset=UTF-8")
    public TableDataInfo getAllEquipment() {
        startPage();
        List<EquipmentDict> equipmentDictList = equipmentDictService.listLatest();
        return getDataTable(equipmentDictList);
    }

    @GetMapping(value = "/list_history/{groupId}", produces = "application/json;charset=UTF-8")
    public TableDataInfo listHistory(@PathVariable("groupId") Long groupId) {
        startPage();
        if (groupId == null) {
            TableDataInfo tableDataInfo = new TableDataInfo();
            tableDataInfo.setCode(0);
            tableDataInfo.setRows(Collections.emptyList());
            tableDataInfo.setTotal(0);
            return tableDataInfo;
        }
        List<EquipmentDict> equipmentDictList = equipmentDictService.listHistoryByGroupId(groupId);
        return getDataTable(equipmentDictList);
    }

    @GetMapping(value = "/delete_by_group_id/{groupId}", produces = "application/json;charset=UTF-8")
    public String delete(@PathVariable("groupId") Long groupId) {
        if (groupId == null) {
            return MessageMaster.DefaultMessage.EMPTY_PARAMS.toString();
        }
        int count = equipmentDictService.deleteByGroupId(groupId);
        if (count == 0) {
            return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "无匹配记录");
        }
        return MessageMaster.getMessage(MessageMaster.Code.OK, "删除成功", count);
    }

    @GetMapping(value = "/import_excel", produces = "application/json;charset=UTF-8")
    public String importExcel(HttpServletRequest request, @RequestParam(required = false, value = "sheet_name") String sheetName) {
        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        MultipartFile multipartFile = multiRequest.getFile("file");
        if (multipartFile == null || multipartFile.isEmpty()) {
            return MessageMaster.DefaultMessage.EMPTY_PARAMS.toString();
        }
        ExcelUtil<EquipmentDict> excelUtil = new ExcelUtil<>(EquipmentDict.class);
        try {
            List<EquipmentDict> content = excelUtil.importExcel(sheetName, multipartFile, IMPORT_COLUMN_MAP);
            if (CollectionUtils.isEmpty(content)) {
                return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "文件内容为空，请检查格式或内容");
            }
            List<EquipmentDict> existData = content.stream().filter(item -> item.getGroupId() != null).collect(Collectors.toList());
            Map<Long, Long> groupVersionMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(existData)) {
                List<EquipmentDict> historyList = equipmentDictService.getAllLatestByGroupId(existData.stream().map(EquipmentDict::getGroupId).collect(Collectors.toList()));
                historyList.forEach(item -> {
                    groupVersionMap.put(item.getGroupId(), item.getVersion() + 1);
                });
            }
            content.forEach(item -> {
                item.setId(IdGenerator.getNextIdAsLong());
                if (item.getGroupId() != null && groupVersionMap.containsKey(item.getGroupId())) {
                    item.setVersion(groupVersionMap.get(item.getGroupId()));
                } else {
                    item.setGroupId(IdGenerator.getNextIdAsLong());
                    item.setVersion(0L);
                }
                item.setCreateTime(new Date());
                item.setUpdateTime(new Date());
            });
            int count = equipmentDictService.insertList(content);
            if (count != content.size()) {
                return MessageMaster.getMessage(MessageMaster.Code.OK, "导入成功，但导条目数和内容条目数不匹配");
            }
            return MessageMaster.getMessage(MessageMaster.Code.OK, "导入成功");
        } catch (Exception e) {
            e.printStackTrace();
            return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "导入错误：" + e.getMessage());
        }
    }

    @PostMapping(value = "export_excel", produces = "application/json;charset=UTF-8")
    public String exportExcel(@RequestBody(required = false) EquipmentDict equipmentDict, @RequestParam(value = "latest", required = false) Boolean latest) {
        if (equipmentDict == null) {
            return MessageMaster.DefaultMessage.EMPTY_PARAMS.toString();
        }
        List<EquipmentDict> equipmentDictList = null;
        if (latest != null && !latest) {
            equipmentDictList = equipmentDictService.selectList(equipmentDict);
        } else {
            equipmentDictList = equipmentDictService.listLatestConditional(equipmentDict);
        }
        if (CollectionUtils.isEmpty(equipmentDictList)) {
            return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "指定条件下无匹配数据");
        }
        Attachments attachments = equipmentDictService.exportExcel(equipmentDictList, EXPORT_COLUMN_MAP, PROMPT_COLUMN_MAP);
        if (attachments == null) {
            return MessageMaster.DefaultMessage.SYSTEM_ERROR.toString();
        }
        return MessageMaster.getMessage(MessageMaster.Code.OK, "上传成功", attachments, true, false);
    }

    @PostMapping(value = "/export_excel_content", produces = "application/json;charset=UTF-8")
    public String exportExcel(@RequestBody(required = false) List<EquipmentDict> equipmentDictList) {
        if (CollectionUtils.isEmpty(equipmentDictList)) {
            return MessageMaster.DefaultMessage.EMPTY_PARAMS.toString();
        }
        Attachments attachments = equipmentDictService.exportExcel(equipmentDictList, EXPORT_COLUMN_MAP, PROMPT_COLUMN_MAP);
        if (attachments == null) {
            return MessageMaster.DefaultMessage.SYSTEM_ERROR.toString();
        }
        return MessageMaster.getMessage(MessageMaster.Code.OK, "上传成功", attachments, true, false);
    }


    /*=======================Finished======================<*/


    /*=================生成接口=================>*/
    @RequiresPermissions("business:equipmentDict:view")
    @GetMapping()
    public String equipmentDict() {
        return prefix + "/equipmentDict";
    }

    /**
     * 查询铁塔站址设备字典列表
     */
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(EquipmentDict equipmentDict) {
        startPage();
        List<EquipmentDict> list = equipmentDictService.selectList(equipmentDict);
        return getDataTable(list);
    }

    /**
     * 新增铁塔站址设备字典
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存铁塔站址设备字典
     */
    @RequiresPermissions("business:equipmentDict:add")
    @Log(title = "铁塔站址设备字典", action = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody EquipmentDict equipmentDict) {
        return toAjax(equipmentDictService.insert(equipmentDict));
    }

    /**
     * 修改铁塔站址设备字典
     */
    @GetMapping("/edit/{id}")
    public AjaxResult edit(@PathVariable("id") Long id) {
        EquipmentDict equipmentDict = equipmentDictService.get(id);

        Object object = JSONObject.toJSON(equipmentDict);

        return this.success(object);
    }

    /**
     * 修改保存铁塔站址设备字典
     */
    @RequiresPermissions("business:equipmentDict:edit")
    @Log(title = "铁塔站址设备字典", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody EquipmentDict equipmentDict) {
        return toAjax(equipmentDictService.update(equipmentDict));
    }

    /**
     * 删除铁塔站址设备字典
     */
    @RequiresPermissions("business:equipmentDict:remove")
    @Log(title = "铁塔站址设备字典", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(equipmentDictService.deleteByIds(Convert.toStrArray(ids)));
    }


    /**
     * 查看铁塔站址设备字典
     */
    @RequiresPermissions("business:equipmentDict:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id) {
        EquipmentDict equipmentDict = equipmentDictService.get(id);

        Object object = JSONObject.toJSON(equipmentDict);

        return this.success(object);
    }

    /*=======================Finished======================<*/

}
