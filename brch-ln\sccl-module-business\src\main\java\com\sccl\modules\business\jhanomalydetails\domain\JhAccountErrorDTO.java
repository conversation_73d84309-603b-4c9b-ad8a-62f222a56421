package com.sccl.modules.business.jhanomalydetails.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.sccl.framework.aspectj.lang.annotation.Excel;
import lombok.Data;


/**
 * 台账/报账 周期连续性
 * */

@Data
public class JhAccountErrorDTO {


    /** 报账单期号
     *
     * */
    @Excel(name = "报账单期号_tz")
    private String bzdqh;
    /** 报账单id */
    @Excel(name = "报账单id_tz")
    private String bzdid;


    /**
     * 电表户名/协议编码
     */
    @Excel(name = "电表户名/协议编码_bz_tz")
    private String dbhm;

    /**
     * 台账期号
     */
    @TableField("tzqh")
    @Excel(name="台账期号_bz_tz")
    private String tzqh;

    /**
     * 集团站址编码
     */
    @TableField("jtzzbm")
    @Excel(name="集团站址编码_bz_tz")
    private String jtzzbm;

    /**
     * 铁塔站址编码
     */
    @TableField("ttzzbm")
    @Excel(name="铁塔站址编码_bz_tz")
    private String ttzzbm;



    /**
     * 起始时间
     */
    @TableField("qssj")
    @Excel(name="起始时间_bz_tz")
    private String qssj;

    /**
     * 截至时间
     */
    @TableField("jzsj")
    @Excel(name="截至时间_bz_tz")
    private String jzsj;

    /**
     * 本期启度
     */
    @TableField("bqqd")
    @Excel(name="本期启度_bz_tz")
    private String bqqd;

    /**
     * 本期止度
     */
    @TableField("bqzd")
    @Excel(name="本期止度_bz_tz")
    private String bqzd;
}
