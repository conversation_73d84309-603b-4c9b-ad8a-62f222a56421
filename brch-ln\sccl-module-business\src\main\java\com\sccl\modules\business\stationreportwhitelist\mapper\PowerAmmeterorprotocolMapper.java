package com.sccl.modules.business.stationreportwhitelist.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseMapper;
import com.sccl.modules.business.stationreportwhitelist.domain.PowerAmmeterorprotocol;
import com.sccl.modules.business.stationreportwhitelist.dto.PowerAmmeterorprotocolQuery;
import com.sccl.modules.business.stationreportwhitelist.vo.OneTableMultiStationListCountVO;
import com.sccl.modules.business.stationreportwhitelist.vo.PowerAmmeterorprotocolVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface PowerAmmeterorprotocolMapper extends MPJBaseMapper<PowerAmmeterorprotocol> {

    IPage<PowerAmmeterorprotocolVO> oneTableMultiStationList(Page<PowerAmmeterorprotocolVO> page, @Param(Constants.WRAPPER) Wrapper<PowerAmmeterorprotocolQuery> wrapper);
    OneTableMultiStationListCountVO oneTableMultiStationListCount(@Param("ammetername")String ammetername);
}
