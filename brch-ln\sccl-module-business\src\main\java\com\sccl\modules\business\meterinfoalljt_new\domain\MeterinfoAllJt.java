package com.sccl.modules.business.meterinfoalljt_new.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 表计清单查询 实体类
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@TableName("meterinfo_all_jt")
public class MeterinfoAllJt extends Model<MeterinfoAllJt> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 省级编码
     */
    @TableField("provinceCode")
    private String provinceCode;

    /**
     * 市局组织编码
     */
    @TableField("cityCode")
    private String cityCode;

    /**
     * 市局组织名称
     */
    @TableField("cityName")
    private String cityName;

    /**
     * 区县组织编码
     */
    @TableField("countyCode")
    private String countyCode;

    /**
     * 区县组织名称
     */
    @TableField("countyName")
    private String countyName;

    /**
     * 电表编码
     */
    @TableField("energyMeterCode")
    private String energyMeterCode;

    /**
     * 电表名称
     */
    @TableField("energyMeterName")
    private String energyMeterName;

    /**
     * 电表状态
     */
    @TableField("status")
    private String status;

    /**
     * 电表用途
     */
    @TableField("usageCopy")
    private String usageCopy;

    /**
     * 电表类型
     */
    @TableField("type")
    private String type;

    /**
     * 局站编码
     */
    @TableField("stationCode")
    private String stationCode;

    /**
     * 局站名称
     */
    @TableField("stationName")
    private String stationName;

    /**
     * 局站位置
     */
    @TableField("stationLocation")
    private String stationLocation;

    /**
     * 局站状态
     */
    @TableField("stationStatus")
    private String stationStatus;

    /**
     * 局站类型
     */
    @TableField("stationType")
    private String stationType;

    /**
     * 大工业用电标识
     */
    @TableField("largeIndustrialElectricityFlag")
    private String largeIndustrialElectricityFlag;

    /**
     * 供电方式
     */
    @TableField("energySupplyWay")
    private String energySupplyWay;

    /**
     * 站点编码
     */
    @TableField("siteCode")
    private String siteCode;

    /**
     * 电网电表编码
     */
    @TableField("powerGridEnergyMeterCode")
    private String powerGridEnergyMeterCode;

    /**
     * 删除标识
     */
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 能源类型
     */
    @TableField("energyType")
    private String energyType;

    /**
     * 类型局站编码
     */
    @TableField("typeStationCode")
    private String typeStationCode;

    /**
     * 合同价格
     */
    @TableField("contractPrice")
    private String contractPrice;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 同步标识
     */
    @TableField("syncFlag")
    private Integer syncFlag;

    /**
     * 失败信息
     */
    @TableField("failMag")
    private String failMag;

    /**
     * 消息ID
     */
    @TableField("msg_id")
    private String msgId;
}
