package com.sccl.modules.business.oilcardaccount.controller;

import com.alibaba.fastjson.JSONObject;
import com.sccl.common.support.Convert;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.energyaccount.domain.EnergyAccount;
import com.sccl.modules.business.energyaccount.mapper.EnergyAccountMapper;
import com.sccl.modules.business.energyaccount.service.IEnergyAccountService;
import com.sccl.modules.business.oilcard.domain.OilCard;
import com.sccl.modules.business.oilcard.mapper.OilCardMapper;
import com.sccl.modules.business.oilcardaccount.domain.OilCardAccount;
import com.sccl.modules.business.oilcardaccount.domain.OilCardAccountVo;
import com.sccl.modules.business.oilcardaccount.mapper.OilCardAccountMapper;
import com.sccl.modules.business.oilcardaccount.service.IOilCardAccountService;
import com.sccl.modules.system.role.domain.Role;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.service.IUserService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLDecoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 油卡台账 用此台账生成归集单
 *
 * <AUTHOR>
 * @date 2021-12-20
 */
@RestController
@RequestMapping("/business/oilCardAccount")
public class OilCardAccountController extends BaseController {
    private final String prefix = "business/oilCardAccount";

    @Autowired
    private IOilCardAccountService oilCardAccountService;

    @Autowired
    private EnergyAccountMapper energyAccountMapper;

    @Autowired
    private OilCardAccountMapper oilCardAccountMapper;

   /* @Resource
    OilCardAccountMapper oilCardAccountMapper;*/

    @Autowired
    private IUserService userService;

    @Resource
    OilCardMapper oilCardMapper;

    @RequiresPermissions("business:oilCardAccount:view")
    @GetMapping()
    public String oilCardAccount() {
        return prefix + "/oilCardAccount";
    }

    /**
     * 查询油卡台账列表
     */
//	@RequiresPermissions("business:oilCardAccount:list")
    @RequestMapping("/list")
    public TableDataInfo list(OilCardAccount oilCardAccount) {
        User user = ShiroUtils.getUser();
        List<Role> roles = userService.selectUserRole(user.getId());
        boolean isProAdmin = false;
        boolean isCityAdmin = false;
        boolean isSubAdmin = false;
        for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
            if (role.getCode().startsWith("PROVI_")) {//省能耗费管理员
                isProAdmin = true;
            }
            if (role.getCode().startsWith("CITY_") || role.getCode().startsWith("POWER_ADMIN")) {//市能耗费管理员
                isCityAdmin = true;
            }
            if (role.getCode().startsWith("SUB_")) {//县能耗费管理员
                isSubAdmin = true;
            }
        }
        if (isProAdmin) {//  查询权限设置 分公司
        } else if (isCityAdmin) {
            List<IdNameVO> companies = user.getCompanies();
            if (companies != null && companies.size() > 0) {
                oilCardAccount.setCompany(Long.parseLong(companies.get(0).getId()));
                //oilCardAccount.setCountry(null);
            }
            if (oilCardAccount.getCountry() == -1) {
                oilCardAccount.setCountry(null);
            }

        } else  {
            if (oilCardAccount.getCountry() == -1) {
                oilCardAccount.setCountry(null);
            }
        }
        startPage();
        List<OilCardAccount> list = oilCardAccountService.selectList(oilCardAccount);
        return getDataTable(list);
    }
    /**
     * 查询油卡台账列表(计算购油台账剩余量)
     */
//	@RequiresPermissions("business:oilCardAccount:list")
    @RequestMapping("/listOilAccount")
    public TableDataInfo listOilAccount(OilCardAccount oilCardAccount) {
        if (oilCardAccount.getCompany() == -1) {
            oilCardAccount.setCompany(null);
        }
        if (oilCardAccount.getCountry() == -1) {
            oilCardAccount.setCountry(null);
        }
        startPage();
        //查询对应的购油台账
        List<OilCardAccountVo> list = oilCardAccountMapper.selectListVo(oilCardAccount);
        for (int i = 0; i < list.size(); i++) {
            OilCardAccountVo oilCardAccount1 = list.get(i);
            Long id = oilCardAccount1.getId();
            BigDecimal useAccount = new BigDecimal(0);
            //查询该每个购油台账被那些加油台账使用了
            List<EnergyAccount> energyAccounts = energyAccountMapper.selectAccountByOilAccount(id);
            for (int i1 = 0; i1 < energyAccounts.size(); i1++) {
                EnergyAccount energyAccount = energyAccounts.get(i1);
                BigDecimal curusedreadings = energyAccount.getCurusedreadings();
                if (curusedreadings==null) {
                    BigDecimal bd2 = new BigDecimal(0); //把 0 转为 BigDecimal
                    curusedreadings=bd2;
                }
                //将该购油台账被加油台账使用的油量相加
                useAccount = useAccount.add(curusedreadings);
            }
            //购油量减去被使用量就是当前购油台账剩余量
            BigDecimal buyQuantity = oilCardAccount1.getBuyQuantity();
            oilCardAccount1.setUseAccount(buyQuantity.subtract(useAccount));
            list.set(i, oilCardAccount1);
        }
        return getDataTable(list);
    }

    /**
     * 新增油卡台账
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增购油台账
     */
//	@RequiresPermissions("business:oilCardAccount:add")
    @Log(title = "油卡台账", action = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult addSave(@RequestBody List<OilCardAccount> oilCardAccount) throws ParseException {
        for (OilCardAccount o : oilCardAccount) {
            o.setCreateTime(new Date());
            if (o.getBuyTime() != null) {
                String format = new SimpleDateFormat("yyyy-MM-dd").format(o.getBuyTime());
                Date parse = new SimpleDateFormat("yyyy-MM-dd").parse(format);
                o.setBuyDate(parse);
            }
            if (o.getId() != null) { // 更新
                OilCardAccount oilCardAccount1 = new OilCardAccount();
                oilCardAccount1.setId(o.getId());
                BigDecimal oldCost = oilCardAccountMapper.selectList(oilCardAccount1).get(0).getCost();
                if (o.getAccountType().equals("1")) { // 充值
                    o.setBalance(o.getBalance().subtract(oldCost).add(o.getCost()));
                } else if (o.getAccountType().equals("2")) { // 购油
                    o.setBalance(o.getBalance().add(oldCost).subtract(o.getCost()));
                }
                oilCardAccountService.updateForModel(o);
            } else {
                if (o.getAccountType().equals("1")) { // 充值 更新油卡余额
                    o.setBalance(o.getBalance().add(o.getCost()));
                } else if (o.getAccountType().equals("2")) { // 购油 更新油卡余额
                    if (o.getBalance().compareTo(o.getCost()) == -1) {//小于
                        return AjaxResult.error("油卡余额不足");
                    }
                    o.setBalance(o.getBalance().subtract(o.getCost()));
                }
                o.setSurplus(o.getBuyQuantity());
                //计算购油单价(保留两位小数并向上取整)
                o.setUnitprice(o.getCost().divide(o.getBuyQuantity(),2, RoundingMode.UP));
                oilCardAccountService.insert(o);
            }
            OilCard oilCard = new OilCard();
            oilCard.setResidue(o.getBalance());// 更新油卡余额
            oilCard.setId(o.getCardId());
            oilCardMapper.updateForModel(oilCard);
        }
        return AjaxResult.success("操作台账成功");
    }

    /**
     * 修改油卡台账
     */
    @GetMapping("/edit/{id}")
    public AjaxResult edit(@PathVariable("id") Long id) {
        OilCardAccount oilCardAccount = oilCardAccountService.get(id);
        Object object = JSONObject.toJSON(oilCardAccount);
        return this.success(object);
    }

    /**
     * 修改保存油卡台账
     */
//	@RequiresPermissions("business:oilCardAccount:edit")
    @Log(title = "油卡台账", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody OilCardAccount oilCardAccount) {
        return toAjax(oilCardAccountService.updateForModel(oilCardAccount));
    }

    /**
     * 删除油卡台账
     */
//	@RequiresPermissions("business:oilCardAccount:remove")
    @Log(title = "油卡台账", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return oilCardAccountService.removeOilCardAccount(ids);
    }


    /**
     * 查看油卡台账
     */
//    @RequiresPermissions("business:oilCardAccount:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id) {
        OilCardAccount oilCardAccount = oilCardAccountService.get(id);
        Object object = JSONObject.toJSON(oilCardAccount);
        return this.success(object);
    }

    // 油卡台账 合计
    @PostMapping("/querySumAccount")
    public AjaxResult querySumAccount(OilCardAccount oilCardAccount) {
        if (oilCardAccount.getCompany() == -1) {
            oilCardAccount.setCompany(null);
        }
        if (oilCardAccount.getCountry() == -1) {
            oilCardAccount.setCountry(null);
        }
        return AjaxResult.success(oilCardAccountMapper.querySumAccount(oilCardAccount));
    }



}
