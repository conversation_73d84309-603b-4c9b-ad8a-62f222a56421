package com.sccl.modules.business.modlebigandwork.service;

import com.sccl.modules.business.modlebigandwork.domain.ModleBigandwork;
import com.sccl.framework.service.IBaseService;

import java.io.InputStream;
import java.util.List;

/**
 * 单价-大工业-办公  服务层
 * 
 * <AUTHOR>
 * @date 2023-03-13
 */
public interface IModleBigandworkService extends IBaseService<ModleBigandwork>
{


    List<ModleBigandwork> importExcel(String sheet1, InputStream inputStream);

    int insertListForTemporary(List newList);

    void deleteRepeat();

    int insertInfo();

    String updatebitch(List<ModleBigandwork> modleBigandworks);

}
