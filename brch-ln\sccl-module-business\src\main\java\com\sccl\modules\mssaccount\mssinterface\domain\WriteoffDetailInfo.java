package com.sccl.modules.mssaccount.mssinterface.domain;

//电表报账单明细信息
public class WriteoffDetailInfo {
    //账期	非必填	格式：YYYYMM  （当type为1和2时必填）


    private String countyName;
    //电表编码	必填
    private String energyMeterCode;
    //电表名称	必填
    private String energyMeterName;
    //用电开始时间	非必填	格式：YYYYMMDD（当type为1和2时必填）
    private String electricityStartDate;
    //    用电截止时间	非必填	格式：YYYYMMDD（当type为1和2时必填）
    private String totalQuantityOfElectricity;
    //    本次电量	非必填	格式：数值（当type为1和2时必填）
    private String contractPrice;
    //    本次电费	非必填	格式：数值（当type为1和2时必填）
    private String powerConsumption;
    //    设备耗电量	非必填	格式：数值（当type为1和2时必填）
    private String thisElectricityCharge;
    //    合同电价	非必填	格式：数值     单价（元）（当type为1和2时必填）
    private String thisQuantityOfElectricity;
    //    总电量(度)	非必填	格式：数值（当type为1和2时必填）
    private String electricityEndDate;
    private String recoveryElectricityFlag;
    private String thisElectricityPrice;
    private String thisElectricityTax;
    public String getRecoveryElectricityFlag() {
        return recoveryElectricityFlag;
    }

    public void setRecoveryElectricityFlag(String recoveryElectricityFlag) {
        this.recoveryElectricityFlag = recoveryElectricityFlag;
    }

    public String getThisElectricityPrice() {
        return thisElectricityPrice;
    }

    public void setThisElectricityPrice(String thisElectricityPrice) {
        this.thisElectricityPrice = thisElectricityPrice;
    }

    public String getThisElectricityTax() {
        return thisElectricityTax;
    }

    public void setThisElectricityTax(String thisElectricityTax) {
        this.thisElectricityTax = thisElectricityTax;
    }


    public String getCountyName() {
        return countyName;
    }

    public void setCountyName(String countyName) {
        this.countyName = countyName;
    }

    public String getEnergyMeterCode() {
        return energyMeterCode;
    }

    public void setEnergyMeterCode(String energyMeterCode) {
        this.energyMeterCode = energyMeterCode;
    }

    public String getEnergyMeterName() {
        return energyMeterName;
    }

    public void setEnergyMeterName(String energyMeterName) {
        this.energyMeterName = energyMeterName;
    }

    public String getElectricityStartDate() {
        return electricityStartDate;
    }

    public void setElectricityStartDate(String electricityStartDate) {
        this.electricityStartDate = electricityStartDate;
    }

    public String getTotalQuantityOfElectricity() {
        return totalQuantityOfElectricity;
    }

    public void setTotalQuantityOfElectricity(String totalQuantityOfElectricity) {
        this.totalQuantityOfElectricity = totalQuantityOfElectricity;
    }

    public String getContractPrice() {
        return contractPrice;
    }

    public void setContractPrice(String contractPrice) {
        this.contractPrice = contractPrice;
    }

    public String getPowerConsumption() {
        return powerConsumption;
    }

    public void setPowerConsumption(String powerConsumption) {
        this.powerConsumption = powerConsumption;
    }

    public String getThisElectricityCharge() {
        return thisElectricityCharge;
    }

    public void setThisElectricityCharge(String thisElectricityCharge) {
        this.thisElectricityCharge = thisElectricityCharge;
    }

    public String getThisQuantityOfElectricity() {
        return thisQuantityOfElectricity;
    }

    public void setThisQuantityOfElectricity(String thisQuantityOfElectricity) {
        this.thisQuantityOfElectricity = thisQuantityOfElectricity;
    }

    public String getElectricityEndDate() {
        return electricityEndDate;
    }

    public void setElectricityEndDate(String electricityEndDate) {
        this.electricityEndDate = electricityEndDate;
    }

    @Override
    public String toString() {
        return "WriteoffDetailInfo{" +
                "countyName='" + countyName + '\'' +
                ", energyMeterCode='" + energyMeterCode + '\'' +
                ", energyMeterName='" + energyMeterName + '\'' +
                ", electricityStartDate='" + electricityStartDate + '\'' +
                ", totalQuantityOfElectricity='" + totalQuantityOfElectricity + '\'' +
                ", contractPrice='" + contractPrice + '\'' +
                ", powerConsumption='" + powerConsumption + '\'' +
                ", thisElectricityCharge='" + thisElectricityCharge + '\'' +
                ", thisQuantityOfElectricity='" + thisQuantityOfElectricity + '\'' +
                ", electricityEndDate='" + electricityEndDate + '\'' +
                '}';
    }
}
