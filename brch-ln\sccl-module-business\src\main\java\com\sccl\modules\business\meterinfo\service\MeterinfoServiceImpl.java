package com.sccl.modules.business.meterinfo.service;

import com.sccl.common.lang.StringUtils;
import com.sccl.common.utils.TimeUtils;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.business.meterinfo.mapper.MeterinfoMapper;
import com.sccl.modules.business.modlebigandwork.domain.ModleBigandwork;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sccl.modules.business.meterinfo.domain.Meterinfo;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.SimpleFormatter;


/**
 * 计量设备 服务层实现
 * 
 * <AUTHOR>
 * @date 2023-03-15
 */
@Service
public class MeterinfoServiceImpl extends BaseServiceImpl<Meterinfo> implements IMeterinfoService
{
    @Autowired
    private MeterinfoMapper mapper;
    @Override
    public List<Meterinfo> importExcel(String sheetName, InputStream input) {

        List<Meterinfo> list = new ArrayList<>();

        Workbook workbook = null;
        try {
            workbook = WorkbookFactory.create(input);
        } catch (IOException e) {
            e.printStackTrace();
        } catch (InvalidFormatException e) {
            e.printStackTrace();
        }
        Sheet sheet = workbook.getSheet(sheetName);
        if (StringUtils.isNotEmpty(sheetName)) {
            // 如果指定sheet名,则取指定sheet中的内容.
            sheet = workbook.getSheet(sheetName);
        }
        if (sheet == null) {
            // 如果传入的sheet名不存在则默认指向第1个sheet.
            sheet = workbook.getSheetAt(0);
        }
        int rows = sheet.getPhysicalNumberOfRows();

        if (rows > 0) {
            Row row0 = sheet.getRow(0);
            for (int i = 1; i < rows; i++) {
                Meterinfo tower = new Meterinfo();
                Row rowo = sheet.getRow(0);
                Row row = sheet.getRow(i);
                int cellNum = sheet.getRow(0).getPhysicalNumberOfCells();
                for (int j = 0; j < cellNum; j++) {
                    Cell cello = rowo.getCell(j);
                    Cell cell = row.getCell(j);
                    if (cell == null) {
                        continue;
                    } else {
                        if ("province_code".equals(cello.getStringCellValue())) {
                            tower.setProvincecode(cell.getStringCellValue());
                        }
                        if ("city_code".equals(cello.getStringCellValue())) {
                            tower.setCitycode(cell.getStringCellValue());
                        }
                        if ("city_name".equals(cello.getStringCellValue())) {
                            tower.setCityname(cell.getStringCellValue());
                        }
                        if ("county_code".equals(cello.getStringCellValue())) {
                            tower.setCountycode(cell.getStringCellValue());
                        }
                        if ("energy_meter_code".equals(cello.getStringCellValue())) {
                            tower.setEnergymetercode(cell.getStringCellValue());
                        } if ("energy_meter_name".equals(cello.getStringCellValue())) {
                            tower.setEnergymetername(cell.getStringCellValue());
                        } if ("contract_price".equals(cello.getStringCellValue())) {
                            tower.setContractprice(cell.getStringCellValue());
                        } if ("contract_price".equals(cello.getStringCellValue())) {
                            tower.setContractprice(cell.getStringCellValue());
                        } if ("contract_price".equals(cello.getStringCellValue())) {
                            tower.setContractprice(cell.getStringCellValue());
                        } if ("status".equals(cello.getStringCellValue())) {
                            tower.setStatus(cell.getStringCellValue());
                        } if ("usage".equals(cello.getStringCellValue())) {
                            tower.setUsagecopy(cell.getStringCellValue());
                        }if ("station_code".equals(cello.getStringCellValue())) {
                            tower.setStationcode(cell.getStringCellValue());
                        }if ("station_name".equals(cello.getStringCellValue())) {
                            tower.setStationname(cell.getStringCellValue());
                        }if ("station_location".equals(cello.getStringCellValue())) {
                            tower.setStationlocation(cell.getStringCellValue());
                        }if ("station_status".equals(cello.getStringCellValue())) {
                            tower.setStationstatus(cell.getStringCellValue());
                        }if ("station_type".equals(cello.getStringCellValue())) {
                            tower.setStationtype(cell.getStringCellValue());
                        }if ("large_lndustrial_electricity_flag".equals(cello.getStringCellValue())) {
                            tower.setLargeindustrialelectricityflag(cell.getStringCellValue());
                        }if ("energy_supply_way".equals(cello.getStringCellValue())) {
                            tower.setEnergysupplyway(cell.getStringCellValue());
                        }if ("power_grid_energy_meter_code".equals(cello.getStringCellValue())) {
                            tower.setPowergridenergymetercode(cell.getStringCellValue());
                        }if ("site_code".equals(cello.getStringCellValue())) {
                            tower.setSitecode(cell.getStringCellValue());
                        }if ("create_time".equals(cello.getStringCellValue())) {
                            try {
                                tower.setCreateTime(
                                        new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(cell.getStringCellValue())
                                );
                            } catch (ParseException e) {
                                e.printStackTrace();
                            }
                        }
//                        if ("变压器计算方式".equals(cello.getStringCellValue())) {
//                            String stringCellValue = String.valueOf(cell.getNumericCellValue());
//                            tower.setBigflag(stringCellValue == null ? null : Integer.valueOf(stringCellValue));
//                        }
                    }
                }
//                if (!StringUtils.isEmpty(tower.getStationaddrcode()) && !StringUtils.isEmpty(tower.getCity()) && !StringUtils.isEmpty(tower.getStationaddrname()) && !StringUtils.isEmpty(tower.getAddress())) {
//                    if (StringUtils.isEmpty(tower.getProvice())) {
//                        if ("sc".equals(deployTo)) {
//                            tower.setProvice("四川省");
//                        } else if ("ln".equals(deployTo)) {
//                            tower.setProvice("辽宁省");
//                        }
//                    }
                list.add(tower);
//                }
            }
        }

        return list;

    }

    @Override
    public int insertListForTemporary(List newList) {
        return mapper.insertListFortemporary(newList);
    }

    @Override
    public int insertInfo() {
        return mapper.insertInfo();
    }

    @Override
    public void deleteRepeat() {
         mapper.deleteRepeat();
    }
}
