package com.sccl.modules.business.powerammeterprice.service;

import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.service.IdGenerator;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.business.account.domain.Account;
import com.sccl.modules.business.account.mapper.AccountMapper;
import com.sccl.modules.business.powerammeterprice.domain.PowerAmmeterPrice;
import com.sccl.modules.business.powerammeterprice.mapper.PowerAmmeterPriceMapper;
import com.sccl.modules.monitor.operlog.domain.OperLog;
import com.sccl.modules.monitor.operlog.mapper.OperLogMapper;
import com.sccl.modules.mssaccount.mssinterface.domain.PowerElePriceItem;
import com.sccl.modules.mssaccount.mssinterface.service.MssJsonClient;
import com.sccl.modules.system.user.domain.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.IntStream;

import static java.util.stream.Collectors.*;


/**
 * 电价上传 服务层实现
 *
 * <AUTHOR>
 * @date 2022-09-18
 */
@Service
@Slf4j
public class PowerAmmeterPriceServiceImpl extends BaseServiceImpl<PowerAmmeterPrice> implements IPowerAmmeterPriceService {
    @Autowired
    PowerAmmeterPriceMapper powerAmmeterPriceMapper;
    @Autowired
    AccountMapper accountMapper;
    @Autowired
    MssJsonClient mssJsonClient;
    @Value("${MssInterface.MssJsonClient.PROVINCECODE}")
    private String PROVINCECODE;
    @Value("${sccl.deployTo}")
    private String deployTo;
    @Autowired
    private OperLogMapper operLogMapper;

    public static void main(String[] args) {
        //102
        //0->102 103  1->102


        List<Integer> list = IntStream.rangeClosed(0, 101).boxed().collect(toList());

        List<Integer> collect = IntStream.rangeClosed(0, 0).boxed().collect(toList());
        collect.forEach(i -> System.out.println("你好"));
        Map<Integer, List<Integer>> map = IntStream.rangeClosed(0, list.size() - 1).boxed().collect(
          groupingBy(
            i -> (i + 50) / 50,
            mapping(list::get, toList())
          )
        );
        System.out.println();
    }

    @Override
    public Object update(List<PowerAmmeterPrice> powerarlist) {
        User user = ShiroUtils.getUser();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (PowerAmmeterPrice powerAmmeterPrice : powerarlist) {
            //判断预警表是否有该分公司下该责任中心的预警值记录，有就修改，无则新增
            int countAlert = 0;
            if (powerAmmeterPrice.getId() == null)
                countAlert = 0;
            else
                countAlert = powerAmmeterPriceMapper.countPrice(powerAmmeterPrice);

            if (countAlert > 0) {
                //更新
                powerAmmeterPrice.setModifyrecord(sdf.format(new Date()) + "-修改");
                powerAmmeterPriceMapper.updateForModel(powerAmmeterPrice);
            } else {
                //新增
                powerAmmeterPrice.setId(IdGenerator.getNextId());//id
                powerAmmeterPrice.setInputdate(new Date());//录入时间
                powerAmmeterPrice.setInputuserid(user.getId());//当前登陆人
                powerAmmeterPrice.setDelFlag("0");//删除flag
                powerAmmeterPrice.setModifyrecord(sdf.format(new Date()) + "-新增");
                powerAmmeterPriceMapper.insert(powerAmmeterPrice);
            }
        }
        return null;
    }

    @Override
    public List<PowerAmmeterPrice> selectbyList(PowerAmmeterPrice powerAmmeterPrice) {
        List<PowerAmmeterPrice> list = powerAmmeterPriceMapper.selectList(powerAmmeterPrice);
        for (PowerAmmeterPrice pp : list) {
            System.out.println("pp.getAmmeterid()" + pp.getAmmeterid());
            Account acc = accountMapper.findLastAccountbyid(pp.getAmmeterid());
            if (acc != null) {
                pp.setLastunitpirce(acc.getUnitpirce());
                pp.setLastpcid(acc.getPcid());
            }
        }
        return list;
    }

    @Override
    public List<PowerAmmeterPrice> selecttabyList(PowerAmmeterPrice powerAmmeterPrice) {
        List<PowerAmmeterPrice> list = powerAmmeterPriceMapper.selectListta(powerAmmeterPrice);
        for (PowerAmmeterPrice pp : list) {
            System.out.println("pp.getAmmeterid()" + pp.getAmmeterid());
            Account acc = accountMapper.findLastAccountbyid(pp.getAmmeterid());
            if (acc != null) {
                pp.setLastunitpirce(acc.getUnitpirce());
                pp.setLastpcid(acc.getPcid());
            }
        }
        return list;
    }

    @Override
    public List<PowerAmmeterPrice> selectListapByIds(String[] ids) {
        return powerAmmeterPriceMapper.selectListapByIds(ids);
    }

    @Override
    public List<PowerElePriceItem> selectListByIds(String[] ids) {
        if ("ln".equals(deployTo))
            return powerAmmeterPriceMapper.selectListByIdsLn(ids);
        else
            return powerAmmeterPriceMapper.selectListByIdsSc(ids);
    }

    @Override
    public List<PowerElePriceItem> selectListall(String ids) {
        if ("ln".equals(deployTo))
            return powerAmmeterPriceMapper.selectListallLn(ids);
        else
            return powerAmmeterPriceMapper.selectListallSc(ids);

    }

    @Override
    public String sendPricedetailInfo(List<PowerElePriceItem> items) throws Exception {
        return mssJsonClient.syncEnergyMeterPriceInfos(items);
    }

    @Override
    public AjaxResult searchPricedetailInfo(List<PowerAmmeterPrice> items) throws Exception {
        String budgetSet = "";
        List<String> energyMeterCodes = new ArrayList();
        for (PowerAmmeterPrice powerAmmeterPrice : items) {

            energyMeterCodes.add(powerAmmeterPrice.getAmmetername());
            budgetSet = powerAmmeterPrice.getYear() + powerAmmeterPrice.getMonth();

        }

        return mssJsonClient.queryEnergyMeterInfo(budgetSet, energyMeterCodes);
    }

    public void sendpricebatchPro(List<PowerElePriceItem> items) throws Exception {
        if (items != null && items.size() > 0) {
            System.out.println("sendpricebatch.size():" + items.size());
            int size = items.size();
            int num = size / 50 + 1;
            boolean result = size % 50 == 0;
            List<PowerElePriceItem> subList_1;
            for (int i = 0; i < num; i++) {

                if (i == num - 1) {
                    if (result) {
                        break;
                    }
                    subList_1 = items.subList(50 * i, size);
                } else {
                    subList_1 = items.subList(50 * i, 50 * (i + 1));
                }


                String s = sendPricedetailInfo(subList_1);
                insertLog("能耗系统同步电价", "price", s);
            }

        }

    }

    public void sendpricebatch(List<PowerElePriceItem> items) throws Exception {
        Integer size = Optional.ofNullable(items).map(List::size).orElse(0);
        Map<Integer, List<PowerElePriceItem>> map = IntStream.rangeClosed(0, size - 1).boxed().collect(
          groupingBy(
            integer -> (integer + 50) / 50,
            mapping(items::get, toList())
          )
        );
        map.forEach(
          (k, v) -> {
              log.info("开始同步第{}批单价数据，单价条目:{}", k, v.size());
              String res = "";
              try {
                  res = sendPricedetailInfo(v);
              } catch (Exception e) {
                  e.printStackTrace();
              }
              log.info("第{}批单价{}条同步完毕,同步反馈：{}", k, v.size(), res);
              insertLog("能耗系统同步电价", "price", res);
          }
        );

    }

    @Override
    public int countPricestatus(PowerAmmeterPrice powerAmmeterPrice) {
        return powerAmmeterPriceMapper.countPricestatus(powerAmmeterPrice);
    }

    @Override
    public int countPricetastatus(PowerAmmeterPrice powerAmmeterPrice) {
        return powerAmmeterPriceMapper.countPricetastatus(powerAmmeterPrice);
    }

    @Override
    public void priceinit(PowerAmmeterPrice powerAmmeterPrice) {
        powerAmmeterPriceMapper.initPrice(powerAmmeterPrice);
    }

    @Override
    public void priceinitta(PowerAmmeterPrice powerAmmeterPrice) {
        powerAmmeterPriceMapper.initPriceta(powerAmmeterPrice);
    }

    @Override
    public AjaxResult createPrice(int year, int month) {
        powerAmmeterPriceMapper.createPrice(year, month);
        String month_str;
        if (month <= 12 && month >= 10) {
            month_str = "" + month;
        } else {
            month_str = "0" + month;
        }

        powerAmmeterPriceMapper.createPrice2(year, month_str);
        return AjaxResult.success("生成单价成功");
    }

    private void insertLog(String title, String method, String errorMes) {
        OperLog model = new OperLog();
        model.setOperName("mss");
        model.setTitle(title);
        model.setMethod(method);
        model.setErrorMsg(errorMes);
        model.setOperTime(new Date());

        operLogMapper.insert(model);
    }
}
