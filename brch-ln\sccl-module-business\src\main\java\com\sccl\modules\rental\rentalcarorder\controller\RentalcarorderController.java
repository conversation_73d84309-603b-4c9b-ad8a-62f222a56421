package com.sccl.modules.rental.rentalcarorder.controller;

import java.util.*;

import com.sccl.common.utils.DateUtils;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.service.IdGenerator;
import com.sccl.modules.rental.rentalcarmodel.domain.Rentalcarmodel;
import com.sccl.modules.rental.rentalcarmodelmain.domain.Rentalcarmodelmain;
import com.sccl.modules.rental.rentalordercarmodel.domain.RentalorderCarmodel;
import com.sccl.modules.rental.rentalordercarmodel.service.IRentalorderCarmodelService;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.service.IUserService;
import com.sccl.modules.uniflow.wfprocinst.service.IWfProcInstService;
import com.sccl.modules.uniflow.wftask.domain.WfTask;
import com.sccl.modules.uniflow.wftask.service.IWfTaskService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.modules.rental.rentalcarorder.domain.Rentalcarorder;
import com.sccl.modules.rental.rentalcarorder.service.IRentalcarorderService;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.common.support.Convert;
import com.alibaba.fastjson.JSONObject;

/**
 * 车辆租赁申请 信息操作处理
 *
 * <AUTHOR>
 * @date 2019-08-23
 */
@RestController
@RequestMapping("/rental/rentalcarorder")
public class RentalcarorderController extends BaseController {
    private String prefix = "rental/rentalcarorder";
    @Autowired
    private IUserService userService;
    @Autowired
    private IWfProcInstService wfProcInstService;// 流程
    @Autowired
    private IWfTaskService wfTaskService;// 流程
    @Autowired
    private IRentalcarorderService rentalcarorderService;
    @Autowired
    private IRentalorderCarmodelService rentalorderCarmodelService;

    @RequiresPermissions("rental:rentalcarorder:view")
    @GetMapping()
    public String rentalcarorder() {
        return prefix + "/rentalcarorder";
    }

    /**
     * 查询车辆租赁申请列表
     */
    @RequiresPermissions("rental:rentalcarorder:list")
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(Rentalcarorder rentalcarorder) {
        startPage();
        List<Rentalcarorder> list = rentalcarorderService.selectList(rentalcarorder);
        for (Rentalcarorder r : list) {
            if (!"1".equals(r.getStatus())) {
                r.set_disabled(true);
            } else {
                r.set_disabled(false);
            }
        }
        return getDataTable(list);
    }

    /**
     * 新增车辆租赁申请
     */
//    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存车辆租赁申请
     */
    @RequiresPermissions("rental:rentalcarorder:add")
    //@Log(title = "车辆租赁申请", action = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody Rentalcarorder rentalcarorder) {
        return rentalcarorderService.saveRentalcarorder(rentalcarorder);
    }

    /**
     * 修改车辆租赁申请
     */
    @GetMapping("/edit/{rcoid}")
    public AjaxResult edit(@PathVariable("rcoid") Long rcoid) {
        Rentalcarorder rentalcarorder = rentalcarorderService.get(rcoid);

        Object object = JSONObject.toJSON(rentalcarorder);

        return this.success(object);
    }

    /**
     * 修改保存车辆租赁申请
     */
    @RequiresPermissions("rental:rentalcarorder:edit")
    //@Log(title = "车辆租赁申请", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody Rentalcarorder rentalcarorder) {
        return toAjax(rentalcarorderService.update(rentalcarorder));
    }

    /**
     * 删除车辆租赁申请
     */
    @RequiresPermissions("rental:rentalcarorder:remove")
    //@Log(title = "车辆租赁申请", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        int i = 0;
        try {
            List<Rentalcarorder> rentalcarmodelmains = rentalcarorderService.selectListByIds(Convert.toStrArray(ids));
            for (Rentalcarorder b : rentalcarmodelmains) {
                if (!"1".equals(b.getStatus())) {
                    return this.error(1, b.getRcoTitle() + "(" + b.getRcoid() + ")" + "草稿状态才能删除");
                }
                // 终止流程
                killFlow(b);
            }
            i = rentalcarorderService.deleteAndItemByIds(Convert.toStrArray(ids));
            return this.success("删除(" + i + ")条");
        } catch (Exception e) {
            e.printStackTrace();
            return this.error(1, "删除失败:" + e.getMessage());
        }
    }

    // 终止流程
    private void killFlow(Rentalcarorder b) throws Exception {
        if (b.getIprocessinstid() != null) {
            WfTask wfTask = new WfTask();
            wfTask.setProcInstId(b.getIprocessinstid().toString());// 流程实例id
            List<WfTask> wfTasks = wfTaskService.selectList(wfTask);
            if (wfTasks != null && wfTasks.size() > 0) {
                Map<String, Object> param = new HashMap<>();
                param.put("procTaskId", wfTasks.get(0).getId());
                param.put("procInstId", b.getIprocessinstid());
                param.put("shardKey", "0000");
                param.put("stop", "sys");
                JSONObject jsonObject = wfProcInstService.stopTask(this.getCurrentUser(), param);
                System.out.println(jsonObject.toJSONString());
            }
        }
    }

    /**
     * 查看车辆租赁申请
     */
    @RequiresPermissions("rental:rentalcarorder:view")
    @GetMapping("/view/{rcoid}")
    @ResponseBody
    public AjaxResult view(@PathVariable("rcoid") Long rcoid) {
        Rentalcarorder rentalcarorder = new Rentalcarorder();
        rentalcarorder.setRcoid(rcoid);
        List<Rentalcarorder> rentalcarorders = rentalcarorderService.selectList(rentalcarorder);
        if (rentalcarorders != null && rentalcarorders.size() > 0) {

            rentalcarorder = rentalcarorders.get(0);
            List<RentalorderCarmodel> rentalorderCarmodels = rentalorderCarmodelService.selectAndNameByRcoid(rcoid);
            if (rentalorderCarmodels != null && rentalorderCarmodels.size() > 0)
                rentalcarorder.setRentalorderCarmodels(rentalorderCarmodels);
            else
                rentalcarorder.setRentalorderCarmodels(new ArrayList<RentalorderCarmodel>());
            Object object = JSONObject.toJSON(rentalcarorder);
            return this.success(object);
        } else {
            return this.error("没查询到 申请信息");
        }
    }

    @GetMapping("/getLoaddata")
    @ResponseBody
    public AjaxResult getLoaddata() {
        AjaxResult rs = new AjaxResult();
        Rentalcarorder rentalcarorder = new Rentalcarorder();
        User user = ShiroUtils.getUser();
        Long userid = user.getId();
        String company = user.getCompanies().get(0).getId();
        String companyName = user.getCompanies().get(0).getName();
        String country = user.getDepartments().get(0).getId();
        String countryName = user.getDepartments().get(0).getName();
        String name = user.getUserName();
        rentalcarorder.setCompany(company);
        rentalcarorder.setCompanyName(companyName);
        rentalcarorder.setCountry(country);
        rentalcarorder.setCountryName(countryName);
        rentalcarorder.setInputuserid(userid);
        rentalcarorder.setInputusername(name);
        rentalcarorder.setInputuserphone(user.getPhone());
        String rcoNo = "";
        String add[] = {"省公司,SGS", "成都,CD", "绵阳,MY", "泸州,LZ", "自贡,ZG", "攀枝花,PZH", "德阳,DY", "广元,GY", "遂宁,SN", "内江,NJ", "南充,NC", "宜宾,YB", "广安,GA", "达州,DZ", "巴中,BZ", "乐山,LS", "凉山,LI", "雅安,YA", "资阳,ZY", "眉山,MS", "甘孜,GZ", "阿坝,AB"};
//        各单位首字母：省公司本部：SGS；成都：CD；绵阳：MY；泸州：LZ；自贡：ZG；
//        攀枝花：PZH；德阳：DY；广元：GY；遂宁：SN；内江：NJ；南充：NC；宜宾：YB；广安：GA；达州：DZ；巴中：BZ；乐山：LS；凉山：LI；雅安：YA；资阳：ZY；眉山：MS；甘孜：GZ；阿坝：AB；
//       系统自动生成：单位拼音首字母+年月日+7位流水号
        for (String str : add) {
            String[] split = str.split(",");
            if (companyName.contains(split[0])) {
                rcoNo = split[1] + "-";
                break;
            }
        }
        if (rcoNo == "") {
            rcoNo = "QT";
        }
        String yyyyMMdd = DateUtils.formatDate(new Date(), "yyyyMMdd");
        String end = "" + IdGenerator.getNextId();
        rcoNo = rcoNo + yyyyMMdd + "-" + end.substring(12);
        rentalcarorder.setRcoNo(rcoNo);
        rs.put("data", rentalcarorder);
        return rs;
    }

}
