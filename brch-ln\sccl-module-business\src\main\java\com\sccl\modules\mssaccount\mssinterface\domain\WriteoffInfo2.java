package com.sccl.modules.mssaccount.mssinterface.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
public class WriteoffInfo2 implements Serializable {
    public List<WriteoffDetailInfo2> getWriteoffDetailInfos() {
        return writeoffDetailInfos;
    }

    public void setWriteoffDetailInfos(List<WriteoffDetailInfo2> writeoffDetailInfos) {
        this.writeoffDetailInfos = writeoffDetailInfos;
    }

    public WriteoffInfo2() {
    }

    public WriteoffInfo2(String otherSystemMainId, String writeoffInstanceCode, String type, String pickingMode,
                         List<WriteoffDetailInfo2> writeoffDetailInfos) {
        this.otherSystemMainId = otherSystemMainId;
        this.writeoffInstanceCode = writeoffInstanceCode;
        this.type = type;
        this.pickingMode = pickingMode;
        this.writeoffDetailInfos = writeoffDetailInfos;
    }

    private String otherSystemMainId;
    private String writeoffInstanceCode;
    //    type	String	同步类型	必填	1：新增，2：修改，3：作废
    private String type;

    public String getPickingMode() {
        return pickingMode;
    }

    public void setPickingMode(String pickingMode) {
        this.pickingMode = pickingMode;
    }

    private String pickingMode;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    private List<WriteoffDetailInfo2> writeoffDetailInfos;

    public String getOtherSystemMainId() {
        return otherSystemMainId;
    }

    public void setOtherSystemMainId(String otherSystemMainId) {
        this.otherSystemMainId = otherSystemMainId;
    }

    public String getWriteoffInstanceCode() {
        return writeoffInstanceCode;
    }

    public void setWriteoffInstanceCode(String writeoffInstanceCode) {
        this.writeoffInstanceCode = writeoffInstanceCode;
    }



    @Override
    public String toString() {
        return "WriteoffInfo{" +
                "otherSystemMainId='" + otherSystemMainId + '\'' +
                ", writeoffInstanceCode='" + writeoffInstanceCode + '\'' +
                ", writeoffDetailInfos=" + writeoffDetailInfos +
                '}';
    }

}
