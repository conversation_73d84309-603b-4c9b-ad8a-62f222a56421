package com.sccl.modules.mssaccount.mssaccountbill.frame;

import org.springframework.util.CollectionUtils;

import java.util.List;

public class ParmVerify implements RemoveVerifyRule {
    @Override
    public String Verify(Long billId, List<Long> accountIds) {
        boolean flag1 = (billId == null);
        boolean flag2 = CollectionUtils.isEmpty(accountIds);
        if (flag1 || flag2) {
            return "参数有误，报账id或台账明细id为空";
        }
        return "";
    }
}
