package com.sccl.modules.mssaccount.mssinterface.domain;

import lombok.Data;

import java.io.Serializable;

/**
 * 同步电价明细信息
 */
@Data
public class syncEnergyMeterPriceInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 省份
     */
    private String provinceCode;
    /**
     * 市局组织编码
     */
    private String cityCode;
    /**
     * 市局组织名称
     */
    private String cityName;
    /**
     * 区县组织编码
     */
    private String countryCode;
    /**
     * 区县组织名称
     */
    private String countryName;
    /**
     * 账期
     */
    private String budgetSet;
    /**
     * 局站编码
     */
    private String stationCode;
    /**
     * 局站名称
     */
    private String stationName;
    /**
     * 电表编码
     */
    private String energyMeterCode;
    /**
     * 电表名称
     */
    private String energyMeterName;
    /**
     * 送合同单价
     */
    private String electricityPrice;
}
