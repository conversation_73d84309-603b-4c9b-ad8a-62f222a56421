package com.sccl.modules.mssaccount.mssinterface.domain;

public enum BillExecuteState {
    EXECUTE(1, "轮询中"), EXCEPTION(2, "轮询失败，等待下次定时轮询"), EXIST(3, "已加入线程池等待队列,等待处理"),
    GetStatusFail(4, "已成功轮询,但未获取到财辅下一状态"),
    GetStatusSuccess(5, "已成功轮询,并获取到财辅下一步状态");

    private Integer state;
    private String desc;

    BillExecuteState(Integer state, String desc) {
        this.state = state;
        this.desc = desc;
    }

    public static BillExecuteState build(Integer executeStatus) {
        switch (executeStatus) {
            case 1:
                return EXECUTE;
            case 2:
                return EXCEPTION;
            case 3:
                return EXIST;
            case 4:
                return GetStatusFail;
            case 5:
                return GetStatusSuccess;
            default:
                return GetStatusFail;
        }
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }


}
