package com.sccl.modules.business.stationaudit.pcontractprice;

import com.enrising.dcarbon.audit.AbstractReferee;
import com.enrising.dcarbon.audit.Auditable;
import com.enrising.dcarbon.audit.RefereeDatasource;
import com.enrising.dcarbon.audit.RefereeResult;
import com.sccl.modules.business.stationaudit.msshistory.HistoryResult;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class ContracatReferee extends AbstractReferee {
    public ContracatReferee(String refereeName) {
        super(refereeName);
    }

    @Override
    public RefereeResult doReferee(RefereeResult lastRefereeResult, Auditable auditable, Map<Class<?
      extends RefereeDatasource>, List<RefereeDatasource>> refereeDatasourceList) {

        //取出数据
        List<RefereeDatasource> refereeDatasources = refereeDatasourceList.get(ContractExCreatorContent.class);

        HistoryResult result = new HistoryResult();
        if (refereeDatasources == null) {
            return result;
        }
        List<RefereeDatasource> list = refereeDatasources
          .stream().filter(Objects::nonNull).collect(Collectors.toList());

        //放数据
        result.setTopic("转供电单价");
        result.setList(list);

        return result;
    }

}
