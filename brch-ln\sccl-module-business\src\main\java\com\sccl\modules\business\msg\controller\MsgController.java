package com.sccl.modules.business.msg.controller;

import com.alibaba.fastjson.JSONObject;
import com.sccl.common.lang.StringUtils;
import com.sccl.common.support.Convert;
import com.sccl.common.utils.DateUtils;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.datasource.DynamicDataSourceContextHolder;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.autojob.util.convert.MessageMaster;
import com.sccl.modules.autojob.util.id.IdGenerator;
import com.sccl.modules.business.msg.domain.Message;
import com.sccl.modules.business.msg.service.IMsgService;
import com.sccl.modules.monitor.operlog.domain.OperLog;
import com.sccl.modules.monitor.operlog.service.OperLogServiceImpl;
import com.sccl.modules.system.attachments.domain.Attachments;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 铁塔稽核结果 信息操作处理
 *
 * <AUTHOR>
 * @date 2021-08-16
 */
@RestController
@RequestMapping("/business/msg")
public class MsgController extends BaseController {
    private String prefix = "business/msg";

    private static final Map<String, String> EXPORT_MAP = new HashMap<>();

    static {
        EXPORT_MAP.put("id", "条目ID");
        EXPORT_MAP.put("city", "地市");
        EXPORT_MAP.put("status", "状态（0-不通过 1-通过）");
        EXPORT_MAP.put("rongStep", "错误步骤");
        EXPORT_MAP.put("towerId", "铁塔账单ID");
        EXPORT_MAP.put("towerKey", "能耗账单ID");
        EXPORT_MAP.put("towerSiteCode", "铁塔站址编码");
        EXPORT_MAP.put("mark", "异常类型");
        EXPORT_MAP.put("towerSiteName", "铁塔站址名称");
        EXPORT_MAP.put("message", "异常详情");
    }

    @Autowired
    private IMsgService msgService;

    @Autowired
    private OperLogServiceImpl service;

    @RequiresPermissions("business:msg:view")
    @GetMapping()
    public String msg() {
        return prefix + "/msg";
    }

    /**
     * 查询铁塔稽核结果列表
     */
    /*	@RequiresPermissions("business:msg:list")*/
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(Message msg) {
        startPage();
        List<Message> list = msgService.selectList(msg);
        return getDataTable(list);
    }

    /**
     * 通过稽核结果查询铁塔列表
     */
    @RequestMapping("/talist")
    @ResponseBody
    public TableDataInfo talist(Message msg) {
        startPage();
        List<Message> list = msgService.selectList(msg);
        return getDataTable(list);
    }

    /**
     * 新增铁塔稽核结果
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存铁塔稽核结果
     */
    @RequiresPermissions("business:msg:add")
    @Log(title = "铁塔稽核结果", action = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody Message msg) {
        return toAjax(msgService.insert(msg));
    }

    /**
     * 修改铁塔稽核结果
     */
    @GetMapping("/edit/{id}")
    public AjaxResult edit(@PathVariable("id") Long id) {
        Message msg = msgService.get(id);

        Object object = JSONObject.toJSON(msg);

        return this.success(object);
    }

    /**
     * 修改保存铁塔稽核结果
     */
    @RequiresPermissions("business:msg:edit")
    @Log(title = "铁塔稽核结果", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody Message msg) {
        return toAjax(msgService.update(msg));
    }

    /**
     * 删除铁塔稽核结果
     */
    @RequiresPermissions("business:msg:remove")
    @Log(title = "铁塔稽核结果", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(msgService.deleteByIds(Convert.toStrArray(ids)));
    }


    /**
     * 查看铁塔稽核结果
     */
    @RequiresPermissions("business:msg:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id) {
        Message msg = msgService.get(id);

        Object object = JSONObject.toJSON(msg);

        return this.success(object);
    }

    @GetMapping(value = "/select_by_city_between", produces = "application/json;charset=UTF-8")
    public String selectMessageByCityBetween(@RequestParam("CITY") String city, @RequestParam(value = "START_TIME", required = false) String from, @RequestParam(value = "END_TIME", required = false) String to) {
        if (StringUtils.isEmpty(city)) {
            return MessageMaster.DefaultMessage.EMPTY_PARAMS.toString();
        }
        List<Message> messageList = msgService.getMessageByCity(city, from, to);
        MessageMaster master = new MessageMaster();
        master.setCode(MessageMaster.Code.OK);
        master.setFormatData(true);
        master.setData(messageList);
        master.insertNewMessage("totalNum", messageList.size());
        return master.toString();
    }

    @GetMapping(value = "/get_latest_by_tower_id", produces = "application/json;charset=UTF-8")
    public String getLatestMessageByTowerId(@RequestBody List<String> towerIdList) {
        if (CollectionUtils.isEmpty(towerIdList)) {
            return MessageMaster.DefaultMessage.EMPTY_PARAMS.toString();
        }
        List<Message> messageList = msgService.getLatestMessageByTowerId(towerIdList);

        /*=================记录Interface调用日志=================>*/
        DynamicDataSourceContextHolder.setDB("RMP");
        OperLog operLog = new OperLog();
        operLog.setMethod("CallbackHandle");
        if (towerIdList.size() > 0) {
            operLog.setErrorMsg(String.format("Interface服务尝试查找towerId：%s等%d个Id的稽核记录，共找到%d条稽核记录", towerIdList.get(0), towerIdList.size(), messageList.size()));
        } else {
            operLog.setErrorMsg("Interface服务尝试查找稽核记录，但是查找参数为空");
        }
        operLog.setOperTime(new Date());
        operLog.setId(IdGenerator.getNextIdAsLong());
        service.insert(operLog);
        DynamicDataSourceContextHolder.setDB("ECM");
        /*=======================Finished======================<*/

        MessageMaster master = new MessageMaster();
        master.setCode(MessageMaster.Code.OK);
        master.setFormatData(true);
        master.setData(messageList);
        master.insertNewMessage("totalNum", messageList.size());
        return master.toString();
    }

    @PostMapping(value = "/get_latest_by_tower_key", produces = "application/json;charset=UTF-8")
    public String getLatestMessageByTowerKey(@RequestBody List<Long> towerKeyList) {
        if (CollectionUtils.isEmpty(towerKeyList)) {
            return MessageMaster.DefaultMessage.EMPTY_PARAMS.toString();
        }
        List<Message> messageList = msgService.getLatestMessageByTowerKey(towerKeyList);

        /*=================记录Interface调用日志=================>*/
        DynamicDataSourceContextHolder.setDB("RMP");
        OperLog operLog = new OperLog();
        operLog.setMethod("CallbackHandle");
        if (towerKeyList.size() > 0) {
            operLog.setErrorMsg(String.format("Interface服务尝试查找ID：%s等%d个Id的稽核记录，共找到%d条稽核记录", towerKeyList.get(0), towerKeyList.size(), messageList.size()));
        } else {
            operLog.setErrorMsg("Interface服务尝试查找稽核记录，但是查找参数为空");
        }
        operLog.setOperTime(new Date());
        operLog.setId(IdGenerator.getNextIdAsLong());
        service.insert(operLog);
        DynamicDataSourceContextHolder.setDB("ECM");
        /*=======================Finished======================<*/

        MessageMaster master = new MessageMaster();
        master.setCode(MessageMaster.Code.OK);
        master.setFormatData(true);
        master.setData(messageList);
        master.insertNewMessage("totalNum", messageList.size());
        return master.toString();
    }

    /*=================导出接口=================>*/

    @GetMapping(value = "export_by_city_between", produces = "application/json;charset=UTF-8")
    public String export(@RequestParam("CITY") String cityName, @RequestParam("FROM") String from, @RequestParam("TO") String to) {
        if (StringUtils.isEmpty(cityName) || StringUtils.isEmpty(from)) {
            return MessageMaster.DefaultMessage.EMPTY_PARAMS.toString();
        }
        boolean isToNow = StringUtils.isEmpty(to);
        to = StringUtils.isEmpty(to) ? DateUtils.getDate() : to;
        List<Message> messageList = msgService.getMessageByCity(cityName, from, to);
        if (!CollectionUtils.isEmpty(messageList)) {
            Attachments attachments = msgService.exportExcel(messageList, String.format("%s%s至%s稽核结果", cityName, from, isToNow ? "现在" : to).replace(" ", "_"), EXPORT_MAP, new HashMap<>());
            return MessageMaster.getMessage(MessageMaster.Code.OK, "导出成功", attachments, true, false);
        }
        return MessageMaster.getMessage(MessageMaster.Code.OK, "没有匹配数据");
    }
    /*=======================Finished======================<*/


}
