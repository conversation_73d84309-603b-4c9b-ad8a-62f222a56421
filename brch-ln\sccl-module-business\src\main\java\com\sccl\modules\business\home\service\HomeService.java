package com.sccl.modules.business.home.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.sccl.common.utils.MathUtils;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.modules.business.home.mapper.HomeMapper;
import com.sccl.modules.system.role.domain.Role;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.service.IUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
@Service
public class HomeService extends BaseServiceImpl<Object>{

	private final List<String> CountPowerIndicatorCodeList = Collections.unmodifiableList(Arrays.asList("1.1", "1.3",
			"1.3.1", "*******", "*******", "1.5", "1.7", "2.1"));
	@Autowired
	private HomeMapper homeMapper;

	@Autowired
	private IUserService userService;


	public List<Map<String, Object>> getAmmeterorRank() {
		User user=ShiroUtils.getUser();
		List<Map<String, Object>> list=null;
		if(userService.isCityUser(user.getId()))
			list=homeMapper.queryAmmeterorRank(user.getCompanies());
		else
			list=homeMapper.queryAmmeterorRank(null);
		return list;
	}

	public List<Map<String, Object>> getUserTask() {
		User user=ShiroUtils.getUser();
		List<Map<String, Object>> taskList=homeMapper.getUserTask(user.getLoginId());
		if(taskList==null)
			taskList=new ArrayList<Map<String,Object>>();
		/*Map<String, Object> map=homeMapper.getUserMonthDoneTask(user.getId().toString());
		if(map!=null)
			taskList.add(map);*/
		return taskList;
	}

	public Map<String, Object> getAlarmCount() {
		User user=ShiroUtils.getUser();
		List<IdNameVO> companies=null;
		List<IdNameVO> deportments=null;
		List<Map<String, Object>> listAccount=null;
		if(userService.isProvUser(user.getId())){
			companies=new ArrayList<IdNameVO>();
			deportments=new ArrayList<IdNameVO>();
		}else {
			companies=user.getCompanies();
			deportments=user.getDepartments();
		}
		//先取设置的告警阀值
		int apoint=homeMapper.getAlerttimepoint(companies, deportments);
		listAccount=homeMapper.getAlarmAccount(companies,deportments,apoint);
		List<Map<String, Object>> listBill=homeMapper.getAlarmBill(companies,deportments);
		List<Map<String, Object>> listProtocol=homeMapper.getAlarmProtocol(companies,deportments);
		Map<String, Object> map=new HashMap<String, Object>();
		if(listAccount!=null)
			map.put("account", listAccount.size());
		else
			map.put("account", 0);
		if(listBill!=null)
			map.put("bill", listBill.size());
		else
			map.put("bill", 0);
		if(listProtocol!=null)
			map.put("protocol", listProtocol.size());
		else
			map.put("protocol", 0);
		return map;
	}

	public HashMap<String, Object> getHomePageData() {
		User user = ShiroUtils.getUser();
		Long companyId = getCompanyId(user);
		HashMap<String, Object> result = new HashMap<>();
		List<HashMap<String, Object>> ammeterOrProtocolList = homeMapper.countAmmeterOrProtocol(companyId);
		HashMap<String, Object> ammeterInfo = new HashMap<>();
		HashMap<String, Object> protocolInfo = new HashMap<>();
		ammeterInfo.put("activate", "0");
		ammeterInfo.put("deactivate", "0");
		protocolInfo.put("activate", "0");
		protocolInfo.put("deactivate", "0");
		for (HashMap<String, Object> item : ammeterOrProtocolList) {
			if ("电表".equals(item.get("power_category").toString())) {
				if ("1".equals(item.get("status").toString())) {
					ammeterInfo.put("activate", item.get("count"));
				} else {
					ammeterInfo.put("deactivate", item.get("count"));
				}
			} else {
				if ("1".equals(item.get("status").toString())) {
					protocolInfo.put("activate", item.get("count"));
				} else {
					protocolInfo.put("deactivate", item.get("count"));
				}
			}
		}
		result.put("ammeterInfo", ammeterInfo);
		result.put("protocolInfo", protocolInfo);
		HashMap<String, Object> stationInfo = new HashMap<>();
		stationInfo.put("activate", "0");
		stationInfo.put("deactivate", "0");
		List<HashMap<String, Object>> stationList = homeMapper.countPowerStation(companyId);
		for (HashMap<String, Object> item : stationList) {
			if ("2".equals(item.get("status").toString())) {
				stationInfo.put("activate", item.get("count"));
			} else if ("1".equals(item.get("status").toString())) {
				stationInfo.put("deactivate", item.get("count"));
			}
		}
		result.put("stationInfo", stationInfo);
		String year = String.valueOf(DateUtil.year(new Date()));
		List<HashMap<String, Object>> energyDataList = homeMapper.countPowerConsumption(companyId, year, CountPowerIndicatorCodeList);
		BigDecimal carbonEmissions = BigDecimal.ZERO;
		BigDecimal powerTotal = BigDecimal.ZERO;
		BigDecimal powerProduction = BigDecimal.ZERO;
		BigDecimal powerManage = BigDecimal.ZERO;
		BigDecimal powerCanal = BigDecimal.ZERO;
		BigDecimal gasolineData = BigDecimal.ZERO;
		BigDecimal thermalData = BigDecimal.ZERO;
		BigDecimal dieselData = BigDecimal.ZERO;
		BigDecimal coalData = BigDecimal.ZERO;
		BigDecimal factor;
		for (HashMap<String, Object> item : energyDataList) {
			String sData = "0";
			if (ObjectUtil.isNotEmpty(item.get("data"))) {
				sData = item.get("data").toString();
			}
			switch (item.get("indicator_code").toString()) {
				case "1.1":
					//煤炭(吨)
					coalData = new BigDecimal(sData);
					factor = homeMapper.getEnergyFactor(13L, year);
					if (ObjectUtil.isNotEmpty(factor)) {
						carbonEmissions = carbonEmissions.add(coalData.multiply(factor).divide(BigDecimal.valueOf(1000)));
					}
					break;
				case "1.3":
					//耗电量（总）(千瓦时)
					powerTotal = new BigDecimal(sData);
					factor = homeMapper.getEnergyFactor(3L, year);
					if (ObjectUtil.isNotEmpty(factor)) {
						carbonEmissions = carbonEmissions.add(powerTotal.multiply(factor).divide(BigDecimal.valueOf(1000)));
					}
					break;
				case "1.3.1":
					//生产用房耗电量(千瓦时)
					powerProduction = new BigDecimal(sData);
					break;
				case "*******":
					//管理用房耗电量(千瓦时)
					powerManage = new BigDecimal(sData);
					break;
				case "*******":
					//渠道用房耗电量(千瓦时)
					powerCanal = new BigDecimal(sData);
					break;
				case "1.5":
					//汽油消耗量(升)
					gasolineData = new BigDecimal(sData);
					factor = homeMapper.getEnergyFactor(7L, year);
					if (ObjectUtil.isNotEmpty(factor)) {
						carbonEmissions = carbonEmissions.add(gasolineData.multiply(factor).divide(BigDecimal.valueOf(1000)));
					}
					break;
				case "1.7":
					//柴油消耗量(升)
					dieselData = new BigDecimal(sData);
					factor = homeMapper.getEnergyFactor(8L, year);
					if (ObjectUtil.isNotEmpty(factor)) {
						carbonEmissions = carbonEmissions.add(dieselData.multiply(factor).divide(BigDecimal.valueOf(1000)));
					}
					break;
				case "2.1":
					//热力
					thermalData = new BigDecimal(sData);
					factor = homeMapper.getEnergyFactor(12L, year);
					if (ObjectUtil.isNotEmpty(factor)) {
						carbonEmissions = carbonEmissions.add(thermalData.multiply(factor).divide(BigDecimal.valueOf(1000)));
					}
					break;
			}
		}
		result.put("gasolineData", gasolineData);
		result.put("thermalData", thermalData);
		result.put("dieselData", dieselData);
		result.put("coalData", coalData);
		BigDecimal powerOther = powerTotal.subtract(powerProduction).subtract(powerManage).subtract(powerCanal);
		HashMap<String, Object> powerInfo = new HashMap<>();
		HashMap<String, Object> powerProductionInfo = new HashMap<>();
		HashMap<String, Object> powerManageInfo = new HashMap<>();
		HashMap<String, Object> powerCanalInfo = new HashMap<>();
		HashMap<String, Object> powerOtherInfo = new HashMap<>();
		powerInfo.put("total", powerTotal.setScale(2, RoundingMode.HALF_UP));
		powerProductionInfo.put("data", powerProduction.setScale(2, RoundingMode.HALF_UP));
		powerProductionInfo.put("percentage", MathUtils.getPercent(powerProduction, powerTotal));
		powerInfo.put("powerProductionInfo", powerProductionInfo);
		powerManageInfo.put("data", powerManage);
		powerManageInfo.put("percentage", MathUtils.getPercent(powerManage, powerTotal));
		powerInfo.put("powerManageInfo", powerManageInfo);
		powerCanalInfo.put("data", powerCanal);
		powerCanalInfo.put("percentage", MathUtils.getPercent(powerCanal, powerTotal));
		powerInfo.put("powerCanalInfo", powerCanalInfo);
		powerOtherInfo.put("data", powerOther);
		powerOtherInfo.put("percentage", MathUtils.getPercent(powerOther, powerTotal));
		powerInfo.put("powerOtherInfo", powerOtherInfo);
		result.put("powerInfo", powerInfo);
		BigDecimal productionBusinessData = BigDecimal.ZERO;
		BigDecimal tmpData = homeMapper.selectProductionDataYear(companyId, year);
		if (ObjectUtil.isNotEmpty(tmpData)) {
			productionBusinessData = tmpData.setScale(2, RoundingMode.HALF_UP);
		}
		result.put("productionBusinessData", productionBusinessData);
		result.put("carbonEmissions", carbonEmissions.setScale(2, RoundingMode.HALF_UP));
		result.put("carbonStrength", MathUtils.division(carbonEmissions.multiply(BigDecimal.valueOf(1000)), productionBusinessData));
		List<HashMap<String, Object>> taskList = homeMapper.getUserTaskList(user.getLoginId());
		result.put("mssAccountList", getMssAccountList(taskList));
		result.put("ammeterOrProtocolList", getAmmeterList(taskList));
		result.put("quotaList", getQuotaList(taskList));
		result.put("stationList", getStationList(taskList));
		result.put("budgetList", getBudgetList(taskList));
		return result;
	}

	private List<HashMap<String, Object>> getMssAccountList(List<HashMap<String, Object>> dataList) {
		List<HashMap<String, Object>> newList = dataList.stream().filter(node -> {
			return node.get("busi_name").toString().contains("报账审批");
		}).collect(Collectors.toList());
		return newList.subList(0, Math.min(20, newList.size()));
	}

	private List<HashMap<String, Object>> getAmmeterList(List<HashMap<String, Object>> dataList) {
		List<HashMap<String, Object>> newList = dataList.stream().filter(node -> {
			return node.get("busi_name").toString().contains("电表") || node.get("busi_name").toString().contains("协议");
		}).collect(Collectors.toList());
		return newList.subList(0, Math.min(20, newList.size()));
	}

	private List<HashMap<String, Object>> getQuotaList(List<HashMap<String, Object>> dataList) {
		List<HashMap<String, Object>> newList =  dataList.stream().filter(node -> {
			return node.get("busi_name").toString().contains("定额") || node.get("busi_name").toString().contains("限额");
		}).collect(Collectors.toList());
		return newList.subList(0, Math.min(20, newList.size()));
	}

	private List<HashMap<String, Object>> getStationList(List<HashMap<String, Object>> dataList) {
		List<HashMap<String, Object>> newList = dataList.stream().filter(node -> {
			return node.get("busi_name").toString().contains("局站");
		}).collect(Collectors.toList());
		return newList.subList(0, Math.min(20, newList.size()));
	}

	private List<HashMap<String, Object>> getBudgetList(List<HashMap<String, Object>> dataList) {
		List<HashMap<String, Object>> newList = dataList.stream().filter(node -> {
			return node.get("busi_name").toString().contains("预算");
		}).collect(Collectors.toList());
		return newList.subList(0, Math.min(20, newList.size()));
	}

	private Long getCompanyId(User user) {
		List<Role> roles = userService.selectUserRole(user.getId());
		for (Role role : roles) {//同时 拥有 市，省 权限 以省优先
			if (role.getCode().startsWith("PROVI_") || role.getCode().startsWith("admin") ||
					role.getCode().equals("SYS_ADMIN") || role.getCode().equals("CARBON_ADMIN")) {
				//省级管理员，查看全省数据
				return null;
			}
		}
		//非省级管理员，查看本公司数据
		List<IdNameVO> companyList = user.getCompanies();
		if (CollectionUtil.isEmpty(companyList)) {
			return null;
		}
		String cid = companyList.get(0).getId();
		if (StrUtil.isBlank(cid) || "2600000000".equals(cid)) {
			return null;
		} else {
			return Long.parseLong(cid);
		}
	}
}
